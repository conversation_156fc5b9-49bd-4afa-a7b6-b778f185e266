{"version": 3, "sources": ["../../domelementtype/lib/esm/index.js", "../../domhandler/lib/esm/node.js", "../../domhandler/lib/esm/index.js", "../../leac/lib/leac.mjs", "../../peberminta/lib/core.mjs", "../../parseley/lib/parseley.mjs", "../../selderee/lib/selderee.mjs", "../../@selderee/plugin-htmlparser2/lib/hp2-builder.mjs", "../../entities/lib/esm/generated/generated/decode-data-html.ts", "../../entities/lib/esm/generated/generated/decode-data-xml.ts", "../../entities/lib/esm/decode_codepoint.ts", "../../entities/lib/esm/decode.ts", "../../htmlparser2/lib/esm/Tokenizer.ts", "../../htmlparser2/lib/esm/Parser.ts", "../../entities/lib/esm/generated/generated/encode-html.ts", "../../entities/lib/esm/escape.ts", "../../entities/lib/esm/index.ts", "../../dom-serializer/lib/esm/foreignNames.js", "../../dom-serializer/lib/esm/index.js", "../../domutils/lib/esm/helpers.ts", "../../htmlparser2/lib/esm/index.ts", "../../html-to-text/lib/html-to-text.mjs"], "sourcesContent": ["/** Types of elements found in htmlparser2's DOM */\nexport var ElementType;\n(function (ElementType) {\n    /** Type for the root element of a document */\n    ElementType[\"Root\"] = \"root\";\n    /** Type for Text */\n    ElementType[\"Text\"] = \"text\";\n    /** Type for <? ... ?> */\n    ElementType[\"Directive\"] = \"directive\";\n    /** Type for <!-- ... --> */\n    ElementType[\"Comment\"] = \"comment\";\n    /** Type for <script> tags */\n    ElementType[\"Script\"] = \"script\";\n    /** Type for <style> tags */\n    ElementType[\"Style\"] = \"style\";\n    /** Type for Any tag */\n    ElementType[\"Tag\"] = \"tag\";\n    /** Type for <![CDATA[ ... ]]> */\n    ElementType[\"CDATA\"] = \"cdata\";\n    /** Type for <!doctype ...> */\n    ElementType[\"Doctype\"] = \"doctype\";\n})(ElementType || (ElementType = {}));\n/**\n * Tests whether an element is a tag or not.\n *\n * @param elem Element to test\n */\nexport function isTag(elem) {\n    return (elem.type === ElementType.Tag ||\n        elem.type === ElementType.Script ||\n        elem.type === ElementType.Style);\n}\n// Exports for backwards compatibility\n/** Type for the root element of a document */\nexport const Root = ElementType.Root;\n/** Type for Text */\nexport const Text = ElementType.Text;\n/** Type for <? ... ?> */\nexport const Directive = ElementType.Directive;\n/** Type for <!-- ... --> */\nexport const Comment = ElementType.Comment;\n/** Type for <script> tags */\nexport const Script = ElementType.Script;\n/** Type for <style> tags */\nexport const Style = ElementType.Style;\n/** Type for Any tag */\nexport const Tag = ElementType.Tag;\n/** Type for <![CDATA[ ... ]]> */\nexport const CDATA = ElementType.CDATA;\n/** Type for <!doctype ...> */\nexport const Doctype = ElementType.Doctype;\n", "import { ElementType, isTag as isTagRaw } from \"domelementtype\";\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nexport class Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nexport class DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nexport class Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nexport class Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nexport class ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nexport class NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nexport class CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nexport class Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nexport class Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? ElementType.Script\n        : name === \"style\"\n            ? ElementType.Style\n            : ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nexport function isTag(node) {\n    return isTagRaw(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nexport function isCDATA(node) {\n    return node.type === ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nexport function isText(node) {\n    return node.type === ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nexport function isComment(node) {\n    return node.type === ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nexport function isDirective(node) {\n    return node.type === ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nexport function isDocument(node) {\n    return node.type === ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nexport function hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nexport function cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n", "import { ElementType } from \"domelementtype\";\nimport { Element, Text, Comment, CDATA, Document, ProcessingInstruction, } from \"./node.js\";\nexport * from \"./node.js\";\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nexport class DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? ElementType.Tag : undefined;\n        const element = new Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new Text(\"\");\n        const node = new CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\nexport default DomHandler;\n", "const e=/\\n/g;function n(n){const o=[...n.matchAll(e)].map((e=>e.index||0));o.unshift(-1);const s=t(o,0,o.length);return e=>r(s,e)}function t(e,n,r){if(r-n==1)return{offset:e[n],index:n+1};const o=Math.ceil((n+r)/2),s=t(e,n,o),l=t(e,o,r);return{offset:s.offset,low:s,high:l}}function r(e,n){return function(e){return Object.prototype.hasOwnProperty.call(e,\"index\")}(e)?{line:e.index,column:n-e.offset}:r(e.high.offset<n?e.high:e.low,n)}function o(e,t=\"\",r={}){const o=\"string\"!=typeof t?t:r,l=\"string\"==typeof t?t:\"\",c=e.map(s),f=!!o.lineNumbers;return function(e,t=0){const r=f?n(e):()=>({line:0,column:0});let o=t;const s=[];e:for(;o<e.length;){let n=!1;for(const t of c){t.regex.lastIndex=o;const c=t.regex.exec(e);if(c&&c[0].length>0){if(!t.discard){const e=r(o),n=\"string\"==typeof t.replace?c[0].replace(new RegExp(t.regex.source,t.regex.flags),t.replace):c[0];s.push({state:l,name:t.name,text:n,offset:o,len:c[0].length,line:e.line,column:e.column})}if(o=t.regex.lastIndex,n=!0,t.push){const n=t.push(e,o);s.push(...n.tokens),o=n.offset}if(t.pop)break e;break}}if(!n)break}return{tokens:s,offset:o,complete:e.length<=o}}}function s(e,n){return{...e,regex:l(e,n)}}function l(e,n){if(0===e.name.length)throw new Error(`Rule #${n} has empty name, which is not allowed.`);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"regex\")}(e))return function(e){if(e.global)throw new Error(`Regular expression /${e.source}/${e.flags} contains the global flag, which is not allowed.`);return e.sticky?e:new RegExp(e.source,e.flags+\"y\")}(e.regex);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"str\")}(e)){if(0===e.str.length)throw new Error(`Rule #${n} (\"${e.name}\") has empty \"str\" property, which is not allowed.`);return new RegExp(c(e.str),\"y\")}return new RegExp(c(e.name),\"y\")}function c(e){return e.replace(/[-[\\]{}()*+!<=:?./\\\\^$|#\\s,]/g,\"\\\\$&\")}export{o as createLexer};\n", "import { clamp, escapeWhitespace } from './util.mjs';\n\nfunction emit(value) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: value\n    });\n}\nfunction make(\nf) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: f(data, i)\n    });\n}\nfunction action(\nf) {\n    return (data, i) => {\n        f(data, i);\n        return {\n            matched: true,\n            position: i,\n            value: null\n        };\n    };\n}\nfunction fail(\ndata, i) {\n    return { matched: false };\n}\nfunction error(message) {\n    return (data, i) => {\n        throw new Error((message instanceof Function) ? message(data, i) : message);\n    };\n}\nfunction token(\nonToken,\nonEnd) {\n    return (data, i) => {\n        let position = i;\n        let value = undefined;\n        if (i < data.tokens.length) {\n            value = onToken(data.tokens[i], data, i);\n            if (value !== undefined) {\n                position++;\n            }\n        }\n        else {\n            onEnd?.(data, i);\n        }\n        return (value === undefined)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: position,\n                value: value\n            };\n    };\n}\nfunction any(data, i) {\n    return (i < data.tokens.length)\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction satisfy(\ntest) {\n    return (data, i) => (i < data.tokens.length && test(data.tokens[i], data, i))\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction mapInner(r, f) {\n    return (r.matched) ? ({\n        matched: true,\n        position: r.position,\n        value: f(r.value, r.position)\n    }) : r;\n}\nfunction mapOuter(r, f) {\n    return (r.matched) ? f(r) : r;\n}\nfunction map(p, mapper) {\n    return (data, i) => mapInner(p(data, i), (v, j) => mapper(v, data, i, j));\n}\nfunction map1(p,\nmapper) {\n    return (data, i) => mapOuter(p(data, i), (m) => mapper(m, data, i));\n}\nfunction peek(p, f) {\n    return (data, i) => {\n        const r = p(data, i);\n        f(r, data, i);\n        return r;\n    };\n}\nfunction option(p, def) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? r\n            : {\n                matched: true,\n                position: i,\n                value: def\n            };\n    };\n}\nfunction not(p) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: i,\n                value: true\n            };\n    };\n}\nfunction choice(...ps) {\n    return (data, i) => {\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched) {\n                return result;\n            }\n        }\n        return { matched: false };\n    };\n}\nfunction otherwise(pa, pb) {\n    return (data, i) => {\n        const r1 = pa(data, i);\n        return (r1.matched)\n            ? r1\n            : pb(data, i);\n    };\n}\nfunction longest(...ps) {\n    return (data, i) => {\n        let match = undefined;\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched && (!match || match.position < result.position)) {\n                match = result;\n            }\n        }\n        return match || { matched: false };\n    };\n}\nfunction takeWhile(p,\ntest) {\n    return (data, i) => {\n        const values = [];\n        let success = true;\n        do {\n            const r = p(data, i);\n            if (r.matched && test(r.value, values.length + 1, data, i, r.position)) {\n                values.push(r.value);\n                i = r.position;\n            }\n            else {\n                success = false;\n            }\n        } while (success);\n        return {\n            matched: true,\n            position: i,\n            value: values\n        };\n    };\n}\nfunction takeUntil(p,\ntest) {\n    return takeWhile(p, (value, n, data, i, j) => !test(value, n, data, i, j));\n}\nfunction takeWhileP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => pTest(data, i).matched);\n}\nfunction takeUntilP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => !pTest(data, i).matched);\n}\nfunction many(p) {\n    return takeWhile(p, () => true);\n}\nfunction many1(p) {\n    return ab(p, many(p), (head, tail) => [head, ...tail]);\n}\nfunction ab(pa, pb, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapInner(pb(data, ma.position), (vb, j) => join(ma.value, vb, data, i, j)));\n}\nfunction left(pa, pb) {\n    return ab(pa, pb, (va) => va);\n}\nfunction right(pa, pb) {\n    return ab(pa, pb, (va, vb) => vb);\n}\nfunction abc(pa, pb, pc, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapOuter(pb(data, ma.position), (mb) => mapInner(pc(data, mb.position), (vc, j) => join(ma.value, mb.value, vc, data, i, j))));\n}\nfunction middle(pa, pb, pc) {\n    return abc(pa, pb, pc, (ra, rb) => rb);\n}\nfunction all(...ps) {\n    return (data, i) => {\n        const result = [];\n        let position = i;\n        for (const p of ps) {\n            const r1 = p(data, position);\n            if (r1.matched) {\n                result.push(r1.value);\n                position = r1.position;\n            }\n            else {\n                return { matched: false };\n            }\n        }\n        return {\n            matched: true,\n            position: position,\n            value: result\n        };\n    };\n}\nfunction skip(...ps) {\n    return map(all(...ps), () => null);\n}\nfunction flatten(...ps) {\n    return flatten1(all(...ps));\n}\nfunction flatten1(p) {\n    return map(p, (vs) => vs.flatMap((v) => v));\n}\nfunction sepBy1(pValue, pSep) {\n    return ab(pValue, many(right(pSep, pValue)), (head, tail) => [head, ...tail]);\n}\nfunction sepBy(pValue, pSep) {\n    return otherwise(sepBy1(pValue, pSep), emit([]));\n}\nfunction chainReduce(acc,\nf) {\n    return (data, i) => {\n        let loop = true;\n        let acc1 = acc;\n        let pos = i;\n        do {\n            const r = f(acc1, data, pos)(data, pos);\n            if (r.matched) {\n                acc1 = r.value;\n                pos = r.position;\n            }\n            else {\n                loop = false;\n            }\n        } while (loop);\n        return {\n            matched: true,\n            position: pos,\n            value: acc1\n        };\n    };\n}\nfunction reduceLeft(acc, p,\nreducer) {\n    return chainReduce(acc, (acc) => map(p, (v, data, i, j) => reducer(acc, v, data, i, j)));\n}\nfunction reduceRight(p, acc,\nreducer) {\n    return map(many(p), (vs, data, i, j) => vs.reduceRight((acc, v) => reducer(v, acc, data, i, j), acc));\n}\nfunction leftAssoc1(pLeft, pOper) {\n    return chain(pLeft, (v0) => reduceLeft(v0, pOper, (acc, f) => f(acc)));\n}\nfunction rightAssoc1(pOper, pRight) {\n    return ab(reduceRight(pOper, (y) => y, (f, acc) => (y) => f(acc(y))), pRight, (f, v) => f(v));\n}\nfunction leftAssoc2(pLeft, pOper, pRight) {\n    return chain(pLeft, (v0) => reduceLeft(v0, ab(pOper, pRight, (f, y) => [f, y]), (acc, [f, y]) => f(acc, y)));\n}\nfunction rightAssoc2(pLeft, pOper, pRight) {\n    return ab(reduceRight(ab(pLeft, pOper, (x, f) => [x, f]), (y) => y, ([x, f], acc) => (y) => f(x, acc(y))), pRight, (f, v) => f(v));\n}\nfunction condition(cond, pTrue, pFalse) {\n    return (data, i) => (cond(data, i))\n        ? pTrue(data, i)\n        : pFalse(data, i);\n}\nfunction decide(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => m1.value(data, m1.position));\n}\nfunction chain(p,\nf) {\n    return (data, i) => mapOuter(p(data, i), (m1) => f(m1.value, data, i, m1.position)(data, m1.position));\n}\nfunction ahead(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => ({\n        matched: true,\n        position: i,\n        value: m1.value\n    }));\n}\nfunction recursive(f) {\n    return function (data, i) {\n        return f()(data, i);\n    };\n}\nfunction start(data, i) {\n    return (i !== 0)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction end(data, i) {\n    return (i < data.tokens.length)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction remainingTokensNumber(data, i) {\n    return data.tokens.length - i;\n}\nfunction parserPosition(data, i, formatToken, contextTokens = 3) {\n    const len = data.tokens.length;\n    const lowIndex = clamp(0, i - contextTokens, len - contextTokens);\n    const highIndex = clamp(contextTokens, i + 1 + contextTokens, len);\n    const tokensSlice = data.tokens.slice(lowIndex, highIndex);\n    const lines = [];\n    const indexWidth = String(highIndex - 1).length + 1;\n    if (i < 0) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    if (0 < lowIndex) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    for (let j = 0; j < tokensSlice.length; j++) {\n        const index = lowIndex + j;\n        lines.push(`${String(index).padStart(indexWidth)} ${(index === i ? '>' : ' ')} ${escapeWhitespace(formatToken(tokensSlice[j]))}`);\n    }\n    if (highIndex < len) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    if (len <= i) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    return lines.join('\\n');\n}\nfunction parse(parser, tokens, options, formatToken = JSON.stringify) {\n    const data = { tokens: tokens, options: options };\n    const result = parser(data, 0);\n    if (!result.matched) {\n        throw new Error('No match');\n    }\n    if (result.position < data.tokens.length) {\n        throw new Error(`Partial match. Parsing stopped at:\\n${parserPosition(data, result.position, formatToken)}`);\n    }\n    return result.value;\n}\nfunction tryParse(parser, tokens, options) {\n    const result = parser({ tokens: tokens, options: options }, 0);\n    return (result.matched)\n        ? result.value\n        : undefined;\n}\nfunction match(matcher, tokens, options) {\n    const result = matcher({ tokens: tokens, options: options }, 0);\n    return result.value;\n}\n\nexport { ab, abc, action, ahead, all, all as and, any, chain, chainReduce, choice, condition, decide, skip as discard, otherwise as eitherOr, emit, end, end as eof, error, fail, flatten, flatten1, left, leftAssoc1, leftAssoc2, longest, ahead as lookAhead, make, many, many1, map, map1, match, middle, not, emit as of, option, choice as or, otherwise, parse, parserPosition, peek, recursive, reduceLeft, reduceRight, remainingTokensNumber, right, rightAssoc1, rightAssoc2, satisfy, sepBy, sepBy1, skip, many1 as some, start, takeUntil, takeUntilP, takeWhile, takeWhileP, token, tryParse };\n", "import { createLexer } from 'leac';\nimport * as p from 'peberminta';\n\nvar ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst ws = `(?:[ \\\\t\\\\r\\\\n\\\\f]*)`;\nconst nl = `(?:\\\\n|\\\\r\\\\n|\\\\r|\\\\f)`;\nconst nonascii = `[^\\\\x00-\\\\x7F]`;\nconst unicode = `(?:\\\\\\\\[0-9a-f]{1,6}(?:\\\\r\\\\n|[ \\\\n\\\\r\\\\t\\\\f])?)`;\nconst escape = `(?:\\\\\\\\[^\\\\n\\\\r\\\\f0-9a-f])`;\nconst nmstart = `(?:[_a-z]|${nonascii}|${unicode}|${escape})`;\nconst nmchar = `(?:[_a-z0-9-]|${nonascii}|${unicode}|${escape})`;\nconst name = `(?:${nmchar}+)`;\nconst ident = `(?:[-]?${nmstart}${nmchar}*)`;\nconst string1 = `'([^\\\\n\\\\r\\\\f\\\\\\\\']|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*'`;\nconst string2 = `\"([^\\\\n\\\\r\\\\f\\\\\\\\\"]|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*\"`;\nconst lexSelector = createLexer([\n    { name: 'ws', regex: new RegExp(ws) },\n    { name: 'hash', regex: new RegExp(`#${name}`, 'i') },\n    { name: 'ident', regex: new RegExp(ident, 'i') },\n    { name: 'str1', regex: new RegExp(string1, 'i') },\n    { name: 'str2', regex: new RegExp(string2, 'i') },\n    { name: '*' },\n    { name: '.' },\n    { name: ',' },\n    { name: '[' },\n    { name: ']' },\n    { name: '=' },\n    { name: '>' },\n    { name: '|' },\n    { name: '+' },\n    { name: '~' },\n    { name: '^' },\n    { name: '$' },\n]);\nconst lexEscapedString = createLexer([\n    { name: 'unicode', regex: new RegExp(unicode, 'i') },\n    { name: 'escape', regex: new RegExp(escape, 'i') },\n    { name: 'any', regex: new RegExp('[\\\\s\\\\S]', 'i') }\n]);\nfunction sumSpec([a0, a1, a2], [b0, b1, b2]) {\n    return [a0 + b0, a1 + b1, a2 + b2];\n}\nfunction sumAllSpec(ss) {\n    return ss.reduce(sumSpec, [0, 0, 0]);\n}\nconst unicodeEscapedSequence_ = p.token((t) => t.name === 'unicode' ? String.fromCodePoint(parseInt(t.text.slice(1), 16)) : undefined);\nconst escapedSequence_ = p.token((t) => t.name === 'escape' ? t.text.slice(1) : undefined);\nconst anyChar_ = p.token((t) => t.name === 'any' ? t.text : undefined);\nconst escapedString_ = p.map(p.many(p.or(unicodeEscapedSequence_, escapedSequence_, anyChar_)), (cs) => cs.join(''));\nfunction unescape(escapedString) {\n    const lexerResult = lexEscapedString(escapedString);\n    const result = escapedString_({ tokens: lexerResult.tokens, options: undefined }, 0);\n    return result.value;\n}\nfunction literal(name) {\n    return p.token((t) => t.name === name ? true : undefined);\n}\nconst whitespace_ = p.token((t) => t.name === 'ws' ? null : undefined);\nconst optionalWhitespace_ = p.option(whitespace_, null);\nfunction optionallySpaced(parser) {\n    return p.middle(optionalWhitespace_, parser, optionalWhitespace_);\n}\nconst identifier_ = p.token((t) => t.name === 'ident' ? unescape(t.text) : undefined);\nconst hashId_ = p.token((t) => t.name === 'hash' ? unescape(t.text.slice(1)) : undefined);\nconst string_ = p.token((t) => t.name.startsWith('str') ? unescape(t.text.slice(1, -1)) : undefined);\nconst namespace_ = p.left(p.option(identifier_, ''), literal('|'));\nconst qualifiedName_ = p.eitherOr(p.ab(namespace_, identifier_, (ns, name) => ({ name: name, namespace: ns })), p.map(identifier_, (name) => ({ name: name, namespace: null })));\nconst uniSelector_ = p.eitherOr(p.ab(namespace_, literal('*'), (ns) => ({ type: 'universal', namespace: ns, specificity: [0, 0, 0] })), p.map(literal('*'), () => ({ type: 'universal', namespace: null, specificity: [0, 0, 0] })));\nconst tagSelector_ = p.map(qualifiedName_, ({ name, namespace }) => ({\n    type: 'tag',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 0, 1]\n}));\nconst classSelector_ = p.ab(literal('.'), identifier_, (fullstop, name) => ({\n    type: 'class',\n    name: name,\n    specificity: [0, 1, 0]\n}));\nconst idSelector_ = p.map(hashId_, (name) => ({\n    type: 'id',\n    name: name,\n    specificity: [1, 0, 0]\n}));\nconst attrModifier_ = p.token((t) => {\n    if (t.name === 'ident') {\n        if (t.text === 'i' || t.text === 'I') {\n            return 'i';\n        }\n        if (t.text === 's' || t.text === 'S') {\n            return 's';\n        }\n    }\n    return undefined;\n});\nconst attrValue_ = p.eitherOr(p.ab(string_, p.option(p.right(optionalWhitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })), p.ab(identifier_, p.option(p.right(whitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })));\nconst attrMatcher_ = p.choice(p.map(literal('='), () => '='), p.ab(literal('~'), literal('='), () => '~='), p.ab(literal('|'), literal('='), () => '|='), p.ab(literal('^'), literal('='), () => '^='), p.ab(literal('$'), literal('='), () => '$='), p.ab(literal('*'), literal('='), () => '*='));\nconst attrPresenceSelector_ = p.abc(literal('['), optionallySpaced(qualifiedName_), literal(']'), (lbr, { name, namespace }) => ({\n    type: 'attrPresence',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 1, 0]\n}));\nconst attrValueSelector_ = p.middle(literal('['), p.abc(optionallySpaced(qualifiedName_), attrMatcher_, optionallySpaced(attrValue_), ({ name, namespace }, matcher, { value, modifier }) => ({\n    type: 'attrValue',\n    name: name,\n    namespace: namespace,\n    matcher: matcher,\n    value: value,\n    modifier: modifier,\n    specificity: [0, 1, 0]\n})), literal(']'));\nconst attrSelector_ = p.eitherOr(attrPresenceSelector_, attrValueSelector_);\nconst typeSelector_ = p.eitherOr(uniSelector_, tagSelector_);\nconst subclassSelector_ = p.choice(idSelector_, classSelector_, attrSelector_);\nconst compoundSelector_ = p.map(p.eitherOr(p.flatten(typeSelector_, p.many(subclassSelector_)), p.many1(subclassSelector_)), (ss) => {\n    return {\n        type: 'compound',\n        list: ss,\n        specificity: sumAllSpec(ss.map(s => s.specificity))\n    };\n});\nconst combinator_ = p.choice(p.map(literal('>'), () => '>'), p.map(literal('+'), () => '+'), p.map(literal('~'), () => '~'), p.ab(literal('|'), literal('|'), () => '||'));\nconst combinatorSeparator_ = p.eitherOr(optionallySpaced(combinator_), p.map(whitespace_, () => ' '));\nconst complexSelector_ = p.leftAssoc2(compoundSelector_, p.map(combinatorSeparator_, (c) => (left, right) => ({\n    type: 'compound',\n    list: [...right.list, { type: 'combinator', combinator: c, left: left, specificity: left.specificity }],\n    specificity: sumSpec(left.specificity, right.specificity)\n})), compoundSelector_);\nconst listSelector_ = p.leftAssoc2(p.map(complexSelector_, (s) => ({ type: 'list', list: [s] })), p.map(optionallySpaced(literal(',')), () => (acc, next) => ({ type: 'list', list: [...acc.list, next] })), complexSelector_);\nfunction parse_(parser, str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n        throw new Error('Expected a selector string. Actual input is not a string!');\n    }\n    const lexerResult = lexSelector(str);\n    if (!lexerResult.complete) {\n        throw new Error(`The input \"${str}\" was only partially tokenized, stopped at offset ${lexerResult.offset}!\\n` +\n            prettyPrintPosition(str, lexerResult.offset));\n    }\n    const result = optionallySpaced(parser)({ tokens: lexerResult.tokens, options: undefined }, 0);\n    if (!result.matched) {\n        throw new Error(`No match for \"${str}\" input!`);\n    }\n    if (result.position < lexerResult.tokens.length) {\n        const token = lexerResult.tokens[result.position];\n        throw new Error(`The input \"${str}\" was only partially parsed, stopped at offset ${token.offset}!\\n` +\n            prettyPrintPosition(str, token.offset, token.len));\n    }\n    return result.value;\n}\nfunction prettyPrintPosition(str, offset, len = 1) {\n    return `${str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\u2409' : r ? '\\u240d' : '\\u240a')}\\n${''.padEnd(offset)}${'^'.repeat(len)}`;\n}\nfunction parse(str) {\n    return parse_(listSelector_, str);\n}\nfunction parse1(str) {\n    return parse_(complexSelector_, str);\n}\n\nfunction serialize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'universal':\n            return _serNs(selector.namespace) + '*';\n        case 'tag':\n            return _serNs(selector.namespace) + _serIdent(selector.name);\n        case 'class':\n            return '.' + _serIdent(selector.name);\n        case 'id':\n            return '#' + _serIdent(selector.name);\n        case 'attrPresence':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}]`;\n        case 'attrValue':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}${selector.matcher}\"${_serStr(selector.value)}\"${(selector.modifier ? selector.modifier : '')}]`;\n        case 'combinator':\n            return serialize(selector.left) + selector.combinator;\n        case 'compound':\n            return selector.list.reduce((acc, node) => {\n                if (node.type === 'combinator') {\n                    return serialize(node) + acc;\n                }\n                else {\n                    return acc + serialize(node);\n                }\n            }, '');\n        case 'list':\n            return selector.list.map(serialize).join(',');\n    }\n}\nfunction _serNs(ns) {\n    return (ns || ns === '')\n        ? _serIdent(ns) + '|'\n        : '';\n}\nfunction _codePoint(char) {\n    return `\\\\${char.codePointAt(0).toString(16)} `;\n}\nfunction _serIdent(str) {\n    return str.replace(\n    /(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\\x00-\\x7F])|(\\x00)|([\\x01-\\x1f]|\\x7f)|([\\s\\S])/g, (m, d1, d2, hy, safe, nl, ctrl, other) => d1 ? _codePoint(d1) :\n        d2 ? '-' + _codePoint(d2.slice(1)) :\n            hy ? '\\\\-' :\n                safe ? safe :\n                    nl ? '\\ufffd' :\n                        ctrl ? _codePoint(ctrl) :\n                            '\\\\' + other);\n}\nfunction _serStr(str) {\n    return str.replace(\n    /(\")|(\\\\)|(\\x00)|([\\x01-\\x1f]|\\x7f)/g, (m, dq, bs, nl, ctrl) => dq ? '\\\\\"' :\n        bs ? '\\\\\\\\' :\n            nl ? '\\ufffd' :\n                _codePoint(ctrl));\n}\nfunction normalize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'compound': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => _compareArrays(_getSelectorPriority(a), _getSelectorPriority(b)));\n            break;\n        }\n        case 'combinator': {\n            normalize(selector.left);\n            break;\n        }\n        case 'list': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => (serialize(a) < serialize(b)) ? -1 : 1);\n            break;\n        }\n    }\n    return selector;\n}\nfunction _getSelectorPriority(selector) {\n    switch (selector.type) {\n        case 'universal':\n            return [1];\n        case 'tag':\n            return [1];\n        case 'id':\n            return [2];\n        case 'class':\n            return [3, selector.name];\n        case 'attrPresence':\n            return [4, serialize(selector)];\n        case 'attrValue':\n            return [5, serialize(selector)];\n        case 'combinator':\n            return [15, serialize(selector)];\n    }\n}\nfunction compareSelectors(a, b) {\n    return _compareArrays(a.specificity, b.specificity);\n}\nfunction compareSpecificity(a, b) {\n    return _compareArrays(a, b);\n}\nfunction _compareArrays(a, b) {\n    if (!Array.isArray(a) || !Array.isArray(b)) {\n        throw new Error('Arguments must be arrays.');\n    }\n    const shorter = (a.length < b.length) ? a.length : b.length;\n    for (let i = 0; i < shorter; i++) {\n        if (a[i] === b[i]) {\n            continue;\n        }\n        return (a[i] < b[i]) ? -1 : 1;\n    }\n    return a.length - b.length;\n}\n\nexport { ast as Ast, compareSelectors, compareSpecificity, normalize, parse, parse1, serialize };\n", "import * as parseley from 'parseley';\nimport { compareSpecificity } from 'parseley';\n\nvar Ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar Types = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst treeify = (nodes) => '▽\\n' + treeifyArray(nodes, thinLines);\nconst thinLines = [['├─', '│ '], ['└─', '  ']];\nconst heavyLines = [['┠─', '┃ '], ['┖─', '  ']];\nconst doubleLines = [['╟─', '║ '], ['╙─', '  ']];\nfunction treeifyArray(nodes, tpl = heavyLines) {\n    return prefixItems(tpl, nodes.map(n => treeifyNode(n)));\n}\nfunction treeifyNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const vctr = node.valueContainer;\n            return `◁ #${vctr.index} ${JSON.stringify(vctr.specificity)} ${vctr.value}`;\n        }\n        case 'tagName':\n            return `◻ Tag name\\n${treeifyArray(node.variants, doubleLines)}`;\n        case 'attrValue':\n            return `▣ Attr value: ${node.name}\\n${treeifyArray(node.matchers, doubleLines)}`;\n        case 'attrPresence':\n            return `◨ Attr presence: ${node.name}\\n${treeifyArray(node.cont)}`;\n        case 'pushElement':\n            return `◉ Push element: ${node.combinator}\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'popElement':\n            return `◌ Pop element\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'variant':\n            return `◇ = ${node.value}\\n${treeifyArray(node.cont)}`;\n        case 'matcher':\n            return `◈ ${node.matcher} \"${node.value}\"${node.modifier || ''}\\n${treeifyArray(node.cont)}`;\n    }\n}\nfunction prefixItems(tpl, items) {\n    return items\n        .map((item, i, { length }) => prefixItem(tpl, item, i === length - 1))\n        .join('\\n');\n}\nfunction prefixItem(tpl, item, tail = true) {\n    const tpl1 = tpl[tail ? 1 : 0];\n    return tpl1[0] + item.split('\\n').join('\\n' + tpl1[1]);\n}\n\nvar TreeifyBuilder = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    treeify: treeify\n});\n\nclass DecisionTree {\n    constructor(input) {\n        this.branches = weave(toAstTerminalPairs(input));\n    }\n    build(builder) {\n        return builder(this.branches);\n    }\n}\nfunction toAstTerminalPairs(array) {\n    const len = array.length;\n    const results = new Array(len);\n    for (let i = 0; i < len; i++) {\n        const [selectorString, val] = array[i];\n        const ast = preprocess(parseley.parse1(selectorString));\n        results[i] = {\n            ast: ast,\n            terminal: {\n                type: 'terminal',\n                valueContainer: { index: i, value: val, specificity: ast.specificity }\n            }\n        };\n    }\n    return results;\n}\nfunction preprocess(ast) {\n    reduceSelectorVariants(ast);\n    parseley.normalize(ast);\n    return ast;\n}\nfunction reduceSelectorVariants(ast) {\n    const newList = [];\n    ast.list.forEach(sel => {\n        switch (sel.type) {\n            case 'class':\n                newList.push({\n                    matcher: '~=',\n                    modifier: null,\n                    name: 'class',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'id':\n                newList.push({\n                    matcher: '=',\n                    modifier: null,\n                    name: 'id',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'combinator':\n                reduceSelectorVariants(sel.left);\n                newList.push(sel);\n                break;\n            case 'universal':\n                break;\n            default:\n                newList.push(sel);\n                break;\n        }\n    });\n    ast.list = newList;\n}\nfunction weave(items) {\n    const branches = [];\n    while (items.length) {\n        const topKind = findTopKey(items, (sel) => true, getSelectorKind);\n        const { matches, nonmatches, empty } = breakByKind(items, topKind);\n        items = nonmatches;\n        if (matches.length) {\n            branches.push(branchOfKind(topKind, matches));\n        }\n        if (empty.length) {\n            branches.push(...terminate(empty));\n        }\n    }\n    return branches;\n}\nfunction terminate(items) {\n    const results = [];\n    for (const item of items) {\n        const terminal = item.terminal;\n        if (terminal.type === 'terminal') {\n            results.push(terminal);\n        }\n        else {\n            const { matches, rest } = partition(terminal.cont, (node) => node.type === 'terminal');\n            matches.forEach((node) => results.push(node));\n            if (rest.length) {\n                terminal.cont = rest;\n                results.push(terminal);\n            }\n        }\n    }\n    return results;\n}\nfunction breakByKind(items, selectedKind) {\n    const matches = [];\n    const nonmatches = [];\n    const empty = [];\n    for (const item of items) {\n        const simpsels = item.ast.list;\n        if (simpsels.length) {\n            const isMatch = simpsels.some(node => getSelectorKind(node) === selectedKind);\n            (isMatch ? matches : nonmatches).push(item);\n        }\n        else {\n            empty.push(item);\n        }\n    }\n    return { matches, nonmatches, empty };\n}\nfunction getSelectorKind(sel) {\n    switch (sel.type) {\n        case 'attrPresence':\n            return `attrPresence ${sel.name}`;\n        case 'attrValue':\n            return `attrValue ${sel.name}`;\n        case 'combinator':\n            return `combinator ${sel.combinator}`;\n        default:\n            return sel.type;\n    }\n}\nfunction branchOfKind(kind, items) {\n    if (kind === 'tag') {\n        return tagNameBranch(items);\n    }\n    if (kind.startsWith('attrValue ')) {\n        return attrValueBranch(kind.substring(10), items);\n    }\n    if (kind.startsWith('attrPresence ')) {\n        return attrPresenceBranch(kind.substring(13), items);\n    }\n    if (kind === 'combinator >') {\n        return combinatorBranch('>', items);\n    }\n    if (kind === 'combinator +') {\n        return combinatorBranch('+', items);\n    }\n    throw new Error(`Unsupported selector kind: ${kind}`);\n}\nfunction tagNameBranch(items) {\n    const groups = spliceAndGroup(items, (x) => x.type === 'tag', (x) => x.name);\n    const variants = Object.entries(groups).map(([name, group]) => ({\n        type: 'variant',\n        value: name,\n        cont: weave(group.items)\n    }));\n    return {\n        type: 'tagName',\n        variants: variants\n    };\n}\nfunction attrPresenceBranch(name, items) {\n    for (const item of items) {\n        spliceSimpleSelector(item, (x) => (x.type === 'attrPresence') && (x.name === name));\n    }\n    return {\n        type: 'attrPresence',\n        name: name,\n        cont: weave(items)\n    };\n}\nfunction attrValueBranch(name, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'attrValue') && (x.name === name), (x) => `${x.matcher} ${x.modifier || ''} ${x.value}`);\n    const matchers = [];\n    for (const group of Object.values(groups)) {\n        const sel = group.oneSimpleSelector;\n        const predicate = getAttrPredicate(sel);\n        const continuation = weave(group.items);\n        matchers.push({\n            type: 'matcher',\n            matcher: sel.matcher,\n            modifier: sel.modifier,\n            value: sel.value,\n            predicate: predicate,\n            cont: continuation\n        });\n    }\n    return {\n        type: 'attrValue',\n        name: name,\n        matchers: matchers\n    };\n}\nfunction getAttrPredicate(sel) {\n    if (sel.modifier === 'i') {\n        const expected = sel.value.toLowerCase();\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual.toLowerCase();\n            case '~=':\n                return (actual) => actual.toLowerCase().split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.toLowerCase().startsWith(expected);\n            case '$=':\n                return (actual) => actual.toLowerCase().endsWith(expected);\n            case '*=':\n                return (actual) => actual.toLowerCase().includes(expected);\n            case '|=':\n                return (actual) => {\n                    const lower = actual.toLowerCase();\n                    return (expected === lower) || (lower.startsWith(expected) && lower[expected.length] === '-');\n                };\n        }\n    }\n    else {\n        const expected = sel.value;\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual;\n            case '~=':\n                return (actual) => actual.split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.startsWith(expected);\n            case '$=':\n                return (actual) => actual.endsWith(expected);\n            case '*=':\n                return (actual) => actual.includes(expected);\n            case '|=':\n                return (actual) => (expected === actual) || (actual.startsWith(expected) && actual[expected.length] === '-');\n        }\n    }\n}\nfunction combinatorBranch(combinator, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'combinator') && (x.combinator === combinator), (x) => parseley.serialize(x.left));\n    const leftItems = [];\n    for (const group of Object.values(groups)) {\n        const rightCont = weave(group.items);\n        const leftAst = group.oneSimpleSelector.left;\n        leftItems.push({\n            ast: leftAst,\n            terminal: { type: 'popElement', cont: rightCont }\n        });\n    }\n    return {\n        type: 'pushElement',\n        combinator: combinator,\n        cont: weave(leftItems)\n    };\n}\nfunction spliceAndGroup(items, predicate, keyCallback) {\n    const groups = {};\n    while (items.length) {\n        const bestKey = findTopKey(items, predicate, keyCallback);\n        const bestKeyPredicate = (sel) => predicate(sel) && keyCallback(sel) === bestKey;\n        const hasBestKeyPredicate = (item) => item.ast.list.some(bestKeyPredicate);\n        const { matches, rest } = partition1(items, hasBestKeyPredicate);\n        let oneSimpleSelector = null;\n        for (const item of matches) {\n            const splicedNode = spliceSimpleSelector(item, bestKeyPredicate);\n            if (!oneSimpleSelector) {\n                oneSimpleSelector = splicedNode;\n            }\n        }\n        if (oneSimpleSelector == null) {\n            throw new Error('No simple selector is found.');\n        }\n        groups[bestKey] = { oneSimpleSelector: oneSimpleSelector, items: matches };\n        items = rest;\n    }\n    return groups;\n}\nfunction spliceSimpleSelector(item, predicate) {\n    const simpsels = item.ast.list;\n    const matches = new Array(simpsels.length);\n    let firstIndex = -1;\n    for (let i = simpsels.length; i-- > 0;) {\n        if (predicate(simpsels[i])) {\n            matches[i] = true;\n            firstIndex = i;\n        }\n    }\n    if (firstIndex == -1) {\n        throw new Error(`Couldn't find the required simple selector.`);\n    }\n    const result = simpsels[firstIndex];\n    item.ast.list = simpsels.filter((sel, i) => !matches[i]);\n    return result;\n}\nfunction findTopKey(items, predicate, keyCallback) {\n    const candidates = {};\n    for (const item of items) {\n        const candidates1 = {};\n        for (const node of item.ast.list.filter(predicate)) {\n            candidates1[keyCallback(node)] = true;\n        }\n        for (const key of Object.keys(candidates1)) {\n            if (candidates[key]) {\n                candidates[key]++;\n            }\n            else {\n                candidates[key] = 1;\n            }\n        }\n    }\n    let topKind = '';\n    let topCounter = 0;\n    for (const entry of Object.entries(candidates)) {\n        if (entry[1] > topCounter) {\n            topKind = entry[0];\n            topCounter = entry[1];\n        }\n    }\n    return topKind;\n}\nfunction partition(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\nfunction partition1(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\n\nclass Picker {\n    constructor(f) {\n        this.f = f;\n    }\n    pickAll(el) {\n        return this.f(el);\n    }\n    pick1(el, preferFirst = false) {\n        const results = this.f(el);\n        const len = results.length;\n        if (len === 0) {\n            return null;\n        }\n        if (len === 1) {\n            return results[0].value;\n        }\n        const comparator = (preferFirst)\n            ? comparatorPreferFirst\n            : comparatorPreferLast;\n        let result = results[0];\n        for (let i = 1; i < len; i++) {\n            const next = results[i];\n            if (comparator(result, next)) {\n                result = next;\n            }\n        }\n        return result.value;\n    }\n}\nfunction comparatorPreferFirst(acc, next) {\n    const diff = compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index < acc.index);\n}\nfunction comparatorPreferLast(acc, next) {\n    const diff = compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index > acc.index);\n}\n\nexport { Ast, DecisionTree, Picker, TreeifyBuilder as Treeify, Types };\n", "import { isTag } from 'domhandler';\nimport { Picker } from 'selderee';\n\nfunction hp2Builder(nodes) {\n    return new Picker(handleArray(nodes));\n}\nfunction handleArray(nodes) {\n    const matchers = nodes.map(handleNode);\n    return (el, ...tail) => matchers.flatMap(m => m(el, ...tail));\n}\nfunction handleNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const result = [node.valueContainer];\n            return (el, ...tail) => result;\n        }\n        case 'tagName':\n            return handleTagName(node);\n        case 'attrValue':\n            return handleAttrValueName(node);\n        case 'attrPresence':\n            return handleAttrPresenceName(node);\n        case 'pushElement':\n            return handlePushElementNode(node);\n        case 'popElement':\n            return handlePopElementNode(node);\n    }\n}\nfunction handleTagName(node) {\n    const variants = {};\n    for (const variant of node.variants) {\n        variants[variant.value] = handleArray(variant.cont);\n    }\n    return (el, ...tail) => {\n        const continuation = variants[el.name];\n        return (continuation) ? continuation(el, ...tail) : [];\n    };\n}\nfunction handleAttrPresenceName(node) {\n    const attrName = node.name;\n    const continuation = handleArray(node.cont);\n    return (el, ...tail) => (Object.prototype.hasOwnProperty.call(el.attribs, attrName))\n        ? continuation(el, ...tail)\n        : [];\n}\nfunction handleAttrValueName(node) {\n    const callbacks = [];\n    for (const matcher of node.matchers) {\n        const predicate = matcher.predicate;\n        const continuation = handleArray(matcher.cont);\n        callbacks.push((attr, el, ...tail) => (predicate(attr) ? continuation(el, ...tail) : []));\n    }\n    const attrName = node.name;\n    return (el, ...tail) => {\n        const attr = el.attribs[attrName];\n        return (attr || attr === '')\n            ? callbacks.flatMap(cb => cb(attr, el, ...tail))\n            : [];\n    };\n}\nfunction handlePushElementNode(node) {\n    const continuation = handleArray(node.cont);\n    const leftElementGetter = (node.combinator === '+')\n        ? getPrecedingElement\n        : getParentElement;\n    return (el, ...tail) => {\n        const next = leftElementGetter(el);\n        if (next === null) {\n            return [];\n        }\n        return continuation(next, el, ...tail);\n    };\n}\nconst getPrecedingElement = (el) => {\n    const prev = el.prev;\n    if (prev === null) {\n        return null;\n    }\n    return (isTag(prev)) ? prev : getPrecedingElement(prev);\n};\nconst getParentElement = (el) => {\n    const parent = el.parent;\n    return (parent && isTag(parent)) ? parent : null;\n};\nfunction handlePopElementNode(node) {\n    const continuation = handleArray(node.cont);\n    return (el, next, ...tail) => continuation(next, ...tail);\n}\n\nexport { hp2Builder };\n", null, null, null, null, null, null, null, null, null, "export const elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map((val) => [val.toLowerCase(), val]));\nexport const attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map((val) => [val.toLowerCase(), val]));\n", "/*\n * Module dependencies\n */\nimport * as ElementType from \"domelementtype\";\nimport { encodeXML, escapeAttribute, escapeText } from \"entities\";\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\nimport { elementNames, attributeNames } from \"./foreignNames.js\";\nconst unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    const encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? encodeXML\n            : escapeAttribute;\n    return Object.keys(attributes)\n        .map((key) => {\n        var _a, _b;\n        const value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return `${key}=\"${encode(value)}\"`;\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nconst singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nexport function render(node, options = {}) {\n    const nodes = \"length\" in node ? node : [node];\n    let output = \"\";\n    for (let i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\nexport default render;\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case ElementType.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case ElementType.Doctype:\n        case ElementType.Directive:\n            return renderDirective(node);\n        case ElementType.Comment:\n            return renderComment(node);\n        case ElementType.CDATA:\n            return renderCdata(node);\n        case ElementType.Script:\n        case ElementType.Style:\n        case ElementType.Tag:\n            return renderTag(node, options);\n        case ElementType.Text:\n            return renderText(node, options);\n    }\n}\nconst foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nconst foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = { ...opts, xmlMode: false };\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = { ...opts, xmlMode: \"foreign\" };\n    }\n    let tag = `<${elem.name}`;\n    const attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += ` ${attribs}`;\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += `</${elem.name}>`;\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return `<${elem.data}>`;\n}\nfunction renderText(elem, opts) {\n    var _a;\n    let data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? encodeXML(data)\n                : escapeText(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return `<![CDATA[${elem.children[0].data}]]>`;\n}\nfunction renderComment(elem) {\n    return `<!--${elem.data}-->`;\n}\n", null, null, "import { hp2Builder } from '@selderee/plugin-htmlparser2';\nimport { parseDocument } from 'htmlparser2';\nimport { DecisionTree } from 'selderee';\nimport merge from 'deepmerge';\nimport { render } from 'dom-serializer';\n\n/**\n * Make a recursive function that will only run to a given depth\n * and switches to an alternative function at that depth. \\\n * No limitation if `n` is `undefined` (Just wraps `f` in that case).\n *\n * @param   { number | undefined } n   Allowed depth of recursion. `undefined` for no limitation.\n * @param   { Function }           f   Function that accepts recursive callback as the first argument.\n * @param   { Function }           [g] Function to run instead, when maximum depth was reached. Do nothing by default.\n * @returns { Function }\n */\nfunction limitedDepthRecursive (n, f, g = () => undefined) {\n  if (n === undefined) {\n    const f1 = function (...args) { return f(f1, ...args); };\n    return f1;\n  }\n  if (n >= 0) {\n    return function (...args) { return f(limitedDepthRecursive(n - 1, f, g), ...args); };\n  }\n  return g;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from each side.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacter (str, char) {\n  let start = 0;\n  let end = str.length;\n  while (start < end && str[start] === char) { ++start; }\n  while (end > start && str[end - 1] === char) { --end; }\n  return (start > 0 || end < str.length)\n    ? str.substring(start, end)\n    : str;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from the end only.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacterEnd (str, char) {\n  let end = str.length;\n  while (end > 0 && str[end - 1] === char) { --end; }\n  return (end < str.length)\n    ? str.substring(0, end)\n    : str;\n}\n\n/**\n * Return a new string will all characters replaced with unicode escape sequences.\n * This extreme kind of escaping can used to be safely compose regular expressions.\n *\n * @param { string } str A string to escape.\n * @returns { string } A string of unicode escape sequences.\n */\nfunction unicodeEscape (str) {\n  return str.replace(/[\\s\\S]/g, c => '\\\\u' + c.charCodeAt().toString(16).padStart(4, '0'));\n}\n\n/**\n * Deduplicate an array by a given key callback.\n * Item properties are merged recursively and with the preference for last defined values.\n * Of items with the same key, merged item takes the place of the last item,\n * others are omitted.\n *\n * @param { any[] } items An array to deduplicate.\n * @param { (x: any) => string } getKey Callback to get a value that distinguishes unique items.\n * @returns { any[] }\n */\nfunction mergeDuplicatesPreferLast (items, getKey) {\n  const map = new Map();\n  for (let i = items.length; i-- > 0;) {\n    const item = items[i];\n    const key = getKey(item);\n    map.set(\n      key,\n      (map.has(key))\n        ? merge(item, map.get(key), { arrayMerge: overwriteMerge$1 })\n        : item\n    );\n  }\n  return [...map.values()].reverse();\n}\n\nconst overwriteMerge$1 = (acc, src, options) => [...src];\n\n/**\n * Get a nested property from an object.\n *\n * @param   { object }   obj  The object to query for the value.\n * @param   { string[] } path The path to the property.\n * @returns { any }\n */\nfunction get (obj, path) {\n  for (const key of path) {\n    if (!obj) { return undefined; }\n    obj = obj[key];\n  }\n  return obj;\n}\n\n/**\n * Convert a number into alphabetic sequence representation (Sequence without zeroes).\n *\n * For example: `a, ..., z, aa, ..., zz, aaa, ...`.\n *\n * @param   { number } num              Number to convert. Must be >= 1.\n * @param   { string } [baseChar = 'a'] Character for 1 in the sequence.\n * @param   { number } [base = 26]      Number of characters in the sequence.\n * @returns { string }\n */\nfunction numberToLetterSequence (num, baseChar = 'a', base = 26) {\n  const digits = [];\n  do {\n    num -= 1;\n    digits.push(num % base);\n    num = (num / base) >> 0; // quick `floor`\n  } while (num > 0);\n  const baseCode = baseChar.charCodeAt(0);\n  return digits\n    .reverse()\n    .map(n => String.fromCharCode(baseCode + n))\n    .join('');\n}\n\nconst I = ['I', 'X', 'C', 'M'];\nconst V = ['V', 'L', 'D'];\n\n/**\n * Convert a number to it's Roman representation. No large numbers extension.\n *\n * @param   { number } num Number to convert. `0 < num <= 3999`.\n * @returns { string }\n */\nfunction numberToRoman (num) {\n  return [...(num) + '']\n    .map(n => +n)\n    .reverse()\n    .map((v, i) => ((v % 5 < 4)\n      ? (v < 5 ? '' : V[i]) + I[i].repeat(v % 5)\n      : I[i] + (v < 5 ? V[i] : I[i + 1])))\n    .reverse()\n    .join('');\n}\n\n/**\n * Helps to build text from words.\n */\nclass InlineTextBuilder {\n  /**\n   * Creates an instance of InlineTextBuilder.\n   *\n   * If `maxLineLength` is not provided then it is either `options.wordwrap` or unlimited.\n   *\n   * @param { Options } options           HtmlToText options.\n   * @param { number }  [ maxLineLength ] This builder will try to wrap text to fit this line length.\n   */\n  constructor (options, maxLineLength = undefined) {\n    /** @type { string[][] } */\n    this.lines = [];\n    /** @type { string[] }   */\n    this.nextLineWords = [];\n    this.maxLineLength = maxLineLength || options.wordwrap || Number.MAX_VALUE;\n    this.nextLineAvailableChars = this.maxLineLength;\n    this.wrapCharacters = get(options, ['longWordSplit', 'wrapCharacters']) || [];\n    this.forceWrapOnLimit = get(options, ['longWordSplit', 'forceWrapOnLimit']) || false;\n\n    this.stashedSpace = false;\n    this.wordBreakOpportunity = false;\n  }\n\n  /**\n   * Add a new word.\n   *\n   * @param { string } word A word to add.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  pushWord (word, noWrap = false) {\n    if (this.nextLineAvailableChars <= 0 && !noWrap) {\n      this.startNewLine();\n    }\n    const isLineStart = this.nextLineWords.length === 0;\n    const cost = word.length + (isLineStart ? 0 : 1);\n    if ((cost <= this.nextLineAvailableChars) || noWrap) { // Fits into available budget\n\n      this.nextLineWords.push(word);\n      this.nextLineAvailableChars -= cost;\n\n    } else { // Does not fit - try to split the word\n\n      // The word is moved to a new line - prefer to wrap between words.\n      const [first, ...rest] = this.splitLongWord(word);\n      if (!isLineStart) { this.startNewLine(); }\n      this.nextLineWords.push(first);\n      this.nextLineAvailableChars -= first.length;\n      for (const part of rest) {\n        this.startNewLine();\n        this.nextLineWords.push(part);\n        this.nextLineAvailableChars -= part.length;\n      }\n\n    }\n  }\n\n  /**\n   * Pop a word from the currently built line.\n   * This doesn't affect completed lines.\n   *\n   * @returns { string }\n   */\n  popWord () {\n    const lastWord = this.nextLineWords.pop();\n    if (lastWord !== undefined) {\n      const isLineStart = this.nextLineWords.length === 0;\n      const cost = lastWord.length + (isLineStart ? 0 : 1);\n      this.nextLineAvailableChars += cost;\n    }\n    return lastWord;\n  }\n\n  /**\n   * Concat a word to the last word already in the builder.\n   * Adds a new word in case there are no words yet in the last line.\n   *\n   * @param { string } word A word to be concatenated.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  concatWord (word, noWrap = false) {\n    if (this.wordBreakOpportunity && word.length > this.nextLineAvailableChars) {\n      this.pushWord(word, noWrap);\n      this.wordBreakOpportunity = false;\n    } else {\n      const lastWord = this.popWord();\n      this.pushWord((lastWord) ? lastWord.concat(word) : word, noWrap);\n    }\n  }\n\n  /**\n   * Add current line (and more empty lines if provided argument > 1) to the list of complete lines and start a new one.\n   *\n   * @param { number } n Number of line breaks that will be added to the resulting string.\n   */\n  startNewLine (n = 1) {\n    this.lines.push(this.nextLineWords);\n    if (n > 1) {\n      this.lines.push(...Array.from({ length: n - 1 }, () => []));\n    }\n    this.nextLineWords = [];\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * No words in this builder.\n   *\n   * @returns { boolean }\n   */\n  isEmpty () {\n    return this.lines.length === 0\n        && this.nextLineWords.length === 0;\n  }\n\n  clear () {\n    this.lines.length = 0;\n    this.nextLineWords.length = 0;\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * Join all lines of words inside the InlineTextBuilder into a complete string.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return [...this.lines, this.nextLineWords]\n      .map(words => words.join(' '))\n      .join('\\n');\n  }\n\n  /**\n   * Split a long word up to fit within the word wrap limit.\n   * Use either a character to split looking back from the word wrap limit,\n   * or truncate to the word wrap limit.\n   *\n   * @param   { string }   word Input word.\n   * @returns { string[] }      Parts of the word.\n   */\n  splitLongWord (word) {\n    const parts = [];\n    let idx = 0;\n    while (word.length > this.maxLineLength) {\n\n      const firstLine = word.substring(0, this.maxLineLength);\n      const remainingChars = word.substring(this.maxLineLength);\n\n      const splitIndex = firstLine.lastIndexOf(this.wrapCharacters[idx]);\n\n      if (splitIndex > -1) { // Found a character to split on\n\n        word = firstLine.substring(splitIndex + 1) + remainingChars;\n        parts.push(firstLine.substring(0, splitIndex + 1));\n\n      } else { // Not found a character to split on\n\n        idx++;\n        if (idx < this.wrapCharacters.length) { // There is next character to try\n\n          word = firstLine + remainingChars;\n\n        } else { // No more characters to try\n\n          if (this.forceWrapOnLimit) {\n            parts.push(firstLine);\n            word = remainingChars;\n            if (word.length > this.maxLineLength) {\n              continue;\n            }\n          } else {\n            word = firstLine + remainingChars;\n          }\n          break;\n\n        }\n\n      }\n\n    }\n    parts.push(word); // Add remaining part to array\n    return parts;\n  }\n}\n\n/* eslint-disable max-classes-per-file */\n\n\nclass StackItem {\n  constructor (next = null) { this.next = next; }\n\n  getRoot () { return (this.next) ? this.next : this; }\n}\n\nclass BlockStackItem extends StackItem {\n  constructor (options, next = null, leadingLineBreaks = 1, maxLineLength = undefined) {\n    super(next);\n    this.leadingLineBreaks = leadingLineBreaks;\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxLineLength);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass ListStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      interRowLineBreaks = 1,\n      leadingLineBreaks = 2,\n      maxLineLength = undefined,\n      maxPrefixLength = 0,\n      prefixAlign = 'left',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.maxPrefixLength = maxPrefixLength;\n    this.prefixAlign = prefixAlign;\n    this.interRowLineBreaks = interRowLineBreaks;\n  }\n}\n\nclass ListItemStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      leadingLineBreaks = 1,\n      maxLineLength = undefined,\n      prefix = '',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.prefix = prefix;\n  }\n}\n\nclass TableStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.rows = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableRowStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.cells = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableCellStackItem extends StackItem {\n  constructor (options, next = null, maxColumnWidth = undefined) {\n    super(next);\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxColumnWidth);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TransformerStackItem extends StackItem {\n  constructor (next = null, transform) {\n    super(next);\n    this.transform = transform;\n  }\n}\n\nfunction charactersToCodes (str) {\n  return [...str]\n    .map(c => '\\\\u' + c.charCodeAt(0).toString(16).padStart(4, '0'))\n    .join('');\n}\n\n/**\n * Helps to handle HTML whitespaces.\n *\n * @class WhitespaceProcessor\n */\nclass WhitespaceProcessor {\n\n  /**\n   * Creates an instance of WhitespaceProcessor.\n   *\n   * @param { Options } options    HtmlToText options.\n   * @memberof WhitespaceProcessor\n   */\n  constructor (options) {\n    this.whitespaceChars = (options.preserveNewlines)\n      ? options.whitespaceCharacters.replace(/\\n/g, '')\n      : options.whitespaceCharacters;\n    const whitespaceCodes = charactersToCodes(this.whitespaceChars);\n    this.leadingWhitespaceRe = new RegExp(`^[${whitespaceCodes}]`);\n    this.trailingWhitespaceRe = new RegExp(`[${whitespaceCodes}]$`);\n    this.allWhitespaceOrEmptyRe = new RegExp(`^[${whitespaceCodes}]*$`);\n    this.newlineOrNonWhitespaceRe = new RegExp(`(\\\\n|[^\\\\n${whitespaceCodes}])`, 'g');\n    this.newlineOrNonNewlineStringRe = new RegExp(`(\\\\n|[^\\\\n]+)`, 'g');\n\n    if (options.preserveNewlines) {\n\n      const wordOrNewlineRe = new RegExp(`\\\\n|[^\\\\n${whitespaceCodes}]+`, 'gm');\n\n      /**\n       * Shrink whitespaces and wrap text, add to the builder.\n       *\n       * @param { string }                  text              Input text.\n       * @param { InlineTextBuilder }       inlineTextBuilder A builder to receive processed text.\n       * @param { (str: string) => string } [ transform ]     A transform to be applied to words.\n       * @param { boolean }                 [noWrap] Don't wrap text even if the line is too long.\n       */\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordOrNewlineRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (m[0] === '\\n') {\n            inlineTextBuilder.startNewLine();\n          } else if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordOrNewlineRe.exec(text)) !== null) {\n            if (m[0] === '\\n') {\n              inlineTextBuilder.startNewLine();\n            } else {\n              inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n            }\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || (this.testTrailingWhitespace(text));\n        // No need to stash a space in case last added item was a new line,\n        // but that won't affect anything later anyway.\n      };\n\n    } else {\n\n      const wordRe = new RegExp(`[^${whitespaceCodes}]+`, 'g');\n\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordRe.exec(text)) !== null) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || this.testTrailingWhitespace(text);\n      };\n\n    }\n  }\n\n  /**\n   * Add text with only minimal processing.\n   * Everything between newlines considered a single word.\n   * No whitespace is trimmed.\n   * Not affected by preserveNewlines option - `\\n` always starts a new line.\n   *\n   * `noWrap` argument is `true` by default - this won't start a new line\n   * even if there is not enough space left in the current line.\n   *\n   * @param { string }            text              Input text.\n   * @param { InlineTextBuilder } inlineTextBuilder A builder to receive processed text.\n   * @param { boolean }           [noWrap] Don't wrap text even if the line is too long.\n   */\n  addLiteral (text, inlineTextBuilder, noWrap = true) {\n    if (!text) { return; }\n    const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n    let anyMatch = false;\n    let m = this.newlineOrNonNewlineStringRe.exec(text);\n    if (m) {\n      anyMatch = true;\n      if (m[0] === '\\n') {\n        inlineTextBuilder.startNewLine();\n      } else if (previouslyStashedSpace) {\n        inlineTextBuilder.pushWord(m[0], noWrap);\n      } else {\n        inlineTextBuilder.concatWord(m[0], noWrap);\n      }\n      while ((m = this.newlineOrNonNewlineStringRe.exec(text)) !== null) {\n        if (m[0] === '\\n') {\n          inlineTextBuilder.startNewLine();\n        } else {\n          inlineTextBuilder.pushWord(m[0], noWrap);\n        }\n      }\n    }\n    inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch);\n  }\n\n  /**\n   * Test whether the given text starts with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testLeadingWhitespace (text) {\n    return this.leadingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text ends with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testTrailingWhitespace (text) {\n    return this.trailingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text contains any non-whitespace characters.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testContainsWords (text) {\n    return !this.allWhitespaceOrEmptyRe.test(text);\n  }\n\n  /**\n   * Return the number of newlines if there are no words.\n   *\n   * If any word is found then return zero regardless of the actual number of newlines.\n   *\n   * @param   { string }  text  Input string.\n   * @returns { number }\n   */\n  countNewlinesNoWords (text) {\n    this.newlineOrNonWhitespaceRe.lastIndex = 0;\n    let counter = 0;\n    let match;\n    while ((match = this.newlineOrNonWhitespaceRe.exec(text)) !== null) {\n      if (match[0] === '\\n') {\n        counter++;\n      } else {\n        return 0;\n      }\n    }\n    return counter;\n  }\n\n}\n\n/**\n * Helps to build text from inline and block elements.\n *\n * @class BlockTextBuilder\n */\nclass BlockTextBuilder {\n\n  /**\n   * Creates an instance of BlockTextBuilder.\n   *\n   * @param { Options } options HtmlToText options.\n   * @param { import('selderee').Picker<DomNode, TagDefinition> } picker Selectors decision tree picker.\n   * @param { any} [metadata] Optional metadata for HTML document, for use in formatters.\n   */\n  constructor (options, picker, metadata = undefined) {\n    this.options = options;\n    this.picker = picker;\n    this.metadata = metadata;\n    this.whitespaceProcessor = new WhitespaceProcessor(options);\n    /** @type { StackItem } */\n    this._stackItem = new BlockStackItem(options);\n    /** @type { TransformerStackItem } */\n    this._wordTransformer = undefined;\n  }\n\n  /**\n   * Put a word-by-word transform function onto the transformations stack.\n   *\n   * Mainly used for uppercasing. Can be bypassed to add unformatted text such as URLs.\n   *\n   * Word transformations applied before wrapping.\n   *\n   * @param { (str: string) => string } wordTransform Word transformation function.\n   */\n  pushWordTransform (wordTransform) {\n    this._wordTransformer = new TransformerStackItem(this._wordTransformer, wordTransform);\n  }\n\n  /**\n   * Remove a function from the word transformations stack.\n   *\n   * @returns { (str: string) => string } A function that was removed.\n   */\n  popWordTransform () {\n    if (!this._wordTransformer) { return undefined; }\n    const transform = this._wordTransformer.transform;\n    this._wordTransformer = this._wordTransformer.next;\n    return transform;\n  }\n\n  /**\n   * Ignore wordwrap option in followup inline additions and disable automatic wrapping.\n   */\n  startNoWrap () {\n    this._stackItem.isNoWrap = true;\n  }\n\n  /**\n   * Return automatic wrapping to behavior defined by options.\n   */\n  stopNoWrap () {\n    this._stackItem.isNoWrap = false;\n  }\n\n  /** @returns { (str: string) => string } */\n  _getCombinedWordTransformer () {\n    const wt = (this._wordTransformer)\n      ? ((str) => applyTransformer(str, this._wordTransformer))\n      : undefined;\n    const ce = this.options.encodeCharacters;\n    return (wt)\n      ? ((ce) ? (str) => ce(wt(str)) : wt)\n      : ce;\n  }\n\n  _popStackItem () {\n    const item = this._stackItem;\n    this._stackItem = item.next;\n    return item;\n  }\n\n  /**\n   * Add a line break into currently built block.\n   */\n  addLineBreak () {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += '\\n';\n    } else {\n      this._stackItem.inlineTextBuilder.startNewLine();\n    }\n  }\n\n  /**\n   * Allow to break line in case directly following text will not fit.\n   */\n  addWordBreakOpportunity () {\n    if (\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    ) {\n      this._stackItem.inlineTextBuilder.wordBreakOpportunity = true;\n    }\n  }\n\n  /**\n   * Add a node inline into the currently built block.\n   *\n   * @param { string } str\n   * Text content of a node to add.\n   *\n   * @param { object } [param1]\n   * Object holding the parameters of the operation.\n   *\n   * @param { boolean } [param1.noWordTransform]\n   * Ignore word transformers if there are any.\n   * Don't encode characters as well.\n   * (Use this for things like URL addresses).\n   */\n  addInline (str, { noWordTransform = false } = {}) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (\n      str.length === 0 || // empty string\n      (\n        this._stackItem.stashedLineBreaks && // stashed linebreaks make whitespace irrelevant\n        !this.whitespaceProcessor.testContainsWords(str) // no words to add\n      )\n    ) { return; }\n\n    if (this.options.preserveNewlines) {\n      const newlinesNumber = this.whitespaceProcessor.countNewlinesNoWords(str);\n      if (newlinesNumber > 0) {\n        this._stackItem.inlineTextBuilder.startNewLine(newlinesNumber);\n        // keep stashedLineBreaks unchanged\n        return;\n      }\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.shrinkWrapAdd(\n      str,\n      this._stackItem.inlineTextBuilder,\n      (noWordTransform) ? undefined : this._getCombinedWordTransformer(),\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0; // inline text doesn't introduce line breaks\n  }\n\n  /**\n   * Add a string inline into the currently built block.\n   *\n   * Use this for markup elements that don't have to adhere\n   * to text layout rules.\n   *\n   * @param { string } str Text to add.\n   */\n  addLiteral (str) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (str.length === 0) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.addLiteral(\n      str,\n      this._stackItem.inlineTextBuilder,\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0;\n  }\n\n  /**\n   * Start building a new block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any preceding block.\n   *\n   * @param { number }  [param0.reservedLineLength]\n   * Reserve this number of characters on each line for block markup.\n   *\n   * @param { boolean } [param0.isPre]\n   * Should HTML whitespace be preserved inside this block.\n   */\n  openBlock ({ leadingLineBreaks = 1, reservedLineLength = 0, isPre = false } = {}) {\n    const maxLineLength = Math.max(20, this._stackItem.inlineTextBuilder.maxLineLength - reservedLineLength);\n    this._stackItem = new BlockStackItem(\n      this.options,\n      this._stackItem,\n      leadingLineBreaks,\n      maxLineLength\n    );\n    if (isPre) { this._stackItem.isPre = true; }\n  }\n\n  /**\n   * Finalize currently built block, add it's content to the parent block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any following block.\n   *\n   * @param { (str: string) => string } [param0.blockTransform]\n   * A function to transform the block text before adding to the parent block.\n   * This happens after word wrap and should be used in combination with reserved line length\n   * in order to keep line lengths correct.\n   * Used for whole block markup.\n   */\n  closeBlock ({ trailingLineBreaks = 1, blockTransform = undefined } = {}) {\n    const block = this._popStackItem();\n    const blockText = (blockTransform) ? blockTransform(getText(block)) : getText(block);\n    addText(this._stackItem, blockText, block.leadingLineBreaks, Math.max(block.stashedLineBreaks, trailingLineBreaks));\n  }\n\n  /**\n   * Start building a new list.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.maxPrefixLength]\n   * Length of the longest list item prefix.\n   * If not supplied or too small then list items won't be aligned properly.\n   *\n   * @param { 'left' | 'right' } [param0.prefixAlign]\n   * Specify how prefixes of different lengths have to be aligned\n   * within a column.\n   *\n   * @param { number } [param0.interRowLineBreaks]\n   * Minimum number of line breaks between list items.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any preceding block.\n   */\n  openList ({ maxPrefixLength = 0, prefixAlign = 'left', interRowLineBreaks = 1, leadingLineBreaks = 2 } = {}) {\n    this._stackItem = new ListStackItem(this.options, this._stackItem, {\n      interRowLineBreaks: interRowLineBreaks,\n      leadingLineBreaks: leadingLineBreaks,\n      maxLineLength: this._stackItem.inlineTextBuilder.maxLineLength,\n      maxPrefixLength: maxPrefixLength,\n      prefixAlign: prefixAlign\n    });\n  }\n\n  /**\n   * Start building a new list item.\n   *\n   * @param {object} param0\n   * Object holding the parameters of the list item.\n   *\n   * @param { string } [param0.prefix]\n   * Prefix for this list item (item number, bullet point, etc).\n   */\n  openListItem ({ prefix = '' } = {}) {\n    if (!(this._stackItem instanceof ListStackItem)) {\n      throw new Error('Can\\'t add a list item to something that is not a list! Check the formatter.');\n    }\n    const list = this._stackItem;\n    const prefixLength = Math.max(prefix.length, list.maxPrefixLength);\n    const maxLineLength = Math.max(20, list.inlineTextBuilder.maxLineLength - prefixLength);\n    this._stackItem = new ListItemStackItem(this.options, list, {\n      prefix: prefix,\n      maxLineLength: maxLineLength,\n      leadingLineBreaks: list.interRowLineBreaks\n    });\n  }\n\n  /**\n   * Finalize currently built list item, add it's content to the parent list.\n   */\n  closeListItem () {\n    const listItem = this._popStackItem();\n    const list = listItem.next;\n\n    const prefixLength = Math.max(listItem.prefix.length, list.maxPrefixLength);\n    const spacing = '\\n' + ' '.repeat(prefixLength);\n    const prefix = (list.prefixAlign === 'right')\n      ? listItem.prefix.padStart(prefixLength)\n      : listItem.prefix.padEnd(prefixLength);\n    const text = prefix + getText(listItem).replace(/\\n/g, spacing);\n\n    addText(\n      list,\n      text,\n      listItem.leadingLineBreaks,\n      Math.max(listItem.stashedLineBreaks, list.interRowLineBreaks)\n    );\n  }\n\n  /**\n   * Finalize currently built list, add it's content to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any following block.\n   */\n  closeList ({ trailingLineBreaks = 2 } = {}) {\n    const list = this._popStackItem();\n    const text = getText(list);\n    if (text) {\n      addText(this._stackItem, text, list.leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Start building a table.\n   */\n  openTable () {\n    this._stackItem = new TableStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table row.\n   */\n  openTableRow () {\n    if (!(this._stackItem instanceof TableStackItem)) {\n      throw new Error('Can\\'t add a table row to something that is not a table! Check the formatter.');\n    }\n    this._stackItem = new TableRowStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table cell.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.maxColumnWidth]\n   * Wrap cell content to this width. Fall back to global wordwrap value if undefined.\n   */\n  openTableCell ({ maxColumnWidth = undefined } = {}) {\n    if (!(this._stackItem instanceof TableRowStackItem)) {\n      throw new Error('Can\\'t add a table cell to something that is not a table row! Check the formatter.');\n    }\n    this._stackItem = new TableCellStackItem(this.options, this._stackItem, maxColumnWidth);\n  }\n\n  /**\n   * Finalize currently built table cell and add it to parent table row's cells.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.colspan] How many columns this cell should occupy.\n   * @param { number } [param0.rowspan] How many rows this cell should occupy.\n   */\n  closeTableCell ({ colspan = 1, rowspan = 1 } = {}) {\n    const cell = this._popStackItem();\n    const text = trimCharacter(getText(cell), '\\n');\n    cell.next.cells.push({ colspan: colspan, rowspan: rowspan, text: text });\n  }\n\n  /**\n   * Finalize currently built table row and add it to parent table's rows.\n   */\n  closeTableRow () {\n    const row = this._popStackItem();\n    row.next.rows.push(row.cells);\n  }\n\n  /**\n   * Finalize currently built table and add the rendered text to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the table.\n   *\n   * @param { TablePrinter } param0.tableToString\n   * A function to convert a table of stringified cells into a complete table.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This table should have at least this number of line breaks to separate if from any preceding block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This table should have at least this number of line breaks to separate it from any following block.\n   */\n  closeTable ({ tableToString, leadingLineBreaks = 2, trailingLineBreaks = 2 }) {\n    const table = this._popStackItem();\n    const output = tableToString(table.rows);\n    if (output) {\n      addText(this._stackItem, output, leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Return the rendered text content of this builder.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return getText(this._stackItem.getRoot());\n    // There should only be the root item if everything is closed properly.\n  }\n\n}\n\nfunction getText (stackItem) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can be requested for text contents.');\n  }\n  return (stackItem.inlineTextBuilder.isEmpty())\n    ? stackItem.rawText\n    : stackItem.rawText + stackItem.inlineTextBuilder.toString();\n}\n\nfunction addText (stackItem, text, leadingLineBreaks, trailingLineBreaks) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can contain text.');\n  }\n  const parentText = getText(stackItem);\n  const lineBreaks = Math.max(stackItem.stashedLineBreaks, leadingLineBreaks);\n  stackItem.inlineTextBuilder.clear();\n  if (parentText) {\n    stackItem.rawText = parentText + '\\n'.repeat(lineBreaks) + text;\n  } else {\n    stackItem.rawText = text;\n    stackItem.leadingLineBreaks = lineBreaks;\n  }\n  stackItem.stashedLineBreaks = trailingLineBreaks;\n}\n\n/**\n * @param { string } str A string to transform.\n * @param { TransformerStackItem } transformer A transformer item (with possible continuation).\n * @returns { string }\n */\nfunction applyTransformer (str, transformer) {\n  return ((transformer) ? applyTransformer(transformer.transform(str), transformer.next) : str);\n}\n\n/**\n * Compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options (defaults, formatters, user options merged, deduplicated).\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile$1 (options = {}) {\n  const selectorsWithoutFormat = options.selectors.filter(s => !s.format);\n  if (selectorsWithoutFormat.length) {\n    throw new Error(\n      'Following selectors have no specified format: ' +\n      selectorsWithoutFormat.map(s => `\\`${s.selector}\\``).join(', ')\n    );\n  }\n  const picker = new DecisionTree(\n    options.selectors.map(s => [s.selector, s])\n  ).build(hp2Builder);\n\n  if (typeof options.encodeCharacters !== 'function') {\n    options.encodeCharacters = makeReplacerFromDict(options.encodeCharacters);\n  }\n\n  const baseSelectorsPicker = new DecisionTree(\n    options.baseElements.selectors.map((s, i) => [s, i + 1])\n  ).build(hp2Builder);\n  function findBaseElements (dom) {\n    return findBases(dom, options, baseSelectorsPicker);\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk,\n    function (dom, builder) {\n      builder.addInline(options.limits.ellipsis || '');\n    }\n  );\n\n  return function (html, metadata = undefined) {\n    return process(html, metadata, options, picker, findBaseElements, limitedWalk);\n  };\n}\n\n\n/**\n * Convert given HTML according to preprocessed options.\n *\n * @param { string } html HTML content to convert.\n * @param { any } metadata Optional metadata for HTML document, for use in formatters.\n * @param { Options } options HtmlToText options (preprocessed).\n * @param { import('selderee').Picker<DomNode, TagDefinition> } picker\n * Tag definition picker for DOM nodes processing.\n * @param { (dom: DomNode[]) => DomNode[] } findBaseElements\n * Function to extract elements from HTML DOM\n * that will only be present in the output text.\n * @param { RecursiveCallback } walk Recursive callback.\n * @returns { string }\n */\nfunction process (html, metadata, options, picker, findBaseElements, walk) {\n  const maxInputLength = options.limits.maxInputLength;\n  if (maxInputLength && html && html.length > maxInputLength) {\n    console.warn(\n      `Input length ${html.length} is above allowed limit of ${maxInputLength}. Truncating without ellipsis.`\n    );\n    html = html.substring(0, maxInputLength);\n  }\n\n  const document = parseDocument(html, { decodeEntities: options.decodeEntities });\n  const bases = findBaseElements(document.children);\n  const builder = new BlockTextBuilder(options, picker, metadata);\n  walk(bases, builder);\n  return builder.toString();\n}\n\n\nfunction findBases (dom, options, baseSelectorsPicker) {\n  const results = [];\n\n  function recursiveWalk (walk, /** @type { DomNode[] } */ dom) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    for (const elem of dom) {\n      if (elem.type !== 'tag') {\n        continue;\n      }\n      const pickedSelectorIndex = baseSelectorsPicker.pick1(elem);\n      if (pickedSelectorIndex > 0) {\n        results.push({ selectorIndex: pickedSelectorIndex, element: elem });\n      } else if (elem.children) {\n        walk(elem.children);\n      }\n      if (results.length >= options.limits.maxBaseElements) {\n        return;\n      }\n    }\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk\n  );\n  limitedWalk(dom);\n\n  if (options.baseElements.orderBy !== 'occurrence') { // 'selectors'\n    results.sort((a, b) => a.selectorIndex - b.selectorIndex);\n  }\n  return (options.baseElements.returnDomByDefault && results.length === 0)\n    ? dom\n    : results.map(x => x.element);\n}\n\n/**\n * Function to walk through DOM nodes and accumulate their string representations.\n *\n * @param   { RecursiveCallback } walk    Recursive callback.\n * @param   { DomNode[] }         [dom]   Nodes array to process.\n * @param   { BlockTextBuilder }  builder Passed around to accumulate output text.\n * @private\n */\nfunction recursiveWalk (walk, dom, builder) {\n  if (!dom) { return; }\n\n  const options = builder.options;\n\n  const tooManyChildNodes = dom.length > options.limits.maxChildNodes;\n  if (tooManyChildNodes) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    dom.push({\n      data: options.limits.ellipsis,\n      type: 'text'\n    });\n  }\n\n  for (const elem of dom) {\n    switch (elem.type) {\n      case 'text': {\n        builder.addInline(elem.data);\n        break;\n      }\n      case 'tag': {\n        const tagDefinition = builder.picker.pick1(elem);\n        const format = options.formatters[tagDefinition.format];\n        format(elem, walk, builder, tagDefinition.options || {});\n        break;\n      }\n    }\n  }\n\n  return;\n}\n\n/**\n * @param { Object<string,string | false> } dict\n * A dictionary where keys are characters to replace\n * and values are replacement strings.\n *\n * First code point from dict keys is used.\n * Compound emojis with ZWJ are not supported (not until Node 16).\n *\n * @returns { ((str: string) => string) | undefined }\n */\nfunction makeReplacerFromDict (dict) {\n  if (!dict || Object.keys(dict).length === 0) {\n    return undefined;\n  }\n  /** @type { [string, string][] } */\n  const entries = Object.entries(dict).filter(([, v]) => v !== false);\n  const regex = new RegExp(\n    entries\n      .map(([c]) => `(${unicodeEscape([...c][0])})`)\n      .join('|'),\n    'g'\n  );\n  const values = entries.map(([, v]) => v);\n  const replacer = (m, ...cgs) => values[cgs.findIndex(cg => cg)];\n  return (str) => str.replace(regex, replacer);\n}\n\n/**\n * Dummy formatter that discards the input and does nothing.\n *\n * @type { FormatCallback }\n */\nfunction formatSkip (elem, walk, builder, formatOptions) {\n  /* do nothing */\n}\n\n/**\n * Insert the given string literal inline instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineString (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.string || '');\n}\n\n/**\n * Insert a block with the given string literal instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockString (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addLiteral(formatOptions.string || '');\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process an inline-level element.\n *\n * @type { FormatCallback }\n */\nfunction formatInline (elem, walk, builder, formatOptions) {\n  walk(elem.children, builder);\n}\n\n/**\n * Process a block-level container.\n *\n * @type { FormatCallback }\n */\nfunction formatBlock$1 (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\nfunction renderOpenTag (elem) {\n  const attrs = (elem.attribs && elem.attribs.length)\n    ? ' ' + Object.entries(elem.attribs)\n      .map(([k, v]) => ((v === '') ? k : `${k}=${v.replace(/\"/g, '&quot;')}`))\n      .join(' ')\n    : '';\n  return `<${elem.name}${attrs}>`;\n}\n\nfunction renderCloseTag (elem) {\n  return `</${elem.name}>`;\n}\n\n/**\n * Render an element as inline HTML tag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineTag (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element as HTML block bag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockTag (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render an element with all it's children as inline HTML.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineHtml (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(\n    render(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element with all it's children as HTML block.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockHtml (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(\n    render(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render inline element wrapped with given strings.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineSurround (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.prefix || '');\n  walk(elem.children, builder);\n  builder.addLiteral(formatOptions.suffix || '');\n}\n\nvar genericFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  block: formatBlock$1,\n  blockHtml: formatBlockHtml,\n  blockString: formatBlockString,\n  blockTag: formatBlockTag,\n  inline: formatInline,\n  inlineHtml: formatInlineHtml,\n  inlineString: formatInlineString,\n  inlineSurround: formatInlineSurround,\n  inlineTag: formatInlineTag,\n  skip: formatSkip\n});\n\nfunction getRow (matrix, j) {\n  if (!matrix[j]) { matrix[j] = []; }\n  return matrix[j];\n}\n\nfunction findFirstVacantIndex (row, x = 0) {\n  while (row[x]) { x++; }\n  return x;\n}\n\nfunction transposeInPlace (matrix, maxSize) {\n  for (let i = 0; i < maxSize; i++) {\n    const rowI = getRow(matrix, i);\n    for (let j = 0; j < i; j++) {\n      const rowJ = getRow(matrix, j);\n      if (rowI[j] || rowJ[i]) {\n        const temp = rowI[j];\n        rowI[j] = rowJ[i];\n        rowJ[i] = temp;\n      }\n    }\n  }\n}\n\nfunction putCellIntoLayout (cell, layout, baseRow, baseCol) {\n  for (let r = 0; r < cell.rowspan; r++) {\n    const layoutRow = getRow(layout, baseRow + r);\n    for (let c = 0; c < cell.colspan; c++) {\n      layoutRow[baseCol + c] = cell;\n    }\n  }\n}\n\nfunction getOrInitOffset (offsets, index) {\n  if (offsets[index] === undefined) {\n    offsets[index] = (index === 0) ? 0 : 1 + getOrInitOffset(offsets, index - 1);\n  }\n  return offsets[index];\n}\n\nfunction updateOffset (offsets, base, span, value) {\n  offsets[base + span] = Math.max(\n    getOrInitOffset(offsets, base + span),\n    getOrInitOffset(offsets, base) + value\n  );\n}\n\n/**\n * Render a table into a string.\n * Cells can contain multiline text and span across multiple rows and columns.\n *\n * Modifies cells to add lines array.\n *\n * @param { TablePrinterCell[][] } tableRows Table to render.\n * @param { number } rowSpacing Number of spaces between columns.\n * @param { number } colSpacing Number of empty lines between rows.\n * @returns { string }\n */\nfunction tableToString (tableRows, rowSpacing, colSpacing) {\n  const layout = [];\n  let colNumber = 0;\n  const rowNumber = tableRows.length;\n  const rowOffsets = [0];\n  // Fill the layout table and row offsets row-by-row.\n  for (let j = 0; j < rowNumber; j++) {\n    const layoutRow = getRow(layout, j);\n    const cells = tableRows[j];\n    let x = 0;\n    for (let i = 0; i < cells.length; i++) {\n      const cell = cells[i];\n      x = findFirstVacantIndex(layoutRow, x);\n      putCellIntoLayout(cell, layout, j, x);\n      x += cell.colspan;\n      cell.lines = cell.text.split('\\n');\n      const cellHeight = cell.lines.length;\n      updateOffset(rowOffsets, j, cell.rowspan, cellHeight + rowSpacing);\n    }\n    colNumber = (layoutRow.length > colNumber) ? layoutRow.length : colNumber;\n  }\n\n  transposeInPlace(layout, (rowNumber > colNumber) ? rowNumber : colNumber);\n\n  const outputLines = [];\n  const colOffsets = [0];\n  // Fill column offsets and output lines column-by-column.\n  for (let x = 0; x < colNumber; x++) {\n    let y = 0;\n    let cell;\n    const rowsInThisColumn = Math.min(rowNumber, layout[x].length);\n    while (y < rowsInThisColumn) {\n      cell = layout[x][y];\n      if (cell) {\n        if (!cell.rendered) {\n          let cellWidth = 0;\n          for (let j = 0; j < cell.lines.length; j++) {\n            const line = cell.lines[j];\n            const lineOffset = rowOffsets[y] + j;\n            outputLines[lineOffset] = (outputLines[lineOffset] || '').padEnd(colOffsets[x]) + line;\n            cellWidth = (line.length > cellWidth) ? line.length : cellWidth;\n          }\n          updateOffset(colOffsets, x, cell.colspan, cellWidth + colSpacing);\n          cell.rendered = true;\n        }\n        y += cell.rowspan;\n      } else {\n        const lineOffset = rowOffsets[y];\n        outputLines[lineOffset] = (outputLines[lineOffset] || '');\n        y++;\n      }\n    }\n  }\n\n  return outputLines.join('\\n');\n}\n\n/**\n * Process a line-break.\n *\n * @type { FormatCallback }\n */\nfunction formatLineBreak (elem, walk, builder, formatOptions) {\n  builder.addLineBreak();\n}\n\n/**\n * Process a `wbr` tag (word break opportunity).\n *\n * @type { FormatCallback }\n */\nfunction formatWbr (elem, walk, builder, formatOptions) {\n  builder.addWordBreakOpportunity();\n}\n\n/**\n * Process a horizontal line.\n *\n * @type { FormatCallback }\n */\nfunction formatHorizontalLine (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addInline('-'.repeat(formatOptions.length || builder.options.wordwrap || 40));\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a paragraph.\n *\n * @type { FormatCallback }\n */\nfunction formatParagraph (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a preformatted content.\n *\n * @type { FormatCallback }\n */\nfunction formatPre (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    isPre: true,\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a heading.\n *\n * @type { FormatCallback }\n */\nfunction formatHeading (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  if (formatOptions.uppercase !== false) {\n    builder.pushWordTransform(str => str.toUpperCase());\n    walk(elem.children, builder);\n    builder.popWordTransform();\n  } else {\n    walk(elem.children, builder);\n  }\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a blockquote.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockquote (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2,\n    reservedLineLength: 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({\n    trailingLineBreaks: formatOptions.trailingLineBreaks || 2,\n    blockTransform: str => ((formatOptions.trimEmptyLines !== false) ? trimCharacter(str, '\\n') : str)\n      .split('\\n')\n      .map(line => '> ' + line)\n      .join('\\n')\n  });\n}\n\nfunction withBrackets (str, brackets) {\n  if (!brackets) { return str; }\n\n  const lbr = (typeof brackets[0] === 'string')\n    ? brackets[0]\n    : '[';\n  const rbr = (typeof brackets[1] === 'string')\n    ? brackets[1]\n    : ']';\n  return lbr + str + rbr;\n}\n\nfunction pathRewrite (path, rewriter, baseUrl, metadata, elem) {\n  const modifiedPath = (typeof rewriter === 'function')\n    ? rewriter(path, metadata, elem)\n    : path;\n  return (modifiedPath[0] === '/' && baseUrl)\n    ? trimCharacterEnd(baseUrl, '/') + modifiedPath\n    : modifiedPath;\n}\n\n/**\n * Process an image.\n *\n * @type { FormatCallback }\n */\nfunction formatImage (elem, walk, builder, formatOptions) {\n  const attribs = elem.attribs || {};\n  const alt = (attribs.alt)\n    ? attribs.alt\n    : '';\n  const src = (!attribs.src)\n    ? ''\n    : pathRewrite(attribs.src, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n  const text = (!src)\n    ? alt\n    : (!alt)\n      ? withBrackets(src, formatOptions.linkBrackets)\n      : alt + ' ' + withBrackets(src, formatOptions.linkBrackets);\n\n  builder.addInline(text, { noWordTransform: true });\n}\n\n// a img baseUrl\n// a img pathRewrite\n// a img linkBrackets\n\n// a     ignoreHref: false\n//            ignoreText ?\n// a     noAnchorUrl: true\n//            can be replaced with selector\n// a     hideLinkHrefIfSameAsText: false\n//            how to compare, what to show (text, href, normalized) ?\n// a     mailto protocol removed without options\n\n// a     protocols: mailto, tel, ...\n//            can be matched with selector?\n\n// anchors, protocols - only if no pathRewrite fn is provided\n\n// normalize-url ?\n\n// a\n// a[href^=\"#\"] - format:skip by default\n// a[href^=\"mailto:\"] - ?\n\n/**\n * Process an anchor.\n *\n * @type { FormatCallback }\n */\nfunction formatAnchor (elem, walk, builder, formatOptions) {\n  function getHref () {\n    if (formatOptions.ignoreHref) { return ''; }\n    if (!elem.attribs || !elem.attribs.href) { return ''; }\n    let href = elem.attribs.href.replace(/^mailto:/, '');\n    if (formatOptions.noAnchorUrl && href[0] === '#') { return ''; }\n    href = pathRewrite(href, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n    return href;\n  }\n  const href = getHref();\n  if (!href) {\n    walk(elem.children, builder);\n  } else {\n    let text = '';\n    builder.pushWordTransform(\n      str => {\n        if (str) { text += str; }\n        return str;\n      }\n    );\n    walk(elem.children, builder);\n    builder.popWordTransform();\n\n    const hideSameLink = formatOptions.hideLinkHrefIfSameAsText && href === text;\n    if (!hideSameLink) {\n      builder.addInline(\n        (!text)\n          ? href\n          : ' ' + withBrackets(href, formatOptions.linkBrackets),\n        { noWordTransform: true }\n      );\n    }\n  }\n}\n\n/**\n * @param { DomNode }           elem               List items with their prefixes.\n * @param { RecursiveCallback } walk               Recursive callback to process child nodes.\n * @param { BlockTextBuilder }  builder            Passed around to accumulate output text.\n * @param { FormatOptions }     formatOptions      Options specific to a formatter.\n * @param { () => string }      nextPrefixCallback Function that returns increasing index each time it is called.\n */\nfunction formatList (elem, walk, builder, formatOptions, nextPrefixCallback) {\n  const isNestedList = get(elem, ['parent', 'name']) === 'li';\n\n  // With Roman numbers, index length is not as straightforward as with Arabic numbers or letters,\n  // so the dumb length comparison is the most robust way to get the correct value.\n  let maxPrefixLength = 0;\n  const listItems = (elem.children || [])\n    // it might be more accurate to check only for html spaces here, but no significant benefit\n    .filter(child => child.type !== 'text' || !/^\\s*$/.test(child.data))\n    .map(function (child) {\n      if (child.name !== 'li') {\n        return { node: child, prefix: '' };\n      }\n      const prefix = (isNestedList)\n        ? nextPrefixCallback().trimStart()\n        : nextPrefixCallback();\n      if (prefix.length > maxPrefixLength) { maxPrefixLength = prefix.length; }\n      return { node: child, prefix: prefix };\n    });\n  if (!listItems.length) { return; }\n\n  builder.openList({\n    interRowLineBreaks: 1,\n    leadingLineBreaks: isNestedList ? 1 : (formatOptions.leadingLineBreaks || 2),\n    maxPrefixLength: maxPrefixLength,\n    prefixAlign: 'left'\n  });\n\n  for (const { node, prefix } of listItems) {\n    builder.openListItem({ prefix: prefix });\n    walk([node], builder);\n    builder.closeListItem();\n  }\n\n  builder.closeList({ trailingLineBreaks: isNestedList ? 1 : (formatOptions.trailingLineBreaks || 2) });\n}\n\n/**\n * Process an unordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatUnorderedList (elem, walk, builder, formatOptions) {\n  const prefix = formatOptions.itemPrefix || ' * ';\n  return formatList(elem, walk, builder, formatOptions, () => prefix);\n}\n\n/**\n * Process an ordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatOrderedList (elem, walk, builder, formatOptions) {\n  let nextIndex = Number(elem.attribs.start || '1');\n  const indexFunction = getOrderedListIndexFunction(elem.attribs.type);\n  const nextPrefixCallback = () => ' ' + indexFunction(nextIndex++) + '. ';\n  return formatList(elem, walk, builder, formatOptions, nextPrefixCallback);\n}\n\n/**\n * Return a function that can be used to generate index markers of a specified format.\n *\n * @param   { string } [olType='1'] Marker type.\n * @returns { (i: number) => string }\n */\nfunction getOrderedListIndexFunction (olType = '1') {\n  switch (olType) {\n    case 'a': return (i) => numberToLetterSequence(i, 'a');\n    case 'A': return (i) => numberToLetterSequence(i, 'A');\n    case 'i': return (i) => numberToRoman(i).toLowerCase();\n    case 'I': return (i) => numberToRoman(i);\n    case '1':\n    default: return (i) => (i).toString();\n  }\n}\n\n/**\n * Given a list of class and ID selectors (prefixed with '.' and '#'),\n * return them as separate lists of names without prefixes.\n *\n * @param { string[] } selectors Class and ID selectors (`[\".class\", \"#id\"]` etc).\n * @returns { { classes: string[], ids: string[] } }\n */\nfunction splitClassesAndIds (selectors) {\n  const classes = [];\n  const ids = [];\n  for (const selector of selectors) {\n    if (selector.startsWith('.')) {\n      classes.push(selector.substring(1));\n    } else if (selector.startsWith('#')) {\n      ids.push(selector.substring(1));\n    }\n  }\n  return { classes: classes, ids: ids };\n}\n\nfunction isDataTable (attr, tables) {\n  if (tables === true) { return true; }\n  if (!attr) { return false; }\n\n  const { classes, ids } = splitClassesAndIds(tables);\n  const attrClasses = (attr['class'] || '').split(' ');\n  const attrIds = (attr['id'] || '').split(' ');\n\n  return attrClasses.some(x => classes.includes(x)) || attrIds.some(x => ids.includes(x));\n}\n\n/**\n * Process a table (either as a container or as a data table, depending on options).\n *\n * @type { FormatCallback }\n */\nfunction formatTable (elem, walk, builder, formatOptions) {\n  return isDataTable(elem.attribs, builder.options.tables)\n    ? formatDataTable(elem, walk, builder, formatOptions)\n    : formatBlock(elem, walk, builder, formatOptions);\n}\n\nfunction formatBlock (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks });\n}\n\n/**\n * Process a data table.\n *\n * @type { FormatCallback }\n */\nfunction formatDataTable (elem, walk, builder, formatOptions) {\n  builder.openTable();\n  elem.children.forEach(walkTable);\n  builder.closeTable({\n    tableToString: (rows) => tableToString(rows, formatOptions.rowSpacing ?? 0, formatOptions.colSpacing ?? 3),\n    leadingLineBreaks: formatOptions.leadingLineBreaks,\n    trailingLineBreaks: formatOptions.trailingLineBreaks\n  });\n\n  function formatCell (cellNode) {\n    const colspan = +get(cellNode, ['attribs', 'colspan']) || 1;\n    const rowspan = +get(cellNode, ['attribs', 'rowspan']) || 1;\n    builder.openTableCell({ maxColumnWidth: formatOptions.maxColumnWidth });\n    walk(cellNode.children, builder);\n    builder.closeTableCell({ colspan: colspan, rowspan: rowspan });\n  }\n\n  function walkTable (elem) {\n    if (elem.type !== 'tag') { return; }\n\n    const formatHeaderCell = (formatOptions.uppercaseHeaderCells !== false)\n      ? (cellNode) => {\n        builder.pushWordTransform(str => str.toUpperCase());\n        formatCell(cellNode);\n        builder.popWordTransform();\n      }\n      : formatCell;\n\n    switch (elem.name) {\n      case 'thead':\n      case 'tbody':\n      case 'tfoot':\n      case 'center':\n        elem.children.forEach(walkTable);\n        return;\n\n      case 'tr': {\n        builder.openTableRow();\n        for (const childOfTr of elem.children) {\n          if (childOfTr.type !== 'tag') { continue; }\n          switch (childOfTr.name) {\n            case 'th': {\n              formatHeaderCell(childOfTr);\n              break;\n            }\n            case 'td': {\n              formatCell(childOfTr);\n              break;\n            }\n              // do nothing\n          }\n        }\n        builder.closeTableRow();\n        break;\n      }\n        // do nothing\n    }\n  }\n}\n\nvar textFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  anchor: formatAnchor,\n  blockquote: formatBlockquote,\n  dataTable: formatDataTable,\n  heading: formatHeading,\n  horizontalLine: formatHorizontalLine,\n  image: formatImage,\n  lineBreak: formatLineBreak,\n  orderedList: formatOrderedList,\n  paragraph: formatParagraph,\n  pre: formatPre,\n  table: formatTable,\n  unorderedList: formatUnorderedList,\n  wbr: formatWbr\n});\n\n/**\n * Default options.\n *\n * @constant\n * @type { Options }\n * @default\n * @private\n */\nconst DEFAULT_OPTIONS = {\n  baseElements: {\n    selectors: [ 'body' ],\n    orderBy: 'selectors', // 'selectors' | 'occurrence'\n    returnDomByDefault: true\n  },\n  decodeEntities: true,\n  encodeCharacters: {},\n  formatters: {},\n  limits: {\n    ellipsis: '...',\n    maxBaseElements: undefined,\n    maxChildNodes: undefined,\n    maxDepth: undefined,\n    maxInputLength: (1 << 24) // 16_777_216\n  },\n  longWordSplit: {\n    forceWrapOnLimit: false,\n    wrapCharacters: []\n  },\n  preserveNewlines: false,\n  selectors: [\n    { selector: '*', format: 'inline' },\n    {\n      selector: 'a',\n      format: 'anchor',\n      options: {\n        baseUrl: null,\n        hideLinkHrefIfSameAsText: false,\n        ignoreHref: false,\n        linkBrackets: ['[', ']'],\n        noAnchorUrl: true\n      }\n    },\n    { selector: 'article', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'aside', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'blockquote',\n      format: 'blockquote',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2, trimEmptyLines: true }\n    },\n    { selector: 'br', format: 'lineBreak' },\n    { selector: 'div', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'footer', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'form', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'h1', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h2', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h3', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h4', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h5', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h6', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'header', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'hr',\n      format: 'horizontalLine',\n      options: { leadingLineBreaks: 2, length: undefined, trailingLineBreaks: 2 }\n    },\n    {\n      selector: 'img',\n      format: 'image',\n      options: { baseUrl: null, linkBrackets: ['[', ']'] }\n    },\n    { selector: 'main', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'nav', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'ol',\n      format: 'orderedList',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'p', format: 'paragraph', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'pre', format: 'pre', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'section', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'table',\n      format: 'table',\n      options: {\n        colSpacing: 3,\n        leadingLineBreaks: 2,\n        maxColumnWidth: 60,\n        rowSpacing: 0,\n        trailingLineBreaks: 2,\n        uppercaseHeaderCells: true\n      }\n    },\n    {\n      selector: 'ul',\n      format: 'unorderedList',\n      options: { itemPrefix: ' * ', leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'wbr', format: 'wbr' },\n  ],\n  tables: [], // deprecated\n  whitespaceCharacters: ' \\t\\r\\n\\f\\u200b',\n  wordwrap: 80\n};\n\nconst concatMerge = (acc, src, options) => [...acc, ...src];\nconst overwriteMerge = (acc, src, options) => [...src];\nconst selectorsMerge = (acc, src, options) => (\n  (acc.some(s => typeof s === 'object'))\n    ? concatMerge(acc, src) // selectors\n    : overwriteMerge(acc, src) // baseElements.selectors\n);\n\n/**\n * Preprocess options, compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options.\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile (options = {}) {\n  options = merge(\n    DEFAULT_OPTIONS,\n    options,\n    {\n      arrayMerge: overwriteMerge,\n      customMerge: (key) => ((key === 'selectors') ? selectorsMerge : undefined)\n    }\n  );\n  options.formatters = Object.assign({}, genericFormatters, textFormatters, options.formatters);\n  options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n\n  handleDeprecatedOptions(options);\n\n  return compile$1(options);\n}\n\n/**\n * Convert given HTML content to plain text string.\n *\n * @param   { string }  html           HTML content to convert.\n * @param   { Options } [options = {}] HtmlToText options.\n * @param   { any }     [metadata]     Optional metadata for HTML document, for use in formatters.\n * @returns { string }                 Plain text string.\n * @static\n *\n * @example\n * const { convert } = require('html-to-text');\n * const text = convert('<h1>Hello World</h1>', {\n *   wordwrap: 130\n * });\n * console.log(text); // HELLO WORLD\n */\nfunction convert (html, options = {}, metadata = undefined) {\n  return compile(options)(html, metadata);\n}\n\n/**\n * Map previously existing and now deprecated options to the new options layout.\n * This is a subject for cleanup in major releases.\n *\n * @param { Options } options HtmlToText options.\n */\nfunction handleDeprecatedOptions (options) {\n  if (options.tags) {\n    const tagDefinitions = Object.entries(options.tags).map(\n      ([selector, definition]) => ({ ...definition, selector: selector || '*' })\n    );\n    options.selectors.push(...tagDefinitions);\n    options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n  }\n\n  function set (obj, path, value) {\n    const valueKey = path.pop();\n    for (const key of path) {\n      let nested = obj[key];\n      if (!nested) {\n        nested = {};\n        obj[key] = nested;\n      }\n      obj = nested;\n    }\n    obj[valueKey] = value;\n  }\n\n  if (options['baseElement']) {\n    const baseElement = options['baseElement'];\n    set(\n      options,\n      ['baseElements', 'selectors'],\n      (Array.isArray(baseElement) ? baseElement : [baseElement])\n    );\n  }\n  if (options['returnDomByDefault'] !== undefined) {\n    set(options, ['baseElements', 'returnDomByDefault'], options['returnDomByDefault']);\n  }\n\n  for (const definition of options.selectors) {\n    if (definition.format === 'anchor' && get(definition, ['options', 'noLinkBrackets'])) {\n      set(definition, ['options', 'linkBrackets'], false);\n    }\n  }\n}\n\nexport { compile, convert, convert as htmlToText };\n"], "mappings": ";;;;;;;;AACO,IAAI;AAAA,CACV,SAAUA,cAAa;AAEpB,EAAAA,aAAY,MAAM,IAAI;AAEtB,EAAAA,aAAY,MAAM,IAAI;AAEtB,EAAAA,aAAY,WAAW,IAAI;AAE3B,EAAAA,aAAY,SAAS,IAAI;AAEzB,EAAAA,aAAY,QAAQ,IAAI;AAExB,EAAAA,aAAY,OAAO,IAAI;AAEvB,EAAAA,aAAY,KAAK,IAAI;AAErB,EAAAA,aAAY,OAAO,IAAI;AAEvB,EAAAA,aAAY,SAAS,IAAI;AAC7B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAM7B,SAAS,MAAM,MAAM;AACxB,SAAQ,KAAK,SAAS,YAAY,OAC9B,KAAK,SAAS,YAAY,UAC1B,KAAK,SAAS,YAAY;AAClC;AAGO,IAAM,OAAO,YAAY;AAEzB,IAAM,OAAO,YAAY;AAEzB,IAAM,YAAY,YAAY;AAE9B,IAAM,UAAU,YAAY;AAE5B,IAAM,SAAS,YAAY;AAE3B,IAAM,QAAQ,YAAY;AAE1B,IAAM,MAAM,YAAY;AAExB,IAAM,QAAQ,YAAY;AAE1B,IAAM,UAAU,YAAY;;;AC7C5B,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AAEV,SAAK,SAAS;AAEd,SAAK,OAAO;AAEZ,SAAK,OAAO;AAEZ,SAAK,aAAa;AAElB,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,QAAQ;AACnB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,kBAAkB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB,MAAM;AACtB,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,MAAM;AAClB,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,YAAY,OAAO;AACzB,WAAO,UAAU,MAAM,SAAS;AAAA,EACpC;AACJ;AAIO,IAAM,WAAN,cAAuB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,MAAM;AACd,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,MAAM;AAChB,SAAK,OAAO;AAAA,EAChB;AACJ;AAIO,IAAMC,QAAN,cAAmB,SAAS;AAAA,EAC/B,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACX,WAAO;AAAA,EACX;AACJ;AAIO,IAAMC,WAAN,cAAsB,SAAS;AAAA,EAClC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACX,WAAO;AAAA,EACX;AACJ;AAIO,IAAM,wBAAN,cAAoC,SAAS;AAAA,EAChD,YAAYC,OAAM,MAAM;AACpB,UAAM,IAAI;AACV,SAAK,OAAOA;AACZ,SAAK,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACX,WAAO;AAAA,EACX;AACJ;AAIO,IAAM,mBAAN,cAA+B,KAAK;AAAA;AAAA;AAAA;AAAA,EAIvC,YAAY,UAAU;AAClB,UAAM;AACN,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA,EAGA,IAAI,aAAa;AACb,QAAIC;AACJ,YAAQA,MAAK,KAAK,SAAS,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK;AAAA,EACpE;AAAA;AAAA,EAEA,IAAI,YAAY;AACZ,WAAO,KAAK,SAAS,SAAS,IACxB,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,IACtC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,UAAU;AACrB,SAAK,WAAW;AAAA,EACpB;AACJ;AACO,IAAMC,SAAN,cAAoB,iBAAiB;AAAA,EACxC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACX,WAAO;AAAA,EACX;AACJ;AAIO,IAAM,WAAN,cAAuB,iBAAiB;AAAA,EAC3C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACX,WAAO;AAAA,EACX;AACJ;AAIO,IAAM,UAAN,cAAsB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,YAAYF,OAAM,SAAS,WAAW,CAAC,GAAG,OAAOA,UAAS,WACpD,YAAY,SACZA,UAAS,UACL,YAAY,QACZ,YAAY,KAAK;AACvB,UAAM,QAAQ;AACd,SAAK,OAAOA;AACZ,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,WAAW;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQA,OAAM;AACd,SAAK,OAAOA;AAAA,EAChB;AAAA,EACA,IAAI,aAAa;AACb,WAAO,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAACA,UAAS;AAC3C,UAAIC,KAAI;AACR,aAAQ;AAAA,QACJ,MAAAD;AAAA,QACA,OAAO,KAAK,QAAQA,KAAI;AAAA,QACxB,YAAYC,MAAK,KAAK,oBAAoB,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAGD,KAAI;AAAA,QACzF,SAAS,KAAK,KAAK,iBAAiB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAGA,KAAI;AAAA,MACvF;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAKO,SAASG,OAAM,MAAM;AACxB,SAAO,MAAS,IAAI;AACxB;AAKO,SAAS,QAAQ,MAAM;AAC1B,SAAO,KAAK,SAAS,YAAY;AACrC;AAKO,SAAS,OAAO,MAAM;AACzB,SAAO,KAAK,SAAS,YAAY;AACrC;AAKO,SAAS,UAAU,MAAM;AAC5B,SAAO,KAAK,SAAS,YAAY;AACrC;AAKO,SAAS,YAAY,MAAM;AAC9B,SAAO,KAAK,SAAS,YAAY;AACrC;AAKO,SAAS,WAAW,MAAM;AAC7B,SAAO,KAAK,SAAS,YAAY;AACrC;AAcO,SAAS,UAAU,MAAM,YAAY,OAAO;AAC/C,MAAI;AACJ,MAAI,OAAO,IAAI,GAAG;AACd,aAAS,IAAIC,MAAK,KAAK,IAAI;AAAA,EAC/B,WACS,UAAU,IAAI,GAAG;AACtB,aAAS,IAAIC,SAAQ,KAAK,IAAI;AAAA,EAClC,WACSC,OAAM,IAAI,GAAG;AAClB,UAAM,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,CAAC;AAC7D,UAAM,QAAQ,IAAI,QAAQ,KAAK,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,QAAQ;AAClE,aAAS,QAAQ,CAAC,UAAW,MAAM,SAAS,KAAM;AAClD,QAAI,KAAK,aAAa,MAAM;AACxB,YAAM,YAAY,KAAK;AAAA,IAC3B;AACA,QAAI,KAAK,oBAAoB,GAAG;AAC5B,YAAM,oBAAoB,IAAI,EAAE,GAAG,KAAK,oBAAoB,EAAE;AAAA,IAClE;AACA,QAAI,KAAK,iBAAiB,GAAG;AACzB,YAAM,iBAAiB,IAAI,EAAE,GAAG,KAAK,iBAAiB,EAAE;AAAA,IAC5D;AACA,aAAS;AAAA,EACb,WACS,QAAQ,IAAI,GAAG;AACpB,UAAM,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,CAAC;AAC7D,UAAM,QAAQ,IAAIC,OAAM,QAAQ;AAChC,aAAS,QAAQ,CAAC,UAAW,MAAM,SAAS,KAAM;AAClD,aAAS;AAAA,EACb,WACS,WAAW,IAAI,GAAG;AACvB,UAAM,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,CAAC;AAC7D,UAAM,QAAQ,IAAI,SAAS,QAAQ;AACnC,aAAS,QAAQ,CAAC,UAAW,MAAM,SAAS,KAAM;AAClD,QAAI,KAAK,QAAQ,GAAG;AAChB,YAAM,QAAQ,IAAI,KAAK,QAAQ;AAAA,IACnC;AACA,aAAS;AAAA,EACb,WACS,YAAY,IAAI,GAAG;AACxB,UAAM,cAAc,IAAI,sBAAsB,KAAK,MAAM,KAAK,IAAI;AAClE,QAAI,KAAK,QAAQ,KAAK,MAAM;AACxB,kBAAY,QAAQ,IAAI,KAAK,QAAQ;AACrC,kBAAY,YAAY,IAAI,KAAK,YAAY;AAC7C,kBAAY,YAAY,IAAI,KAAK,YAAY;AAAA,IACjD;AACA,aAAS;AAAA,EACb,OACK;AACD,UAAM,IAAI,MAAM,wBAAwB,KAAK,IAAI,EAAE;AAAA,EACvD;AACA,SAAO,aAAa,KAAK;AACzB,SAAO,WAAW,KAAK;AACvB,MAAI,KAAK,sBAAsB,MAAM;AACjC,WAAO,qBAAqB,KAAK;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ;AAC3B,QAAM,WAAW,OAAO,IAAI,CAAC,UAAU,UAAU,OAAO,IAAI,CAAC;AAC7D,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,aAAS,CAAC,EAAE,OAAO,SAAS,IAAI,CAAC;AACjC,aAAS,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC;AAAA,EACrC;AACA,SAAO;AACX;;;AC7UA,IAAM,cAAc;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,SAAS;AACb;AACO,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,YAAY,UAAU,SAAS,WAAW;AAEtC,SAAK,MAAM,CAAC;AAEZ,SAAK,OAAO,IAAI,SAAS,KAAK,GAAG;AAEjC,SAAK,OAAO;AAEZ,SAAK,WAAW,CAAC,KAAK,IAAI;AAE1B,SAAK,WAAW;AAEhB,SAAK,SAAS;AAEd,QAAI,OAAO,YAAY,YAAY;AAC/B,kBAAY;AACZ,gBAAU;AAAA,IACd;AACA,QAAI,OAAO,aAAa,UAAU;AAC9B,gBAAU;AACV,iBAAW;AAAA,IACf;AACA,SAAK,WAAW,aAAa,QAAQ,aAAa,SAAS,WAAW;AACtE,SAAK,UAAU,YAAY,QAAQ,YAAY,SAAS,UAAU;AAClE,SAAK,YAAY,cAAc,QAAQ,cAAc,SAAS,YAAY;AAAA,EAC9E;AAAA,EACA,aAAa,QAAQ;AACjB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAEA,UAAU;AACN,SAAK,MAAM,CAAC;AACZ,SAAK,OAAO,IAAI,SAAS,KAAK,GAAG;AACjC,SAAK,OAAO;AACZ,SAAK,WAAW,CAAC,KAAK,IAAI;AAC1B,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA,EAEA,QAAQ;AACJ,QAAI,KAAK;AACL;AACJ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,eAAe,IAAI;AAAA,EAC5B;AAAA,EACA,QAAQ,OAAO;AACX,SAAK,eAAe,KAAK;AAAA,EAC7B;AAAA,EACA,aAAa;AACT,SAAK,WAAW;AAChB,UAAM,OAAO,KAAK,SAAS,IAAI;AAC/B,QAAI,KAAK,QAAQ,gBAAgB;AAC7B,WAAK,WAAW,KAAK,OAAO;AAAA,IAChC;AACA,QAAI,KAAK;AACL,WAAK,UAAU,IAAI;AAAA,EAC3B;AAAA,EACA,UAAUC,OAAM,SAAS;AACrB,UAAM,OAAO,KAAK,QAAQ,UAAU,YAAY,MAAM;AACtD,UAAM,UAAU,IAAI,QAAQA,OAAM,SAAS,QAAW,IAAI;AAC1D,SAAK,QAAQ,OAAO;AACpB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC9B;AAAA,EACA,OAAO,MAAM;AACT,UAAM,EAAE,SAAS,IAAI;AACrB,QAAI,YAAY,SAAS,SAAS,YAAY,MAAM;AAChD,eAAS,QAAQ;AACjB,UAAI,KAAK,QAAQ,gBAAgB;AAC7B,iBAAS,WAAW,KAAK,OAAO;AAAA,MACpC;AAAA,IACJ,OACK;AACD,YAAM,OAAO,IAAIC,MAAK,IAAI;AAC1B,WAAK,QAAQ,IAAI;AACjB,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,UAAU,MAAM;AACZ,QAAI,KAAK,YAAY,KAAK,SAAS,SAAS,YAAY,SAAS;AAC7D,WAAK,SAAS,QAAQ;AACtB;AAAA,IACJ;AACA,UAAM,OAAO,IAAIC,SAAQ,IAAI;AAC7B,SAAK,QAAQ,IAAI;AACjB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AACX,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AACX,UAAM,OAAO,IAAID,MAAK,EAAE;AACxB,UAAM,OAAO,IAAIE,OAAM,CAAC,IAAI,CAAC;AAC7B,SAAK,QAAQ,IAAI;AACjB,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AACT,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,wBAAwBH,OAAM,MAAM;AAChC,UAAM,OAAO,IAAI,sBAAsBA,OAAM,IAAI;AACjD,SAAK,QAAQ,IAAI;AAAA,EACrB;AAAA,EACA,eAAe,OAAO;AAClB,QAAI,OAAO,KAAK,aAAa,YAAY;AACrC,WAAK,SAAS,OAAO,KAAK,GAAG;AAAA,IACjC,WACS,OAAO;AACZ,YAAM;AAAA,IACV;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM;AACV,UAAM,SAAS,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AACrD,UAAM,kBAAkB,OAAO,SAAS,OAAO,SAAS,SAAS,CAAC;AAClE,QAAI,KAAK,QAAQ,kBAAkB;AAC/B,WAAK,aAAa,KAAK,OAAO;AAAA,IAClC;AACA,QAAI,KAAK,QAAQ,gBAAgB;AAC7B,WAAK,WAAW,KAAK,OAAO;AAAA,IAChC;AACA,WAAO,SAAS,KAAK,IAAI;AACzB,QAAI,iBAAiB;AACjB,WAAK,OAAO;AACZ,sBAAgB,OAAO;AAAA,IAC3B;AACA,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EACpB;AACJ;;;AChJA,IAAM,IAAE;AAAM,SAAS,EAAEI,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAGD,GAAE,SAAS,CAAC,CAAC,EAAE,IAAK,CAAAE,OAAGA,GAAE,SAAO,CAAE;AAAE,EAAAD,GAAE,QAAQ,EAAE;AAAE,QAAME,KAAE,EAAEF,IAAE,GAAEA,GAAE,MAAM;AAAE,SAAO,CAAAC,OAAG,EAAEC,IAAED,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEF,IAAEI,IAAE;AAAC,MAAGA,KAAEJ,MAAG,EAAE,QAAM,EAAC,QAAOE,GAAEF,EAAC,GAAE,OAAMA,KAAE,EAAC;AAAE,QAAMC,KAAE,KAAK,MAAMD,KAAEI,MAAG,CAAC,GAAED,KAAE,EAAED,IAAEF,IAAEC,EAAC,GAAEI,KAAE,EAAEH,IAAED,IAAEG,EAAC;AAAE,SAAM,EAAC,QAAOD,GAAE,QAAO,KAAIA,IAAE,MAAKE,GAAC;AAAC;AAAC,SAAS,EAAEH,IAAEF,IAAE;AAAC,SAAO,SAASE,IAAE;AAAC,WAAO,OAAO,UAAU,eAAe,KAAKA,IAAE,OAAO;AAAA,EAAC,EAAEA,EAAC,IAAE,EAAC,MAAKA,GAAE,OAAM,QAAOF,KAAEE,GAAE,OAAM,IAAE,EAAEA,GAAE,KAAK,SAAOF,KAAEE,GAAE,OAAKA,GAAE,KAAIF,EAAC;AAAC;AAAC,SAAS,EAAEE,IAAEI,KAAE,IAAGF,KAAE,CAAC,GAAE;AAAC,QAAMH,KAAE,YAAU,OAAOK,KAAEA,KAAEF,IAAEC,KAAE,YAAU,OAAOC,KAAEA,KAAE,IAAGC,KAAEL,GAAE,IAAI,CAAC,GAAE,IAAE,CAAC,CAACD,GAAE;AAAY,SAAO,SAASC,IAAEI,KAAE,GAAE;AAAC,UAAMF,KAAE,IAAE,EAAEF,EAAC,IAAE,OAAK,EAAC,MAAK,GAAE,QAAO,EAAC;AAAG,QAAID,KAAEK;AAAE,UAAMH,KAAE,CAAC;AAAE,MAAE,QAAKF,KAAEC,GAAE,UAAQ;AAAC,UAAIF,KAAE;AAAG,iBAAUM,MAAKC,IAAE;AAAC,QAAAD,GAAE,MAAM,YAAUL;AAAE,cAAMM,KAAED,GAAE,MAAM,KAAKJ,EAAC;AAAE,YAAGK,MAAGA,GAAE,CAAC,EAAE,SAAO,GAAE;AAAC,cAAG,CAACD,GAAE,SAAQ;AAAC,kBAAMJ,KAAEE,GAAEH,EAAC,GAAED,KAAE,YAAU,OAAOM,GAAE,UAAQC,GAAE,CAAC,EAAE,QAAQ,IAAI,OAAOD,GAAE,MAAM,QAAOA,GAAE,MAAM,KAAK,GAAEA,GAAE,OAAO,IAAEC,GAAE,CAAC;AAAE,YAAAJ,GAAE,KAAK,EAAC,OAAME,IAAE,MAAKC,GAAE,MAAK,MAAKN,IAAE,QAAOC,IAAE,KAAIM,GAAE,CAAC,EAAE,QAAO,MAAKL,GAAE,MAAK,QAAOA,GAAE,OAAM,CAAC;AAAA,UAAC;AAAC,cAAGD,KAAEK,GAAE,MAAM,WAAUN,KAAE,MAAGM,GAAE,MAAK;AAAC,kBAAMN,KAAEM,GAAE,KAAKJ,IAAED,EAAC;AAAE,YAAAE,GAAE,KAAK,GAAGH,GAAE,MAAM,GAAEC,KAAED,GAAE;AAAA,UAAM;AAAC,cAAGM,GAAE,IAAI,OAAM;AAAE;AAAA,QAAK;AAAA,MAAC;AAAC,UAAG,CAACN,GAAE;AAAA,IAAK;AAAC,WAAM,EAAC,QAAOG,IAAE,QAAOF,IAAE,UAASC,GAAE,UAAQD,GAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEC,IAAEF,IAAE;AAAC,SAAM,EAAC,GAAGE,IAAE,OAAM,EAAEA,IAAEF,EAAC,EAAC;AAAC;AAAC,SAAS,EAAEE,IAAEF,IAAE;AAAC,MAAG,MAAIE,GAAE,KAAK,OAAO,OAAM,IAAI,MAAM,SAASF,EAAC,wCAAwC;AAAE,MAAG,SAASE,IAAE;AAAC,WAAO,OAAO,UAAU,eAAe,KAAKA,IAAE,OAAO;AAAA,EAAC,EAAEA,EAAC,EAAE,QAAO,SAASA,IAAE;AAAC,QAAGA,GAAE,OAAO,OAAM,IAAI,MAAM,uBAAuBA,GAAE,MAAM,IAAIA,GAAE,KAAK,kDAAkD;AAAE,WAAOA,GAAE,SAAOA,KAAE,IAAI,OAAOA,GAAE,QAAOA,GAAE,QAAM,GAAG;AAAA,EAAC,EAAEA,GAAE,KAAK;AAAE,MAAG,SAASA,IAAE;AAAC,WAAO,OAAO,UAAU,eAAe,KAAKA,IAAE,KAAK;AAAA,EAAC,EAAEA,EAAC,GAAE;AAAC,QAAG,MAAIA,GAAE,IAAI,OAAO,OAAM,IAAI,MAAM,SAASF,EAAC,MAAME,GAAE,IAAI,oDAAoD;AAAE,WAAO,IAAI,OAAO,EAAEA,GAAE,GAAG,GAAE,GAAG;AAAA,EAAC;AAAC,SAAO,IAAI,OAAO,EAAEA,GAAE,IAAI,GAAE,GAAG;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,GAAE,QAAQ,iCAAgC,MAAM;AAAC;;;ACqC90D,SAAS,MACT,SACA,OAAO;AACH,SAAO,CAAC,MAAM,MAAM;AAChB,QAAI,WAAW;AACf,QAAI,QAAQ;AACZ,QAAI,IAAI,KAAK,OAAO,QAAQ;AACxB,cAAQ,QAAQ,KAAK,OAAO,CAAC,GAAG,MAAM,CAAC;AACvC,UAAI,UAAU,QAAW;AACrB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,cAAQ,MAAM,CAAC;AAAA,IACnB;AACA,WAAQ,UAAU,SACZ,EAAE,SAAS,MAAM,IACjB;AAAA,MACE,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACJ;AAAA,EACR;AACJ;AAoBA,SAAS,SAASM,IAAG,GAAG;AACpB,SAAQA,GAAE,UAAY;AAAA,IAClB,SAAS;AAAA,IACT,UAAUA,GAAE;AAAA,IACZ,OAAO,EAAEA,GAAE,OAAOA,GAAE,QAAQ;AAAA,EAChC,IAAKA;AACT;AACA,SAAS,SAASA,IAAG,GAAG;AACpB,SAAQA,GAAE,UAAW,EAAEA,EAAC,IAAIA;AAChC;AACA,SAAS,IAAI,GAAG,QAAQ;AACpB,SAAO,CAAC,MAAM,MAAM,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;AAC5E;AAYA,SAAS,OAAO,GAAG,KAAK;AACpB,SAAO,CAAC,MAAM,MAAM;AAChB,UAAMC,KAAI,EAAE,MAAM,CAAC;AACnB,WAAQA,GAAE,UACJA,KACA;AAAA,MACE,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACX;AAAA,EACR;AACJ;AAaA,SAAS,UAAU,IAAI;AACnB,SAAO,CAAC,MAAM,MAAM;AAChB,eAAW,KAAK,IAAI;AAChB,YAAM,SAAS,EAAE,MAAM,CAAC;AACxB,UAAI,OAAO,SAAS;AAChB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO,EAAE,SAAS,MAAM;AAAA,EAC5B;AACJ;AACA,SAAS,UAAU,IAAI,IAAI;AACvB,SAAO,CAAC,MAAM,MAAM;AAChB,UAAM,KAAK,GAAG,MAAM,CAAC;AACrB,WAAQ,GAAG,UACL,KACA,GAAG,MAAM,CAAC;AAAA,EACpB;AACJ;AAaA,SAAS,UAAU,GACnB,MAAM;AACF,SAAO,CAAC,MAAM,MAAM;AAChB,UAAM,SAAS,CAAC;AAChB,QAAI,UAAU;AACd,OAAG;AACC,YAAMC,KAAI,EAAE,MAAM,CAAC;AACnB,UAAIA,GAAE,WAAW,KAAKA,GAAE,OAAO,OAAO,SAAS,GAAG,MAAM,GAAGA,GAAE,QAAQ,GAAG;AACpE,eAAO,KAAKA,GAAE,KAAK;AACnB,YAAIA,GAAE;AAAA,MACV,OACK;AACD,kBAAU;AAAA,MACd;AAAA,IACJ,SAAS;AACT,WAAO;AAAA,MACH,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAWA,SAAS,KAAK,GAAG;AACb,SAAO,UAAU,GAAG,MAAM,IAAI;AAClC;AACA,SAAS,MAAM,GAAG;AACd,SAAO,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;AACzD;AACA,SAAS,GAAG,IAAI,IAAI,MAAM;AACtB,SAAO,CAAC,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,IAAI,MAAM,KAAK,GAAG,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;AAChI;AACA,SAAS,KAAK,IAAI,IAAI;AAClB,SAAO,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;AAChC;AACA,SAAS,MAAM,IAAI,IAAI;AACnB,SAAO,GAAG,IAAI,IAAI,CAAC,IAAI,OAAO,EAAE;AACpC;AACA,SAAS,IAAI,IAAI,IAAI,IAAI,MAAM;AAC3B,SAAO,CAAC,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,OAAO,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,IAAI,MAAM,KAAK,GAAG,OAAO,GAAG,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACnL;AACA,SAAS,OAAO,IAAI,IAAI,IAAI;AACxB,SAAO,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,EAAE;AACzC;AACA,SAAS,OAAO,IAAI;AAChB,SAAO,CAAC,MAAM,MAAM;AAChB,UAAM,SAAS,CAAC;AAChB,QAAI,WAAW;AACf,eAAW,KAAK,IAAI;AAChB,YAAM,KAAK,EAAE,MAAM,QAAQ;AAC3B,UAAI,GAAG,SAAS;AACZ,eAAO,KAAK,GAAG,KAAK;AACpB,mBAAW,GAAG;AAAA,MAClB,OACK;AACD,eAAO,EAAE,SAAS,MAAM;AAAA,MAC5B;AAAA,IACJ;AACA,WAAO;AAAA,MACH,SAAS;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAIA,SAAS,WAAW,IAAI;AACpB,SAAO,SAAS,IAAI,GAAG,EAAE,CAAC;AAC9B;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,IAAI,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9C;AAOA,SAAS,YAAY,KACrB,GAAG;AACC,SAAO,CAAC,MAAM,MAAM;AAChB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,MAAM;AACV,OAAG;AACC,YAAMC,KAAI,EAAE,MAAM,MAAM,GAAG,EAAE,MAAM,GAAG;AACtC,UAAIA,GAAE,SAAS;AACX,eAAOA,GAAE;AACT,cAAMA,GAAE;AAAA,MACZ,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ,SAAS;AACT,WAAO;AAAA,MACH,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,KAAK,GACzB,SAAS;AACL,SAAO,YAAY,KAAK,CAACC,SAAQ,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,QAAQA,MAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3F;AAWA,SAAS,WAAW,OAAO,OAAO,QAAQ;AACtC,SAAO,MAAM,OAAO,CAAC,OAAO,WAAW,IAAI,GAAG,OAAO,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/G;AAYA,SAAS,MAAM,GACf,GAAG;AACC,SAAO,CAAC,MAAM,MAAM,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,MAAM,GAAG,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAQ,CAAC;AACzG;;;AC3SA,IAAI,MAAmB,OAAO,OAAO;AAAA,EACjC,WAAW;AACf,CAAC;AAED,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU,aAAa,QAAQ,IAAI,OAAO,IAAI,MAAM;AAC1D,IAAM,SAAS,iBAAiB,QAAQ,IAAI,OAAO,IAAI,MAAM;AAC7D,IAAM,OAAO,MAAM,MAAM;AACzB,IAAM,QAAQ,UAAU,OAAO,GAAG,MAAM;AACxC,IAAM,UAAU,2BAA2B,EAAE,IAAI,QAAQ,IAAI,OAAO,IAAI,MAAM;AAC9E,IAAM,UAAU,2BAA2B,EAAE,IAAI,QAAQ,IAAI,OAAO,IAAI,MAAM;AAC9E,IAAM,cAAc,EAAY;AAAA,EAC5B,EAAE,MAAM,MAAM,OAAO,IAAI,OAAO,EAAE,EAAE;AAAA,EACpC,EAAE,MAAM,QAAQ,OAAO,IAAI,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE;AAAA,EACnD,EAAE,MAAM,SAAS,OAAO,IAAI,OAAO,OAAO,GAAG,EAAE;AAAA,EAC/C,EAAE,MAAM,QAAQ,OAAO,IAAI,OAAO,SAAS,GAAG,EAAE;AAAA,EAChD,EAAE,MAAM,QAAQ,OAAO,IAAI,OAAO,SAAS,GAAG,EAAE;AAAA,EAChD,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAAA,EACZ,EAAE,MAAM,IAAI;AAChB,CAAC;AACD,IAAM,mBAAmB,EAAY;AAAA,EACjC,EAAE,MAAM,WAAW,OAAO,IAAI,OAAO,SAAS,GAAG,EAAE;AAAA,EACnD,EAAE,MAAM,UAAU,OAAO,IAAI,OAAO,QAAQ,GAAG,EAAE;AAAA,EACjD,EAAE,MAAM,OAAO,OAAO,IAAI,OAAO,YAAY,GAAG,EAAE;AACtD,CAAC;AACD,SAAS,QAAQ,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG;AACzC,SAAO,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACrC;AACA,SAAS,WAAW,IAAI;AACpB,SAAO,GAAG,OAAO,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC;AACvC;AACA,IAAM,0BAA4B,MAAM,CAACC,OAAMA,GAAE,SAAS,YAAY,OAAO,cAAc,SAASA,GAAE,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,MAAS;AACrI,IAAM,mBAAqB,MAAM,CAACA,OAAMA,GAAE,SAAS,WAAWA,GAAE,KAAK,MAAM,CAAC,IAAI,MAAS;AACzF,IAAM,WAAa,MAAM,CAACA,OAAMA,GAAE,SAAS,QAAQA,GAAE,OAAO,MAAS;AACrE,IAAM,iBAAmB,IAAM,KAAO,OAAG,yBAAyB,kBAAkB,QAAQ,CAAC,GAAG,CAAC,OAAO,GAAG,KAAK,EAAE,CAAC;AACnH,SAAS,SAAS,eAAe;AAC7B,QAAM,cAAc,iBAAiB,aAAa;AAClD,QAAM,SAAS,eAAe,EAAE,QAAQ,YAAY,QAAQ,SAAS,OAAU,GAAG,CAAC;AACnF,SAAO,OAAO;AAClB;AACA,SAAS,QAAQC,OAAM;AACnB,SAAS,MAAM,CAACD,OAAMA,GAAE,SAASC,QAAO,OAAO,MAAS;AAC5D;AACA,IAAM,cAAgB,MAAM,CAACD,OAAMA,GAAE,SAAS,OAAO,OAAO,MAAS;AACrE,IAAM,sBAAwB,OAAO,aAAa,IAAI;AACtD,SAAS,iBAAiB,QAAQ;AAC9B,SAAS,OAAO,qBAAqB,QAAQ,mBAAmB;AACpE;AACA,IAAM,cAAgB,MAAM,CAACA,OAAMA,GAAE,SAAS,UAAU,SAASA,GAAE,IAAI,IAAI,MAAS;AACpF,IAAM,UAAY,MAAM,CAACA,OAAMA,GAAE,SAAS,SAAS,SAASA,GAAE,KAAK,MAAM,CAAC,CAAC,IAAI,MAAS;AACxF,IAAM,UAAY,MAAM,CAACA,OAAMA,GAAE,KAAK,WAAW,KAAK,IAAI,SAASA,GAAE,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI,MAAS;AACnG,IAAM,aAAe,KAAO,OAAO,aAAa,EAAE,GAAG,QAAQ,GAAG,CAAC;AACjE,IAAM,iBAAmB,UAAW,GAAG,YAAY,aAAa,CAAC,IAAIC,WAAU,EAAE,MAAMA,OAAM,WAAW,GAAG,EAAE,GAAK,IAAI,aAAa,CAACA,WAAU,EAAE,MAAMA,OAAM,WAAW,KAAK,EAAE,CAAC;AAC/K,IAAM,eAAiB,UAAW,GAAG,YAAY,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,MAAM,aAAa,WAAW,IAAI,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,GAAK,IAAI,QAAQ,GAAG,GAAG,OAAO,EAAE,MAAM,aAAa,WAAW,MAAM,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;AACnO,IAAM,eAAiB,IAAI,gBAAgB,CAAC,EAAE,MAAAA,OAAM,UAAU,OAAO;AAAA,EACjE,MAAM;AAAA,EACN,MAAMA;AAAA,EACN;AAAA,EACA,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,EAAE;AACF,IAAM,iBAAmB,GAAG,QAAQ,GAAG,GAAG,aAAa,CAAC,UAAUA,WAAU;AAAA,EACxE,MAAM;AAAA,EACN,MAAMA;AAAA,EACN,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,EAAE;AACF,IAAM,cAAgB,IAAI,SAAS,CAACA,WAAU;AAAA,EAC1C,MAAM;AAAA,EACN,MAAMA;AAAA,EACN,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,EAAE;AACF,IAAM,gBAAkB,MAAM,CAACD,OAAM;AACjC,MAAIA,GAAE,SAAS,SAAS;AACpB,QAAIA,GAAE,SAAS,OAAOA,GAAE,SAAS,KAAK;AAClC,aAAO;AAAA,IACX;AACA,QAAIA,GAAE,SAAS,OAAOA,GAAE,SAAS,KAAK;AAClC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX,CAAC;AACD,IAAM,aAAe,UAAW,GAAG,SAAW,OAAS,MAAM,qBAAqB,aAAa,GAAG,IAAI,GAAG,CAAC,GAAG,SAAS,EAAE,OAAO,GAAG,UAAU,IAAI,EAAE,GAAK,GAAG,aAAe,OAAS,MAAM,aAAa,aAAa,GAAG,IAAI,GAAG,CAAC,GAAG,SAAS,EAAE,OAAO,GAAG,UAAU,IAAI,EAAE,CAAC;AACtQ,IAAM,eAAiB,OAAS,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,GAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM,IAAI,GAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM,IAAI,GAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM,IAAI,GAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM,IAAI,GAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM,IAAI,CAAC;AAClS,IAAM,wBAA0B,IAAI,QAAQ,GAAG,GAAG,iBAAiB,cAAc,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,EAAE,MAAAC,OAAM,UAAU,OAAO;AAAA,EAC7H,MAAM;AAAA,EACN,MAAMA;AAAA,EACN;AAAA,EACA,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,EAAE;AACF,IAAM,qBAAuB,OAAO,QAAQ,GAAG,GAAK,IAAI,iBAAiB,cAAc,GAAG,cAAc,iBAAiB,UAAU,GAAG,CAAC,EAAE,MAAAA,OAAM,UAAU,GAAG,SAAS,EAAE,OAAO,SAAS,OAAO;AAAA,EAC1L,MAAM;AAAA,EACN,MAAMA;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,EAAE,GAAG,QAAQ,GAAG,CAAC;AACjB,IAAM,gBAAkB,UAAS,uBAAuB,kBAAkB;AAC1E,IAAM,gBAAkB,UAAS,cAAc,YAAY;AAC3D,IAAM,oBAAsB,OAAO,aAAa,gBAAgB,aAAa;AAC7E,IAAM,oBAAsB,IAAM,UAAW,QAAQ,eAAiB,KAAK,iBAAiB,CAAC,GAAK,MAAM,iBAAiB,CAAC,GAAG,CAAC,OAAO;AACjI,SAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,WAAW,GAAG,IAAI,CAAAC,OAAKA,GAAE,WAAW,CAAC;AAAA,EACtD;AACJ,CAAC;AACD,IAAM,cAAgB,OAAS,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,GAAK,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,GAAK,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,GAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM,IAAI,CAAC;AACzK,IAAM,uBAAyB,UAAS,iBAAiB,WAAW,GAAK,IAAI,aAAa,MAAM,GAAG,CAAC;AACpG,IAAM,mBAAqB,WAAW,mBAAqB,IAAI,sBAAsB,CAACC,OAAM,CAACC,OAAMC,YAAW;AAAA,EAC1G,MAAM;AAAA,EACN,MAAM,CAAC,GAAGA,OAAM,MAAM,EAAE,MAAM,cAAc,YAAYF,IAAG,MAAMC,OAAM,aAAaA,MAAK,YAAY,CAAC;AAAA,EACtG,aAAa,QAAQA,MAAK,aAAaC,OAAM,WAAW;AAC5D,EAAE,GAAG,iBAAiB;AACtB,IAAM,gBAAkB,WAAa,IAAI,kBAAkB,CAACH,QAAO,EAAE,MAAM,QAAQ,MAAM,CAACA,EAAC,EAAE,EAAE,GAAK,IAAI,iBAAiB,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,UAAU,EAAE,MAAM,QAAQ,MAAM,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE,EAAE,GAAG,gBAAgB;AAC7N,SAAS,OAAO,QAAQ,KAAK;AACzB,MAAI,EAAE,OAAO,QAAQ,YAAY,eAAe,SAAS;AACrD,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC/E;AACA,QAAM,cAAc,YAAY,GAAG;AACnC,MAAI,CAAC,YAAY,UAAU;AACvB,UAAM,IAAI,MAAM,cAAc,GAAG,qDAAqD,YAAY,MAAM;AAAA,IACpG,oBAAoB,KAAK,YAAY,MAAM,CAAC;AAAA,EACpD;AACA,QAAM,SAAS,iBAAiB,MAAM,EAAE,EAAE,QAAQ,YAAY,QAAQ,SAAS,OAAU,GAAG,CAAC;AAC7F,MAAI,CAAC,OAAO,SAAS;AACjB,UAAM,IAAI,MAAM,iBAAiB,GAAG,UAAU;AAAA,EAClD;AACA,MAAI,OAAO,WAAW,YAAY,OAAO,QAAQ;AAC7C,UAAMI,SAAQ,YAAY,OAAO,OAAO,QAAQ;AAChD,UAAM,IAAI,MAAM,cAAc,GAAG,kDAAkDA,OAAM,MAAM;AAAA,IAC3F,oBAAoB,KAAKA,OAAM,QAAQA,OAAM,GAAG,CAAC;AAAA,EACzD;AACA,SAAO,OAAO;AAClB;AACA,SAAS,oBAAoB,KAAK,QAAQ,MAAM,GAAG;AAC/C,SAAO,GAAG,IAAI,QAAQ,mBAAmB,CAAC,GAAGN,IAAGO,OAAMP,KAAI,MAAWO,KAAI,MAAW,GAAQ,CAAC;AAAA,EAAK,GAAG,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,GAAG,CAAC;AACzI;AAIA,SAAS,OAAO,KAAK;AACjB,SAAO,OAAO,kBAAkB,GAAG;AACvC;AAEA,SAAS,UAAU,UAAU;AACzB,MAAI,CAAC,SAAS,MAAM;AAChB,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,UAAQ,SAAS,MAAM;AAAA,IACnB,KAAK;AACD,aAAO,OAAO,SAAS,SAAS,IAAI;AAAA,IACxC,KAAK;AACD,aAAO,OAAO,SAAS,SAAS,IAAI,UAAU,SAAS,IAAI;AAAA,IAC/D,KAAK;AACD,aAAO,MAAM,UAAU,SAAS,IAAI;AAAA,IACxC,KAAK;AACD,aAAO,MAAM,UAAU,SAAS,IAAI;AAAA,IACxC,KAAK;AACD,aAAO,IAAI,OAAO,SAAS,SAAS,CAAC,GAAG,UAAU,SAAS,IAAI,CAAC;AAAA,IACpE,KAAK;AACD,aAAO,IAAI,OAAO,SAAS,SAAS,CAAC,GAAG,UAAU,SAAS,IAAI,CAAC,GAAG,SAAS,OAAO,IAAI,QAAQ,SAAS,KAAK,CAAC,IAAK,SAAS,WAAW,SAAS,WAAW,EAAG;AAAA,IAClK,KAAK;AACD,aAAO,UAAU,SAAS,IAAI,IAAI,SAAS;AAAA,IAC/C,KAAK;AACD,aAAO,SAAS,KAAK,OAAO,CAAC,KAAK,SAAS;AACvC,YAAI,KAAK,SAAS,cAAc;AAC5B,iBAAO,UAAU,IAAI,IAAI;AAAA,QAC7B,OACK;AACD,iBAAO,MAAM,UAAU,IAAI;AAAA,QAC/B;AAAA,MACJ,GAAG,EAAE;AAAA,IACT,KAAK;AACD,aAAO,SAAS,KAAK,IAAI,SAAS,EAAE,KAAK,GAAG;AAAA,EACpD;AACJ;AACA,SAAS,OAAO,IAAI;AAChB,SAAQ,MAAM,OAAO,KACf,UAAU,EAAE,IAAI,MAChB;AACV;AACA,SAAS,WAAW,MAAM;AACtB,SAAO,KAAK,KAAK,YAAY,CAAC,EAAE,SAAS,EAAE,CAAC;AAChD;AACA,SAAS,UAAU,KAAK;AACpB,SAAO,IAAI;AAAA,IACX;AAAA,IAA6F,CAAC,GAAG,IAAI,IAAI,IAAI,MAAMC,KAAI,MAAM,UAAU,KAAK,WAAW,EAAE,IACrJ,KAAK,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,IAC7B,KAAK,QACD,OAAO,OACHA,MAAK,MACD,OAAO,WAAW,IAAI,IAClB,OAAO;AAAA,EAAK;AACxC;AACA,SAAS,QAAQ,KAAK;AAClB,SAAO,IAAI;AAAA,IACX;AAAA,IAAuC,CAAC,GAAG,IAAI,IAAIA,KAAI,SAAS,KAAK,QACjE,KAAK,SACDA,MAAK,MACD,WAAW,IAAI;AAAA,EAAC;AAChC;AACA,SAAS,UAAU,UAAU;AACzB,MAAI,CAAC,SAAS,MAAM;AAChB,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,UAAQ,SAAS,MAAM;AAAA,IACnB,KAAK,YAAY;AACb,eAAS,KAAK,QAAQ,SAAS;AAC/B,eAAS,KAAK,KAAK,CAAC,GAAG,MAAM,eAAe,qBAAqB,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;AAC7F;AAAA,IACJ;AAAA,IACA,KAAK,cAAc;AACf,gBAAU,SAAS,IAAI;AACvB;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,eAAS,KAAK,QAAQ,SAAS;AAC/B,eAAS,KAAK,KAAK,CAAC,GAAG,MAAO,UAAU,CAAC,IAAI,UAAU,CAAC,IAAK,KAAK,CAAC;AACnE;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,UAAU;AACpC,UAAQ,SAAS,MAAM;AAAA,IACnB,KAAK;AACD,aAAO,CAAC,CAAC;AAAA,IACb,KAAK;AACD,aAAO,CAAC,CAAC;AAAA,IACb,KAAK;AACD,aAAO,CAAC,CAAC;AAAA,IACb,KAAK;AACD,aAAO,CAAC,GAAG,SAAS,IAAI;AAAA,IAC5B,KAAK;AACD,aAAO,CAAC,GAAG,UAAU,QAAQ,CAAC;AAAA,IAClC,KAAK;AACD,aAAO,CAAC,GAAG,UAAU,QAAQ,CAAC;AAAA,IAClC,KAAK;AACD,aAAO,CAAC,IAAI,UAAU,QAAQ,CAAC;AAAA,EACvC;AACJ;AAIA,SAAS,mBAAmB,GAAG,GAAG;AAC9B,SAAO,eAAe,GAAG,CAAC;AAC9B;AACA,SAAS,eAAe,GAAG,GAAG;AAC1B,MAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,GAAG;AACxC,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC/C;AACA,QAAM,UAAW,EAAE,SAAS,EAAE,SAAU,EAAE,SAAS,EAAE;AACrD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACf;AAAA,IACJ;AACA,WAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAK,KAAK;AAAA,EAChC;AACA,SAAO,EAAE,SAAS,EAAE;AACxB;;;ACnRA,IAAI,MAAmB,OAAO,OAAO;AAAA,EACjC,WAAW;AACf,CAAC;AAED,IAAI,QAAqB,OAAO,OAAO;AAAA,EACnC,WAAW;AACf,CAAC;AAED,IAAM,UAAU,CAAC,UAAU,QAAQ,aAAa,OAAO,SAAS;AAChE,IAAM,YAAY,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC;AAC7C,IAAM,aAAa,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC;AAC9C,IAAM,cAAc,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC;AAC/C,SAAS,aAAa,OAAO,MAAM,YAAY;AAC3C,SAAO,YAAY,KAAK,MAAM,IAAI,CAAAC,OAAK,YAAYA,EAAC,CAAC,CAAC;AAC1D;AACA,SAAS,YAAY,MAAM;AACvB,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK,YAAY;AACb,YAAM,OAAO,KAAK;AAClB,aAAO,MAAM,KAAK,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,CAAC,IAAI,KAAK,KAAK;AAAA,IAC7E;AAAA,IACA,KAAK;AACD,aAAO;AAAA,EAAe,aAAa,KAAK,UAAU,WAAW,CAAC;AAAA,IAClE,KAAK;AACD,aAAO,iBAAiB,KAAK,IAAI;AAAA,EAAK,aAAa,KAAK,UAAU,WAAW,CAAC;AAAA,IAClF,KAAK;AACD,aAAO,oBAAoB,KAAK,IAAI;AAAA,EAAK,aAAa,KAAK,IAAI,CAAC;AAAA,IACpE,KAAK;AACD,aAAO,mBAAmB,KAAK,UAAU;AAAA,EAAK,aAAa,KAAK,MAAM,SAAS,CAAC;AAAA,IACpF,KAAK;AACD,aAAO;AAAA,EAAkB,aAAa,KAAK,MAAM,SAAS,CAAC;AAAA,IAC/D,KAAK;AACD,aAAO,OAAO,KAAK,KAAK;AAAA,EAAK,aAAa,KAAK,IAAI,CAAC;AAAA,IACxD,KAAK;AACD,aAAO,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,YAAY,EAAE;AAAA,EAAK,aAAa,KAAK,IAAI,CAAC;AAAA,EAClG;AACJ;AACA,SAAS,YAAY,KAAK,OAAO;AAC7B,SAAO,MACF,IAAI,CAAC,MAAM,GAAG,EAAE,OAAO,MAAM,WAAW,KAAK,MAAM,MAAM,SAAS,CAAC,CAAC,EACpE,KAAK,IAAI;AAClB;AACA,SAAS,WAAW,KAAK,MAAM,OAAO,MAAM;AACxC,QAAM,OAAO,IAAI,OAAO,IAAI,CAAC;AAC7B,SAAO,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,EAAE,KAAK,OAAO,KAAK,CAAC,CAAC;AACzD;AAEA,IAAI,iBAA8B,OAAO,OAAO;AAAA,EAC5C,WAAW;AAAA,EACX;AACJ,CAAC;AAED,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,OAAO;AACf,SAAK,WAAW,MAAM,mBAAmB,KAAK,CAAC;AAAA,EACnD;AAAA,EACA,MAAM,SAAS;AACX,WAAO,QAAQ,KAAK,QAAQ;AAAA,EAChC;AACJ;AACA,SAAS,mBAAmB,OAAO;AAC/B,QAAM,MAAM,MAAM;AAClB,QAAM,UAAU,IAAI,MAAM,GAAG;AAC7B,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,CAAC,gBAAgB,GAAG,IAAI,MAAM,CAAC;AACrC,UAAMC,OAAM,WAAoB,OAAO,cAAc,CAAC;AACtD,YAAQ,CAAC,IAAI;AAAA,MACT,KAAKA;AAAA,MACL,UAAU;AAAA,QACN,MAAM;AAAA,QACN,gBAAgB,EAAE,OAAO,GAAG,OAAO,KAAK,aAAaA,KAAI,YAAY;AAAA,MACzE;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAWA,MAAK;AACrB,yBAAuBA,IAAG;AAC1B,EAAS,UAAUA,IAAG;AACtB,SAAOA;AACX;AACA,SAAS,uBAAuBA,MAAK;AACjC,QAAM,UAAU,CAAC;AACjB,EAAAA,KAAI,KAAK,QAAQ,SAAO;AACpB,YAAQ,IAAI,MAAM;AAAA,MACd,KAAK;AACD,gBAAQ,KAAK;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,MAAM;AAAA,UACN,WAAW;AAAA,UACX,aAAa,IAAI;AAAA,UACjB,MAAM;AAAA,UACN,OAAO,IAAI;AAAA,QACf,CAAC;AACD;AAAA,MACJ,KAAK;AACD,gBAAQ,KAAK;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,MAAM;AAAA,UACN,WAAW;AAAA,UACX,aAAa,IAAI;AAAA,UACjB,MAAM;AAAA,UACN,OAAO,IAAI;AAAA,QACf,CAAC;AACD;AAAA,MACJ,KAAK;AACD,+BAAuB,IAAI,IAAI;AAC/B,gBAAQ,KAAK,GAAG;AAChB;AAAA,MACJ,KAAK;AACD;AAAA,MACJ;AACI,gBAAQ,KAAK,GAAG;AAChB;AAAA,IACR;AAAA,EACJ,CAAC;AACD,EAAAA,KAAI,OAAO;AACf;AACA,SAAS,MAAM,OAAO;AAClB,QAAM,WAAW,CAAC;AAClB,SAAO,MAAM,QAAQ;AACjB,UAAM,UAAU,WAAW,OAAO,CAAC,QAAQ,MAAM,eAAe;AAChE,UAAM,EAAE,SAAS,YAAY,MAAM,IAAI,YAAY,OAAO,OAAO;AACjE,YAAQ;AACR,QAAI,QAAQ,QAAQ;AAChB,eAAS,KAAK,aAAa,SAAS,OAAO,CAAC;AAAA,IAChD;AACA,QAAI,MAAM,QAAQ;AACd,eAAS,KAAK,GAAG,UAAU,KAAK,CAAC;AAAA,IACrC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,UAAU,OAAO;AACtB,QAAM,UAAU,CAAC;AACjB,aAAW,QAAQ,OAAO;AACtB,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,SAAS,YAAY;AAC9B,cAAQ,KAAK,QAAQ;AAAA,IACzB,OACK;AACD,YAAM,EAAE,SAAS,KAAK,IAAI,UAAU,SAAS,MAAM,CAAC,SAAS,KAAK,SAAS,UAAU;AACrF,cAAQ,QAAQ,CAAC,SAAS,QAAQ,KAAK,IAAI,CAAC;AAC5C,UAAI,KAAK,QAAQ;AACb,iBAAS,OAAO;AAChB,gBAAQ,KAAK,QAAQ;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO,cAAc;AACtC,QAAM,UAAU,CAAC;AACjB,QAAM,aAAa,CAAC;AACpB,QAAM,QAAQ,CAAC;AACf,aAAW,QAAQ,OAAO;AACtB,UAAM,WAAW,KAAK,IAAI;AAC1B,QAAI,SAAS,QAAQ;AACjB,YAAM,UAAU,SAAS,KAAK,UAAQ,gBAAgB,IAAI,MAAM,YAAY;AAC5E,OAAC,UAAU,UAAU,YAAY,KAAK,IAAI;AAAA,IAC9C,OACK;AACD,YAAM,KAAK,IAAI;AAAA,IACnB;AAAA,EACJ;AACA,SAAO,EAAE,SAAS,YAAY,MAAM;AACxC;AACA,SAAS,gBAAgB,KAAK;AAC1B,UAAQ,IAAI,MAAM;AAAA,IACd,KAAK;AACD,aAAO,gBAAgB,IAAI,IAAI;AAAA,IACnC,KAAK;AACD,aAAO,aAAa,IAAI,IAAI;AAAA,IAChC,KAAK;AACD,aAAO,cAAc,IAAI,UAAU;AAAA,IACvC;AACI,aAAO,IAAI;AAAA,EACnB;AACJ;AACA,SAAS,aAAa,MAAM,OAAO;AAC/B,MAAI,SAAS,OAAO;AAChB,WAAO,cAAc,KAAK;AAAA,EAC9B;AACA,MAAI,KAAK,WAAW,YAAY,GAAG;AAC/B,WAAO,gBAAgB,KAAK,UAAU,EAAE,GAAG,KAAK;AAAA,EACpD;AACA,MAAI,KAAK,WAAW,eAAe,GAAG;AAClC,WAAO,mBAAmB,KAAK,UAAU,EAAE,GAAG,KAAK;AAAA,EACvD;AACA,MAAI,SAAS,gBAAgB;AACzB,WAAO,iBAAiB,KAAK,KAAK;AAAA,EACtC;AACA,MAAI,SAAS,gBAAgB;AACzB,WAAO,iBAAiB,KAAK,KAAK;AAAA,EACtC;AACA,QAAM,IAAI,MAAM,8BAA8B,IAAI,EAAE;AACxD;AACA,SAAS,cAAc,OAAO;AAC1B,QAAM,SAAS,eAAe,OAAO,CAAC,MAAM,EAAE,SAAS,OAAO,CAAC,MAAM,EAAE,IAAI;AAC3E,QAAM,WAAW,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAACC,OAAM,KAAK,OAAO;AAAA,IAC5D,MAAM;AAAA,IACN,OAAOA;AAAA,IACP,MAAM,MAAM,MAAM,KAAK;AAAA,EAC3B,EAAE;AACF,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmBA,OAAM,OAAO;AACrC,aAAW,QAAQ,OAAO;AACtB,yBAAqB,MAAM,CAAC,MAAO,EAAE,SAAS,kBAAoB,EAAE,SAASA,KAAK;AAAA,EACtF;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAMA;AAAA,IACN,MAAM,MAAM,KAAK;AAAA,EACrB;AACJ;AACA,SAAS,gBAAgBA,OAAM,OAAO;AAClC,QAAM,SAAS,eAAe,OAAO,CAAC,MAAO,EAAE,SAAS,eAAiB,EAAE,SAASA,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE;AAC/I,QAAM,WAAW,CAAC;AAClB,aAAW,SAAS,OAAO,OAAO,MAAM,GAAG;AACvC,UAAM,MAAM,MAAM;AAClB,UAAM,YAAY,iBAAiB,GAAG;AACtC,UAAM,eAAe,MAAM,MAAM,KAAK;AACtC,aAAS,KAAK;AAAA,MACV,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb,UAAU,IAAI;AAAA,MACd,OAAO,IAAI;AAAA,MACX;AAAA,MACA,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAMA;AAAA,IACN;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,KAAK;AAC3B,MAAI,IAAI,aAAa,KAAK;AACtB,UAAM,WAAW,IAAI,MAAM,YAAY;AACvC,YAAQ,IAAI,SAAS;AAAA,MACjB,KAAK;AACD,eAAO,CAAC,WAAW,aAAa,OAAO,YAAY;AAAA,MACvD,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,YAAY,EAAE,MAAM,QAAQ,EAAE,SAAS,QAAQ;AAAA,MAC7E,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,YAAY,EAAE,WAAW,QAAQ;AAAA,MAC/D,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,YAAY,EAAE,SAAS,QAAQ;AAAA,MAC7D,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,YAAY,EAAE,SAAS,QAAQ;AAAA,MAC7D,KAAK;AACD,eAAO,CAAC,WAAW;AACf,gBAAM,QAAQ,OAAO,YAAY;AACjC,iBAAQ,aAAa,SAAW,MAAM,WAAW,QAAQ,KAAK,MAAM,SAAS,MAAM,MAAM;AAAA,QAC7F;AAAA,IACR;AAAA,EACJ,OACK;AACD,UAAM,WAAW,IAAI;AACrB,YAAQ,IAAI,SAAS;AAAA,MACjB,KAAK;AACD,eAAO,CAAC,WAAW,aAAa;AAAA,MACpC,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,MAAM,QAAQ,EAAE,SAAS,QAAQ;AAAA,MAC/D,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,WAAW,QAAQ;AAAA,MACjD,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,SAAS,QAAQ;AAAA,MAC/C,KAAK;AACD,eAAO,CAAC,WAAW,OAAO,SAAS,QAAQ;AAAA,MAC/C,KAAK;AACD,eAAO,CAAC,WAAY,aAAa,UAAY,OAAO,WAAW,QAAQ,KAAK,OAAO,SAAS,MAAM,MAAM;AAAA,IAChH;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,YAAY,OAAO;AACzC,QAAM,SAAS,eAAe,OAAO,CAAC,MAAO,EAAE,SAAS,gBAAkB,EAAE,eAAe,YAAa,CAAC,MAAe,UAAU,EAAE,IAAI,CAAC;AACzI,QAAM,YAAY,CAAC;AACnB,aAAW,SAAS,OAAO,OAAO,MAAM,GAAG;AACvC,UAAM,YAAY,MAAM,MAAM,KAAK;AACnC,UAAM,UAAU,MAAM,kBAAkB;AACxC,cAAU,KAAK;AAAA,MACX,KAAK;AAAA,MACL,UAAU,EAAE,MAAM,cAAc,MAAM,UAAU;AAAA,IACpD,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,MAAM,MAAM,SAAS;AAAA,EACzB;AACJ;AACA,SAAS,eAAe,OAAO,WAAW,aAAa;AACnD,QAAM,SAAS,CAAC;AAChB,SAAO,MAAM,QAAQ;AACjB,UAAM,UAAU,WAAW,OAAO,WAAW,WAAW;AACxD,UAAM,mBAAmB,CAAC,QAAQ,UAAU,GAAG,KAAK,YAAY,GAAG,MAAM;AACzE,UAAM,sBAAsB,CAAC,SAAS,KAAK,IAAI,KAAK,KAAK,gBAAgB;AACzE,UAAM,EAAE,SAAS,KAAK,IAAI,WAAW,OAAO,mBAAmB;AAC/D,QAAI,oBAAoB;AACxB,eAAW,QAAQ,SAAS;AACxB,YAAM,cAAc,qBAAqB,MAAM,gBAAgB;AAC/D,UAAI,CAAC,mBAAmB;AACpB,4BAAoB;AAAA,MACxB;AAAA,IACJ;AACA,QAAI,qBAAqB,MAAM;AAC3B,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAClD;AACA,WAAO,OAAO,IAAI,EAAE,mBAAsC,OAAO,QAAQ;AACzE,YAAQ;AAAA,EACZ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM,WAAW;AAC3C,QAAM,WAAW,KAAK,IAAI;AAC1B,QAAM,UAAU,IAAI,MAAM,SAAS,MAAM;AACzC,MAAI,aAAa;AACjB,WAAS,IAAI,SAAS,QAAQ,MAAM,KAAI;AACpC,QAAI,UAAU,SAAS,CAAC,CAAC,GAAG;AACxB,cAAQ,CAAC,IAAI;AACb,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,MAAI,cAAc,IAAI;AAClB,UAAM,IAAI,MAAM,6CAA6C;AAAA,EACjE;AACA,QAAM,SAAS,SAAS,UAAU;AAClC,OAAK,IAAI,OAAO,SAAS,OAAO,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvD,SAAO;AACX;AACA,SAAS,WAAW,OAAO,WAAW,aAAa;AAC/C,QAAM,aAAa,CAAC;AACpB,aAAW,QAAQ,OAAO;AACtB,UAAM,cAAc,CAAC;AACrB,eAAW,QAAQ,KAAK,IAAI,KAAK,OAAO,SAAS,GAAG;AAChD,kBAAY,YAAY,IAAI,CAAC,IAAI;AAAA,IACrC;AACA,eAAW,OAAO,OAAO,KAAK,WAAW,GAAG;AACxC,UAAI,WAAW,GAAG,GAAG;AACjB,mBAAW,GAAG;AAAA,MAClB,OACK;AACD,mBAAW,GAAG,IAAI;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,aAAW,SAAS,OAAO,QAAQ,UAAU,GAAG;AAC5C,QAAI,MAAM,CAAC,IAAI,YAAY;AACvB,gBAAU,MAAM,CAAC;AACjB,mBAAa,MAAM,CAAC;AAAA,IACxB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,UAAU,KAAK,WAAW;AAC/B,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO,CAAC;AACd,aAAW,KAAK,KAAK;AACjB,QAAI,UAAU,CAAC,GAAG;AACd,cAAQ,KAAK,CAAC;AAAA,IAClB,OACK;AACD,WAAK,KAAK,CAAC;AAAA,IACf;AAAA,EACJ;AACA,SAAO,EAAE,SAAS,KAAK;AAC3B;AACA,SAAS,WAAW,KAAK,WAAW;AAChC,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO,CAAC;AACd,aAAW,KAAK,KAAK;AACjB,QAAI,UAAU,CAAC,GAAG;AACd,cAAQ,KAAK,CAAC;AAAA,IAClB,OACK;AACD,WAAK,KAAK,CAAC;AAAA,IACf;AAAA,EACJ;AACA,SAAO,EAAE,SAAS,KAAK;AAC3B;AAEA,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,GAAG;AACX,SAAK,IAAI;AAAA,EACb;AAAA,EACA,QAAQ,IAAI;AACR,WAAO,KAAK,EAAE,EAAE;AAAA,EACpB;AAAA,EACA,MAAM,IAAI,cAAc,OAAO;AAC3B,UAAM,UAAU,KAAK,EAAE,EAAE;AACzB,UAAM,MAAM,QAAQ;AACpB,QAAI,QAAQ,GAAG;AACX,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,GAAG;AACX,aAAO,QAAQ,CAAC,EAAE;AAAA,IACtB;AACA,UAAM,aAAc,cACd,wBACA;AACN,QAAI,SAAS,QAAQ,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,WAAW,QAAQ,IAAI,GAAG;AAC1B,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,WAAO,OAAO;AAAA,EAClB;AACJ;AACA,SAAS,sBAAsB,KAAK,MAAM;AACtC,QAAM,OAAO,mBAAmB,KAAK,aAAa,IAAI,WAAW;AACjE,SAAO,OAAO,KAAM,SAAS,KAAK,KAAK,QAAQ,IAAI;AACvD;AACA,SAAS,qBAAqB,KAAK,MAAM;AACrC,QAAM,OAAO,mBAAmB,KAAK,aAAa,IAAI,WAAW;AACjE,SAAO,OAAO,KAAM,SAAS,KAAK,KAAK,QAAQ,IAAI;AACvD;;;AC3aA,SAAS,WAAW,OAAO;AACvB,SAAO,IAAI,OAAO,YAAY,KAAK,CAAC;AACxC;AACA,SAAS,YAAY,OAAO;AACxB,QAAM,WAAW,MAAM,IAAI,UAAU;AACrC,SAAO,CAAC,OAAO,SAAS,SAAS,QAAQ,OAAK,EAAE,IAAI,GAAG,IAAI,CAAC;AAChE;AACA,SAAS,WAAW,MAAM;AACtB,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK,YAAY;AACb,YAAM,SAAS,CAAC,KAAK,cAAc;AACnC,aAAO,CAAC,OAAO,SAAS;AAAA,IAC5B;AAAA,IACA,KAAK;AACD,aAAO,cAAc,IAAI;AAAA,IAC7B,KAAK;AACD,aAAO,oBAAoB,IAAI;AAAA,IACnC,KAAK;AACD,aAAO,uBAAuB,IAAI;AAAA,IACtC,KAAK;AACD,aAAO,sBAAsB,IAAI;AAAA,IACrC,KAAK;AACD,aAAO,qBAAqB,IAAI;AAAA,EACxC;AACJ;AACA,SAAS,cAAc,MAAM;AACzB,QAAM,WAAW,CAAC;AAClB,aAAW,WAAW,KAAK,UAAU;AACjC,aAAS,QAAQ,KAAK,IAAI,YAAY,QAAQ,IAAI;AAAA,EACtD;AACA,SAAO,CAAC,OAAO,SAAS;AACpB,UAAM,eAAe,SAAS,GAAG,IAAI;AACrC,WAAQ,eAAgB,aAAa,IAAI,GAAG,IAAI,IAAI,CAAC;AAAA,EACzD;AACJ;AACA,SAAS,uBAAuB,MAAM;AAClC,QAAM,WAAW,KAAK;AACtB,QAAM,eAAe,YAAY,KAAK,IAAI;AAC1C,SAAO,CAAC,OAAO,SAAU,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,QAAQ,IAC5E,aAAa,IAAI,GAAG,IAAI,IACxB,CAAC;AACX;AACA,SAAS,oBAAoB,MAAM;AAC/B,QAAM,YAAY,CAAC;AACnB,aAAW,WAAW,KAAK,UAAU;AACjC,UAAM,YAAY,QAAQ;AAC1B,UAAM,eAAe,YAAY,QAAQ,IAAI;AAC7C,cAAU,KAAK,CAAC,MAAM,OAAO,SAAU,UAAU,IAAI,IAAI,aAAa,IAAI,GAAG,IAAI,IAAI,CAAC,CAAE;AAAA,EAC5F;AACA,QAAM,WAAW,KAAK;AACtB,SAAO,CAAC,OAAO,SAAS;AACpB,UAAM,OAAO,GAAG,QAAQ,QAAQ;AAChC,WAAQ,QAAQ,SAAS,KACnB,UAAU,QAAQ,QAAM,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,IAC7C,CAAC;AAAA,EACX;AACJ;AACA,SAAS,sBAAsB,MAAM;AACjC,QAAM,eAAe,YAAY,KAAK,IAAI;AAC1C,QAAM,oBAAqB,KAAK,eAAe,MACzC,sBACA;AACN,SAAO,CAAC,OAAO,SAAS;AACpB,UAAM,OAAO,kBAAkB,EAAE;AACjC,QAAI,SAAS,MAAM;AACf,aAAO,CAAC;AAAA,IACZ;AACA,WAAO,aAAa,MAAM,IAAI,GAAG,IAAI;AAAA,EACzC;AACJ;AACA,IAAM,sBAAsB,CAAC,OAAO;AAChC,QAAM,OAAO,GAAG;AAChB,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAQC,OAAM,IAAI,IAAK,OAAO,oBAAoB,IAAI;AAC1D;AACA,IAAM,mBAAmB,CAAC,OAAO;AAC7B,QAAM,SAAS,GAAG;AAClB,SAAQ,UAAUA,OAAM,MAAM,IAAK,SAAS;AAChD;AACA,SAAS,qBAAqB,MAAM;AAChC,QAAM,eAAe,YAAY,KAAK,IAAI;AAC1C,SAAO,CAAC,IAAI,SAAS,SAAS,aAAa,MAAM,GAAG,IAAI;AAC5D;;;ACrFA,IAAA,2BAAe,IAAI;;EAEf,2keACK,MAAM,EAAE,EACR,IAAI,CAACC,OAAMA,GAAE,WAAW,CAAC,CAAC;AAAC;;;ACJpC,IAAA,0BAAe,IAAI;;EAEf,wCACK,MAAM,EAAE,EACR,IAAI,CAACC,OAAMA,GAAE,WAAW,CAAC,CAAC;AAAC;;;;ACJpC,IAAM,YAAY,oBAAI,IAAI;EACtB,CAAC,GAAG,KAAK;;EAET,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,GAAG;CACZ;AAKM,IAAM;;GAET,KAAA,OAAO,mBAAa,QAAA,OAAA,SAAA,KACpB,SAAU,WAAiB;AACvB,QAAI,SAAS;AAEb,QAAI,YAAY,OAAQ;AACpB,mBAAa;AACb,gBAAU,OAAO,aACX,cAAc,KAAM,OAAS,KAAM;AAEzC,kBAAY,QAAU,YAAY;;AAGtC,cAAU,OAAO,aAAa,SAAS;AACvC,WAAO;EACX;;AAOE,SAAU,iBAAiB,WAAiB;;AAC9C,MAAK,aAAa,SAAU,aAAa,SAAW,YAAY,SAAU;AACtE,WAAO;;AAGX,UAAOC,MAAA,UAAU,IAAI,SAAS,OAAC,QAAAA,QAAA,SAAAA,MAAI;AACvC;;;ACvDA,IAAW;CAAX,SAAWC,YAAS;AAChB,EAAAA,WAAAA,WAAA,KAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACJ,GAbW,cAAA,YAAS,CAAA,EAAA;AAgBpB,IAAM,eAAe;AAErB,IAAY;CAAZ,SAAYC,eAAY;AACpB,EAAAA,cAAAA,cAAA,cAAA,IAAA,KAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,eAAA,IAAA,KAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,YAAA,IAAA,GAAA,IAAA;AACJ,GAJY,iBAAA,eAAY,CAAA,EAAA;AAMxB,SAAS,SAAS,MAAY;AAC1B,SAAO,QAAQ,UAAU,QAAQ,QAAQ,UAAU;AACvD;AAEA,SAAS,uBAAuB,MAAY;AACxC,SACK,QAAQ,UAAU,WAAW,QAAQ,UAAU,WAC/C,QAAQ,UAAU,WAAW,QAAQ,UAAU;AAExD;AAEA,SAAS,oBAAoB,MAAY;AACrC,SACK,QAAQ,UAAU,WAAW,QAAQ,UAAU,WAC/C,QAAQ,UAAU,WAAW,QAAQ,UAAU,WAChD,SAAS,IAAI;AAErB;AAQA,SAAS,8BAA8B,MAAY;AAC/C,SAAO,SAAS,UAAU,UAAU,oBAAoB,IAAI;AAChE;AAEA,IAAW;CAAX,SAAWC,qBAAkB;AACzB,EAAAA,oBAAAA,oBAAA,aAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,cAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,gBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,YAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,aAAA,IAAA,CAAA,IAAA;AACJ,GANW,uBAAA,qBAAkB,CAAA,EAAA;AAQ7B,IAAY;CAAZ,SAAYC,eAAY;AAEpB,EAAAA,cAAAA,cAAA,QAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,cAAAA,cAAA,QAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,cAAAA,cAAA,WAAA,IAAA,CAAA,IAAA;AACJ,GAPY,iBAAA,eAAY,CAAA,EAAA;AAuBlB,IAAO,gBAAP,MAAoB;EACtB,YAEqB,YAUA,eAEA,QAA4B;AAZ5B,SAAA,aAAA;AAUA,SAAA,gBAAA;AAEA,SAAA,SAAA;AAIb,SAAA,QAAQ,mBAAmB;AAE3B,SAAA,WAAW;AAOX,SAAA,SAAS;AAGT,SAAA,YAAY;AAEZ,SAAA,SAAS;AAET,SAAA,aAAa,aAAa;EAnB/B;;EAsBH,YAAY,YAAwB;AAChC,SAAK,aAAa;AAClB,SAAK,QAAQ,mBAAmB;AAChC,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,WAAW;EACpB;;;;;;;;;;;;EAaA,MAAM,KAAa,QAAc;AAC7B,YAAQ,KAAK,OAAO;MAChB,KAAK,mBAAmB,aAAa;AACjC,YAAI,IAAI,WAAW,MAAM,MAAM,UAAU,KAAK;AAC1C,eAAK,QAAQ,mBAAmB;AAChC,eAAK,YAAY;AACjB,iBAAO,KAAK,kBAAkB,KAAK,SAAS,CAAC;;AAEjD,aAAK,QAAQ,mBAAmB;AAChC,eAAO,KAAK,iBAAiB,KAAK,MAAM;;MAG5C,KAAK,mBAAmB,cAAc;AAClC,eAAO,KAAK,kBAAkB,KAAK,MAAM;;MAG7C,KAAK,mBAAmB,gBAAgB;AACpC,eAAO,KAAK,oBAAoB,KAAK,MAAM;;MAG/C,KAAK,mBAAmB,YAAY;AAChC,eAAO,KAAK,gBAAgB,KAAK,MAAM;;MAG3C,KAAK,mBAAmB,aAAa;AACjC,eAAO,KAAK,iBAAiB,KAAK,MAAM;;;EAGpD;;;;;;;;;;EAWQ,kBAAkB,KAAa,QAAc;AACjD,QAAI,UAAU,IAAI,QAAQ;AACtB,aAAO;;AAGX,SAAK,IAAI,WAAW,MAAM,IAAI,kBAAkB,UAAU,SAAS;AAC/D,WAAK,QAAQ,mBAAmB;AAChC,WAAK,YAAY;AACjB,aAAO,KAAK,gBAAgB,KAAK,SAAS,CAAC;;AAG/C,SAAK,QAAQ,mBAAmB;AAChC,WAAO,KAAK,oBAAoB,KAAK,MAAM;EAC/C;EAEQ,mBACJ,KACA,OACA,KACA,MAAY;AAEZ,QAAI,UAAU,KAAK;AACf,YAAM,aAAa,MAAM;AACzB,WAAK,SACD,KAAK,SAAS,KAAK,IAAI,MAAM,UAAU,IACvC,SAAS,IAAI,OAAO,OAAO,UAAU,GAAG,IAAI;AAChD,WAAK,YAAY;;EAEzB;;;;;;;;;;EAWQ,gBAAgB,KAAa,QAAc;AAC/C,UAAM,WAAW;AAEjB,WAAO,SAAS,IAAI,QAAQ;AACxB,YAAM,OAAO,IAAI,WAAW,MAAM;AAClC,UAAI,SAAS,IAAI,KAAK,uBAAuB,IAAI,GAAG;AAChD,kBAAU;aACP;AACH,aAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AACjD,eAAO,KAAK,kBAAkB,MAAM,CAAC;;;AAI7C,SAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AAEjD,WAAO;EACX;;;;;;;;;;EAWQ,oBAAoB,KAAa,QAAc;AACnD,UAAM,WAAW;AAEjB,WAAO,SAAS,IAAI,QAAQ;AACxB,YAAM,OAAO,IAAI,WAAW,MAAM;AAClC,UAAI,SAAS,IAAI,GAAG;AAChB,kBAAU;aACP;AACH,aAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AACjD,eAAO,KAAK,kBAAkB,MAAM,CAAC;;;AAI7C,SAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AAEjD,WAAO;EACX;;;;;;;;;;;;;;EAeQ,kBAAkB,QAAgB,gBAAsB;;AAE5D,QAAI,KAAK,YAAY,gBAAgB;AACjC,OAAAC,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,2CACT,KAAK,QAAQ;AAEjB,aAAO;;AAIX,QAAI,WAAW,UAAU,MAAM;AAC3B,WAAK,YAAY;eACV,KAAK,eAAe,aAAa,QAAQ;AAChD,aAAO;;AAGX,SAAK,cAAc,iBAAiB,KAAK,MAAM,GAAG,KAAK,QAAQ;AAE/D,QAAI,KAAK,QAAQ;AACb,UAAI,WAAW,UAAU,MAAM;AAC3B,aAAK,OAAO,wCAAuC;;AAGvD,WAAK,OAAO,kCAAkC,KAAK,MAAM;;AAG7D,WAAO,KAAK;EAChB;;;;;;;;;;EAWQ,iBAAiB,KAAa,QAAc;AAChD,UAAM,EAAE,WAAU,IAAK;AACvB,QAAI,UAAU,WAAW,KAAK,SAAS;AAEvC,QAAI,eAAe,UAAU,aAAa,iBAAiB;AAE3D,WAAO,SAAS,IAAI,QAAQ,UAAU,KAAK,UAAU;AACjD,YAAM,OAAO,IAAI,WAAW,MAAM;AAElC,WAAK,YAAY,gBACb,YACA,SACA,KAAK,YAAY,KAAK,IAAI,GAAG,WAAW,GACxC,IAAI;AAGR,UAAI,KAAK,YAAY,GAAG;AACpB,eAAO,KAAK,WAAW;QAElB,KAAK,eAAe,aAAa;SAE7B,gBAAgB;QAEb,8BAA8B,IAAI,KACxC,IACA,KAAK,6BAA4B;;AAG3C,gBAAU,WAAW,KAAK,SAAS;AACnC,qBAAe,UAAU,aAAa,iBAAiB;AAGvD,UAAI,gBAAgB,GAAG;AAEnB,YAAI,SAAS,UAAU,MAAM;AACzB,iBAAO,KAAK,oBACR,KAAK,WACL,aACA,KAAK,WAAW,KAAK,MAAM;;AAKnC,YAAI,KAAK,eAAe,aAAa,QAAQ;AACzC,eAAK,SAAS,KAAK;AACnB,eAAK,YAAY,KAAK;AACtB,eAAK,SAAS;;;;AAK1B,WAAO;EACX;;;;;;EAOQ,+BAA4B;;AAChC,UAAM,EAAE,QAAQ,WAAU,IAAK;AAE/B,UAAM,eACD,WAAW,MAAM,IAAI,aAAa,iBAAiB;AAExD,SAAK,oBAAoB,QAAQ,aAAa,KAAK,QAAQ;AAC3D,KAAAA,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,wCAAuC;AAEpD,WAAO,KAAK;EAChB;;;;;;;;;;EAWQ,oBACJ,QACA,aACA,UAAgB;AAEhB,UAAM,EAAE,WAAU,IAAK;AAEvB,SAAK,cACD,gBAAgB,IACV,WAAW,MAAM,IAAI,CAAC,aAAa,eACnC,WAAW,SAAS,CAAC,GAC3B,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AAEnB,WAAK,cAAc,WAAW,SAAS,CAAC,GAAG,QAAQ;;AAGvD,WAAO;EACX;;;;;;;;EASA,MAAG;;AACC,YAAQ,KAAK,OAAO;MAChB,KAAK,mBAAmB,aAAa;AAEjC,eAAO,KAAK,WAAW,MAClB,KAAK,eAAe,aAAa,aAC9B,KAAK,WAAW,KAAK,aACvB,KAAK,6BAA4B,IACjC;;MAGV,KAAK,mBAAmB,gBAAgB;AACpC,eAAO,KAAK,kBAAkB,GAAG,CAAC;;MAEtC,KAAK,mBAAmB,YAAY;AAChC,eAAO,KAAK,kBAAkB,GAAG,CAAC;;MAEtC,KAAK,mBAAmB,cAAc;AAClC,SAAAA,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,2CACT,KAAK,QAAQ;AAEjB,eAAO;;MAEX,KAAK,mBAAmB,aAAa;AAEjC,eAAO;;;EAGnB;;AASJ,SAAS,WAAW,YAAuB;AACvC,MAAI,MAAM;AACV,QAAM,UAAU,IAAI,cAChB,YACA,CAAC,QAAS,OAAO,cAAc,GAAG,CAAE;AAGxC,SAAO,SAAS,eACZ,KACA,YAAwB;AAExB,QAAI,YAAY;AAChB,QAAI,SAAS;AAEb,YAAQ,SAAS,IAAI,QAAQ,KAAK,MAAM,MAAM,GAAG;AAC7C,aAAO,IAAI,MAAM,WAAW,MAAM;AAElC,cAAQ,YAAY,UAAU;AAE9B,YAAM,MAAM,QAAQ;QAChB;;QAEA,SAAS;MAAC;AAGd,UAAI,MAAM,GAAG;AACT,oBAAY,SAAS,QAAQ,IAAG;AAChC;;AAGJ,kBAAY,SAAS;AAErB,eAAS,QAAQ,IAAI,YAAY,IAAI;;AAGzC,UAAM,SAAS,MAAM,IAAI,MAAM,SAAS;AAGxC,UAAM;AAEN,WAAO;EACX;AACJ;AAYM,SAAU,gBACZ,YACA,SACA,SACA,MAAY;AAEZ,QAAM,eAAe,UAAU,aAAa,kBAAkB;AAC9D,QAAM,aAAa,UAAU,aAAa;AAG1C,MAAI,gBAAgB,GAAG;AACnB,WAAO,eAAe,KAAK,SAAS,aAAa,UAAU;;AAI/D,MAAI,YAAY;AACZ,UAAM,QAAQ,OAAO;AAErB,WAAO,QAAQ,KAAK,SAAS,cACvB,KACA,WAAW,UAAU,KAAK,IAAI;;AAMxC,MAAI,KAAK;AACT,MAAI,KAAK,KAAK,cAAc;AAE5B,SAAO,MAAM,IAAI;AACb,UAAM,MAAO,KAAK,OAAQ;AAC1B,UAAM,SAAS,WAAW,GAAG;AAE7B,QAAI,SAAS,MAAM;AACf,WAAK,MAAM;eACJ,SAAS,MAAM;AACtB,WAAK,MAAM;WACR;AACH,aAAO,WAAW,MAAM,WAAW;;;AAI3C,SAAO;AACX;AAEA,IAAM,cAAc,WAAW,wBAAc;AAC7C,IAAM,aAAa,WAAW,uBAAa;;;ACpjB3C,IAAWC;CAAX,SAAWA,YAAS;AAChB,EAAAA,WAAAA,WAAA,KAAA,IAAA,CAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,UAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,gBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,OAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,iBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,KAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,aAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,aAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,OAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,IAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,IAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,IAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,cAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,sBAAA,IAAA,EAAA,IAAA;AACJ,GA5BWA,eAAAA,aAAS,CAAA,EAAA;AA+BpB,IAAW;CAAX,SAAWC,QAAK;AACZ,EAAAA,OAAAA,OAAA,MAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,eAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,kBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,sBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,kBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,qBAAA,IAAA,CAAA,IAAA;AAGA,EAAAA,OAAAA,OAAA,qBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,iBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,oBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,sBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,oBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,oBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,oBAAA,IAAA,EAAA,IAAA;AAGA,EAAAA,OAAAA,OAAA,mBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,eAAA,IAAA,EAAA,IAAA;AAGA,EAAAA,OAAAA,OAAA,yBAAA,IAAA,EAAA,IAAA;AAGA,EAAAA,OAAAA,OAAA,eAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,eAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,kBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,eAAA,IAAA,EAAA,IAAA;AAGA,EAAAA,OAAAA,OAAA,gBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,sBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,cAAA,IAAA,EAAA,IAAA;AAEA,EAAAA,OAAAA,OAAA,cAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,qBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,eAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,iBAAA,IAAA,EAAA,IAAA;AACA,EAAAA,OAAAA,OAAA,aAAA,IAAA,EAAA,IAAA;AACJ,GAzCW,UAAA,QAAK,CAAA,EAAA;AA2ChB,SAAS,aAAaC,IAAS;AAC3B,SACIA,OAAMF,WAAU,SAChBE,OAAMF,WAAU,WAChBE,OAAMF,WAAU,OAChBE,OAAMF,WAAU,YAChBE,OAAMF,WAAU;AAExB;AAEA,SAAS,kBAAkBE,IAAS;AAChC,SAAOA,OAAMF,WAAU,SAASE,OAAMF,WAAU,MAAM,aAAaE,EAAC;AACxE;AAEA,SAASC,UAASD,IAAS;AACvB,SAAOA,MAAKF,WAAU,QAAQE,MAAKF,WAAU;AACjD;AAEA,SAAS,aAAaE,IAAS;AAC3B,SACKA,MAAKF,WAAU,UAAUE,MAAKF,WAAU,UACxCE,MAAKF,WAAU,UAAUE,MAAKF,WAAU;AAEjD;AAEA,SAAS,WAAWE,IAAS;AACzB,SACKA,MAAKF,WAAU,UAAUE,MAAKF,WAAU,UACxCE,MAAKF,WAAU,UAAUE,MAAKF,WAAU;AAEjD;AAEA,IAAY;CAAZ,SAAYI,YAAS;AACjB,EAAAA,WAAAA,WAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,CAAA,IAAA;AACJ,GALY,cAAA,YAAS,CAAA,EAAA;AA+BrB,IAAM,YAAY;EACd,OAAO,IAAI,WAAW,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,EAAI,CAAC;EAC1D,UAAU,IAAI,WAAW,CAAC,IAAM,IAAM,EAAI,CAAC;EAC3C,YAAY,IAAI,WAAW,CAAC,IAAM,IAAM,EAAI,CAAC;EAC7C,WAAW,IAAI,WAAW,CAAC,IAAM,IAAM,KAAM,IAAM,KAAM,KAAM,KAAM,GAAI,CAAC;EAC1E,UAAU,IAAI,WAAW,CAAC,IAAM,IAAM,KAAM,KAAM,KAAM,KAAM,GAAI,CAAC;EACnE,UAAU,IAAI,WAAW,CAAC,IAAM,IAAM,KAAM,KAAM,KAAM,KAAM,GAAI,CAAC;;;AAGvE,IAAqB,YAArB,MAA8B;EAsB1B,YACI,EACI,UAAU,OACV,iBAAiB,KAAI,GAER,KAAc;AAAd,SAAA,MAAA;AAzBb,SAAA,QAAQ,MAAM;AAEd,SAAA,SAAS;AAET,SAAA,eAAe;AAEf,SAAA,QAAQ;AAER,SAAA,YAAY,MAAM;AAElB,SAAA,YAAY;AAEb,SAAA,UAAU;AAET,SAAA,SAAS;AA+ET,SAAA,kBAA8B;AAC9B,SAAA,gBAAgB;AA+WhB,SAAA,YAAY;AACZ,SAAA,cAAc;AAEd,SAAA,eAAe;AACf,SAAA,eAAe;AAtbnB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,aAAa,UAAU,0BAAgB;EAChD;EAEO,QAAK;AACR,SAAK,QAAQ,MAAM;AACnB,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,YAAY,MAAM;AACvB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,SAAS;EAClB;EAEO,MAAM,OAAa;AACtB,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,SAAS;AACd,SAAK,MAAK;EACd;EAEO,MAAG;AACN,QAAI,KAAK;AAAS,WAAK,OAAM;EACjC;EAEO,QAAK;AACR,SAAK,UAAU;EACnB;EAEO,SAAM;AACT,SAAK,UAAU;AACf,QAAI,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,QAAQ;AAC/C,WAAK,MAAK;;EAElB;;;;EAKO,WAAQ;AACX,WAAO,KAAK;EAChB;;;;EAKO,kBAAe;AAClB,WAAO,KAAK;EAChB;EAEQ,UAAUF,IAAS;AACvB,QACIA,OAAMF,WAAU,MACf,CAAC,KAAK,kBAAkB,KAAK,cAAcA,WAAU,EAAE,GAC1D;AACE,UAAI,KAAK,QAAQ,KAAK,cAAc;AAChC,aAAK,IAAI,OAAO,KAAK,cAAc,KAAK,KAAK;;AAEjD,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK;eAClB,KAAK,kBAAkBE,OAAMF,WAAU,KAAK;AACnD,WAAK,QAAQ,MAAM;;EAE3B;EAIQ,0BAA0BE,IAAS;AACvC,UAAM,QAAQ,KAAK,kBAAkB,KAAK,gBAAgB;AAC1D,UAAM,UAAU;;MAEV,kBAAkBA,EAAC;;;OAElBA,KAAI,QAAU,KAAK,gBAAgB,KAAK,aAAa;;AAE5D,QAAI,CAAC,SAAS;AACV,WAAK,YAAY;eACV,CAAC,OAAO;AACf,WAAK;AACL;;AAGJ,SAAK,gBAAgB;AACrB,SAAK,QAAQ,MAAM;AACnB,SAAK,eAAeA,EAAC;EACzB;;EAGQ,kBAAkBA,IAAS;AAC/B,QAAI,KAAK,kBAAkB,KAAK,gBAAgB,QAAQ;AACpD,UAAIA,OAAMF,WAAU,MAAM,aAAaE,EAAC,GAAG;AACvC,cAAM,YAAY,KAAK,QAAQ,KAAK,gBAAgB;AAEpD,YAAI,KAAK,eAAe,WAAW;AAE/B,gBAAM,cAAc,KAAK;AACzB,eAAK,QAAQ;AACb,eAAK,IAAI,OAAO,KAAK,cAAc,SAAS;AAC5C,eAAK,QAAQ;;AAGjB,aAAK,YAAY;AACjB,aAAK,eAAe,YAAY;AAChC,aAAK,sBAAsBA,EAAC;AAC5B;;AAGJ,WAAK,gBAAgB;;AAGzB,SAAKA,KAAI,QAAU,KAAK,gBAAgB,KAAK,aAAa,GAAG;AACzD,WAAK,iBAAiB;eACf,KAAK,kBAAkB,GAAG;AACjC,UAAI,KAAK,oBAAoB,UAAU,UAAU;AAE7C,YAAI,KAAK,kBAAkBA,OAAMF,WAAU,KAAK;AAC5C,eAAK,QAAQ,MAAM;;iBAEhB,KAAK,cAAcA,WAAU,EAAE,GAAG;AAEzC,aAAK,gBAAgB;;WAEtB;AAEH,WAAK,gBAAgB,OAAOE,OAAMF,WAAU,EAAE;;EAEtD;EAEQ,mBAAmBE,IAAS;AAChC,QAAIA,OAAM,UAAU,MAAM,KAAK,aAAa,GAAG;AAC3C,UAAI,EAAE,KAAK,kBAAkB,UAAU,MAAM,QAAQ;AACjD,aAAK,QAAQ,MAAM;AACnB,aAAK,kBAAkB,UAAU;AACjC,aAAK,gBAAgB;AACrB,aAAK,eAAe,KAAK,QAAQ;;WAElC;AACH,WAAK,gBAAgB;AACrB,WAAK,QAAQ,MAAM;AACnB,WAAK,mBAAmBA,EAAC;;EAEjC;;;;;;;EAQQ,cAAcA,IAAS;AAC3B,WAAO,EAAE,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,QAAQ;AACpD,UAAI,KAAK,OAAO,WAAW,KAAK,QAAQ,KAAK,MAAM,MAAMA,IAAG;AACxD,eAAO;;;AAUf,SAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,SAAS;AAEhD,WAAO;EACX;;;;;;;;;EAUQ,mBAAmBA,IAAS;AAChC,QAAIA,OAAM,KAAK,gBAAgB,KAAK,aAAa,GAAG;AAChD,UAAI,EAAE,KAAK,kBAAkB,KAAK,gBAAgB,QAAQ;AACtD,YAAI,KAAK,oBAAoB,UAAU,UAAU;AAC7C,eAAK,IAAI,QAAQ,KAAK,cAAc,KAAK,OAAO,CAAC;eAC9C;AACH,eAAK,IAAI,UAAU,KAAK,cAAc,KAAK,OAAO,CAAC;;AAGvD,aAAK,gBAAgB;AACrB,aAAK,eAAe,KAAK,QAAQ;AACjC,aAAK,QAAQ,MAAM;;eAEhB,KAAK,kBAAkB,GAAG;AAEjC,UAAI,KAAK,cAAc,KAAK,gBAAgB,CAAC,CAAC,GAAG;AAC7C,aAAK,gBAAgB;;eAElBA,OAAM,KAAK,gBAAgB,KAAK,gBAAgB,CAAC,GAAG;AAE3D,WAAK,gBAAgB;;EAE7B;;;;;;;EAQQ,eAAeA,IAAS;AAC5B,WAAO,KAAK,UAAU,CAAC,kBAAkBA,EAAC,IAAI,aAAaA,EAAC;EAChE;EAEQ,aAAa,UAAsB,QAAc;AACrD,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,QAAQ,MAAM;EACvB;EAEQ,mBAAmBA,IAAS;AAChC,QAAIA,OAAMF,WAAU,iBAAiB;AACjC,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;eAC1BE,OAAMF,WAAU,cAAc;AACrC,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;eAC1B,KAAK,eAAeE,EAAC,GAAG;AAC/B,YAAM,QAAQA,KAAI;AAClB,WAAK,eAAe,KAAK;AACzB,UAAI,CAAC,KAAK,WAAW,UAAU,UAAU,SAAS,CAAC,GAAG;AAClD,aAAK,aAAa,UAAU,UAAU,CAAC;aACpC;AACH,aAAK,QACD,CAAC,KAAK,WAAW,UAAU,UAAU,UAAU,CAAC,IAC1C,MAAM,iBACN,MAAM;;eAEbA,OAAMF,WAAU,OAAO;AAC9B,WAAK,QAAQ,MAAM;WAChB;AACH,WAAK,QAAQ,MAAM;AACnB,WAAK,UAAUE,EAAC;;EAExB;EACQ,eAAeA,IAAS;AAC5B,QAAI,kBAAkBA,EAAC,GAAG;AACtB,WAAK,IAAI,cAAc,KAAK,cAAc,KAAK,KAAK;AACpD,WAAK,eAAe;AACpB,WAAK,QAAQ,MAAM;AACnB,WAAK,yBAAyBA,EAAC;;EAEvC;EACQ,0BAA0BA,IAAS;AACvC,QAAI,aAAaA,EAAC,GAAG;eAEVA,OAAMF,WAAU,IAAI;AAC3B,WAAK,QAAQ,MAAM;WAChB;AACH,WAAK,QAAQ,KAAK,eAAeE,EAAC,IAC5B,MAAM,mBACN,MAAM;AACZ,WAAK,eAAe,KAAK;;EAEjC;EACQ,sBAAsBA,IAAS;AACnC,QAAIA,OAAMF,WAAU,MAAM,aAAaE,EAAC,GAAG;AACvC,WAAK,IAAI,WAAW,KAAK,cAAc,KAAK,KAAK;AACjD,WAAK,eAAe;AACpB,WAAK,QAAQ,MAAM;AACnB,WAAK,yBAAyBA,EAAC;;EAEvC;EACQ,yBAAyBA,IAAS;AAEtC,QAAIA,OAAMF,WAAU,MAAM,KAAK,cAAcA,WAAU,EAAE,GAAG;AACxD,WAAK,QAAQ,MAAM;AACnB,WAAK,YAAY,MAAM;AACvB,WAAK,eAAe,KAAK,QAAQ;;EAEzC;EACQ,yBAAyBE,IAAS;AACtC,QAAIA,OAAMF,WAAU,IAAI;AACpB,WAAK,IAAI,aAAa,KAAK,KAAK;AAChC,UAAI,KAAK,WAAW;AAChB,aAAK,QAAQ,MAAM;AACnB,aAAK,gBAAgB;aAClB;AACH,aAAK,QAAQ,MAAM;;AAEvB,WAAK,YAAY,KAAK;AACtB,WAAK,eAAe,KAAK,QAAQ;eAC1BE,OAAMF,WAAU,OAAO;AAC9B,WAAK,QAAQ,MAAM;eACZ,CAAC,aAAaE,EAAC,GAAG;AACzB,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK;;EAEjC;EACQ,sBAAsBA,IAAS;AACnC,QAAIA,OAAMF,WAAU,IAAI;AACpB,WAAK,IAAI,iBAAiB,KAAK,KAAK;AACpC,WAAK,QAAQ,MAAM;AACnB,WAAK,YAAY,MAAM;AACvB,WAAK,eAAe,KAAK,QAAQ;AACjC,WAAK,YAAY;eACV,CAAC,aAAaE,EAAC,GAAG;AACzB,WAAK,QAAQ,MAAM;AACnB,WAAK,yBAAyBA,EAAC;;EAEvC;EACQ,qBAAqBA,IAAS;AAClC,QAAIA,OAAMF,WAAU,MAAM,kBAAkBE,EAAC,GAAG;AAC5C,WAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,WAAK,eAAe;AACpB,WAAK,QAAQ,MAAM;AACnB,WAAK,wBAAwBA,EAAC;;EAEtC;EACQ,wBAAwBA,IAAS;AACrC,QAAIA,OAAMF,WAAU,IAAI;AACpB,WAAK,QAAQ,MAAM;eACZE,OAAMF,WAAU,SAASE,OAAMF,WAAU,IAAI;AACpD,WAAK,IAAI,YAAY,UAAU,SAAS,KAAK,KAAK;AAClD,WAAK,QAAQ,MAAM;AACnB,WAAK,yBAAyBE,EAAC;eACxB,CAAC,aAAaA,EAAC,GAAG;AACzB,WAAK,IAAI,YAAY,UAAU,SAAS,KAAK,KAAK;AAClD,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK;;EAEjC;EACQ,0BAA0BA,IAAS;AACvC,QAAIA,OAAMF,WAAU,aAAa;AAC7B,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;eAC1BE,OAAMF,WAAU,aAAa;AACpC,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;eAC1B,CAAC,aAAaE,EAAC,GAAG;AACzB,WAAK,eAAe,KAAK;AACzB,WAAK,QAAQ,MAAM;AACnB,WAAK,8BAA8BA,EAAC;;EAE5C;EACQ,uBAAuBA,IAAW,OAAa;AACnD,QACIA,OAAM,SACL,CAAC,KAAK,kBAAkB,KAAK,cAAc,KAAK,GACnD;AACE,WAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,WAAK,eAAe;AACpB,WAAK,IAAI,YACL,UAAUF,WAAU,cACd,UAAU,SACV,UAAU,QAChB,KAAK,KAAK;AAEd,WAAK,QAAQ,MAAM;eACZ,KAAK,kBAAkBE,OAAMF,WAAU,KAAK;AACnD,WAAK,YAAY,KAAK;AACtB,WAAK,QAAQ,MAAM;;EAE3B;EACQ,kCAAkCE,IAAS;AAC/C,SAAK,uBAAuBA,IAAGF,WAAU,WAAW;EACxD;EACQ,kCAAkCE,IAAS;AAC/C,SAAK,uBAAuBA,IAAGF,WAAU,WAAW;EACxD;EACQ,8BAA8BE,IAAS;AAC3C,QAAI,aAAaA,EAAC,KAAKA,OAAMF,WAAU,IAAI;AACvC,WAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,WAAK,eAAe;AACpB,WAAK,IAAI,YAAY,UAAU,UAAU,KAAK,KAAK;AACnD,WAAK,QAAQ,MAAM;AACnB,WAAK,yBAAyBE,EAAC;eACxB,KAAK,kBAAkBA,OAAMF,WAAU,KAAK;AACnD,WAAK,YAAY,KAAK;AACtB,WAAK,QAAQ,MAAM;;EAE3B;EACQ,uBAAuBE,IAAS;AACpC,QAAIA,OAAMF,WAAU,sBAAsB;AACtC,WAAK,QAAQ,MAAM;AACnB,WAAK,gBAAgB;WAClB;AACH,WAAK,QACDE,OAAMF,WAAU,OACV,MAAM,gBACN,MAAM;;EAExB;EACQ,mBAAmBE,IAAS;AAChC,QAAIA,OAAMF,WAAU,MAAM,KAAK,cAAcA,WAAU,EAAE,GAAG;AACxD,WAAK,IAAI,cAAc,KAAK,cAAc,KAAK,KAAK;AACpD,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;;EAEzC;EACQ,6BAA6BE,IAAS;AAC1C,QAAIA,OAAMF,WAAU,MAAM,KAAK,cAAcA,WAAU,EAAE,GAAG;AACxD,WAAK,IAAI,wBAAwB,KAAK,cAAc,KAAK,KAAK;AAC9D,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;;EAEzC;EACQ,mBAAmBE,IAAS;AAChC,QAAIA,OAAMF,WAAU,MAAM;AACtB,WAAK,QAAQ,MAAM;AACnB,WAAK,kBAAkB,UAAU;AAEjC,WAAK,gBAAgB;AACrB,WAAK,eAAe,KAAK,QAAQ;WAC9B;AACH,WAAK,QAAQ,MAAM;;EAE3B;EACQ,sBAAsBE,IAAS;AACnC,QAAIA,OAAMF,WAAU,MAAM,KAAK,cAAcA,WAAU,EAAE,GAAG;AACxD,WAAK,IAAI,UAAU,KAAK,cAAc,KAAK,OAAO,CAAC;AACnD,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAe,KAAK,QAAQ;;EAEzC;EACQ,oBAAoBE,IAAS;AACjC,UAAM,QAAQA,KAAI;AAClB,QAAI,UAAU,UAAU,UAAU,CAAC,GAAG;AAClC,WAAK,aAAa,UAAU,WAAW,CAAC;eACjC,UAAU,UAAU,SAAS,CAAC,GAAG;AACxC,WAAK,aAAa,UAAU,UAAU,CAAC;WACpC;AACH,WAAK,QAAQ,MAAM;AACnB,WAAK,eAAeA,EAAC;;EAE7B;EAQQ,kBAAkBA,IAAS;AAE/B,SAAK,eAAe;AACpB,SAAK,eAAe;AAEpB,QAAIA,OAAMF,WAAU,QAAQ;AACxB,WAAK,QAAQ,MAAM;eACZE,OAAMF,WAAU,KAAK;WAEzB;AACH,WAAK,YAAY;AACjB,WAAK,cAAc,KAAK,WAAW,CAAC;AACpC,WAAK,QAAQ,MAAM;AACnB,WAAK,mBAAmBE,EAAC;;EAEjC;EAEQ,mBAAmBA,IAAS;AAChC,SAAK,gBAAgB;AAErB,SAAK,YAAY,gBACb,KAAK,YACL,KAAK,aACL,KAAK,YAAY,GACjBA,EAAC;AAGL,QAAI,KAAK,YAAY,GAAG;AACpB,WAAK,gBAAe;AACpB,WAAK;AACL;;AAGJ,SAAK,cAAc,KAAK,WAAW,KAAK,SAAS;AAEjD,UAAM,SAAS,KAAK,cAAc,aAAa;AAG/C,QAAI,QAAQ;AAER,YAAM,eAAe,UAAU,MAAM;AAGrC,UAAI,CAAC,KAAK,kBAAiB,KAAMA,OAAMF,WAAU,MAAM;AACnD,aAAK,aAAa;aACf;AAEH,cAAM,cAAc,KAAK,QAAQ,KAAK,eAAe;AAErD,YAAI,cAAc,KAAK,cAAc;AACjC,eAAK,YAAY,KAAK,cAAc,WAAW;;AAInD,aAAK,eAAe,KAAK;AACzB,aAAK,aAAa;AAClB,aAAK,eAAe;AACpB,aAAK,eAAe,KAAK,QAAQ;AAEjC,YAAI,gBAAgB,GAAG;AACnB,eAAK,gBAAe;;;;EAIpC;EAEQ,kBAAe;AACnB,SAAK,QAAQ,KAAK;AAElB,QAAI,KAAK,iBAAiB,GAAG;AACzB;;AAGJ,UAAM,eACD,KAAK,WAAW,KAAK,YAAY,IAAI,aAAa,iBACnD;AAEJ,YAAQ,aAAa;MACjB,KAAK,GAAG;AACJ,aAAK,cACD,KAAK,WAAW,KAAK,YAAY,IAC7B,CAAC,aAAa,YAAY;AAElC;;MAEJ,KAAK,GAAG;AACJ,aAAK,cAAc,KAAK,WAAW,KAAK,eAAe,CAAC,CAAC;AACzD;;MAEJ,KAAK,GAAG;AACJ,aAAK,cAAc,KAAK,WAAW,KAAK,eAAe,CAAC,CAAC;AACzD,aAAK,cAAc,KAAK,WAAW,KAAK,eAAe,CAAC,CAAC;;;EAGrE;EAEQ,yBAAyBE,IAAS;AACtC,SAAKA,KAAI,QAAUF,WAAU,QAAQ;AACjC,WAAK;AACL,WAAK,QAAQ,MAAM;WAChB;AACH,WAAK,QAAQ,MAAM;AACnB,WAAK,qBAAqBE,EAAC;;EAEnC;EAEQ,kBAAkB,QAAe;AACrC,UAAM,cAAc,KAAK,QAAQ,KAAK,eAAe;AACrD,UAAM,cACF,cAAc,IAAI,OAAO,KAAK,UAAU,MAAM,WAAW;AAE7D,QAAI,gBAAgB,KAAK,OAAO;AAE5B,UAAI,cAAc,KAAK,cAAc;AACjC,aAAK,YAAY,KAAK,cAAc,WAAW;;AAGnD,WAAK,eAAe,KAAK,QAAQ,OAAO,MAAM;AAC9C,WAAK,cAAc,iBAAiB,KAAK,YAAY,CAAC;;AAE1D,SAAK,QAAQ,KAAK;EACtB;EACQ,qBAAqBA,IAAS;AAClC,QAAIA,OAAMF,WAAU,MAAM;AACtB,WAAK,kBAAkB,IAAI;eACpBG,UAASD,EAAC,GAAG;AACpB,WAAK,eAAe,KAAK,eAAe,MAAMA,KAAIF,WAAU;AAC5D,WAAK;WACF;AACH,UAAI,KAAK,kBAAiB,GAAI;AAC1B,aAAK,kBAAkB,KAAK;aACzB;AACH,aAAK,QAAQ,KAAK;;AAEtB,WAAK;;EAEb;EACQ,iBAAiBE,IAAS;AAC9B,QAAIA,OAAMF,WAAU,MAAM;AACtB,WAAK,kBAAkB,IAAI;eACpBG,UAASD,EAAC,GAAG;AACpB,WAAK,eAAe,KAAK,eAAe,MAAMA,KAAIF,WAAU;AAC5D,WAAK;eACE,WAAWE,EAAC,GAAG;AACtB,WAAK,eACD,KAAK,eAAe,OAAOA,KAAI,MAAQF,WAAU,SAAS;AAC9D,WAAK;WACF;AACH,UAAI,KAAK,kBAAiB,GAAI;AAC1B,aAAK,kBAAkB,KAAK;aACzB;AACH,aAAK,QAAQ,KAAK;;AAEtB,WAAK;;EAEb;EAEQ,oBAAiB;AACrB,WACI,CAAC,KAAK,YACL,KAAK,cAAc,MAAM,QACtB,KAAK,cAAc,MAAM;EAErC;;;;EAKQ,UAAO;AAEX,QAAI,KAAK,WAAW,KAAK,iBAAiB,KAAK,OAAO;AAClD,UACI,KAAK,UAAU,MAAM,QACpB,KAAK,UAAU,MAAM,gBAAgB,KAAK,kBAAkB,GAC/D;AACE,aAAK,IAAI,OAAO,KAAK,cAAc,KAAK,KAAK;AAC7C,aAAK,eAAe,KAAK;iBAEzB,KAAK,UAAU,MAAM,sBACrB,KAAK,UAAU,MAAM,sBACrB,KAAK,UAAU,MAAM,oBACvB;AACE,aAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,aAAK,eAAe,KAAK;;;EAGrC;EAEQ,iBAAc;AAClB,WAAO,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,UAAU,KAAK;EACjE;;;;;;EAOQ,QAAK;AACT,WAAO,KAAK,eAAc,GAAI;AAC1B,YAAME,KAAI,KAAK,OAAO,WAAW,KAAK,QAAQ,KAAK,MAAM;AACzD,cAAQ,KAAK,OAAO;QAChB,KAAK,MAAM,MAAM;AACb,eAAK,UAAUA,EAAC;AAChB;;QAEJ,KAAK,MAAM,sBAAsB;AAC7B,eAAK,0BAA0BA,EAAC;AAChC;;QAEJ,KAAK,MAAM,cAAc;AACrB,eAAK,kBAAkBA,EAAC;AACxB;;QAEJ,KAAK,MAAM,eAAe;AACtB,eAAK,mBAAmBA,EAAC;AACzB;;QAEJ,KAAK,MAAM,oBAAoB;AAC3B,eAAK,kCAAkCA,EAAC;AACxC;;QAEJ,KAAK,MAAM,iBAAiB;AACxB,eAAK,qBAAqBA,EAAC;AAC3B;;QAEJ,KAAK,MAAM,eAAe;AACtB,eAAK,mBAAmBA,EAAC;AACzB;;QAEJ,KAAK,MAAM,kBAAkB;AACzB,eAAK,sBAAsBA,EAAC;AAC5B;;QAEJ,KAAK,MAAM,qBAAqB;AAC5B,eAAK,yBAAyBA,EAAC;AAC/B;;QAEJ,KAAK,MAAM,WAAW;AAClB,eAAK,eAAeA,EAAC;AACrB;;QAEJ,KAAK,MAAM,kBAAkB;AACzB,eAAK,sBAAsBA,EAAC;AAC5B;;QAEJ,KAAK,MAAM,eAAe;AACtB,eAAK,mBAAmBA,EAAC;AACzB;;QAEJ,KAAK,MAAM,oBAAoB;AAC3B,eAAK,wBAAwBA,EAAC;AAC9B;;QAEJ,KAAK,MAAM,oBAAoB;AAC3B,eAAK,kCAAkCA,EAAC;AACxC;;QAEJ,KAAK,MAAM,sBAAsB;AAC7B,eAAK,0BAA0BA,EAAC;AAChC;;QAEJ,KAAK,MAAM,sBAAsB;AAC7B,eAAK,0BAA0BA,EAAC;AAChC;;QAEJ,KAAK,MAAM,qBAAqB;AAC5B,eAAK,yBAAyBA,EAAC;AAC/B;;QAEJ,KAAK,MAAM,gBAAgB;AACvB,eAAK,oBAAoBA,EAAC;AAC1B;;QAEJ,KAAK,MAAM,oBAAoB;AAC3B,eAAK,8BAA8BA,EAAC;AACpC;;QAEJ,KAAK,MAAM,kBAAkB;AACzB,eAAK,sBAAsBA,EAAC;AAC5B;;QAEJ,KAAK,MAAM,eAAe;AACtB,eAAK,mBAAmBA,EAAC;AACzB;;QAEJ,KAAK,MAAM,mBAAmB;AAC1B,eAAK,uBAAuBA,EAAC;AAC7B;;QAEJ,KAAK,MAAM,eAAe;AACtB,eAAK,mBAAmBA,EAAC;AACzB;;QAEJ,KAAK,MAAM,yBAAyB;AAChC,eAAK,6BAA6BA,EAAC;AACnC;;QAEJ,KAAK,MAAM,eAAe;AACtB,eAAK,mBAAmBA,EAAC;AACzB;;QAEJ,KAAK,MAAM,cAAc;AACrB,eAAK,kBAAkBA,EAAC;AACxB;;QAEJ,KAAK,MAAM,aAAa;AACpB,eAAK,iBAAiBA,EAAC;AACvB;;QAEJ,KAAK,MAAM,iBAAiB;AACxB,eAAK,qBAAqBA,EAAC;AAC3B;;QAEJ,SAAS;AAEL,eAAK,yBAAyBA,EAAC;;;AAGvC,WAAK;;AAET,SAAK,QAAO;EAChB;EAEQ,SAAM;AACV,QAAI,KAAK,UAAU,MAAM,eAAe;AACpC,WAAK,gBAAe;;AAIxB,QAAI,KAAK,eAAe,KAAK,OAAO;AAChC,WAAK,mBAAkB;;AAE3B,SAAK,IAAI,MAAK;EAClB;;EAGQ,qBAAkB;AACtB,UAAM,WAAW,KAAK,OAAO,SAAS,KAAK;AAC3C,QAAI,KAAK,UAAU,MAAM,eAAe;AACpC,UAAI,KAAK,oBAAoB,UAAU,UAAU;AAC7C,aAAK,IAAI,QAAQ,KAAK,cAAc,UAAU,CAAC;aAC5C;AACH,aAAK,IAAI,UAAU,KAAK,cAAc,UAAU,CAAC;;eAGrD,KAAK,UAAU,MAAM,mBACrB,KAAK,kBAAiB,GACxB;AACE,WAAK,kBAAkB,KAAK;eAG5B,KAAK,UAAU,MAAM,eACrB,KAAK,kBAAiB,GACxB;AACE,WAAK,kBAAkB,KAAK;eAG5B,KAAK,UAAU,MAAM,aACrB,KAAK,UAAU,MAAM,uBACrB,KAAK,UAAU,MAAM,wBACrB,KAAK,UAAU,MAAM,sBACrB,KAAK,UAAU,MAAM,mBACrB,KAAK,UAAU,MAAM,sBACrB,KAAK,UAAU,MAAM,sBACrB,KAAK,UAAU,MAAM,sBACrB,KAAK,UAAU,MAAM,kBACvB;WAKK;AACH,WAAK,IAAI,OAAO,KAAK,cAAc,QAAQ;;EAEnD;EAEQ,YAAY,OAAe,UAAgB;AAC/C,QACI,KAAK,cAAc,MAAM,QACzB,KAAK,cAAc,MAAM,cAC3B;AACE,WAAK,IAAI,aAAa,OAAO,QAAQ;WAClC;AACH,WAAK,IAAI,OAAO,OAAO,QAAQ;;EAEvC;EACQ,cAAc,IAAU;AAC5B,QACI,KAAK,cAAc,MAAM,QACzB,KAAK,cAAc,MAAM,cAC3B;AACE,WAAK,IAAI,eAAe,EAAE;WACvB;AACH,WAAK,IAAI,aAAa,EAAE;;EAEhC;;;;ACv/BJ,IAAM,WAAW,oBAAI,IAAI;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;CACH;AACD,IAAM,OAAO,oBAAI,IAAI,CAAC,GAAG,CAAC;AAC1B,IAAM,mBAAmB,oBAAI,IAAI,CAAC,SAAS,OAAO,CAAC;AACnD,IAAM,UAAU,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AACpC,IAAM,UAAU,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAEpC,IAAM,mBAAmB,oBAAI,IAAyB;EAClD,CAAC,MAAM,oBAAI,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC;EAClC,CAAC,MAAM,oBAAI,IAAI,CAAC,IAAI,CAAC,CAAC;EACtB,CAAC,MAAM,oBAAI,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,CAAC;EACrC,CAAC,QAAQ,oBAAI,IAAI,CAAC,QAAQ,QAAQ,QAAQ,CAAC,CAAC;EAC5C,CAAC,MAAM,oBAAI,IAAI,CAAC,IAAI,CAAC,CAAC;EACtB,CAAC,KAAK,IAAI;EACV,CAAC,MAAM,IAAI;EACX,CAAC,MAAM,IAAI;EACX,CAAC,MAAM,IAAI;EACX,CAAC,MAAM,IAAI;EACX,CAAC,MAAM,IAAI;EACX,CAAC,MAAM,IAAI;EACX,CAAC,UAAU,QAAQ;EACnB,CAAC,SAAS,QAAQ;EAClB,CAAC,UAAU,QAAQ;EACnB,CAAC,UAAU,QAAQ;EACnB,CAAC,YAAY,QAAQ;EACrB,CAAC,YAAY,QAAQ;EACrB,CAAC,UAAU,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC9B,CAAC,YAAY,oBAAI,IAAI,CAAC,YAAY,QAAQ,CAAC,CAAC;EAC5C,CAAC,MAAM,OAAO;EACd,CAAC,MAAM,OAAO;EACd,CAAC,WAAW,IAAI;EAChB,CAAC,WAAW,IAAI;EAChB,CAAC,SAAS,IAAI;EACd,CAAC,cAAc,IAAI;EACnB,CAAC,WAAW,IAAI;EAChB,CAAC,OAAO,IAAI;EACZ,CAAC,MAAM,IAAI;EACX,CAAC,YAAY,IAAI;EACjB,CAAC,cAAc,IAAI;EACnB,CAAC,UAAU,IAAI;EACf,CAAC,UAAU,IAAI;EACf,CAAC,QAAQ,IAAI;EACb,CAAC,UAAU,IAAI;EACf,CAAC,MAAM,IAAI;EACX,CAAC,QAAQ,IAAI;EACb,CAAC,OAAO,IAAI;EACZ,CAAC,MAAM,IAAI;EACX,CAAC,OAAO,IAAI;EACZ,CAAC,WAAW,IAAI;EAChB,CAAC,SAAS,IAAI;EACd,CAAC,MAAM,IAAI;EACX,CAAC,MAAM,OAAO;EACd,CAAC,MAAM,OAAO;EACd,CAAC,SAAS,gBAAgB;EAC1B,CAAC,SAAS,gBAAgB;CAC7B;AAED,IAAM,eAAe,oBAAI,IAAI;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACH;AAED,IAAM,yBAAyB,oBAAI,IAAI,CAAC,QAAQ,KAAK,CAAC;AAEtD,IAAM,0BAA0B,oBAAI,IAAI;EACpC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACH;AA+FD,IAAM,YAAY;AAEZ,IAAO,SAAP,MAAa;EA6Bf,YACI,KACiB,UAAyB,CAAA,GAAE;;AAA3B,SAAA,UAAA;AA7Bd,SAAA,aAAa;AAEb,SAAA,WAAW;AAKV,SAAA,eAAe;AAEf,SAAA,UAAU;AACV,SAAA,aAAa;AACb,SAAA,cAAc;AACd,SAAA,UAA4C;AACnC,SAAA,QAAkB,CAAA;AAClB,SAAA,iBAA4B,CAAA;AAM5B,SAAA,UAAoB,CAAA;AAC7B,SAAA,eAAe;AAEf,SAAA,aAAa;AAEb,SAAA,QAAQ;AAMZ,SAAK,MAAM,QAAG,QAAH,QAAG,SAAH,MAAO,CAAA;AAClB,SAAK,qBAAoBG,MAAA,QAAQ,mBAAa,QAAAA,QAAA,SAAAA,MAAI,CAAC,QAAQ;AAC3D,SAAK,2BACD,KAAA,QAAQ,6BAAuB,QAAA,OAAA,SAAA,KAAI,CAAC,QAAQ;AAChD,SAAK,YAAY,MAAK,KAAA,QAAQ,eAAS,QAAA,OAAA,SAAA,KAAI,WACvC,KAAK,SACL,IAAI;AAER,KAAA,MAAA,KAAA,KAAK,KAAI,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,IAAI;EAChC;;;EAKA,OAAO,OAAe,UAAgB;;AAClC,UAAM,OAAO,KAAK,SAAS,OAAO,QAAQ;AAC1C,SAAK,WAAW,WAAW;AAC3B,KAAA,MAAAA,MAAA,KAAK,KAAI,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;AACtB,SAAK,aAAa;EACtB;;EAGA,aAAa,IAAU;;AAKnB,UAAM,QAAQ,KAAK,UAAU,gBAAe;AAC5C,SAAK,WAAW,QAAQ;AACxB,KAAA,MAAAA,MAAA,KAAK,KAAI,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,cAAc,EAAE,CAAC;AACnC,SAAK,aAAa;EACtB;EAEU,cAAcC,OAAY;AAChC,WAAO,CAAC,KAAK,QAAQ,WAAW,aAAa,IAAIA,KAAI;EACzD;;EAGA,cAAc,OAAe,UAAgB;AACzC,SAAK,WAAW;AAEhB,QAAIA,QAAO,KAAK,SAAS,OAAO,QAAQ;AAExC,QAAI,KAAK,mBAAmB;AACxB,MAAAA,QAAOA,MAAK,YAAW;;AAG3B,SAAK,YAAYA,KAAI;EACzB;EAEQ,YAAYA,OAAY;;AAC5B,SAAK,eAAe,KAAK;AACzB,SAAK,UAAUA;AAEf,UAAM,eACF,CAAC,KAAK,QAAQ,WAAW,iBAAiB,IAAIA,KAAI;AAEtD,QAAI,cAAc;AACd,aACI,KAAK,MAAM,SAAS,KACpB,aAAa,IAAI,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,CAAC,GACpD;AACE,cAAM,UAAU,KAAK,MAAM,IAAG;AAC9B,SAAA,MAAAD,MAAA,KAAK,KAAI,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,SAAS,IAAI;;;AAG3C,QAAI,CAAC,KAAK,cAAcC,KAAI,GAAG;AAC3B,WAAK,MAAM,KAAKA,KAAI;AACpB,UAAI,uBAAuB,IAAIA,KAAI,GAAG;AAClC,aAAK,eAAe,KAAK,IAAI;iBACtB,wBAAwB,IAAIA,KAAI,GAAG;AAC1C,aAAK,eAAe,KAAK,KAAK;;;AAGtC,KAAA,MAAA,KAAA,KAAK,KAAI,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAGA,KAAI;AAC7B,QAAI,KAAK,IAAI;AAAW,WAAK,UAAU,CAAA;EAC3C;EAEQ,WAAW,WAAkB;;AACjC,SAAK,aAAa,KAAK;AAEvB,QAAI,KAAK,SAAS;AACd,OAAA,MAAAD,MAAA,KAAK,KAAI,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK,SAAS,KAAK,SAAS,SAAS;AAC1D,WAAK,UAAU;;AAEnB,QAAI,KAAK,IAAI,cAAc,KAAK,cAAc,KAAK,OAAO,GAAG;AACzD,WAAK,IAAI,WAAW,KAAK,SAAS,IAAI;;AAG1C,SAAK,UAAU;EACnB;;EAGA,aAAa,UAAgB;AACzB,SAAK,WAAW;AAChB,SAAK,WAAW,KAAK;AAGrB,SAAK,aAAa,WAAW;EACjC;;EAGA,WAAW,OAAe,UAAgB;;AACtC,SAAK,WAAW;AAEhB,QAAIC,QAAO,KAAK,SAAS,OAAO,QAAQ;AAExC,QAAI,KAAK,mBAAmB;AACxB,MAAAA,QAAOA,MAAK,YAAW;;AAG3B,QACI,uBAAuB,IAAIA,KAAI,KAC/B,wBAAwB,IAAIA,KAAI,GAClC;AACE,WAAK,eAAe,IAAG;;AAG3B,QAAI,CAAC,KAAK,cAAcA,KAAI,GAAG;AAC3B,YAAM,MAAM,KAAK,MAAM,YAAYA,KAAI;AACvC,UAAI,QAAQ,IAAI;AACZ,YAAI,KAAK,IAAI,YAAY;AACrB,cAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,iBAAO,SAAS;AAEZ,iBAAK,IAAI,WAAW,KAAK,MAAM,IAAG,GAAK,UAAU,CAAC;;;AAEnD,eAAK,MAAM,SAAS;iBACpB,CAAC,KAAK,QAAQ,WAAWA,UAAS,KAAK;AAE9C,aAAK,YAAY,GAAG;AACpB,aAAK,gBAAgB,IAAI;;eAEtB,CAAC,KAAK,QAAQ,WAAWA,UAAS,MAAM;AAE/C,OAAA,MAAAD,MAAA,KAAK,KAAI,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;AAC7B,OAAA,MAAA,KAAA,KAAK,KAAI,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,MAAM,CAAA,GAAI,IAAI;AACnC,OAAA,MAAA,KAAA,KAAK,KAAI,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,MAAM,KAAK;;AAIrC,SAAK,aAAa,WAAW;EACjC;;EAGA,iBAAiB,UAAgB;AAC7B,SAAK,WAAW;AAChB,QACI,KAAK,QAAQ,WACb,KAAK,QAAQ,wBACb,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GACpD;AACE,WAAK,gBAAgB,KAAK;AAG1B,WAAK,aAAa,WAAW;WAC1B;AAEH,WAAK,aAAa,QAAQ;;EAElC;EAEQ,gBAAgB,eAAsB;;AAC1C,UAAMC,QAAO,KAAK;AAClB,SAAK,WAAW,aAAa;AAG7B,QAAI,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,MAAMA,OAAM;AAE5C,OAAA,MAAAD,MAAA,KAAK,KAAI,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAGC,OAAM,CAAC,aAAa;AAC1C,WAAK,MAAM,IAAG;;EAEtB;;EAGA,aAAa,OAAe,UAAgB;AACxC,SAAK,aAAa;AAClB,UAAMA,QAAO,KAAK,SAAS,OAAO,QAAQ;AAE1C,SAAK,aAAa,KAAK,0BACjBA,MAAK,YAAW,IAChBA;EACV;;EAGA,aAAa,OAAe,UAAgB;AACxC,SAAK,eAAe,KAAK,SAAS,OAAO,QAAQ;EACrD;;EAGA,eAAe,IAAU;AACrB,SAAK,eAAe,cAAc,EAAE;EACxC;;EAGA,YAAY,OAAkB,UAAgB;;AAC1C,SAAK,WAAW;AAEhB,KAAA,MAAAD,MAAA,KAAK,KAAI,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAChB,KAAK,YACL,KAAK,aACL,UAAU,UAAU,SACd,MACA,UAAU,UAAU,SACpB,MACA,UAAU,UAAU,UACpB,SACA,IAAI;AAGd,QACI,KAAK,WACL,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,SAAS,KAAK,UAAU,GACrE;AACE,WAAK,QAAQ,KAAK,UAAU,IAAI,KAAK;;AAEzC,SAAK,cAAc;EACvB;EAEQ,mBAAmB,OAAa;AACpC,UAAM,QAAQ,MAAM,OAAO,SAAS;AACpC,QAAIC,QAAO,QAAQ,IAAI,QAAQ,MAAM,OAAO,GAAG,KAAK;AAEpD,QAAI,KAAK,mBAAmB;AACxB,MAAAA,QAAOA,MAAK,YAAW;;AAG3B,WAAOA;EACX;;EAGA,cAAc,OAAe,UAAgB;AACzC,SAAK,WAAW;AAChB,UAAM,QAAQ,KAAK,SAAS,OAAO,QAAQ;AAE3C,QAAI,KAAK,IAAI,yBAAyB;AAClC,YAAMA,QAAO,KAAK,mBAAmB,KAAK;AAC1C,WAAK,IAAI,wBAAwB,IAAIA,KAAI,IAAI,IAAI,KAAK,EAAE;;AAI5D,SAAK,aAAa,WAAW;EACjC;;EAGA,wBAAwB,OAAe,UAAgB;AACnD,SAAK,WAAW;AAChB,UAAM,QAAQ,KAAK,SAAS,OAAO,QAAQ;AAE3C,QAAI,KAAK,IAAI,yBAAyB;AAClC,YAAMA,QAAO,KAAK,mBAAmB,KAAK;AAC1C,WAAK,IAAI,wBAAwB,IAAIA,KAAI,IAAI,IAAI,KAAK,EAAE;;AAI5D,SAAK,aAAa,WAAW;EACjC;;EAGA,UAAU,OAAe,UAAkB,QAAc;;AACrD,SAAK,WAAW;AAEhB,KAAA,MAAAD,MAAA,KAAK,KAAI,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK,SAAS,OAAO,WAAW,MAAM,CAAC;AAC5D,KAAA,MAAA,KAAA,KAAK,KAAI,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;AAGrB,SAAK,aAAa,WAAW;EACjC;;EAGA,QAAQ,OAAe,UAAkB,QAAc;;AACnD,SAAK,WAAW;AAChB,UAAM,QAAQ,KAAK,SAAS,OAAO,WAAW,MAAM;AAEpD,QAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,gBAAgB;AACrD,OAAA,MAAAA,MAAA,KAAK,KAAI,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;AACrB,OAAA,MAAA,KAAA,KAAK,KAAI,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,KAAK;AACvB,OAAA,MAAA,KAAA,KAAK,KAAI,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;WAChB;AACH,OAAA,MAAA,KAAA,KAAK,KAAI,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,UAAU,KAAK,IAAI;AACxC,OAAA,MAAA,KAAA,KAAK,KAAI,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;AAIzB,SAAK,aAAa,WAAW;EACjC;;EAGA,QAAK;;AACD,QAAI,KAAK,IAAI,YAAY;AAErB,WAAK,WAAW,KAAK;AACrB,eACQ,QAAQ,KAAK,MAAM,QACvB,QAAQ,GACR,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,GAAG,IAAI;AAChD;;AAEL,KAAA,MAAAA,MAAA,KAAK,KAAI,WAAK,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;EAClB;;;;EAKO,QAAK;;AACR,KAAA,MAAAA,MAAA,KAAK,KAAI,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;AAChB,SAAK,UAAU,MAAK;AACpB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,MAAM,SAAS;AACpB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,KAAA,MAAA,KAAA,KAAK,KAAI,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,IAAI;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,QAAQ;EACjB;;;;;;;EAQO,cAAc,MAAY;AAC7B,SAAK,MAAK;AACV,SAAK,IAAI,IAAI;EACjB;EAEQ,SAAS,OAAe,KAAW;AACvC,WAAO,QAAQ,KAAK,gBAAgB,KAAK,QAAQ,CAAC,EAAE,QAAQ;AACxD,WAAK,YAAW;;AAGpB,QAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE,MACxB,QAAQ,KAAK,cACb,MAAM,KAAK,YAAY;AAG3B,WAAO,MAAM,KAAK,eAAe,KAAK,QAAQ,CAAC,EAAE,QAAQ;AACrD,WAAK,YAAW;AAChB,eAAS,KAAK,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,KAAK,YAAY;;AAG7D,WAAO;EACX;EAEQ,cAAW;AACf,SAAK,gBAAgB,KAAK,QAAQ,CAAC,EAAE;AACrC,SAAK;AACL,SAAK,QAAQ,MAAK;EACtB;;;;;;EAOO,MAAM,OAAa;;AACtB,QAAI,KAAK,OAAO;AACZ,OAAA,MAAAA,MAAA,KAAK,KAAI,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI,MAAM,sBAAsB,CAAC;AACpD;;AAGJ,SAAK,QAAQ,KAAK,KAAK;AACvB,QAAI,KAAK,UAAU,SAAS;AACxB,WAAK,UAAU,MAAM,KAAK;AAC1B,WAAK;;EAEb;;;;;;EAOO,IAAI,OAAc;;AACrB,QAAI,KAAK,OAAO;AACZ,OAAA,MAAAA,MAAA,KAAK,KAAI,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI,MAAM,oBAAoB,CAAC;AAClD;;AAGJ,QAAI;AAAO,WAAK,MAAM,KAAK;AAC3B,SAAK,QAAQ;AACb,SAAK,UAAU,IAAG;EACtB;;;;EAKO,QAAK;AACR,SAAK,UAAU,MAAK;EACxB;;;;EAKO,SAAM;AACT,SAAK,UAAU,OAAM;AAErB,WACI,KAAK,UAAU,WACf,KAAK,aAAa,KAAK,QAAQ,QACjC;AACE,WAAK,UAAU,MAAM,KAAK,QAAQ,KAAK,YAAY,CAAC;;AAGxD,QAAI,KAAK;AAAO,WAAK,UAAU,IAAG;EACtC;;;;;;;EAQO,WAAW,OAAa;AAC3B,SAAK,MAAM,KAAK;EACpB;;;;;;;EAOO,KAAK,OAAc;AACtB,SAAK,IAAI,KAAK;EAClB;;;;AC9oBJ,SAAS,YACL,KAAM;AAEN,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI;;AAEjC,SAAO;AACX;AAGA,IAAA,sBAAe,IAAI,IAA0C,YAAY,CAAC,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,YAAW,GAAE,MAAK,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,EAAC,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,KAAI,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,aAAa,GAAE,CAAC,KAAI,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,MAAK,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,iBAAgB,GAAE,MAAK,GAAE,eAAc,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,wBAAwB,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,oBAAoB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,4BAA4B,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,KAAI,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,EAAC,GAAE,eAAc,GAAE,MAAK,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,IAAI,IAAkC,YAAY,CAAC,CAAC,KAAI,QAAQ,GAAE,CAAC,MAAK,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,IAAI,IAAkC,YAAY,CAAC,CAAC,KAAI,QAAQ,GAAE,CAAC,MAAK,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,qBAAoB,CAAC,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,gBAAe,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,iBAAgB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,iBAAgB,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,oBAAmB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,sBAAqB,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,EAAC,GAAE,uBAAsB,GAAE,MAAK,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,wBAAuB,GAAE,MAAK,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,0BAA0B,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,KAAI,GAAE,aAAY,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,WAAW,GAAE,CAAC,IAAG,cAAc,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,IAAG,mBAAmB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,KAAI,YAAY,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,KAAI,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,IAAG,qBAAqB,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,IAAG,qBAAqB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,4BAA4B,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,KAAI,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,EAAC,GAAE,qBAAoB,GAAE,KAAI,GAAE,uBAAsB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,sBAAqB,GAAE,KAAI,GAAE,wBAAuB,CAAC,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,IAAG,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,KAAI,GAAE,aAAY,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,KAAI,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,EAAC,GAAE,cAAa,GAAE,KAAI,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,cAAa,GAAE,KAAI,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,cAAa,GAAE,KAAI,GAAE,sBAAqB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,oBAAmB,GAAE,KAAI,GAAE,4BAA2B,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,OAAM,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,OAAM,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,mBAAkB,GAAE,KAAI,GAAE,qBAAoB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,KAAI,GAAE,qBAAoB,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,kBAAiB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,kBAAiB,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,MAAK,GAAE,WAAU,CAAC,GAAE,CAAC,OAAM,EAAC,GAAE,IAAI,IAAkC,YAAY,CAAC,CAAC,OAAM,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,IAAG,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,MAAK,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,CAAC,CAAC,CAAC;;;AChB39tB,IAAM,cAAc;AAE3B,IAAM,aAAa,oBAAI,IAAI;EACvB,CAAC,IAAI,QAAQ;EACb,CAAC,IAAI,OAAO;EACZ,CAAC,IAAI,QAAQ;EACb,CAAC,IAAI,MAAM;EACX,CAAC,IAAI,MAAM;CACd;AAGM,IAAM;;EAET,OAAO,UAAU,eAAe,OAC1B,CAAC,KAAa,UAA0B,IAAI,YAAY,KAAK;;IAE7D,CAACE,IAAW,WACPA,GAAE,WAAW,KAAK,IAAI,WAAY,SAC5BA,GAAE,WAAW,KAAK,IAAI,SAAU,OACjCA,GAAE,WAAW,QAAQ,CAAC,IACtB,QACA,QACAA,GAAE,WAAW,KAAK;;;AAShC,SAAU,UAAU,KAAW;AACjC,MAAI,MAAM;AACV,MAAI,UAAU;AACd,MAAI;AAEJ,UAAQ,QAAQ,YAAY,KAAK,GAAG,OAAO,MAAM;AAC7C,UAAM,IAAI,MAAM;AAChB,UAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,UAAM,OAAO,WAAW,IAAI,IAAI;AAEhC,QAAI,SAAS,QAAW;AACpB,aAAO,IAAI,UAAU,SAAS,CAAC,IAAI;AACnC,gBAAU,IAAI;WACX;AACH,aAAO,GAAG,IAAI,UAAU,SAAS,CAAC,CAAC,MAAM,aACrC,KACA,CAAC,EACH,SAAS,EAAE,CAAC;AAEd,gBAAU,YAAY,aAAa,QAC9B,OAAO,WAAY,KAAM;;;AAKtC,SAAO,MAAM,IAAI,OAAO,OAAO;AACnC;AAuBA,SAAS,WACL,OACAC,MAAwB;AAExB,SAAO,SAASC,QAAO,MAAY;AAC/B,QAAI;AACJ,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,WAAQ,QAAQ,MAAM,KAAK,IAAI,GAAI;AAC/B,UAAI,YAAY,MAAM,OAAO;AACzB,kBAAU,KAAK,UAAU,SAAS,MAAM,KAAK;;AAIjD,gBAAUD,KAAI,IAAI,MAAM,CAAC,EAAE,WAAW,CAAC,CAAC;AAGxC,gBAAU,MAAM,QAAQ;;AAG5B,WAAO,SAAS,KAAK,UAAU,OAAO;EAC1C;AACJ;AASO,IAAM,aAAa,WAAW,YAAY,UAAU;AAQpD,IAAM,kBAAkB,WAC3B,eACA,oBAAI,IAAI;EACJ,CAAC,IAAI,QAAQ;EACb,CAAC,IAAI,OAAO;EACZ,CAAC,KAAK,QAAQ;CACjB,CAAC;AASC,IAAM,aAAa,WACtB,gBACA,oBAAI,IAAI;EACJ,CAAC,IAAI,OAAO;EACZ,CAAC,IAAI,MAAM;EACX,CAAC,IAAI,MAAM;EACX,CAAC,KAAK,QAAQ;CACjB,CAAC;;;ACpIN,IAAY;CAAZ,SAAYE,cAAW;AAEnB,EAAAA,aAAAA,aAAA,KAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,MAAA,IAAA,CAAA,IAAA;AACJ,GALY,gBAAA,cAAW,CAAA,EAAA;AAOvB,IAAY;CAAZ,SAAYC,eAAY;AAKpB,EAAAA,cAAAA,cAAA,MAAA,IAAA,CAAA,IAAA;AAMA,EAAAA,cAAAA,cAAA,OAAA,IAAA,CAAA,IAAA;AAKA,EAAAA,cAAAA,cAAA,WAAA,IAAA,CAAA,IAAA;AAKA,EAAAA,cAAAA,cAAA,WAAA,IAAA,CAAA,IAAA;AAKA,EAAAA,cAAAA,cAAA,MAAA,IAAA,CAAA,IAAA;AACJ,GA3BY,iBAAA,eAAY,CAAA,EAAA;;;ACjBjB,IAAM,eAAe,IAAI,IAAI;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,YAAY,GAAG,GAAG,CAAC,CAAC;AACjC,IAAM,iBAAiB,IAAI,IAAI;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,YAAY,GAAG,GAAG,CAAC,CAAC;;;ACvFxC,IAAM,oBAAoB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AACD,SAAS,cAAc,OAAO;AAC1B,SAAO,MAAM,QAAQ,MAAM,QAAQ;AACvC;AAIA,SAAS,iBAAiB,YAAY,MAAM;AACxC,MAAIC;AACJ,MAAI,CAAC;AACD;AACJ,QAAM,WAAWA,MAAK,KAAK,oBAAoB,QAAQA,QAAO,SAASA,MAAK,KAAK,oBAAoB,QAC/F,gBACA,KAAK,WAAW,KAAK,mBAAmB,SACpC,YACA;AACV,SAAO,OAAO,KAAK,UAAU,EACxB,IAAI,CAAC,QAAQ;AACd,QAAIA,KAAI;AACR,UAAM,SAASA,MAAK,WAAW,GAAG,OAAO,QAAQA,QAAO,SAASA,MAAK;AACtE,QAAI,KAAK,YAAY,WAAW;AAE5B,aAAO,KAAK,eAAe,IAAI,GAAG,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC1E;AACA,QAAI,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,UAAU,IAAI;AACnD,aAAO;AAAA,IACX;AACA,WAAO,GAAG,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,EACnC,CAAC,EACI,KAAK,GAAG;AACjB;AAIA,IAAM,YAAY,oBAAI,IAAI;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AASM,SAAS,OAAO,MAAM,UAAU,CAAC,GAAG;AACvC,QAAM,QAAQ,YAAY,OAAO,OAAO,CAAC,IAAI;AAC7C,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAU,WAAW,MAAM,CAAC,GAAG,OAAO;AAAA,EAC1C;AACA,SAAO;AACX;AAEA,SAAS,WAAW,MAAM,SAAS;AAC/B,UAAQ,KAAK,MAAM;AAAA,IACf,KAAiB;AACb,aAAO,OAAO,KAAK,UAAU,OAAO;AAAA,IAExC,KAAiB;AAAA,IACjB,KAAiB;AACb,aAAO,gBAAgB,IAAI;AAAA,IAC/B,KAAiB;AACb,aAAO,cAAc,IAAI;AAAA,IAC7B,KAAiB;AACb,aAAO,YAAY,IAAI;AAAA,IAC3B,KAAiB;AAAA,IACjB,KAAiB;AAAA,IACjB,KAAiB;AACb,aAAO,UAAU,MAAM,OAAO;AAAA,IAClC,KAAiB;AACb,aAAO,WAAW,MAAM,OAAO;AAAA,EACvC;AACJ;AACA,IAAM,+BAA+B,oBAAI,IAAI;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AACD,IAAM,kBAAkB,oBAAI,IAAI,CAAC,OAAO,MAAM,CAAC;AAC/C,SAAS,UAAU,MAAM,MAAM;AAC3B,MAAIC;AAEJ,MAAI,KAAK,YAAY,WAAW;AAE5B,SAAK,QAAQA,MAAK,aAAa,IAAI,KAAK,IAAI,OAAO,QAAQA,QAAO,SAASA,MAAK,KAAK;AAErF,QAAI,KAAK,UACL,6BAA6B,IAAI,KAAK,OAAO,IAAI,GAAG;AACpD,aAAO,EAAE,GAAG,MAAM,SAAS,MAAM;AAAA,IACrC;AAAA,EACJ;AACA,MAAI,CAAC,KAAK,WAAW,gBAAgB,IAAI,KAAK,IAAI,GAAG;AACjD,WAAO,EAAE,GAAG,MAAM,SAAS,UAAU;AAAA,EACzC;AACA,MAAI,MAAM,IAAI,KAAK,IAAI;AACvB,QAAM,UAAU,iBAAiB,KAAK,SAAS,IAAI;AACnD,MAAI,SAAS;AACT,WAAO,IAAI,OAAO;AAAA,EACtB;AACA,MAAI,KAAK,SAAS,WAAW,MACxB,KAAK;AAAA;AAAA,IAEE,KAAK,oBAAoB;AAAA;AAAA;AAAA,IAEzB,KAAK,mBAAmB,UAAU,IAAI,KAAK,IAAI;AAAA,MAAI;AAC3D,QAAI,CAAC,KAAK;AACN,aAAO;AACX,WAAO;AAAA,EACX,OACK;AACD,WAAO;AACP,QAAI,KAAK,SAAS,SAAS,GAAG;AAC1B,aAAO,OAAO,KAAK,UAAU,IAAI;AAAA,IACrC;AACA,QAAI,KAAK,WAAW,CAAC,UAAU,IAAI,KAAK,IAAI,GAAG;AAC3C,aAAO,KAAK,KAAK,IAAI;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,IAAI,KAAK,IAAI;AACxB;AACA,SAAS,WAAW,MAAM,MAAM;AAC5B,MAAIA;AACJ,MAAI,OAAO,KAAK,QAAQ;AAExB,QAAMA,MAAK,KAAK,oBAAoB,QAAQA,QAAO,SAASA,MAAK,KAAK,oBAAoB,SACtF,EAAE,CAAC,KAAK,WACJ,KAAK,UACL,kBAAkB,IAAI,KAAK,OAAO,IAAI,IAAI;AAC9C,WACI,KAAK,WAAW,KAAK,mBAAmB,SAClC,UAAU,IAAI,IACd,WAAW,IAAI;AAAA,EAC7B;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM;AACvB,SAAO,YAAY,KAAK,SAAS,CAAC,EAAE,IAAI;AAC5C;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,OAAO,KAAK,IAAI;AAC3B;;;ACjJA,IAAkB;CAAlB,SAAkBC,mBAAgB;AAC9B,EAAAA,kBAAAA,kBAAA,cAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,cAAA,IAAA,EAAA,IAAA;AACJ,GANkB,qBAAA,mBAAgB,CAAA,EAAA;;;AChB5B,SAAU,cAAc,MAAc,SAAiB;AACzD,QAAM,UAAU,IAAI,WAAW,QAAW,OAAO;AACjD,MAAI,OAAO,SAAS,OAAO,EAAE,IAAI,IAAI;AACrC,SAAO,QAAQ;AACnB;;;AC7BA,uBAAkB;AAalB,SAAS,sBAAuBC,IAAG,GAAG,IAAI,MAAM,QAAW;AACzD,MAAIA,OAAM,QAAW;AACnB,UAAM,KAAK,YAAa,MAAM;AAAE,aAAO,EAAE,IAAI,GAAG,IAAI;AAAA,IAAG;AACvD,WAAO;AAAA,EACT;AACA,MAAIA,MAAK,GAAG;AACV,WAAO,YAAa,MAAM;AAAE,aAAO,EAAE,sBAAsBA,KAAI,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI;AAAA,IAAG;AAAA,EACrF;AACA,SAAO;AACT;AAUA,SAAS,cAAe,KAAK,MAAM;AACjC,MAAI,QAAQ;AACZ,MAAI,MAAM,IAAI;AACd,SAAO,QAAQ,OAAO,IAAI,KAAK,MAAM,MAAM;AAAE,MAAE;AAAA,EAAO;AACtD,SAAO,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,MAAM;AAAE,MAAE;AAAA,EAAK;AACtD,SAAQ,QAAQ,KAAK,MAAM,IAAI,SAC3B,IAAI,UAAU,OAAO,GAAG,IACxB;AACN;AAUA,SAAS,iBAAkB,KAAK,MAAM;AACpC,MAAI,MAAM,IAAI;AACd,SAAO,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,MAAM;AAAE,MAAE;AAAA,EAAK;AAClD,SAAQ,MAAM,IAAI,SACd,IAAI,UAAU,GAAG,GAAG,IACpB;AACN;AASA,SAAS,cAAe,KAAK;AAC3B,SAAO,IAAI,QAAQ,WAAW,CAAAC,OAAK,QAAQA,GAAE,WAAW,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AACzF;AAYA,SAAS,0BAA2B,OAAO,QAAQ;AACjD,QAAMC,OAAM,oBAAI,IAAI;AACpB,WAAS,IAAI,MAAM,QAAQ,MAAM,KAAI;AACnC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,MAAM,OAAO,IAAI;AACvB,IAAAA,KAAI;AAAA,MACF;AAAA,MACCA,KAAI,IAAI,GAAG,QACR,iBAAAC,SAAM,MAAMD,KAAI,IAAI,GAAG,GAAG,EAAE,YAAY,iBAAiB,CAAC,IAC1D;AAAA,IACN;AAAA,EACF;AACA,SAAO,CAAC,GAAGA,KAAI,OAAO,CAAC,EAAE,QAAQ;AACnC;AAEA,IAAM,mBAAmB,CAAC,KAAK,KAAK,YAAY,CAAC,GAAG,GAAG;AASvD,SAAS,IAAK,KAAK,MAAM;AACvB,aAAW,OAAO,MAAM;AACtB,QAAI,CAAC,KAAK;AAAE,aAAO;AAAA,IAAW;AAC9B,UAAM,IAAI,GAAG;AAAA,EACf;AACA,SAAO;AACT;AAYA,SAAS,uBAAwB,KAAK,WAAW,KAAK,OAAO,IAAI;AAC/D,QAAM,SAAS,CAAC;AAChB,KAAG;AACD,WAAO;AACP,WAAO,KAAK,MAAM,IAAI;AACtB,UAAO,MAAM,QAAS;AAAA,EACxB,SAAS,MAAM;AACf,QAAM,WAAW,SAAS,WAAW,CAAC;AACtC,SAAO,OACJ,QAAQ,EACR,IAAI,CAAAF,OAAK,OAAO,aAAa,WAAWA,EAAC,CAAC,EAC1C,KAAK,EAAE;AACZ;AAEA,IAAM,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG;AAC7B,IAAM,IAAI,CAAC,KAAK,KAAK,GAAG;AAQxB,SAAS,cAAe,KAAK;AAC3B,SAAO,CAAC,GAAI,MAAO,EAAE,EAClB,IAAI,CAAAA,OAAK,CAACA,EAAC,EACX,QAAQ,EACR,IAAI,CAAC,GAAG,MAAQ,IAAI,IAAI,KACpB,IAAI,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC,IACvC,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAG,EACpC,QAAQ,EACR,KAAK,EAAE;AACZ;AAKA,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStB,YAAa,SAAS,gBAAgB,QAAW;AAE/C,SAAK,QAAQ,CAAC;AAEd,SAAK,gBAAgB,CAAC;AACtB,SAAK,gBAAgB,iBAAiB,QAAQ,YAAY,OAAO;AACjE,SAAK,yBAAyB,KAAK;AACnC,SAAK,iBAAiB,IAAI,SAAS,CAAC,iBAAiB,gBAAgB,CAAC,KAAK,CAAC;AAC5E,SAAK,mBAAmB,IAAI,SAAS,CAAC,iBAAiB,kBAAkB,CAAC,KAAK;AAE/E,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAU,MAAM,SAAS,OAAO;AAC9B,QAAI,KAAK,0BAA0B,KAAK,CAAC,QAAQ;AAC/C,WAAK,aAAa;AAAA,IACpB;AACA,UAAM,cAAc,KAAK,cAAc,WAAW;AAClD,UAAM,OAAO,KAAK,UAAU,cAAc,IAAI;AAC9C,QAAK,QAAQ,KAAK,0BAA2B,QAAQ;AAEnD,WAAK,cAAc,KAAK,IAAI;AAC5B,WAAK,0BAA0B;AAAA,IAEjC,OAAO;AAGL,YAAM,CAAC,OAAO,GAAG,IAAI,IAAI,KAAK,cAAc,IAAI;AAChD,UAAI,CAAC,aAAa;AAAE,aAAK,aAAa;AAAA,MAAG;AACzC,WAAK,cAAc,KAAK,KAAK;AAC7B,WAAK,0BAA0B,MAAM;AACrC,iBAAW,QAAQ,MAAM;AACvB,aAAK,aAAa;AAClB,aAAK,cAAc,KAAK,IAAI;AAC5B,aAAK,0BAA0B,KAAK;AAAA,MACtC;AAAA,IAEF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAW;AACT,UAAM,WAAW,KAAK,cAAc,IAAI;AACxC,QAAI,aAAa,QAAW;AAC1B,YAAM,cAAc,KAAK,cAAc,WAAW;AAClD,YAAM,OAAO,SAAS,UAAU,cAAc,IAAI;AAClD,WAAK,0BAA0B;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAY,MAAM,SAAS,OAAO;AAChC,QAAI,KAAK,wBAAwB,KAAK,SAAS,KAAK,wBAAwB;AAC1E,WAAK,SAAS,MAAM,MAAM;AAC1B,WAAK,uBAAuB;AAAA,IAC9B,OAAO;AACL,YAAM,WAAW,KAAK,QAAQ;AAC9B,WAAK,SAAU,WAAY,SAAS,OAAO,IAAI,IAAI,MAAM,MAAM;AAAA,IACjE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAcA,KAAI,GAAG;AACnB,SAAK,MAAM,KAAK,KAAK,aAAa;AAClC,QAAIA,KAAI,GAAG;AACT,WAAK,MAAM,KAAK,GAAG,MAAM,KAAK,EAAE,QAAQA,KAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAAA,IAC5D;AACA,SAAK,gBAAgB,CAAC;AACtB,SAAK,yBAAyB,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAW;AACT,WAAO,KAAK,MAAM,WAAW,KACtB,KAAK,cAAc,WAAW;AAAA,EACvC;AAAA,EAEA,QAAS;AACP,SAAK,MAAM,SAAS;AACpB,SAAK,cAAc,SAAS;AAC5B,SAAK,yBAAyB,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAY;AACV,WAAO,CAAC,GAAG,KAAK,OAAO,KAAK,aAAa,EACtC,IAAI,WAAS,MAAM,KAAK,GAAG,CAAC,EAC5B,KAAK,IAAI;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAe,MAAM;AACnB,UAAM,QAAQ,CAAC;AACf,QAAI,MAAM;AACV,WAAO,KAAK,SAAS,KAAK,eAAe;AAEvC,YAAM,YAAY,KAAK,UAAU,GAAG,KAAK,aAAa;AACtD,YAAM,iBAAiB,KAAK,UAAU,KAAK,aAAa;AAExD,YAAM,aAAa,UAAU,YAAY,KAAK,eAAe,GAAG,CAAC;AAEjE,UAAI,aAAa,IAAI;AAEnB,eAAO,UAAU,UAAU,aAAa,CAAC,IAAI;AAC7C,cAAM,KAAK,UAAU,UAAU,GAAG,aAAa,CAAC,CAAC;AAAA,MAEnD,OAAO;AAEL;AACA,YAAI,MAAM,KAAK,eAAe,QAAQ;AAEpC,iBAAO,YAAY;AAAA,QAErB,OAAO;AAEL,cAAI,KAAK,kBAAkB;AACzB,kBAAM,KAAK,SAAS;AACpB,mBAAO;AACP,gBAAI,KAAK,SAAS,KAAK,eAAe;AACpC;AAAA,YACF;AAAA,UACF,OAAO;AACL,mBAAO,YAAY;AAAA,UACrB;AACA;AAAA,QAEF;AAAA,MAEF;AAAA,IAEF;AACA,UAAM,KAAK,IAAI;AACf,WAAO;AAAA,EACT;AACF;AAKA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAa,OAAO,MAAM;AAAE,SAAK,OAAO;AAAA,EAAM;AAAA,EAE9C,UAAW;AAAE,WAAQ,KAAK,OAAQ,KAAK,OAAO;AAAA,EAAM;AACtD;AAEA,IAAM,iBAAN,cAA6B,UAAU;AAAA,EACrC,YAAa,SAAS,OAAO,MAAM,oBAAoB,GAAG,gBAAgB,QAAW;AACnF,UAAM,IAAI;AACV,SAAK,oBAAoB;AACzB,SAAK,oBAAoB,IAAI,kBAAkB,SAAS,aAAa;AACrE,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,QAAQ,QAAQ,KAAK;AAC1B,SAAK,WAAW,QAAQ,KAAK;AAAA,EAC/B;AACF;AAEA,IAAM,gBAAN,cAA4B,eAAe;AAAA,EACzC,YACE,SACA,OAAO,MACP;AAAA,IACE,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB,IAAI,CAAC,GACL;AACA,UAAM,SAAS,MAAM,mBAAmB,aAAa;AACrD,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAAA,EAC5B;AACF;AAEA,IAAM,oBAAN,cAAgC,eAAe;AAAA,EAC7C,YACE,SACA,OAAO,MACP;AAAA,IACE,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,SAAS;AAAA,EACX,IAAI,CAAC,GACL;AACA,UAAM,SAAS,MAAM,mBAAmB,aAAa;AACrD,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,IAAM,iBAAN,cAA6B,UAAU;AAAA,EACrC,YAAa,OAAO,MAAM;AACxB,UAAM,IAAI;AACV,SAAK,OAAO,CAAC;AACb,SAAK,QAAQ,QAAQ,KAAK;AAC1B,SAAK,WAAW,QAAQ,KAAK;AAAA,EAC/B;AACF;AAEA,IAAM,oBAAN,cAAgC,UAAU;AAAA,EACxC,YAAa,OAAO,MAAM;AACxB,UAAM,IAAI;AACV,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,QAAQ,KAAK;AAC1B,SAAK,WAAW,QAAQ,KAAK;AAAA,EAC/B;AACF;AAEA,IAAM,qBAAN,cAAiC,UAAU;AAAA,EACzC,YAAa,SAAS,OAAO,MAAM,iBAAiB,QAAW;AAC7D,UAAM,IAAI;AACV,SAAK,oBAAoB,IAAI,kBAAkB,SAAS,cAAc;AACtE,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,QAAQ,QAAQ,KAAK;AAC1B,SAAK,WAAW,QAAQ,KAAK;AAAA,EAC/B;AACF;AAEA,IAAM,uBAAN,cAAmC,UAAU;AAAA,EAC3C,YAAa,OAAO,MAAM,WAAW;AACnC,UAAM,IAAI;AACV,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,SAAS,kBAAmB,KAAK;AAC/B,SAAO,CAAC,GAAG,GAAG,EACX,IAAI,CAAAC,OAAK,QAAQA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAC9D,KAAK,EAAE;AACZ;AAOA,IAAM,sBAAN,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxB,YAAa,SAAS;AACpB,SAAK,kBAAmB,QAAQ,mBAC5B,QAAQ,qBAAqB,QAAQ,OAAO,EAAE,IAC9C,QAAQ;AACZ,UAAM,kBAAkB,kBAAkB,KAAK,eAAe;AAC9D,SAAK,sBAAsB,IAAI,OAAO,KAAK,eAAe,GAAG;AAC7D,SAAK,uBAAuB,IAAI,OAAO,IAAI,eAAe,IAAI;AAC9D,SAAK,yBAAyB,IAAI,OAAO,KAAK,eAAe,KAAK;AAClE,SAAK,2BAA2B,IAAI,OAAO,aAAa,eAAe,MAAM,GAAG;AAChF,SAAK,8BAA8B,IAAI,OAAO,iBAAiB,GAAG;AAElE,QAAI,QAAQ,kBAAkB;AAE5B,YAAM,kBAAkB,IAAI,OAAO,YAAY,eAAe,MAAM,IAAI;AAUxE,WAAK,gBAAgB,SAAU,MAAM,mBAAmB,YAAa,SAAO,KAAM,SAAS,OAAO;AAChG,YAAI,CAAC,MAAM;AAAE;AAAA,QAAQ;AACrB,cAAM,yBAAyB,kBAAkB;AACjD,YAAI,WAAW;AACf,YAAI,IAAI,gBAAgB,KAAK,IAAI;AACjC,YAAI,GAAG;AACL,qBAAW;AACX,cAAI,EAAE,CAAC,MAAM,MAAM;AACjB,8BAAkB,aAAa;AAAA,UACjC,WAAW,0BAA0B,KAAK,sBAAsB,IAAI,GAAG;AACrE,8BAAkB,SAAS,UAAU,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,UACpD,OAAO;AACL,8BAAkB,WAAW,UAAU,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,UACtD;AACA,kBAAQ,IAAI,gBAAgB,KAAK,IAAI,OAAO,MAAM;AAChD,gBAAI,EAAE,CAAC,MAAM,MAAM;AACjB,gCAAkB,aAAa;AAAA,YACjC,OAAO;AACL,gCAAkB,SAAS,UAAU,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,YACpD;AAAA,UACF;AAAA,QACF;AACA,0BAAkB,eAAgB,0BAA0B,CAAC,YAAc,KAAK,uBAAuB,IAAI;AAAA,MAG7G;AAAA,IAEF,OAAO;AAEL,YAAM,SAAS,IAAI,OAAO,KAAK,eAAe,MAAM,GAAG;AAEvD,WAAK,gBAAgB,SAAU,MAAM,mBAAmB,YAAa,SAAO,KAAM,SAAS,OAAO;AAChG,YAAI,CAAC,MAAM;AAAE;AAAA,QAAQ;AACrB,cAAM,yBAAyB,kBAAkB;AACjD,YAAI,WAAW;AACf,YAAI,IAAI,OAAO,KAAK,IAAI;AACxB,YAAI,GAAG;AACL,qBAAW;AACX,cAAI,0BAA0B,KAAK,sBAAsB,IAAI,GAAG;AAC9D,8BAAkB,SAAS,UAAU,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,UACpD,OAAO;AACL,8BAAkB,WAAW,UAAU,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,UACtD;AACA,kBAAQ,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM;AACvC,8BAAkB,SAAS,UAAU,EAAE,CAAC,CAAC,GAAG,MAAM;AAAA,UACpD;AAAA,QACF;AACA,0BAAkB,eAAgB,0BAA0B,CAAC,YAAa,KAAK,uBAAuB,IAAI;AAAA,MAC5G;AAAA,IAEF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,WAAY,MAAM,mBAAmB,SAAS,MAAM;AAClD,QAAI,CAAC,MAAM;AAAE;AAAA,IAAQ;AACrB,UAAM,yBAAyB,kBAAkB;AACjD,QAAI,WAAW;AACf,QAAI,IAAI,KAAK,4BAA4B,KAAK,IAAI;AAClD,QAAI,GAAG;AACL,iBAAW;AACX,UAAI,EAAE,CAAC,MAAM,MAAM;AACjB,0BAAkB,aAAa;AAAA,MACjC,WAAW,wBAAwB;AACjC,0BAAkB,SAAS,EAAE,CAAC,GAAG,MAAM;AAAA,MACzC,OAAO;AACL,0BAAkB,WAAW,EAAE,CAAC,GAAG,MAAM;AAAA,MAC3C;AACA,cAAQ,IAAI,KAAK,4BAA4B,KAAK,IAAI,OAAO,MAAM;AACjE,YAAI,EAAE,CAAC,MAAM,MAAM;AACjB,4BAAkB,aAAa;AAAA,QACjC,OAAO;AACL,4BAAkB,SAAS,EAAE,CAAC,GAAG,MAAM;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AACA,sBAAkB,eAAgB,0BAA0B,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAuB,MAAM;AAC3B,WAAO,KAAK,oBAAoB,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAwB,MAAM;AAC5B,WAAO,KAAK,qBAAqB,KAAK,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAmB,MAAM;AACvB,WAAO,CAAC,KAAK,uBAAuB,KAAK,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAsB,MAAM;AAC1B,SAAK,yBAAyB,YAAY;AAC1C,QAAI,UAAU;AACd,QAAI;AACJ,YAAQ,QAAQ,KAAK,yBAAyB,KAAK,IAAI,OAAO,MAAM;AAClE,UAAI,MAAM,CAAC,MAAM,MAAM;AACrB;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEF;AAOA,IAAM,mBAAN,MAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,YAAa,SAAS,QAAQ,WAAW,QAAW;AAClD,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,sBAAsB,IAAI,oBAAoB,OAAO;AAE1D,SAAK,aAAa,IAAI,eAAe,OAAO;AAE5C,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAmB,eAAe;AAChC,SAAK,mBAAmB,IAAI,qBAAqB,KAAK,kBAAkB,aAAa;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAoB;AAClB,QAAI,CAAC,KAAK,kBAAkB;AAAE,aAAO;AAAA,IAAW;AAChD,UAAM,YAAY,KAAK,iBAAiB;AACxC,SAAK,mBAAmB,KAAK,iBAAiB;AAC9C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,cAAe;AACb,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAc;AACZ,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA;AAAA,EAGA,8BAA+B;AAC7B,UAAM,KAAM,KAAK,mBACZ,CAAC,QAAQ,iBAAiB,KAAK,KAAK,gBAAgB,IACrD;AACJ,UAAM,KAAK,KAAK,QAAQ;AACxB,WAAQ,KACF,KAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,IAAI,KAC/B;AAAA,EACN;AAAA,EAEA,gBAAiB;AACf,UAAM,OAAO,KAAK;AAClB,SAAK,aAAa,KAAK;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAgB;AACd,QAAI,EACF,KAAK,sBAAsB,kBACxB,KAAK,sBAAsB,qBAC3B,KAAK,sBAAsB,qBAC7B;AAAE;AAAA,IAAQ;AACb,QAAI,KAAK,WAAW,OAAO;AACzB,WAAK,WAAW,WAAW;AAAA,IAC7B,OAAO;AACL,WAAK,WAAW,kBAAkB,aAAa;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA2B;AACzB,QACE,KAAK,sBAAsB,kBACxB,KAAK,sBAAsB,qBAC3B,KAAK,sBAAsB,oBAC9B;AACA,WAAK,WAAW,kBAAkB,uBAAuB;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,UAAW,KAAK,EAAE,kBAAkB,MAAM,IAAI,CAAC,GAAG;AAChD,QAAI,EACF,KAAK,sBAAsB,kBACxB,KAAK,sBAAsB,qBAC3B,KAAK,sBAAsB,qBAC7B;AAAE;AAAA,IAAQ;AAEb,QAAI,KAAK,WAAW,OAAO;AACzB,WAAK,WAAW,WAAW;AAC3B;AAAA,IACF;AAEA,QACE,IAAI,WAAW;AAAA,IAEb,KAAK,WAAW;AAAA,IAChB,CAAC,KAAK,oBAAoB,kBAAkB,GAAG,GAEjD;AAAE;AAAA,IAAQ;AAEZ,QAAI,KAAK,QAAQ,kBAAkB;AACjC,YAAM,iBAAiB,KAAK,oBAAoB,qBAAqB,GAAG;AACxE,UAAI,iBAAiB,GAAG;AACtB,aAAK,WAAW,kBAAkB,aAAa,cAAc;AAE7D;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,WAAW,mBAAmB;AACrC,WAAK,WAAW,kBAAkB,aAAa,KAAK,WAAW,iBAAiB;AAAA,IAClF;AACA,SAAK,oBAAoB;AAAA,MACvB;AAAA,MACA,KAAK,WAAW;AAAA,MACf,kBAAmB,SAAY,KAAK,4BAA4B;AAAA,MACjE,KAAK,WAAW;AAAA,IAClB;AACA,SAAK,WAAW,oBAAoB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAY,KAAK;AACf,QAAI,EACF,KAAK,sBAAsB,kBACxB,KAAK,sBAAsB,qBAC3B,KAAK,sBAAsB,qBAC7B;AAAE;AAAA,IAAQ;AAEb,QAAI,IAAI,WAAW,GAAG;AAAE;AAAA,IAAQ;AAEhC,QAAI,KAAK,WAAW,OAAO;AACzB,WAAK,WAAW,WAAW;AAC3B;AAAA,IACF;AAEA,QAAI,KAAK,WAAW,mBAAmB;AACrC,WAAK,WAAW,kBAAkB,aAAa,KAAK,WAAW,iBAAiB;AAAA,IAClF;AACA,SAAK,oBAAoB;AAAA,MACvB;AAAA,MACA,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,IAClB;AACA,SAAK,WAAW,oBAAoB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,UAAW,EAAE,oBAAoB,GAAG,qBAAqB,GAAG,QAAQ,MAAM,IAAI,CAAC,GAAG;AAChF,UAAM,gBAAgB,KAAK,IAAI,IAAI,KAAK,WAAW,kBAAkB,gBAAgB,kBAAkB;AACvG,SAAK,aAAa,IAAI;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,QAAI,OAAO;AAAE,WAAK,WAAW,QAAQ;AAAA,IAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,WAAY,EAAE,qBAAqB,GAAG,iBAAiB,OAAU,IAAI,CAAC,GAAG;AACvE,UAAM,QAAQ,KAAK,cAAc;AACjC,UAAM,YAAa,iBAAkB,eAAe,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK;AACnF,YAAQ,KAAK,YAAY,WAAW,MAAM,mBAAmB,KAAK,IAAI,MAAM,mBAAmB,kBAAkB,CAAC;AAAA,EACpH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,SAAU,EAAE,kBAAkB,GAAG,cAAc,QAAQ,qBAAqB,GAAG,oBAAoB,EAAE,IAAI,CAAC,GAAG;AAC3G,SAAK,aAAa,IAAI,cAAc,KAAK,SAAS,KAAK,YAAY;AAAA,MACjE;AAAA,MACA;AAAA,MACA,eAAe,KAAK,WAAW,kBAAkB;AAAA,MACjD;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAc,EAAE,SAAS,GAAG,IAAI,CAAC,GAAG;AAClC,QAAI,EAAE,KAAK,sBAAsB,gBAAgB;AAC/C,YAAM,IAAI,MAAM,6EAA8E;AAAA,IAChG;AACA,UAAM,OAAO,KAAK;AAClB,UAAM,eAAe,KAAK,IAAI,OAAO,QAAQ,KAAK,eAAe;AACjE,UAAM,gBAAgB,KAAK,IAAI,IAAI,KAAK,kBAAkB,gBAAgB,YAAY;AACtF,SAAK,aAAa,IAAI,kBAAkB,KAAK,SAAS,MAAM;AAAA,MAC1D;AAAA,MACA;AAAA,MACA,mBAAmB,KAAK;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAiB;AACf,UAAM,WAAW,KAAK,cAAc;AACpC,UAAM,OAAO,SAAS;AAEtB,UAAM,eAAe,KAAK,IAAI,SAAS,OAAO,QAAQ,KAAK,eAAe;AAC1E,UAAM,UAAU,OAAO,IAAI,OAAO,YAAY;AAC9C,UAAM,SAAU,KAAK,gBAAgB,UACjC,SAAS,OAAO,SAAS,YAAY,IACrC,SAAS,OAAO,OAAO,YAAY;AACvC,UAAM,OAAO,SAAS,QAAQ,QAAQ,EAAE,QAAQ,OAAO,OAAO;AAE9D;AAAA,MACE;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,KAAK,IAAI,SAAS,mBAAmB,KAAK,kBAAkB;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,UAAW,EAAE,qBAAqB,EAAE,IAAI,CAAC,GAAG;AAC1C,UAAM,OAAO,KAAK,cAAc;AAChC,UAAM,OAAO,QAAQ,IAAI;AACzB,QAAI,MAAM;AACR,cAAQ,KAAK,YAAY,MAAM,KAAK,mBAAmB,kBAAkB;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAa;AACX,SAAK,aAAa,IAAI,eAAe,KAAK,UAAU;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAgB;AACd,QAAI,EAAE,KAAK,sBAAsB,iBAAiB;AAChD,YAAM,IAAI,MAAM,8EAA+E;AAAA,IACjG;AACA,SAAK,aAAa,IAAI,kBAAkB,KAAK,UAAU;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,cAAe,EAAE,iBAAiB,OAAU,IAAI,CAAC,GAAG;AAClD,QAAI,EAAE,KAAK,sBAAsB,oBAAoB;AACnD,YAAM,IAAI,MAAM,mFAAoF;AAAA,IACtG;AACA,SAAK,aAAa,IAAI,mBAAmB,KAAK,SAAS,KAAK,YAAY,cAAc;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAgB,EAAE,UAAU,GAAG,UAAU,EAAE,IAAI,CAAC,GAAG;AACjD,UAAM,OAAO,KAAK,cAAc;AAChC,UAAM,OAAO,cAAc,QAAQ,IAAI,GAAG,IAAI;AAC9C,SAAK,KAAK,MAAM,KAAK,EAAE,SAAkB,SAAkB,KAAW,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAiB;AACf,UAAM,MAAM,KAAK,cAAc;AAC/B,QAAI,KAAK,KAAK,KAAK,IAAI,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,WAAY,EAAE,eAAAG,gBAAe,oBAAoB,GAAG,qBAAqB,EAAE,GAAG;AAC5E,UAAM,QAAQ,KAAK,cAAc;AACjC,UAAM,SAASA,eAAc,MAAM,IAAI;AACvC,QAAI,QAAQ;AACV,cAAQ,KAAK,YAAY,QAAQ,mBAAmB,kBAAkB;AAAA,IACxE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAY;AACV,WAAO,QAAQ,KAAK,WAAW,QAAQ,CAAC;AAAA,EAE1C;AAEF;AAEA,SAAS,QAAS,WAAW;AAC3B,MAAI,EACF,qBAAqB,kBAClB,qBAAqB,qBACrB,qBAAqB,qBACvB;AACD,UAAM,IAAI,MAAM,6EAA6E;AAAA,EAC/F;AACA,SAAQ,UAAU,kBAAkB,QAAQ,IACxC,UAAU,UACV,UAAU,UAAU,UAAU,kBAAkB,SAAS;AAC/D;AAEA,SAAS,QAAS,WAAW,MAAM,mBAAmB,oBAAoB;AACxE,MAAI,EACF,qBAAqB,kBAClB,qBAAqB,qBACrB,qBAAqB,qBACvB;AACD,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AACA,QAAM,aAAa,QAAQ,SAAS;AACpC,QAAM,aAAa,KAAK,IAAI,UAAU,mBAAmB,iBAAiB;AAC1E,YAAU,kBAAkB,MAAM;AAClC,MAAI,YAAY;AACd,cAAU,UAAU,aAAa,KAAK,OAAO,UAAU,IAAI;AAAA,EAC7D,OAAO;AACL,cAAU,UAAU;AACpB,cAAU,oBAAoB;AAAA,EAChC;AACA,YAAU,oBAAoB;AAChC;AAOA,SAAS,iBAAkB,KAAK,aAAa;AAC3C,SAAS,cAAe,iBAAiB,YAAY,UAAU,GAAG,GAAG,YAAY,IAAI,IAAI;AAC3F;AAUA,SAAS,UAAW,UAAU,CAAC,GAAG;AAChC,QAAM,yBAAyB,QAAQ,UAAU,OAAO,CAAAC,OAAK,CAACA,GAAE,MAAM;AACtE,MAAI,uBAAuB,QAAQ;AACjC,UAAM,IAAI;AAAA,MACR,mDACA,uBAAuB,IAAI,CAAAA,OAAK,KAAKA,GAAE,QAAQ,IAAI,EAAE,KAAK,IAAI;AAAA,IAChE;AAAA,EACF;AACA,QAAM,SAAS,IAAI;AAAA,IACjB,QAAQ,UAAU,IAAI,CAAAA,OAAK,CAACA,GAAE,UAAUA,EAAC,CAAC;AAAA,EAC5C,EAAE,MAAM,UAAU;AAElB,MAAI,OAAO,QAAQ,qBAAqB,YAAY;AAClD,YAAQ,mBAAmB,qBAAqB,QAAQ,gBAAgB;AAAA,EAC1E;AAEA,QAAM,sBAAsB,IAAI;AAAA,IAC9B,QAAQ,aAAa,UAAU,IAAI,CAACA,IAAG,MAAM,CAACA,IAAG,IAAI,CAAC,CAAC;AAAA,EACzD,EAAE,MAAM,UAAU;AAClB,WAAS,iBAAkB,KAAK;AAC9B,WAAO,UAAU,KAAK,SAAS,mBAAmB;AAAA,EACpD;AAEA,QAAM,cAAc;AAAA,IAClB,QAAQ,OAAO;AAAA,IACf;AAAA,IACA,SAAU,KAAK,SAAS;AACtB,cAAQ,UAAU,QAAQ,OAAO,YAAY,EAAE;AAAA,IACjD;AAAA,EACF;AAEA,SAAO,SAAU,MAAM,WAAW,QAAW;AAC3C,WAAO,QAAQ,MAAM,UAAU,SAAS,QAAQ,kBAAkB,WAAW;AAAA,EAC/E;AACF;AAiBA,SAAS,QAAS,MAAM,UAAU,SAAS,QAAQ,kBAAkB,MAAM;AACzE,QAAM,iBAAiB,QAAQ,OAAO;AACtC,MAAI,kBAAkB,QAAQ,KAAK,SAAS,gBAAgB;AAC1D,YAAQ;AAAA,MACN,gBAAgB,KAAK,MAAM,8BAA8B,cAAc;AAAA,IACzE;AACA,WAAO,KAAK,UAAU,GAAG,cAAc;AAAA,EACzC;AAEA,QAAM,WAAW,cAAc,MAAM,EAAE,gBAAgB,QAAQ,eAAe,CAAC;AAC/E,QAAM,QAAQ,iBAAiB,SAAS,QAAQ;AAChD,QAAM,UAAU,IAAI,iBAAiB,SAAS,QAAQ,QAAQ;AAC9D,OAAK,OAAO,OAAO;AACnB,SAAO,QAAQ,SAAS;AAC1B;AAGA,SAAS,UAAW,KAAK,SAAS,qBAAqB;AACrD,QAAM,UAAU,CAAC;AAEjB,WAASC,eAAe,MAAiCC,MAAK;AAC5D,IAAAA,OAAMA,KAAI,MAAM,GAAG,QAAQ,OAAO,aAAa;AAC/C,eAAW,QAAQA,MAAK;AACtB,UAAI,KAAK,SAAS,OAAO;AACvB;AAAA,MACF;AACA,YAAM,sBAAsB,oBAAoB,MAAM,IAAI;AAC1D,UAAI,sBAAsB,GAAG;AAC3B,gBAAQ,KAAK,EAAE,eAAe,qBAAqB,SAAS,KAAK,CAAC;AAAA,MACpE,WAAW,KAAK,UAAU;AACxB,aAAK,KAAK,QAAQ;AAAA,MACpB;AACA,UAAI,QAAQ,UAAU,QAAQ,OAAO,iBAAiB;AACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,cAAc;AAAA,IAClB,QAAQ,OAAO;AAAA,IACfD;AAAA,EACF;AACA,cAAY,GAAG;AAEf,MAAI,QAAQ,aAAa,YAAY,cAAc;AACjD,YAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,gBAAgB,EAAE,aAAa;AAAA,EAC1D;AACA,SAAQ,QAAQ,aAAa,sBAAsB,QAAQ,WAAW,IAClE,MACA,QAAQ,IAAI,OAAK,EAAE,OAAO;AAChC;AAUA,SAAS,cAAe,MAAM,KAAK,SAAS;AAC1C,MAAI,CAAC,KAAK;AAAE;AAAA,EAAQ;AAEpB,QAAM,UAAU,QAAQ;AAExB,QAAM,oBAAoB,IAAI,SAAS,QAAQ,OAAO;AACtD,MAAI,mBAAmB;AACrB,UAAM,IAAI,MAAM,GAAG,QAAQ,OAAO,aAAa;AAC/C,QAAI,KAAK;AAAA,MACP,MAAM,QAAQ,OAAO;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAEA,aAAW,QAAQ,KAAK;AACtB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,QAAQ;AACX,gBAAQ,UAAU,KAAK,IAAI;AAC3B;AAAA,MACF;AAAA,MACA,KAAK,OAAO;AACV,cAAM,gBAAgB,QAAQ,OAAO,MAAM,IAAI;AAC/C,cAAM,SAAS,QAAQ,WAAW,cAAc,MAAM;AACtD,eAAO,MAAM,MAAM,SAAS,cAAc,WAAW,CAAC,CAAC;AACvD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA;AACF;AAYA,SAAS,qBAAsB,MAAM;AACnC,MAAI,CAAC,QAAQ,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AAC3C,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK;AAClE,QAAM,QAAQ,IAAI;AAAA,IAChB,QACG,IAAI,CAAC,CAACL,EAAC,MAAM,IAAI,cAAc,CAAC,GAAGA,EAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAC5C,KAAK,GAAG;AAAA,IACX;AAAA,EACF;AACA,QAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC;AACvC,QAAM,WAAW,CAAC,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAM,EAAE,CAAC;AAC9D,SAAO,CAAC,QAAQ,IAAI,QAAQ,OAAO,QAAQ;AAC7C;AAOA,SAAS,WAAY,MAAM,MAAM,SAAS,eAAe;AAEzD;AAOA,SAAS,mBAAoB,MAAM,MAAM,SAAS,eAAe;AAC/D,UAAQ,WAAW,cAAc,UAAU,EAAE;AAC/C;AAOA,SAAS,kBAAmB,MAAM,MAAM,SAAS,eAAe;AAC9D,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,UAAQ,WAAW,cAAc,UAAU,EAAE;AAC7C,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,aAAc,MAAM,MAAM,SAAS,eAAe;AACzD,OAAK,KAAK,UAAU,OAAO;AAC7B;AAOA,SAAS,cAAe,MAAM,MAAM,SAAS,eAAe;AAC1D,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAEA,SAAS,cAAe,MAAM;AAC5B,QAAM,QAAS,KAAK,WAAW,KAAK,QAAQ,SACxC,MAAM,OAAO,QAAQ,KAAK,OAAO,EAChC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAQ,MAAM,KAAM,IAAI,GAAG,CAAC,IAAI,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAG,EACtE,KAAK,GAAG,IACT;AACJ,SAAO,IAAI,KAAK,IAAI,GAAG,KAAK;AAC9B;AAEA,SAAS,eAAgB,MAAM;AAC7B,SAAO,KAAK,KAAK,IAAI;AACvB;AAOA,SAAS,gBAAiB,MAAM,MAAM,SAAS,eAAe;AAC5D,UAAQ,YAAY;AACpB,UAAQ,WAAW,cAAc,IAAI,CAAC;AACtC,UAAQ,WAAW;AACnB,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,YAAY;AACpB,UAAQ,WAAW,eAAe,IAAI,CAAC;AACvC,UAAQ,WAAW;AACrB;AAOA,SAAS,eAAgB,MAAM,MAAM,SAAS,eAAe;AAC3D,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,UAAQ,YAAY;AACpB,UAAQ,WAAW,cAAc,IAAI,CAAC;AACtC,UAAQ,WAAW;AACnB,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,YAAY;AACpB,UAAQ,WAAW,eAAe,IAAI,CAAC;AACvC,UAAQ,WAAW;AACnB,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,iBAAkB,MAAM,MAAM,SAAS,eAAe;AAC7D,UAAQ,YAAY;AACpB,UAAQ;AAAA,IACN,OAAO,MAAM,EAAE,gBAAgB,QAAQ,QAAQ,eAAe,CAAC;AAAA,EACjE;AACA,UAAQ,WAAW;AACrB;AAOA,SAAS,gBAAiB,MAAM,MAAM,SAAS,eAAe;AAC5D,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,UAAQ,YAAY;AACpB,UAAQ;AAAA,IACN,OAAO,MAAM,EAAE,gBAAgB,QAAQ,QAAQ,eAAe,CAAC;AAAA,EACjE;AACA,UAAQ,WAAW;AACnB,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,qBAAsB,MAAM,MAAM,SAAS,eAAe;AACjE,UAAQ,WAAW,cAAc,UAAU,EAAE;AAC7C,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,WAAW,cAAc,UAAU,EAAE;AAC/C;AAEA,IAAI,oBAAiC,OAAO,OAAO;AAAA,EACjD,WAAW;AAAA,EACX,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,MAAM;AACR,CAAC;AAED,SAAS,OAAQ,QAAQ,GAAG;AAC1B,MAAI,CAAC,OAAO,CAAC,GAAG;AAAE,WAAO,CAAC,IAAI,CAAC;AAAA,EAAG;AAClC,SAAO,OAAO,CAAC;AACjB;AAEA,SAAS,qBAAsB,KAAK,IAAI,GAAG;AACzC,SAAO,IAAI,CAAC,GAAG;AAAE;AAAA,EAAK;AACtB,SAAO;AACT;AAEA,SAAS,iBAAkB,QAAQ,SAAS;AAC1C,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,UAAM,OAAO,OAAO,QAAQ,CAAC;AAC7B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,OAAO,OAAO,QAAQ,CAAC;AAC7B,UAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;AACtB,cAAM,OAAO,KAAK,CAAC;AACnB,aAAK,CAAC,IAAI,KAAK,CAAC;AAChB,aAAK,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAmB,MAAM,QAAQ,SAAS,SAAS;AAC1D,WAASO,KAAI,GAAGA,KAAI,KAAK,SAASA,MAAK;AACrC,UAAM,YAAY,OAAO,QAAQ,UAAUA,EAAC;AAC5C,aAASP,KAAI,GAAGA,KAAI,KAAK,SAASA,MAAK;AACrC,gBAAU,UAAUA,EAAC,IAAI;AAAA,IAC3B;AAAA,EACF;AACF;AAEA,SAAS,gBAAiB,SAAS,OAAO;AACxC,MAAI,QAAQ,KAAK,MAAM,QAAW;AAChC,YAAQ,KAAK,IAAK,UAAU,IAAK,IAAI,IAAI,gBAAgB,SAAS,QAAQ,CAAC;AAAA,EAC7E;AACA,SAAO,QAAQ,KAAK;AACtB;AAEA,SAAS,aAAc,SAAS,MAAM,MAAM,OAAO;AACjD,UAAQ,OAAO,IAAI,IAAI,KAAK;AAAA,IAC1B,gBAAgB,SAAS,OAAO,IAAI;AAAA,IACpC,gBAAgB,SAAS,IAAI,IAAI;AAAA,EACnC;AACF;AAaA,SAAS,cAAe,WAAW,YAAY,YAAY;AACzD,QAAM,SAAS,CAAC;AAChB,MAAI,YAAY;AAChB,QAAM,YAAY,UAAU;AAC5B,QAAM,aAAa,CAAC,CAAC;AAErB,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,YAAY,OAAO,QAAQ,CAAC;AAClC,UAAM,QAAQ,UAAU,CAAC;AACzB,QAAI,IAAI;AACR,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,OAAO,MAAM,CAAC;AACpB,UAAI,qBAAqB,WAAW,CAAC;AACrC,wBAAkB,MAAM,QAAQ,GAAG,CAAC;AACpC,WAAK,KAAK;AACV,WAAK,QAAQ,KAAK,KAAK,MAAM,IAAI;AACjC,YAAM,aAAa,KAAK,MAAM;AAC9B,mBAAa,YAAY,GAAG,KAAK,SAAS,aAAa,UAAU;AAAA,IACnE;AACA,gBAAa,UAAU,SAAS,YAAa,UAAU,SAAS;AAAA,EAClE;AAEA,mBAAiB,QAAS,YAAY,YAAa,YAAY,SAAS;AAExE,QAAM,cAAc,CAAC;AACrB,QAAM,aAAa,CAAC,CAAC;AAErB,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,QAAI,IAAI;AACR,QAAI;AACJ,UAAM,mBAAmB,KAAK,IAAI,WAAW,OAAO,CAAC,EAAE,MAAM;AAC7D,WAAO,IAAI,kBAAkB;AAC3B,aAAO,OAAO,CAAC,EAAE,CAAC;AAClB,UAAI,MAAM;AACR,YAAI,CAAC,KAAK,UAAU;AAClB,cAAI,YAAY;AAChB,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,kBAAM,OAAO,KAAK,MAAM,CAAC;AACzB,kBAAM,aAAa,WAAW,CAAC,IAAI;AACnC,wBAAY,UAAU,KAAK,YAAY,UAAU,KAAK,IAAI,OAAO,WAAW,CAAC,CAAC,IAAI;AAClF,wBAAa,KAAK,SAAS,YAAa,KAAK,SAAS;AAAA,UACxD;AACA,uBAAa,YAAY,GAAG,KAAK,SAAS,YAAY,UAAU;AAChE,eAAK,WAAW;AAAA,QAClB;AACA,aAAK,KAAK;AAAA,MACZ,OAAO;AACL,cAAM,aAAa,WAAW,CAAC;AAC/B,oBAAY,UAAU,IAAK,YAAY,UAAU,KAAK;AACtD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,YAAY,KAAK,IAAI;AAC9B;AAOA,SAAS,gBAAiB,MAAM,MAAM,SAAS,eAAe;AAC5D,UAAQ,aAAa;AACvB;AAOA,SAAS,UAAW,MAAM,MAAM,SAAS,eAAe;AACtD,UAAQ,wBAAwB;AAClC;AAOA,SAAS,qBAAsB,MAAM,MAAM,SAAS,eAAe;AACjE,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,UAAQ,UAAU,IAAI,OAAO,cAAc,UAAU,QAAQ,QAAQ,YAAY,EAAE,CAAC;AACpF,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,gBAAiB,MAAM,MAAM,SAAS,eAAe;AAC5D,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,UAAW,MAAM,MAAM,SAAS,eAAe;AACtD,UAAQ,UAAU;AAAA,IAChB,OAAO;AAAA,IACP,mBAAmB,cAAc,qBAAqB;AAAA,EACxD,CAAC;AACD,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,cAAe,MAAM,MAAM,SAAS,eAAe;AAC1D,UAAQ,UAAU,EAAE,mBAAmB,cAAc,qBAAqB,EAAE,CAAC;AAC7E,MAAI,cAAc,cAAc,OAAO;AACrC,YAAQ,kBAAkB,SAAO,IAAI,YAAY,CAAC;AAClD,SAAK,KAAK,UAAU,OAAO;AAC3B,YAAQ,iBAAiB;AAAA,EAC3B,OAAO;AACL,SAAK,KAAK,UAAU,OAAO;AAAA,EAC7B;AACA,UAAQ,WAAW,EAAE,oBAAoB,cAAc,sBAAsB,EAAE,CAAC;AAClF;AAOA,SAAS,iBAAkB,MAAM,MAAM,SAAS,eAAe;AAC7D,UAAQ,UAAU;AAAA,IAChB,mBAAmB,cAAc,qBAAqB;AAAA,IACtD,oBAAoB;AAAA,EACtB,CAAC;AACD,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,WAAW;AAAA,IACjB,oBAAoB,cAAc,sBAAsB;AAAA,IACxD,gBAAgB,UAAS,cAAc,mBAAmB,QAAS,cAAc,KAAK,IAAI,IAAI,KAC3F,MAAM,IAAI,EACV,IAAI,UAAQ,OAAO,IAAI,EACvB,KAAK,IAAI;AAAA,EACd,CAAC;AACH;AAEA,SAAS,aAAc,KAAK,UAAU;AACpC,MAAI,CAAC,UAAU;AAAE,WAAO;AAAA,EAAK;AAE7B,QAAM,MAAO,OAAO,SAAS,CAAC,MAAM,WAChC,SAAS,CAAC,IACV;AACJ,QAAM,MAAO,OAAO,SAAS,CAAC,MAAM,WAChC,SAAS,CAAC,IACV;AACJ,SAAO,MAAM,MAAM;AACrB;AAEA,SAAS,YAAa,MAAM,UAAU,SAAS,UAAU,MAAM;AAC7D,QAAM,eAAgB,OAAO,aAAa,aACtC,SAAS,MAAM,UAAU,IAAI,IAC7B;AACJ,SAAQ,aAAa,CAAC,MAAM,OAAO,UAC/B,iBAAiB,SAAS,GAAG,IAAI,eACjC;AACN;AAOA,SAAS,YAAa,MAAM,MAAM,SAAS,eAAe;AACxD,QAAM,UAAU,KAAK,WAAW,CAAC;AACjC,QAAM,MAAO,QAAQ,MACjB,QAAQ,MACR;AACJ,QAAM,MAAO,CAAC,QAAQ,MAClB,KACA,YAAY,QAAQ,KAAK,cAAc,aAAa,cAAc,SAAS,QAAQ,UAAU,IAAI;AACrG,QAAM,OAAQ,CAAC,MACX,MACC,CAAC,MACA,aAAa,KAAK,cAAc,YAAY,IAC5C,MAAM,MAAM,aAAa,KAAK,cAAc,YAAY;AAE9D,UAAQ,UAAU,MAAM,EAAE,iBAAiB,KAAK,CAAC;AACnD;AA8BA,SAAS,aAAc,MAAM,MAAM,SAAS,eAAe;AACzD,WAAS,UAAW;AAClB,QAAI,cAAc,YAAY;AAAE,aAAO;AAAA,IAAI;AAC3C,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ,MAAM;AAAE,aAAO;AAAA,IAAI;AACtD,QAAIQ,QAAO,KAAK,QAAQ,KAAK,QAAQ,YAAY,EAAE;AACnD,QAAI,cAAc,eAAeA,MAAK,CAAC,MAAM,KAAK;AAAE,aAAO;AAAA,IAAI;AAC/D,IAAAA,QAAO,YAAYA,OAAM,cAAc,aAAa,cAAc,SAAS,QAAQ,UAAU,IAAI;AACjG,WAAOA;AAAA,EACT;AACA,QAAM,OAAO,QAAQ;AACrB,MAAI,CAAC,MAAM;AACT,SAAK,KAAK,UAAU,OAAO;AAAA,EAC7B,OAAO;AACL,QAAI,OAAO;AACX,YAAQ;AAAA,MACN,SAAO;AACL,YAAI,KAAK;AAAE,kBAAQ;AAAA,QAAK;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,KAAK,UAAU,OAAO;AAC3B,YAAQ,iBAAiB;AAEzB,UAAM,eAAe,cAAc,4BAA4B,SAAS;AACxE,QAAI,CAAC,cAAc;AACjB,cAAQ;AAAA,QACL,CAAC,OACE,OACA,MAAM,aAAa,MAAM,cAAc,YAAY;AAAA,QACvD,EAAE,iBAAiB,KAAK;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF;AASA,SAAS,WAAY,MAAM,MAAM,SAAS,eAAe,oBAAoB;AAC3E,QAAM,eAAe,IAAI,MAAM,CAAC,UAAU,MAAM,CAAC,MAAM;AAIvD,MAAI,kBAAkB;AACtB,QAAM,aAAa,KAAK,YAAY,CAAC,GAElC,OAAO,WAAS,MAAM,SAAS,UAAU,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,EAClE,IAAI,SAAU,OAAO;AACpB,QAAI,MAAM,SAAS,MAAM;AACvB,aAAO,EAAE,MAAM,OAAO,QAAQ,GAAG;AAAA,IACnC;AACA,UAAM,SAAU,eACZ,mBAAmB,EAAE,UAAU,IAC/B,mBAAmB;AACvB,QAAI,OAAO,SAAS,iBAAiB;AAAE,wBAAkB,OAAO;AAAA,IAAQ;AACxE,WAAO,EAAE,MAAM,OAAO,OAAe;AAAA,EACvC,CAAC;AACH,MAAI,CAAC,UAAU,QAAQ;AAAE;AAAA,EAAQ;AAEjC,UAAQ,SAAS;AAAA,IACf,oBAAoB;AAAA,IACpB,mBAAmB,eAAe,IAAK,cAAc,qBAAqB;AAAA,IAC1E;AAAA,IACA,aAAa;AAAA,EACf,CAAC;AAED,aAAW,EAAE,MAAM,OAAO,KAAK,WAAW;AACxC,YAAQ,aAAa,EAAE,OAAe,CAAC;AACvC,SAAK,CAAC,IAAI,GAAG,OAAO;AACpB,YAAQ,cAAc;AAAA,EACxB;AAEA,UAAQ,UAAU,EAAE,oBAAoB,eAAe,IAAK,cAAc,sBAAsB,EAAG,CAAC;AACtG;AAOA,SAAS,oBAAqB,MAAM,MAAM,SAAS,eAAe;AAChE,QAAM,SAAS,cAAc,cAAc;AAC3C,SAAO,WAAW,MAAM,MAAM,SAAS,eAAe,MAAM,MAAM;AACpE;AAOA,SAAS,kBAAmB,MAAM,MAAM,SAAS,eAAe;AAC9D,MAAI,YAAY,OAAO,KAAK,QAAQ,SAAS,GAAG;AAChD,QAAM,gBAAgB,4BAA4B,KAAK,QAAQ,IAAI;AACnE,QAAM,qBAAqB,MAAM,MAAM,cAAc,WAAW,IAAI;AACpE,SAAO,WAAW,MAAM,MAAM,SAAS,eAAe,kBAAkB;AAC1E;AAQA,SAAS,4BAA6B,SAAS,KAAK;AAClD,UAAQ,QAAQ;AAAA,IACd,KAAK;AAAK,aAAO,CAAC,MAAM,uBAAuB,GAAG,GAAG;AAAA,IACrD,KAAK;AAAK,aAAO,CAAC,MAAM,uBAAuB,GAAG,GAAG;AAAA,IACrD,KAAK;AAAK,aAAO,CAAC,MAAM,cAAc,CAAC,EAAE,YAAY;AAAA,IACrD,KAAK;AAAK,aAAO,CAAC,MAAM,cAAc,CAAC;AAAA,IACvC,KAAK;AAAA,IACL;AAAS,aAAO,CAAC,MAAO,EAAG,SAAS;AAAA,EACtC;AACF;AASA,SAAS,mBAAoB,WAAW;AACtC,QAAM,UAAU,CAAC;AACjB,QAAM,MAAM,CAAC;AACb,aAAW,YAAY,WAAW;AAChC,QAAI,SAAS,WAAW,GAAG,GAAG;AAC5B,cAAQ,KAAK,SAAS,UAAU,CAAC,CAAC;AAAA,IACpC,WAAW,SAAS,WAAW,GAAG,GAAG;AACnC,UAAI,KAAK,SAAS,UAAU,CAAC,CAAC;AAAA,IAChC;AAAA,EACF;AACA,SAAO,EAAE,SAAkB,IAAS;AACtC;AAEA,SAAS,YAAa,MAAM,QAAQ;AAClC,MAAI,WAAW,MAAM;AAAE,WAAO;AAAA,EAAM;AACpC,MAAI,CAAC,MAAM;AAAE,WAAO;AAAA,EAAO;AAE3B,QAAM,EAAE,SAAS,IAAI,IAAI,mBAAmB,MAAM;AAClD,QAAM,eAAe,KAAK,OAAO,KAAK,IAAI,MAAM,GAAG;AACnD,QAAM,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG;AAE5C,SAAO,YAAY,KAAK,OAAK,QAAQ,SAAS,CAAC,CAAC,KAAK,QAAQ,KAAK,OAAK,IAAI,SAAS,CAAC,CAAC;AACxF;AAOA,SAAS,YAAa,MAAM,MAAM,SAAS,eAAe;AACxD,SAAO,YAAY,KAAK,SAAS,QAAQ,QAAQ,MAAM,IACnD,gBAAgB,MAAM,MAAM,SAAS,aAAa,IAClD,YAAY,MAAM,MAAM,SAAS,aAAa;AACpD;AAEA,SAAS,YAAa,MAAM,MAAM,SAAS,eAAe;AACxD,UAAQ,UAAU,EAAE,mBAAmB,cAAc,kBAAkB,CAAC;AACxE,OAAK,KAAK,UAAU,OAAO;AAC3B,UAAQ,WAAW,EAAE,oBAAoB,cAAc,mBAAmB,CAAC;AAC7E;AAOA,SAAS,gBAAiB,MAAM,MAAM,SAAS,eAAe;AAC5D,UAAQ,UAAU;AAClB,OAAK,SAAS,QAAQ,SAAS;AAC/B,UAAQ,WAAW;AAAA,IACjB,eAAe,CAAC,SAAS,cAAc,MAAM,cAAc,cAAc,GAAG,cAAc,cAAc,CAAC;AAAA,IACzG,mBAAmB,cAAc;AAAA,IACjC,oBAAoB,cAAc;AAAA,EACpC,CAAC;AAED,WAAS,WAAY,UAAU;AAC7B,UAAM,UAAU,CAAC,IAAI,UAAU,CAAC,WAAW,SAAS,CAAC,KAAK;AAC1D,UAAM,UAAU,CAAC,IAAI,UAAU,CAAC,WAAW,SAAS,CAAC,KAAK;AAC1D,YAAQ,cAAc,EAAE,gBAAgB,cAAc,eAAe,CAAC;AACtE,SAAK,SAAS,UAAU,OAAO;AAC/B,YAAQ,eAAe,EAAE,SAAkB,QAAiB,CAAC;AAAA,EAC/D;AAEA,WAAS,UAAWC,OAAM;AACxB,QAAIA,MAAK,SAAS,OAAO;AAAE;AAAA,IAAQ;AAEnC,UAAM,mBAAoB,cAAc,yBAAyB,QAC7D,CAAC,aAAa;AACd,cAAQ,kBAAkB,SAAO,IAAI,YAAY,CAAC;AAClD,iBAAW,QAAQ;AACnB,cAAQ,iBAAiB;AAAA,IAC3B,IACE;AAEJ,YAAQA,MAAK,MAAM;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,QAAAA,MAAK,SAAS,QAAQ,SAAS;AAC/B;AAAA,MAEF,KAAK,MAAM;AACT,gBAAQ,aAAa;AACrB,mBAAW,aAAaA,MAAK,UAAU;AACrC,cAAI,UAAU,SAAS,OAAO;AAAE;AAAA,UAAU;AAC1C,kBAAQ,UAAU,MAAM;AAAA,YACtB,KAAK,MAAM;AACT,+BAAiB,SAAS;AAC1B;AAAA,YACF;AAAA,YACA,KAAK,MAAM;AACT,yBAAW,SAAS;AACpB;AAAA,YACF;AAAA,UAEF;AAAA,QACF;AACA,gBAAQ,cAAc;AACtB;AAAA,MACF;AAAA,IAEF;AAAA,EACF;AACF;AAEA,IAAI,iBAA8B,OAAO,OAAO;AAAA,EAC9C,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,OAAO;AAAA,EACP,eAAe;AAAA,EACf,KAAK;AACP,CAAC;AAUD,IAAM,kBAAkB;AAAA,EACtB,cAAc;AAAA,IACZ,WAAW,CAAE,MAAO;AAAA,IACpB,SAAS;AAAA;AAAA,IACT,oBAAoB;AAAA,EACtB;AAAA,EACA,gBAAgB;AAAA,EAChB,kBAAkB,CAAC;AAAA,EACnB,YAAY,CAAC;AAAA,EACb,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,UAAU;AAAA,IACV,gBAAiB,KAAK;AAAA;AAAA,EACxB;AAAA,EACA,eAAe;AAAA,IACb,kBAAkB;AAAA,IAClB,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA,kBAAkB;AAAA,EAClB,WAAW;AAAA,IACT,EAAE,UAAU,KAAK,QAAQ,SAAS;AAAA,IAClC;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,SAAS;AAAA,QACT,0BAA0B;AAAA,QAC1B,YAAY;AAAA,QACZ,cAAc,CAAC,KAAK,GAAG;AAAA,QACvB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,EAAE,UAAU,WAAW,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IACjG,EAAE,UAAU,SAAS,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC/F;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,gBAAgB,KAAK;AAAA,IAC/E;AAAA,IACA,EAAE,UAAU,MAAM,QAAQ,YAAY;AAAA,IACtC,EAAE,UAAU,OAAO,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC7F,EAAE,UAAU,UAAU,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAChG,EAAE,UAAU,QAAQ,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC9F,EAAE,UAAU,MAAM,QAAQ,WAAW,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,WAAW,KAAK,EAAE;AAAA,IAC/G,EAAE,UAAU,MAAM,QAAQ,WAAW,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,WAAW,KAAK,EAAE;AAAA,IAC/G,EAAE,UAAU,MAAM,QAAQ,WAAW,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,WAAW,KAAK,EAAE;AAAA,IAC/G,EAAE,UAAU,MAAM,QAAQ,WAAW,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,WAAW,KAAK,EAAE;AAAA,IAC/G,EAAE,UAAU,MAAM,QAAQ,WAAW,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,WAAW,KAAK,EAAE;AAAA,IAC/G,EAAE,UAAU,MAAM,QAAQ,WAAW,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,WAAW,KAAK,EAAE;AAAA,IAC/G,EAAE,UAAU,UAAU,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAChG;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS,EAAE,mBAAmB,GAAG,QAAQ,QAAW,oBAAoB,EAAE;AAAA,IAC5E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS,EAAE,SAAS,MAAM,cAAc,CAAC,KAAK,GAAG,EAAE;AAAA,IACrD;AAAA,IACA,EAAE,UAAU,QAAQ,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC9F,EAAE,UAAU,OAAO,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC7F;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE;AAAA,IACzD;AAAA,IACA,EAAE,UAAU,KAAK,QAAQ,aAAa,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC/F,EAAE,UAAU,OAAO,QAAQ,OAAO,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IAC3F,EAAE,UAAU,WAAW,QAAQ,SAAS,SAAS,EAAE,mBAAmB,GAAG,oBAAoB,EAAE,EAAE;AAAA,IACjG;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,IACF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS,EAAE,YAAY,OAAO,mBAAmB,GAAG,oBAAoB,EAAE;AAAA,IAC5E;AAAA,IACA,EAAE,UAAU,OAAO,QAAQ,MAAM;AAAA,EACnC;AAAA,EACA,QAAQ,CAAC;AAAA;AAAA,EACT,sBAAsB;AAAA,EACtB,UAAU;AACZ;AAEA,IAAM,cAAc,CAAC,KAAK,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,GAAG;AAC1D,IAAM,iBAAiB,CAAC,KAAK,KAAK,YAAY,CAAC,GAAG,GAAG;AACrD,IAAM,iBAAiB,CAAC,KAAK,KAAK,YAC/B,IAAI,KAAK,CAAAL,OAAK,OAAOA,OAAM,QAAQ,IAChC,YAAY,KAAK,GAAG,IACpB,eAAe,KAAK,GAAG;AAW7B,SAAS,QAAS,UAAU,CAAC,GAAG;AAC9B,gBAAU,iBAAAF;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,aAAa,CAAC,QAAU,QAAQ,cAAe,iBAAiB;AAAA,IAClE;AAAA,EACF;AACA,UAAQ,aAAa,OAAO,OAAO,CAAC,GAAG,mBAAmB,gBAAgB,QAAQ,UAAU;AAC5F,UAAQ,YAAY,0BAA0B,QAAQ,WAAY,CAAAE,OAAKA,GAAE,QAAS;AAElF,0BAAwB,OAAO;AAE/B,SAAO,UAAU,OAAO;AAC1B;AAkBA,SAAS,QAAS,MAAM,UAAU,CAAC,GAAG,WAAW,QAAW;AAC1D,SAAO,QAAQ,OAAO,EAAE,MAAM,QAAQ;AACxC;AAQA,SAAS,wBAAyB,SAAS;AACzC,MAAI,QAAQ,MAAM;AAChB,UAAM,iBAAiB,OAAO,QAAQ,QAAQ,IAAI,EAAE;AAAA,MAClD,CAAC,CAAC,UAAU,UAAU,OAAO,EAAE,GAAG,YAAY,UAAU,YAAY,IAAI;AAAA,IAC1E;AACA,YAAQ,UAAU,KAAK,GAAG,cAAc;AACxC,YAAQ,YAAY,0BAA0B,QAAQ,WAAY,CAAAA,OAAKA,GAAE,QAAS;AAAA,EACpF;AAEA,WAAS,IAAK,KAAK,MAAM,OAAO;AAC9B,UAAM,WAAW,KAAK,IAAI;AAC1B,eAAW,OAAO,MAAM;AACtB,UAAI,SAAS,IAAI,GAAG;AACpB,UAAI,CAAC,QAAQ;AACX,iBAAS,CAAC;AACV,YAAI,GAAG,IAAI;AAAA,MACb;AACA,YAAM;AAAA,IACR;AACA,QAAI,QAAQ,IAAI;AAAA,EAClB;AAEA,MAAI,QAAQ,aAAa,GAAG;AAC1B,UAAM,cAAc,QAAQ,aAAa;AACzC;AAAA,MACE;AAAA,MACA,CAAC,gBAAgB,WAAW;AAAA,MAC3B,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AAAA,IAC1D;AAAA,EACF;AACA,MAAI,QAAQ,oBAAoB,MAAM,QAAW;AAC/C,QAAI,SAAS,CAAC,gBAAgB,oBAAoB,GAAG,QAAQ,oBAAoB,CAAC;AAAA,EACpF;AAEA,aAAW,cAAc,QAAQ,WAAW;AAC1C,QAAI,WAAW,WAAW,YAAY,IAAI,YAAY,CAAC,WAAW,gBAAgB,CAAC,GAAG;AACpF,UAAI,YAAY,CAAC,WAAW,cAAc,GAAG,KAAK;AAAA,IACpD;AAAA,EACF;AACF;", "names": ["ElementType", "Text", "Comment", "name", "_a", "CDATA", "isTag", "Text", "Comment", "isTag", "CDATA", "name", "Text", "Comment", "CDATA", "n", "o", "e", "s", "r", "l", "t", "c", "r", "r", "r", "r", "acc", "t", "name", "s", "c", "left", "right", "token", "r", "nl", "n", "ast", "name", "isTag", "c", "c", "_a", "CharCodes", "BinTrieFlags", "EntityDecoderState", "DecodingMode", "_a", "CharCodes", "State", "c", "isNumber", "QuoteType", "_a", "name", "c", "map", "escape", "EntityLevel", "EncodingMode", "_a", "_a", "DocumentPosition", "n", "c", "map", "merge", "tableToString", "s", "recursiveWalk", "dom", "r", "href", "elem"]}
{"version": 3, "sources": ["../../react-fast-compare/index.js", "../../warning/warning.js", "../../react-popper/lib/esm/Popper.js", "../../react-popper/lib/esm/Manager.js", "../../react-popper/lib/esm/utils.js", "../../react-popper/lib/esm/usePopper.js", "../../react-popper/lib/esm/Reference.js"], "sourcesContent": ["/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n", "import * as React from 'react';\nimport { ManagerReferenceNodeContext } from './Manager';\nimport { unwrapArray, setRef } from './utils';\nimport { usePopper } from './usePopper';\n\nvar NOOP = function NOOP() {\n  return void 0;\n};\n\nvar NOOP_PROMISE = function NOOP_PROMISE() {\n  return Promise.resolve(null);\n};\n\nvar EMPTY_MODIFIERS = [];\nexport function Popper(_ref) {\n  var _ref$placement = _ref.placement,\n      placement = _ref$placement === void 0 ? 'bottom' : _ref$placement,\n      _ref$strategy = _ref.strategy,\n      strategy = _ref$strategy === void 0 ? 'absolute' : _ref$strategy,\n      _ref$modifiers = _ref.modifiers,\n      modifiers = _ref$modifiers === void 0 ? EMPTY_MODIFIERS : _ref$modifiers,\n      referenceElement = _ref.referenceElement,\n      onFirstUpdate = _ref.onFirstUpdate,\n      innerRef = _ref.innerRef,\n      children = _ref.children;\n  var referenceNode = React.useContext(ManagerReferenceNodeContext);\n\n  var _React$useState = React.useState(null),\n      popperElement = _React$useState[0],\n      setPopperElement = _React$useState[1];\n\n  var _React$useState2 = React.useState(null),\n      arrowElement = _React$useState2[0],\n      setArrowElement = _React$useState2[1];\n\n  React.useEffect(function () {\n    setRef(innerRef, popperElement);\n  }, [innerRef, popperElement]);\n  var options = React.useMemo(function () {\n    return {\n      placement: placement,\n      strategy: strategy,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: [].concat(modifiers, [{\n        name: 'arrow',\n        enabled: arrowElement != null,\n        options: {\n          element: arrowElement\n        }\n      }])\n    };\n  }, [placement, strategy, onFirstUpdate, modifiers, arrowElement]);\n\n  var _usePopper = usePopper(referenceElement || referenceNode, popperElement, options),\n      state = _usePopper.state,\n      styles = _usePopper.styles,\n      forceUpdate = _usePopper.forceUpdate,\n      update = _usePopper.update;\n\n  var childrenProps = React.useMemo(function () {\n    return {\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : placement,\n      hasPopperEscaped: state && state.modifiersData.hide ? state.modifiersData.hide.hasPopperEscaped : null,\n      isReferenceHidden: state && state.modifiersData.hide ? state.modifiersData.hide.isReferenceHidden : null,\n      arrowProps: {\n        style: styles.arrow,\n        ref: setArrowElement\n      },\n      forceUpdate: forceUpdate || NOOP,\n      update: update || NOOP_PROMISE\n    };\n  }, [setPopperElement, setArrowElement, placement, state, styles, update, forceUpdate]);\n  return unwrapArray(children)(childrenProps);\n}", "import * as React from 'react';\nexport var ManagerReferenceNodeContext = React.createContext();\nexport var ManagerReferenceNodeSetterContext = React.createContext();\nexport function Manager(_ref) {\n  var children = _ref.children;\n\n  var _React$useState = React.useState(null),\n      referenceNode = _React$useState[0],\n      setReferenceNode = _React$useState[1];\n\n  var hasUnmounted = React.useRef(false);\n  React.useEffect(function () {\n    return function () {\n      hasUnmounted.current = true;\n    };\n  }, []);\n  var handleSetReferenceNode = React.useCallback(function (node) {\n    if (!hasUnmounted.current) {\n      setReferenceNode(node);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ManagerReferenceNodeContext.Provider, {\n    value: referenceNode\n  }, /*#__PURE__*/React.createElement(ManagerReferenceNodeSetterContext.Provider, {\n    value: handleSetReferenceNode\n  }, children));\n}", "import * as React from 'react';\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === 'function') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === 'function') {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};\n/**\n * Simple ponyfill for Object.fromEntries\n */\n\nexport var fromEntries = function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var key = _ref[0],\n        value = _ref[1];\n    acc[key] = value;\n    return acc;\n  }, {});\n};\n/**\n * Small wrapper around `useLayoutEffect` to get rid of the warning on SSR envs\n */\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && window.document && window.document.createElement ? React.useLayoutEffect : React.useEffect;", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createPopper as defaultCreatePopper } from '@popperjs/core';\nimport isEqual from 'react-fast-compare';\nimport { fromEntries, useIsomorphicLayoutEffect } from './utils';\nvar EMPTY_MODIFIERS = [];\nexport var usePopper = function usePopper(referenceElement, popperElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var prevOptions = React.useRef(null);\n  var optionsWithDefaults = {\n    onFirstUpdate: options.onFirstUpdate,\n    placement: options.placement || 'bottom',\n    strategy: options.strategy || 'absolute',\n    modifiers: options.modifiers || EMPTY_MODIFIERS\n  };\n\n  var _React$useState = React.useState({\n    styles: {\n      popper: {\n        position: optionsWithDefaults.strategy,\n        left: '0',\n        top: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      }\n    },\n    attributes: {}\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var updateStateModifier = React.useMemo(function () {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'write',\n      fn: function fn(_ref) {\n        var state = _ref.state;\n        var elements = Object.keys(state.elements);\n        ReactDOM.flushSync(function () {\n          setState({\n            styles: fromEntries(elements.map(function (element) {\n              return [element, state.styles[element] || {}];\n            })),\n            attributes: fromEntries(elements.map(function (element) {\n              return [element, state.attributes[element]];\n            }))\n          });\n        });\n      },\n      requires: ['computeStyles']\n    };\n  }, []);\n  var popperOptions = React.useMemo(function () {\n    var newOptions = {\n      onFirstUpdate: optionsWithDefaults.onFirstUpdate,\n      placement: optionsWithDefaults.placement,\n      strategy: optionsWithDefaults.strategy,\n      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {\n        name: 'applyStyles',\n        enabled: false\n      }])\n    };\n\n    if (isEqual(prevOptions.current, newOptions)) {\n      return prevOptions.current || newOptions;\n    } else {\n      prevOptions.current = newOptions;\n      return newOptions;\n    }\n  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);\n  var popperInstanceRef = React.useRef();\n  useIsomorphicLayoutEffect(function () {\n    if (popperInstanceRef.current) {\n      popperInstanceRef.current.setOptions(popperOptions);\n    }\n  }, [popperOptions]);\n  useIsomorphicLayoutEffect(function () {\n    if (referenceElement == null || popperElement == null) {\n      return;\n    }\n\n    var createPopper = options.createPopper || defaultCreatePopper;\n    var popperInstance = createPopper(referenceElement, popperElement, popperOptions);\n    popperInstanceRef.current = popperInstance;\n    return function () {\n      popperInstance.destroy();\n      popperInstanceRef.current = null;\n    };\n  }, [referenceElement, popperElement, options.createPopper]);\n  return {\n    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,\n    styles: state.styles,\n    attributes: state.attributes,\n    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,\n    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null\n  };\n};", "import * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\nexport function Reference(_ref) {\n  var children = _ref.children,\n      innerRef = _ref.innerRef;\n  var setReferenceNode = React.useContext(ManagerReferenceNodeSetterContext);\n  var refHandler = React.useCallback(function (node) {\n    setRef(innerRef, node);\n    safeInvoke(setReferenceNode, node);\n  }, [innerRef, setReferenceNode]); // ran on unmount\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  React.useEffect(function () {\n    return function () {\n      return setRef(innerRef, null);\n    };\n  }, []);\n  React.useEffect(function () {\n    warning(Boolean(setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n  }, [setReferenceNode]);\n  return unwrapArray(children)({\n    ref: refHand<PERSON>\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAEA,QAAI,iBAAiB,OAAO,YAAY;AACxC,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,iBAAiB,OAAO,gBAAgB,cAAc,CAAC,CAAC,YAAY;AAIxE,aAAS,MAAM,GAAG,GAAG;AAEnB,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAsBA,YAAI;AACJ,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAG,QAAO;AACjC,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,EAAG,QAAO;AACpD,iBAAO;AAAA,QACT;AAEA,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AACpE,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAG,QAAO;AAC5B,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAK5E,YAAI,EAAE,YAAY,OAAO,UAAU,WAAW,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,YAAY,WAAY,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AACnJ,YAAI,EAAE,aAAa,OAAO,UAAU,YAAY,OAAO,EAAE,aAAa,cAAc,OAAO,EAAE,aAAa,WAAY,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAGzJ,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAKhE,YAAI,kBAAkB,aAAa,QAAS,QAAO;AAGnD,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,eAAK,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAM,UAAU,EAAE,UAAU;AASlF;AAAA,UACF;AAGA,cAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAG,QAAO;AAAA,QAC7C;AAIA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,MAAM;AAAA,IAC1B;AAGA,WAAO,UAAU,SAASA,SAAQ,GAAG,GAAG;AACtC,UAAI;AACF,eAAO,MAAM,GAAG,CAAC;AAAA,MACnB,SAAS,OAAO;AACd,aAAM,MAAM,WAAW,IAAI,MAAM,kBAAkB,GAAI;AAMrD,kBAAQ,KAAK,gDAAgD;AAC7D,iBAAO;AAAA,QACT;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;AC1IA;AAAA;AAAA;AAgBA,QAAI,UAAU;AAEd,QAAIC,WAAU,WAAW;AAAA,IAAC;AAE1B,QAAI,SAAS;AACP,qBAAe,SAASC,cAAa,QAAQ,MAAM;AACrD,YAAI,MAAM,UAAU;AACpB,eAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,iBAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAClC,eAAK,MAAM,CAAC,IAAI,UAAU,GAAG;AAAA,QAC/B;AACA,YAAI,WAAW;AACf,YAAI,UAAU,cACZ,OAAO,QAAQ,OAAO,WAAW;AAC/B,iBAAO,KAAK,UAAU;AAAA,QACxB,CAAC;AACH,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAEA,MAAAD,WAAU,SAAS,WAAW,QAAQ,MAAM;AAC1C,YAAI,MAAM,UAAU;AACpB,eAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,iBAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAClC,eAAK,MAAM,CAAC,IAAI,UAAU,GAAG;AAAA,QAC/B;AACA,YAAI,WAAW,QAAW;AACxB,gBAAM,IAAI;AAAA,YACN;AAAA,UAEJ;AAAA,QACF;AACA,YAAI,CAAC,WAAW;AACd,uBAAa,MAAM,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAtCM;AAwCN,WAAO,UAAUA;AAAA;AAAA;;;AC7DjB,IAAAE,SAAuB;;;ACAvB,YAAuB;AAChB,IAAI,8BAAoC,oBAAc;AACtD,IAAI,oCAA0C,oBAAc;AAC5D,SAAS,QAAQ,MAAM;AAC5B,MAAI,WAAW,KAAK;AAEpB,MAAI,kBAAwB,eAAS,IAAI,GACrC,gBAAgB,gBAAgB,CAAC,GACjC,mBAAmB,gBAAgB,CAAC;AAExC,MAAI,eAAqB,aAAO,KAAK;AACrC,EAAM,gBAAU,WAAY;AAC1B,WAAO,WAAY;AACjB,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,yBAA+B,kBAAY,SAAU,MAAM;AAC7D,QAAI,CAAC,aAAa,SAAS;AACzB,uBAAiB,IAAI;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAA0B,oBAAc,4BAA4B,UAAU;AAAA,IAC5E,OAAO;AAAA,EACT,GAAsB,oBAAc,kCAAkC,UAAU;AAAA,IAC9E,OAAO;AAAA,EACT,GAAG,QAAQ,CAAC;AACd;;;AC1BA,IAAAC,SAAuB;AAMhB,IAAI,cAAc,SAASC,aAAY,KAAK;AACjD,SAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI;AACvC;AAMO,IAAI,aAAa,SAASC,YAAW,IAAI;AAC9C,MAAI,OAAO,OAAO,YAAY;AAC5B,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,GAAG,MAAM,QAAQ,IAAI;AAAA,EAC9B;AACF;AAKO,IAAI,SAAS,SAASC,QAAO,KAAK,MAAM;AAE7C,MAAI,OAAO,QAAQ,YAAY;AAC7B,WAAO,WAAW,KAAK,IAAI;AAAA,EAC7B,WACS,OAAO,MAAM;AAClB,QAAI,UAAU;AAAA,EAChB;AACJ;AAKO,IAAI,cAAc,SAASC,aAAY,SAAS;AACrD,SAAO,QAAQ,OAAO,SAAU,KAAK,MAAM;AACzC,QAAI,MAAM,KAAK,CAAC,GACZ,QAAQ,KAAK,CAAC;AAClB,QAAI,GAAG,IAAI;AACX,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAKO,IAAI,4BAA4B,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS,gBAAsB,yBAAwB;;;ACpDzJ,IAAAC,SAAuB;AACvB,eAA0B;AAE1B,gCAAoB;AAEpB,IAAI,kBAAkB,CAAC;AAChB,IAAI,YAAY,SAASC,WAAU,kBAAkB,eAAe,SAAS;AAClF,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,cAAoB,cAAO,IAAI;AACnC,MAAI,sBAAsB;AAAA,IACxB,eAAe,QAAQ;AAAA,IACvB,WAAW,QAAQ,aAAa;AAAA,IAChC,UAAU,QAAQ,YAAY;AAAA,IAC9B,WAAW,QAAQ,aAAa;AAAA,EAClC;AAEA,MAAI,kBAAwB,gBAAS;AAAA,IACnC,QAAQ;AAAA,MACN,QAAQ;AAAA,QACN,UAAU,oBAAoB;AAAA,QAC9B,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY,CAAC;AAAA,EACf,CAAC,GACG,QAAQ,gBAAgB,CAAC,GACzB,WAAW,gBAAgB,CAAC;AAEhC,MAAI,sBAA4B,eAAQ,WAAY;AAClD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,IAAI,SAAS,GAAG,MAAM;AACpB,YAAIC,SAAQ,KAAK;AACjB,YAAI,WAAW,OAAO,KAAKA,OAAM,QAAQ;AACzC,QAAS,mBAAU,WAAY;AAC7B,mBAAS;AAAA,YACP,QAAQ,YAAY,SAAS,IAAI,SAAU,SAAS;AAClD,qBAAO,CAAC,SAASA,OAAM,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,YAC9C,CAAC,CAAC;AAAA,YACF,YAAY,YAAY,SAAS,IAAI,SAAU,SAAS;AACtD,qBAAO,CAAC,SAASA,OAAM,WAAW,OAAO,CAAC;AAAA,YAC5C,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,eAAQ,WAAY;AAC5C,QAAI,aAAa;AAAA,MACf,eAAe,oBAAoB;AAAA,MACnC,WAAW,oBAAoB;AAAA,MAC/B,UAAU,oBAAoB;AAAA,MAC9B,WAAW,CAAC,EAAE,OAAO,oBAAoB,WAAW,CAAC,qBAAqB;AAAA,QACxE,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC,CAAC;AAAA,IACJ;AAEA,YAAI,0BAAAC,SAAQ,YAAY,SAAS,UAAU,GAAG;AAC5C,aAAO,YAAY,WAAW;AAAA,IAChC,OAAO;AACL,kBAAY,UAAU;AACtB,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,oBAAoB,eAAe,oBAAoB,WAAW,oBAAoB,UAAU,oBAAoB,WAAW,mBAAmB,CAAC;AACvJ,MAAI,oBAA0B,cAAO;AACrC,4BAA0B,WAAY;AACpC,QAAI,kBAAkB,SAAS;AAC7B,wBAAkB,QAAQ,WAAW,aAAa;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,4BAA0B,WAAY;AACpC,QAAI,oBAAoB,QAAQ,iBAAiB,MAAM;AACrD;AAAA,IACF;AAEA,QAAIC,gBAAe,QAAQ,gBAAgB;AAC3C,QAAI,iBAAiBA,cAAa,kBAAkB,eAAe,aAAa;AAChF,sBAAkB,UAAU;AAC5B,WAAO,WAAY;AACjB,qBAAe,QAAQ;AACvB,wBAAkB,UAAU;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,kBAAkB,eAAe,QAAQ,YAAY,CAAC;AAC1D,SAAO;AAAA,IACL,OAAO,kBAAkB,UAAU,kBAAkB,QAAQ,QAAQ;AAAA,IACrE,QAAQ,MAAM;AAAA,IACd,YAAY,MAAM;AAAA,IAClB,QAAQ,kBAAkB,UAAU,kBAAkB,QAAQ,SAAS;AAAA,IACvE,aAAa,kBAAkB,UAAU,kBAAkB,QAAQ,cAAc;AAAA,EACnF;AACF;;;AHhGA,IAAI,OAAO,SAASC,QAAO;AACzB,SAAO;AACT;AAEA,IAAI,eAAe,SAASC,gBAAe;AACzC,SAAO,QAAQ,QAAQ,IAAI;AAC7B;AAEA,IAAIC,mBAAkB,CAAC;AAChB,SAAS,OAAO,MAAM;AAC3B,MAAI,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,WAAW,gBACnD,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,aAAa,eACnD,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAASA,mBAAkB,gBAC1D,mBAAmB,KAAK,kBACxB,gBAAgB,KAAK,eACrB,WAAW,KAAK,UAChB,WAAW,KAAK;AACpB,MAAI,gBAAsB,kBAAW,2BAA2B;AAEhE,MAAI,kBAAwB,gBAAS,IAAI,GACrC,gBAAgB,gBAAgB,CAAC,GACjC,mBAAmB,gBAAgB,CAAC;AAExC,MAAI,mBAAyB,gBAAS,IAAI,GACtC,eAAe,iBAAiB,CAAC,GACjC,kBAAkB,iBAAiB,CAAC;AAExC,EAAM,iBAAU,WAAY;AAC1B,WAAO,UAAU,aAAa;AAAA,EAChC,GAAG,CAAC,UAAU,aAAa,CAAC;AAC5B,MAAI,UAAgB,eAAQ,WAAY;AACtC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,CAAC,EAAE,OAAO,WAAW,CAAC;AAAA,QAC/B,MAAM;AAAA,QACN,SAAS,gBAAgB;AAAA,QACzB,SAAS;AAAA,UACP,SAAS;AAAA,QACX;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,WAAW,UAAU,eAAe,WAAW,YAAY,CAAC;AAEhE,MAAI,aAAa,UAAU,oBAAoB,eAAe,eAAe,OAAO,GAChF,QAAQ,WAAW,OACnB,SAAS,WAAW,QACpB,cAAc,WAAW,aACzB,SAAS,WAAW;AAExB,MAAI,gBAAsB,eAAQ,WAAY;AAC5C,WAAO;AAAA,MACL,KAAK;AAAA,MACL,OAAO,OAAO;AAAA,MACd,WAAW,QAAQ,MAAM,YAAY;AAAA,MACrC,kBAAkB,SAAS,MAAM,cAAc,OAAO,MAAM,cAAc,KAAK,mBAAmB;AAAA,MAClG,mBAAmB,SAAS,MAAM,cAAc,OAAO,MAAM,cAAc,KAAK,oBAAoB;AAAA,MACpG,YAAY;AAAA,QACV,OAAO,OAAO;AAAA,QACd,KAAK;AAAA,MACP;AAAA,MACA,aAAa,eAAe;AAAA,MAC5B,QAAQ,UAAU;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,kBAAkB,iBAAiB,WAAW,OAAO,QAAQ,QAAQ,WAAW,CAAC;AACrF,SAAO,YAAY,QAAQ,EAAE,aAAa;AAC5C;;;AI3EA,IAAAC,SAAuB;AACvB,qBAAoB;AAGb,SAAS,UAAU,MAAM;AAC9B,MAAI,WAAW,KAAK,UAChB,WAAW,KAAK;AACpB,MAAI,mBAAyB,kBAAW,iCAAiC;AACzE,MAAI,aAAmB,mBAAY,SAAU,MAAM;AACjD,WAAO,UAAU,IAAI;AACrB,eAAW,kBAAkB,IAAI;AAAA,EACnC,GAAG,CAAC,UAAU,gBAAgB,CAAC;AAG/B,EAAM,iBAAU,WAAY;AAC1B,WAAO,WAAY;AACjB,aAAO,OAAO,UAAU,IAAI;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,WAAY;AAC1B,uBAAAC,SAAQ,QAAQ,gBAAgB,GAAG,kEAAkE;AAAA,EACvG,GAAG,CAAC,gBAAgB,CAAC;AACrB,SAAO,YAAY,QAAQ,EAAE;AAAA,IAC3B,KAAK;AAAA,EACP,CAAC;AACH;", "names": ["isEqual", "warning", "printWarning", "React", "React", "unwrapArray", "safeInvoke", "setRef", "fromEntries", "React", "usePopper", "state", "isEqual", "createPopper", "NOOP", "NOOP_PROMISE", "EMPTY_MODIFIERS", "React", "warning"]}
{"version": 3, "sources": ["../../lru-cache/src/index.ts"], "sourcesContent": ["/**\n * @module LRUCache\n */\n\n// module-private names and types\ntype Perf = { now: () => number }\nconst perf: Perf =\n  typeof performance === 'object' &&\n  performance &&\n  typeof performance.now === 'function'\n    ? performance\n    : Date\n\nconst warned = new Set<string>()\n\n// either a function or a class\ntype ForC = ((...a: any[]) => any) | { new (...a: any[]): any }\n\n/* c8 ignore start */\nconst PROCESS = (\n  typeof process === 'object' && !!process ? process : {}\n) as { [k: string]: any }\n/* c8 ignore start */\n\nconst emitWarning = (\n  msg: string,\n  type: string,\n  code: string,\n  fn: ForC\n) => {\n  typeof PROCESS.emitWarning === 'function'\n    ? PROCESS.emitWarning(msg, type, code, fn)\n    : console.error(`[${code}] ${type}: ${msg}`)\n}\n\nlet AC = globalThis.AbortController\nlet AS = globalThis.AbortSignal\n\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n  //@ts-ignore\n  AS = class AbortSignal {\n    onabort?: (...a: any[]) => any\n    _onabort: ((...a: any[]) => any)[] = []\n    reason?: any\n    aborted: boolean = false\n    addEventListener(_: string, fn: (...a: any[]) => any) {\n      this._onabort.push(fn)\n    }\n  }\n  //@ts-ignore\n  AC = class AbortController {\n    constructor() {\n      warnACPolyfill()\n    }\n    signal = new AS()\n    abort(reason: any) {\n      if (this.signal.aborted) return\n      //@ts-ignore\n      this.signal.reason = reason\n      //@ts-ignore\n      this.signal.aborted = true\n      //@ts-ignore\n      for (const fn of this.signal._onabort) {\n        fn(reason)\n      }\n      this.signal.onabort?.(reason)\n    }\n  }\n  let printACPolyfillWarning =\n    PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1'\n  const warnACPolyfill = () => {\n    if (!printACPolyfillWarning) return\n    printACPolyfillWarning = false\n    emitWarning(\n      'AbortController is not defined. If using lru-cache in ' +\n        'node 14, load an AbortController polyfill from the ' +\n        '`node-abort-controller` package. A minimal polyfill is ' +\n        'provided for use by LRUCache.fetch(), but it should not be ' +\n        'relied upon in other contexts (eg, passing it to other APIs that ' +\n        'use AbortController/AbortSignal might have undesirable effects). ' +\n        'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.',\n      'NO_ABORT_CONTROLLER',\n      'ENOTSUP',\n      warnACPolyfill\n    )\n  }\n}\n/* c8 ignore stop */\n\nconst shouldWarn = (code: string) => !warned.has(code)\n\nconst TYPE = Symbol('type')\nexport type PosInt = number & { [TYPE]: 'Positive Integer' }\nexport type Index = number & { [TYPE]: 'LRUCache Index' }\n\nconst isPosInt = (n: any): n is PosInt =>\n  n && n === Math.floor(n) && n > 0 && isFinite(n)\n\nexport type UintArray = Uint8Array | Uint16Array | Uint32Array\nexport type NumberArray = UintArray | number[]\n\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max: number) =>\n  !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n    ? Uint8Array\n    : max <= Math.pow(2, 16)\n    ? Uint16Array\n    : max <= Math.pow(2, 32)\n    ? Uint32Array\n    : max <= Number.MAX_SAFE_INTEGER\n    ? ZeroArray\n    : null\n/* c8 ignore stop */\n\nclass ZeroArray extends Array<number> {\n  constructor(size: number) {\n    super(size)\n    this.fill(0)\n  }\n}\nexport type { ZeroArray }\nexport type { Stack }\n\nexport type StackLike = Stack | Index[]\nclass Stack {\n  heap: NumberArray\n  length: number\n  // private constructor\n  static #constructing: boolean = false\n  static create(max: number): StackLike {\n    const HeapCls = getUintArray(max)\n    if (!HeapCls) return []\n    Stack.#constructing = true\n    const s = new Stack(max, HeapCls)\n    Stack.#constructing = false\n    return s\n  }\n  constructor(\n    max: number,\n    HeapCls: { new (n: number): NumberArray }\n  ) {\n    /* c8 ignore start */\n    if (!Stack.#constructing) {\n      throw new TypeError('instantiate Stack using Stack.create(n)')\n    }\n    /* c8 ignore stop */\n    this.heap = new HeapCls(max)\n    this.length = 0\n  }\n  push(n: Index) {\n    this.heap[this.length++] = n\n  }\n  pop(): Index {\n    return this.heap[--this.length] as Index\n  }\n}\n\n/**\n * Promise representing an in-progress {@link LRUCache#fetch} call\n */\nexport type BackgroundFetch<V> = Promise<V | undefined> & {\n  __returned: BackgroundFetch<V> | undefined\n  __abortController: AbortController\n  __staleWhileFetching: V | undefined\n}\n\nexport type DisposeTask<K, V> = [\n  value: V,\n  key: K,\n  reason: LRUCache.DisposeReason\n]\n\nexport namespace LRUCache {\n  /**\n   * An integer greater than 0, reflecting the calculated size of items\n   */\n  export type Size = number\n\n  /**\n   * Integer greater than 0, representing some number of milliseconds, or the\n   * time at which a TTL started counting from.\n   */\n  export type Milliseconds = number\n\n  /**\n   * An integer greater than 0, reflecting a number of items\n   */\n  export type Count = number\n\n  /**\n   * The reason why an item was removed from the cache, passed\n   * to the {@link Disposer} methods.\n   *\n   * - `evict`: The item was evicted because it is the least recently used,\n   *   and the cache is full.\n   * - `set`: A new value was set, overwriting the old value being disposed.\n   * - `delete`: The item was explicitly deleted, either by calling\n   *   {@link LRUCache#delete}, {@link LRUCache#clear}, or\n   *   {@link LRUCache#set} with an undefined value.\n   * - `expire`: The item was removed due to exceeding its TTL.\n   * - `fetch`: A {@link OptionsBase#fetchMethod} operation returned\n   *   `undefined` or was aborted, causing the item to be deleted.\n   */\n  export type DisposeReason =\n    | 'evict'\n    | 'set'\n    | 'delete'\n    | 'expire'\n    | 'fetch'\n  /**\n   * A method called upon item removal, passed as the\n   * {@link OptionsBase.dispose} and/or\n   * {@link OptionsBase.disposeAfter} options.\n   */\n  export type Disposer<K, V> = (\n    value: V,\n    key: K,\n    reason: DisposeReason\n  ) => void\n\n  /**\n   * A function that returns the effective calculated size\n   * of an entry in the cache.\n   */\n  export type SizeCalculator<K, V> = (value: V, key: K) => Size\n\n  /**\n   * Options provided to the\n   * {@link OptionsBase.fetchMethod} function.\n   */\n  export interface FetcherOptions<K, V, FC = unknown> {\n    signal: AbortSignal\n    options: FetcherFetchOptions<K, V, FC>\n    /**\n     * Object provided in the {@link FetchOptions.context} option to\n     * {@link LRUCache#fetch}\n     */\n    context: FC\n  }\n\n  /**\n   * Occasionally, it may be useful to track the internal behavior of the\n   * cache, particularly for logging, debugging, or for behavior within the\n   * `fetchMethod`. To do this, you can pass a `status` object to the\n   * {@link LRUCache#fetch}, {@link LRUCache#get}, {@link LRUCache#set},\n   * {@link LRUCache#memo}, and {@link LRUCache#has} methods.\n   *\n   * The `status` option should be a plain JavaScript object. The following\n   * fields will be set on it appropriately, depending on the situation.\n   */\n  export interface Status<V> {\n    /**\n     * The status of a set() operation.\n     *\n     * - add: the item was not found in the cache, and was added\n     * - update: the item was in the cache, with the same value provided\n     * - replace: the item was in the cache, and replaced\n     * - miss: the item was not added to the cache for some reason\n     */\n    set?: 'add' | 'update' | 'replace' | 'miss'\n\n    /**\n     * the ttl stored for the item, or undefined if ttls are not used.\n     */\n    ttl?: Milliseconds\n\n    /**\n     * the start time for the item, or undefined if ttls are not used.\n     */\n    start?: Milliseconds\n\n    /**\n     * The timestamp used for TTL calculation\n     */\n    now?: Milliseconds\n\n    /**\n     * the remaining ttl for the item, or undefined if ttls are not used.\n     */\n    remainingTTL?: Milliseconds\n\n    /**\n     * The calculated size for the item, if sizes are used.\n     */\n    entrySize?: Size\n\n    /**\n     * The total calculated size of the cache, if sizes are used.\n     */\n    totalCalculatedSize?: Size\n\n    /**\n     * A flag indicating that the item was not stored, due to exceeding the\n     * {@link OptionsBase.maxEntrySize}\n     */\n    maxEntrySizeExceeded?: true\n\n    /**\n     * The old value, specified in the case of `set:'update'` or\n     * `set:'replace'`\n     */\n    oldValue?: V\n\n    /**\n     * The results of a {@link LRUCache#has} operation\n     *\n     * - hit: the item was found in the cache\n     * - stale: the item was found in the cache, but is stale\n     * - miss: the item was not found in the cache\n     */\n    has?: 'hit' | 'stale' | 'miss'\n\n    /**\n     * The status of a {@link LRUCache#fetch} operation.\n     * Note that this can change as the underlying fetch() moves through\n     * various states.\n     *\n     * - inflight: there is another fetch() for this key which is in process\n     * - get: there is no {@link OptionsBase.fetchMethod}, so\n     *   {@link LRUCache#get} was called.\n     * - miss: the item is not in cache, and will be fetched.\n     * - hit: the item is in the cache, and was resolved immediately.\n     * - stale: the item is in the cache, but stale.\n     * - refresh: the item is in the cache, and not stale, but\n     *   {@link FetchOptions.forceRefresh} was specified.\n     */\n    fetch?: 'get' | 'inflight' | 'miss' | 'hit' | 'stale' | 'refresh'\n\n    /**\n     * The {@link OptionsBase.fetchMethod} was called\n     */\n    fetchDispatched?: true\n\n    /**\n     * The cached value was updated after a successful call to\n     * {@link OptionsBase.fetchMethod}\n     */\n    fetchUpdated?: true\n\n    /**\n     * The reason for a fetch() rejection.  Either the error raised by the\n     * {@link OptionsBase.fetchMethod}, or the reason for an\n     * AbortSignal.\n     */\n    fetchError?: Error\n\n    /**\n     * The fetch received an abort signal\n     */\n    fetchAborted?: true\n\n    /**\n     * The abort signal received was ignored, and the fetch was allowed to\n     * continue.\n     */\n    fetchAbortIgnored?: true\n\n    /**\n     * The fetchMethod promise resolved successfully\n     */\n    fetchResolved?: true\n\n    /**\n     * The fetchMethod promise was rejected\n     */\n    fetchRejected?: true\n\n    /**\n     * The status of a {@link LRUCache#get} operation.\n     *\n     * - fetching: The item is currently being fetched.  If a previous value\n     *   is present and allowed, that will be returned.\n     * - stale: The item is in the cache, and is stale.\n     * - hit: the item is in the cache\n     * - miss: the item is not in the cache\n     */\n    get?: 'stale' | 'hit' | 'miss'\n\n    /**\n     * A fetch or get operation returned a stale value.\n     */\n    returnedStale?: true\n  }\n\n  /**\n   * options which override the options set in the LRUCache constructor\n   * when calling {@link LRUCache#fetch}.\n   *\n   * This is the union of {@link GetOptions} and {@link SetOptions}, plus\n   * {@link OptionsBase.noDeleteOnFetchRejection},\n   * {@link OptionsBase.allowStaleOnFetchRejection},\n   * {@link FetchOptions.forceRefresh}, and\n   * {@link FetcherOptions.context}\n   *\n   * Any of these may be modified in the {@link OptionsBase.fetchMethod}\n   * function, but the {@link GetOptions} fields will of course have no\n   * effect, as the {@link LRUCache#get} call already happened by the time\n   * the fetchMethod is called.\n   */\n  export interface FetcherFetchOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n      | 'noDeleteOnFetchRejection'\n      | 'allowStaleOnFetchRejection'\n      | 'ignoreFetchAbort'\n      | 'allowStaleOnFetchAbort'\n    > {\n    status?: Status<V>\n    size?: Size\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#fetch} method.\n   */\n  export interface FetchOptions<K, V, FC>\n    extends FetcherFetchOptions<K, V, FC> {\n    /**\n     * Set to true to force a re-load of the existing data, even if it\n     * is not yet stale.\n     */\n    forceRefresh?: boolean\n    /**\n     * Context provided to the {@link OptionsBase.fetchMethod} as\n     * the {@link FetcherOptions.context} param.\n     *\n     * If the FC type is specified as unknown (the default),\n     * undefined or void, then this is optional.  Otherwise, it will\n     * be required.\n     */\n    context?: FC\n    signal?: AbortSignal\n    status?: Status<V>\n  }\n  /**\n   * Options provided to {@link LRUCache#fetch} when the FC type is something\n   * other than `unknown`, `undefined`, or `void`\n   */\n  export interface FetchOptionsWithContext<K, V, FC>\n    extends FetchOptions<K, V, FC> {\n    context: FC\n  }\n  /**\n   * Options provided to {@link LRUCache#fetch} when the FC type is\n   * `undefined` or `void`\n   */\n  export interface FetchOptionsNoContext<K, V>\n    extends FetchOptions<K, V, undefined> {\n    context?: undefined\n  }\n\n  export interface MemoOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n      | 'noDeleteOnFetchRejection'\n      | 'allowStaleOnFetchRejection'\n      | 'ignoreFetchAbort'\n      | 'allowStaleOnFetchAbort'\n    > {\n    /**\n     * Set to true to force a re-load of the existing data, even if it\n     * is not yet stale.\n     */\n    forceRefresh?: boolean\n    /**\n     * Context provided to the {@link OptionsBase.memoMethod} as\n     * the {@link MemoizerOptions.context} param.\n     *\n     * If the FC type is specified as unknown (the default),\n     * undefined or void, then this is optional.  Otherwise, it will\n     * be required.\n     */\n    context?: FC\n    status?: Status<V>\n  }\n  /**\n   * Options provided to {@link LRUCache#memo} when the FC type is something\n   * other than `unknown`, `undefined`, or `void`\n   */\n  export interface MemoOptionsWithContext<K, V, FC>\n    extends MemoOptions<K, V, FC> {\n    context: FC\n  }\n  /**\n   * Options provided to {@link LRUCache#memo} when the FC type is\n   * `undefined` or `void`\n   */\n  export interface MemoOptionsNoContext<K, V>\n    extends MemoOptions<K, V, undefined> {\n    context?: undefined\n  }\n\n  /**\n   * Options provided to the\n   * {@link OptionsBase.memoMethod} function.\n   */\n  export interface MemoizerOptions<K, V, FC = unknown> {\n    options: MemoizerMemoOptions<K, V, FC>\n    /**\n     * Object provided in the {@link MemoOptions.context} option to\n     * {@link LRUCache#memo}\n     */\n    context: FC\n  }\n\n  /**\n   * options which override the options set in the LRUCache constructor\n   * when calling {@link LRUCache#memo}.\n   *\n   * This is the union of {@link GetOptions} and {@link SetOptions}, plus\n   * {@link MemoOptions.forceRefresh}, and\n   * {@link MemoerOptions.context}\n   *\n   * Any of these may be modified in the {@link OptionsBase.memoMethod}\n   * function, but the {@link GetOptions} fields will of course have no\n   * effect, as the {@link LRUCache#get} call already happened by the time\n   * the memoMethod is called.\n   */\n  export interface MemoizerMemoOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n    > {\n    status?: Status<V>\n    size?: Size\n    start?: Milliseconds\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#has} method.\n   */\n  export interface HasOptions<K, V, FC>\n    extends Pick<OptionsBase<K, V, FC>, 'updateAgeOnHas'> {\n    status?: Status<V>\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#get} method.\n   */\n  export interface GetOptions<K, V, FC>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      'allowStale' | 'updateAgeOnGet' | 'noDeleteOnStaleGet'\n    > {\n    status?: Status<V>\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#peek} method.\n   */\n  export interface PeekOptions<K, V, FC>\n    extends Pick<OptionsBase<K, V, FC>, 'allowStale'> {}\n\n  /**\n   * Options that may be passed to the {@link LRUCache#set} method.\n   */\n  export interface SetOptions<K, V, FC>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      'sizeCalculation' | 'ttl' | 'noDisposeOnSet' | 'noUpdateTTL'\n    > {\n    /**\n     * If size tracking is enabled, then setting an explicit size\n     * in the {@link LRUCache#set} call will prevent calling the\n     * {@link OptionsBase.sizeCalculation} function.\n     */\n    size?: Size\n    /**\n     * If TTL tracking is enabled, then setting an explicit start\n     * time in the {@link LRUCache#set} call will override the\n     * default time from `performance.now()` or `Date.now()`.\n     *\n     * Note that it must be a valid value for whichever time-tracking\n     * method is in use.\n     */\n    start?: Milliseconds\n    status?: Status<V>\n  }\n\n  /**\n   * The type signature for the {@link OptionsBase.fetchMethod} option.\n   */\n  export type Fetcher<K, V, FC = unknown> = (\n    key: K,\n    staleValue: V | undefined,\n    options: FetcherOptions<K, V, FC>\n  ) => Promise<V | undefined | void> | V | undefined | void\n\n  /**\n   * the type signature for the {@link OptionsBase.memoMethod} option.\n   */\n  export type Memoizer<K, V, FC = unknown> = (\n    key: K,\n    staleValue: V | undefined,\n    options: MemoizerOptions<K, V, FC>\n  ) => V\n\n  /**\n   * Options which may be passed to the {@link LRUCache} constructor.\n   *\n   * Most of these may be overridden in the various options that use\n   * them.\n   *\n   * Despite all being technically optional, the constructor requires that\n   * a cache is at minimum limited by one or more of {@link OptionsBase.max},\n   * {@link OptionsBase.ttl}, or {@link OptionsBase.maxSize}.\n   *\n   * If {@link OptionsBase.ttl} is used alone, then it is strongly advised\n   * (and in fact required by the type definitions here) that the cache\n   * also set {@link OptionsBase.ttlAutopurge}, to prevent potentially\n   * unbounded storage.\n   *\n   * All options are also available on the {@link LRUCache} instance, making\n   * it safe to pass an LRUCache instance as the options argumemnt to\n   * make another empty cache of the same type.\n   *\n   * Some options are marked as read-only, because changing them after\n   * instantiation is not safe. Changing any of the other options will of\n   * course only have an effect on subsequent method calls.\n   */\n  export interface OptionsBase<K, V, FC> {\n    /**\n     * The maximum number of items to store in the cache before evicting\n     * old entries. This is read-only on the {@link LRUCache} instance,\n     * and may not be overridden.\n     *\n     * If set, then storage space will be pre-allocated at construction\n     * time, and the cache will perform significantly faster.\n     *\n     * Note that significantly fewer items may be stored, if\n     * {@link OptionsBase.maxSize} and/or {@link OptionsBase.ttl} are also\n     * set.\n     *\n     * **It is strongly recommended to set a `max` to prevent unbounded growth\n     * of the cache.**\n     */\n    max?: Count\n\n    /**\n     * Max time in milliseconds for items to live in cache before they are\n     * considered stale.  Note that stale items are NOT preemptively removed by\n     * default, and MAY live in the cache, contributing to its LRU max, long\n     * after they have expired, unless {@link OptionsBase.ttlAutopurge} is\n     * set.\n     *\n     * If set to `0` (the default value), then that means \"do not track\n     * TTL\", not \"expire immediately\".\n     *\n     * Also, as this cache is optimized for LRU/MRU operations, some of\n     * the staleness/TTL checks will reduce performance, as they will incur\n     * overhead by deleting items.\n     *\n     * This is not primarily a TTL cache, and does not make strong TTL\n     * guarantees. There is no pre-emptive pruning of expired items, but you\n     * _may_ set a TTL on the cache, and it will treat expired items as missing\n     * when they are fetched, and delete them.\n     *\n     * Optional, but must be a non-negative integer in ms if specified.\n     *\n     * This may be overridden by passing an options object to `cache.set()`.\n     *\n     * At least one of `max`, `maxSize`, or `TTL` is required. This must be a\n     * positive integer if set.\n     *\n     * Even if ttl tracking is enabled, **it is strongly recommended to set a\n     * `max` to prevent unbounded growth of the cache.**\n     *\n     * If ttl tracking is enabled, and `max` and `maxSize` are not set,\n     * and `ttlAutopurge` is not set, then a warning will be emitted\n     * cautioning about the potential for unbounded memory consumption.\n     * (The TypeScript definitions will also discourage this.)\n     */\n    ttl?: Milliseconds\n\n    /**\n     * Minimum amount of time in ms in which to check for staleness.\n     * Defaults to 1, which means that the current time is checked\n     * at most once per millisecond.\n     *\n     * Set to 0 to check the current time every time staleness is tested.\n     * (This reduces performance, and is theoretically unnecessary.)\n     *\n     * Setting this to a higher value will improve performance somewhat\n     * while using ttl tracking, albeit at the expense of keeping stale\n     * items around a bit longer than their TTLs would indicate.\n     *\n     * @default 1\n     */\n    ttlResolution?: Milliseconds\n\n    /**\n     * Preemptively remove stale items from the cache.\n     *\n     * Note that this may *significantly* degrade performance, especially if\n     * the cache is storing a large number of items. It is almost always best\n     * to just leave the stale items in the cache, and let them fall out as new\n     * items are added.\n     *\n     * Note that this means that {@link OptionsBase.allowStale} is a bit\n     * pointless, as stale items will be deleted almost as soon as they\n     * expire.\n     *\n     * Use with caution!\n     */\n    ttlAutopurge?: boolean\n\n    /**\n     * When using time-expiring entries with `ttl`, setting this to `true` will\n     * make each item's age reset to 0 whenever it is retrieved from cache with\n     * {@link LRUCache#get}, causing it to not expire. (It can still fall out\n     * of cache based on recency of use, of course.)\n     *\n     * Has no effect if {@link OptionsBase.ttl} is not set.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     */\n    updateAgeOnGet?: boolean\n\n    /**\n     * When using time-expiring entries with `ttl`, setting this to `true` will\n     * make each item's age reset to 0 whenever its presence in the cache is\n     * checked with {@link LRUCache#has}, causing it to not expire. (It can\n     * still fall out of cache based on recency of use, of course.)\n     *\n     * Has no effect if {@link OptionsBase.ttl} is not set.\n     */\n    updateAgeOnHas?: boolean\n\n    /**\n     * Allow {@link LRUCache#get} and {@link LRUCache#fetch} calls to return\n     * stale data, if available.\n     *\n     * By default, if you set `ttl`, stale items will only be deleted from the\n     * cache when you `get(key)`. That is, it's not preemptively pruning items,\n     * unless {@link OptionsBase.ttlAutopurge} is set.\n     *\n     * If you set `allowStale:true`, it'll return the stale value *as well as*\n     * deleting it. If you don't set this, then it'll return `undefined` when\n     * you try to get a stale entry.\n     *\n     * Note that when a stale entry is fetched, _even if it is returned due to\n     * `allowStale` being set_, it is removed from the cache immediately. You\n     * can suppress this behavior by setting\n     * {@link OptionsBase.noDeleteOnStaleGet}, either in the constructor, or in\n     * the options provided to {@link LRUCache#get}.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     * The `cache.has()` method will always return `false` for stale items.\n     *\n     * Only relevant if a ttl is set.\n     */\n    allowStale?: boolean\n\n    /**\n     * Function that is called on items when they are dropped from the\n     * cache, as `dispose(value, key, reason)`.\n     *\n     * This can be handy if you want to close file descriptors or do\n     * other cleanup tasks when items are no longer stored in the cache.\n     *\n     * **NOTE**: It is called _before_ the item has been fully removed\n     * from the cache, so if you want to put it right back in, you need\n     * to wait until the next tick. If you try to add it back in during\n     * the `dispose()` function call, it will break things in subtle and\n     * weird ways.\n     *\n     * Unlike several other options, this may _not_ be overridden by\n     * passing an option to `set()`, for performance reasons.\n     *\n     * The `reason` will be one of the following strings, corresponding\n     * to the reason for the item's deletion:\n     *\n     * - `evict` Item was evicted to make space for a new addition\n     * - `set` Item was overwritten by a new value\n     * - `expire` Item expired its TTL\n     * - `fetch` Item was deleted due to a failed or aborted fetch, or a\n     *   fetchMethod returning `undefined.\n     * - `delete` Item was removed by explicit `cache.delete(key)`,\n     *   `cache.clear()`, or `cache.set(key, undefined)`.\n     */\n    dispose?: Disposer<K, V>\n\n    /**\n     * The same as {@link OptionsBase.dispose}, but called *after* the entry\n     * is completely removed and the cache is once again in a clean state.\n     *\n     * It is safe to add an item right back into the cache at this point.\n     * However, note that it is *very* easy to inadvertently create infinite\n     * recursion this way.\n     */\n    disposeAfter?: Disposer<K, V>\n\n    /**\n     * Set to true to suppress calling the\n     * {@link OptionsBase.dispose} function if the entry key is\n     * still accessible within the cache.\n     *\n     * This may be overridden by passing an options object to\n     * {@link LRUCache#set}.\n     *\n     * Only relevant if `dispose` or `disposeAfter` are set.\n     */\n    noDisposeOnSet?: boolean\n\n    /**\n     * Boolean flag to tell the cache to not update the TTL when setting a new\n     * value for an existing key (ie, when updating a value rather than\n     * inserting a new value).  Note that the TTL value is _always_ set (if\n     * provided) when adding a new entry into the cache.\n     *\n     * Has no effect if a {@link OptionsBase.ttl} is not set.\n     *\n     * May be passed as an option to {@link LRUCache#set}.\n     */\n    noUpdateTTL?: boolean\n\n    /**\n     * Set to a positive integer to track the sizes of items added to the\n     * cache, and automatically evict items in order to stay below this size.\n     * Note that this may result in fewer than `max` items being stored.\n     *\n     * Attempting to add an item to the cache whose calculated size is greater\n     * that this amount will be a no-op. The item will not be cached, and no\n     * other items will be evicted.\n     *\n     * Optional, must be a positive integer if provided.\n     *\n     * Sets `maxEntrySize` to the same value, unless a different value is\n     * provided for `maxEntrySize`.\n     *\n     * At least one of `max`, `maxSize`, or `TTL` is required. This must be a\n     * positive integer if set.\n     *\n     * Even if size tracking is enabled, **it is strongly recommended to set a\n     * `max` to prevent unbounded growth of the cache.**\n     *\n     * Note also that size tracking can negatively impact performance,\n     * though for most cases, only minimally.\n     */\n    maxSize?: Size\n\n    /**\n     * The maximum allowed size for any single item in the cache.\n     *\n     * If a larger item is passed to {@link LRUCache#set} or returned by a\n     * {@link OptionsBase.fetchMethod} or {@link OptionsBase.memoMethod}, then\n     * it will not be stored in the cache.\n     *\n     * Attempting to add an item whose calculated size is greater than\n     * this amount will not cache the item or evict any old items, but\n     * WILL delete an existing value if one is already present.\n     *\n     * Optional, must be a positive integer if provided. Defaults to\n     * the value of `maxSize` if provided.\n     */\n    maxEntrySize?: Size\n\n    /**\n     * A function that returns a number indicating the item's size.\n     *\n     * Requires {@link OptionsBase.maxSize} to be set.\n     *\n     * If not provided, and {@link OptionsBase.maxSize} or\n     * {@link OptionsBase.maxEntrySize} are set, then all\n     * {@link LRUCache#set} calls **must** provide an explicit\n     * {@link SetOptions.size} or sizeCalculation param.\n     */\n    sizeCalculation?: SizeCalculator<K, V>\n\n    /**\n     * Method that provides the implementation for {@link LRUCache#fetch}\n     *\n     * ```ts\n     * fetchMethod(key, staleValue, { signal, options, context })\n     * ```\n     *\n     * If `fetchMethod` is not provided, then `cache.fetch(key)` is equivalent\n     * to `Promise.resolve(cache.get(key))`.\n     *\n     * If at any time, `signal.aborted` is set to `true`, or if the\n     * `signal.onabort` method is called, or if it emits an `'abort'` event\n     * which you can listen to with `addEventListener`, then that means that\n     * the fetch should be abandoned. This may be passed along to async\n     * functions aware of AbortController/AbortSignal behavior.\n     *\n     * The `fetchMethod` should **only** return `undefined` or a Promise\n     * resolving to `undefined` if the AbortController signaled an `abort`\n     * event. In all other cases, it should return or resolve to a value\n     * suitable for adding to the cache.\n     *\n     * The `options` object is a union of the options that may be provided to\n     * `set()` and `get()`. If they are modified, then that will result in\n     * modifying the settings to `cache.set()` when the value is resolved, and\n     * in the case of\n     * {@link OptionsBase.noDeleteOnFetchRejection} and\n     * {@link OptionsBase.allowStaleOnFetchRejection}, the handling of\n     * `fetchMethod` failures.\n     *\n     * For example, a DNS cache may update the TTL based on the value returned\n     * from a remote DNS server by changing `options.ttl` in the `fetchMethod`.\n     */\n    fetchMethod?: Fetcher<K, V, FC>\n\n    /**\n     * Method that provides the implementation for {@link LRUCache#memo}\n     */\n    memoMethod?: Memoizer<K, V, FC>\n\n    /**\n     * Set to true to suppress the deletion of stale data when a\n     * {@link OptionsBase.fetchMethod} returns a rejected promise.\n     */\n    noDeleteOnFetchRejection?: boolean\n\n    /**\n     * Do not delete stale items when they are retrieved with\n     * {@link LRUCache#get}.\n     *\n     * Note that the `get` return value will still be `undefined`\n     * unless {@link OptionsBase.allowStale} is true.\n     *\n     * When using time-expiring entries with `ttl`, by default stale\n     * items will be removed from the cache when the key is accessed\n     * with `cache.get()`.\n     *\n     * Setting this option will cause stale items to remain in the cache, until\n     * they are explicitly deleted with `cache.delete(key)`, or retrieved with\n     * `noDeleteOnStaleGet` set to `false`.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     *\n     * Only relevant if a ttl is used.\n     */\n    noDeleteOnStaleGet?: boolean\n\n    /**\n     * Set to true to allow returning stale data when a\n     * {@link OptionsBase.fetchMethod} throws an error or returns a rejected\n     * promise.\n     *\n     * This differs from using {@link OptionsBase.allowStale} in that stale\n     * data will ONLY be returned in the case that the {@link LRUCache#fetch}\n     * fails, not any other times.\n     *\n     * If a `fetchMethod` fails, and there is no stale value available, the\n     * `fetch()` will resolve to `undefined`. Ie, all `fetchMethod` errors are\n     * suppressed.\n     *\n     * Implies `noDeleteOnFetchRejection`.\n     *\n     * This may be set in calls to `fetch()`, or defaulted on the constructor,\n     * or overridden by modifying the options object in the `fetchMethod`.\n     */\n    allowStaleOnFetchRejection?: boolean\n\n    /**\n     * Set to true to return a stale value from the cache when the\n     * `AbortSignal` passed to the {@link OptionsBase.fetchMethod} dispatches\n     * an `'abort'` event, whether user-triggered, or due to internal cache\n     * behavior.\n     *\n     * Unless {@link OptionsBase.ignoreFetchAbort} is also set, the underlying\n     * {@link OptionsBase.fetchMethod} will still be considered canceled, and\n     * any value it returns will be ignored and not cached.\n     *\n     * Caveat: since fetches are aborted when a new value is explicitly\n     * set in the cache, this can lead to fetch returning a stale value,\n     * since that was the fallback value _at the moment the `fetch()` was\n     * initiated_, even though the new updated value is now present in\n     * the cache.\n     *\n     * For example:\n     *\n     * ```ts\n     * const cache = new LRUCache<string, any>({\n     *   ttl: 100,\n     *   fetchMethod: async (url, oldValue, { signal }) =>  {\n     *     const res = await fetch(url, { signal })\n     *     return await res.json()\n     *   }\n     * })\n     * cache.set('https://example.com/', { some: 'data' })\n     * // 100ms go by...\n     * const result = cache.fetch('https://example.com/')\n     * cache.set('https://example.com/', { other: 'thing' })\n     * console.log(await result) // { some: 'data' }\n     * console.log(cache.get('https://example.com/')) // { other: 'thing' }\n     * ```\n     */\n    allowStaleOnFetchAbort?: boolean\n\n    /**\n     * Set to true to ignore the `abort` event emitted by the `AbortSignal`\n     * object passed to {@link OptionsBase.fetchMethod}, and still cache the\n     * resulting resolution value, as long as it is not `undefined`.\n     *\n     * When used on its own, this means aborted {@link LRUCache#fetch} calls\n     * are not immediately resolved or rejected when they are aborted, and\n     * instead take the full time to await.\n     *\n     * When used with {@link OptionsBase.allowStaleOnFetchAbort}, aborted\n     * {@link LRUCache#fetch} calls will resolve immediately to their stale\n     * cached value or `undefined`, and will continue to process and eventually\n     * update the cache when they resolve, as long as the resulting value is\n     * not `undefined`, thus supporting a \"return stale on timeout while\n     * refreshing\" mechanism by passing `AbortSignal.timeout(n)` as the signal.\n     *\n     * For example:\n     *\n     * ```ts\n     * const c = new LRUCache({\n     *   ttl: 100,\n     *   ignoreFetchAbort: true,\n     *   allowStaleOnFetchAbort: true,\n     *   fetchMethod: async (key, oldValue, { signal }) => {\n     *     // note: do NOT pass the signal to fetch()!\n     *     // let's say this fetch can take a long time.\n     *     const res = await fetch(`https://slow-backend-server/${key}`)\n     *     return await res.json()\n     *   },\n     * })\n     *\n     * // this will return the stale value after 100ms, while still\n     * // updating in the background for next time.\n     * const val = await c.fetch('key', { signal: AbortSignal.timeout(100) })\n     * ```\n     *\n     * **Note**: regardless of this setting, an `abort` event _is still\n     * emitted on the `AbortSignal` object_, so may result in invalid results\n     * when passed to other underlying APIs that use AbortSignals.\n     *\n     * This may be overridden in the {@link OptionsBase.fetchMethod} or the\n     * call to {@link LRUCache#fetch}.\n     */\n    ignoreFetchAbort?: boolean\n  }\n\n  export interface OptionsMaxLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    max: Count\n  }\n  export interface OptionsTTLLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    ttl: Milliseconds\n    ttlAutopurge: boolean\n  }\n  export interface OptionsSizeLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    maxSize: Size\n  }\n\n  /**\n   * The valid safe options for the {@link LRUCache} constructor\n   */\n  export type Options<K, V, FC> =\n    | OptionsMaxLimit<K, V, FC>\n    | OptionsSizeLimit<K, V, FC>\n    | OptionsTTLLimit<K, V, FC>\n\n  /**\n   * Entry objects used by {@link LRUCache#load} and {@link LRUCache#dump},\n   * and returned by {@link LRUCache#info}.\n   */\n  export interface Entry<V> {\n    value: V\n    ttl?: Milliseconds\n    size?: Size\n    start?: Milliseconds\n  }\n}\n\n/**\n * Default export, the thing you're using this module to get.\n *\n * The `K` and `V` types define the key and value types, respectively. The\n * optional `FC` type defines the type of the `context` object passed to\n * `cache.fetch()` and `cache.memo()`.\n *\n * Keys and values **must not** be `null` or `undefined`.\n *\n * All properties from the options object (with the exception of `max`,\n * `maxSize`, `fetchMethod`, `memoMethod`, `dispose` and `disposeAfter`) are\n * added as normal public members. (The listed options are read-only getters.)\n *\n * Changing any of these will alter the defaults for subsequent method calls.\n */\nexport class LRUCache<K extends {}, V extends {}, FC = unknown>\n  implements Map<K, V>\n{\n  // options that cannot be changed without disaster\n  readonly #max: LRUCache.Count\n  readonly #maxSize: LRUCache.Size\n  readonly #dispose?: LRUCache.Disposer<K, V>\n  readonly #disposeAfter?: LRUCache.Disposer<K, V>\n  readonly #fetchMethod?: LRUCache.Fetcher<K, V, FC>\n  readonly #memoMethod?: LRUCache.Memoizer<K, V, FC>\n\n  /**\n   * {@link LRUCache.OptionsBase.ttl}\n   */\n  ttl: LRUCache.Milliseconds\n\n  /**\n   * {@link LRUCache.OptionsBase.ttlResolution}\n   */\n  ttlResolution: LRUCache.Milliseconds\n  /**\n   * {@link LRUCache.OptionsBase.ttlAutopurge}\n   */\n  ttlAutopurge: boolean\n  /**\n   * {@link LRUCache.OptionsBase.updateAgeOnGet}\n   */\n  updateAgeOnGet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.updateAgeOnHas}\n   */\n  updateAgeOnHas: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStale}\n   */\n  allowStale: boolean\n\n  /**\n   * {@link LRUCache.OptionsBase.noDisposeOnSet}\n   */\n  noDisposeOnSet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.noUpdateTTL}\n   */\n  noUpdateTTL: boolean\n  /**\n   * {@link LRUCache.OptionsBase.maxEntrySize}\n   */\n  maxEntrySize: LRUCache.Size\n  /**\n   * {@link LRUCache.OptionsBase.sizeCalculation}\n   */\n  sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  /**\n   * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n   */\n  noDeleteOnFetchRejection: boolean\n  /**\n   * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n   */\n  noDeleteOnStaleGet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n   */\n  allowStaleOnFetchAbort: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n   */\n  allowStaleOnFetchRejection: boolean\n  /**\n   * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n   */\n  ignoreFetchAbort: boolean\n\n  // computed properties\n  #size: LRUCache.Count\n  #calculatedSize: LRUCache.Size\n  #keyMap: Map<K, Index>\n  #keyList: (K | undefined)[]\n  #valList: (V | BackgroundFetch<V> | undefined)[]\n  #next: NumberArray\n  #prev: NumberArray\n  #head: Index\n  #tail: Index\n  #free: StackLike\n  #disposed?: DisposeTask<K, V>[]\n  #sizes?: ZeroArray\n  #starts?: ZeroArray\n  #ttls?: ZeroArray\n\n  #hasDispose: boolean\n  #hasFetchMethod: boolean\n  #hasDisposeAfter: boolean\n\n  /**\n   * Do not call this method unless you need to inspect the\n   * inner workings of the cache.  If anything returned by this\n   * object is modified in any way, strange breakage may occur.\n   *\n   * These fields are private for a reason!\n   *\n   * @internal\n   */\n  static unsafeExposeInternals<\n    K extends {},\n    V extends {},\n    FC extends unknown = unknown\n  >(c: LRUCache<K, V, FC>) {\n    return {\n      // properties\n      starts: c.#starts,\n      ttls: c.#ttls,\n      sizes: c.#sizes,\n      keyMap: c.#keyMap as Map<K, number>,\n      keyList: c.#keyList,\n      valList: c.#valList,\n      next: c.#next,\n      prev: c.#prev,\n      get head() {\n        return c.#head\n      },\n      get tail() {\n        return c.#tail\n      },\n      free: c.#free,\n      // methods\n      isBackgroundFetch: (p: any) => c.#isBackgroundFetch(p),\n      backgroundFetch: (\n        k: K,\n        index: number | undefined,\n        options: LRUCache.FetchOptions<K, V, FC>,\n        context: any\n      ): BackgroundFetch<V> =>\n        c.#backgroundFetch(\n          k,\n          index as Index | undefined,\n          options,\n          context\n        ),\n      moveToTail: (index: number): void =>\n        c.#moveToTail(index as Index),\n      indexes: (options?: { allowStale: boolean }) =>\n        c.#indexes(options),\n      rindexes: (options?: { allowStale: boolean }) =>\n        c.#rindexes(options),\n      isStale: (index: number | undefined) =>\n        c.#isStale(index as Index),\n    }\n  }\n\n  // Protected read-only members\n\n  /**\n   * {@link LRUCache.OptionsBase.max} (read-only)\n   */\n  get max(): LRUCache.Count {\n    return this.#max\n  }\n  /**\n   * {@link LRUCache.OptionsBase.maxSize} (read-only)\n   */\n  get maxSize(): LRUCache.Count {\n    return this.#maxSize\n  }\n  /**\n   * The total computed size of items in the cache (read-only)\n   */\n  get calculatedSize(): LRUCache.Size {\n    return this.#calculatedSize\n  }\n  /**\n   * The number of items stored in the cache (read-only)\n   */\n  get size(): LRUCache.Count {\n    return this.#size\n  }\n  /**\n   * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n   */\n  get fetchMethod(): LRUCache.Fetcher<K, V, FC> | undefined {\n    return this.#fetchMethod\n  }\n  get memoMethod(): LRUCache.Memoizer<K, V, FC> | undefined {\n    return this.#memoMethod\n  }\n  /**\n   * {@link LRUCache.OptionsBase.dispose} (read-only)\n   */\n  get dispose() {\n    return this.#dispose\n  }\n  /**\n   * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n   */\n  get disposeAfter() {\n    return this.#disposeAfter\n  }\n\n  constructor(\n    options: LRUCache.Options<K, V, FC> | LRUCache<K, V, FC>\n  ) {\n    const {\n      max = 0,\n      ttl,\n      ttlResolution = 1,\n      ttlAutopurge,\n      updateAgeOnGet,\n      updateAgeOnHas,\n      allowStale,\n      dispose,\n      disposeAfter,\n      noDisposeOnSet,\n      noUpdateTTL,\n      maxSize = 0,\n      maxEntrySize = 0,\n      sizeCalculation,\n      fetchMethod,\n      memoMethod,\n      noDeleteOnFetchRejection,\n      noDeleteOnStaleGet,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n    } = options\n\n    if (max !== 0 && !isPosInt(max)) {\n      throw new TypeError('max option must be a nonnegative integer')\n    }\n\n    const UintArray = max ? getUintArray(max) : Array\n    if (!UintArray) {\n      throw new Error('invalid max value: ' + max)\n    }\n\n    this.#max = max\n    this.#maxSize = maxSize\n    this.maxEntrySize = maxEntrySize || this.#maxSize\n    this.sizeCalculation = sizeCalculation\n    if (this.sizeCalculation) {\n      if (!this.#maxSize && !this.maxEntrySize) {\n        throw new TypeError(\n          'cannot set sizeCalculation without setting maxSize or maxEntrySize'\n        )\n      }\n      if (typeof this.sizeCalculation !== 'function') {\n        throw new TypeError('sizeCalculation set to non-function')\n      }\n    }\n\n    if (\n      memoMethod !== undefined &&\n      typeof memoMethod !== 'function'\n    ) {\n      throw new TypeError('memoMethod must be a function if defined')\n    }\n    this.#memoMethod = memoMethod\n\n    if (\n      fetchMethod !== undefined &&\n      typeof fetchMethod !== 'function'\n    ) {\n      throw new TypeError(\n        'fetchMethod must be a function if specified'\n      )\n    }\n    this.#fetchMethod = fetchMethod\n    this.#hasFetchMethod = !!fetchMethod\n\n    this.#keyMap = new Map()\n    this.#keyList = new Array(max).fill(undefined)\n    this.#valList = new Array(max).fill(undefined)\n    this.#next = new UintArray(max)\n    this.#prev = new UintArray(max)\n    this.#head = 0 as Index\n    this.#tail = 0 as Index\n    this.#free = Stack.create(max)\n    this.#size = 0\n    this.#calculatedSize = 0\n\n    if (typeof dispose === 'function') {\n      this.#dispose = dispose\n    }\n    if (typeof disposeAfter === 'function') {\n      this.#disposeAfter = disposeAfter\n      this.#disposed = []\n    } else {\n      this.#disposeAfter = undefined\n      this.#disposed = undefined\n    }\n    this.#hasDispose = !!this.#dispose\n    this.#hasDisposeAfter = !!this.#disposeAfter\n\n    this.noDisposeOnSet = !!noDisposeOnSet\n    this.noUpdateTTL = !!noUpdateTTL\n    this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection\n    this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection\n    this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort\n    this.ignoreFetchAbort = !!ignoreFetchAbort\n\n    // NB: maxEntrySize is set to maxSize if it's set\n    if (this.maxEntrySize !== 0) {\n      if (this.#maxSize !== 0) {\n        if (!isPosInt(this.#maxSize)) {\n          throw new TypeError(\n            'maxSize must be a positive integer if specified'\n          )\n        }\n      }\n      if (!isPosInt(this.maxEntrySize)) {\n        throw new TypeError(\n          'maxEntrySize must be a positive integer if specified'\n        )\n      }\n      this.#initializeSizeTracking()\n    }\n\n    this.allowStale = !!allowStale\n    this.noDeleteOnStaleGet = !!noDeleteOnStaleGet\n    this.updateAgeOnGet = !!updateAgeOnGet\n    this.updateAgeOnHas = !!updateAgeOnHas\n    this.ttlResolution =\n      isPosInt(ttlResolution) || ttlResolution === 0\n        ? ttlResolution\n        : 1\n    this.ttlAutopurge = !!ttlAutopurge\n    this.ttl = ttl || 0\n    if (this.ttl) {\n      if (!isPosInt(this.ttl)) {\n        throw new TypeError(\n          'ttl must be a positive integer if specified'\n        )\n      }\n      this.#initializeTTLTracking()\n    }\n\n    // do not allow completely unbounded caches\n    if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n      throw new TypeError(\n        'At least one of max, maxSize, or ttl is required'\n      )\n    }\n    if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n      const code = 'LRU_CACHE_UNBOUNDED'\n      if (shouldWarn(code)) {\n        warned.add(code)\n        const msg =\n          'TTL caching without ttlAutopurge, max, or maxSize can ' +\n          'result in unbounded memory consumption.'\n        emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache)\n      }\n    }\n  }\n\n  /**\n   * Return the number of ms left in the item's TTL. If item is not in cache,\n   * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.\n   */\n  getRemainingTTL(key: K) {\n    return this.#keyMap.has(key) ? Infinity : 0\n  }\n\n  #initializeTTLTracking() {\n    const ttls = new ZeroArray(this.#max)\n    const starts = new ZeroArray(this.#max)\n    this.#ttls = ttls\n    this.#starts = starts\n\n    this.#setItemTTL = (index, ttl, start = perf.now()) => {\n      starts[index] = ttl !== 0 ? start : 0\n      ttls[index] = ttl\n      if (ttl !== 0 && this.ttlAutopurge) {\n        const t = setTimeout(() => {\n          if (this.#isStale(index)) {\n            this.#delete(this.#keyList[index] as K, 'expire')\n          }\n        }, ttl + 1)\n        // unref() not supported on all platforms\n        /* c8 ignore start */\n        if (t.unref) {\n          t.unref()\n        }\n        /* c8 ignore stop */\n      }\n    }\n\n    this.#updateItemAge = index => {\n      starts[index] = ttls[index] !== 0 ? perf.now() : 0\n    }\n\n    this.#statusTTL = (status, index) => {\n      if (ttls[index]) {\n        const ttl = ttls[index]\n        const start = starts[index]\n        /* c8 ignore next */\n        if (!ttl || !start) return\n        status.ttl = ttl\n        status.start = start\n        status.now = cachedNow || getNow()\n        const age = status.now - start\n        status.remainingTTL = ttl - age\n      }\n    }\n\n    // debounce calls to perf.now() to 1s so we're not hitting\n    // that costly call repeatedly.\n    let cachedNow = 0\n    const getNow = () => {\n      const n = perf.now()\n      if (this.ttlResolution > 0) {\n        cachedNow = n\n        const t = setTimeout(\n          () => (cachedNow = 0),\n          this.ttlResolution\n        )\n        // not available on all platforms\n        /* c8 ignore start */\n        if (t.unref) {\n          t.unref()\n        }\n        /* c8 ignore stop */\n      }\n      return n\n    }\n\n    this.getRemainingTTL = key => {\n      const index = this.#keyMap.get(key)\n      if (index === undefined) {\n        return 0\n      }\n      const ttl = ttls[index]\n      const start = starts[index]\n      if (!ttl || !start) {\n        return Infinity\n      }\n      const age = (cachedNow || getNow()) - start\n      return ttl - age\n    }\n\n    this.#isStale = index => {\n      const s = starts[index]\n      const t = ttls[index]\n      return !!t && !!s && (cachedNow || getNow()) - s > t\n    }\n  }\n\n  // conditionally set private methods related to TTL\n  #updateItemAge: (index: Index) => void = () => {}\n  #statusTTL: (status: LRUCache.Status<V>, index: Index) => void =\n    () => {}\n  #setItemTTL: (\n    index: Index,\n    ttl: LRUCache.Milliseconds,\n    start?: LRUCache.Milliseconds\n    // ignore because we never call this if we're not already in TTL mode\n    /* c8 ignore start */\n  ) => void = () => {}\n  /* c8 ignore stop */\n\n  #isStale: (index: Index) => boolean = () => false\n\n  #initializeSizeTracking() {\n    const sizes = new ZeroArray(this.#max)\n    this.#calculatedSize = 0\n    this.#sizes = sizes\n    this.#removeItemSize = index => {\n      this.#calculatedSize -= sizes[index] as number\n      sizes[index] = 0\n    }\n    this.#requireSize = (k, v, size, sizeCalculation) => {\n      // provisionally accept background fetches.\n      // actual value size will be checked when they return.\n      if (this.#isBackgroundFetch(v)) {\n        return 0\n      }\n      if (!isPosInt(size)) {\n        if (sizeCalculation) {\n          if (typeof sizeCalculation !== 'function') {\n            throw new TypeError('sizeCalculation must be a function')\n          }\n          size = sizeCalculation(v, k)\n          if (!isPosInt(size)) {\n            throw new TypeError(\n              'sizeCalculation return invalid (expect positive integer)'\n            )\n          }\n        } else {\n          throw new TypeError(\n            'invalid size value (must be positive integer). ' +\n              'When maxSize or maxEntrySize is used, sizeCalculation ' +\n              'or size must be set.'\n          )\n        }\n      }\n      return size\n    }\n    this.#addItemSize = (\n      index: Index,\n      size: LRUCache.Size,\n      status?: LRUCache.Status<V>\n    ) => {\n      sizes[index] = size\n      if (this.#maxSize) {\n        const maxSize = this.#maxSize - (sizes[index] as number)\n        while (this.#calculatedSize > maxSize) {\n          this.#evict(true)\n        }\n      }\n      this.#calculatedSize += sizes[index] as number\n      if (status) {\n        status.entrySize = size\n        status.totalCalculatedSize = this.#calculatedSize\n      }\n    }\n  }\n\n  #removeItemSize: (index: Index) => void = _i => {}\n  #addItemSize: (\n    index: Index,\n    size: LRUCache.Size,\n    status?: LRUCache.Status<V>\n  ) => void = (_i, _s, _st) => {}\n  #requireSize: (\n    k: K,\n    v: V | BackgroundFetch<V>,\n    size?: LRUCache.Size,\n    sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  ) => LRUCache.Size = (\n    _k: K,\n    _v: V | BackgroundFetch<V>,\n    size?: LRUCache.Size,\n    sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  ) => {\n    if (size || sizeCalculation) {\n      throw new TypeError(\n        'cannot set size without setting maxSize or maxEntrySize on cache'\n      )\n    }\n    return 0\n  };\n\n  *#indexes({ allowStale = this.allowStale } = {}) {\n    if (this.#size) {\n      for (let i = this.#tail; true; ) {\n        if (!this.#isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.#isStale(i)) {\n          yield i\n        }\n        if (i === this.#head) {\n          break\n        } else {\n          i = this.#prev[i] as Index\n        }\n      }\n    }\n  }\n\n  *#rindexes({ allowStale = this.allowStale } = {}) {\n    if (this.#size) {\n      for (let i = this.#head; true; ) {\n        if (!this.#isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.#isStale(i)) {\n          yield i\n        }\n        if (i === this.#tail) {\n          break\n        } else {\n          i = this.#next[i] as Index\n        }\n      }\n    }\n  }\n\n  #isValidIndex(index: Index) {\n    return (\n      index !== undefined &&\n      this.#keyMap.get(this.#keyList[index] as K) === index\n    )\n  }\n\n  /**\n   * Return a generator yielding `[key, value]` pairs,\n   * in order from most recently used to least recently used.\n   */\n  *entries() {\n    for (const i of this.#indexes()) {\n      if (\n        this.#valList[i] !== undefined &&\n        this.#keyList[i] !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield [this.#keyList[i], this.#valList[i]] as [K, V]\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.entries}\n   *\n   * Return a generator yielding `[key, value]` pairs,\n   * in order from least recently used to most recently used.\n   */\n  *rentries() {\n    for (const i of this.#rindexes()) {\n      if (\n        this.#valList[i] !== undefined &&\n        this.#keyList[i] !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield [this.#keyList[i], this.#valList[i]]\n      }\n    }\n  }\n\n  /**\n   * Return a generator yielding the keys in the cache,\n   * in order from most recently used to least recently used.\n   */\n  *keys() {\n    for (const i of this.#indexes()) {\n      const k = this.#keyList[i]\n      if (\n        k !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield k\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.keys}\n   *\n   * Return a generator yielding the keys in the cache,\n   * in order from least recently used to most recently used.\n   */\n  *rkeys() {\n    for (const i of this.#rindexes()) {\n      const k = this.#keyList[i]\n      if (\n        k !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield k\n      }\n    }\n  }\n\n  /**\n   * Return a generator yielding the values in the cache,\n   * in order from most recently used to least recently used.\n   */\n  *values() {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      if (\n        v !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield this.#valList[i] as V\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.values}\n   *\n   * Return a generator yielding the values in the cache,\n   * in order from least recently used to most recently used.\n   */\n  *rvalues() {\n    for (const i of this.#rindexes()) {\n      const v = this.#valList[i]\n      if (\n        v !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield this.#valList[i]\n      }\n    }\n  }\n\n  /**\n   * Iterating over the cache itself yields the same results as\n   * {@link LRUCache.entries}\n   */\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  /**\n   * A String value that is used in the creation of the default string\n   * description of an object. Called by the built-in method\n   * `Object.prototype.toString`.\n   */\n  [Symbol.toStringTag] = 'LRUCache'\n\n  /**\n   * Find a value for which the supplied fn method returns a truthy value,\n   * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.\n   */\n  find(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => boolean,\n    getOptions: LRUCache.GetOptions<K, V, FC> = {}\n  ) {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      if (fn(value, this.#keyList[i] as K, this)) {\n        return this.get(this.#keyList[i] as K, getOptions)\n      }\n    }\n  }\n\n  /**\n   * Call the supplied function on each item in the cache, in order from most\n   * recently used to least recently used.\n   *\n   * `fn` is called as `fn(value, key, cache)`.\n   *\n   * If `thisp` is provided, function will be called in the `this`-context of\n   * the provided object, or the cache if no `thisp` object is provided.\n   *\n   * Does not update age or recenty of use, or iterate over stale values.\n   */\n  forEach(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => any,\n    thisp: any = this\n  ) {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.#keyList[i] as K, this)\n    }\n  }\n\n  /**\n   * The same as {@link LRUCache.forEach} but items are iterated over in\n   * reverse order.  (ie, less recently used items are iterated over first.)\n   */\n  rforEach(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => any,\n    thisp: any = this\n  ) {\n    for (const i of this.#rindexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.#keyList[i] as K, this)\n    }\n  }\n\n  /**\n   * Delete any stale entries. Returns true if anything was removed,\n   * false otherwise.\n   */\n  purgeStale() {\n    let deleted = false\n    for (const i of this.#rindexes({ allowStale: true })) {\n      if (this.#isStale(i)) {\n        this.#delete(this.#keyList[i] as K, 'expire')\n        deleted = true\n      }\n    }\n    return deleted\n  }\n\n  /**\n   * Get the extended info about a given entry, to get its value, size, and\n   * TTL info simultaneously. Returns `undefined` if the key is not present.\n   *\n   * Unlike {@link LRUCache#dump}, which is designed to be portable and survive\n   * serialization, the `start` value is always the current timestamp, and the\n   * `ttl` is a calculated remaining time to live (negative if expired).\n   *\n   * Always returns stale values, if their info is found in the cache, so be\n   * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})\n   * if relevant.\n   */\n  info(key: K): LRUCache.Entry<V> | undefined {\n    const i = this.#keyMap.get(key)\n    if (i === undefined) return undefined\n    const v = this.#valList[i]\n    const value: V | undefined = this.#isBackgroundFetch(v)\n      ? v.__staleWhileFetching\n      : v\n    if (value === undefined) return undefined\n    const entry: LRUCache.Entry<V> = { value }\n    if (this.#ttls && this.#starts) {\n      const ttl = this.#ttls[i]\n      const start = this.#starts[i]\n      if (ttl && start) {\n        const remain = ttl - (perf.now() - start)\n        entry.ttl = remain\n        entry.start = Date.now()\n      }\n    }\n    if (this.#sizes) {\n      entry.size = this.#sizes[i]\n    }\n    return entry\n  }\n\n  /**\n   * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n   * passed to {@link LRLUCache#load}.\n   *\n   * The `start` fields are calculated relative to a portable `Date.now()`\n   * timestamp, even if `performance.now()` is available.\n   *\n   * Stale entries are always included in the `dump`, even if\n   * {@link LRUCache.OptionsBase.allowStale} is false.\n   *\n   * Note: this returns an actual array, not a generator, so it can be more\n   * easily passed around.\n   */\n  dump() {\n    const arr: [K, LRUCache.Entry<V>][] = []\n    for (const i of this.#indexes({ allowStale: true })) {\n      const key = this.#keyList[i]\n      const v = this.#valList[i]\n      const value: V | undefined = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined || key === undefined) continue\n      const entry: LRUCache.Entry<V> = { value }\n      if (this.#ttls && this.#starts) {\n        entry.ttl = this.#ttls[i]\n        // always dump the start relative to a portable timestamp\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = perf.now() - (this.#starts[i] as number)\n        entry.start = Math.floor(Date.now() - age)\n      }\n      if (this.#sizes) {\n        entry.size = this.#sizes[i]\n      }\n      arr.unshift([key, entry])\n    }\n    return arr\n  }\n\n  /**\n   * Reset the cache and load in the items in entries in the order listed.\n   *\n   * The shape of the resulting cache may be different if the same options are\n   * not used in both caches.\n   *\n   * The `start` fields are assumed to be calculated relative to a portable\n   * `Date.now()` timestamp, even if `performance.now()` is available.\n   */\n  load(arr: [K, LRUCache.Entry<V>][]) {\n    this.clear()\n    for (const [key, entry] of arr) {\n      if (entry.start) {\n        // entry.start is a portable timestamp, but we may be using\n        // node's performance.now(), so calculate the offset, so that\n        // we get the intended remaining TTL, no matter how long it's\n        // been on ice.\n        //\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = Date.now() - entry.start\n        entry.start = perf.now() - age\n      }\n      this.set(key, entry.value, entry)\n    }\n  }\n\n  /**\n   * Add a value to the cache.\n   *\n   * Note: if `undefined` is specified as a value, this is an alias for\n   * {@link LRUCache#delete}\n   *\n   * Fields on the {@link LRUCache.SetOptions} options param will override\n   * their corresponding values in the constructor options for the scope\n   * of this single `set()` operation.\n   *\n   * If `start` is provided, then that will set the effective start\n   * time for the TTL calculation. Note that this must be a previous\n   * value of `performance.now()` if supported, or a previous value of\n   * `Date.now()` if not.\n   *\n   * Options object may also include `size`, which will prevent\n   * calling the `sizeCalculation` function and just use the specified\n   * number if it is a positive integer, and `noDisposeOnSet` which\n   * will prevent calling a `dispose` function in the case of\n   * overwrites.\n   *\n   * If the `size` (or return value of `sizeCalculation`) for a given\n   * entry is greater than `maxEntrySize`, then the item will not be\n   * added to the cache.\n   *\n   * Will update the recency of the entry.\n   *\n   * If the value is `undefined`, then this is an alias for\n   * `cache.delete(key)`. `undefined` is never stored in the cache.\n   */\n  set(\n    k: K,\n    v: V | BackgroundFetch<V> | undefined,\n    setOptions: LRUCache.SetOptions<K, V, FC> = {}\n  ) {\n    if (v === undefined) {\n      this.delete(k)\n      return this\n    }\n    const {\n      ttl = this.ttl,\n      start,\n      noDisposeOnSet = this.noDisposeOnSet,\n      sizeCalculation = this.sizeCalculation,\n      status,\n    } = setOptions\n    let { noUpdateTTL = this.noUpdateTTL } = setOptions\n\n    const size = this.#requireSize(\n      k,\n      v,\n      setOptions.size || 0,\n      sizeCalculation\n    )\n    // if the item doesn't fit, don't do anything\n    // NB: maxEntrySize set to maxSize by default\n    if (this.maxEntrySize && size > this.maxEntrySize) {\n      if (status) {\n        status.set = 'miss'\n        status.maxEntrySizeExceeded = true\n      }\n      // have to delete, in case something is there already.\n      this.#delete(k, 'set')\n      return this\n    }\n    let index = this.#size === 0 ? undefined : this.#keyMap.get(k)\n    if (index === undefined) {\n      // addition\n      index = (\n        this.#size === 0\n          ? this.#tail\n          : this.#free.length !== 0\n          ? this.#free.pop()\n          : this.#size === this.#max\n          ? this.#evict(false)\n          : this.#size\n      ) as Index\n      this.#keyList[index] = k\n      this.#valList[index] = v\n      this.#keyMap.set(k, index)\n      this.#next[this.#tail] = index\n      this.#prev[index] = this.#tail\n      this.#tail = index\n      this.#size++\n      this.#addItemSize(index, size, status)\n      if (status) status.set = 'add'\n      noUpdateTTL = false\n    } else {\n      // update\n      this.#moveToTail(index)\n      const oldVal = this.#valList[index] as V | BackgroundFetch<V>\n      if (v !== oldVal) {\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n          oldVal.__abortController.abort(new Error('replaced'))\n          const { __staleWhileFetching: s } = oldVal\n          if (s !== undefined && !noDisposeOnSet) {\n            if (this.#hasDispose) {\n              this.#dispose?.(s as V, k, 'set')\n            }\n            if (this.#hasDisposeAfter) {\n              this.#disposed?.push([s as V, k, 'set'])\n            }\n          }\n        } else if (!noDisposeOnSet) {\n          if (this.#hasDispose) {\n            this.#dispose?.(oldVal as V, k, 'set')\n          }\n          if (this.#hasDisposeAfter) {\n            this.#disposed?.push([oldVal as V, k, 'set'])\n          }\n        }\n        this.#removeItemSize(index)\n        this.#addItemSize(index, size, status)\n        this.#valList[index] = v\n        if (status) {\n          status.set = 'replace'\n          const oldValue =\n            oldVal && this.#isBackgroundFetch(oldVal)\n              ? oldVal.__staleWhileFetching\n              : oldVal\n          if (oldValue !== undefined) status.oldValue = oldValue\n        }\n      } else if (status) {\n        status.set = 'update'\n      }\n    }\n    if (ttl !== 0 && !this.#ttls) {\n      this.#initializeTTLTracking()\n    }\n    if (this.#ttls) {\n      if (!noUpdateTTL) {\n        this.#setItemTTL(index, ttl, start)\n      }\n      if (status) this.#statusTTL(status, index)\n    }\n    if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n    return this\n  }\n\n  /**\n   * Evict the least recently used item, returning its value or\n   * `undefined` if cache is empty.\n   */\n  pop(): V | undefined {\n    try {\n      while (this.#size) {\n        const val = this.#valList[this.#head]\n        this.#evict(true)\n        if (this.#isBackgroundFetch(val)) {\n          if (val.__staleWhileFetching) {\n            return val.__staleWhileFetching\n          }\n        } else if (val !== undefined) {\n          return val\n        }\n      }\n    } finally {\n      if (this.#hasDisposeAfter && this.#disposed) {\n        const dt = this.#disposed\n        let task: DisposeTask<K, V> | undefined\n        while ((task = dt?.shift())) {\n          this.#disposeAfter?.(...task)\n        }\n      }\n    }\n  }\n\n  #evict(free: boolean) {\n    const head = this.#head\n    const k = this.#keyList[head] as K\n    const v = this.#valList[head] as V\n    if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n      v.__abortController.abort(new Error('evicted'))\n    } else if (this.#hasDispose || this.#hasDisposeAfter) {\n      if (this.#hasDispose) {\n        this.#dispose?.(v, k, 'evict')\n      }\n      if (this.#hasDisposeAfter) {\n        this.#disposed?.push([v, k, 'evict'])\n      }\n    }\n    this.#removeItemSize(head)\n    // if we aren't about to use the index, then null these out\n    if (free) {\n      this.#keyList[head] = undefined\n      this.#valList[head] = undefined\n      this.#free.push(head)\n    }\n    if (this.#size === 1) {\n      this.#head = this.#tail = 0 as Index\n      this.#free.length = 0\n    } else {\n      this.#head = this.#next[head] as Index\n    }\n    this.#keyMap.delete(k)\n    this.#size--\n    return head\n  }\n\n  /**\n   * Check if a key is in the cache, without updating the recency of use.\n   * Will return false if the item is stale, even though it is technically\n   * in the cache.\n   *\n   * Check if a key is in the cache, without updating the recency of\n   * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set\n   * to `true` in either the options or the constructor.\n   *\n   * Will return `false` if the item is stale, even though it is technically in\n   * the cache. The difference can be determined (if it matters) by using a\n   * `status` argument, and inspecting the `has` field.\n   *\n   * Will not update item age unless\n   * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n   */\n  has(k: K, hasOptions: LRUCache.HasOptions<K, V, FC> = {}) {\n    const { updateAgeOnHas = this.updateAgeOnHas, status } =\n      hasOptions\n    const index = this.#keyMap.get(k)\n    if (index !== undefined) {\n      const v = this.#valList[index]\n      if (\n        this.#isBackgroundFetch(v) &&\n        v.__staleWhileFetching === undefined\n      ) {\n        return false\n      }\n      if (!this.#isStale(index)) {\n        if (updateAgeOnHas) {\n          this.#updateItemAge(index)\n        }\n        if (status) {\n          status.has = 'hit'\n          this.#statusTTL(status, index)\n        }\n        return true\n      } else if (status) {\n        status.has = 'stale'\n        this.#statusTTL(status, index)\n      }\n    } else if (status) {\n      status.has = 'miss'\n    }\n    return false\n  }\n\n  /**\n   * Like {@link LRUCache#get} but doesn't update recency or delete stale\n   * items.\n   *\n   * Returns `undefined` if the item is stale, unless\n   * {@link LRUCache.OptionsBase.allowStale} is set.\n   */\n  peek(k: K, peekOptions: LRUCache.PeekOptions<K, V, FC> = {}) {\n    const { allowStale = this.allowStale } = peekOptions\n    const index = this.#keyMap.get(k)\n    if (\n      index === undefined ||\n      (!allowStale && this.#isStale(index))\n    ) {\n      return\n    }\n    const v = this.#valList[index]\n    // either stale and allowed, or forcing a refresh of non-stale value\n    return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v\n  }\n\n  #backgroundFetch(\n    k: K,\n    index: Index | undefined,\n    options: LRUCache.FetchOptions<K, V, FC>,\n    context: any\n  ): BackgroundFetch<V> {\n    const v = index === undefined ? undefined : this.#valList[index]\n    if (this.#isBackgroundFetch(v)) {\n      return v\n    }\n\n    const ac = new AC()\n    const { signal } = options\n    // when/if our AC signals, then stop listening to theirs.\n    signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n      signal: ac.signal,\n    })\n\n    const fetchOpts = {\n      signal: ac.signal,\n      options,\n      context,\n    }\n\n    const cb = (\n      v: V | undefined,\n      updateCache = false\n    ): V | undefined => {\n      const { aborted } = ac.signal\n      const ignoreAbort = options.ignoreFetchAbort && v !== undefined\n      if (options.status) {\n        if (aborted && !updateCache) {\n          options.status.fetchAborted = true\n          options.status.fetchError = ac.signal.reason\n          if (ignoreAbort) options.status.fetchAbortIgnored = true\n        } else {\n          options.status.fetchResolved = true\n        }\n      }\n      if (aborted && !ignoreAbort && !updateCache) {\n        return fetchFail(ac.signal.reason)\n      }\n      // either we didn't abort, and are still here, or we did, and ignored\n      const bf = p as BackgroundFetch<V>\n      if (this.#valList[index as Index] === p) {\n        if (v === undefined) {\n          if (bf.__staleWhileFetching) {\n            this.#valList[index as Index] = bf.__staleWhileFetching\n          } else {\n            this.#delete(k, 'fetch')\n          }\n        } else {\n          if (options.status) options.status.fetchUpdated = true\n          this.set(k, v, fetchOpts.options)\n        }\n      }\n      return v\n    }\n\n    const eb = (er: any) => {\n      if (options.status) {\n        options.status.fetchRejected = true\n        options.status.fetchError = er\n      }\n      return fetchFail(er)\n    }\n\n    const fetchFail = (er: any): V | undefined => {\n      const { aborted } = ac.signal\n      const allowStaleAborted =\n        aborted && options.allowStaleOnFetchAbort\n      const allowStale =\n        allowStaleAborted || options.allowStaleOnFetchRejection\n      const noDelete = allowStale || options.noDeleteOnFetchRejection\n      const bf = p as BackgroundFetch<V>\n      if (this.#valList[index as Index] === p) {\n        // if we allow stale on fetch rejections, then we need to ensure that\n        // the stale value is not removed from the cache when the fetch fails.\n        const del = !noDelete || bf.__staleWhileFetching === undefined\n        if (del) {\n          this.#delete(k, 'fetch')\n        } else if (!allowStaleAborted) {\n          // still replace the *promise* with the stale value,\n          // since we are done with the promise at this point.\n          // leave it untouched if we're still waiting for an\n          // aborted background fetch that hasn't yet returned.\n          this.#valList[index as Index] = bf.__staleWhileFetching\n        }\n      }\n      if (allowStale) {\n        if (options.status && bf.__staleWhileFetching !== undefined) {\n          options.status.returnedStale = true\n        }\n        return bf.__staleWhileFetching\n      } else if (bf.__returned === bf) {\n        throw er\n      }\n    }\n\n    const pcall = (\n      res: (v: V | undefined) => void,\n      rej: (e: any) => void\n    ) => {\n      const fmp = this.#fetchMethod?.(k, v, fetchOpts)\n      if (fmp && fmp instanceof Promise) {\n        fmp.then(v => res(v === undefined ? undefined : v), rej)\n      }\n      // ignored, we go until we finish, regardless.\n      // defer check until we are actually aborting,\n      // so fetchMethod can override.\n      ac.signal.addEventListener('abort', () => {\n        if (\n          !options.ignoreFetchAbort ||\n          options.allowStaleOnFetchAbort\n        ) {\n          res(undefined)\n          // when it eventually resolves, update the cache.\n          if (options.allowStaleOnFetchAbort) {\n            res = v => cb(v, true)\n          }\n        }\n      })\n    }\n\n    if (options.status) options.status.fetchDispatched = true\n    const p = new Promise(pcall).then(cb, eb)\n    const bf: BackgroundFetch<V> = Object.assign(p, {\n      __abortController: ac,\n      __staleWhileFetching: v,\n      __returned: undefined,\n    })\n\n    if (index === undefined) {\n      // internal, don't expose status.\n      this.set(k, bf, { ...fetchOpts.options, status: undefined })\n      index = this.#keyMap.get(k)\n    } else {\n      this.#valList[index] = bf\n    }\n    return bf\n  }\n\n  #isBackgroundFetch(p: any): p is BackgroundFetch<V> {\n    if (!this.#hasFetchMethod) return false\n    const b = p as BackgroundFetch<V>\n    return (\n      !!b &&\n      b instanceof Promise &&\n      b.hasOwnProperty('__staleWhileFetching') &&\n      b.__abortController instanceof AC\n    )\n  }\n\n  /**\n   * Make an asynchronous cached fetch using the\n   * {@link LRUCache.OptionsBase.fetchMethod} function.\n   *\n   * If the value is in the cache and not stale, then the returned\n   * Promise resolves to the value.\n   *\n   * If not in the cache, or beyond its TTL staleness, then\n   * `fetchMethod(key, staleValue, { options, signal, context })` is\n   * called, and the value returned will be added to the cache once\n   * resolved.\n   *\n   * If called with `allowStale`, and an asynchronous fetch is\n   * currently in progress to reload a stale value, then the former\n   * stale value will be returned.\n   *\n   * If called with `forceRefresh`, then the cached item will be\n   * re-fetched, even if it is not stale. However, if `allowStale` is also\n   * set, then the old value will still be returned. This is useful\n   * in cases where you want to force a reload of a cached value. If\n   * a background fetch is already in progress, then `forceRefresh`\n   * has no effect.\n   *\n   * If multiple fetches for the same key are issued, then they will all be\n   * coalesced into a single call to fetchMethod.\n   *\n   * Note that this means that handling options such as\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort},\n   * {@link LRUCache.FetchOptions.signal},\n   * and {@link LRUCache.OptionsBase.allowStaleOnFetchRejection} will be\n   * determined by the FIRST fetch() call for a given key.\n   *\n   * This is a known (fixable) shortcoming which will be addresed on when\n   * someone complains about it, as the fix would involve added complexity and\n   * may not be worth the costs for this edge case.\n   *\n   * If {@link LRUCache.OptionsBase.fetchMethod} is not specified, then this is\n   * effectively an alias for `Promise.resolve(cache.get(key))`.\n   *\n   * When the fetch method resolves to a value, if the fetch has not\n   * been aborted due to deletion, eviction, or being overwritten,\n   * then it is added to the cache using the options provided.\n   *\n   * If the key is evicted or deleted before the `fetchMethod`\n   * resolves, then the AbortSignal passed to the `fetchMethod` will\n   * receive an `abort` event, and the promise returned by `fetch()`\n   * will reject with the reason for the abort.\n   *\n   * If a `signal` is passed to the `fetch()` call, then aborting the\n   * signal will abort the fetch and cause the `fetch()` promise to\n   * reject with the reason provided.\n   *\n   * **Setting `context`**\n   *\n   * If an `FC` type is set to a type other than `unknown`, `void`, or\n   * `undefined` in the {@link LRUCache} constructor, then all\n   * calls to `cache.fetch()` _must_ provide a `context` option. If\n   * set to `undefined` or `void`, then calls to fetch _must not_\n   * provide a `context` option.\n   *\n   * The `context` param allows you to provide arbitrary data that\n   * might be relevant in the course of fetching the data. It is only\n   * relevant for the course of a single `fetch()` operation, and\n   * discarded afterwards.\n   *\n   * **Note: `fetch()` calls are inflight-unique**\n   *\n   * If you call `fetch()` multiple times with the same key value,\n   * then every call after the first will resolve on the same\n   * promise<sup>1</sup>,\n   * _even if they have different settings that would otherwise change\n   * the behavior of the fetch_, such as `noDeleteOnFetchRejection`\n   * or `ignoreFetchAbort`.\n   *\n   * In most cases, this is not a problem (in fact, only fetching\n   * something once is what you probably want, if you're caching in\n   * the first place). If you are changing the fetch() options\n   * dramatically between runs, there's a good chance that you might\n   * be trying to fit divergent semantics into a single object, and\n   * would be better off with multiple cache instances.\n   *\n   * **1**: Ie, they're not the \"same Promise\", but they resolve at\n   * the same time, because they're both waiting on the same\n   * underlying fetchMethod response.\n   */\n\n  fetch(\n    k: K,\n    fetchOptions: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : LRUCache.FetchOptionsWithContext<K, V, FC>\n  ): Promise<undefined | V>\n\n  // this overload not allowed if context is required\n  fetch(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    fetchOptions?: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : never\n  ): Promise<undefined | V>\n\n  async fetch(\n    k: K,\n    fetchOptions: LRUCache.FetchOptions<K, V, FC> = {}\n  ): Promise<undefined | V> {\n    const {\n      // get options\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      // set options\n      ttl = this.ttl,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      // fetch exclusive options\n      noDeleteOnFetchRejection = this.noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection = this.allowStaleOnFetchRejection,\n      ignoreFetchAbort = this.ignoreFetchAbort,\n      allowStaleOnFetchAbort = this.allowStaleOnFetchAbort,\n      context,\n      forceRefresh = false,\n      status,\n      signal,\n    } = fetchOptions\n\n    if (!this.#hasFetchMethod) {\n      if (status) status.fetch = 'get'\n      return this.get(k, {\n        allowStale,\n        updateAgeOnGet,\n        noDeleteOnStaleGet,\n        status,\n      })\n    }\n\n    const options = {\n      allowStale,\n      updateAgeOnGet,\n      noDeleteOnStaleGet,\n      ttl,\n      noDisposeOnSet,\n      size,\n      sizeCalculation,\n      noUpdateTTL,\n      noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n      status,\n      signal,\n    }\n\n    let index = this.#keyMap.get(k)\n    if (index === undefined) {\n      if (status) status.fetch = 'miss'\n      const p = this.#backgroundFetch(k, index, options, context)\n      return (p.__returned = p)\n    } else {\n      // in cache, maybe already fetching\n      const v = this.#valList[index]\n      if (this.#isBackgroundFetch(v)) {\n        const stale =\n          allowStale && v.__staleWhileFetching !== undefined\n        if (status) {\n          status.fetch = 'inflight'\n          if (stale) status.returnedStale = true\n        }\n        return stale ? v.__staleWhileFetching : (v.__returned = v)\n      }\n\n      // if we force a refresh, that means do NOT serve the cached value,\n      // unless we are already in the process of refreshing the cache.\n      const isStale = this.#isStale(index)\n      if (!forceRefresh && !isStale) {\n        if (status) status.fetch = 'hit'\n        this.#moveToTail(index)\n        if (updateAgeOnGet) {\n          this.#updateItemAge(index)\n        }\n        if (status) this.#statusTTL(status, index)\n        return v\n      }\n\n      // ok, it is stale or a forced refresh, and not already fetching.\n      // refresh the cache.\n      const p = this.#backgroundFetch(k, index, options, context)\n      const hasStale = p.__staleWhileFetching !== undefined\n      const staleVal = hasStale && allowStale\n      if (status) {\n        status.fetch = isStale ? 'stale' : 'refresh'\n        if (staleVal && isStale) status.returnedStale = true\n      }\n      return staleVal ? p.__staleWhileFetching : (p.__returned = p)\n    }\n  }\n\n  /**\n   * In some cases, `cache.fetch()` may resolve to `undefined`, either because\n   * a {@link LRUCache.OptionsBase#fetchMethod} was not provided (turning\n   * `cache.fetch(k)` into just an async wrapper around `cache.get(k)`) or\n   * because `ignoreFetchAbort` was specified (either to the constructor or\n   * in the {@link LRUCache.FetchOptions}). Also, the\n   * {@link OptionsBase.fetchMethod} may return `undefined` or `void`, making\n   * the test even more complicated.\n   *\n   * Because inferring the cases where `undefined` might be returned are so\n   * cumbersome, but testing for `undefined` can also be annoying, this method\n   * can be used, which will reject if `this.fetch()` resolves to undefined.\n   */\n  forceFetch(\n    k: K,\n    fetchOptions: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : LRUCache.FetchOptionsWithContext<K, V, FC>\n  ): Promise<V>\n  // this overload not allowed if context is required\n  forceFetch(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    fetchOptions?: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : never\n  ): Promise<V>\n  async forceFetch(\n    k: K,\n    fetchOptions: LRUCache.FetchOptions<K, V, FC> = {}\n  ): Promise<V> {\n    const v = await this.fetch(\n      k,\n      fetchOptions as unknown extends FC\n        ? LRUCache.FetchOptions<K, V, FC>\n        : FC extends undefined | void\n        ? LRUCache.FetchOptionsNoContext<K, V>\n        : LRUCache.FetchOptionsWithContext<K, V, FC>\n    )\n    if (v === undefined) throw new Error('fetch() returned undefined')\n    return v\n  }\n\n  /**\n   * If the key is found in the cache, then this is equivalent to\n   * {@link LRUCache#get}. If not, in the cache, then calculate the value using\n   * the {@link LRUCache.OptionsBase.memoMethod}, and add it to the cache.\n   *\n   * If an `FC` type is set to a type other than `unknown`, `void`, or\n   * `undefined` in the LRUCache constructor, then all calls to `cache.memo()`\n   * _must_ provide a `context` option. If set to `undefined` or `void`, then\n   * calls to memo _must not_ provide a `context` option.\n   *\n   * The `context` param allows you to provide arbitrary data that might be\n   * relevant in the course of fetching the data. It is only relevant for the\n   * course of a single `memo()` operation, and discarded afterwards.\n   */\n  memo(\n    k: K,\n    memoOptions: unknown extends FC\n      ? LRUCache.MemoOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.MemoOptionsNoContext<K, V>\n      : LRUCache.MemoOptionsWithContext<K, V, FC>\n  ): V\n  // this overload not allowed if context is required\n  memo(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    memoOptions?: unknown extends FC\n      ? LRUCache.MemoOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.MemoOptionsNoContext<K, V>\n      : never\n  ): V\n  memo(k: K, memoOptions: LRUCache.MemoOptions<K, V, FC> = {}) {\n    const memoMethod = this.#memoMethod\n    if (!memoMethod) {\n      throw new Error('no memoMethod provided to constructor')\n    }\n    const { context, forceRefresh, ...options } = memoOptions\n    const v = this.get(k, options)\n    if (!forceRefresh && v !== undefined) return v\n    const vv = memoMethod(k, v, {\n      options,\n      context,\n    } as LRUCache.MemoizerOptions<K, V, FC>)\n    this.set(k, vv, options)\n    return vv\n  }\n\n  /**\n   * Return a value from the cache. Will update the recency of the cache\n   * entry found.\n   *\n   * If the key is not found, get() will return `undefined`.\n   */\n  get(k: K, getOptions: LRUCache.GetOptions<K, V, FC> = {}) {\n    const {\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      status,\n    } = getOptions\n    const index = this.#keyMap.get(k)\n    if (index !== undefined) {\n      const value = this.#valList[index]\n      const fetching = this.#isBackgroundFetch(value)\n      if (status) this.#statusTTL(status, index)\n      if (this.#isStale(index)) {\n        if (status) status.get = 'stale'\n        // delete only if not an in-flight background fetch\n        if (!fetching) {\n          if (!noDeleteOnStaleGet) {\n            this.#delete(k, 'expire')\n          }\n          if (status && allowStale) status.returnedStale = true\n          return allowStale ? value : undefined\n        } else {\n          if (\n            status &&\n            allowStale &&\n            value.__staleWhileFetching !== undefined\n          ) {\n            status.returnedStale = true\n          }\n          return allowStale ? value.__staleWhileFetching : undefined\n        }\n      } else {\n        if (status) status.get = 'hit'\n        // if we're currently fetching it, we don't actually have it yet\n        // it's not stale, which means this isn't a staleWhileRefetching.\n        // If it's not stale, and fetching, AND has a __staleWhileFetching\n        // value, then that means the user fetched with {forceRefresh:true},\n        // so it's safe to return that value.\n        if (fetching) {\n          return value.__staleWhileFetching\n        }\n        this.#moveToTail(index)\n        if (updateAgeOnGet) {\n          this.#updateItemAge(index)\n        }\n        return value\n      }\n    } else if (status) {\n      status.get = 'miss'\n    }\n  }\n\n  #connect(p: Index, n: Index) {\n    this.#prev[n] = p\n    this.#next[p] = n\n  }\n\n  #moveToTail(index: Index): void {\n    // if tail already, nothing to do\n    // if head, move head to next[index]\n    // else\n    //   move next[prev[index]] to next[index] (head has no prev)\n    //   move prev[next[index]] to prev[index]\n    // prev[index] = tail\n    // next[tail] = index\n    // tail = index\n    if (index !== this.#tail) {\n      if (index === this.#head) {\n        this.#head = this.#next[index] as Index\n      } else {\n        this.#connect(\n          this.#prev[index] as Index,\n          this.#next[index] as Index\n        )\n      }\n      this.#connect(this.#tail, index)\n      this.#tail = index\n    }\n  }\n\n  /**\n   * Deletes a key out of the cache.\n   *\n   * Returns true if the key was deleted, false otherwise.\n   */\n  delete(k: K) {\n    return this.#delete(k, 'delete')\n  }\n\n  #delete(k: K, reason: LRUCache.DisposeReason) {\n    let deleted = false\n    if (this.#size !== 0) {\n      const index = this.#keyMap.get(k)\n      if (index !== undefined) {\n        deleted = true\n        if (this.#size === 1) {\n          this.#clear(reason)\n        } else {\n          this.#removeItemSize(index)\n          const v = this.#valList[index]\n          if (this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('deleted'))\n          } else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n              this.#dispose?.(v as V, k, reason)\n            }\n            if (this.#hasDisposeAfter) {\n              this.#disposed?.push([v as V, k, reason])\n            }\n          }\n          this.#keyMap.delete(k)\n          this.#keyList[index] = undefined\n          this.#valList[index] = undefined\n          if (index === this.#tail) {\n            this.#tail = this.#prev[index] as Index\n          } else if (index === this.#head) {\n            this.#head = this.#next[index] as Index\n          } else {\n            const pi = this.#prev[index] as number\n            this.#next[pi] = this.#next[index] as number\n            const ni = this.#next[index] as number\n            this.#prev[ni] = this.#prev[index] as number\n          }\n          this.#size--\n          this.#free.push(index)\n        }\n      }\n    }\n    if (this.#hasDisposeAfter && this.#disposed?.length) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n    return deleted\n  }\n\n  /**\n   * Clear the cache entirely, throwing away all values.\n   */\n  clear() {\n    return this.#clear('delete')\n  }\n  #clear(reason: LRUCache.DisposeReason) {\n    for (const index of this.#rindexes({ allowStale: true })) {\n      const v = this.#valList[index]\n      if (this.#isBackgroundFetch(v)) {\n        v.__abortController.abort(new Error('deleted'))\n      } else {\n        const k = this.#keyList[index]\n        if (this.#hasDispose) {\n          this.#dispose?.(v as V, k as K, reason)\n        }\n        if (this.#hasDisposeAfter) {\n          this.#disposed?.push([v as V, k as K, reason])\n        }\n      }\n    }\n\n    this.#keyMap.clear()\n    this.#valList.fill(undefined)\n    this.#keyList.fill(undefined)\n    if (this.#ttls && this.#starts) {\n      this.#ttls.fill(0)\n      this.#starts.fill(0)\n    }\n    if (this.#sizes) {\n      this.#sizes.fill(0)\n    }\n    this.#head = 0 as Index\n    this.#tail = 0 as Index\n    this.#free.length = 0\n    this.#calculatedSize = 0\n    this.#size = 0\n    if (this.#hasDisposeAfter && this.#disposed) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n  }\n}\n"], "mappings": ";;;AAMA,IAAM,OACJ,OAAO,gBAAgB,YACvB,eACA,OAAO,YAAY,QAAQ,aACvB,cACA;AAEN,IAAM,SAAS,oBAAI,IAAG;AAMtB,IAAM,UACJ,OAAO,YAAY,YAAY,CAAC,CAAC,UAAU,UAAU,CAAA;AAIvD,IAAM,cAAc,CAClB,KACA,MACA,MACA,OACE;AACF,SAAO,QAAQ,gBAAgB,aAC3B,QAAQ,YAAY,KAAK,MAAM,MAAM,EAAE,IACvC,QAAQ,MAAM,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG,EAAE;AAC/C;AAEA,IAAI,KAAK,WAAW;AACpB,IAAI,KAAK,WAAW;AAGpB,IAAI,OAAO,OAAO,aAAa;AAE7B,OAAK,MAAM,YAAW;IACpB;IACA,WAAqC,CAAA;IACrC;IACA,UAAmB;IACnB,iBAAiB,GAAW,IAAwB;AAClD,WAAK,SAAS,KAAK,EAAE;IACvB;;AAGF,OAAK,MAAM,gBAAe;IACxB,cAAA;AACE,qBAAc;IAChB;IACA,SAAS,IAAI,GAAE;IACf,MAAM,QAAW;AACf,UAAI,KAAK,OAAO;AAAS;AAEzB,WAAK,OAAO,SAAS;AAErB,WAAK,OAAO,UAAU;AAEtB,iBAAW,MAAM,KAAK,OAAO,UAAU;AACrC,WAAG,MAAM;;AAEX,WAAK,OAAO,UAAU,MAAM;IAC9B;;AAEF,MAAI,yBACF,QAAQ,KAAK,gCAAgC;AAC/C,QAAM,iBAAiB,MAAK;AAC1B,QAAI,CAAC;AAAwB;AAC7B,6BAAyB;AACzB,gBACE,oaAOA,uBACA,WACA,cAAc;EAElB;;AAIF,IAAM,aAAa,CAAC,SAAiB,CAAC,OAAO,IAAI,IAAI;AAErD,IAAM,OAAO,OAAO,MAAM;AAI1B,IAAM,WAAW,CAAC,MAChB,KAAK,MAAM,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,SAAS,CAAC;AAcjD,IAAM,eAAe,CAAC,QACpB,CAAC,SAAS,GAAG,IACT,OACA,OAAO,KAAK,IAAI,GAAG,CAAC,IACpB,aACA,OAAO,KAAK,IAAI,GAAG,EAAE,IACrB,cACA,OAAO,KAAK,IAAI,GAAG,EAAE,IACrB,cACA,OAAO,OAAO,mBACd,YACA;AAGN,IAAM,YAAN,cAAwB,MAAa;EACnC,YAAY,MAAY;AACtB,UAAM,IAAI;AACV,SAAK,KAAK,CAAC;EACb;;AAMF,IAAM,QAAN,MAAM,OAAK;EACT;EACA;;EAEA,OAAO,gBAAyB;EAChC,OAAO,OAAO,KAAW;AACvB,UAAM,UAAU,aAAa,GAAG;AAChC,QAAI,CAAC;AAAS,aAAO,CAAA;AACrB,WAAM,gBAAgB;AACtB,UAAM,IAAI,IAAI,OAAM,KAAK,OAAO;AAChC,WAAM,gBAAgB;AACtB,WAAO;EACT;EACA,YACE,KACA,SAAyC;AAGzC,QAAI,CAAC,OAAM,eAAe;AACxB,YAAM,IAAI,UAAU,yCAAyC;;AAG/D,SAAK,OAAO,IAAI,QAAQ,GAAG;AAC3B,SAAK,SAAS;EAChB;EACA,KAAK,GAAQ;AACX,SAAK,KAAK,KAAK,QAAQ,IAAI;EAC7B;EACA,MAAG;AACD,WAAO,KAAK,KAAK,EAAE,KAAK,MAAM;EAChC;;AAu7BI,IAAO,WAAP,MAAO,UAAQ;;EAIV;EACA;EACA;EACA;EACA;EACA;;;;EAKT;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;;;;;;;;;;EAWA,OAAO,sBAIL,GAAqB;AACrB,WAAO;;MAEL,QAAQ,EAAE;MACV,MAAM,EAAE;MACR,OAAO,EAAE;MACT,QAAQ,EAAE;MACV,SAAS,EAAE;MACX,SAAS,EAAE;MACX,MAAM,EAAE;MACR,MAAM,EAAE;MACR,IAAI,OAAI;AACN,eAAO,EAAE;MACX;MACA,IAAI,OAAI;AACN,eAAO,EAAE;MACX;MACA,MAAM,EAAE;;MAER,mBAAmB,CAAC,MAAW,EAAE,mBAAmB,CAAC;MACrD,iBAAiB,CACf,GACA,OACA,SACA,YAEA,EAAE,iBACA,GACA,OACA,SACA,OAAO;MAEX,YAAY,CAAC,UACX,EAAE,YAAY,KAAc;MAC9B,SAAS,CAAC,YACR,EAAE,SAAS,OAAO;MACpB,UAAU,CAAC,YACT,EAAE,UAAU,OAAO;MACrB,SAAS,CAAC,UACR,EAAE,SAAS,KAAc;;EAE/B;;;;;EAOA,IAAI,MAAG;AACL,WAAO,KAAK;EACd;;;;EAIA,IAAI,UAAO;AACT,WAAO,KAAK;EACd;;;;EAIA,IAAI,iBAAc;AAChB,WAAO,KAAK;EACd;;;;EAIA,IAAI,OAAI;AACN,WAAO,KAAK;EACd;;;;EAIA,IAAI,cAAW;AACb,WAAO,KAAK;EACd;EACA,IAAI,aAAU;AACZ,WAAO,KAAK;EACd;;;;EAIA,IAAI,UAAO;AACT,WAAO,KAAK;EACd;;;;EAIA,IAAI,eAAY;AACd,WAAO,KAAK;EACd;EAEA,YACE,SAAwD;AAExD,UAAM,EACJ,MAAM,GACN,KACA,gBAAgB,GAChB,cACA,gBACA,gBACA,YACA,SACA,cACA,gBACA,aACA,UAAU,GACV,eAAe,GACf,iBACA,aACA,YACA,0BACA,oBACA,4BACA,wBACA,iBAAgB,IACd;AAEJ,QAAI,QAAQ,KAAK,CAAC,SAAS,GAAG,GAAG;AAC/B,YAAM,IAAI,UAAU,0CAA0C;;AAGhE,UAAM,YAAY,MAAM,aAAa,GAAG,IAAI;AAC5C,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,MAAM,wBAAwB,GAAG;;AAG7C,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,eAAe,gBAAgB,KAAK;AACzC,SAAK,kBAAkB;AACvB,QAAI,KAAK,iBAAiB;AACxB,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,cAAc;AACxC,cAAM,IAAI,UACR,oEAAoE;;AAGxE,UAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,cAAM,IAAI,UAAU,qCAAqC;;;AAI7D,QACE,eAAe,UACf,OAAO,eAAe,YACtB;AACA,YAAM,IAAI,UAAU,0CAA0C;;AAEhE,SAAK,cAAc;AAEnB,QACE,gBAAgB,UAChB,OAAO,gBAAgB,YACvB;AACA,YAAM,IAAI,UACR,6CAA6C;;AAGjD,SAAK,eAAe;AACpB,SAAK,kBAAkB,CAAC,CAAC;AAEzB,SAAK,UAAU,oBAAI,IAAG;AACtB,SAAK,WAAW,IAAI,MAAM,GAAG,EAAE,KAAK,MAAS;AAC7C,SAAK,WAAW,IAAI,MAAM,GAAG,EAAE,KAAK,MAAS;AAC7C,SAAK,QAAQ,IAAI,UAAU,GAAG;AAC9B,SAAK,QAAQ,IAAI,UAAU,GAAG;AAC9B,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ,MAAM,OAAO,GAAG;AAC7B,SAAK,QAAQ;AACb,SAAK,kBAAkB;AAEvB,QAAI,OAAO,YAAY,YAAY;AACjC,WAAK,WAAW;;AAElB,QAAI,OAAO,iBAAiB,YAAY;AACtC,WAAK,gBAAgB;AACrB,WAAK,YAAY,CAAA;WACZ;AACL,WAAK,gBAAgB;AACrB,WAAK,YAAY;;AAEnB,SAAK,cAAc,CAAC,CAAC,KAAK;AAC1B,SAAK,mBAAmB,CAAC,CAAC,KAAK;AAE/B,SAAK,iBAAiB,CAAC,CAAC;AACxB,SAAK,cAAc,CAAC,CAAC;AACrB,SAAK,2BAA2B,CAAC,CAAC;AAClC,SAAK,6BAA6B,CAAC,CAAC;AACpC,SAAK,yBAAyB,CAAC,CAAC;AAChC,SAAK,mBAAmB,CAAC,CAAC;AAG1B,QAAI,KAAK,iBAAiB,GAAG;AAC3B,UAAI,KAAK,aAAa,GAAG;AACvB,YAAI,CAAC,SAAS,KAAK,QAAQ,GAAG;AAC5B,gBAAM,IAAI,UACR,iDAAiD;;;AAIvD,UAAI,CAAC,SAAS,KAAK,YAAY,GAAG;AAChC,cAAM,IAAI,UACR,sDAAsD;;AAG1D,WAAK,wBAAuB;;AAG9B,SAAK,aAAa,CAAC,CAAC;AACpB,SAAK,qBAAqB,CAAC,CAAC;AAC5B,SAAK,iBAAiB,CAAC,CAAC;AACxB,SAAK,iBAAiB,CAAC,CAAC;AACxB,SAAK,gBACH,SAAS,aAAa,KAAK,kBAAkB,IACzC,gBACA;AACN,SAAK,eAAe,CAAC,CAAC;AACtB,SAAK,MAAM,OAAO;AAClB,QAAI,KAAK,KAAK;AACZ,UAAI,CAAC,SAAS,KAAK,GAAG,GAAG;AACvB,cAAM,IAAI,UACR,6CAA6C;;AAGjD,WAAK,uBAAsB;;AAI7B,QAAI,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK,aAAa,GAAG;AAC5D,YAAM,IAAI,UACR,kDAAkD;;AAGtD,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,QAAQ,CAAC,KAAK,UAAU;AACtD,YAAM,OAAO;AACb,UAAI,WAAW,IAAI,GAAG;AACpB,eAAO,IAAI,IAAI;AACf,cAAM,MACJ;AAEF,oBAAY,KAAK,yBAAyB,MAAM,SAAQ;;;EAG9D;;;;;EAMA,gBAAgB,KAAM;AACpB,WAAO,KAAK,QAAQ,IAAI,GAAG,IAAI,WAAW;EAC5C;EAEA,yBAAsB;AACpB,UAAM,OAAO,IAAI,UAAU,KAAK,IAAI;AACpC,UAAM,SAAS,IAAI,UAAU,KAAK,IAAI;AACtC,SAAK,QAAQ;AACb,SAAK,UAAU;AAEf,SAAK,cAAc,CAAC,OAAO,KAAK,QAAQ,KAAK,IAAG,MAAM;AACpD,aAAO,KAAK,IAAI,QAAQ,IAAI,QAAQ;AACpC,WAAK,KAAK,IAAI;AACd,UAAI,QAAQ,KAAK,KAAK,cAAc;AAClC,cAAM,IAAI,WAAW,MAAK;AACxB,cAAI,KAAK,SAAS,KAAK,GAAG;AACxB,iBAAK,QAAQ,KAAK,SAAS,KAAK,GAAQ,QAAQ;;QAEpD,GAAG,MAAM,CAAC;AAGV,YAAI,EAAE,OAAO;AACX,YAAE,MAAK;;;IAIb;AAEA,SAAK,iBAAiB,WAAQ;AAC5B,aAAO,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAG,IAAK;IACnD;AAEA,SAAK,aAAa,CAAC,QAAQ,UAAS;AAClC,UAAI,KAAK,KAAK,GAAG;AACf,cAAM,MAAM,KAAK,KAAK;AACtB,cAAM,QAAQ,OAAO,KAAK;AAE1B,YAAI,CAAC,OAAO,CAAC;AAAO;AACpB,eAAO,MAAM;AACb,eAAO,QAAQ;AACf,eAAO,MAAM,aAAa,OAAM;AAChC,cAAM,MAAM,OAAO,MAAM;AACzB,eAAO,eAAe,MAAM;;IAEhC;AAIA,QAAI,YAAY;AAChB,UAAM,SAAS,MAAK;AAClB,YAAM,IAAI,KAAK,IAAG;AAClB,UAAI,KAAK,gBAAgB,GAAG;AAC1B,oBAAY;AACZ,cAAM,IAAI,WACR,MAAO,YAAY,GACnB,KAAK,aAAa;AAIpB,YAAI,EAAE,OAAO;AACX,YAAE,MAAK;;;AAIX,aAAO;IACT;AAEA,SAAK,kBAAkB,SAAM;AAC3B,YAAM,QAAQ,KAAK,QAAQ,IAAI,GAAG;AAClC,UAAI,UAAU,QAAW;AACvB,eAAO;;AAET,YAAM,MAAM,KAAK,KAAK;AACtB,YAAM,QAAQ,OAAO,KAAK;AAC1B,UAAI,CAAC,OAAO,CAAC,OAAO;AAClB,eAAO;;AAET,YAAM,OAAO,aAAa,OAAM,KAAM;AACtC,aAAO,MAAM;IACf;AAEA,SAAK,WAAW,WAAQ;AACtB,YAAM,IAAI,OAAO,KAAK;AACtB,YAAM,IAAI,KAAK,KAAK;AACpB,aAAO,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,aAAa,OAAM,KAAM,IAAI;IACrD;EACF;;EAGA,iBAAyC,MAAK;EAAE;EAChD,aACE,MAAK;EAAE;EACT,cAMY,MAAK;EAAE;;EAGnB,WAAsC,MAAM;EAE5C,0BAAuB;AACrB,UAAM,QAAQ,IAAI,UAAU,KAAK,IAAI;AACrC,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,kBAAkB,WAAQ;AAC7B,WAAK,mBAAmB,MAAM,KAAK;AACnC,YAAM,KAAK,IAAI;IACjB;AACA,SAAK,eAAe,CAAC,GAAG,GAAG,MAAM,oBAAmB;AAGlD,UAAI,KAAK,mBAAmB,CAAC,GAAG;AAC9B,eAAO;;AAET,UAAI,CAAC,SAAS,IAAI,GAAG;AACnB,YAAI,iBAAiB;AACnB,cAAI,OAAO,oBAAoB,YAAY;AACzC,kBAAM,IAAI,UAAU,oCAAoC;;AAE1D,iBAAO,gBAAgB,GAAG,CAAC;AAC3B,cAAI,CAAC,SAAS,IAAI,GAAG;AACnB,kBAAM,IAAI,UACR,0DAA0D;;eAGzD;AACL,gBAAM,IAAI,UACR,2HAEwB;;;AAI9B,aAAO;IACT;AACA,SAAK,eAAe,CAClB,OACA,MACA,WACE;AACF,YAAM,KAAK,IAAI;AACf,UAAI,KAAK,UAAU;AACjB,cAAM,UAAU,KAAK,WAAY,MAAM,KAAK;AAC5C,eAAO,KAAK,kBAAkB,SAAS;AACrC,eAAK,OAAO,IAAI;;;AAGpB,WAAK,mBAAmB,MAAM,KAAK;AACnC,UAAI,QAAQ;AACV,eAAO,YAAY;AACnB,eAAO,sBAAsB,KAAK;;IAEtC;EACF;EAEA,kBAA0C,QAAK;EAAE;EACjD,eAIY,CAAC,IAAI,IAAI,QAAO;EAAE;EAC9B,eAKqB,CACnB,IACA,IACA,MACA,oBACE;AACF,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,IAAI,UACR,kEAAkE;;AAGtE,WAAO;EACT;EAEA,CAAC,SAAS,EAAE,aAAa,KAAK,WAAU,IAAK,CAAA,GAAE;AAC7C,QAAI,KAAK,OAAO;AACd,eAAS,IAAI,KAAK,OAAO,QAAQ;AAC/B,YAAI,CAAC,KAAK,cAAc,CAAC,GAAG;AAC1B;;AAEF,YAAI,cAAc,CAAC,KAAK,SAAS,CAAC,GAAG;AACnC,gBAAM;;AAER,YAAI,MAAM,KAAK,OAAO;AACpB;eACK;AACL,cAAI,KAAK,MAAM,CAAC;;;;EAIxB;EAEA,CAAC,UAAU,EAAE,aAAa,KAAK,WAAU,IAAK,CAAA,GAAE;AAC9C,QAAI,KAAK,OAAO;AACd,eAAS,IAAI,KAAK,OAAO,QAAQ;AAC/B,YAAI,CAAC,KAAK,cAAc,CAAC,GAAG;AAC1B;;AAEF,YAAI,cAAc,CAAC,KAAK,SAAS,CAAC,GAAG;AACnC,gBAAM;;AAER,YAAI,MAAM,KAAK,OAAO;AACpB;eACK;AACL,cAAI,KAAK,MAAM,CAAC;;;;EAIxB;EAEA,cAAc,OAAY;AACxB,WACE,UAAU,UACV,KAAK,QAAQ,IAAI,KAAK,SAAS,KAAK,CAAM,MAAM;EAEpD;;;;;EAMA,CAAC,UAAO;AACN,eAAW,KAAK,KAAK,SAAQ,GAAI;AAC/B,UACE,KAAK,SAAS,CAAC,MAAM,UACrB,KAAK,SAAS,CAAC,MAAM,UACrB,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC,CAAC,GACzC;AACA,cAAM,CAAC,KAAK,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;;;EAG/C;;;;;;;EAQA,CAAC,WAAQ;AACP,eAAW,KAAK,KAAK,UAAS,GAAI;AAChC,UACE,KAAK,SAAS,CAAC,MAAM,UACrB,KAAK,SAAS,CAAC,MAAM,UACrB,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC,CAAC,GACzC;AACA,cAAM,CAAC,KAAK,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;;;EAG/C;;;;;EAMA,CAAC,OAAI;AACH,eAAW,KAAK,KAAK,SAAQ,GAAI;AAC/B,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,UACE,MAAM,UACN,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC,CAAC,GACzC;AACA,cAAM;;;EAGZ;;;;;;;EAQA,CAAC,QAAK;AACJ,eAAW,KAAK,KAAK,UAAS,GAAI;AAChC,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,UACE,MAAM,UACN,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC,CAAC,GACzC;AACA,cAAM;;;EAGZ;;;;;EAMA,CAAC,SAAM;AACL,eAAW,KAAK,KAAK,SAAQ,GAAI;AAC/B,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,UACE,MAAM,UACN,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC,CAAC,GACzC;AACA,cAAM,KAAK,SAAS,CAAC;;;EAG3B;;;;;;;EAQA,CAAC,UAAO;AACN,eAAW,KAAK,KAAK,UAAS,GAAI;AAChC,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,UACE,MAAM,UACN,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC,CAAC,GACzC;AACA,cAAM,KAAK,SAAS,CAAC;;;EAG3B;;;;;EAMA,CAAC,OAAO,QAAQ,IAAC;AACf,WAAO,KAAK,QAAO;EACrB;;;;;;EAOA,CAAC,OAAO,WAAW,IAAI;;;;;EAMvB,KACE,IACA,aAA4C,CAAA,GAAE;AAE9C,eAAW,KAAK,KAAK,SAAQ,GAAI;AAC/B,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,QAAQ,KAAK,mBAAmB,CAAC,IACnC,EAAE,uBACF;AACJ,UAAI,UAAU;AAAW;AACzB,UAAI,GAAG,OAAO,KAAK,SAAS,CAAC,GAAQ,IAAI,GAAG;AAC1C,eAAO,KAAK,IAAI,KAAK,SAAS,CAAC,GAAQ,UAAU;;;EAGvD;;;;;;;;;;;;EAaA,QACE,IACA,QAAa,MAAI;AAEjB,eAAW,KAAK,KAAK,SAAQ,GAAI;AAC/B,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,QAAQ,KAAK,mBAAmB,CAAC,IACnC,EAAE,uBACF;AACJ,UAAI,UAAU;AAAW;AACzB,SAAG,KAAK,OAAO,OAAO,KAAK,SAAS,CAAC,GAAQ,IAAI;;EAErD;;;;;EAMA,SACE,IACA,QAAa,MAAI;AAEjB,eAAW,KAAK,KAAK,UAAS,GAAI;AAChC,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,QAAQ,KAAK,mBAAmB,CAAC,IACnC,EAAE,uBACF;AACJ,UAAI,UAAU;AAAW;AACzB,SAAG,KAAK,OAAO,OAAO,KAAK,SAAS,CAAC,GAAQ,IAAI;;EAErD;;;;;EAMA,aAAU;AACR,QAAI,UAAU;AACd,eAAW,KAAK,KAAK,UAAU,EAAE,YAAY,KAAI,CAAE,GAAG;AACpD,UAAI,KAAK,SAAS,CAAC,GAAG;AACpB,aAAK,QAAQ,KAAK,SAAS,CAAC,GAAQ,QAAQ;AAC5C,kBAAU;;;AAGd,WAAO;EACT;;;;;;;;;;;;;EAcA,KAAK,KAAM;AACT,UAAM,IAAI,KAAK,QAAQ,IAAI,GAAG;AAC9B,QAAI,MAAM;AAAW,aAAO;AAC5B,UAAM,IAAI,KAAK,SAAS,CAAC;AACzB,UAAM,QAAuB,KAAK,mBAAmB,CAAC,IAClD,EAAE,uBACF;AACJ,QAAI,UAAU;AAAW,aAAO;AAChC,UAAM,QAA2B,EAAE,MAAK;AACxC,QAAI,KAAK,SAAS,KAAK,SAAS;AAC9B,YAAM,MAAM,KAAK,MAAM,CAAC;AACxB,YAAM,QAAQ,KAAK,QAAQ,CAAC;AAC5B,UAAI,OAAO,OAAO;AAChB,cAAM,SAAS,OAAO,KAAK,IAAG,IAAK;AACnC,cAAM,MAAM;AACZ,cAAM,QAAQ,KAAK,IAAG;;;AAG1B,QAAI,KAAK,QAAQ;AACf,YAAM,OAAO,KAAK,OAAO,CAAC;;AAE5B,WAAO;EACT;;;;;;;;;;;;;;EAeA,OAAI;AACF,UAAM,MAAgC,CAAA;AACtC,eAAW,KAAK,KAAK,SAAS,EAAE,YAAY,KAAI,CAAE,GAAG;AACnD,YAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,QAAuB,KAAK,mBAAmB,CAAC,IAClD,EAAE,uBACF;AACJ,UAAI,UAAU,UAAa,QAAQ;AAAW;AAC9C,YAAM,QAA2B,EAAE,MAAK;AACxC,UAAI,KAAK,SAAS,KAAK,SAAS;AAC9B,cAAM,MAAM,KAAK,MAAM,CAAC;AAGxB,cAAM,MAAM,KAAK,IAAG,IAAM,KAAK,QAAQ,CAAC;AACxC,cAAM,QAAQ,KAAK,MAAM,KAAK,IAAG,IAAK,GAAG;;AAE3C,UAAI,KAAK,QAAQ;AACf,cAAM,OAAO,KAAK,OAAO,CAAC;;AAE5B,UAAI,QAAQ,CAAC,KAAK,KAAK,CAAC;;AAE1B,WAAO;EACT;;;;;;;;;;EAWA,KAAK,KAA6B;AAChC,SAAK,MAAK;AACV,eAAW,CAAC,KAAK,KAAK,KAAK,KAAK;AAC9B,UAAI,MAAM,OAAO;AAOf,cAAM,MAAM,KAAK,IAAG,IAAK,MAAM;AAC/B,cAAM,QAAQ,KAAK,IAAG,IAAK;;AAE7B,WAAK,IAAI,KAAK,MAAM,OAAO,KAAK;;EAEpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgCA,IACE,GACA,GACA,aAA4C,CAAA,GAAE;AAE9C,QAAI,MAAM,QAAW;AACnB,WAAK,OAAO,CAAC;AACb,aAAO;;AAET,UAAM,EACJ,MAAM,KAAK,KACX,OACA,iBAAiB,KAAK,gBACtB,kBAAkB,KAAK,iBACvB,OAAM,IACJ;AACJ,QAAI,EAAE,cAAc,KAAK,YAAW,IAAK;AAEzC,UAAM,OAAO,KAAK,aAChB,GACA,GACA,WAAW,QAAQ,GACnB,eAAe;AAIjB,QAAI,KAAK,gBAAgB,OAAO,KAAK,cAAc;AACjD,UAAI,QAAQ;AACV,eAAO,MAAM;AACb,eAAO,uBAAuB;;AAGhC,WAAK,QAAQ,GAAG,KAAK;AACrB,aAAO;;AAET,QAAI,QAAQ,KAAK,UAAU,IAAI,SAAY,KAAK,QAAQ,IAAI,CAAC;AAC7D,QAAI,UAAU,QAAW;AAEvB,cACE,KAAK,UAAU,IACX,KAAK,QACL,KAAK,MAAM,WAAW,IACtB,KAAK,MAAM,IAAG,IACd,KAAK,UAAU,KAAK,OACpB,KAAK,OAAO,KAAK,IACjB,KAAK;AAEX,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,QAAQ,IAAI,GAAG,KAAK;AACzB,WAAK,MAAM,KAAK,KAAK,IAAI;AACzB,WAAK,MAAM,KAAK,IAAI,KAAK;AACzB,WAAK,QAAQ;AACb,WAAK;AACL,WAAK,aAAa,OAAO,MAAM,MAAM;AACrC,UAAI;AAAQ,eAAO,MAAM;AACzB,oBAAc;WACT;AAEL,WAAK,YAAY,KAAK;AACtB,YAAM,SAAS,KAAK,SAAS,KAAK;AAClC,UAAI,MAAM,QAAQ;AAChB,YAAI,KAAK,mBAAmB,KAAK,mBAAmB,MAAM,GAAG;AAC3D,iBAAO,kBAAkB,MAAM,IAAI,MAAM,UAAU,CAAC;AACpD,gBAAM,EAAE,sBAAsB,EAAC,IAAK;AACpC,cAAI,MAAM,UAAa,CAAC,gBAAgB;AACtC,gBAAI,KAAK,aAAa;AACpB,mBAAK,WAAW,GAAQ,GAAG,KAAK;;AAElC,gBAAI,KAAK,kBAAkB;AACzB,mBAAK,WAAW,KAAK,CAAC,GAAQ,GAAG,KAAK,CAAC;;;mBAGlC,CAAC,gBAAgB;AAC1B,cAAI,KAAK,aAAa;AACpB,iBAAK,WAAW,QAAa,GAAG,KAAK;;AAEvC,cAAI,KAAK,kBAAkB;AACzB,iBAAK,WAAW,KAAK,CAAC,QAAa,GAAG,KAAK,CAAC;;;AAGhD,aAAK,gBAAgB,KAAK;AAC1B,aAAK,aAAa,OAAO,MAAM,MAAM;AACrC,aAAK,SAAS,KAAK,IAAI;AACvB,YAAI,QAAQ;AACV,iBAAO,MAAM;AACb,gBAAM,WACJ,UAAU,KAAK,mBAAmB,MAAM,IACpC,OAAO,uBACP;AACN,cAAI,aAAa;AAAW,mBAAO,WAAW;;iBAEvC,QAAQ;AACjB,eAAO,MAAM;;;AAGjB,QAAI,QAAQ,KAAK,CAAC,KAAK,OAAO;AAC5B,WAAK,uBAAsB;;AAE7B,QAAI,KAAK,OAAO;AACd,UAAI,CAAC,aAAa;AAChB,aAAK,YAAY,OAAO,KAAK,KAAK;;AAEpC,UAAI;AAAQ,aAAK,WAAW,QAAQ,KAAK;;AAE3C,QAAI,CAAC,kBAAkB,KAAK,oBAAoB,KAAK,WAAW;AAC9D,YAAM,KAAK,KAAK;AAChB,UAAI;AACJ,aAAQ,OAAO,IAAI,MAAK,GAAK;AAC3B,aAAK,gBAAgB,GAAG,IAAI;;;AAGhC,WAAO;EACT;;;;;EAMA,MAAG;AACD,QAAI;AACF,aAAO,KAAK,OAAO;AACjB,cAAM,MAAM,KAAK,SAAS,KAAK,KAAK;AACpC,aAAK,OAAO,IAAI;AAChB,YAAI,KAAK,mBAAmB,GAAG,GAAG;AAChC,cAAI,IAAI,sBAAsB;AAC5B,mBAAO,IAAI;;mBAEJ,QAAQ,QAAW;AAC5B,iBAAO;;;;AAIX,UAAI,KAAK,oBAAoB,KAAK,WAAW;AAC3C,cAAM,KAAK,KAAK;AAChB,YAAI;AACJ,eAAQ,OAAO,IAAI,MAAK,GAAK;AAC3B,eAAK,gBAAgB,GAAG,IAAI;;;;EAIpC;EAEA,OAAO,MAAa;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,UAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,QAAI,KAAK,mBAAmB,KAAK,mBAAmB,CAAC,GAAG;AACtD,QAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC;eACrC,KAAK,eAAe,KAAK,kBAAkB;AACpD,UAAI,KAAK,aAAa;AACpB,aAAK,WAAW,GAAG,GAAG,OAAO;;AAE/B,UAAI,KAAK,kBAAkB;AACzB,aAAK,WAAW,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;;;AAGxC,SAAK,gBAAgB,IAAI;AAEzB,QAAI,MAAM;AACR,WAAK,SAAS,IAAI,IAAI;AACtB,WAAK,SAAS,IAAI,IAAI;AACtB,WAAK,MAAM,KAAK,IAAI;;AAEtB,QAAI,KAAK,UAAU,GAAG;AACpB,WAAK,QAAQ,KAAK,QAAQ;AAC1B,WAAK,MAAM,SAAS;WACf;AACL,WAAK,QAAQ,KAAK,MAAM,IAAI;;AAE9B,SAAK,QAAQ,OAAO,CAAC;AACrB,SAAK;AACL,WAAO;EACT;;;;;;;;;;;;;;;;;EAkBA,IAAI,GAAM,aAA4C,CAAA,GAAE;AACtD,UAAM,EAAE,iBAAiB,KAAK,gBAAgB,OAAM,IAClD;AACF,UAAM,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAChC,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,UACE,KAAK,mBAAmB,CAAC,KACzB,EAAE,yBAAyB,QAC3B;AACA,eAAO;;AAET,UAAI,CAAC,KAAK,SAAS,KAAK,GAAG;AACzB,YAAI,gBAAgB;AAClB,eAAK,eAAe,KAAK;;AAE3B,YAAI,QAAQ;AACV,iBAAO,MAAM;AACb,eAAK,WAAW,QAAQ,KAAK;;AAE/B,eAAO;iBACE,QAAQ;AACjB,eAAO,MAAM;AACb,aAAK,WAAW,QAAQ,KAAK;;eAEtB,QAAQ;AACjB,aAAO,MAAM;;AAEf,WAAO;EACT;;;;;;;;EASA,KAAK,GAAM,cAA8C,CAAA,GAAE;AACzD,UAAM,EAAE,aAAa,KAAK,WAAU,IAAK;AACzC,UAAM,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAChC,QACE,UAAU,UACT,CAAC,cAAc,KAAK,SAAS,KAAK,GACnC;AACA;;AAEF,UAAM,IAAI,KAAK,SAAS,KAAK;AAE7B,WAAO,KAAK,mBAAmB,CAAC,IAAI,EAAE,uBAAuB;EAC/D;EAEA,iBACE,GACA,OACA,SACA,SAAY;AAEZ,UAAM,IAAI,UAAU,SAAY,SAAY,KAAK,SAAS,KAAK;AAC/D,QAAI,KAAK,mBAAmB,CAAC,GAAG;AAC9B,aAAO;;AAGT,UAAM,KAAK,IAAI,GAAE;AACjB,UAAM,EAAE,OAAM,IAAK;AAEnB,YAAQ,iBAAiB,SAAS,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG;MAC/D,QAAQ,GAAG;KACZ;AAED,UAAM,YAAY;MAChB,QAAQ,GAAG;MACX;MACA;;AAGF,UAAM,KAAK,CACTA,IACA,cAAc,UACG;AACjB,YAAM,EAAE,QAAO,IAAK,GAAG;AACvB,YAAM,cAAc,QAAQ,oBAAoBA,OAAM;AACtD,UAAI,QAAQ,QAAQ;AAClB,YAAI,WAAW,CAAC,aAAa;AAC3B,kBAAQ,OAAO,eAAe;AAC9B,kBAAQ,OAAO,aAAa,GAAG,OAAO;AACtC,cAAI;AAAa,oBAAQ,OAAO,oBAAoB;eAC/C;AACL,kBAAQ,OAAO,gBAAgB;;;AAGnC,UAAI,WAAW,CAAC,eAAe,CAAC,aAAa;AAC3C,eAAO,UAAU,GAAG,OAAO,MAAM;;AAGnC,YAAMC,MAAK;AACX,UAAI,KAAK,SAAS,KAAc,MAAM,GAAG;AACvC,YAAID,OAAM,QAAW;AACnB,cAAIC,IAAG,sBAAsB;AAC3B,iBAAK,SAAS,KAAc,IAAIA,IAAG;iBAC9B;AACL,iBAAK,QAAQ,GAAG,OAAO;;eAEpB;AACL,cAAI,QAAQ;AAAQ,oBAAQ,OAAO,eAAe;AAClD,eAAK,IAAI,GAAGD,IAAG,UAAU,OAAO;;;AAGpC,aAAOA;IACT;AAEA,UAAM,KAAK,CAAC,OAAW;AACrB,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,OAAO,gBAAgB;AAC/B,gBAAQ,OAAO,aAAa;;AAE9B,aAAO,UAAU,EAAE;IACrB;AAEA,UAAM,YAAY,CAAC,OAA0B;AAC3C,YAAM,EAAE,QAAO,IAAK,GAAG;AACvB,YAAM,oBACJ,WAAW,QAAQ;AACrB,YAAM,aACJ,qBAAqB,QAAQ;AAC/B,YAAM,WAAW,cAAc,QAAQ;AACvC,YAAMC,MAAK;AACX,UAAI,KAAK,SAAS,KAAc,MAAM,GAAG;AAGvC,cAAM,MAAM,CAAC,YAAYA,IAAG,yBAAyB;AACrD,YAAI,KAAK;AACP,eAAK,QAAQ,GAAG,OAAO;mBACd,CAAC,mBAAmB;AAK7B,eAAK,SAAS,KAAc,IAAIA,IAAG;;;AAGvC,UAAI,YAAY;AACd,YAAI,QAAQ,UAAUA,IAAG,yBAAyB,QAAW;AAC3D,kBAAQ,OAAO,gBAAgB;;AAEjC,eAAOA,IAAG;iBACDA,IAAG,eAAeA,KAAI;AAC/B,cAAM;;IAEV;AAEA,UAAM,QAAQ,CACZ,KACA,QACE;AACF,YAAM,MAAM,KAAK,eAAe,GAAG,GAAG,SAAS;AAC/C,UAAI,OAAO,eAAe,SAAS;AACjC,YAAI,KAAK,CAAAD,OAAK,IAAIA,OAAM,SAAY,SAAYA,EAAC,GAAG,GAAG;;AAKzD,SAAG,OAAO,iBAAiB,SAAS,MAAK;AACvC,YACE,CAAC,QAAQ,oBACT,QAAQ,wBACR;AACA,cAAI,MAAS;AAEb,cAAI,QAAQ,wBAAwB;AAClC,kBAAM,CAAAA,OAAK,GAAGA,IAAG,IAAI;;;MAG3B,CAAC;IACH;AAEA,QAAI,QAAQ;AAAQ,cAAQ,OAAO,kBAAkB;AACrD,UAAM,IAAI,IAAI,QAAQ,KAAK,EAAE,KAAK,IAAI,EAAE;AACxC,UAAM,KAAyB,OAAO,OAAO,GAAG;MAC9C,mBAAmB;MACnB,sBAAsB;MACtB,YAAY;KACb;AAED,QAAI,UAAU,QAAW;AAEvB,WAAK,IAAI,GAAG,IAAI,EAAE,GAAG,UAAU,SAAS,QAAQ,OAAS,CAAE;AAC3D,cAAQ,KAAK,QAAQ,IAAI,CAAC;WACrB;AACL,WAAK,SAAS,KAAK,IAAI;;AAEzB,WAAO;EACT;EAEA,mBAAmB,GAAM;AACvB,QAAI,CAAC,KAAK;AAAiB,aAAO;AAClC,UAAM,IAAI;AACV,WACE,CAAC,CAAC,KACF,aAAa,WACb,EAAE,eAAe,sBAAsB,KACvC,EAAE,6BAA6B;EAEnC;EA+GA,MAAM,MACJ,GACA,eAAgD,CAAA,GAAE;AAElD,UAAM;;MAEJ,aAAa,KAAK;MAClB,iBAAiB,KAAK;MACtB,qBAAqB,KAAK;;MAE1B,MAAM,KAAK;MACX,iBAAiB,KAAK;MACtB,OAAO;MACP,kBAAkB,KAAK;MACvB,cAAc,KAAK;;MAEnB,2BAA2B,KAAK;MAChC,6BAA6B,KAAK;MAClC,mBAAmB,KAAK;MACxB,yBAAyB,KAAK;MAC9B;MACA,eAAe;MACf;MACA;IAAM,IACJ;AAEJ,QAAI,CAAC,KAAK,iBAAiB;AACzB,UAAI;AAAQ,eAAO,QAAQ;AAC3B,aAAO,KAAK,IAAI,GAAG;QACjB;QACA;QACA;QACA;OACD;;AAGH,UAAM,UAAU;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;AAGF,QAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAC9B,QAAI,UAAU,QAAW;AACvB,UAAI;AAAQ,eAAO,QAAQ;AAC3B,YAAM,IAAI,KAAK,iBAAiB,GAAG,OAAO,SAAS,OAAO;AAC1D,aAAQ,EAAE,aAAa;WAClB;AAEL,YAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,UAAI,KAAK,mBAAmB,CAAC,GAAG;AAC9B,cAAM,QACJ,cAAc,EAAE,yBAAyB;AAC3C,YAAI,QAAQ;AACV,iBAAO,QAAQ;AACf,cAAI;AAAO,mBAAO,gBAAgB;;AAEpC,eAAO,QAAQ,EAAE,uBAAwB,EAAE,aAAa;;AAK1D,YAAM,UAAU,KAAK,SAAS,KAAK;AACnC,UAAI,CAAC,gBAAgB,CAAC,SAAS;AAC7B,YAAI;AAAQ,iBAAO,QAAQ;AAC3B,aAAK,YAAY,KAAK;AACtB,YAAI,gBAAgB;AAClB,eAAK,eAAe,KAAK;;AAE3B,YAAI;AAAQ,eAAK,WAAW,QAAQ,KAAK;AACzC,eAAO;;AAKT,YAAM,IAAI,KAAK,iBAAiB,GAAG,OAAO,SAAS,OAAO;AAC1D,YAAM,WAAW,EAAE,yBAAyB;AAC5C,YAAM,WAAW,YAAY;AAC7B,UAAI,QAAQ;AACV,eAAO,QAAQ,UAAU,UAAU;AACnC,YAAI,YAAY;AAAS,iBAAO,gBAAgB;;AAElD,aAAO,WAAW,EAAE,uBAAwB,EAAE,aAAa;;EAE/D;EAoCA,MAAM,WACJ,GACA,eAAgD,CAAA,GAAE;AAElD,UAAM,IAAI,MAAM,KAAK,MACnB,GACA,YAI8C;AAEhD,QAAI,MAAM;AAAW,YAAM,IAAI,MAAM,4BAA4B;AACjE,WAAO;EACT;EAqCA,KAAK,GAAM,cAA8C,CAAA,GAAE;AACzD,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,uCAAuC;;AAEzD,UAAM,EAAE,SAAS,cAAc,GAAG,QAAO,IAAK;AAC9C,UAAM,IAAI,KAAK,IAAI,GAAG,OAAO;AAC7B,QAAI,CAAC,gBAAgB,MAAM;AAAW,aAAO;AAC7C,UAAM,KAAK,WAAW,GAAG,GAAG;MAC1B;MACA;KACqC;AACvC,SAAK,IAAI,GAAG,IAAI,OAAO;AACvB,WAAO;EACT;;;;;;;EAQA,IAAI,GAAM,aAA4C,CAAA,GAAE;AACtD,UAAM,EACJ,aAAa,KAAK,YAClB,iBAAiB,KAAK,gBACtB,qBAAqB,KAAK,oBAC1B,OAAM,IACJ;AACJ,UAAM,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAChC,QAAI,UAAU,QAAW;AACvB,YAAM,QAAQ,KAAK,SAAS,KAAK;AACjC,YAAM,WAAW,KAAK,mBAAmB,KAAK;AAC9C,UAAI;AAAQ,aAAK,WAAW,QAAQ,KAAK;AACzC,UAAI,KAAK,SAAS,KAAK,GAAG;AACxB,YAAI;AAAQ,iBAAO,MAAM;AAEzB,YAAI,CAAC,UAAU;AACb,cAAI,CAAC,oBAAoB;AACvB,iBAAK,QAAQ,GAAG,QAAQ;;AAE1B,cAAI,UAAU;AAAY,mBAAO,gBAAgB;AACjD,iBAAO,aAAa,QAAQ;eACvB;AACL,cACE,UACA,cACA,MAAM,yBAAyB,QAC/B;AACA,mBAAO,gBAAgB;;AAEzB,iBAAO,aAAa,MAAM,uBAAuB;;aAE9C;AACL,YAAI;AAAQ,iBAAO,MAAM;AAMzB,YAAI,UAAU;AACZ,iBAAO,MAAM;;AAEf,aAAK,YAAY,KAAK;AACtB,YAAI,gBAAgB;AAClB,eAAK,eAAe,KAAK;;AAE3B,eAAO;;eAEA,QAAQ;AACjB,aAAO,MAAM;;EAEjB;EAEA,SAAS,GAAU,GAAQ;AACzB,SAAK,MAAM,CAAC,IAAI;AAChB,SAAK,MAAM,CAAC,IAAI;EAClB;EAEA,YAAY,OAAY;AAStB,QAAI,UAAU,KAAK,OAAO;AACxB,UAAI,UAAU,KAAK,OAAO;AACxB,aAAK,QAAQ,KAAK,MAAM,KAAK;aACxB;AACL,aAAK,SACH,KAAK,MAAM,KAAK,GAChB,KAAK,MAAM,KAAK,CAAU;;AAG9B,WAAK,SAAS,KAAK,OAAO,KAAK;AAC/B,WAAK,QAAQ;;EAEjB;;;;;;EAOA,OAAO,GAAI;AACT,WAAO,KAAK,QAAQ,GAAG,QAAQ;EACjC;EAEA,QAAQ,GAAM,QAA8B;AAC1C,QAAI,UAAU;AACd,QAAI,KAAK,UAAU,GAAG;AACpB,YAAM,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAChC,UAAI,UAAU,QAAW;AACvB,kBAAU;AACV,YAAI,KAAK,UAAU,GAAG;AACpB,eAAK,OAAO,MAAM;eACb;AACL,eAAK,gBAAgB,KAAK;AAC1B,gBAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,cAAI,KAAK,mBAAmB,CAAC,GAAG;AAC9B,cAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC;qBACrC,KAAK,eAAe,KAAK,kBAAkB;AACpD,gBAAI,KAAK,aAAa;AACpB,mBAAK,WAAW,GAAQ,GAAG,MAAM;;AAEnC,gBAAI,KAAK,kBAAkB;AACzB,mBAAK,WAAW,KAAK,CAAC,GAAQ,GAAG,MAAM,CAAC;;;AAG5C,eAAK,QAAQ,OAAO,CAAC;AACrB,eAAK,SAAS,KAAK,IAAI;AACvB,eAAK,SAAS,KAAK,IAAI;AACvB,cAAI,UAAU,KAAK,OAAO;AACxB,iBAAK,QAAQ,KAAK,MAAM,KAAK;qBACpB,UAAU,KAAK,OAAO;AAC/B,iBAAK,QAAQ,KAAK,MAAM,KAAK;iBACxB;AACL,kBAAM,KAAK,KAAK,MAAM,KAAK;AAC3B,iBAAK,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK;AACjC,kBAAM,KAAK,KAAK,MAAM,KAAK;AAC3B,iBAAK,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK;;AAEnC,eAAK;AACL,eAAK,MAAM,KAAK,KAAK;;;;AAI3B,QAAI,KAAK,oBAAoB,KAAK,WAAW,QAAQ;AACnD,YAAM,KAAK,KAAK;AAChB,UAAI;AACJ,aAAQ,OAAO,IAAI,MAAK,GAAK;AAC3B,aAAK,gBAAgB,GAAG,IAAI;;;AAGhC,WAAO;EACT;;;;EAKA,QAAK;AACH,WAAO,KAAK,OAAO,QAAQ;EAC7B;EACA,OAAO,QAA8B;AACnC,eAAW,SAAS,KAAK,UAAU,EAAE,YAAY,KAAI,CAAE,GAAG;AACxD,YAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,UAAI,KAAK,mBAAmB,CAAC,GAAG;AAC9B,UAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC;aACzC;AACL,cAAM,IAAI,KAAK,SAAS,KAAK;AAC7B,YAAI,KAAK,aAAa;AACpB,eAAK,WAAW,GAAQ,GAAQ,MAAM;;AAExC,YAAI,KAAK,kBAAkB;AACzB,eAAK,WAAW,KAAK,CAAC,GAAQ,GAAQ,MAAM,CAAC;;;;AAKnD,SAAK,QAAQ,MAAK;AAClB,SAAK,SAAS,KAAK,MAAS;AAC5B,SAAK,SAAS,KAAK,MAAS;AAC5B,QAAI,KAAK,SAAS,KAAK,SAAS;AAC9B,WAAK,MAAM,KAAK,CAAC;AACjB,WAAK,QAAQ,KAAK,CAAC;;AAErB,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,KAAK,CAAC;;AAEpB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,MAAM,SAAS;AACpB,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AACb,QAAI,KAAK,oBAAoB,KAAK,WAAW;AAC3C,YAAM,KAAK,KAAK;AAChB,UAAI;AACJ,aAAQ,OAAO,IAAI,MAAK,GAAK;AAC3B,aAAK,gBAAgB,GAAG,IAAI;;;EAGlC;;", "names": ["v", "bf"]}
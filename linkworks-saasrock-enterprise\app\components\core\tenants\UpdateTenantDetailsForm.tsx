import { Form } from "react-router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import ButtonTertiary from "~/components/ui/buttons/ButtonTertiary";
import UploadDocuments from "~/components/ui/uploaders/UploadDocument";
import InputText from "~/components/ui/input/InputText";
import UrlUtils from "~/utils/app/UrlUtils";
import { TenantWithDetails } from "~/utils/db/tenants.db.server";
import RowProperties from "~/components/entities/rows/RowProperties";
import LoadingButton from "~/components/ui/buttons/LoadingButton";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { TenantType } from "@prisma/client";
import InputCombobox from "~/components/ui/input/InputCombobox";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import InputMedia from "~/components/ui/input/InputMedia";
import { useNavigation } from "react-router";
interface Props {
  tenant: TenantWithDetails;
  disabled: boolean;
  tenantSettingsEntity: EntityWithDetails | null;
  tenantTypes?: TenantType[];
  options?: {
    canChangeType: boolean;
  };
}

export default function UpdateTenantDetailsForm({ tenant, disabled, tenantSettingsEntity, tenantTypes, options }: Props) {
  const { t } = useTranslation();

  const [slug, setSlug] = useState<string | undefined>(tenant?.slug ?? "");
  const [icon, setIcon] = useState<string | undefined>(tenant?.icon ?? "");
  const [types, setTypes] = useState<string[]>(tenant.types.map((f) => f.id));
  const navigation = useNavigation();

  function loadedImage(image: string | undefined) {
    setIcon(image);
  }
  return (
    <Form method="post">
      <input type="hidden" name="action" value="edit" hidden readOnly />
      <div
        className="p-4"
        style={{
          borderTop: "1px solid #E6E6E6",
          borderRight: "1px solid #E6E6E6",
          borderLeft: "1px solid #E6E6E6",
          borderRadius: "6px 6px 0px 0px",
          boxShadow: "0px 4px 14.9px 0px rgba(183, 183, 183, 0.25)",
        }}
      >
        <div className="">
          <div className="grid grid-cols-6 gap-2">
            <div className="col-span-6 mt-[4px] sm:col-span-6">
              <InputText className="mt-[1px]" autoFocus disabled={disabled} name="name" title={t("shared.name")} defaultValue={tenant?.name} />
            </div>
            <div className="col-span-6 mt-[4px] sm:col-span-6">
              <InputText
                className="mt-[1px]"
                disabled={disabled}
                //required
                name="slug"
                title={t("shared.slug")}
                value={slug}
                setValue={(e) => {
                  const slug = UrlUtils.slugify(e.toString());
                  if (slug) {
                    setSlug(slug);
                  }
                }}
              />
            </div>
            {tenantTypes !== undefined && tenantTypes.length > 0 && (
              <div className="col-span-6 sm:col-span-6">
                {types?.map((item, idx) => {
                  return <input key={idx} type="hidden" name={`typeIds[]`} value={item} hidden readOnly />;
                })}
                <InputCombobox
                  withSearch={false}
                  disabled={!options?.canChangeType}
                  name="typeIds"
                  title={t("shared.type")}
                  value={types}
                  onChange={(e) => setTypes(e as string[])}
                  options={tenantTypes.map((f) => {
                    return {
                      value: f.id,
                      name: f.title,
                    };
                  })}
                />
              </div>
            )}

            <div className="col-span-6 mt-[4px] sm:col-span-6">
              <div className="flex items-center space-x-3">
                {icon ? (
                  <ButtonTertiary disabled={disabled} destructive={true} onClick={() => loadedImage("")} type="button">
                    {t("shared.delete")}
                  </ButtonTertiary>
                ) : (
                  <div className="w-full">
                    <div className="col-span-6 sm:col-span-6">
                      <div className="flex justify-end"></div>
                      <div className="">
                        <div className="flex items-center justify-between">
                          <div></div>
                        </div>

                        <InputMedia className="mt-[10px]" name="Icon" title={t("shared.icon")} accept=".png, .jpg, .jpeg" maxSize={10} />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {tenantSettingsEntity && (
              <div className="col-span-6 sm:col-span-6">
                <RowProperties entity={tenantSettingsEntity} item={tenant.tenantSettingsRow?.row ?? null} />
              </div>
            )}
          </div>
        </div>

        <div className="border-border mt-3 border-t pt-3">
          <div className="flex justify-end gap-2">
            <div>
              <button
                disabled={navigation.state === "submitting"}
                type="reset"
                className="border-input text-foreground bg-primary-foreground p flex h-8 w-16 cursor-pointer items-center justify-center gap-2 rounded-sm border-1 px-6 py-4.5 text-sm font-normal"
              >
                {t("shared.cancel")}
              </button>
            </div>

            <ButtonPrimary disabled={disabled} type="submit">
              {t("shared.saveDetails")}
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </Form>
  );
}

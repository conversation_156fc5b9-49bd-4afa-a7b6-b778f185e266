import { MapPin, ExternalLink, Star, Phone, Globe } from "lucide-react";
import { LocationData, AddressDisplayFormat } from "~/types/location";
import { cn } from "~/lib/utils";
import LocationHelper from "~/utils/helpers/LocationHelper";

export interface RowLocationCellProps {
  value?: LocationData | string | null;
  format?: AddressDisplayFormat;
  showIcon?: boolean;
  showDetails?: boolean;
  maxWidth?: string;
}

export default function RowLocationCell({
  value,
  format = "address",
  showIcon = true,
  showDetails = false,
  maxWidth = "max-w-xs"
}: RowLocationCellProps) {
  if (!value) {
    return <div className="text-muted-foreground">-</div>;
  }


  // Parse and validate location data
  let locationData: LocationData | null = null;

  try {
    if (typeof value === 'string') {
      // Handle empty or special strings
      if (!value.trim() || value === 'Not supported') {
        return <div className="text-muted-foreground">-</div>;
      }

      // Try to parse JSON string
      const parsed = JSON.parse(value);
      if (typeof parsed === 'object' && parsed !== null) {
        locationData = LocationHelper.migrateLegacyLocationData(parsed);
      }
    } else if (value && typeof value === 'object') {
      locationData = LocationHelper.migrateLegacyLocationData(value);
    }
  } catch (error) {
    // If JSON parsing fails, treat as plain text address
    if (typeof value === 'string' && value.trim()) {
      locationData = {
        address: value.trim(),
        lat: 0,
        lng: 0,
        formattedAddress: value.trim(),
        addressLine1: value.trim(),
        addressLine2: '',
        street: '',
        country: '',
        state: '',
        city: '',
        postalCode: ''
      };
    }
  }

  // Validate using LocationHelper
  if (!LocationHelper.hasValidLocationData(locationData)) {
    return <div className="text-muted-foreground">-</div>;
  }

  // Get display text using LocationHelper
  const displayText = LocationHelper.getDisplayAddress(locationData!, format);
  const isStructuredFormat = format === 'structured';

  // Handle structured format with multi-line display
  if (isStructuredFormat) {
    const lines = displayText.split('\n').filter(Boolean);
    return (
      <div className={cn("space-y-1", maxWidth)}>
        {showIcon && (
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-blue-500 flex-shrink-0" />
            <span className="text-sm font-medium">Address</span>
          </div>
        )}
        <div className="text-sm space-y-0.5">
          {lines.map((line, index) => (
            <div key={index} className="truncate" title={line}>
              {line}
            </div>
          ))}
        </div>
        {showDetails && locationData!.name && (
          <div className="text-xs text-muted-foreground truncate" title={locationData!.name}>
            {locationData!.name}
          </div>
        )}
      </div>
    );
  }

  // Handle full format with enhanced details
  if (format === 'full' && showDetails) {
    return (
      <div className={cn("space-y-2", maxWidth)}>
        {/* Main address display */}
        <div className="flex items-center space-x-2">
          {showIcon && <MapPin className="h-4 w-4 text-blue-500 flex-shrink-0" />}
          <div className="min-w-0 flex-1">
            <div className="truncate text-sm font-medium" title={displayText}>
              {displayText}
            </div>
            {locationData!.name && (
              <div className="text-xs text-muted-foreground truncate" title={locationData!.name}>
                {locationData!.name}
              </div>
            )}
          </div>
        </div>

        {/* Enhanced details */}
        <div className="pl-6 space-y-1">
          {/* Address components */}
          {locationData!.addressLine1 && (
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Address:</span> {locationData!.addressLine1}
              {locationData!.addressLine2 && `, ${locationData!.addressLine2}`}
            </div>
          )}

          {/* City, State, Postal Code */}
          {(locationData!.city || locationData!.state || locationData!.postalCode) && (
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Location:</span> {[
                locationData!.city,
                locationData!.state,
                locationData!.postalCode
              ].filter(Boolean).join(', ')}
            </div>
          )}

          {/* Country */}
          {locationData!.country && (
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Country:</span> {locationData!.country}
            </div>
          )}

          {/* Rating */}
          {locationData!.rating && (
            <div className="flex items-center space-x-1 text-xs">
              <Star className="h-3 w-3 text-yellow-500" />
              <span>{locationData!.rating}/5</span>
            </div>
          )}

          {/* Phone */}
          {locationData!.phoneNumber && (
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <Phone className="h-3 w-3" />
              <span>{locationData!.phoneNumber}</span>
            </div>
          )}

          {/* Website */}
          {locationData!.website && (
            <div className="flex items-center space-x-1 text-xs">
              <Globe className="h-3 w-3 text-blue-500" />
              <a
                href={locationData!.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 hover:underline"
              >
                Website
                <ExternalLink className="h-3 w-3 inline ml-1" />
              </a>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Standard single-line display - consistent with other cell types
  return (
    <div className={cn("flex items-center space-x-2 min-w-0", maxWidth)}>
      {showIcon && (
        <MapPin
          className="h-4 w-4 flex-shrink-0 text-blue-500"
        />
      )}
      <div className="min-w-0 flex-1">
        <span className={cn("block", maxWidth === "max-w-full" ? "break-words" : "truncate")} title={displayText}>
          {displayText}
        </span>
        {showDetails && (
          <div className="flex flex-wrap items-center gap-x-4 gap-y-1 mt-1">
            {locationData!.name && (
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <span className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></span>
                <span className="truncate" title={locationData!.name}>{locationData!.name}</span>
              </div>
            )}
            {locationData!.city && locationData!.state && (
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <span className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></span>
                <span className="truncate">{locationData!.city}, {locationData!.state}</span>
              </div>
            )}
            {locationData!.postalCode && (
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <span className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></span>
                <span>{locationData!.postalCode}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

import { useRef, useCallback } from 'react';

interface ZoomState {
  zoom: number;
  userAdjusted: boolean;
  lastUpdated: number;
}

// Global zoom state storage for persistence across component instances
const globalZoomState = new Map<string, ZoomState>();

// Default zoom levels
export const STREET_LEVEL_ZOOM = 17;
export const DEFAULT_CITY_ZOOM = 10;
export const ZOOM_PERSISTENCE_DURATION = 5 * 60 * 1000; // 5 minutes

export interface UseMapZoomPersistenceOptions {
  componentId?: string;
  defaultZoom?: number;
  enablePersistence?: boolean;
}

export function useMapZoomPersistence({
  componentId = 'default',
  defaultZoom = STREET_LEVEL_ZOOM,
  enablePersistence = true
}: UseMapZoomPersistenceOptions = {}) {
  const currentZoomRef = useRef<number>(defaultZoom);
  const userAdjustedZoomRef = useRef<boolean>(false);

  // Initialize zoom state from global storage if available
  const initializeZoom = useCallback((hasLocation: boolean = false) => {
    if (!enablePersistence) {
      const initialZoom = hasLocation ? STREET_LEVEL_ZOOM : DEFAULT_CITY_ZOOM;
      currentZoomRef.current = initialZoom;
      userAdjustedZoomRef.current = false;
      return initialZoom;
    }

    const stored = globalZoomState.get(componentId);
    const now = Date.now();

    // Check if stored zoom is still valid (within persistence duration)
    if (stored && (now - stored.lastUpdated) < ZOOM_PERSISTENCE_DURATION) {
      currentZoomRef.current = stored.zoom;
      userAdjustedZoomRef.current = stored.userAdjusted;
      return stored.zoom;
    }

    // Use appropriate default zoom
    const initialZoom = hasLocation ? STREET_LEVEL_ZOOM : DEFAULT_CITY_ZOOM;
    currentZoomRef.current = initialZoom;
    userAdjustedZoomRef.current = false;

    // Store initial state
    globalZoomState.set(componentId, {
      zoom: initialZoom,
      userAdjusted: false,
      lastUpdated: now
    });

    return initialZoom;
  }, [componentId, enablePersistence]);

  // Update zoom state and persist it
  const updateZoom = useCallback((newZoom: number, isUserAdjusted: boolean = false) => {
    currentZoomRef.current = newZoom;
    
    if (isUserAdjusted) {
      userAdjustedZoomRef.current = true;
    }

    if (enablePersistence) {
      globalZoomState.set(componentId, {
        zoom: newZoom,
        userAdjusted: userAdjustedZoomRef.current,
        lastUpdated: Date.now()
      });
    }
  }, [componentId, enablePersistence]);

  // Get current zoom level
  const getCurrentZoom = useCallback(() => {
    return currentZoomRef.current;
  }, []);

  // Check if user has manually adjusted zoom
  const isUserAdjusted = useCallback(() => {
    return userAdjustedZoomRef.current;
  }, []);

  // Reset user adjustment flag (for new location selections)
  const resetUserAdjustment = useCallback(() => {
    userAdjustedZoomRef.current = false;
    
    if (enablePersistence) {
      const stored = globalZoomState.get(componentId);
      if (stored) {
        globalZoomState.set(componentId, {
          ...stored,
          userAdjusted: false,
          lastUpdated: Date.now()
        });
      }
    }
  }, [componentId, enablePersistence]);

  // Get appropriate zoom for location selection
  const getLocationSelectionZoom = useCallback(() => {
    // If user has adjusted zoom recently, respect their preference
    if (userAdjustedZoomRef.current) {
      return currentZoomRef.current;
    }
    
    // Otherwise use street level zoom for new selections
    return STREET_LEVEL_ZOOM;
  }, []);

  // Clear persistence for this component
  const clearPersistence = useCallback(() => {
    if (enablePersistence) {
      globalZoomState.delete(componentId);
    }
    currentZoomRef.current = defaultZoom;
    userAdjustedZoomRef.current = false;
  }, [componentId, enablePersistence, defaultZoom]);

  return {
    initializeZoom,
    updateZoom,
    getCurrentZoom,
    isUserAdjusted,
    resetUserAdjustment,
    getLocationSelectionZoom,
    clearPersistence,
    STREET_LEVEL_ZOOM,
    DEFAULT_CITY_ZOOM
  };
}

// Utility function to clean up old zoom states
export function cleanupOldZoomStates() {
  const now = Date.now();
  for (const [key, state] of globalZoomState.entries()) {
    if ((now - state.lastUpdated) > ZOOM_PERSISTENCE_DURATION) {
      globalZoomState.delete(key);
    }
  }
}

// Auto cleanup every 10 minutes
if (typeof window !== 'undefined') {
  setInterval(cleanupOldZoomStates, 10 * 60 * 1000);
}

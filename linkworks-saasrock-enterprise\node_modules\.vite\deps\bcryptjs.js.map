{"version": 3, "sources": ["../../bcryptjs/dist/bcrypt.js"], "sourcesContent": ["/*\r\n Copyright (c) 2012 N<PERSON><PERSON> <nevins.bartolo<PERSON><EMAIL>>\r\n Copyright (c) 2012 <PERSON> <<EMAIL>>\r\n Copyright (c) 2014 <PERSON> <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEM<PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDI<PERSON>, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\n/**\r\n * @license bcrypt.js (c) 2013 Daniel Wirtz <<EMAIL>>\r\n * Released under the Apache License, Version 2.0\r\n * see: https://github.com/dcodeIO/bcrypt.js for details\r\n */\r\n(function(global, factory) {\r\n\r\n    /* AMD */ if (typeof define === 'function' && define[\"amd\"])\r\n        define([], factory);\r\n    /* CommonJS */ else if (typeof require === 'function' && typeof module === \"object\" && module && module[\"exports\"])\r\n        module[\"exports\"] = factory();\r\n    /* Global */ else\r\n        (global[\"dcodeIO\"] = global[\"dcodeIO\"] || {})[\"bcrypt\"] = factory();\r\n\r\n}(this, function() {\r\n    \"use strict\";\r\n\r\n    /**\r\n     * bcrypt namespace.\r\n     * @type {Object.<string,*>}\r\n     */\r\n    var bcrypt = {};\r\n\r\n    /**\r\n     * The random implementation to use as a fallback.\r\n     * @type {?function(number):!Array.<number>}\r\n     * @inner\r\n     */\r\n    var randomFallback = null;\r\n\r\n    /**\r\n     * Generates cryptographically secure random bytes.\r\n     * @function\r\n     * @param {number} len Bytes length\r\n     * @returns {!Array.<number>} Random bytes\r\n     * @throws {Error} If no random implementation is available\r\n     * @inner\r\n     */\r\n    function random(len) {\r\n        /* node */ if (typeof module !== 'undefined' && module && module['exports'])\r\n            try {\r\n                return require(\"crypto\")['randomBytes'](len);\r\n            } catch (e) {}\r\n        /* WCA */ try {\r\n            var a; (self['crypto']||self['msCrypto'])['getRandomValues'](a = new Uint32Array(len));\r\n            return Array.prototype.slice.call(a);\r\n        } catch (e) {}\r\n        /* fallback */ if (!randomFallback)\r\n            throw Error(\"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\");\r\n        return randomFallback(len);\r\n    }\r\n\r\n    // Test if any secure randomness source is available\r\n    var randomAvailable = false;\r\n    try {\r\n        random(1);\r\n        randomAvailable = true;\r\n    } catch (e) {}\r\n\r\n    // Default fallback, if any\r\n    randomFallback = null;\r\n    /**\r\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\r\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\r\n     *  is seeded properly!\r\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\r\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\r\n     * @see http://nodejs.org/api/crypto.html\r\n     * @see http://www.w3.org/TR/WebCryptoAPI/\r\n     */\r\n    bcrypt.setRandomFallback = function(random) {\r\n        randomFallback = random;\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a salt.\r\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {number=} seed_length Not supported.\r\n     * @returns {string} Resulting salt\r\n     * @throws {Error} If a random fallback is required but not set\r\n     * @expose\r\n     */\r\n    bcrypt.genSaltSync = function(rounds, seed_length) {\r\n        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof rounds !== 'number')\r\n            throw Error(\"Illegal arguments: \"+(typeof rounds)+\", \"+(typeof seed_length));\r\n        if (rounds < 4)\r\n            rounds = 4;\r\n        else if (rounds > 31)\r\n            rounds = 31;\r\n        var salt = [];\r\n        salt.push(\"$2a$\");\r\n        if (rounds < 10)\r\n            salt.push(\"0\");\r\n        salt.push(rounds.toString());\r\n        salt.push('$');\r\n        salt.push(base64_encode(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\r\n        return salt.join('');\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a salt.\r\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.genSalt = function(rounds, seed_length, callback) {\r\n        if (typeof seed_length === 'function')\r\n            callback = seed_length,\r\n            seed_length = undefined; // Not supported.\r\n        if (typeof rounds === 'function')\r\n            callback = rounds,\r\n            rounds = undefined;\r\n        if (typeof rounds === 'undefined')\r\n            rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        else if (typeof rounds !== 'number')\r\n            throw Error(\"illegal arguments: \"+(typeof rounds));\r\n\r\n        function _async(callback) {\r\n            nextTick(function() { // Pretty thin, but salting is fast enough\r\n                try {\r\n                    callback(null, bcrypt.genSaltSync(rounds));\r\n                } catch (err) {\r\n                    callback(err);\r\n                }\r\n            });\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\r\n     * @returns {string} Resulting hash\r\n     * @expose\r\n     */\r\n    bcrypt.hashSync = function(s, salt) {\r\n        if (typeof salt === 'undefined')\r\n            salt = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof salt === 'number')\r\n            salt = bcrypt.genSaltSync(salt);\r\n        if (typeof s !== 'string' || typeof salt !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt));\r\n        return _hash(s, salt);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {number|string} salt Salt length to generate or salt to use\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.hash = function(s, salt, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s === 'string' && typeof salt === 'number')\r\n                bcrypt.genSalt(salt, function(err, salt) {\r\n                    _hash(s, salt, callback, progressCallback);\r\n                });\r\n            else if (typeof s === 'string' && typeof salt === 'string')\r\n                _hash(s, salt, callback, progressCallback);\r\n            else\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt))));\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Compares two strings of the same length in constant time.\r\n     * @param {string} known Must be of the correct length\r\n     * @param {string} unknown Must be the same length as `known`\r\n     * @returns {boolean}\r\n     * @inner\r\n     */\r\n    function safeStringCompare(known, unknown) {\r\n        var right = 0,\r\n            wrong = 0;\r\n        for (var i=0, k=known.length; i<k; ++i) {\r\n            if (known.charCodeAt(i) === unknown.charCodeAt(i))\r\n                ++right;\r\n            else\r\n                ++wrong;\r\n        }\r\n        // Prevent removal of unused variables (never true, actually)\r\n        if (right < 0)\r\n            return false;\r\n        return wrong === 0;\r\n    }\r\n\r\n    /**\r\n     * Synchronously tests a string against a hash.\r\n     * @param {string} s String to compare\r\n     * @param {string} hash Hash to test against\r\n     * @returns {boolean} true if matching, otherwise false\r\n     * @throws {Error} If an argument is illegal\r\n     * @expose\r\n     */\r\n    bcrypt.compareSync = function(s, hash) {\r\n        if (typeof s !== \"string\" || typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash));\r\n        if (hash.length !== 60)\r\n            return false;\r\n        return safeStringCompare(bcrypt.hashSync(s, hash.substr(0, hash.length-31)), hash);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously compares the given data against the given hash.\r\n     * @param {string} s Data to compare\r\n     * @param {string} hash Data to be compared to\r\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.compare = function(s, hash, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s !== \"string\" || typeof hash !== \"string\") {\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash))));\r\n                return;\r\n            }\r\n            if (hash.length !== 60) {\r\n                nextTick(callback.bind(this, null, false));\r\n                return;\r\n            }\r\n            bcrypt.hash(s, hash.substr(0, 29), function(err, comp) {\r\n                if (err)\r\n                    callback(err);\r\n                else\r\n                    callback(null, safeStringCompare(comp, hash));\r\n            }, progressCallback);\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Gets the number of rounds used to encrypt the specified hash.\r\n     * @param {string} hash Hash to extract the used number of rounds from\r\n     * @returns {number} Number of rounds used\r\n     * @throws {Error} If `hash` is not a string\r\n     * @expose\r\n     */\r\n    bcrypt.getRounds = function(hash) {\r\n        if (typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        return parseInt(hash.split(\"$\")[2], 10);\r\n    };\r\n\r\n    /**\r\n     * Gets the salt portion from a hash. Does not validate the hash.\r\n     * @param {string} hash Hash to extract the salt from\r\n     * @returns {string} Extracted salt part\r\n     * @throws {Error} If `hash` is not a string or otherwise invalid\r\n     * @expose\r\n     */\r\n    bcrypt.getSalt = function(hash) {\r\n        if (typeof hash !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        if (hash.length !== 60)\r\n            throw Error(\"Illegal hash length: \"+hash.length+\" != 60\");\r\n        return hash.substring(0, 29);\r\n    };\r\n\r\n    /**\r\n     * Continues with the callback on the next tick.\r\n     * @function\r\n     * @param {function(...[*])} callback Callback to execute\r\n     * @inner\r\n     */\r\n    var nextTick = typeof process !== 'undefined' && process && typeof process.nextTick === 'function'\r\n        ? (typeof setImmediate === 'function' ? setImmediate : process.nextTick)\r\n        : setTimeout;\r\n\r\n    /**\r\n     * Converts a JavaScript string to UTF8 bytes.\r\n     * @param {string} str String\r\n     * @returns {!Array.<number>} UTF8 bytes\r\n     * @inner\r\n     */\r\n    function stringToBytes(str) {\r\n        var out = [],\r\n            i = 0;\r\n        utfx.encodeUTF16toUTF8(function() {\r\n            if (i >= str.length) return null;\r\n            return str.charCodeAt(i++);\r\n        }, function(b) {\r\n            out.push(b);\r\n        });\r\n        return out;\r\n    }\r\n\r\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\r\n\r\n    /**\r\n     * bcrypt's own non-standard base64 dictionary.\r\n     * @type {!Array.<string>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_CODE = \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split('');\r\n\r\n    /**\r\n     * @type {!Array.<number>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_INDEX = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0,\r\n        1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, -1, -1, -1, -1, -1, -1,\r\n        -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\r\n        20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28, 29, 30,\r\n        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\r\n        48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1];\r\n\r\n    /**\r\n     * @type {!function(...number):string}\r\n     * @inner\r\n     */\r\n    var stringFromCharCode = String.fromCharCode;\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input.\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @inner\r\n     */\r\n    function base64_encode(b, len) {\r\n        var off = 0,\r\n            rs = [],\r\n            c1, c2;\r\n        if (len <= 0 || len > b.length)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < len) {\r\n            c1 = b[off++] & 0xff;\r\n            rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\r\n            c1 = (c1 & 0x03) << 4;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 4) & 0x0f;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            c1 = (c2 & 0x0f) << 2;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 6) & 0x03;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            rs.push(BASE64_CODE[c2 & 0x3f]);\r\n        }\r\n        return rs.join('');\r\n    }\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output.\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @inner\r\n     */\r\n    function base64_decode(s, len) {\r\n        var off = 0,\r\n            slen = s.length,\r\n            olen = 0,\r\n            rs = [],\r\n            c1, c2, c3, c4, o, code;\r\n        if (len <= 0)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < slen - 1 && olen < len) {\r\n            code = s.charCodeAt(off++);\r\n            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            code = s.charCodeAt(off++);\r\n            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c1 == -1 || c2 == -1)\r\n                break;\r\n            o = (c1 << 2) >>> 0;\r\n            o |= (c2 & 0x30) >> 4;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c3 == -1)\r\n                break;\r\n            o = ((c2 & 0x0f) << 4) >>> 0;\r\n            o |= (c3 & 0x3c) >> 2;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            o = ((c3 & 0x03) << 6) >>> 0;\r\n            o |= c4;\r\n            rs.push(stringFromCharCode(o));\r\n            ++olen;\r\n        }\r\n        var res = [];\r\n        for (off = 0; off<olen; off++)\r\n            res.push(rs[off].charCodeAt(0));\r\n        return res;\r\n    }\r\n\r\n    /**\r\n     * utfx-embeddable (c) 2014 Daniel Wirtz <<EMAIL>>\r\n     * Released under the Apache License, Version 2.0\r\n     * see: https://github.com/dcodeIO/utfx for details\r\n     */\r\n    var utfx = function() {\r\n        \"use strict\";\r\n\r\n        /**\r\n         * utfx namespace.\r\n         * @inner\r\n         * @type {!Object.<string,*>}\r\n         */\r\n        var utfx = {};\r\n\r\n        /**\r\n         * Maximum valid code point.\r\n         * @type {number}\r\n         * @const\r\n         */\r\n        utfx.MAX_CODEPOINT = 0x10FFFF;\r\n\r\n        /**\r\n         * Encodes UTF8 code points to UTF8 bytes.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte\r\n         */\r\n        utfx.encodeUTF8 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src,\r\n                src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp < 0x80)\r\n                    dst(cp&0x7F);\r\n                else if (cp < 0x800)\r\n                    dst(((cp>>6)&0x1F)|0xC0),\r\n                    dst((cp&0x3F)|0x80);\r\n                else if (cp < 0x10000)\r\n                    dst(((cp>>12)&0x0F)|0xE0),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                else\r\n                    dst(((cp>>18)&0x07)|0xF0),\r\n                    dst(((cp>>12)&0x3F)|0x80),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Decodes UTF8 bytes to UTF8 code points.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each decoded code point.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\r\n         *  remaining bytes.\r\n         */\r\n        utfx.decodeUTF8 = function(src, dst) {\r\n            var a, b, c, d, fail = function(b) {\r\n                b = b.slice(0, b.indexOf(null));\r\n                var err = Error(b.toString());\r\n                err.name = \"TruncatedError\";\r\n                err['bytes'] = b;\r\n                throw err;\r\n            };\r\n            while ((a = src()) !== null) {\r\n                if ((a&0x80) === 0)\r\n                    dst(a);\r\n                else if ((a&0xE0) === 0xC0)\r\n                    ((b = src()) === null) && fail([a, b]),\r\n                    dst(((a&0x1F)<<6) | (b&0x3F));\r\n                else if ((a&0xF0) === 0xE0)\r\n                    ((b=src()) === null || (c=src()) === null) && fail([a, b, c]),\r\n                    dst(((a&0x0F)<<12) | ((b&0x3F)<<6) | (c&0x3F));\r\n                else if ((a&0xF8) === 0xF0)\r\n                    ((b=src()) === null || (c=src()) === null || (d=src()) === null) && fail([a, b, c ,d]),\r\n                    dst(((a&0x07)<<18) | ((b&0x3F)<<12) | ((c&0x3F)<<6) | (d&0x3F));\r\n                else throw RangeError(\"Illegal starting byte: \"+a);\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts UTF16 characters to UTF8 code points.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each converted code\r\n         *  point.\r\n         */\r\n        utfx.UTF16toUTF8 = function(src, dst) {\r\n            var c1, c2 = null;\r\n            while (true) {\r\n                if ((c1 = c2 !== null ? c2 : src()) === null)\r\n                    break;\r\n                if (c1 >= 0xD800 && c1 <= 0xDFFF) {\r\n                    if ((c2 = src()) !== null) {\r\n                        if (c2 >= 0xDC00 && c2 <= 0xDFFF) {\r\n                            dst((c1-0xD800)*0x400+c2-0xDC00+0x10000);\r\n                            c2 = null; continue;\r\n                        }\r\n                    }\r\n                }\r\n                dst(c1);\r\n            }\r\n            if (c2 !== null) dst(c2);\r\n        };\r\n\r\n        /**\r\n         * Converts UTF8 code points to UTF16 characters.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a code point is out of range\r\n         */\r\n        utfx.UTF8toUTF16 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src, src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp <= 0xFFFF)\r\n                    dst(cp);\r\n                else\r\n                    cp -= 0x10000,\r\n                    dst((cp>>10)+0xD800),\r\n                    dst((cp%0x400)+0xDC00);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts and encodes UTF16 characters to UTF8 bytes.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively `null`\r\n         *  if there are no more characters left.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte.\r\n         */\r\n        utfx.encodeUTF16toUTF8 = function(src, dst) {\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                utfx.encodeUTF8(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Decodes and converts UTF8 bytes to UTF16 characters.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\r\n         */\r\n        utfx.decodeUTF8toUTF16 = function(src, dst) {\r\n            utfx.decodeUTF8(src, function(cp) {\r\n                utfx.UTF8toUTF16(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Calculates the byte length of an UTF8 code point.\r\n         * @param {number} cp UTF8 code point\r\n         * @returns {number} Byte length\r\n         */\r\n        utfx.calculateCodePoint = function(cp) {\r\n            return (cp < 0x80) ? 1 : (cp < 0x800) ? 2 : (cp < 0x10000) ? 3 : 4;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 bytes required to store UTF8 code points.\r\n         * @param {(!function():number|null)} src Code points source as a function returning the next code point respectively\r\n         *  `null` if there are no more code points left.\r\n         * @returns {number} The number of UTF8 bytes required\r\n         */\r\n        utfx.calculateUTF8 = function(src) {\r\n            var cp, l=0;\r\n            while ((cp = src()) !== null)\r\n                l += utfx.calculateCodePoint(cp);\r\n            return l;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\r\n         * @param {(!function():number|null)} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @returns {!Array.<number>} The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\r\n         */\r\n        utfx.calculateUTF16asUTF8 = function(src) {\r\n            var n=0, l=0;\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                ++n; l += utfx.calculateCodePoint(cp);\r\n            });\r\n            return [n,l];\r\n        };\r\n\r\n        return utfx;\r\n    }();\r\n\r\n    Date.now = Date.now || function() { return +new Date; };\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BCRYPT_SALT_LEN = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BLOWFISH_NUM_ROUNDS = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var MAX_EXECUTION_TIME = 100;\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var P_ORIG = [\r\n        0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822,\r\n        0x299f31d0, 0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377,\r\n        0xbe5466cf, 0x34e90c6c, 0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5,\r\n        0xb5470917, 0x9216d5d9, 0x8979fb1b\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var S_ORIG = [\r\n        0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed,\r\n        0x6a267e96, 0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7,\r\n        0x0801f2e2, 0x858efc16, 0x636920d8, 0x71574e69, 0xa458fea3,\r\n        0xf4933d7e, 0x0d95748f, 0x728eb658, 0x718bcd58, 0x82154aee,\r\n        0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013, 0xc5d1b023,\r\n        0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\r\n        0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda,\r\n        0x55605c60, 0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440,\r\n        0x55ca396a, 0x2aab10b6, 0xb4cc5c34, 0x1141e8ce, 0xa15486af,\r\n        0x7c72e993, 0xb3ee1411, 0x636fbc2a, 0x2ba9c55d, 0x741831f6,\r\n        0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c, 0x7a325381,\r\n        0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\r\n        0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d,\r\n        0xe98575b1, 0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5,\r\n        0x0f6d6ff3, 0x83f44239, 0x2e0b4482, 0xa4842004, 0x69c8f04a,\r\n        0x9e1f9b5e, 0x21c66842, 0xf6e96c9a, 0x670c9c61, 0xabd388f0,\r\n        0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3, 0x6eef0b6c,\r\n        0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\r\n        0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3,\r\n        0x3b8b5ebe, 0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6,\r\n        0x4ed3aa62, 0x363f7706, 0x1bfedf72, 0x429b023d, 0x37d0d724,\r\n        0xd00a1248, 0xdb0fead3, 0x49f1c09b, 0x075372c9, 0x80991b7b,\r\n        0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b, 0x976ce0bd,\r\n        0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\r\n        0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f,\r\n        0x9b30952c, 0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd,\r\n        0x660f2807, 0x192e4bb3, 0xc0cba857, 0x45c8740f, 0xd20b5f39,\r\n        0xb9d3fbdb, 0x5579c0bd, 0x1a60320a, 0xd6a100c6, 0x402c7279,\r\n        0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8, 0x3c7516df,\r\n        0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\r\n        0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e,\r\n        0xdf1769db, 0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573,\r\n        0x695b27b0, 0xbbca58c8, 0xe1ffa35d, 0xb8f011a0, 0x10fa3d98,\r\n        0xfd2183b8, 0x4afcb56c, 0x2dd1d35b, 0x9a53e479, 0xb6f84565,\r\n        0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33, 0x62fb1341,\r\n        0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\r\n        0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0,\r\n        0xafc725e0, 0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64,\r\n        0x8888b812, 0x900df01c, 0x4fad5ea0, 0x688fc31c, 0xd1cff191,\r\n        0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777, 0xea752dfe, 0x8b021fa1,\r\n        0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299, 0xb4a84fe0,\r\n        0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\r\n        0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5,\r\n        0xfb9d35cf, 0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49,\r\n        0x00250e2d, 0x2071b35e, 0x226800bb, 0x57b8e0af, 0x2464369b,\r\n        0xf009b91e, 0x5563911d, 0x59dfa6aa, 0x78c14389, 0xd95a537f,\r\n        0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9, 0x11c81968,\r\n        0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\r\n        0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5,\r\n        0x571be91f, 0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6,\r\n        0xff34052e, 0xc5855664, 0x53b02d5d, 0xa99f8fa1, 0x08ba4799,\r\n        0x6e85076a, 0x4b7a70e9, 0xb5b32944, 0xdb75092e, 0xc4192623,\r\n        0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266, 0xecaa8c71,\r\n        0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\r\n        0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6,\r\n        0x99f73fd6, 0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1,\r\n        0x4cdd2086, 0x8470eb26, 0x6382e9c6, 0x021ecc5e, 0x09686b3f,\r\n        0x3ebaefc9, 0x3c971814, 0x6b6a70a1, 0x687f3584, 0x52a0e286,\r\n        0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c, 0x8e7d44ec,\r\n        0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\r\n        0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9,\r\n        0x7ca92ff6, 0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc,\r\n        0xc8b57634, 0x9af3dda7, 0xa9446146, 0x0fd0030e, 0xecc8c73e,\r\n        0xa4751e41, 0xe238cd99, 0x3bea0e2f, 0x3280bba1, 0x183eb331,\r\n        0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf, 0x2cb81290,\r\n        0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\r\n        0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6,\r\n        0x9f84cd87, 0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c,\r\n        0xec7aec3a, 0xdb851dfa, 0x63094366, 0xc464c3d2, 0xef1c1847,\r\n        0x3215d908, 0xdd433b37, 0x24c2ba16, 0x12a14d43, 0x2a65c451,\r\n        0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55, 0x81ac77d6,\r\n        0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\r\n        0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570,\r\n        0xeae96fb1, 0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa,\r\n        0x2965dcb9, 0x99e71d0f, 0x803e89d6, 0x5266c825, 0x2e4cc978,\r\n        0x9c10b36a, 0xc6150eba, 0x94e2ea78, 0xa5fc3c53, 0x1e0a2df4,\r\n        0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960, 0x5223a708,\r\n        0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\r\n        0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185,\r\n        0x68ab9802, 0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84,\r\n        0x1521b628, 0x29076170, 0xecdd4775, 0x619f1510, 0x13cca830,\r\n        0xeb61bd96, 0x0334fe1e, 0xaa0363cf, 0xb5735c90, 0x4c70a239,\r\n        0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7, 0x9cab5cab,\r\n        0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\r\n        0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19,\r\n        0x875fa099, 0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77,\r\n        0x11ed935f, 0x16681281, 0x0e358829, 0xc7e61fd6, 0x96dedfa1,\r\n        0x7858ba99, 0x57f584a5, 0x1b227263, 0x9b83c3ff, 0x1ac24696,\r\n        0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128, 0x58ebf2ef,\r\n        0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\r\n        0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15,\r\n        0xfacb4fd0, 0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105,\r\n        0xd81e799e, 0x86854dc7, 0xe44b476a, 0x3d816250, 0xcf62a1f2,\r\n        0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3, 0x7f1524c3, 0x69cb7492,\r\n        0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d, 0x1462b174,\r\n        0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\r\n        0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759,\r\n        0xcbee7460, 0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e,\r\n        0xe8efd855, 0x61d99735, 0xa969a7aa, 0xc50c06c2, 0x5a04abfc,\r\n        0x800bcadc, 0x9e447a2e, 0xc3453484, 0xfdd56705, 0x0e1e9ec9,\r\n        0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340, 0xc5c43465,\r\n        0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\r\n        0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c,\r\n        0x94692934, 0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068,\r\n        0xd4082471, 0x3320f46a, 0x43b7d4b7, 0x500061af, 0x1e39f62e,\r\n        0x97244546, 0x14214f74, 0xbf8b8840, 0x4d95fc1d, 0x96b591af,\r\n        0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785, 0x7fac6dd0,\r\n        0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\r\n        0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462,\r\n        0xd7486900, 0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c,\r\n        0xb58ce006, 0x7af4d6b6, 0xaace1e7c, 0xd3375fec, 0xce78a399,\r\n        0x406b2a42, 0x20fe9e35, 0xd9f385b9, 0xee39d7ab, 0x3b124e8b,\r\n        0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2, 0x3a6efa74,\r\n        0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\r\n        0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7,\r\n        0xd096954b, 0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33,\r\n        0xa62a4a56, 0x3f3125f9, 0x5ef47e1c, 0x9029317c, 0xfdf8e802,\r\n        0x04272f70, 0x80bb155c, 0x05282ce3, 0x95c11548, 0xe4c66d22,\r\n        0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f, 0x404779a4,\r\n        0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\r\n        0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2,\r\n        0x02e1329e, 0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1,\r\n        0x3b240b62, 0xeebeb922, 0x85b2a20e, 0xe6ba0d99, 0xde720c8c,\r\n        0x2da2f728, 0xd0127845, 0x95b794fd, 0x647d0862, 0xe7ccf5f0,\r\n        0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e, 0x0a476341,\r\n        0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\r\n        0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b,\r\n        0xdcd0e804, 0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b,\r\n        0x667b9ffb, 0xcedb7d9c, 0xa091cf0b, 0xd9155ea3, 0xbb132f88,\r\n        0x515bad24, 0x7b9479bf, 0x763bd6eb, 0x37392eb3, 0xcc115979,\r\n        0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b, 0x12754ccc,\r\n        0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\r\n        0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659,\r\n        0x0a121386, 0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f,\r\n        0xbebfe988, 0x64e4c3fe, 0x9dbc8057, 0xf0f7c086, 0x60787bf8,\r\n        0x6003604d, 0xd1fd8346, 0xf6381fb0, 0x7745ae04, 0xd736fccc,\r\n        0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f, 0x77a057be,\r\n        0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\r\n        0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255,\r\n        0x46fcd9b9, 0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2,\r\n        0x466e598e, 0x20b45770, 0x8cd55591, 0xc902de4c, 0xb90bace1,\r\n        0xbb8205d0, 0x11a86248, 0x7574a99e, 0xb77f19b6, 0xe0a9dc09,\r\n        0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c, 0x4a99a025,\r\n        0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\r\n        0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01,\r\n        0xa70683fa, 0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641,\r\n        0xc3604c06, 0x61a806b5, 0xf0177a28, 0xc0f586e0, 0x006058aa,\r\n        0x30dc7d62, 0x11e69ed7, 0x2338ea63, 0x53c2dd94, 0xc2c21634,\r\n        0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76, 0x6f05e409,\r\n        0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\r\n        0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3,\r\n        0x4dad0fc4, 0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c,\r\n        0x6fd5c7e7, 0x56e14ec4, 0x362abfce, 0xddc6c837, 0xd79a3234,\r\n        0x92638212, 0x670efa8e, 0x406000e0, 0x3a39ce37, 0xd3faf5cf,\r\n        0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742, 0xd3822740,\r\n        0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\r\n        0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f,\r\n        0xbc946e79, 0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d,\r\n        0xd5730a1d, 0x4cd04dc6, 0x2939bbdb, 0xa9ba4650, 0xac9526e8,\r\n        0xbe5ee304, 0xa1fad5f0, 0x6a2d519a, 0x63ef8ce2, 0x9a86ee22,\r\n        0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4, 0x83c061ba,\r\n        0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\r\n        0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69,\r\n        0x77fa0a59, 0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593,\r\n        0xe990fd5a, 0x9e34d797, 0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a,\r\n        0x017da67d, 0xd1cf3ed6, 0x7c7d2d28, 0x1f9f25cf, 0xadf2b89b,\r\n        0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6, 0x47b0acfd,\r\n        0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\r\n        0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4,\r\n        0x88f46dba, 0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2,\r\n        0x97271aec, 0xa93a072a, 0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb,\r\n        0x26dcf319, 0x7533d928, 0xb155fdf5, 0x03563482, 0x8aba3cbb,\r\n        0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f, 0x4de81751,\r\n        0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\r\n        0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369,\r\n        0x6413e680, 0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166,\r\n        0xb39a460a, 0x6445c0dd, 0x586cdecf, 0x1c20c8ae, 0x5bbef7dd,\r\n        0x1b588d40, 0xccd2017f, 0x6bb4e3bb, 0xdda26a7e, 0x3a59ff45,\r\n        0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb, 0x8d6612ae,\r\n        0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\r\n        0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08,\r\n        0x4eb4e2cc, 0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d,\r\n        0x06b89fb4, 0xce6ea048, 0x6f3f3b82, 0x3520ab82, 0x011a1d4b,\r\n        0x277227f8, 0x611560b1, 0xe7933fdc, 0xbb3a792b, 0x344525bd,\r\n        0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9, 0xe01cc87e,\r\n        0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\r\n        0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c,\r\n        0xe0b12b4f, 0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c,\r\n        0xbf97222c, 0x15e6fc2a, 0x0f91fc71, 0x9b941525, 0xfae59361,\r\n        0xceb69ceb, 0xc2a86459, 0x12baa8d1, 0xb6c1075e, 0xe3056a0c,\r\n        0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b, 0x4c98a0be,\r\n        0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\r\n        0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d,\r\n        0x9b992f2e, 0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891,\r\n        0xce6279cf, 0xcd3e7e6f, 0x1618b166, 0xfd2c1d05, 0x848fd2c5,\r\n        0xf6fb2299, 0xf523f357, 0xa6327623, 0x93a83531, 0x56cccd02,\r\n        0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc, 0xde966292,\r\n        0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\r\n        0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2,\r\n        0x35bdd2f6, 0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b,\r\n        0x53113ec0, 0x1640e3d3, 0x38abbd60, 0x2547adf0, 0xba38209c,\r\n        0xf746ce76, 0x77afa1c5, 0x20756060, 0x85cbfe4e, 0x8ae88dd8,\r\n        0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c, 0x01c36ae4,\r\n        0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\r\n        0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var C_ORIG = [\r\n        0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944,\r\n        0x6f756274\r\n    ];\r\n\r\n    /**\r\n     * @param {Array.<number>} lr\r\n     * @param {number} off\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @returns {Array.<number>}\r\n     * @inner\r\n     */\r\n    function _encipher(lr, off, P, S) { // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\r\n        var n,\r\n            l = lr[off],\r\n            r = lr[off + 1];\r\n\r\n        l ^= P[0];\r\n\r\n        /*\r\n        for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\r\n            // Feistel substitution on left word\r\n            n  = S[l >>> 24],\r\n            n += S[0x100 | ((l >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((l >> 8) & 0xff)],\r\n            n += S[0x300 | (l & 0xff)],\r\n            r ^= n ^ P[++i],\r\n            // Feistel substitution on right word\r\n            n  = S[r >>> 24],\r\n            n += S[0x100 | ((r >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((r >> 8) & 0xff)],\r\n            n += S[0x300 | (r & 0xff)],\r\n            l ^= n ^ P[++i];\r\n        */\r\n\r\n        //The following is an unrolled version of the above loop.\r\n        //Iteration 0\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[1];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[2];\r\n        //Iteration 1\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[3];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[4];\r\n        //Iteration 2\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[5];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[6];\r\n        //Iteration 3\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[7];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[8];\r\n        //Iteration 4\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[9];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[10];\r\n        //Iteration 5\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[11];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[12];\r\n        //Iteration 6\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[13];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[14];\r\n        //Iteration 7\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[15];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[16];\r\n\r\n        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\r\n        lr[off + 1] = l;\r\n        return lr;\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} data\r\n     * @param {number} offp\r\n     * @returns {{key: number, offp: number}}\r\n     * @inner\r\n     */\r\n    function _streamtoword(data, offp) {\r\n        for (var i = 0, word = 0; i < 4; ++i)\r\n            word = (word << 8) | (data[offp] & 0xff),\r\n            offp = (offp + 1) % data.length;\r\n        return { key: word, offp: offp };\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _key(key, P, S) {\r\n        var offset = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offset),\r\n            offset = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        for (i = 0; i < plen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Expensive key schedule Blowfish.\r\n     * @param {Array.<number>} data\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _ekskey(data, key, P, S) {\r\n        var offp = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offp),\r\n            offp = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        offp = 0;\r\n        for (i = 0; i < plen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Internaly crypts a string.\r\n     * @param {Array.<number>} b Bytes to crypt\r\n     * @param {Array.<number>} salt Salt bytes to use\r\n     * @param {number} rounds Number of rounds\r\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\r\n     *  omitted, the operation will be performed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _crypt(b, salt, rounds, callback, progressCallback) {\r\n        var cdata = C_ORIG.slice(),\r\n            clen = cdata.length,\r\n            err;\r\n\r\n        // Validate\r\n        if (rounds < 4 || rounds > 31) {\r\n            err = Error(\"Illegal number of rounds (4-31): \"+rounds);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        if (salt.length !== BCRYPT_SALT_LEN) {\r\n            err =Error(\"Illegal salt length: \"+salt.length+\" != \"+BCRYPT_SALT_LEN);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        rounds = (1 << rounds) >>> 0;\r\n\r\n        var P, S, i = 0, j;\r\n\r\n        //Use typed arrays when available - huge speedup!\r\n        if (Int32Array) {\r\n            P = new Int32Array(P_ORIG);\r\n            S = new Int32Array(S_ORIG);\r\n        } else {\r\n            P = P_ORIG.slice();\r\n            S = S_ORIG.slice();\r\n        }\r\n\r\n        _ekskey(salt, b, P, S);\r\n\r\n        /**\r\n         * Calcualtes the next round.\r\n         * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\r\n         * @inner\r\n         */\r\n        function next() {\r\n            if (progressCallback)\r\n                progressCallback(i / rounds);\r\n            if (i < rounds) {\r\n                var start = Date.now();\r\n                for (; i < rounds;) {\r\n                    i = i + 1;\r\n                    _key(b, P, S);\r\n                    _key(salt, P, S);\r\n                    if (Date.now() - start > MAX_EXECUTION_TIME)\r\n                        break;\r\n                }\r\n            } else {\r\n                for (i = 0; i < 64; i++)\r\n                    for (j = 0; j < (clen >> 1); j++)\r\n                        _encipher(cdata, j << 1, P, S);\r\n                var ret = [];\r\n                for (i = 0; i < clen; i++)\r\n                    ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\r\n                    ret.push((cdata[i] & 0xff) >>> 0);\r\n                if (callback) {\r\n                    callback(null, ret);\r\n                    return;\r\n                } else\r\n                    return ret;\r\n            }\r\n            if (callback)\r\n                nextTick(next);\r\n        }\r\n\r\n        // Async\r\n        if (typeof callback !== 'undefined') {\r\n            next();\r\n\r\n            // Sync\r\n        } else {\r\n            var res;\r\n            while (true)\r\n                if (typeof(res = next()) !== 'undefined')\r\n                    return res || [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Internally hashes a string.\r\n     * @param {string} s String to hash\r\n     * @param {?string} salt Salt to use, actually never null\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\r\n     *  hashing is perormed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _hash(s, salt, callback, progressCallback) {\r\n        var err;\r\n        if (typeof s !== 'string' || typeof salt !== 'string') {\r\n            err = Error(\"Invalid string / salt: Not a string\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n\r\n        // Validate the salt\r\n        var minor, offset;\r\n        if (salt.charAt(0) !== '$' || salt.charAt(1) !== '2') {\r\n            err = Error(\"Invalid salt version: \"+salt.substring(0,2));\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n        if (salt.charAt(2) === '$')\r\n            minor = String.fromCharCode(0),\r\n            offset = 3;\r\n        else {\r\n            minor = salt.charAt(2);\r\n            if ((minor !== 'a' && minor !== 'b' && minor !== 'y') || salt.charAt(3) !== '$') {\r\n                err = Error(\"Invalid salt revision: \"+salt.substring(2,4));\r\n                if (callback) {\r\n                    nextTick(callback.bind(this, err));\r\n                    return;\r\n                } else\r\n                    throw err;\r\n            }\r\n            offset = 4;\r\n        }\r\n\r\n        // Extract number of rounds\r\n        if (salt.charAt(offset + 2) > '$') {\r\n            err = Error(\"Missing salt rounds\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\r\n            r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\r\n            rounds = r1 + r2,\r\n            real_salt = salt.substring(offset + 3, offset + 25);\r\n        s += minor >= 'a' ? \"\\x00\" : \"\";\r\n\r\n        var passwordb = stringToBytes(s),\r\n            saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\r\n\r\n        /**\r\n         * Finishes hashing.\r\n         * @param {Array.<number>} bytes Byte array\r\n         * @returns {string}\r\n         * @inner\r\n         */\r\n        function finish(bytes) {\r\n            var res = [];\r\n            res.push(\"$2\");\r\n            if (minor >= 'a')\r\n                res.push(minor);\r\n            res.push(\"$\");\r\n            if (rounds < 10)\r\n                res.push(\"0\");\r\n            res.push(rounds.toString());\r\n            res.push(\"$\");\r\n            res.push(base64_encode(saltb, saltb.length));\r\n            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\r\n            return res.join('');\r\n        }\r\n\r\n        // Sync\r\n        if (typeof callback == 'undefined')\r\n            return finish(_crypt(passwordb, saltb, rounds));\r\n\r\n        // Async\r\n        else {\r\n            _crypt(passwordb, saltb, rounds, function(err, bytes) {\r\n                if (err)\r\n                    callback(err, null);\r\n                else\r\n                    callback(null, finish(bytes));\r\n            }, progressCallback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @expose\r\n     */\r\n    bcrypt.encodeBase64 = base64_encode;\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @expose\r\n     */\r\n    bcrypt.decodeBase64 = base64_decode;\r\n\r\n    return bcrypt;\r\n}));\r\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAiCA,KAAC,SAAS,QAAQ,SAAS;AAEb,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AACtD,eAAO,CAAC,GAAG,OAAO;AAAA,eACE,OAAO,cAAY,cAAc,OAAO,WAAW,YAAY,UAAU,OAAO,SAAS;AAC7G,eAAO,SAAS,IAAI,QAAQ;AAAA;AAE5B,SAAC,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,QAAQ,IAAI,QAAQ;AAAA,IAE1E,GAAE,SAAM,WAAW;AACf;AAMA,UAAI,SAAS,CAAC;AAOd,UAAI,iBAAiB;AAUrB,eAAS,OAAO,KAAK;AACN,YAAI,OAAO,WAAW,eAAe,UAAU,OAAO,SAAS;AACtE,cAAI;AACA,mBAAO,iBAAkB,aAAa,EAAE,GAAG;AAAA,UAC/C,SAAS,GAAG;AAAA,UAAC;AACP,YAAI;AACV,cAAI;AAAG,WAAC,KAAK,QAAQ,KAAG,KAAK,UAAU,GAAG,iBAAiB,EAAE,IAAI,IAAI,YAAY,GAAG,CAAC;AACrF,iBAAO,MAAM,UAAU,MAAM,KAAK,CAAC;AAAA,QACvC,SAAS,GAAG;AAAA,QAAC;AACE,YAAI,CAAC;AAChB,gBAAM,MAAM,2GAA2G;AAC3H,eAAO,eAAe,GAAG;AAAA,MAC7B;AAGA,UAAI,kBAAkB;AACtB,UAAI;AACA,eAAO,CAAC;AACR,0BAAkB;AAAA,MACtB,SAAS,GAAG;AAAA,MAAC;AAGb,uBAAiB;AAUjB,aAAO,oBAAoB,SAASA,SAAQ;AACxC,yBAAiBA;AAAA,MACrB;AAUA,aAAO,cAAc,SAAS,QAAQ,aAAa;AAC/C,iBAAS,UAAU;AACnB,YAAI,OAAO,WAAW;AAClB,gBAAM,MAAM,wBAAuB,OAAO,SAAQ,OAAM,OAAO,WAAY;AAC/E,YAAI,SAAS;AACT,mBAAS;AAAA,iBACJ,SAAS;AACd,mBAAS;AACb,YAAI,OAAO,CAAC;AACZ,aAAK,KAAK,MAAM;AAChB,YAAI,SAAS;AACT,eAAK,KAAK,GAAG;AACjB,aAAK,KAAK,OAAO,SAAS,CAAC;AAC3B,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,cAAc,OAAO,eAAe,GAAG,eAAe,CAAC;AACjE,eAAO,KAAK,KAAK,EAAE;AAAA,MACvB;AAWA,aAAO,UAAU,SAAS,QAAQ,aAAa,UAAU;AACrD,YAAI,OAAO,gBAAgB;AACvB,qBAAW,aACX,cAAc;AAClB,YAAI,OAAO,WAAW;AAClB,qBAAW,QACX,SAAS;AACb,YAAI,OAAO,WAAW;AAClB,mBAAS;AAAA,iBACJ,OAAO,WAAW;AACvB,gBAAM,MAAM,wBAAuB,OAAO,MAAO;AAErD,iBAAS,OAAOC,WAAU;AACtB,mBAAS,WAAW;AAChB,gBAAI;AACA,cAAAA,UAAS,MAAM,OAAO,YAAY,MAAM,CAAC;AAAA,YAC7C,SAAS,KAAK;AACV,cAAAA,UAAS,GAAG;AAAA,YAChB;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,YAAI,UAAU;AACV,cAAI,OAAO,aAAa;AACpB,kBAAM,MAAM,uBAAqB,OAAO,QAAS;AACrD,iBAAO,QAAQ;AAAA,QACnB;AACI,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,mBAAO,SAAS,KAAK,KAAK;AACtB,kBAAI,KAAK;AACL,uBAAO,GAAG;AACV;AAAA,cACJ;AACA,sBAAQ,GAAG;AAAA,YACf,CAAC;AAAA,UACL,CAAC;AAAA,MACT;AASA,aAAO,WAAW,SAAS,GAAG,MAAM;AAChC,YAAI,OAAO,SAAS;AAChB,iBAAO;AACX,YAAI,OAAO,SAAS;AAChB,iBAAO,OAAO,YAAY,IAAI;AAClC,YAAI,OAAO,MAAM,YAAY,OAAO,SAAS;AACzC,gBAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAO,IAAK;AACnE,eAAO,MAAM,GAAG,IAAI;AAAA,MACxB;AAaA,aAAO,OAAO,SAAS,GAAG,MAAM,UAAU,kBAAkB;AAExD,iBAAS,OAAOA,WAAU;AACtB,cAAI,OAAO,MAAM,YAAY,OAAO,SAAS;AACzC,mBAAO,QAAQ,MAAM,SAAS,KAAKC,OAAM;AACrC,oBAAM,GAAGA,OAAMD,WAAU,gBAAgB;AAAA,YAC7C,CAAC;AAAA,mBACI,OAAO,MAAM,YAAY,OAAO,SAAS;AAC9C,kBAAM,GAAG,MAAMA,WAAU,gBAAgB;AAAA;AAEzC,qBAASA,UAAS,KAAK,MAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAO,IAAK,CAAC,CAAC;AAAA,QAChG;AAEA,YAAI,UAAU;AACV,cAAI,OAAO,aAAa;AACpB,kBAAM,MAAM,uBAAqB,OAAO,QAAS;AACrD,iBAAO,QAAQ;AAAA,QACnB;AACI,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,mBAAO,SAAS,KAAK,KAAK;AACtB,kBAAI,KAAK;AACL,uBAAO,GAAG;AACV;AAAA,cACJ;AACA,sBAAQ,GAAG;AAAA,YACf,CAAC;AAAA,UACL,CAAC;AAAA,MACT;AASA,eAAS,kBAAkB,OAAO,SAAS;AACvC,YAAI,QAAQ,GACR,QAAQ;AACZ,iBAAS,IAAE,GAAG,IAAE,MAAM,QAAQ,IAAE,GAAG,EAAE,GAAG;AACpC,cAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,WAAW,CAAC;AAC5C,cAAE;AAAA;AAEF,cAAE;AAAA,QACV;AAEA,YAAI,QAAQ;AACR,iBAAO;AACX,eAAO,UAAU;AAAA,MACrB;AAUA,aAAO,cAAc,SAAS,GAAG,MAAM;AACnC,YAAI,OAAO,MAAM,YAAY,OAAO,SAAS;AACzC,gBAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAO,IAAK;AACnE,YAAI,KAAK,WAAW;AAChB,iBAAO;AACX,eAAO,kBAAkB,OAAO,SAAS,GAAG,KAAK,OAAO,GAAG,KAAK,SAAO,EAAE,CAAC,GAAG,IAAI;AAAA,MACrF;AAaA,aAAO,UAAU,SAAS,GAAG,MAAM,UAAU,kBAAkB;AAE3D,iBAAS,OAAOA,WAAU;AACtB,cAAI,OAAO,MAAM,YAAY,OAAO,SAAS,UAAU;AACnD,qBAASA,UAAS,KAAK,MAAM,MAAM,wBAAuB,OAAO,IAAG,OAAM,OAAO,IAAK,CAAC,CAAC;AACxF;AAAA,UACJ;AACA,cAAI,KAAK,WAAW,IAAI;AACpB,qBAASA,UAAS,KAAK,MAAM,MAAM,KAAK,CAAC;AACzC;AAAA,UACJ;AACA,iBAAO,KAAK,GAAG,KAAK,OAAO,GAAG,EAAE,GAAG,SAAS,KAAK,MAAM;AACnD,gBAAI;AACA,cAAAA,UAAS,GAAG;AAAA;AAEZ,cAAAA,UAAS,MAAM,kBAAkB,MAAM,IAAI,CAAC;AAAA,UACpD,GAAG,gBAAgB;AAAA,QACvB;AAEA,YAAI,UAAU;AACV,cAAI,OAAO,aAAa;AACpB,kBAAM,MAAM,uBAAqB,OAAO,QAAS;AACrD,iBAAO,QAAQ;AAAA,QACnB;AACI,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AACzC,mBAAO,SAAS,KAAK,KAAK;AACtB,kBAAI,KAAK;AACL,uBAAO,GAAG;AACV;AAAA,cACJ;AACA,sBAAQ,GAAG;AAAA,YACf,CAAC;AAAA,UACL,CAAC;AAAA,MACT;AASA,aAAO,YAAY,SAAS,MAAM;AAC9B,YAAI,OAAO,SAAS;AAChB,gBAAM,MAAM,wBAAuB,OAAO,IAAK;AACnD,eAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAAA,MAC1C;AASA,aAAO,UAAU,SAAS,MAAM;AAC5B,YAAI,OAAO,SAAS;AAChB,gBAAM,MAAM,wBAAuB,OAAO,IAAK;AACnD,YAAI,KAAK,WAAW;AAChB,gBAAM,MAAM,0BAAwB,KAAK,SAAO,QAAQ;AAC5D,eAAO,KAAK,UAAU,GAAG,EAAE;AAAA,MAC/B;AAQA,UAAI,WAAW,OAAO,YAAY,eAAe,WAAW,OAAO,QAAQ,aAAa,aACjF,OAAO,iBAAiB,aAAa,eAAe,QAAQ,WAC7D;AAQN,eAAS,cAAc,KAAK;AACxB,YAAI,MAAM,CAAC,GACP,IAAI;AACR,aAAK,kBAAkB,WAAW;AAC9B,cAAI,KAAK,IAAI,OAAQ,QAAO;AAC5B,iBAAO,IAAI,WAAW,GAAG;AAAA,QAC7B,GAAG,SAAS,GAAG;AACX,cAAI,KAAK,CAAC;AAAA,QACd,CAAC;AACD,eAAO;AAAA,MACX;AAUA,UAAI,cAAc,mEAAmE,MAAM,EAAE;AAO7F,UAAI,eAAe;AAAA,QAAC;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAG;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAC/D;AAAA,QAAI;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAChE;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,MAAE;AAM9C,UAAI,qBAAqB,OAAO;AAShC,eAAS,cAAc,GAAG,KAAK;AAC3B,YAAI,MAAM,GACN,KAAK,CAAC,GACN,IAAI;AACR,YAAI,OAAO,KAAK,MAAM,EAAE;AACpB,gBAAM,MAAM,kBAAgB,GAAG;AACnC,eAAO,MAAM,KAAK;AACd,eAAK,EAAE,KAAK,IAAI;AAChB,aAAG,KAAK,YAAa,MAAM,IAAK,EAAI,CAAC;AACrC,gBAAM,KAAK,MAAS;AACpB,cAAI,OAAO,KAAK;AACZ,eAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B;AAAA,UACJ;AACA,eAAK,EAAE,KAAK,IAAI;AAChB,gBAAO,MAAM,IAAK;AAClB,aAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B,gBAAM,KAAK,OAAS;AACpB,cAAI,OAAO,KAAK;AACZ,eAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B;AAAA,UACJ;AACA,eAAK,EAAE,KAAK,IAAI;AAChB,gBAAO,MAAM,IAAK;AAClB,aAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAC9B,aAAG,KAAK,YAAY,KAAK,EAAI,CAAC;AAAA,QAClC;AACA,eAAO,GAAG,KAAK,EAAE;AAAA,MACrB;AASA,eAAS,cAAc,GAAG,KAAK;AAC3B,YAAI,MAAM,GACN,OAAO,EAAE,QACT,OAAO,GACP,KAAK,CAAC,GACN,IAAI,IAAI,IAAI,IAAI,GAAG;AACvB,YAAI,OAAO;AACP,gBAAM,MAAM,kBAAgB,GAAG;AACnC,eAAO,MAAM,OAAO,KAAK,OAAO,KAAK;AACjC,iBAAO,EAAE,WAAW,KAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,iBAAO,EAAE,WAAW,KAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,cAAI,MAAM,MAAM,MAAM;AAClB;AACJ,cAAK,MAAM,MAAO;AAClB,gBAAM,KAAK,OAAS;AACpB,aAAG,KAAK,mBAAmB,CAAC,CAAC;AAC7B,cAAI,EAAE,QAAQ,OAAO,OAAO;AACxB;AACJ,iBAAO,EAAE,WAAW,KAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,cAAI,MAAM;AACN;AACJ,eAAM,KAAK,OAAS,MAAO;AAC3B,gBAAM,KAAK,OAAS;AACpB,aAAG,KAAK,mBAAmB,CAAC,CAAC;AAC7B,cAAI,EAAE,QAAQ,OAAO,OAAO;AACxB;AACJ,iBAAO,EAAE,WAAW,KAAK;AACzB,eAAK,OAAO,aAAa,SAAS,aAAa,IAAI,IAAI;AACvD,eAAM,KAAK,MAAS,MAAO;AAC3B,eAAK;AACL,aAAG,KAAK,mBAAmB,CAAC,CAAC;AAC7B,YAAE;AAAA,QACN;AACA,YAAI,MAAM,CAAC;AACX,aAAK,MAAM,GAAG,MAAI,MAAM;AACpB,cAAI,KAAK,GAAG,GAAG,EAAE,WAAW,CAAC,CAAC;AAClC,eAAO;AAAA,MACX;AAOA,UAAI,OAAO,WAAW;AAClB;AAOA,YAAIE,QAAO,CAAC;AAOZ,QAAAA,MAAK,gBAAgB;AAQrB,QAAAA,MAAK,aAAa,SAAS,KAAK,KAAK;AACjC,cAAI,KAAK;AACT,cAAI,OAAO,QAAQ;AACf,iBAAK,KACL,MAAM,WAAW;AAAE,qBAAO;AAAA,YAAM;AACpC,iBAAO,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM;AACzC,gBAAI,KAAK;AACL,kBAAI,KAAG,GAAI;AAAA,qBACN,KAAK;AACV,kBAAM,MAAI,IAAG,KAAM,GAAI,GACvB,IAAK,KAAG,KAAM,GAAI;AAAA,qBACb,KAAK;AACV,kBAAM,MAAI,KAAI,KAAM,GAAI,GACxB,IAAM,MAAI,IAAG,KAAM,GAAI,GACvB,IAAK,KAAG,KAAM,GAAI;AAAA;AAElB,kBAAM,MAAI,KAAI,IAAM,GAAI,GACxB,IAAM,MAAI,KAAI,KAAM,GAAI,GACxB,IAAM,MAAI,IAAG,KAAM,GAAI,GACvB,IAAK,KAAG,KAAM,GAAI;AACtB,iBAAK;AAAA,UACT;AAAA,QACJ;AAWA,QAAAA,MAAK,aAAa,SAAS,KAAK,KAAK;AACjC,cAAI,GAAG,GAAG,GAAG,GAAG,OAAO,SAASC,IAAG;AAC/B,YAAAA,KAAIA,GAAE,MAAM,GAAGA,GAAE,QAAQ,IAAI,CAAC;AAC9B,gBAAI,MAAM,MAAMA,GAAE,SAAS,CAAC;AAC5B,gBAAI,OAAO;AACX,gBAAI,OAAO,IAAIA;AACf,kBAAM;AAAA,UACV;AACA,kBAAQ,IAAI,IAAI,OAAO,MAAM;AACzB,iBAAK,IAAE,SAAU;AACb,kBAAI,CAAC;AAAA,sBACC,IAAE,SAAU;AAClB,eAAE,IAAI,IAAI,OAAO,QAAS,KAAK,CAAC,GAAG,CAAC,CAAC,GACrC,KAAM,IAAE,OAAO,IAAM,IAAE,EAAK;AAAA,sBACtB,IAAE,SAAU;AAClB,gBAAE,IAAE,IAAI,OAAO,SAAS,IAAE,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAC5D,KAAM,IAAE,OAAO,MAAQ,IAAE,OAAO,IAAM,IAAE,EAAK;AAAA,sBACvC,IAAE,SAAU;AAClB,gBAAE,IAAE,IAAI,OAAO,SAAS,IAAE,IAAI,OAAO,SAAS,IAAE,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GACrF,KAAM,IAAE,MAAO,MAAQ,IAAE,OAAO,MAAQ,IAAE,OAAO,IAAM,IAAE,EAAK;AAAA,gBAC7D,OAAM,WAAW,4BAA0B,CAAC;AAAA,UACrD;AAAA,QACJ;AASA,QAAAD,MAAK,cAAc,SAAS,KAAK,KAAK;AAClC,cAAI,IAAI,KAAK;AACb,iBAAO,MAAM;AACT,iBAAK,KAAK,OAAO,OAAO,KAAK,IAAI,OAAO;AACpC;AACJ,gBAAI,MAAM,SAAU,MAAM,OAAQ;AAC9B,mBAAK,KAAK,IAAI,OAAO,MAAM;AACvB,oBAAI,MAAM,SAAU,MAAM,OAAQ;AAC9B,uBAAK,KAAG,SAAQ,OAAM,KAAG,QAAO,KAAO;AACvC,uBAAK;AAAM;AAAA,gBACf;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,EAAE;AAAA,UACV;AACA,cAAI,OAAO,KAAM,KAAI,EAAE;AAAA,QAC3B;AASA,QAAAA,MAAK,cAAc,SAAS,KAAK,KAAK;AAClC,cAAI,KAAK;AACT,cAAI,OAAO,QAAQ;AACf,iBAAK,KAAK,MAAM,WAAW;AAAE,qBAAO;AAAA,YAAM;AAC9C,iBAAO,OAAO,SAAS,KAAK,IAAI,OAAO,MAAM;AACzC,gBAAI,MAAM;AACN,kBAAI,EAAE;AAAA;AAEN,oBAAM,OACN,KAAK,MAAI,MAAI,KAAM,GACnB,IAAK,KAAG,OAAO,KAAM;AACzB,iBAAK;AAAA,UACT;AAAA,QACJ;AAQA,QAAAA,MAAK,oBAAoB,SAAS,KAAK,KAAK;AACxC,UAAAA,MAAK,YAAY,KAAK,SAAS,IAAI;AAC/B,YAAAA,MAAK,WAAW,IAAI,GAAG;AAAA,UAC3B,CAAC;AAAA,QACL;AAUA,QAAAA,MAAK,oBAAoB,SAAS,KAAK,KAAK;AACxC,UAAAA,MAAK,WAAW,KAAK,SAAS,IAAI;AAC9B,YAAAA,MAAK,YAAY,IAAI,GAAG;AAAA,UAC5B,CAAC;AAAA,QACL;AAOA,QAAAA,MAAK,qBAAqB,SAAS,IAAI;AACnC,iBAAQ,KAAK,MAAQ,IAAK,KAAK,OAAS,IAAK,KAAK,QAAW,IAAI;AAAA,QACrE;AAQA,QAAAA,MAAK,gBAAgB,SAAS,KAAK;AAC/B,cAAI,IAAI,IAAE;AACV,kBAAQ,KAAK,IAAI,OAAO;AACpB,iBAAKA,MAAK,mBAAmB,EAAE;AACnC,iBAAO;AAAA,QACX;AAQA,QAAAA,MAAK,uBAAuB,SAAS,KAAK;AACtC,cAAI,IAAE,GAAG,IAAE;AACX,UAAAA,MAAK,YAAY,KAAK,SAAS,IAAI;AAC/B,cAAE;AAAG,iBAAKA,MAAK,mBAAmB,EAAE;AAAA,UACxC,CAAC;AACD,iBAAO,CAAC,GAAE,CAAC;AAAA,QACf;AAEA,eAAOA;AAAA,MACX,EAAE;AAEF,WAAK,MAAM,KAAK,OAAO,WAAW;AAAE,eAAO,CAAC,oBAAI;AAAA,MAAM;AAOtD,UAAI,kBAAkB;AAOtB,UAAI,8BAA8B;AAOlC,UAAI,sBAAsB;AAO1B,UAAI,qBAAqB;AAOzB,UAAI,SAAS;AAAA,QACT;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,MAC5B;AAOA,UAAI,SAAS;AAAA,QACT;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,MACxC;AAOA,UAAI,SAAS;AAAA,QACT;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAChD;AAAA,MACJ;AAUA,eAAS,UAAU,IAAI,KAAK,GAAG,GAAG;AAC9B,YAAI,GACA,IAAI,GAAG,GAAG,GACV,IAAI,GAAG,MAAM,CAAC;AAElB,aAAK,EAAE,CAAC;AAoBR,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AAEZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,CAAC;AACZ,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AACb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AACb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AACb,YAAK,EAAE,MAAM,EAAE;AACf,aAAK,EAAE,MAAU,KAAK,KAAM,GAAK;AACjC,aAAK,EAAE,MAAU,KAAK,IAAK,GAAK;AAChC,aAAK,EAAE,MAAS,IAAI,GAAK;AACzB,aAAK,IAAI,EAAE,EAAE;AAEb,WAAG,GAAG,IAAI,IAAI,EAAE,sBAAsB,CAAC;AACvC,WAAG,MAAM,CAAC,IAAI;AACd,eAAO;AAAA,MACX;AAQA,eAAS,cAAc,MAAM,MAAM;AAC/B,iBAAS,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE;AAC/B,iBAAQ,QAAQ,IAAM,KAAK,IAAI,IAAI,KACnC,QAAQ,OAAO,KAAK,KAAK;AAC7B,eAAO,EAAE,KAAK,MAAM,KAAW;AAAA,MACnC;AAQA,eAAS,KAAK,KAAK,GAAG,GAAG;AACrB,YAAI,SAAS,GACT,KAAK,CAAC,GAAG,CAAC,GACV,OAAO,EAAE,QACT,OAAO,EAAE,QACT;AACJ,iBAAS,IAAI,GAAG,IAAI,MAAM;AACtB,eAAK,cAAc,KAAK,MAAM,GAC9B,SAAS,GAAG,MACZ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACrB,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACnB,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,MACvB;AAUA,eAAS,QAAQ,MAAM,KAAK,GAAG,GAAG;AAC9B,YAAI,OAAO,GACP,KAAK,CAAC,GAAG,CAAC,GACV,OAAO,EAAE,QACT,OAAO,EAAE,QACT;AACJ,iBAAS,IAAI,GAAG,IAAI,MAAM;AACtB,eAAK,cAAc,KAAK,IAAI,GAC5B,OAAO,GAAG,MACV,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACrB,eAAO;AACP,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AACnB,aAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACvB,eAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,cAAc,MAAM,IAAI,GAC7B,OAAO,GAAG,MACV,GAAG,CAAC,KAAK,GAAG,KACZ,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,GAC1B,EAAE,CAAC,IAAI,GAAG,CAAC,GACX,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,MACvB;AAaA,eAAS,OAAO,GAAG,MAAM,QAAQ,UAAU,kBAAkB;AACzD,YAAI,QAAQ,OAAO,MAAM,GACrB,OAAO,MAAM,QACb;AAGJ,YAAI,SAAS,KAAK,SAAS,IAAI;AAC3B,gBAAM,MAAM,sCAAoC,MAAM;AACtD,cAAI,UAAU;AACV,qBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,YAAI,KAAK,WAAW,iBAAiB;AACjC,gBAAK,MAAM,0BAAwB,KAAK,SAAO,SAAO,eAAe;AACrE,cAAI,UAAU;AACV,qBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,iBAAU,KAAK,WAAY;AAE3B,YAAI,GAAG,GAAG,IAAI,GAAG;AAGjB,YAAI,YAAY;AACZ,cAAI,IAAI,WAAW,MAAM;AACzB,cAAI,IAAI,WAAW,MAAM;AAAA,QAC7B,OAAO;AACH,cAAI,OAAO,MAAM;AACjB,cAAI,OAAO,MAAM;AAAA,QACrB;AAEA,gBAAQ,MAAM,GAAG,GAAG,CAAC;AAOrB,iBAAS,OAAO;AACZ,cAAI;AACA,6BAAiB,IAAI,MAAM;AAC/B,cAAI,IAAI,QAAQ;AACZ,gBAAI,QAAQ,KAAK,IAAI;AACrB,mBAAO,IAAI,UAAS;AAChB,kBAAI,IAAI;AACR,mBAAK,GAAG,GAAG,CAAC;AACZ,mBAAK,MAAM,GAAG,CAAC;AACf,kBAAI,KAAK,IAAI,IAAI,QAAQ;AACrB;AAAA,YACR;AAAA,UACJ,OAAO;AACH,iBAAK,IAAI,GAAG,IAAI,IAAI;AAChB,mBAAK,IAAI,GAAG,IAAK,QAAQ,GAAI;AACzB,0BAAU,OAAO,KAAK,GAAG,GAAG,CAAC;AACrC,gBAAI,MAAM,CAAC;AACX,iBAAK,IAAI,GAAG,IAAI,MAAM;AAClB,kBAAI,MAAO,MAAM,CAAC,KAAK,KAAM,SAAU,CAAC,GACxC,IAAI,MAAO,MAAM,CAAC,KAAK,KAAM,SAAU,CAAC,GACxC,IAAI,MAAO,MAAM,CAAC,KAAK,IAAK,SAAU,CAAC,GACvC,IAAI,MAAM,MAAM,CAAC,IAAI,SAAU,CAAC;AACpC,gBAAI,UAAU;AACV,uBAAS,MAAM,GAAG;AAClB;AAAA,YACJ;AACI,qBAAO;AAAA,UACf;AACA,cAAI;AACA,qBAAS,IAAI;AAAA,QACrB;AAGA,YAAI,OAAO,aAAa,aAAa;AACjC,eAAK;AAAA,QAGT,OAAO;AACH,cAAI;AACJ,iBAAO;AACH,gBAAI,QAAO,MAAM,KAAK,OAAO;AACzB,qBAAO,OAAO,CAAC;AAAA,QAC3B;AAAA,MACJ;AAYA,eAAS,MAAM,GAAG,MAAM,UAAU,kBAAkB;AAChD,YAAI;AACJ,YAAI,OAAO,MAAM,YAAY,OAAO,SAAS,UAAU;AACnD,gBAAM,MAAM,qCAAqC;AACjD,cAAI,UAAU;AACV,qBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AAEI,kBAAM;AAAA,QACd;AAGA,YAAI,OAAO;AACX,YAAI,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK;AAClD,gBAAM,MAAM,2BAAyB,KAAK,UAAU,GAAE,CAAC,CAAC;AACxD,cAAI,UAAU;AACV,qBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AAEI,kBAAM;AAAA,QACd;AACA,YAAI,KAAK,OAAO,CAAC,MAAM;AACnB,kBAAQ,OAAO,aAAa,CAAC,GAC7B,SAAS;AAAA,aACR;AACD,kBAAQ,KAAK,OAAO,CAAC;AACrB,cAAK,UAAU,OAAO,UAAU,OAAO,UAAU,OAAQ,KAAK,OAAO,CAAC,MAAM,KAAK;AAC7E,kBAAM,MAAM,4BAA0B,KAAK,UAAU,GAAE,CAAC,CAAC;AACzD,gBAAI,UAAU;AACV,uBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,YACJ;AACI,oBAAM;AAAA,UACd;AACA,mBAAS;AAAA,QACb;AAGA,YAAI,KAAK,OAAO,SAAS,CAAC,IAAI,KAAK;AAC/B,gBAAM,MAAM,qBAAqB;AACjC,cAAI,UAAU;AACV,qBAAS,SAAS,KAAK,MAAM,GAAG,CAAC;AACjC;AAAA,UACJ;AACI,kBAAM;AAAA,QACd;AACA,YAAI,KAAK,SAAS,KAAK,UAAU,QAAQ,SAAS,CAAC,GAAG,EAAE,IAAI,IACxD,KAAK,SAAS,KAAK,UAAU,SAAS,GAAG,SAAS,CAAC,GAAG,EAAE,GACxD,SAAS,KAAK,IACd,YAAY,KAAK,UAAU,SAAS,GAAG,SAAS,EAAE;AACtD,aAAK,SAAS,MAAM,OAAS;AAE7B,YAAI,YAAY,cAAc,CAAC,GAC3B,QAAQ,cAAc,WAAW,eAAe;AAQpD,iBAAS,OAAO,OAAO;AACnB,cAAI,MAAM,CAAC;AACX,cAAI,KAAK,IAAI;AACb,cAAI,SAAS;AACT,gBAAI,KAAK,KAAK;AAClB,cAAI,KAAK,GAAG;AACZ,cAAI,SAAS;AACT,gBAAI,KAAK,GAAG;AAChB,cAAI,KAAK,OAAO,SAAS,CAAC;AAC1B,cAAI,KAAK,GAAG;AACZ,cAAI,KAAK,cAAc,OAAO,MAAM,MAAM,CAAC;AAC3C,cAAI,KAAK,cAAc,OAAO,OAAO,SAAS,IAAI,CAAC,CAAC;AACpD,iBAAO,IAAI,KAAK,EAAE;AAAA,QACtB;AAGA,YAAI,OAAO,YAAY;AACnB,iBAAO,OAAO,OAAO,WAAW,OAAO,MAAM,CAAC;AAAA,aAG7C;AACD,iBAAO,WAAW,OAAO,QAAQ,SAASE,MAAK,OAAO;AAClD,gBAAIA;AACA,uBAASA,MAAK,IAAI;AAAA;AAElB,uBAAS,MAAM,OAAO,KAAK,CAAC;AAAA,UACpC,GAAG,gBAAgB;AAAA,QACvB;AAAA,MACJ;AAUA,aAAO,eAAe;AAUtB,aAAO,eAAe;AAEtB,aAAO;AAAA,IACX,CAAC;AAAA;AAAA;", "names": ["random", "callback", "salt", "utfx", "b", "err"]}
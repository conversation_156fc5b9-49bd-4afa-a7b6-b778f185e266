// vite.config.ts
import { reactRouter } from "file:///C:/Users/<USER>/OneDrive/Desktop/Linkworks/linkworks-saasrock-enterprise/node_modules/@react-router/dev/dist/vite.js";
import { defineConfig } from "file:///C:/Users/<USER>/OneDrive/Desktop/Linkworks/linkworks-saasrock-enterprise/node_modules/vite/dist/node/index.js";
import tsconfigPaths from "file:///C:/Users/<USER>/OneDrive/Desktop/Linkworks/linkworks-saasrock-enterprise/node_modules/vite-tsconfig-paths/dist/index.mjs";
import viteCompression from "file:///C:/Users/<USER>/OneDrive/Desktop/Linkworks/linkworks-saasrock-enterprise/node_modules/vite-plugin-compression/dist/index.mjs";
var vite_config_default = defineConfig({
  server: { port: 3e3 },
  plugins: [
    reactRouter(),
    tsconfigPaths(),
    viteCompression({ algorithm: "gzip" }),
    viteCompression({ algorithm: "brotliCompress" })
  ],
  ssr: {
    // noExternal: ["remix-i18next"],
  },
  resolve: {
    alias: {
      ".prisma/client/index-browser": "./node_modules/.prisma/client/index-browser.js"
    }
  },
  optimizeDeps: {
    esbuildOptions: {
      target: "esnext"
    }
  },
  build: {
    target: "esnext"
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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

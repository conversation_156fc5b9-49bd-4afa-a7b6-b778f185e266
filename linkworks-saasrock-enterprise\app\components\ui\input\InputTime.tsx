import clsx from "clsx";
import { forwardRef, Fragment, ReactNode, Ref, RefObject, useEffect, useImperativeHandle, useRef, useState, useMemo, useCallback } from "react";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import HintTooltip from "~/components/ui/tooltips/HintTooltip";
import { Clock } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";
import { Button } from "../button";
import { useTranslation } from "react-i18next";
import { Input } from "../input";
import { cn } from "~/lib/utils";
import { ScrollArea } from "../scroll-area";

export interface RefInputTime {
  input: RefObject<HTMLInputElement | null>;
}

// Pre-computed constants to avoid repeated array creation
const HOURS_12 = Array.from({ length: 12 }, (_, i) => i + 1);
const HOURS_24 = Array.from({ length: 24 }, (_, i) => i);
const MINUTES_60 = Array.from({ length: 60 }, (_, i) => i);
const AMPM_OPTIONS = ["AM", "PM"] as const;

interface Props {
  format?: "12h" | "24h";
  name?: string;
  title?: string;
  defaultValue?: Date | string | undefined;
  value?: Date | string;
  onChange?: (date: Date) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: ReactNode;
  icon?: string;
  darkMode?: boolean;
  autoFocus?: boolean;
  classNameInput?: string;
  defaultTime?: string; // Format: "HH:mm" or "hh:mm AM/PM"
  minTime?: string; // Format: "HH:mm"
  maxTime?: string; // Format: "HH:mm"
}

const InputTime = (
  {
    format = "24h",
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    darkMode,
    autoFocus,
    classNameInput,
    defaultTime,
    minTime,
    maxTime,
  }: Props,
  ref: Ref<RefInputTime>
) => {
  const { t } = useTranslation();
  useImperativeHandle(ref, () => ({ input }));
  const input = useRef<HTMLInputElement>(null);

  // Helper functions for time validation
  const parseTimeString = (timeStr: string): { hours: number; minutes: number } | null => {
    if (!timeStr) return null;

    console.log("Parsing time string:", timeStr);

    // Handle both 12h and 24h formats
    const timeRegex12h = /^(\d{1,2}):(\d{2})\s*(AM|PM)$/i;
    const timeRegex24h = /^(\d{1,2}):(\d{2})$/;

    let hours: number, minutes: number;

    // Try 12-hour format first
    const match12h = timeStr.match(timeRegex12h);
    if (match12h) {
      hours = parseInt(match12h[1]);
      minutes = parseInt(match12h[2]);
      const period = match12h[3].toUpperCase();

      // Convert to 24-hour format
      if (period === 'AM') {
        if (hours === 12) hours = 0;
      } else { // PM
        if (hours !== 12) hours += 12;
      }
    } else {
      // Try 24-hour format
      const match24h = timeStr.match(timeRegex24h);
      if (match24h) {
        console.log("Matched 24h format:", match24h);
        hours = parseInt(match24h[1]);
        minutes = parseInt(match24h[2]);
      } else {
        console.log("No format matched for:", timeStr);
        return null; // Invalid format
      }
    }

    // Validate the parsed time
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      console.log("Invalid time values:", { hours, minutes });
      return null;
    }

    console.log("Parsed time:", { hours, minutes });
    return { hours, minutes };
  };

  const isTimeAllowed = (hours: number, minutes: number): boolean => {
    // Debug logging - remove after testing
    if (minTime || maxTime) {
      console.log(`Checking time ${hours}:${minutes.toString().padStart(2, '0')} against minTime: ${minTime}, maxTime: ${maxTime}`);
    }

    const timeInMinutes = hours * 60 + minutes;

    if (minTime) {
      const minTimeObj = parseTimeString(minTime);
      if (minTimeObj) {
        const minTimeInMinutes = minTimeObj.hours * 60 + minTimeObj.minutes;
        console.log(`Min constraint: ${timeInMinutes} >= ${minTimeInMinutes} = ${timeInMinutes >= minTimeInMinutes}`);
        if (timeInMinutes < minTimeInMinutes) return false;
      }
    }

    if (maxTime) {
      const maxTimeObj = parseTimeString(maxTime);
      if (maxTimeObj) {
        const maxTimeInMinutes = maxTimeObj.hours * 60 + maxTimeObj.minutes;
        console.log(`Max constraint: ${timeInMinutes} <= ${maxTimeInMinutes} = ${timeInMinutes <= maxTimeInMinutes}`);
        // Use <= to make maxTime inclusive (you can select the exact max time)
        if (timeInMinutes > maxTimeInMinutes) return false;
      }
    }

    return true;
  };

  const parseDate = (date: Date | string | undefined): Date | undefined => {
    if (!date) return undefined;
    if (date instanceof Date) return date;
    try {
      // Try to parse string date
      const parsed = new Date(date);
      return isNaN(parsed.getTime()) ? undefined : parsed;
    } catch {
      return undefined;
    }
  };

  const [actualValue, setActualValue] = useState<Date | undefined>(parseDate(value) || parseDate(defaultValue));
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const parsedValue = parseDate(value);
    if (parsedValue?.getTime() !== actualValue?.getTime()) {
      setActualValue(parsedValue);
    }
  }, [value]);

  useEffect(() => {
    const parseTimeString = (timeStr: string): Date | null => {
      if (!timeStr) return null;
      try {
        const today = new Date();
        today.setSeconds(0);
        today.setMilliseconds(0);
        const [time, ampm] = timeStr.split(" ");
        const [h, m] = time.split(":");
        let hour = parseInt(h);
        const minute = parseInt(m);
        if (ampm === "PM" && hour !== 12) hour += 12;
        if (ampm === "AM" && hour === 12) hour = 0;
        today.setHours(hour, minute);
        return today;
      } catch {
        return null;
      }
    };
    if (defaultTime && !actualValue) {
      const timeDate = parseTimeString(defaultTime);
      if (timeDate) setActualValue(timeDate);
    }
  }, [actualValue, defaultTime]);

  const formatTimeDisplay = (date: Date | undefined): string => {
    if (!date || isNaN(date.getTime())) return "";
    return format === "12h"
      ? date.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        })
      : `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  };

  const getTimeInputValue = (): string => {
    if (!actualValue || isNaN(actualValue.getTime())) return "";
    return `${actualValue.getHours().toString().padStart(2, "0")}:${actualValue.getMinutes().toString().padStart(2, "0")}`;
  };

  const updateTime = (type: "hour" | "minute" | "ampm", val: string) => {
    const defaultDate = new Date();
    defaultDate.setHours(0, 0, 0, 0);

    const newDate = new Date(actualValue || defaultDate);
    if (isNaN(newDate.getTime())) {
      newDate.setHours(0, 0, 0, 0); // Reset to midnight if invalid
    }

    let hour = newDate.getHours();
    let minute = newDate.getMinutes();

    if (type === "hour") {
      const selectedHour = parseInt(val);
      const isPM = hour >= 12;
      if (format === "12h") {
        newDate.setHours((selectedHour % 12) + (isPM ? 12 : 0));
      } else {
        newDate.setHours(selectedHour % 24);
      }
    } else if (type === "minute") {
      newDate.setMinutes(parseInt(val));
    } else if (type === "ampm") {
      const isCurrentlyPM = hour >= 12;
      if (val === "AM" && isCurrentlyPM) newDate.setHours(hour - 12);
      if (val === "PM" && !isCurrentlyPM) newDate.setHours(hour + 12);
    }

    // Validate the new time against constraints
    let finalHour = newDate.getHours();
    let finalMinute = newDate.getMinutes();

    console.log(`updateTime: Trying to set ${type} to ${val}, final time: ${finalHour}:${finalMinute.toString().padStart(2, '0')}`);

    // If the time is not allowed, try to adjust it to the closest valid time
    if (!isTimeAllowed(finalHour, finalMinute)) {
      console.log("updateTime: Time not allowed, trying to adjust...");

      if (type === "hour") {
        // When selecting an hour, adjust minutes to make it valid
        // Try minute 0 first (top of the hour)
        if (isTimeAllowed(finalHour, 0)) {
          newDate.setMinutes(0);
          finalMinute = 0;
          console.log(`updateTime: Adjusted to ${finalHour}:00`);
        } else {
          // If even minute 0 doesn't work, find the latest valid minute for this hour
          for (let m = 59; m >= 0; m--) {
            if (isTimeAllowed(finalHour, m)) {
              newDate.setMinutes(m);
              finalMinute = m;
              console.log(`updateTime: Adjusted to ${finalHour}:${m.toString().padStart(2, '0')}`);
              break;
            }
          }
        }
      }
    }

    const finalAllowed = isTimeAllowed(finalHour, finalMinute);
    console.log(`updateTime: Final time ${finalHour}:${finalMinute.toString().padStart(2, '0')} allowed: ${finalAllowed}`);

    if (finalAllowed) {
      console.log("updateTime: Setting new time");
      setActualValue(newDate);
      onChange?.(newDate);
    } else {
      console.log("updateTime: Time still not allowed after adjustment, not updating");
    }
  };

  const selectedHour = format === "12h" ? (actualValue?.getHours() ?? 0) % 12 || 12 : actualValue?.getHours() ?? 0;
  const selectedMinute = actualValue?.getMinutes() ?? 0;

  // Smart default AM/PM selection based on time constraints
  const getDefaultAMPM = (): "AM" | "PM" => {
    if (actualValue) {
      return (actualValue.getHours() ?? 0) >= 12 ? "PM" : "AM";
    }

    // If no value is set, choose AM/PM based on which period has valid times
    const amHasValidTimes = HOURS_12.some(h => {
      const hour24 = h === 12 ? 0 : h;
      return isTimeAllowed(hour24, 0);
    });

    const pmHasValidTimes = HOURS_12.some(h => {
      const hour24 = h === 12 ? 12 : h + 12;
      return isTimeAllowed(hour24, 0);
    });

    console.log("AM/PM validation:", { amHasValidTimes, pmHasValidTimes });

    // If only PM has valid times, default to PM
    if (pmHasValidTimes && !amHasValidTimes) {
      console.log("Defaulting to PM");
      return "PM";
    }

    // Otherwise default to AM
    console.log("Defaulting to AM");
    return "AM";
  };

  const selectedAMPM = getDefaultAMPM();

  const hoursRange = useMemo(() => format === "12h" ? HOURS_12 : HOURS_24, [format]);

  // Filter hours and minutes based on time constraints
  const getFilteredHours = useMemo(() => {
    return hoursRange.filter(h => {
      let hour24: number;
      if (format === "12h") {
        // Convert 12h format to 24h format
        if (selectedAMPM === "AM") {
          hour24 = h === 12 ? 0 : h;
        } else { // PM
          hour24 = h === 12 ? 12 : h + 12;
        }
      } else {
        hour24 = h;
      }

      // Check if ANY minute in this hour is valid, not just the selected minute
      const hasValidMinute = MINUTES_60.some(minute => {
        return isTimeAllowed(hour24, minute);
      });

      return hasValidMinute;
    });
  }, [hoursRange, format, selectedAMPM, minTime, maxTime]);

  const getFilteredMinutes = useMemo(() => {
    // If no value is set yet and we have time constraints, show minutes that are valid for ANY available hour
    if (!actualValue && (minTime || maxTime)) {
      const allValidMinutes = new Set<number>();

      // Check all possible hours in the current AM/PM period
      const hoursToCheck = format === "12h" ? HOURS_12 : HOURS_24;

      hoursToCheck.forEach(h => {
        let hour24: number;
        if (format === "12h") {
          if (selectedAMPM === "AM") {
            hour24 = h === 12 ? 0 : h;
          } else { // PM
            hour24 = h === 12 ? 12 : h + 12;
          }
        } else {
          hour24 = h;
        }

        // Check if this hour has any valid minutes, and if so, add them
        const hasValidMinute = MINUTES_60.some(minute => {
          return isTimeAllowed(hour24, minute);
        });

        if (hasValidMinute) {
          MINUTES_60.forEach(minute => {
            if (isTimeAllowed(hour24, minute)) {
              allValidMinutes.add(minute);
            }
          });
        }
      });

      return Array.from(allValidMinutes).sort((a, b) => a - b);
    }

    // Use the selected hour as before when a value is set
    let currentHour: number;
    if (format === "12h") {
      // Convert 12h format to 24h format
      if (selectedAMPM === "AM") {
        currentHour = selectedHour === 12 ? 0 : selectedHour;
      } else { // PM
        currentHour = selectedHour === 12 ? 12 : selectedHour + 12;
      }
    } else {
      currentHour = selectedHour;
    }

    return MINUTES_60.filter(m => {
      return isTimeAllowed(currentHour, m);
    });
  }, [actualValue, minTime, maxTime, format, selectedAMPM, selectedHour]);

  // Filter AM/PM options based on time constraints (for 12h format)
  const getFilteredAMPM = useMemo(() => {
    if (format !== "12h") return AMPM_OPTIONS;

    return AMPM_OPTIONS.filter(period => {
      // Check if any hour in this period is allowed with any minute
      return HOURS_12.some(h => {
        const hour24 = period === "AM" ? (h === 12 ? 0 : h) : (h === 12 ? 12 : h + 12);
        // Check if this hour has any valid minutes
        return MINUTES_60.some(minute => {
          return isTimeAllowed(hour24, minute);
        });
      });
    });
  }, [format, minTime, maxTime]);

  return (
    <div className={clsx(className, !darkMode && "")}>
      <label htmlFor={name} className="mb-1 flex justify-between space-x-2 truncate text-xs font-medium">
        <div className="flex items-center space-x-1">
          <div className="truncate">
            {title && title}
            {required && <span className="text-destructive ml-1">*</span>}
          </div>
          {help && <HintTooltip text={help} />}
        </div>
        {hint}
      </label>
      <div className={`${title && "mt-1"} relative space-y-1`}>
        {name && <input type="hidden" name={name} value={getTimeInputValue()} required={required} readOnly />}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={clsx(
                "hover:bg-accent hover:text-accent-foreground w-full justify-start text-left font-normal",
                !actualValue && "text-muted-foreground",
                (disabled || readOnly) && "cursor-not-allowed opacity-50",
                classNameInput
              )}
              disabled={disabled || readOnly}
              autoFocus={autoFocus}
            >
              <Clock className="text-muted-foreground mr-2 h-4 w-4" />
              {actualValue ? formatTimeDisplay(actualValue) : t("components.time.pick")}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="flex divide-x rounded-md border">
              {/* Hours */}
              <ScrollArea className="h-60 w-24">
                <div className="flex flex-col p-1">
                  {getFilteredHours.map((h) => (
                    <Button
                      key={h}
                      variant={h === selectedHour ? "default" : "ghost"}
                      className={cn("h-8 w-full justify-center rounded-sm text-xs mb-1", h === selectedHour && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground")}
                      onClick={() => updateTime("hour", h.toString())}
                    >
                      {h.toString().padStart(2, "0")}
                    </Button>
                  ))}
                </div>
              </ScrollArea>

              {/* Minutes */}
              <ScrollArea className="h-60 w-24">
                <div className="flex flex-col p-1">
                  {getFilteredMinutes.map((m) => (
                    <Button
                      key={m}
                      variant={m === selectedMinute ? "default" : "ghost"}
                      className={cn("h-8 w-full justify-center rounded-sm text-xs mb-1", m === selectedMinute && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground")}
                      onClick={() => updateTime("minute", m.toString())}
                    >
                      {m.toString().padStart(2, "0")}
                    </Button>
                  ))}
                </div>
              </ScrollArea>

              {/* AM/PM - Only show for 12h format */}
              {format === "12h" && (
                <ScrollArea className="h-60 w-24">
                  <div className="flex flex-col p-1">
                    {getFilteredAMPM.map((ampm) => (
                      <Button
                        key={ampm}
                        variant={ampm === selectedAMPM ? "default" : "ghost"}
                        className={cn("h-8 w-full justify-center rounded-sm text-xs mb-1", ampm === selectedAMPM && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground")}
                        onClick={() => updateTime("ampm", ampm)}
                      >
                        {ampm}
                      </Button>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default forwardRef(InputTime);
import { forwardRef, Ref, useImperativeHandle, useRef, useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { cn } from "~/lib/utils";
import HintTooltip from "../../tooltips/HintTooltip";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import { Crosshair, Loader2, Shield, Map } from "lucide-react";
import { Input } from "../../input";
import { useGoogleMaps } from "~/hooks/useGoogleMaps";
import { LocationData } from "~/types/location";
import LocationHelper from "~/utils/helpers/LocationHelper";

export interface RefSearchOnlyInput {
  input: HTMLInputElement | null;
}

export interface SearchOnlyInputProps {
  name?: string;
  title?: string;
  value?: LocationData;
  defaultValue?: LocationData;
  onChange?: (value: LocationData | undefined) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: string;
  icon?: string;
  autoFocus?: boolean;
  placeholder?: string;
  showCurrentLocationButton?: boolean;
  columns?: number;
  group?: string;
  // Security & Validation
  minSearchLength?: number;
  validatePlaceDetails?: boolean;
  storePlaceId?: boolean;
}

const SearchOnlyInput = (
  {
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    autoFocus = false,
    placeholder,
    showCurrentLocationButton = true,
    columns,
    group,
    // Security & Validation defaults
    minSearchLength = 1,
    validatePlaceDetails = true,
    storePlaceId = true,
  }: SearchOnlyInputProps,
  ref: Ref<RefSearchOnlyInput>
) => {
  const { t } = useTranslation();
  const input = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<any>(null);
  const placesServiceRef = useRef<any>(null);


  useImperativeHandle(ref, () => ({ input: input.current }), []);

  const [actualValue, setActualValue] = useState<LocationData | undefined>(
    value || defaultValue
  );
  const [searchValue, setSearchValue] = useState<string>(
    value?.address || defaultValue?.address || ""
  );
  const [isGettingCurrentLocation, setIsGettingCurrentLocation] = useState(false);
  const [isValidatingPlace, setIsValidatingPlace] = useState(false);

  const [searchError, setSearchError] = useState<string>("");
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isLoaded: isGoogleMapsLoaded, isLoading, error, getCurrentLocation } = useGoogleMaps();

  // Search for places using Google Places API
  const searchPlaces = useCallback(async (query: string) => {
    if (!isGoogleMapsLoaded || !query || query.length < minSearchLength) {
      setSuggestions([]);
      setShowDropdown(false);
      return;
    }

    try {
      const service = new (window as any).google.maps.places.AutocompleteService();
      const request = {
        input: query,
        types: ['establishment', 'geocode'],
        fields: ['place_id', 'description', 'structured_formatting']
      };

      service.getPlacePredictions(request, (predictions: any[], status: any) => {
        if (status === (window as any).google.maps.places.PlacesServiceStatus.OK && predictions) {
          setSuggestions(predictions);
          setShowDropdown(true);
          setSelectedIndex(-1);
        } else {
          setSuggestions([]);
          setShowDropdown(false);
        }
      });
    } catch (error) {
      console.error('Error searching places:', error);
      setSuggestions([]);
      setShowDropdown(false);
    }
  }, [isGoogleMapsLoaded, minSearchLength]);

  // Debounced search
  const debouncedSearch = useCallback((query: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchPlaces(query);
    }, 300);
  }, [searchPlaces]);

  // Sync with external value changes (only when truly different)
  useEffect(() => {
    // Only update if the value is genuinely different (not just a reference change)
    const valueChanged = JSON.stringify(value) !== JSON.stringify(actualValue);
    if (valueChanged) {
      setActualValue(value);
      setSearchValue(value?.address || "");
    }
  }, [value]);



  // Simple validation function (no artificial debouncing needed for Google autocomplete)
  const validateSearchLength = useCallback((searchTerm: string) => {
    if (searchTerm.length > 0 && searchTerm.length < minSearchLength) {
      setSearchError(`Please enter at least ${minSearchLength} characters to search.`);
      return false;
    } else {
      setSearchError("");
      return true;
    }
  }, [minSearchLength]);

  // Handle suggestion selection
  const selectSuggestion = useCallback(async (suggestion: any) => {
    if (!isGoogleMapsLoaded) return;

    try {
      setIsValidatingPlace(true);
      setShowDropdown(false);
      setSearchValue(suggestion.description);

      // Get place details
      const service = new (window as any).google.maps.places.PlacesService(
        document.createElement('div')
      );

      const request = {
        placeId: suggestion.place_id,
        fields: ['geometry', 'address_components', 'formatted_address', 'name', 'types', 'business_status', 'rating', 'website', 'formatted_phone_number']
      };

      service.getDetails(request, (place: any, status: any) => {
        setIsValidatingPlace(false);

        if (status === (window as any).google.maps.places.PlacesServiceStatus.OK && place) {
          let locationData = LocationHelper.extractAddressComponents(place);

          if (!storePlaceId) {
            locationData.placeId = undefined;
          }

          if (validatePlaceDetails) {
            locationData.name = place.name;
            locationData.types = place.types;
            locationData.businessStatus = place.business_status;
            locationData.rating = place.rating;
            locationData.website = place.website;
            locationData.phoneNumber = place.formatted_phone_number;
          }

          setActualValue(locationData);
          setSearchValue(locationData.formattedAddress || locationData.address);
          setSearchError("");

          if (onChange) {
            onChange(locationData);
          }
        } else {
          setSearchError("Failed to get place details");
        }
      });
    } catch (error) {
      setIsValidatingPlace(false);
      setSearchError("Error getting place details");
      console.error('Error getting place details:', error);
    }
  }, [isGoogleMapsLoaded, onChange, validatePlaceDetails, storePlaceId]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showDropdown || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => prev < suggestions.length - 1 ? prev + 1 : prev);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          selectSuggestion(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowDropdown(false);
        setSelectedIndex(-1);
        break;
    }
  }, [showDropdown, suggestions, selectedIndex, selectSuggestion]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          input.current && !input.current.contains(event.target as Node)) {
        setShowDropdown(false);
        setSelectedIndex(-1);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  // Cleanup search timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Enhanced place details fetching
  const fetchPlaceDetails = useCallback(async (placeId: string): Promise<any> => {
    if (!validatePlaceDetails || !placesServiceRef.current) return null;

    return new Promise((resolve, reject) => {
      setIsValidatingPlace(true);
      placesServiceRef.current.getDetails(
        {
          placeId,
          fields: [
            'place_id',
            'formatted_address',
            'geometry',
            'address_components',
            'name',
            'types',
            'business_status',
            'rating',
            'user_ratings_total',
            'website',
            'formatted_phone_number',
            'opening_hours',
            'photos'
          ]
        },
        (place: any, status: any) => {
          setIsValidatingPlace(false);
          if (status === (window as any).google.maps.places.PlacesServiceStatus.OK) {
            resolve(place);
          } else {
            reject(new Error(`Place details fetch failed: ${status}`));
          }
        }
      );
    });
  }, [validatePlaceDetails]);

  // Handle current location with useCallback to prevent re-renders
  const handleGetCurrentLocation = useCallback(async () => {
    if (!isGoogleMapsLoaded || isGettingCurrentLocation) return;

    setIsGettingCurrentLocation(true);
    try {
      const location = await getCurrentLocation();
      setActualValue(location);
      setSearchValue(location.address);
      if (onChange) {
        onChange(location);
      }
    } catch (error) {
      console.error("Error getting current location:", error);
    } finally {
      setIsGettingCurrentLocation(false);
    }
  }, [isGoogleMapsLoaded, isGettingCurrentLocation, getCurrentLocation, onChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchValue(newValue);

    // Clear location data if input is cleared
    if (!newValue) {
      setActualValue(undefined);
      setSearchError("");
      setSuggestions([]);
      setShowDropdown(false);
      if (onChange) {
        onChange(undefined);
      }
    } else {
      // Validate search length and trigger search
      if (validateSearchLength(newValue)) {
        debouncedSearch(newValue);
      } else {
        setSuggestions([]);
        setShowDropdown(false);
      }
    }
  };

  const handleClearLocation = () => {
    setActualValue(undefined);
    setSearchValue("");
    setSearchError("");
    if (onChange) {
      onChange(undefined);
    }
  };

  // Initialize autocomplete
  useEffect(() => {
    if (!isGoogleMapsLoaded || !input.current) return;

    const autocompleteOptions: any = {
      // No type restrictions - allow all types of places
      fields: [
        "place_id",
        "formatted_address",
        "geometry",
        "address_components",
        "name",
        "types",
        "business_status"
      ],
    };

    // No restrictions - allow global search for all types of places

    const autocomplete = new (window as any).google.maps.places.Autocomplete(input.current, autocompleteOptions);

    // Initialize Places Service for detailed place information
    if (validatePlaceDetails) {
      // Create a dummy map for places service centered on Hyderabad, India
      const dummyMapOptions = {
        center: { lat: 17.3850, lng: 78.4867 }, // Hyderabad, India
        zoom: 10
      };
      const dummyMap = new (window as any).google.maps.Map(document.createElement('div'), dummyMapOptions);
      placesServiceRef.current = new (window as any).google.maps.places.PlacesService(dummyMap);
    }

    autocomplete.addListener("place_changed", async () => {
      const place = autocomplete.getPlace();
      if (place.geometry && place.geometry.location) {
        // Use the new LocationHelper to extract enhanced address components
        let locationData = LocationHelper.extractAddressComponents(place);

        // Override placeId storage based on component setting
        if (!storePlaceId) {
          locationData.placeId = undefined;
        }

        // Fetch detailed place information if enabled
        if (validatePlaceDetails && place.place_id) {
          try {
            const detailedPlace = await fetchPlaceDetails(place.place_id);
            if (detailedPlace) {
              // Add additional metadata from place details
              locationData.name = detailedPlace.name;
              locationData.types = detailedPlace.types;
              locationData.businessStatus = detailedPlace.business_status;
              locationData.rating = detailedPlace.rating;
              locationData.website = detailedPlace.website;
              locationData.phoneNumber = detailedPlace.formatted_phone_number;

              // Re-extract address components from detailed place if available
              if (detailedPlace.address_components) {
                locationData = LocationHelper.extractAddressComponents(detailedPlace);
                if (!storePlaceId) {
                  locationData.placeId = undefined;
                }
              }
            }
          } catch (error) {
            console.warn("Failed to fetch place details:", error);
          }
        }

        setActualValue(locationData);
        setSearchValue(locationData.formattedAddress || locationData.address);
        setSearchError("");
        if (onChange) {
          onChange(locationData);
        }
      }
    });

    autocompleteRef.current = autocomplete;

    return () => {
      if (autocompleteRef.current && (window as any).google) {
        (window as any).google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
    };
  }, [isGoogleMapsLoaded, onChange, validatePlaceDetails, storePlaceId, fetchPlaceDetails]);

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <div className="flex justify-between space-x-2 items-center mb-1">
          <label htmlFor={name} className="mb-1 flex justify-between space-x-2 truncate text-xs font-medium">
            {title}
            {required && <span className="ml-1 text-red-500">*</span>}
          </label>

          <div className="flex items-center space-x-1">
            {hint && <HintTooltip text={hint} />}
          </div>
        </div>
      )}

      {/* Hidden input for form submission - JSON data */}
      {name && (
        <input
          type="hidden"
          name={name}
          value={actualValue ? JSON.stringify(actualValue) : ""}
          required={required}
        />
      )}

      <div className="space-y-3">
        <div className="relative group">
          {/* Maps icon - always at the left start */}
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2 z-10">
            <Map className="text-gray-600 h-4 w-4 flex-shrink-0 group-focus-within:text-gray-800 transition-colors" />
          </div>

          {/* Custom entity icon (if provided) - positioned after location icon */}
          {icon && (
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-8 z-10">
              <EntityIcon className="text-gray-400 group-focus-within:text-primary h-3 w-3 transition-colors" icon={icon} />
            </div>
          )}

          <Input
            ref={input}
            type="text"
            id={name}
            required={required}
            name={`${name}_search`}
            value={searchValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={disabled || readOnly || isLoading || isValidatingPlace}
            readOnly={readOnly}
            autoFocus={autoFocus}
            placeholder={placeholder || t("location.searchPlaceholder", "Search for a location...")}
            className={cn(
              "pl-8 h-11 border-gray-200 focus:border-primary focus:ring-primary/20 bg-white shadow-sm",
              icon && "pl-12", // Extra padding when custom icon is present
              searchError && "border-red-300 focus:border-red-500",
              isValidatingPlace && "border-yellow-300",
              showCurrentLocationButton ? "pr-20" : "pr-12"
            )}
          />

          {/* Right side buttons container */}
          <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-2">
            {/* Validation indicator */}
            {isValidatingPlace && (
              <div className="flex items-center">
                <Shield className="h-4 w-4 text-yellow-500 animate-pulse" />
              </div>
            )}

            {/* Current Location Button */}
            {showCurrentLocationButton && !readOnly && !disabled && (
              <button
                type="button"
                onClick={handleGetCurrentLocation}
                disabled={!isGoogleMapsLoaded || isGettingCurrentLocation}
                className="flex items-center justify-center w-7 h-7 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Use current location"
              >
                {isGettingCurrentLocation ? (
                  <Loader2 className="h-3.5 w-3.5 animate-spin" />
                ) : (
                  <Crosshair className="h-3.5 w-3.5" />
                )}
              </button>
            )}

            {/* Clear button */}
            {actualValue && !readOnly && !disabled && (
              <button
                type="button"
                onClick={handleClearLocation}
                className="flex items-center justify-center w-7 h-7 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-all duration-200"
                title="Clear location"
              >
                <span className="text-base font-medium">×</span>
              </button>
            )}
          </div>

          {/* Suggestions Dropdown */}
          {showDropdown && suggestions.length > 0 && (
            <div
              ref={dropdownRef}
              className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
            >
              {suggestions.map((suggestion, index) => (
                <button
                  key={suggestion.place_id}
                  type="button"
                  className={cn(
                    "w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0 transition-colors cursor-pointer",
                    index === selectedIndex && "bg-blue-50 hover:bg-blue-100"
                  )}
                  onClick={() => selectSuggestion(suggestion)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <div className="flex items-start space-x-3">
                    <Map className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {suggestion.structured_formatting?.main_text || suggestion.description}
                      </div>
                      {suggestion.structured_formatting?.secondary_text && (
                        <div className="text-xs text-gray-500 truncate mt-0.5">
                          {suggestion.structured_formatting.secondary_text}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Search Error Display */}
        {searchError && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
              <span className="text-sm text-red-700">{searchError}</span>
            </div>
          </div>
        )}

        {/* Minimum character hint */}
        {searchValue && searchValue.length > 0 && searchValue.length < minSearchLength && (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">i</span>
              </div>
              <span className="text-sm text-blue-700">
                Type at least {minSearchLength} characters to start searching ({searchValue.length}/{minSearchLength})
              </span>
            </div>
          </div>
        )}
      </div>

      {help && (
        <div className="mt-1">
          <div className="text-xs text-muted-foreground">{help}</div>
        </div>
      )}
    </div>
  );
};

export default forwardRef(SearchOnlyInput);

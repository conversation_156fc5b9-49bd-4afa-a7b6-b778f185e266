import {
  _defineProperty
} from "./chunk-J5JDLICV.js";
import {
  _classCallCheck,
  _createClass
} from "./chunk-FHECEMZG.js";
import {
  _typeof
} from "./chunk-EXOAFPCI.js";
import {
  _assertThisInitialized
} from "./chunk-A3Q7B7W4.js";
import {
  _extends,
  _objectWithoutPropertiesLoose,
  _setPrototypeOf
} from "./chunk-4YBQ5TDS.js";
import {
  require_prop_types
} from "./chunk-C2LS64Q7.js";
import {
  require_react
} from "./chunk-R26XTA6N.js";
import {
  __commonJS,
  __export,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/color-name/index.js
var require_color_name = __commonJS({
  "node_modules/color-name/index.js"(exports, module) {
    "use strict";
    module.exports = {
      "aliceblue": [240, 248, 255],
      "antiquewhite": [250, 235, 215],
      "aqua": [0, 255, 255],
      "aquamarine": [127, 255, 212],
      "azure": [240, 255, 255],
      "beige": [245, 245, 220],
      "bisque": [255, 228, 196],
      "black": [0, 0, 0],
      "blanchedalmond": [255, 235, 205],
      "blue": [0, 0, 255],
      "blueviolet": [138, 43, 226],
      "brown": [165, 42, 42],
      "burlywood": [222, 184, 135],
      "cadetblue": [95, 158, 160],
      "chartreuse": [127, 255, 0],
      "chocolate": [210, 105, 30],
      "coral": [255, 127, 80],
      "cornflowerblue": [100, 149, 237],
      "cornsilk": [255, 248, 220],
      "crimson": [220, 20, 60],
      "cyan": [0, 255, 255],
      "darkblue": [0, 0, 139],
      "darkcyan": [0, 139, 139],
      "darkgoldenrod": [184, 134, 11],
      "darkgray": [169, 169, 169],
      "darkgreen": [0, 100, 0],
      "darkgrey": [169, 169, 169],
      "darkkhaki": [189, 183, 107],
      "darkmagenta": [139, 0, 139],
      "darkolivegreen": [85, 107, 47],
      "darkorange": [255, 140, 0],
      "darkorchid": [153, 50, 204],
      "darkred": [139, 0, 0],
      "darksalmon": [233, 150, 122],
      "darkseagreen": [143, 188, 143],
      "darkslateblue": [72, 61, 139],
      "darkslategray": [47, 79, 79],
      "darkslategrey": [47, 79, 79],
      "darkturquoise": [0, 206, 209],
      "darkviolet": [148, 0, 211],
      "deeppink": [255, 20, 147],
      "deepskyblue": [0, 191, 255],
      "dimgray": [105, 105, 105],
      "dimgrey": [105, 105, 105],
      "dodgerblue": [30, 144, 255],
      "firebrick": [178, 34, 34],
      "floralwhite": [255, 250, 240],
      "forestgreen": [34, 139, 34],
      "fuchsia": [255, 0, 255],
      "gainsboro": [220, 220, 220],
      "ghostwhite": [248, 248, 255],
      "gold": [255, 215, 0],
      "goldenrod": [218, 165, 32],
      "gray": [128, 128, 128],
      "green": [0, 128, 0],
      "greenyellow": [173, 255, 47],
      "grey": [128, 128, 128],
      "honeydew": [240, 255, 240],
      "hotpink": [255, 105, 180],
      "indianred": [205, 92, 92],
      "indigo": [75, 0, 130],
      "ivory": [255, 255, 240],
      "khaki": [240, 230, 140],
      "lavender": [230, 230, 250],
      "lavenderblush": [255, 240, 245],
      "lawngreen": [124, 252, 0],
      "lemonchiffon": [255, 250, 205],
      "lightblue": [173, 216, 230],
      "lightcoral": [240, 128, 128],
      "lightcyan": [224, 255, 255],
      "lightgoldenrodyellow": [250, 250, 210],
      "lightgray": [211, 211, 211],
      "lightgreen": [144, 238, 144],
      "lightgrey": [211, 211, 211],
      "lightpink": [255, 182, 193],
      "lightsalmon": [255, 160, 122],
      "lightseagreen": [32, 178, 170],
      "lightskyblue": [135, 206, 250],
      "lightslategray": [119, 136, 153],
      "lightslategrey": [119, 136, 153],
      "lightsteelblue": [176, 196, 222],
      "lightyellow": [255, 255, 224],
      "lime": [0, 255, 0],
      "limegreen": [50, 205, 50],
      "linen": [250, 240, 230],
      "magenta": [255, 0, 255],
      "maroon": [128, 0, 0],
      "mediumaquamarine": [102, 205, 170],
      "mediumblue": [0, 0, 205],
      "mediumorchid": [186, 85, 211],
      "mediumpurple": [147, 112, 219],
      "mediumseagreen": [60, 179, 113],
      "mediumslateblue": [123, 104, 238],
      "mediumspringgreen": [0, 250, 154],
      "mediumturquoise": [72, 209, 204],
      "mediumvioletred": [199, 21, 133],
      "midnightblue": [25, 25, 112],
      "mintcream": [245, 255, 250],
      "mistyrose": [255, 228, 225],
      "moccasin": [255, 228, 181],
      "navajowhite": [255, 222, 173],
      "navy": [0, 0, 128],
      "oldlace": [253, 245, 230],
      "olive": [128, 128, 0],
      "olivedrab": [107, 142, 35],
      "orange": [255, 165, 0],
      "orangered": [255, 69, 0],
      "orchid": [218, 112, 214],
      "palegoldenrod": [238, 232, 170],
      "palegreen": [152, 251, 152],
      "paleturquoise": [175, 238, 238],
      "palevioletred": [219, 112, 147],
      "papayawhip": [255, 239, 213],
      "peachpuff": [255, 218, 185],
      "peru": [205, 133, 63],
      "pink": [255, 192, 203],
      "plum": [221, 160, 221],
      "powderblue": [176, 224, 230],
      "purple": [128, 0, 128],
      "rebeccapurple": [102, 51, 153],
      "red": [255, 0, 0],
      "rosybrown": [188, 143, 143],
      "royalblue": [65, 105, 225],
      "saddlebrown": [139, 69, 19],
      "salmon": [250, 128, 114],
      "sandybrown": [244, 164, 96],
      "seagreen": [46, 139, 87],
      "seashell": [255, 245, 238],
      "sienna": [160, 82, 45],
      "silver": [192, 192, 192],
      "skyblue": [135, 206, 235],
      "slateblue": [106, 90, 205],
      "slategray": [112, 128, 144],
      "slategrey": [112, 128, 144],
      "snow": [255, 250, 250],
      "springgreen": [0, 255, 127],
      "steelblue": [70, 130, 180],
      "tan": [210, 180, 140],
      "teal": [0, 128, 128],
      "thistle": [216, 191, 216],
      "tomato": [255, 99, 71],
      "turquoise": [64, 224, 208],
      "violet": [238, 130, 238],
      "wheat": [245, 222, 179],
      "white": [255, 255, 255],
      "whitesmoke": [245, 245, 245],
      "yellow": [255, 255, 0],
      "yellowgreen": [154, 205, 50]
    };
  }
});

// node_modules/is-arrayish/index.js
var require_is_arrayish = __commonJS({
  "node_modules/is-arrayish/index.js"(exports, module) {
    module.exports = function isArrayish(obj) {
      if (!obj || typeof obj === "string") {
        return false;
      }
      return obj instanceof Array || Array.isArray(obj) || obj.length >= 0 && (obj.splice instanceof Function || Object.getOwnPropertyDescriptor(obj, obj.length - 1) && obj.constructor.name !== "String");
    };
  }
});

// node_modules/simple-swizzle/index.js
var require_simple_swizzle = __commonJS({
  "node_modules/simple-swizzle/index.js"(exports, module) {
    "use strict";
    var isArrayish = require_is_arrayish();
    var concat = Array.prototype.concat;
    var slice = Array.prototype.slice;
    var swizzle = module.exports = function swizzle2(args) {
      var results = [];
      for (var i = 0, len = args.length; i < len; i++) {
        var arg = args[i];
        if (isArrayish(arg)) {
          results = concat.call(results, slice.call(arg));
        } else {
          results.push(arg);
        }
      }
      return results;
    };
    swizzle.wrap = function(fn) {
      return function() {
        return fn(swizzle(arguments));
      };
    };
  }
});

// node_modules/color-string/index.js
var require_color_string = __commonJS({
  "node_modules/color-string/index.js"(exports, module) {
    var colorNames = require_color_name();
    var swizzle = require_simple_swizzle();
    var hasOwnProperty = Object.hasOwnProperty;
    var reverseNames = /* @__PURE__ */ Object.create(null);
    for (name in colorNames) {
      if (hasOwnProperty.call(colorNames, name)) {
        reverseNames[colorNames[name]] = name;
      }
    }
    var name;
    var cs = module.exports = {
      to: {},
      get: {}
    };
    cs.get = function(string) {
      var prefix = string.substring(0, 3).toLowerCase();
      var val;
      var model;
      switch (prefix) {
        case "hsl":
          val = cs.get.hsl(string);
          model = "hsl";
          break;
        case "hwb":
          val = cs.get.hwb(string);
          model = "hwb";
          break;
        default:
          val = cs.get.rgb(string);
          model = "rgb";
          break;
      }
      if (!val) {
        return null;
      }
      return { model, value: val };
    };
    cs.get.rgb = function(string) {
      if (!string) {
        return null;
      }
      var abbr = /^#([a-f0-9]{3,4})$/i;
      var hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;
      var rgba = /^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/;
      var per = /^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/;
      var keyword = /^(\w+)$/;
      var rgb = [0, 0, 0, 1];
      var match;
      var i;
      var hexAlpha;
      if (match = string.match(hex)) {
        hexAlpha = match[2];
        match = match[1];
        for (i = 0; i < 3; i++) {
          var i2 = i * 2;
          rgb[i] = parseInt(match.slice(i2, i2 + 2), 16);
        }
        if (hexAlpha) {
          rgb[3] = parseInt(hexAlpha, 16) / 255;
        }
      } else if (match = string.match(abbr)) {
        match = match[1];
        hexAlpha = match[3];
        for (i = 0; i < 3; i++) {
          rgb[i] = parseInt(match[i] + match[i], 16);
        }
        if (hexAlpha) {
          rgb[3] = parseInt(hexAlpha + hexAlpha, 16) / 255;
        }
      } else if (match = string.match(rgba)) {
        for (i = 0; i < 3; i++) {
          rgb[i] = parseInt(match[i + 1], 0);
        }
        if (match[4]) {
          if (match[5]) {
            rgb[3] = parseFloat(match[4]) * 0.01;
          } else {
            rgb[3] = parseFloat(match[4]);
          }
        }
      } else if (match = string.match(per)) {
        for (i = 0; i < 3; i++) {
          rgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);
        }
        if (match[4]) {
          if (match[5]) {
            rgb[3] = parseFloat(match[4]) * 0.01;
          } else {
            rgb[3] = parseFloat(match[4]);
          }
        }
      } else if (match = string.match(keyword)) {
        if (match[1] === "transparent") {
          return [0, 0, 0, 0];
        }
        if (!hasOwnProperty.call(colorNames, match[1])) {
          return null;
        }
        rgb = colorNames[match[1]];
        rgb[3] = 1;
        return rgb;
      } else {
        return null;
      }
      for (i = 0; i < 3; i++) {
        rgb[i] = clamp(rgb[i], 0, 255);
      }
      rgb[3] = clamp(rgb[3], 0, 1);
      return rgb;
    };
    cs.get.hsl = function(string) {
      if (!string) {
        return null;
      }
      var hsl = /^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/;
      var match = string.match(hsl);
      if (match) {
        var alpha = parseFloat(match[4]);
        var h = (parseFloat(match[1]) % 360 + 360) % 360;
        var s = clamp(parseFloat(match[2]), 0, 100);
        var l = clamp(parseFloat(match[3]), 0, 100);
        var a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);
        return [h, s, l, a];
      }
      return null;
    };
    cs.get.hwb = function(string) {
      if (!string) {
        return null;
      }
      var hwb = /^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/;
      var match = string.match(hwb);
      if (match) {
        var alpha = parseFloat(match[4]);
        var h = (parseFloat(match[1]) % 360 + 360) % 360;
        var w = clamp(parseFloat(match[2]), 0, 100);
        var b = clamp(parseFloat(match[3]), 0, 100);
        var a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);
        return [h, w, b, a];
      }
      return null;
    };
    cs.to.hex = function() {
      var rgba = swizzle(arguments);
      return "#" + hexDouble(rgba[0]) + hexDouble(rgba[1]) + hexDouble(rgba[2]) + (rgba[3] < 1 ? hexDouble(Math.round(rgba[3] * 255)) : "");
    };
    cs.to.rgb = function() {
      var rgba = swizzle(arguments);
      return rgba.length < 4 || rgba[3] === 1 ? "rgb(" + Math.round(rgba[0]) + ", " + Math.round(rgba[1]) + ", " + Math.round(rgba[2]) + ")" : "rgba(" + Math.round(rgba[0]) + ", " + Math.round(rgba[1]) + ", " + Math.round(rgba[2]) + ", " + rgba[3] + ")";
    };
    cs.to.rgb.percent = function() {
      var rgba = swizzle(arguments);
      var r = Math.round(rgba[0] / 255 * 100);
      var g = Math.round(rgba[1] / 255 * 100);
      var b = Math.round(rgba[2] / 255 * 100);
      return rgba.length < 4 || rgba[3] === 1 ? "rgb(" + r + "%, " + g + "%, " + b + "%)" : "rgba(" + r + "%, " + g + "%, " + b + "%, " + rgba[3] + ")";
    };
    cs.to.hsl = function() {
      var hsla = swizzle(arguments);
      return hsla.length < 4 || hsla[3] === 1 ? "hsl(" + hsla[0] + ", " + hsla[1] + "%, " + hsla[2] + "%)" : "hsla(" + hsla[0] + ", " + hsla[1] + "%, " + hsla[2] + "%, " + hsla[3] + ")";
    };
    cs.to.hwb = function() {
      var hwba = swizzle(arguments);
      var a = "";
      if (hwba.length >= 4 && hwba[3] !== 1) {
        a = ", " + hwba[3];
      }
      return "hwb(" + hwba[0] + ", " + hwba[1] + "%, " + hwba[2] + "%" + a + ")";
    };
    cs.to.keyword = function(rgb) {
      return reverseNames[rgb.slice(0, 3)];
    };
    function clamp(num, min, max) {
      return Math.min(Math.max(min, num), max);
    }
    function hexDouble(num) {
      var str = Math.round(num).toString(16).toUpperCase();
      return str.length < 2 ? "0" + str : str;
    }
  }
});

// node_modules/color/node_modules/color-name/index.js
var require_color_name2 = __commonJS({
  "node_modules/color/node_modules/color-name/index.js"(exports, module) {
    "use strict";
    module.exports = {
      "aliceblue": [240, 248, 255],
      "antiquewhite": [250, 235, 215],
      "aqua": [0, 255, 255],
      "aquamarine": [127, 255, 212],
      "azure": [240, 255, 255],
      "beige": [245, 245, 220],
      "bisque": [255, 228, 196],
      "black": [0, 0, 0],
      "blanchedalmond": [255, 235, 205],
      "blue": [0, 0, 255],
      "blueviolet": [138, 43, 226],
      "brown": [165, 42, 42],
      "burlywood": [222, 184, 135],
      "cadetblue": [95, 158, 160],
      "chartreuse": [127, 255, 0],
      "chocolate": [210, 105, 30],
      "coral": [255, 127, 80],
      "cornflowerblue": [100, 149, 237],
      "cornsilk": [255, 248, 220],
      "crimson": [220, 20, 60],
      "cyan": [0, 255, 255],
      "darkblue": [0, 0, 139],
      "darkcyan": [0, 139, 139],
      "darkgoldenrod": [184, 134, 11],
      "darkgray": [169, 169, 169],
      "darkgreen": [0, 100, 0],
      "darkgrey": [169, 169, 169],
      "darkkhaki": [189, 183, 107],
      "darkmagenta": [139, 0, 139],
      "darkolivegreen": [85, 107, 47],
      "darkorange": [255, 140, 0],
      "darkorchid": [153, 50, 204],
      "darkred": [139, 0, 0],
      "darksalmon": [233, 150, 122],
      "darkseagreen": [143, 188, 143],
      "darkslateblue": [72, 61, 139],
      "darkslategray": [47, 79, 79],
      "darkslategrey": [47, 79, 79],
      "darkturquoise": [0, 206, 209],
      "darkviolet": [148, 0, 211],
      "deeppink": [255, 20, 147],
      "deepskyblue": [0, 191, 255],
      "dimgray": [105, 105, 105],
      "dimgrey": [105, 105, 105],
      "dodgerblue": [30, 144, 255],
      "firebrick": [178, 34, 34],
      "floralwhite": [255, 250, 240],
      "forestgreen": [34, 139, 34],
      "fuchsia": [255, 0, 255],
      "gainsboro": [220, 220, 220],
      "ghostwhite": [248, 248, 255],
      "gold": [255, 215, 0],
      "goldenrod": [218, 165, 32],
      "gray": [128, 128, 128],
      "green": [0, 128, 0],
      "greenyellow": [173, 255, 47],
      "grey": [128, 128, 128],
      "honeydew": [240, 255, 240],
      "hotpink": [255, 105, 180],
      "indianred": [205, 92, 92],
      "indigo": [75, 0, 130],
      "ivory": [255, 255, 240],
      "khaki": [240, 230, 140],
      "lavender": [230, 230, 250],
      "lavenderblush": [255, 240, 245],
      "lawngreen": [124, 252, 0],
      "lemonchiffon": [255, 250, 205],
      "lightblue": [173, 216, 230],
      "lightcoral": [240, 128, 128],
      "lightcyan": [224, 255, 255],
      "lightgoldenrodyellow": [250, 250, 210],
      "lightgray": [211, 211, 211],
      "lightgreen": [144, 238, 144],
      "lightgrey": [211, 211, 211],
      "lightpink": [255, 182, 193],
      "lightsalmon": [255, 160, 122],
      "lightseagreen": [32, 178, 170],
      "lightskyblue": [135, 206, 250],
      "lightslategray": [119, 136, 153],
      "lightslategrey": [119, 136, 153],
      "lightsteelblue": [176, 196, 222],
      "lightyellow": [255, 255, 224],
      "lime": [0, 255, 0],
      "limegreen": [50, 205, 50],
      "linen": [250, 240, 230],
      "magenta": [255, 0, 255],
      "maroon": [128, 0, 0],
      "mediumaquamarine": [102, 205, 170],
      "mediumblue": [0, 0, 205],
      "mediumorchid": [186, 85, 211],
      "mediumpurple": [147, 112, 219],
      "mediumseagreen": [60, 179, 113],
      "mediumslateblue": [123, 104, 238],
      "mediumspringgreen": [0, 250, 154],
      "mediumturquoise": [72, 209, 204],
      "mediumvioletred": [199, 21, 133],
      "midnightblue": [25, 25, 112],
      "mintcream": [245, 255, 250],
      "mistyrose": [255, 228, 225],
      "moccasin": [255, 228, 181],
      "navajowhite": [255, 222, 173],
      "navy": [0, 0, 128],
      "oldlace": [253, 245, 230],
      "olive": [128, 128, 0],
      "olivedrab": [107, 142, 35],
      "orange": [255, 165, 0],
      "orangered": [255, 69, 0],
      "orchid": [218, 112, 214],
      "palegoldenrod": [238, 232, 170],
      "palegreen": [152, 251, 152],
      "paleturquoise": [175, 238, 238],
      "palevioletred": [219, 112, 147],
      "papayawhip": [255, 239, 213],
      "peachpuff": [255, 218, 185],
      "peru": [205, 133, 63],
      "pink": [255, 192, 203],
      "plum": [221, 160, 221],
      "powderblue": [176, 224, 230],
      "purple": [128, 0, 128],
      "rebeccapurple": [102, 51, 153],
      "red": [255, 0, 0],
      "rosybrown": [188, 143, 143],
      "royalblue": [65, 105, 225],
      "saddlebrown": [139, 69, 19],
      "salmon": [250, 128, 114],
      "sandybrown": [244, 164, 96],
      "seagreen": [46, 139, 87],
      "seashell": [255, 245, 238],
      "sienna": [160, 82, 45],
      "silver": [192, 192, 192],
      "skyblue": [135, 206, 235],
      "slateblue": [106, 90, 205],
      "slategray": [112, 128, 144],
      "slategrey": [112, 128, 144],
      "snow": [255, 250, 250],
      "springgreen": [0, 255, 127],
      "steelblue": [70, 130, 180],
      "tan": [210, 180, 140],
      "teal": [0, 128, 128],
      "thistle": [216, 191, 216],
      "tomato": [255, 99, 71],
      "turquoise": [64, 224, 208],
      "violet": [238, 130, 238],
      "wheat": [245, 222, 179],
      "white": [255, 255, 255],
      "whitesmoke": [245, 245, 245],
      "yellow": [255, 255, 0],
      "yellowgreen": [154, 205, 50]
    };
  }
});

// node_modules/color/node_modules/color-convert/conversions.js
var require_conversions = __commonJS({
  "node_modules/color/node_modules/color-convert/conversions.js"(exports, module) {
    var cssKeywords = require_color_name2();
    var reverseKeywords = {};
    for (key in cssKeywords) {
      if (cssKeywords.hasOwnProperty(key)) {
        reverseKeywords[cssKeywords[key]] = key;
      }
    }
    var key;
    var convert = module.exports = {
      rgb: { channels: 3, labels: "rgb" },
      hsl: { channels: 3, labels: "hsl" },
      hsv: { channels: 3, labels: "hsv" },
      hwb: { channels: 3, labels: "hwb" },
      cmyk: { channels: 4, labels: "cmyk" },
      xyz: { channels: 3, labels: "xyz" },
      lab: { channels: 3, labels: "lab" },
      lch: { channels: 3, labels: "lch" },
      hex: { channels: 1, labels: ["hex"] },
      keyword: { channels: 1, labels: ["keyword"] },
      ansi16: { channels: 1, labels: ["ansi16"] },
      ansi256: { channels: 1, labels: ["ansi256"] },
      hcg: { channels: 3, labels: ["h", "c", "g"] },
      apple: { channels: 3, labels: ["r16", "g16", "b16"] },
      gray: { channels: 1, labels: ["gray"] }
    };
    for (model in convert) {
      if (convert.hasOwnProperty(model)) {
        if (!("channels" in convert[model])) {
          throw new Error("missing channels property: " + model);
        }
        if (!("labels" in convert[model])) {
          throw new Error("missing channel labels property: " + model);
        }
        if (convert[model].labels.length !== convert[model].channels) {
          throw new Error("channel and label counts mismatch: " + model);
        }
        channels = convert[model].channels;
        labels = convert[model].labels;
        delete convert[model].channels;
        delete convert[model].labels;
        Object.defineProperty(convert[model], "channels", { value: channels });
        Object.defineProperty(convert[model], "labels", { value: labels });
      }
    }
    var channels;
    var labels;
    var model;
    convert.rgb.hsl = function(rgb) {
      var r = rgb[0] / 255;
      var g = rgb[1] / 255;
      var b = rgb[2] / 255;
      var min = Math.min(r, g, b);
      var max = Math.max(r, g, b);
      var delta = max - min;
      var h;
      var s;
      var l;
      if (max === min) {
        h = 0;
      } else if (r === max) {
        h = (g - b) / delta;
      } else if (g === max) {
        h = 2 + (b - r) / delta;
      } else if (b === max) {
        h = 4 + (r - g) / delta;
      }
      h = Math.min(h * 60, 360);
      if (h < 0) {
        h += 360;
      }
      l = (min + max) / 2;
      if (max === min) {
        s = 0;
      } else if (l <= 0.5) {
        s = delta / (max + min);
      } else {
        s = delta / (2 - max - min);
      }
      return [h, s * 100, l * 100];
    };
    convert.rgb.hsv = function(rgb) {
      var rdif;
      var gdif;
      var bdif;
      var h;
      var s;
      var r = rgb[0] / 255;
      var g = rgb[1] / 255;
      var b = rgb[2] / 255;
      var v = Math.max(r, g, b);
      var diff = v - Math.min(r, g, b);
      var diffc = function(c) {
        return (v - c) / 6 / diff + 1 / 2;
      };
      if (diff === 0) {
        h = s = 0;
      } else {
        s = diff / v;
        rdif = diffc(r);
        gdif = diffc(g);
        bdif = diffc(b);
        if (r === v) {
          h = bdif - gdif;
        } else if (g === v) {
          h = 1 / 3 + rdif - bdif;
        } else if (b === v) {
          h = 2 / 3 + gdif - rdif;
        }
        if (h < 0) {
          h += 1;
        } else if (h > 1) {
          h -= 1;
        }
      }
      return [
        h * 360,
        s * 100,
        v * 100
      ];
    };
    convert.rgb.hwb = function(rgb) {
      var r = rgb[0];
      var g = rgb[1];
      var b = rgb[2];
      var h = convert.rgb.hsl(rgb)[0];
      var w = 1 / 255 * Math.min(r, Math.min(g, b));
      b = 1 - 1 / 255 * Math.max(r, Math.max(g, b));
      return [h, w * 100, b * 100];
    };
    convert.rgb.cmyk = function(rgb) {
      var r = rgb[0] / 255;
      var g = rgb[1] / 255;
      var b = rgb[2] / 255;
      var c;
      var m;
      var y;
      var k;
      k = Math.min(1 - r, 1 - g, 1 - b);
      c = (1 - r - k) / (1 - k) || 0;
      m = (1 - g - k) / (1 - k) || 0;
      y = (1 - b - k) / (1 - k) || 0;
      return [c * 100, m * 100, y * 100, k * 100];
    };
    function comparativeDistance(x, y) {
      return Math.pow(x[0] - y[0], 2) + Math.pow(x[1] - y[1], 2) + Math.pow(x[2] - y[2], 2);
    }
    convert.rgb.keyword = function(rgb) {
      var reversed = reverseKeywords[rgb];
      if (reversed) {
        return reversed;
      }
      var currentClosestDistance = Infinity;
      var currentClosestKeyword;
      for (var keyword in cssKeywords) {
        if (cssKeywords.hasOwnProperty(keyword)) {
          var value = cssKeywords[keyword];
          var distance = comparativeDistance(rgb, value);
          if (distance < currentClosestDistance) {
            currentClosestDistance = distance;
            currentClosestKeyword = keyword;
          }
        }
      }
      return currentClosestKeyword;
    };
    convert.keyword.rgb = function(keyword) {
      return cssKeywords[keyword];
    };
    convert.rgb.xyz = function(rgb) {
      var r = rgb[0] / 255;
      var g = rgb[1] / 255;
      var b = rgb[2] / 255;
      r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
      g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
      b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;
      var x = r * 0.4124 + g * 0.3576 + b * 0.1805;
      var y = r * 0.2126 + g * 0.7152 + b * 0.0722;
      var z = r * 0.0193 + g * 0.1192 + b * 0.9505;
      return [x * 100, y * 100, z * 100];
    };
    convert.rgb.lab = function(rgb) {
      var xyz = convert.rgb.xyz(rgb);
      var x = xyz[0];
      var y = xyz[1];
      var z = xyz[2];
      var l;
      var a;
      var b;
      x /= 95.047;
      y /= 100;
      z /= 108.883;
      x = x > 8856e-6 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;
      y = y > 8856e-6 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;
      z = z > 8856e-6 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;
      l = 116 * y - 16;
      a = 500 * (x - y);
      b = 200 * (y - z);
      return [l, a, b];
    };
    convert.hsl.rgb = function(hsl) {
      var h = hsl[0] / 360;
      var s = hsl[1] / 100;
      var l = hsl[2] / 100;
      var t1;
      var t2;
      var t3;
      var rgb;
      var val;
      if (s === 0) {
        val = l * 255;
        return [val, val, val];
      }
      if (l < 0.5) {
        t2 = l * (1 + s);
      } else {
        t2 = l + s - l * s;
      }
      t1 = 2 * l - t2;
      rgb = [0, 0, 0];
      for (var i = 0; i < 3; i++) {
        t3 = h + 1 / 3 * -(i - 1);
        if (t3 < 0) {
          t3++;
        }
        if (t3 > 1) {
          t3--;
        }
        if (6 * t3 < 1) {
          val = t1 + (t2 - t1) * 6 * t3;
        } else if (2 * t3 < 1) {
          val = t2;
        } else if (3 * t3 < 2) {
          val = t1 + (t2 - t1) * (2 / 3 - t3) * 6;
        } else {
          val = t1;
        }
        rgb[i] = val * 255;
      }
      return rgb;
    };
    convert.hsl.hsv = function(hsl) {
      var h = hsl[0];
      var s = hsl[1] / 100;
      var l = hsl[2] / 100;
      var smin = s;
      var lmin = Math.max(l, 0.01);
      var sv;
      var v;
      l *= 2;
      s *= l <= 1 ? l : 2 - l;
      smin *= lmin <= 1 ? lmin : 2 - lmin;
      v = (l + s) / 2;
      sv = l === 0 ? 2 * smin / (lmin + smin) : 2 * s / (l + s);
      return [h, sv * 100, v * 100];
    };
    convert.hsv.rgb = function(hsv) {
      var h = hsv[0] / 60;
      var s = hsv[1] / 100;
      var v = hsv[2] / 100;
      var hi = Math.floor(h) % 6;
      var f = h - Math.floor(h);
      var p = 255 * v * (1 - s);
      var q = 255 * v * (1 - s * f);
      var t = 255 * v * (1 - s * (1 - f));
      v *= 255;
      switch (hi) {
        case 0:
          return [v, t, p];
        case 1:
          return [q, v, p];
        case 2:
          return [p, v, t];
        case 3:
          return [p, q, v];
        case 4:
          return [t, p, v];
        case 5:
          return [v, p, q];
      }
    };
    convert.hsv.hsl = function(hsv) {
      var h = hsv[0];
      var s = hsv[1] / 100;
      var v = hsv[2] / 100;
      var vmin = Math.max(v, 0.01);
      var lmin;
      var sl;
      var l;
      l = (2 - s) * v;
      lmin = (2 - s) * vmin;
      sl = s * vmin;
      sl /= lmin <= 1 ? lmin : 2 - lmin;
      sl = sl || 0;
      l /= 2;
      return [h, sl * 100, l * 100];
    };
    convert.hwb.rgb = function(hwb) {
      var h = hwb[0] / 360;
      var wh = hwb[1] / 100;
      var bl = hwb[2] / 100;
      var ratio = wh + bl;
      var i;
      var v;
      var f;
      var n;
      if (ratio > 1) {
        wh /= ratio;
        bl /= ratio;
      }
      i = Math.floor(6 * h);
      v = 1 - bl;
      f = 6 * h - i;
      if ((i & 1) !== 0) {
        f = 1 - f;
      }
      n = wh + f * (v - wh);
      var r;
      var g;
      var b;
      switch (i) {
        default:
        case 6:
        case 0:
          r = v;
          g = n;
          b = wh;
          break;
        case 1:
          r = n;
          g = v;
          b = wh;
          break;
        case 2:
          r = wh;
          g = v;
          b = n;
          break;
        case 3:
          r = wh;
          g = n;
          b = v;
          break;
        case 4:
          r = n;
          g = wh;
          b = v;
          break;
        case 5:
          r = v;
          g = wh;
          b = n;
          break;
      }
      return [r * 255, g * 255, b * 255];
    };
    convert.cmyk.rgb = function(cmyk) {
      var c = cmyk[0] / 100;
      var m = cmyk[1] / 100;
      var y = cmyk[2] / 100;
      var k = cmyk[3] / 100;
      var r;
      var g;
      var b;
      r = 1 - Math.min(1, c * (1 - k) + k);
      g = 1 - Math.min(1, m * (1 - k) + k);
      b = 1 - Math.min(1, y * (1 - k) + k);
      return [r * 255, g * 255, b * 255];
    };
    convert.xyz.rgb = function(xyz) {
      var x = xyz[0] / 100;
      var y = xyz[1] / 100;
      var z = xyz[2] / 100;
      var r;
      var g;
      var b;
      r = x * 3.2406 + y * -1.5372 + z * -0.4986;
      g = x * -0.9689 + y * 1.8758 + z * 0.0415;
      b = x * 0.0557 + y * -0.204 + z * 1.057;
      r = r > 31308e-7 ? 1.055 * Math.pow(r, 1 / 2.4) - 0.055 : r * 12.92;
      g = g > 31308e-7 ? 1.055 * Math.pow(g, 1 / 2.4) - 0.055 : g * 12.92;
      b = b > 31308e-7 ? 1.055 * Math.pow(b, 1 / 2.4) - 0.055 : b * 12.92;
      r = Math.min(Math.max(0, r), 1);
      g = Math.min(Math.max(0, g), 1);
      b = Math.min(Math.max(0, b), 1);
      return [r * 255, g * 255, b * 255];
    };
    convert.xyz.lab = function(xyz) {
      var x = xyz[0];
      var y = xyz[1];
      var z = xyz[2];
      var l;
      var a;
      var b;
      x /= 95.047;
      y /= 100;
      z /= 108.883;
      x = x > 8856e-6 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;
      y = y > 8856e-6 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;
      z = z > 8856e-6 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;
      l = 116 * y - 16;
      a = 500 * (x - y);
      b = 200 * (y - z);
      return [l, a, b];
    };
    convert.lab.xyz = function(lab) {
      var l = lab[0];
      var a = lab[1];
      var b = lab[2];
      var x;
      var y;
      var z;
      y = (l + 16) / 116;
      x = a / 500 + y;
      z = y - b / 200;
      var y2 = Math.pow(y, 3);
      var x2 = Math.pow(x, 3);
      var z2 = Math.pow(z, 3);
      y = y2 > 8856e-6 ? y2 : (y - 16 / 116) / 7.787;
      x = x2 > 8856e-6 ? x2 : (x - 16 / 116) / 7.787;
      z = z2 > 8856e-6 ? z2 : (z - 16 / 116) / 7.787;
      x *= 95.047;
      y *= 100;
      z *= 108.883;
      return [x, y, z];
    };
    convert.lab.lch = function(lab) {
      var l = lab[0];
      var a = lab[1];
      var b = lab[2];
      var hr;
      var h;
      var c;
      hr = Math.atan2(b, a);
      h = hr * 360 / 2 / Math.PI;
      if (h < 0) {
        h += 360;
      }
      c = Math.sqrt(a * a + b * b);
      return [l, c, h];
    };
    convert.lch.lab = function(lch) {
      var l = lch[0];
      var c = lch[1];
      var h = lch[2];
      var a;
      var b;
      var hr;
      hr = h / 360 * 2 * Math.PI;
      a = c * Math.cos(hr);
      b = c * Math.sin(hr);
      return [l, a, b];
    };
    convert.rgb.ansi16 = function(args) {
      var r = args[0];
      var g = args[1];
      var b = args[2];
      var value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2];
      value = Math.round(value / 50);
      if (value === 0) {
        return 30;
      }
      var ansi = 30 + (Math.round(b / 255) << 2 | Math.round(g / 255) << 1 | Math.round(r / 255));
      if (value === 2) {
        ansi += 60;
      }
      return ansi;
    };
    convert.hsv.ansi16 = function(args) {
      return convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);
    };
    convert.rgb.ansi256 = function(args) {
      var r = args[0];
      var g = args[1];
      var b = args[2];
      if (r === g && g === b) {
        if (r < 8) {
          return 16;
        }
        if (r > 248) {
          return 231;
        }
        return Math.round((r - 8) / 247 * 24) + 232;
      }
      var ansi = 16 + 36 * Math.round(r / 255 * 5) + 6 * Math.round(g / 255 * 5) + Math.round(b / 255 * 5);
      return ansi;
    };
    convert.ansi16.rgb = function(args) {
      var color = args % 10;
      if (color === 0 || color === 7) {
        if (args > 50) {
          color += 3.5;
        }
        color = color / 10.5 * 255;
        return [color, color, color];
      }
      var mult = (~~(args > 50) + 1) * 0.5;
      var r = (color & 1) * mult * 255;
      var g = (color >> 1 & 1) * mult * 255;
      var b = (color >> 2 & 1) * mult * 255;
      return [r, g, b];
    };
    convert.ansi256.rgb = function(args) {
      if (args >= 232) {
        var c = (args - 232) * 10 + 8;
        return [c, c, c];
      }
      args -= 16;
      var rem;
      var r = Math.floor(args / 36) / 5 * 255;
      var g = Math.floor((rem = args % 36) / 6) / 5 * 255;
      var b = rem % 6 / 5 * 255;
      return [r, g, b];
    };
    convert.rgb.hex = function(args) {
      var integer = ((Math.round(args[0]) & 255) << 16) + ((Math.round(args[1]) & 255) << 8) + (Math.round(args[2]) & 255);
      var string = integer.toString(16).toUpperCase();
      return "000000".substring(string.length) + string;
    };
    convert.hex.rgb = function(args) {
      var match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);
      if (!match) {
        return [0, 0, 0];
      }
      var colorString = match[0];
      if (match[0].length === 3) {
        colorString = colorString.split("").map(function(char) {
          return char + char;
        }).join("");
      }
      var integer = parseInt(colorString, 16);
      var r = integer >> 16 & 255;
      var g = integer >> 8 & 255;
      var b = integer & 255;
      return [r, g, b];
    };
    convert.rgb.hcg = function(rgb) {
      var r = rgb[0] / 255;
      var g = rgb[1] / 255;
      var b = rgb[2] / 255;
      var max = Math.max(Math.max(r, g), b);
      var min = Math.min(Math.min(r, g), b);
      var chroma = max - min;
      var grayscale;
      var hue;
      if (chroma < 1) {
        grayscale = min / (1 - chroma);
      } else {
        grayscale = 0;
      }
      if (chroma <= 0) {
        hue = 0;
      } else if (max === r) {
        hue = (g - b) / chroma % 6;
      } else if (max === g) {
        hue = 2 + (b - r) / chroma;
      } else {
        hue = 4 + (r - g) / chroma + 4;
      }
      hue /= 6;
      hue %= 1;
      return [hue * 360, chroma * 100, grayscale * 100];
    };
    convert.hsl.hcg = function(hsl) {
      var s = hsl[1] / 100;
      var l = hsl[2] / 100;
      var c = 1;
      var f = 0;
      if (l < 0.5) {
        c = 2 * s * l;
      } else {
        c = 2 * s * (1 - l);
      }
      if (c < 1) {
        f = (l - 0.5 * c) / (1 - c);
      }
      return [hsl[0], c * 100, f * 100];
    };
    convert.hsv.hcg = function(hsv) {
      var s = hsv[1] / 100;
      var v = hsv[2] / 100;
      var c = s * v;
      var f = 0;
      if (c < 1) {
        f = (v - c) / (1 - c);
      }
      return [hsv[0], c * 100, f * 100];
    };
    convert.hcg.rgb = function(hcg) {
      var h = hcg[0] / 360;
      var c = hcg[1] / 100;
      var g = hcg[2] / 100;
      if (c === 0) {
        return [g * 255, g * 255, g * 255];
      }
      var pure = [0, 0, 0];
      var hi = h % 1 * 6;
      var v = hi % 1;
      var w = 1 - v;
      var mg = 0;
      switch (Math.floor(hi)) {
        case 0:
          pure[0] = 1;
          pure[1] = v;
          pure[2] = 0;
          break;
        case 1:
          pure[0] = w;
          pure[1] = 1;
          pure[2] = 0;
          break;
        case 2:
          pure[0] = 0;
          pure[1] = 1;
          pure[2] = v;
          break;
        case 3:
          pure[0] = 0;
          pure[1] = w;
          pure[2] = 1;
          break;
        case 4:
          pure[0] = v;
          pure[1] = 0;
          pure[2] = 1;
          break;
        default:
          pure[0] = 1;
          pure[1] = 0;
          pure[2] = w;
      }
      mg = (1 - c) * g;
      return [
        (c * pure[0] + mg) * 255,
        (c * pure[1] + mg) * 255,
        (c * pure[2] + mg) * 255
      ];
    };
    convert.hcg.hsv = function(hcg) {
      var c = hcg[1] / 100;
      var g = hcg[2] / 100;
      var v = c + g * (1 - c);
      var f = 0;
      if (v > 0) {
        f = c / v;
      }
      return [hcg[0], f * 100, v * 100];
    };
    convert.hcg.hsl = function(hcg) {
      var c = hcg[1] / 100;
      var g = hcg[2] / 100;
      var l = g * (1 - c) + 0.5 * c;
      var s = 0;
      if (l > 0 && l < 0.5) {
        s = c / (2 * l);
      } else if (l >= 0.5 && l < 1) {
        s = c / (2 * (1 - l));
      }
      return [hcg[0], s * 100, l * 100];
    };
    convert.hcg.hwb = function(hcg) {
      var c = hcg[1] / 100;
      var g = hcg[2] / 100;
      var v = c + g * (1 - c);
      return [hcg[0], (v - c) * 100, (1 - v) * 100];
    };
    convert.hwb.hcg = function(hwb) {
      var w = hwb[1] / 100;
      var b = hwb[2] / 100;
      var v = 1 - b;
      var c = v - w;
      var g = 0;
      if (c < 1) {
        g = (v - c) / (1 - c);
      }
      return [hwb[0], c * 100, g * 100];
    };
    convert.apple.rgb = function(apple) {
      return [apple[0] / 65535 * 255, apple[1] / 65535 * 255, apple[2] / 65535 * 255];
    };
    convert.rgb.apple = function(rgb) {
      return [rgb[0] / 255 * 65535, rgb[1] / 255 * 65535, rgb[2] / 255 * 65535];
    };
    convert.gray.rgb = function(args) {
      return [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];
    };
    convert.gray.hsl = convert.gray.hsv = function(args) {
      return [0, 0, args[0]];
    };
    convert.gray.hwb = function(gray) {
      return [0, 100, gray[0]];
    };
    convert.gray.cmyk = function(gray) {
      return [0, 0, 0, gray[0]];
    };
    convert.gray.lab = function(gray) {
      return [gray[0], 0, 0];
    };
    convert.gray.hex = function(gray) {
      var val = Math.round(gray[0] / 100 * 255) & 255;
      var integer = (val << 16) + (val << 8) + val;
      var string = integer.toString(16).toUpperCase();
      return "000000".substring(string.length) + string;
    };
    convert.rgb.gray = function(rgb) {
      var val = (rgb[0] + rgb[1] + rgb[2]) / 3;
      return [val / 255 * 100];
    };
  }
});

// node_modules/color/node_modules/color-convert/route.js
var require_route = __commonJS({
  "node_modules/color/node_modules/color-convert/route.js"(exports, module) {
    var conversions = require_conversions();
    function buildGraph() {
      var graph = {};
      var models = Object.keys(conversions);
      for (var len = models.length, i = 0; i < len; i++) {
        graph[models[i]] = {
          // http://jsperf.com/1-vs-infinity
          // micro-opt, but this is simple.
          distance: -1,
          parent: null
        };
      }
      return graph;
    }
    function deriveBFS(fromModel) {
      var graph = buildGraph();
      var queue = [fromModel];
      graph[fromModel].distance = 0;
      while (queue.length) {
        var current = queue.pop();
        var adjacents = Object.keys(conversions[current]);
        for (var len = adjacents.length, i = 0; i < len; i++) {
          var adjacent = adjacents[i];
          var node = graph[adjacent];
          if (node.distance === -1) {
            node.distance = graph[current].distance + 1;
            node.parent = current;
            queue.unshift(adjacent);
          }
        }
      }
      return graph;
    }
    function link(from, to) {
      return function(args) {
        return to(from(args));
      };
    }
    function wrapConversion(toModel, graph) {
      var path = [graph[toModel].parent, toModel];
      var fn = conversions[graph[toModel].parent][toModel];
      var cur = graph[toModel].parent;
      while (graph[cur].parent) {
        path.unshift(graph[cur].parent);
        fn = link(conversions[graph[cur].parent][cur], fn);
        cur = graph[cur].parent;
      }
      fn.conversion = path;
      return fn;
    }
    module.exports = function(fromModel) {
      var graph = deriveBFS(fromModel);
      var conversion = {};
      var models = Object.keys(graph);
      for (var len = models.length, i = 0; i < len; i++) {
        var toModel = models[i];
        var node = graph[toModel];
        if (node.parent === null) {
          continue;
        }
        conversion[toModel] = wrapConversion(toModel, graph);
      }
      return conversion;
    };
  }
});

// node_modules/color/node_modules/color-convert/index.js
var require_color_convert = __commonJS({
  "node_modules/color/node_modules/color-convert/index.js"(exports, module) {
    var conversions = require_conversions();
    var route = require_route();
    var convert = {};
    var models = Object.keys(conversions);
    function wrapRaw(fn) {
      var wrappedFn = function(args) {
        if (args === void 0 || args === null) {
          return args;
        }
        if (arguments.length > 1) {
          args = Array.prototype.slice.call(arguments);
        }
        return fn(args);
      };
      if ("conversion" in fn) {
        wrappedFn.conversion = fn.conversion;
      }
      return wrappedFn;
    }
    function wrapRounded(fn) {
      var wrappedFn = function(args) {
        if (args === void 0 || args === null) {
          return args;
        }
        if (arguments.length > 1) {
          args = Array.prototype.slice.call(arguments);
        }
        var result = fn(args);
        if (typeof result === "object") {
          for (var len = result.length, i = 0; i < len; i++) {
            result[i] = Math.round(result[i]);
          }
        }
        return result;
      };
      if ("conversion" in fn) {
        wrappedFn.conversion = fn.conversion;
      }
      return wrappedFn;
    }
    models.forEach(function(fromModel) {
      convert[fromModel] = {};
      Object.defineProperty(convert[fromModel], "channels", { value: conversions[fromModel].channels });
      Object.defineProperty(convert[fromModel], "labels", { value: conversions[fromModel].labels });
      var routes = route(fromModel);
      var routeModels = Object.keys(routes);
      routeModels.forEach(function(toModel) {
        var fn = routes[toModel];
        convert[fromModel][toModel] = wrapRounded(fn);
        convert[fromModel][toModel].raw = wrapRaw(fn);
      });
    });
    module.exports = convert;
  }
});

// node_modules/color/index.js
var require_color = __commonJS({
  "node_modules/color/index.js"(exports, module) {
    "use strict";
    var colorString = require_color_string();
    var convert = require_color_convert();
    var _slice = [].slice;
    var skippedModels = [
      // to be honest, I don't really feel like keyword belongs in color convert, but eh.
      "keyword",
      // gray conflicts with some method names, and has its own method defined.
      "gray",
      // shouldn't really be in color-convert either...
      "hex"
    ];
    var hashedModelKeys = {};
    Object.keys(convert).forEach(function(model) {
      hashedModelKeys[_slice.call(convert[model].labels).sort().join("")] = model;
    });
    var limiters = {};
    function Color2(obj, model) {
      if (!(this instanceof Color2)) {
        return new Color2(obj, model);
      }
      if (model && model in skippedModels) {
        model = null;
      }
      if (model && !(model in convert)) {
        throw new Error("Unknown model: " + model);
      }
      var i;
      var channels;
      if (obj == null) {
        this.model = "rgb";
        this.color = [0, 0, 0];
        this.valpha = 1;
      } else if (obj instanceof Color2) {
        this.model = obj.model;
        this.color = obj.color.slice();
        this.valpha = obj.valpha;
      } else if (typeof obj === "string") {
        var result = colorString.get(obj);
        if (result === null) {
          throw new Error("Unable to parse color from string: " + obj);
        }
        this.model = result.model;
        channels = convert[this.model].channels;
        this.color = result.value.slice(0, channels);
        this.valpha = typeof result.value[channels] === "number" ? result.value[channels] : 1;
      } else if (obj.length) {
        this.model = model || "rgb";
        channels = convert[this.model].channels;
        var newArr = _slice.call(obj, 0, channels);
        this.color = zeroArray(newArr, channels);
        this.valpha = typeof obj[channels] === "number" ? obj[channels] : 1;
      } else if (typeof obj === "number") {
        obj &= 16777215;
        this.model = "rgb";
        this.color = [
          obj >> 16 & 255,
          obj >> 8 & 255,
          obj & 255
        ];
        this.valpha = 1;
      } else {
        this.valpha = 1;
        var keys = Object.keys(obj);
        if ("alpha" in obj) {
          keys.splice(keys.indexOf("alpha"), 1);
          this.valpha = typeof obj.alpha === "number" ? obj.alpha : 0;
        }
        var hashedKeys = keys.sort().join("");
        if (!(hashedKeys in hashedModelKeys)) {
          throw new Error("Unable to parse color from object: " + JSON.stringify(obj));
        }
        this.model = hashedModelKeys[hashedKeys];
        var labels = convert[this.model].labels;
        var color = [];
        for (i = 0; i < labels.length; i++) {
          color.push(obj[labels[i]]);
        }
        this.color = zeroArray(color);
      }
      if (limiters[this.model]) {
        channels = convert[this.model].channels;
        for (i = 0; i < channels; i++) {
          var limit = limiters[this.model][i];
          if (limit) {
            this.color[i] = limit(this.color[i]);
          }
        }
      }
      this.valpha = Math.max(0, Math.min(1, this.valpha));
      if (Object.freeze) {
        Object.freeze(this);
      }
    }
    Color2.prototype = {
      toString: function() {
        return this.string();
      },
      toJSON: function() {
        return this[this.model]();
      },
      string: function(places) {
        var self2 = this.model in colorString.to ? this : this.rgb();
        self2 = self2.round(typeof places === "number" ? places : 1);
        var args = self2.valpha === 1 ? self2.color : self2.color.concat(this.valpha);
        return colorString.to[self2.model](args);
      },
      percentString: function(places) {
        var self2 = this.rgb().round(typeof places === "number" ? places : 1);
        var args = self2.valpha === 1 ? self2.color : self2.color.concat(this.valpha);
        return colorString.to.rgb.percent(args);
      },
      array: function() {
        return this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);
      },
      object: function() {
        var result = {};
        var channels = convert[this.model].channels;
        var labels = convert[this.model].labels;
        for (var i = 0; i < channels; i++) {
          result[labels[i]] = this.color[i];
        }
        if (this.valpha !== 1) {
          result.alpha = this.valpha;
        }
        return result;
      },
      unitArray: function() {
        var rgb = this.rgb().color;
        rgb[0] /= 255;
        rgb[1] /= 255;
        rgb[2] /= 255;
        if (this.valpha !== 1) {
          rgb.push(this.valpha);
        }
        return rgb;
      },
      unitObject: function() {
        var rgb = this.rgb().object();
        rgb.r /= 255;
        rgb.g /= 255;
        rgb.b /= 255;
        if (this.valpha !== 1) {
          rgb.alpha = this.valpha;
        }
        return rgb;
      },
      round: function(places) {
        places = Math.max(places || 0, 0);
        return new Color2(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);
      },
      alpha: function(val) {
        if (arguments.length) {
          return new Color2(this.color.concat(Math.max(0, Math.min(1, val))), this.model);
        }
        return this.valpha;
      },
      // rgb
      red: getset("rgb", 0, maxfn(255)),
      green: getset("rgb", 1, maxfn(255)),
      blue: getset("rgb", 2, maxfn(255)),
      hue: getset(["hsl", "hsv", "hsl", "hwb", "hcg"], 0, function(val) {
        return (val % 360 + 360) % 360;
      }),
      // eslint-disable-line brace-style
      saturationl: getset("hsl", 1, maxfn(100)),
      lightness: getset("hsl", 2, maxfn(100)),
      saturationv: getset("hsv", 1, maxfn(100)),
      value: getset("hsv", 2, maxfn(100)),
      chroma: getset("hcg", 1, maxfn(100)),
      gray: getset("hcg", 2, maxfn(100)),
      white: getset("hwb", 1, maxfn(100)),
      wblack: getset("hwb", 2, maxfn(100)),
      cyan: getset("cmyk", 0, maxfn(100)),
      magenta: getset("cmyk", 1, maxfn(100)),
      yellow: getset("cmyk", 2, maxfn(100)),
      black: getset("cmyk", 3, maxfn(100)),
      x: getset("xyz", 0, maxfn(100)),
      y: getset("xyz", 1, maxfn(100)),
      z: getset("xyz", 2, maxfn(100)),
      l: getset("lab", 0, maxfn(100)),
      a: getset("lab", 1),
      b: getset("lab", 2),
      keyword: function(val) {
        if (arguments.length) {
          return new Color2(val);
        }
        return convert[this.model].keyword(this.color);
      },
      hex: function(val) {
        if (arguments.length) {
          return new Color2(val);
        }
        return colorString.to.hex(this.rgb().round().color);
      },
      rgbNumber: function() {
        var rgb = this.rgb().color;
        return (rgb[0] & 255) << 16 | (rgb[1] & 255) << 8 | rgb[2] & 255;
      },
      luminosity: function() {
        var rgb = this.rgb().color;
        var lum = [];
        for (var i = 0; i < rgb.length; i++) {
          var chan = rgb[i] / 255;
          lum[i] = chan <= 0.03928 ? chan / 12.92 : Math.pow((chan + 0.055) / 1.055, 2.4);
        }
        return 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];
      },
      contrast: function(color2) {
        var lum1 = this.luminosity();
        var lum2 = color2.luminosity();
        if (lum1 > lum2) {
          return (lum1 + 0.05) / (lum2 + 0.05);
        }
        return (lum2 + 0.05) / (lum1 + 0.05);
      },
      level: function(color2) {
        var contrastRatio = this.contrast(color2);
        if (contrastRatio >= 7.1) {
          return "AAA";
        }
        return contrastRatio >= 4.5 ? "AA" : "";
      },
      isDark: function() {
        var rgb = this.rgb().color;
        var yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1e3;
        return yiq < 128;
      },
      isLight: function() {
        return !this.isDark();
      },
      negate: function() {
        var rgb = this.rgb();
        for (var i = 0; i < 3; i++) {
          rgb.color[i] = 255 - rgb.color[i];
        }
        return rgb;
      },
      lighten: function(ratio) {
        var hsl = this.hsl();
        hsl.color[2] += hsl.color[2] * ratio;
        return hsl;
      },
      darken: function(ratio) {
        var hsl = this.hsl();
        hsl.color[2] -= hsl.color[2] * ratio;
        return hsl;
      },
      saturate: function(ratio) {
        var hsl = this.hsl();
        hsl.color[1] += hsl.color[1] * ratio;
        return hsl;
      },
      desaturate: function(ratio) {
        var hsl = this.hsl();
        hsl.color[1] -= hsl.color[1] * ratio;
        return hsl;
      },
      whiten: function(ratio) {
        var hwb = this.hwb();
        hwb.color[1] += hwb.color[1] * ratio;
        return hwb;
      },
      blacken: function(ratio) {
        var hwb = this.hwb();
        hwb.color[2] += hwb.color[2] * ratio;
        return hwb;
      },
      grayscale: function() {
        var rgb = this.rgb().color;
        var val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;
        return Color2.rgb(val, val, val);
      },
      fade: function(ratio) {
        return this.alpha(this.valpha - this.valpha * ratio);
      },
      opaquer: function(ratio) {
        return this.alpha(this.valpha + this.valpha * ratio);
      },
      rotate: function(degrees) {
        var hsl = this.hsl();
        var hue = hsl.color[0];
        hue = (hue + degrees) % 360;
        hue = hue < 0 ? 360 + hue : hue;
        hsl.color[0] = hue;
        return hsl;
      },
      mix: function(mixinColor, weight) {
        if (!mixinColor || !mixinColor.rgb) {
          throw new Error('Argument to "mix" was not a Color instance, but rather an instance of ' + typeof mixinColor);
        }
        var color1 = mixinColor.rgb();
        var color2 = this.rgb();
        var p = weight === void 0 ? 0.5 : weight;
        var w = 2 * p - 1;
        var a = color1.alpha() - color2.alpha();
        var w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2;
        var w2 = 1 - w1;
        return Color2.rgb(
          w1 * color1.red() + w2 * color2.red(),
          w1 * color1.green() + w2 * color2.green(),
          w1 * color1.blue() + w2 * color2.blue(),
          color1.alpha() * p + color2.alpha() * (1 - p)
        );
      }
    };
    Object.keys(convert).forEach(function(model) {
      if (skippedModels.indexOf(model) !== -1) {
        return;
      }
      var channels = convert[model].channels;
      Color2.prototype[model] = function() {
        if (this.model === model) {
          return new Color2(this);
        }
        if (arguments.length) {
          return new Color2(arguments, model);
        }
        var newAlpha = typeof arguments[channels] === "number" ? channels : this.valpha;
        return new Color2(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);
      };
      Color2[model] = function(color) {
        if (typeof color === "number") {
          color = zeroArray(_slice.call(arguments), channels);
        }
        return new Color2(color, model);
      };
    });
    function roundTo(num, places) {
      return Number(num.toFixed(places));
    }
    function roundToPlace(places) {
      return function(num) {
        return roundTo(num, places);
      };
    }
    function getset(model, channel, modifier) {
      model = Array.isArray(model) ? model : [model];
      model.forEach(function(m) {
        (limiters[m] || (limiters[m] = []))[channel] = modifier;
      });
      model = model[0];
      return function(val) {
        var result;
        if (arguments.length) {
          if (modifier) {
            val = modifier(val);
          }
          result = this[model]();
          result.color[channel] = val;
          return result;
        }
        result = this[model]().color[channel];
        if (modifier) {
          result = modifier(result);
        }
        return result;
      };
    }
    function maxfn(max) {
      return function(v) {
        return Math.max(0, Math.min(max, v));
      };
    }
    function assertArray(val) {
      return Array.isArray(val) ? val : [val];
    }
    function zeroArray(arr, length) {
      for (var i = 0; i < length; i++) {
        if (typeof arr[i] !== "number") {
          arr[i] = 0;
        }
      }
      return arr;
    }
    module.exports = Color2;
  }
});

// node_modules/lodash.curry/index.js
var require_lodash = __commonJS({
  "node_modules/lodash.curry/index.js"(exports, module) {
    var FUNC_ERROR_TEXT = "Expected a function";
    var PLACEHOLDER = "__lodash_placeholder__";
    var BIND_FLAG = 1;
    var BIND_KEY_FLAG = 2;
    var CURRY_BOUND_FLAG = 4;
    var CURRY_FLAG = 8;
    var CURRY_RIGHT_FLAG = 16;
    var PARTIAL_FLAG = 32;
    var PARTIAL_RIGHT_FLAG = 64;
    var ARY_FLAG = 128;
    var REARG_FLAG = 256;
    var FLIP_FLAG = 512;
    var INFINITY = 1 / 0;
    var MAX_SAFE_INTEGER = 9007199254740991;
    var MAX_INTEGER = 17976931348623157e292;
    var NAN = 0 / 0;
    var wrapFlags = [
      ["ary", ARY_FLAG],
      ["bind", BIND_FLAG],
      ["bindKey", BIND_KEY_FLAG],
      ["curry", CURRY_FLAG],
      ["curryRight", CURRY_RIGHT_FLAG],
      ["flip", FLIP_FLAG],
      ["partial", PARTIAL_FLAG],
      ["partialRight", PARTIAL_RIGHT_FLAG],
      ["rearg", REARG_FLAG]
    ];
    var funcTag = "[object Function]";
    var genTag = "[object GeneratorFunction]";
    var symbolTag = "[object Symbol]";
    var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
    var reTrim = /^\s+|\s+$/g;
    var reWrapComment = /\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/;
    var reWrapDetails = /\{\n\/\* \[wrapped with (.+)\] \*/;
    var reSplitDetails = /,? & /;
    var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;
    var reIsBinary = /^0b[01]+$/i;
    var reIsHostCtor = /^\[object .+?Constructor\]$/;
    var reIsOctal = /^0o[0-7]+$/i;
    var reIsUint = /^(?:0|[1-9]\d*)$/;
    var freeParseInt = parseInt;
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    function apply(func, thisArg, args) {
      switch (args.length) {
        case 0:
          return func.call(thisArg);
        case 1:
          return func.call(thisArg, args[0]);
        case 2:
          return func.call(thisArg, args[0], args[1]);
        case 3:
          return func.call(thisArg, args[0], args[1], args[2]);
      }
      return func.apply(thisArg, args);
    }
    function arrayEach(array, iteratee) {
      var index = -1, length = array ? array.length : 0;
      while (++index < length) {
        if (iteratee(array[index], index, array) === false) {
          break;
        }
      }
      return array;
    }
    function arrayIncludes(array, value) {
      var length = array ? array.length : 0;
      return !!length && baseIndexOf(array, value, 0) > -1;
    }
    function baseFindIndex(array, predicate, fromIndex, fromRight) {
      var length = array.length, index = fromIndex + (fromRight ? 1 : -1);
      while (fromRight ? index-- : ++index < length) {
        if (predicate(array[index], index, array)) {
          return index;
        }
      }
      return -1;
    }
    function baseIndexOf(array, value, fromIndex) {
      if (value !== value) {
        return baseFindIndex(array, baseIsNaN, fromIndex);
      }
      var index = fromIndex - 1, length = array.length;
      while (++index < length) {
        if (array[index] === value) {
          return index;
        }
      }
      return -1;
    }
    function baseIsNaN(value) {
      return value !== value;
    }
    function countHolders(array, placeholder) {
      var length = array.length, result = 0;
      while (length--) {
        if (array[length] === placeholder) {
          result++;
        }
      }
      return result;
    }
    function getValue(object, key) {
      return object == null ? void 0 : object[key];
    }
    function isHostObject(value) {
      var result = false;
      if (value != null && typeof value.toString != "function") {
        try {
          result = !!(value + "");
        } catch (e) {
        }
      }
      return result;
    }
    function replaceHolders(array, placeholder) {
      var index = -1, length = array.length, resIndex = 0, result = [];
      while (++index < length) {
        var value = array[index];
        if (value === placeholder || value === PLACEHOLDER) {
          array[index] = PLACEHOLDER;
          result[resIndex++] = index;
        }
      }
      return result;
    }
    var funcProto = Function.prototype;
    var objectProto = Object.prototype;
    var coreJsData = root["__core-js_shared__"];
    var maskSrcKey = function() {
      var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || "");
      return uid ? "Symbol(src)_1." + uid : "";
    }();
    var funcToString = funcProto.toString;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var objectToString = objectProto.toString;
    var reIsNative = RegExp(
      "^" + funcToString.call(hasOwnProperty).replace(reRegExpChar, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
    );
    var objectCreate = Object.create;
    var nativeMax = Math.max;
    var nativeMin = Math.min;
    var defineProperty = function() {
      var func = getNative(Object, "defineProperty"), name = getNative.name;
      return name && name.length > 2 ? func : void 0;
    }();
    function baseCreate(proto) {
      return isObject(proto) ? objectCreate(proto) : {};
    }
    function baseIsNative(value) {
      if (!isObject(value) || isMasked(value)) {
        return false;
      }
      var pattern = isFunction(value) || isHostObject(value) ? reIsNative : reIsHostCtor;
      return pattern.test(toSource(value));
    }
    function composeArgs(args, partials, holders, isCurried) {
      var argsIndex = -1, argsLength = args.length, holdersLength = holders.length, leftIndex = -1, leftLength = partials.length, rangeLength = nativeMax(argsLength - holdersLength, 0), result = Array(leftLength + rangeLength), isUncurried = !isCurried;
      while (++leftIndex < leftLength) {
        result[leftIndex] = partials[leftIndex];
      }
      while (++argsIndex < holdersLength) {
        if (isUncurried || argsIndex < argsLength) {
          result[holders[argsIndex]] = args[argsIndex];
        }
      }
      while (rangeLength--) {
        result[leftIndex++] = args[argsIndex++];
      }
      return result;
    }
    function composeArgsRight(args, partials, holders, isCurried) {
      var argsIndex = -1, argsLength = args.length, holdersIndex = -1, holdersLength = holders.length, rightIndex = -1, rightLength = partials.length, rangeLength = nativeMax(argsLength - holdersLength, 0), result = Array(rangeLength + rightLength), isUncurried = !isCurried;
      while (++argsIndex < rangeLength) {
        result[argsIndex] = args[argsIndex];
      }
      var offset = argsIndex;
      while (++rightIndex < rightLength) {
        result[offset + rightIndex] = partials[rightIndex];
      }
      while (++holdersIndex < holdersLength) {
        if (isUncurried || argsIndex < argsLength) {
          result[offset + holders[holdersIndex]] = args[argsIndex++];
        }
      }
      return result;
    }
    function copyArray(source, array) {
      var index = -1, length = source.length;
      array || (array = Array(length));
      while (++index < length) {
        array[index] = source[index];
      }
      return array;
    }
    function createBind(func, bitmask, thisArg) {
      var isBind = bitmask & BIND_FLAG, Ctor = createCtor(func);
      function wrapper() {
        var fn = this && this !== root && this instanceof wrapper ? Ctor : func;
        return fn.apply(isBind ? thisArg : this, arguments);
      }
      return wrapper;
    }
    function createCtor(Ctor) {
      return function() {
        var args = arguments;
        switch (args.length) {
          case 0:
            return new Ctor();
          case 1:
            return new Ctor(args[0]);
          case 2:
            return new Ctor(args[0], args[1]);
          case 3:
            return new Ctor(args[0], args[1], args[2]);
          case 4:
            return new Ctor(args[0], args[1], args[2], args[3]);
          case 5:
            return new Ctor(args[0], args[1], args[2], args[3], args[4]);
          case 6:
            return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5]);
          case 7:
            return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5], args[6]);
        }
        var thisBinding = baseCreate(Ctor.prototype), result = Ctor.apply(thisBinding, args);
        return isObject(result) ? result : thisBinding;
      };
    }
    function createCurry(func, bitmask, arity) {
      var Ctor = createCtor(func);
      function wrapper() {
        var length = arguments.length, args = Array(length), index = length, placeholder = getHolder(wrapper);
        while (index--) {
          args[index] = arguments[index];
        }
        var holders = length < 3 && args[0] !== placeholder && args[length - 1] !== placeholder ? [] : replaceHolders(args, placeholder);
        length -= holders.length;
        if (length < arity) {
          return createRecurry(
            func,
            bitmask,
            createHybrid,
            wrapper.placeholder,
            void 0,
            args,
            holders,
            void 0,
            void 0,
            arity - length
          );
        }
        var fn = this && this !== root && this instanceof wrapper ? Ctor : func;
        return apply(fn, this, args);
      }
      return wrapper;
    }
    function createHybrid(func, bitmask, thisArg, partials, holders, partialsRight, holdersRight, argPos, ary, arity) {
      var isAry = bitmask & ARY_FLAG, isBind = bitmask & BIND_FLAG, isBindKey = bitmask & BIND_KEY_FLAG, isCurried = bitmask & (CURRY_FLAG | CURRY_RIGHT_FLAG), isFlip = bitmask & FLIP_FLAG, Ctor = isBindKey ? void 0 : createCtor(func);
      function wrapper() {
        var length = arguments.length, args = Array(length), index = length;
        while (index--) {
          args[index] = arguments[index];
        }
        if (isCurried) {
          var placeholder = getHolder(wrapper), holdersCount = countHolders(args, placeholder);
        }
        if (partials) {
          args = composeArgs(args, partials, holders, isCurried);
        }
        if (partialsRight) {
          args = composeArgsRight(args, partialsRight, holdersRight, isCurried);
        }
        length -= holdersCount;
        if (isCurried && length < arity) {
          var newHolders = replaceHolders(args, placeholder);
          return createRecurry(
            func,
            bitmask,
            createHybrid,
            wrapper.placeholder,
            thisArg,
            args,
            newHolders,
            argPos,
            ary,
            arity - length
          );
        }
        var thisBinding = isBind ? thisArg : this, fn = isBindKey ? thisBinding[func] : func;
        length = args.length;
        if (argPos) {
          args = reorder(args, argPos);
        } else if (isFlip && length > 1) {
          args.reverse();
        }
        if (isAry && ary < length) {
          args.length = ary;
        }
        if (this && this !== root && this instanceof wrapper) {
          fn = Ctor || createCtor(fn);
        }
        return fn.apply(thisBinding, args);
      }
      return wrapper;
    }
    function createPartial(func, bitmask, thisArg, partials) {
      var isBind = bitmask & BIND_FLAG, Ctor = createCtor(func);
      function wrapper() {
        var argsIndex = -1, argsLength = arguments.length, leftIndex = -1, leftLength = partials.length, args = Array(leftLength + argsLength), fn = this && this !== root && this instanceof wrapper ? Ctor : func;
        while (++leftIndex < leftLength) {
          args[leftIndex] = partials[leftIndex];
        }
        while (argsLength--) {
          args[leftIndex++] = arguments[++argsIndex];
        }
        return apply(fn, isBind ? thisArg : this, args);
      }
      return wrapper;
    }
    function createRecurry(func, bitmask, wrapFunc, placeholder, thisArg, partials, holders, argPos, ary, arity) {
      var isCurry = bitmask & CURRY_FLAG, newHolders = isCurry ? holders : void 0, newHoldersRight = isCurry ? void 0 : holders, newPartials = isCurry ? partials : void 0, newPartialsRight = isCurry ? void 0 : partials;
      bitmask |= isCurry ? PARTIAL_FLAG : PARTIAL_RIGHT_FLAG;
      bitmask &= ~(isCurry ? PARTIAL_RIGHT_FLAG : PARTIAL_FLAG);
      if (!(bitmask & CURRY_BOUND_FLAG)) {
        bitmask &= ~(BIND_FLAG | BIND_KEY_FLAG);
      }
      var result = wrapFunc(func, bitmask, thisArg, newPartials, newHolders, newPartialsRight, newHoldersRight, argPos, ary, arity);
      result.placeholder = placeholder;
      return setWrapToString(result, func, bitmask);
    }
    function createWrap(func, bitmask, thisArg, partials, holders, argPos, ary, arity) {
      var isBindKey = bitmask & BIND_KEY_FLAG;
      if (!isBindKey && typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      var length = partials ? partials.length : 0;
      if (!length) {
        bitmask &= ~(PARTIAL_FLAG | PARTIAL_RIGHT_FLAG);
        partials = holders = void 0;
      }
      ary = ary === void 0 ? ary : nativeMax(toInteger(ary), 0);
      arity = arity === void 0 ? arity : toInteger(arity);
      length -= holders ? holders.length : 0;
      if (bitmask & PARTIAL_RIGHT_FLAG) {
        var partialsRight = partials, holdersRight = holders;
        partials = holders = void 0;
      }
      var newData = [
        func,
        bitmask,
        thisArg,
        partials,
        holders,
        partialsRight,
        holdersRight,
        argPos,
        ary,
        arity
      ];
      func = newData[0];
      bitmask = newData[1];
      thisArg = newData[2];
      partials = newData[3];
      holders = newData[4];
      arity = newData[9] = newData[9] == null ? isBindKey ? 0 : func.length : nativeMax(newData[9] - length, 0);
      if (!arity && bitmask & (CURRY_FLAG | CURRY_RIGHT_FLAG)) {
        bitmask &= ~(CURRY_FLAG | CURRY_RIGHT_FLAG);
      }
      if (!bitmask || bitmask == BIND_FLAG) {
        var result = createBind(func, bitmask, thisArg);
      } else if (bitmask == CURRY_FLAG || bitmask == CURRY_RIGHT_FLAG) {
        result = createCurry(func, bitmask, arity);
      } else if ((bitmask == PARTIAL_FLAG || bitmask == (BIND_FLAG | PARTIAL_FLAG)) && !holders.length) {
        result = createPartial(func, bitmask, thisArg, partials);
      } else {
        result = createHybrid.apply(void 0, newData);
      }
      return setWrapToString(result, func, bitmask);
    }
    function getHolder(func) {
      var object = func;
      return object.placeholder;
    }
    function getNative(object, key) {
      var value = getValue(object, key);
      return baseIsNative(value) ? value : void 0;
    }
    function getWrapDetails(source) {
      var match = source.match(reWrapDetails);
      return match ? match[1].split(reSplitDetails) : [];
    }
    function insertWrapDetails(source, details) {
      var length = details.length, lastIndex = length - 1;
      details[lastIndex] = (length > 1 ? "& " : "") + details[lastIndex];
      details = details.join(length > 2 ? ", " : " ");
      return source.replace(reWrapComment, "{\n/* [wrapped with " + details + "] */\n");
    }
    function isIndex(value, length) {
      length = length == null ? MAX_SAFE_INTEGER : length;
      return !!length && (typeof value == "number" || reIsUint.test(value)) && (value > -1 && value % 1 == 0 && value < length);
    }
    function isMasked(func) {
      return !!maskSrcKey && maskSrcKey in func;
    }
    function reorder(array, indexes) {
      var arrLength = array.length, length = nativeMin(indexes.length, arrLength), oldArray = copyArray(array);
      while (length--) {
        var index = indexes[length];
        array[length] = isIndex(index, arrLength) ? oldArray[index] : void 0;
      }
      return array;
    }
    var setWrapToString = !defineProperty ? identity3 : function(wrapper, reference, bitmask) {
      var source = reference + "";
      return defineProperty(wrapper, "toString", {
        "configurable": true,
        "enumerable": false,
        "value": constant(insertWrapDetails(source, updateWrapDetails(getWrapDetails(source), bitmask)))
      });
    };
    function toSource(func) {
      if (func != null) {
        try {
          return funcToString.call(func);
        } catch (e) {
        }
        try {
          return func + "";
        } catch (e) {
        }
      }
      return "";
    }
    function updateWrapDetails(details, bitmask) {
      arrayEach(wrapFlags, function(pair) {
        var value = "_." + pair[0];
        if (bitmask & pair[1] && !arrayIncludes(details, value)) {
          details.push(value);
        }
      });
      return details.sort();
    }
    function curry2(func, arity, guard) {
      arity = guard ? void 0 : arity;
      var result = createWrap(func, CURRY_FLAG, void 0, void 0, void 0, void 0, void 0, arity);
      result.placeholder = curry2.placeholder;
      return result;
    }
    function isFunction(value) {
      var tag = isObject(value) ? objectToString.call(value) : "";
      return tag == funcTag || tag == genTag;
    }
    function isObject(value) {
      var type = typeof value;
      return !!value && (type == "object" || type == "function");
    }
    function isObjectLike(value) {
      return !!value && typeof value == "object";
    }
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && objectToString.call(value) == symbolTag;
    }
    function toFinite(value) {
      if (!value) {
        return value === 0 ? value : 0;
      }
      value = toNumber(value);
      if (value === INFINITY || value === -INFINITY) {
        var sign = value < 0 ? -1 : 1;
        return sign * MAX_INTEGER;
      }
      return value === value ? value : 0;
    }
    function toInteger(value) {
      var result = toFinite(value), remainder = result % 1;
      return result === result ? remainder ? result - remainder : result : 0;
    }
    function toNumber(value) {
      if (typeof value == "number") {
        return value;
      }
      if (isSymbol(value)) {
        return NAN;
      }
      if (isObject(value)) {
        var other = typeof value.valueOf == "function" ? value.valueOf() : value;
        value = isObject(other) ? other + "" : other;
      }
      if (typeof value != "string") {
        return value === 0 ? value : +value;
      }
      value = value.replace(reTrim, "");
      var isBinary = reIsBinary.test(value);
      return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;
    }
    function constant(value) {
      return function() {
        return value;
      };
    }
    function identity3(value) {
      return value;
    }
    curry2.placeholder = {};
    module.exports = curry2;
  }
});

// node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js
function _objectWithoutProperties(e, t) {
  if (null == e) return {};
  var o, r, i = _objectWithoutPropertiesLoose(e, t);
  if (Object.getOwnPropertySymbols) {
    var n = Object.getOwnPropertySymbols(e);
    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
  }
  return i;
}

// node_modules/@babel/runtime/helpers/esm/inherits.js
function _inherits(t, e) {
  if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function");
  t.prototype = Object.create(e && e.prototype, {
    constructor: {
      value: t,
      writable: true,
      configurable: true
    }
  }), Object.defineProperty(t, "prototype", {
    writable: false
  }), e && _setPrototypeOf(t, e);
}

// node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js
function _possibleConstructorReturn(t, e) {
  if (e && ("object" == _typeof(e) || "function" == typeof e)) return e;
  if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined");
  return _assertThisInitialized(t);
}

// node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js
function _getPrototypeOf(t) {
  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t2) {
    return t2.__proto__ || Object.getPrototypeOf(t2);
  }, _getPrototypeOf(t);
}

// node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js
function _arrayWithHoles(r) {
  if (Array.isArray(r)) return r;
}

// node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js
function _iterableToArrayLimit(r, l) {
  var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (null != t) {
    var e, n, i, u, a = [], f = true, o = false;
    try {
      if (i = (t = t.call(r)).next, 0 === l) {
        if (Object(t) !== t) return;
        f = false;
      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = true) ;
    } catch (r2) {
      o = true, n = r2;
    } finally {
      try {
        if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return;
      } finally {
        if (o) throw n;
      }
    }
    return a;
  }
}

// node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js
function _arrayLikeToArray(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}

// node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
  }
}

// node_modules/@babel/runtime/helpers/esm/nonIterableRest.js
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

// node_modules/@babel/runtime/helpers/esm/slicedToArray.js
function _slicedToArray(r, e) {
  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();
}

// node_modules/react-json-tree/lib/esm/index.js
var import_react9 = __toESM(require_react());
var import_prop_types8 = __toESM(require_prop_types());

// node_modules/react-json-tree/lib/esm/JSONNode.js
var import_react8 = __toESM(require_react());
var import_prop_types7 = __toESM(require_prop_types());

// node_modules/react-json-tree/lib/esm/objType.js
function objType(obj) {
  var type = Object.prototype.toString.call(obj).slice(8, -1);
  if (type === "Object" && typeof obj[Symbol.iterator] === "function") {
    return "Iterable";
  }
  if (type === "Custom" && obj.constructor !== Object && obj instanceof Object) {
    return "Object";
  }
  return type;
}

// node_modules/react-json-tree/lib/esm/JSONObjectNode.js
var import_react4 = __toESM(require_react());
var import_prop_types4 = __toESM(require_prop_types());

// node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js
function _arrayWithoutHoles(r) {
  if (Array.isArray(r)) return _arrayLikeToArray(r);
}

// node_modules/@babel/runtime/helpers/esm/iterableToArray.js
function _iterableToArray(r) {
  if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}

// node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

// node_modules/@babel/runtime/helpers/esm/toConsumableArray.js
function _toConsumableArray(r) {
  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();
}

// node_modules/react-json-tree/lib/esm/JSONNestedNode.js
var import_react3 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());

// node_modules/react-json-tree/lib/esm/JSONArrow.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var JSONArrow = function JSONArrow2(_ref) {
  var styling = _ref.styling, arrowStyle = _ref.arrowStyle, expanded = _ref.expanded, nodeType = _ref.nodeType, onClick = _ref.onClick;
  return import_react.default.createElement("div", _extends({}, styling("arrowContainer", arrowStyle), {
    onClick
  }), import_react.default.createElement("div", styling(["arrow", "arrowSign"], nodeType, expanded, arrowStyle), "▶", arrowStyle === "double" && import_react.default.createElement("div", styling(["arrowSign", "arrowSignInner"]), "▶")));
};
JSONArrow.propTypes = {
  styling: import_prop_types.default.func.isRequired,
  arrowStyle: import_prop_types.default.oneOf(["single", "double"]),
  expanded: import_prop_types.default.bool.isRequired,
  nodeType: import_prop_types.default.string.isRequired,
  onClick: import_prop_types.default.func.isRequired
};
JSONArrow.defaultProps = {
  arrowStyle: "single"
};
var JSONArrow_default = JSONArrow;

// node_modules/react-json-tree/lib/esm/getCollectionEntries.js
function _createForOfIteratorHelper(o, allowArrayLike) {
  var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
  if (!it) {
    if (Array.isArray(o) || (it = _unsupportedIterableToArray2(o)) || allowArrayLike && o && typeof o.length === "number") {
      if (it) o = it;
      var i = 0;
      var F = function F2() {
      };
      return { s: F, n: function n() {
        if (i >= o.length) return { done: true };
        return { done: false, value: o[i++] };
      }, e: function e(_e) {
        throw _e;
      }, f: F };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var normalCompletion = true, didErr = false, err;
  return { s: function s() {
    it = it.call(o);
  }, n: function n() {
    var step = it.next();
    normalCompletion = step.done;
    return step;
  }, e: function e(_e2) {
    didErr = true;
    err = _e2;
  }, f: function f() {
    try {
      if (!normalCompletion && it.return != null) it.return();
    } finally {
      if (didErr) throw err;
    }
  } };
}
function _unsupportedIterableToArray2(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray2(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray2(o, minLen);
}
function _arrayLikeToArray2(arr, len) {
  if (len == null || len > arr.length) len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) {
    arr2[i] = arr[i];
  }
  return arr2;
}
function getLength(type, collection) {
  if (type === "Object") {
    return Object.keys(collection).length;
  } else if (type === "Array") {
    return collection.length;
  }
  return Infinity;
}
function isIterableMap(collection) {
  return typeof collection.set === "function";
}
function getEntries(type, collection, sortObjectKeys) {
  var from = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;
  var to = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : Infinity;
  var res;
  if (type === "Object") {
    var keys = Object.getOwnPropertyNames(collection);
    if (sortObjectKeys) {
      keys.sort(sortObjectKeys === true ? void 0 : sortObjectKeys);
    }
    keys = keys.slice(from, to + 1);
    res = {
      entries: keys.map(function(key) {
        return {
          key,
          value: collection[key]
        };
      })
    };
  } else if (type === "Array") {
    res = {
      entries: collection.slice(from, to + 1).map(function(val, idx2) {
        return {
          key: idx2 + from,
          value: val
        };
      })
    };
  } else {
    var idx = 0;
    var entries = [];
    var done = true;
    var isMap = isIterableMap(collection);
    var _iterator = _createForOfIteratorHelper(collection), _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done; ) {
        var item = _step.value;
        if (idx > to) {
          done = false;
          break;
        }
        if (from <= idx) {
          if (isMap && Array.isArray(item)) {
            if (typeof item[0] === "string" || typeof item[0] === "number") {
              entries.push({
                key: item[0],
                value: item[1]
              });
            } else {
              entries.push({
                key: "[entry ".concat(idx, "]"),
                value: {
                  "[key]": item[0],
                  "[value]": item[1]
                }
              });
            }
          } else {
            entries.push({
              key: idx,
              value: item
            });
          }
        }
        idx++;
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    res = {
      hasMore: !done,
      entries
    };
  }
  return res;
}
function getRanges(from, to, limit) {
  var ranges = [];
  while (to - from > limit * limit) {
    limit = limit * limit;
  }
  for (var i = from; i <= to; i += limit) {
    ranges.push({
      from: i,
      to: Math.min(to, i + limit - 1)
    });
  }
  return ranges;
}
function getCollectionEntries(type, collection, sortObjectKeys, limit) {
  var from = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;
  var to = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : Infinity;
  var getEntriesBound = getEntries.bind(null, type, collection, sortObjectKeys);
  if (!limit) {
    return getEntriesBound().entries;
  }
  var isSubset = to < Infinity;
  var length = Math.min(to - from, getLength(type, collection));
  if (type !== "Iterable") {
    if (length <= limit || limit < 7) {
      return getEntriesBound(from, to).entries;
    }
  } else {
    if (length <= limit && !isSubset) {
      return getEntriesBound(from, to).entries;
    }
  }
  var limitedEntries;
  if (type === "Iterable") {
    var _getEntriesBound = getEntriesBound(from, from + limit - 1), hasMore = _getEntriesBound.hasMore, entries = _getEntriesBound.entries;
    limitedEntries = hasMore ? [].concat(_toConsumableArray(entries), _toConsumableArray(getRanges(from + limit, from + 2 * limit - 1, limit))) : entries;
  } else {
    limitedEntries = isSubset ? getRanges(from, to, limit) : [].concat(_toConsumableArray(getEntriesBound(0, limit - 5).entries), _toConsumableArray(getRanges(limit - 4, length - 5, limit)), _toConsumableArray(getEntriesBound(length - 4, length - 1).entries));
  }
  return limitedEntries;
}

// node_modules/react-json-tree/lib/esm/ItemRange.js
var import_react2 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());
function _createSuper(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived), result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
var ItemRange = function(_React$Component) {
  _inherits(ItemRange2, _React$Component);
  var _super = _createSuper(ItemRange2);
  function ItemRange2(props) {
    var _this;
    _classCallCheck(this, ItemRange2);
    _this = _super.call(this, props);
    _defineProperty(_assertThisInitialized(_this), "handleClick", function() {
      _this.setState({
        expanded: !_this.state.expanded
      });
    });
    _this.state = {
      expanded: false
    };
    return _this;
  }
  _createClass(ItemRange2, [{
    key: "render",
    value: function render() {
      var _this$props = this.props, styling = _this$props.styling, from = _this$props.from, to = _this$props.to, renderChildNodes2 = _this$props.renderChildNodes, nodeType = _this$props.nodeType;
      return this.state.expanded ? import_react2.default.createElement("div", styling("itemRange", this.state.expanded), renderChildNodes2(this.props, from, to)) : import_react2.default.createElement("div", _extends({}, styling("itemRange", this.state.expanded), {
        onClick: this.handleClick
      }), import_react2.default.createElement(JSONArrow_default, {
        nodeType,
        styling,
        expanded: false,
        onClick: this.handleClick,
        arrowStyle: "double"
      }), "".concat(from, " ... ").concat(to));
    }
  }]);
  return ItemRange2;
}(import_react2.default.Component);
_defineProperty(ItemRange, "propTypes", {
  styling: import_prop_types2.default.func.isRequired,
  from: import_prop_types2.default.number.isRequired,
  to: import_prop_types2.default.number.isRequired,
  renderChildNodes: import_prop_types2.default.func.isRequired,
  nodeType: import_prop_types2.default.string.isRequired
});

// node_modules/react-json-tree/lib/esm/JSONNestedNode.js
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _createSuper2(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct2();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived), result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
function _isNativeReflectConstruct2() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
function isRange(rangeOrEntry) {
  return rangeOrEntry.to !== void 0;
}
function renderChildNodes(props, from, to) {
  var nodeType = props.nodeType, data = props.data, collectionLimit = props.collectionLimit, circularCache = props.circularCache, keyPath = props.keyPath, postprocessValue = props.postprocessValue, sortObjectKeys = props.sortObjectKeys;
  var childNodes = [];
  getCollectionEntries(nodeType, data, sortObjectKeys, collectionLimit, from, to).forEach(function(entry) {
    if (isRange(entry)) {
      childNodes.push(import_react3.default.createElement(ItemRange, _extends({}, props, {
        key: "ItemRange--".concat(entry.from, "-").concat(entry.to),
        from: entry.from,
        to: entry.to,
        renderChildNodes
      })));
    } else {
      var key = entry.key, value = entry.value;
      var isCircular = circularCache.indexOf(value) !== -1;
      childNodes.push(import_react3.default.createElement(JSONNode_default, _extends({}, props, {
        postprocessValue,
        collectionLimit,
        key: "Node--".concat(key),
        keyPath: [key].concat(_toConsumableArray(keyPath)),
        value: postprocessValue(value),
        circularCache: [].concat(_toConsumableArray(circularCache), [value]),
        isCircular,
        hideRoot: false
      })));
    }
  });
  return childNodes;
}
function getStateFromProps(props) {
  var expanded = !props.isCircular ? props.shouldExpandNode(props.keyPath, props.data, props.level) : false;
  return {
    expanded
  };
}
var JSONNestedNode = function(_React$Component) {
  _inherits(JSONNestedNode2, _React$Component);
  var _super = _createSuper2(JSONNestedNode2);
  function JSONNestedNode2(props) {
    var _this;
    _classCallCheck(this, JSONNestedNode2);
    _this = _super.call(this, props);
    _defineProperty(_assertThisInitialized(_this), "handleClick", function() {
      if (_this.props.expandable) {
        _this.setState({
          expanded: !_this.state.expanded
        });
      }
    });
    _this.state = getStateFromProps(props);
    return _this;
  }
  _createClass(JSONNestedNode2, [{
    key: "UNSAFE_componentWillReceiveProps",
    value: function UNSAFE_componentWillReceiveProps(nextProps) {
      var nextState = getStateFromProps(nextProps);
      if (getStateFromProps(this.props).expanded !== nextState.expanded) {
        this.setState(nextState);
      }
    }
  }, {
    key: "shouldComponentUpdate",
    value: function shouldComponentUpdate(nextProps, nextState) {
      var _this2 = this;
      return !!Object.keys(nextProps).find(function(key) {
        return key !== "circularCache" && (key === "keyPath" ? nextProps[key].join("/") !== _this2.props[key].join("/") : nextProps[key] !== _this2.props[key]);
      }) || nextState.expanded !== this.state.expanded;
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props, getItemString = _this$props.getItemString, nodeTypeIndicator = _this$props.nodeTypeIndicator, nodeType = _this$props.nodeType, data = _this$props.data, hideRoot = _this$props.hideRoot, createItemString4 = _this$props.createItemString, styling = _this$props.styling, collectionLimit = _this$props.collectionLimit, keyPath = _this$props.keyPath, labelRenderer = _this$props.labelRenderer, expandable = _this$props.expandable;
      var expanded = this.state.expanded;
      var renderedChildren = expanded || hideRoot && this.props.level === 0 ? renderChildNodes(_objectSpread(_objectSpread({}, this.props), {}, {
        level: this.props.level + 1
      })) : null;
      var itemType = import_react3.default.createElement("span", styling("nestedNodeItemType", expanded), nodeTypeIndicator);
      var renderedItemString = getItemString(nodeType, data, itemType, createItemString4(data, collectionLimit), keyPath);
      var stylingArgs = [keyPath, nodeType, expanded, expandable];
      return hideRoot ? import_react3.default.createElement("li", styling.apply(void 0, ["rootNode"].concat(stylingArgs)), import_react3.default.createElement("ul", styling.apply(void 0, ["rootNodeChildren"].concat(stylingArgs)), renderedChildren)) : import_react3.default.createElement("li", styling.apply(void 0, ["nestedNode"].concat(stylingArgs)), expandable && import_react3.default.createElement(JSONArrow_default, {
        styling,
        nodeType,
        expanded,
        onClick: this.handleClick
      }), import_react3.default.createElement("label", _extends({}, styling.apply(void 0, [["label", "nestedNodeLabel"]].concat(stylingArgs)), {
        onClick: this.handleClick
      }), labelRenderer.apply(void 0, stylingArgs)), import_react3.default.createElement("span", _extends({}, styling.apply(void 0, ["nestedNodeItemString"].concat(stylingArgs)), {
        onClick: this.handleClick
      }), renderedItemString), import_react3.default.createElement("ul", styling.apply(void 0, ["nestedNodeChildren"].concat(stylingArgs)), renderedChildren));
    }
  }]);
  return JSONNestedNode2;
}(import_react3.default.Component);
_defineProperty(JSONNestedNode, "propTypes", {
  getItemString: import_prop_types3.default.func.isRequired,
  nodeTypeIndicator: import_prop_types3.default.any,
  nodeType: import_prop_types3.default.string.isRequired,
  data: import_prop_types3.default.any,
  hideRoot: import_prop_types3.default.bool.isRequired,
  createItemString: import_prop_types3.default.func.isRequired,
  styling: import_prop_types3.default.func.isRequired,
  collectionLimit: import_prop_types3.default.number,
  keyPath: import_prop_types3.default.arrayOf(import_prop_types3.default.oneOfType([import_prop_types3.default.string, import_prop_types3.default.number])).isRequired,
  labelRenderer: import_prop_types3.default.func.isRequired,
  shouldExpandNode: import_prop_types3.default.func,
  level: import_prop_types3.default.number.isRequired,
  sortObjectKeys: import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.bool]),
  isCircular: import_prop_types3.default.bool,
  expandable: import_prop_types3.default.bool
});
_defineProperty(JSONNestedNode, "defaultProps", {
  data: [],
  circularCache: [],
  level: 0,
  expandable: true
});

// node_modules/react-json-tree/lib/esm/JSONObjectNode.js
var _excluded = ["data"];
function createItemString(data) {
  var len = Object.getOwnPropertyNames(data).length;
  return "".concat(len, " ").concat(len !== 1 ? "keys" : "key");
}
var JSONObjectNode = function JSONObjectNode2(_ref) {
  var data = _ref.data, props = _objectWithoutProperties(_ref, _excluded);
  return import_react4.default.createElement(JSONNestedNode, _extends({}, props, {
    data,
    nodeType: "Object",
    nodeTypeIndicator: props.nodeType === "Error" ? "Error()" : "{}",
    createItemString,
    expandable: Object.getOwnPropertyNames(data).length > 0
  }));
};
JSONObjectNode.propTypes = {
  data: import_prop_types4.default.object,
  nodeType: import_prop_types4.default.string.isRequired
};
var JSONObjectNode_default = JSONObjectNode;

// node_modules/react-json-tree/lib/esm/JSONArrayNode.js
var import_react5 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());
var _excluded2 = ["data"];
function createItemString2(data) {
  return "".concat(data.length, " ").concat(data.length !== 1 ? "items" : "item");
}
var JSONArrayNode = function JSONArrayNode2(_ref) {
  var data = _ref.data, props = _objectWithoutProperties(_ref, _excluded2);
  return import_react5.default.createElement(JSONNestedNode, _extends({}, props, {
    data,
    nodeType: "Array",
    nodeTypeIndicator: "[]",
    createItemString: createItemString2,
    expandable: data.length > 0
  }));
};
JSONArrayNode.propTypes = {
  data: import_prop_types5.default.array
};
var JSONArrayNode_default = JSONArrayNode;

// node_modules/react-json-tree/lib/esm/JSONIterableNode.js
var import_react6 = __toESM(require_react());
function _createForOfIteratorHelper2(o, allowArrayLike) {
  var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
  if (!it) {
    if (Array.isArray(o) || (it = _unsupportedIterableToArray3(o)) || allowArrayLike && o && typeof o.length === "number") {
      if (it) o = it;
      var i = 0;
      var F = function F2() {
      };
      return { s: F, n: function n() {
        if (i >= o.length) return { done: true };
        return { done: false, value: o[i++] };
      }, e: function e(_e) {
        throw _e;
      }, f: F };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var normalCompletion = true, didErr = false, err;
  return { s: function s() {
    it = it.call(o);
  }, n: function n() {
    var step = it.next();
    normalCompletion = step.done;
    return step;
  }, e: function e(_e2) {
    didErr = true;
    err = _e2;
  }, f: function f() {
    try {
      if (!normalCompletion && it.return != null) it.return();
    } finally {
      if (didErr) throw err;
    }
  } };
}
function _unsupportedIterableToArray3(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray3(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray3(o, minLen);
}
function _arrayLikeToArray3(arr, len) {
  if (len == null || len > arr.length) len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) {
    arr2[i] = arr[i];
  }
  return arr2;
}
function createItemString3(data, limit) {
  var count = 0;
  var hasMore = false;
  if (Number.isSafeInteger(data.size)) {
    count = data.size;
  } else {
    var _iterator = _createForOfIteratorHelper2(data), _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done; ) {
        var entry = _step.value;
        if (limit && count + 1 > limit) {
          hasMore = true;
          break;
        }
        count += 1;
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
  }
  return "".concat(hasMore ? ">" : "").concat(count, " ").concat(count !== 1 ? "entries" : "entry");
}
var JSONIterableNode = function JSONIterableNode2(_ref) {
  var props = _extends({}, _ref);
  return import_react6.default.createElement(JSONNestedNode, _extends({}, props, {
    nodeType: "Iterable",
    nodeTypeIndicator: "()",
    createItemString: createItemString3
  }));
};
var JSONIterableNode_default = JSONIterableNode;

// node_modules/react-json-tree/lib/esm/JSONValueNode.js
var import_react7 = __toESM(require_react());
var import_prop_types6 = __toESM(require_prop_types());
var JSONValueNode = function JSONValueNode2(_ref) {
  var nodeType = _ref.nodeType, styling = _ref.styling, labelRenderer = _ref.labelRenderer, keyPath = _ref.keyPath, valueRenderer = _ref.valueRenderer, value = _ref.value, _ref$valueGetter = _ref.valueGetter, valueGetter = _ref$valueGetter === void 0 ? function(value2) {
    return value2;
  } : _ref$valueGetter;
  return import_react7.default.createElement("li", styling("value", nodeType, keyPath), import_react7.default.createElement("label", styling(["label", "valueLabel"], nodeType, keyPath), labelRenderer(keyPath, nodeType, false, false)), import_react7.default.createElement("span", styling("valueText", nodeType, keyPath), valueRenderer.apply(void 0, [valueGetter(value), value].concat(_toConsumableArray(keyPath)))));
};
JSONValueNode.propTypes = {
  nodeType: import_prop_types6.default.string.isRequired,
  styling: import_prop_types6.default.func.isRequired,
  labelRenderer: import_prop_types6.default.func.isRequired,
  keyPath: import_prop_types6.default.arrayOf(import_prop_types6.default.oneOfType([import_prop_types6.default.string, import_prop_types6.default.number]).isRequired).isRequired,
  valueRenderer: import_prop_types6.default.func.isRequired,
  value: import_prop_types6.default.any,
  valueGetter: import_prop_types6.default.func
};
var JSONValueNode_default = JSONValueNode;

// node_modules/react-json-tree/lib/esm/JSONNode.js
var _excluded3 = ["getItemString", "keyPath", "labelRenderer", "styling", "value", "valueRenderer", "isCustomNode"];
function ownKeys2(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys2(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys2(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
var JSONNode = function JSONNode2(_ref) {
  var getItemString = _ref.getItemString, keyPath = _ref.keyPath, labelRenderer = _ref.labelRenderer, styling = _ref.styling, value = _ref.value, valueRenderer = _ref.valueRenderer, isCustomNode = _ref.isCustomNode, rest = _objectWithoutProperties(_ref, _excluded3);
  var nodeType = isCustomNode(value) ? "Custom" : objType(value);
  var simpleNodeProps = {
    getItemString,
    key: keyPath[0],
    keyPath,
    labelRenderer,
    nodeType,
    styling,
    value,
    valueRenderer
  };
  var nestedNodeProps = _objectSpread2(_objectSpread2(_objectSpread2({}, rest), simpleNodeProps), {}, {
    data: value,
    isCustomNode
  });
  switch (nodeType) {
    case "Object":
    case "Error":
    case "WeakMap":
    case "WeakSet":
      return import_react8.default.createElement(JSONObjectNode_default, nestedNodeProps);
    case "Array":
      return import_react8.default.createElement(JSONArrayNode_default, nestedNodeProps);
    case "Iterable":
    case "Map":
    case "Set":
      return import_react8.default.createElement(JSONIterableNode_default, nestedNodeProps);
    case "String":
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter(raw) {
          return '"'.concat(raw, '"');
        }
      }));
    case "Number":
      return import_react8.default.createElement(JSONValueNode_default, simpleNodeProps);
    case "Boolean":
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter(raw) {
          return raw ? "true" : "false";
        }
      }));
    case "Date":
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter(raw) {
          return raw.toISOString();
        }
      }));
    case "Null":
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter() {
          return "null";
        }
      }));
    case "Undefined":
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter() {
          return "undefined";
        }
      }));
    case "Function":
    case "Symbol":
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter(raw) {
          return raw.toString();
        }
      }));
    case "Custom":
      return import_react8.default.createElement(JSONValueNode_default, simpleNodeProps);
    default:
      return import_react8.default.createElement(JSONValueNode_default, _extends({}, simpleNodeProps, {
        valueGetter: function valueGetter() {
          return "<".concat(nodeType, ">");
        }
      }));
  }
};
JSONNode.propTypes = {
  getItemString: import_prop_types7.default.func.isRequired,
  keyPath: import_prop_types7.default.arrayOf(import_prop_types7.default.oneOfType([import_prop_types7.default.string, import_prop_types7.default.number]).isRequired).isRequired,
  labelRenderer: import_prop_types7.default.func.isRequired,
  styling: import_prop_types7.default.func.isRequired,
  value: import_prop_types7.default.any,
  valueRenderer: import_prop_types7.default.func.isRequired,
  isCustomNode: import_prop_types7.default.func.isRequired
};
var JSONNode_default = JSONNode;

// node_modules/base16/src/index.js
var src_exports = {};
__export(src_exports, {
  apathy: () => apathy_default,
  ashes: () => ashes_default,
  atelierDune: () => atelier_dune_default,
  atelierForest: () => atelier_forest_default,
  atelierHeath: () => atelier_heath_default,
  atelierLakeside: () => atelier_lakeside_default,
  atelierSeaside: () => atelier_seaside_default,
  bespin: () => bespin_default,
  brewer: () => brewer_default,
  bright: () => bright_default,
  chalk: () => chalk_default,
  codeschool: () => codeschool_default,
  colors: () => colors_default,
  default: () => default_default,
  eighties: () => eighties_default,
  embers: () => embers_default,
  flat: () => flat_default,
  google: () => google_default,
  grayscale: () => grayscale_default,
  greenscreen: () => greenscreen_default,
  harmonic: () => harmonic_default,
  hopscotch: () => hopscotch_default,
  isotope: () => isotope_default,
  marrakesh: () => marrakesh_default,
  mocha: () => mocha_default,
  monokai: () => monokai_default,
  ocean: () => ocean_default,
  paraiso: () => paraiso_default,
  pop: () => pop_default,
  railscasts: () => railscasts_default,
  shapeshifter: () => shapeshifter_default,
  solarized: () => solarized_default,
  summerfruit: () => summerfruit_default,
  threezerotwofour: () => threezerotwofour_default,
  tomorrow: () => tomorrow_default,
  tube: () => tube_default,
  twilight: () => twilight_default
});

// node_modules/base16/src/threezerotwofour.js
var threezerotwofour_default = {
  scheme: "threezerotwofour",
  author: "jan t. sott (http://github.com/idleberg)",
  base00: "#090300",
  base01: "#3a3432",
  base02: "#4a4543",
  base03: "#5c5855",
  base04: "#807d7c",
  base05: "#a5a2a2",
  base06: "#d6d5d4",
  base07: "#f7f7f7",
  base08: "#db2d20",
  base09: "#e8bbd0",
  base0A: "#fded02",
  base0B: "#01a252",
  base0C: "#b5e4f4",
  base0D: "#01a0e4",
  base0E: "#a16a94",
  base0F: "#cdab53"
};

// node_modules/base16/src/apathy.js
var apathy_default = {
  scheme: "apathy",
  author: "jannik siebert (https://github.com/janniks)",
  base00: "#031A16",
  base01: "#0B342D",
  base02: "#184E45",
  base03: "#2B685E",
  base04: "#5F9C92",
  base05: "#81B5AC",
  base06: "#A7CEC8",
  base07: "#D2E7E4",
  base08: "#3E9688",
  base09: "#3E7996",
  base0A: "#3E4C96",
  base0B: "#883E96",
  base0C: "#963E4C",
  base0D: "#96883E",
  base0E: "#4C963E",
  base0F: "#3E965B"
};

// node_modules/base16/src/ashes.js
var ashes_default = {
  scheme: "ashes",
  author: "jannik siebert (https://github.com/janniks)",
  base00: "#1C2023",
  base01: "#393F45",
  base02: "#565E65",
  base03: "#747C84",
  base04: "#ADB3BA",
  base05: "#C7CCD1",
  base06: "#DFE2E5",
  base07: "#F3F4F5",
  base08: "#C7AE95",
  base09: "#C7C795",
  base0A: "#AEC795",
  base0B: "#95C7AE",
  base0C: "#95AEC7",
  base0D: "#AE95C7",
  base0E: "#C795AE",
  base0F: "#C79595"
};

// node_modules/base16/src/atelier-dune.js
var atelier_dune_default = {
  scheme: "atelier dune",
  author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/dune)",
  base00: "#20201d",
  base01: "#292824",
  base02: "#6e6b5e",
  base03: "#7d7a68",
  base04: "#999580",
  base05: "#a6a28c",
  base06: "#e8e4cf",
  base07: "#fefbec",
  base08: "#d73737",
  base09: "#b65611",
  base0A: "#cfb017",
  base0B: "#60ac39",
  base0C: "#1fad83",
  base0D: "#6684e1",
  base0E: "#b854d4",
  base0F: "#d43552"
};

// node_modules/base16/src/atelier-forest.js
var atelier_forest_default = {
  scheme: "atelier forest",
  author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/forest)",
  base00: "#1b1918",
  base01: "#2c2421",
  base02: "#68615e",
  base03: "#766e6b",
  base04: "#9c9491",
  base05: "#a8a19f",
  base06: "#e6e2e0",
  base07: "#f1efee",
  base08: "#f22c40",
  base09: "#df5320",
  base0A: "#d5911a",
  base0B: "#5ab738",
  base0C: "#00ad9c",
  base0D: "#407ee7",
  base0E: "#6666ea",
  base0F: "#c33ff3"
};

// node_modules/base16/src/atelier-heath.js
var atelier_heath_default = {
  scheme: "atelier heath",
  author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/heath)",
  base00: "#1b181b",
  base01: "#292329",
  base02: "#695d69",
  base03: "#776977",
  base04: "#9e8f9e",
  base05: "#ab9bab",
  base06: "#d8cad8",
  base07: "#f7f3f7",
  base08: "#ca402b",
  base09: "#a65926",
  base0A: "#bb8a35",
  base0B: "#379a37",
  base0C: "#159393",
  base0D: "#516aec",
  base0E: "#7b59c0",
  base0F: "#cc33cc"
};

// node_modules/base16/src/atelier-lakeside.js
var atelier_lakeside_default = {
  scheme: "atelier lakeside",
  author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/lakeside/)",
  base00: "#161b1d",
  base01: "#1f292e",
  base02: "#516d7b",
  base03: "#5a7b8c",
  base04: "#7195a8",
  base05: "#7ea2b4",
  base06: "#c1e4f6",
  base07: "#ebf8ff",
  base08: "#d22d72",
  base09: "#935c25",
  base0A: "#8a8a0f",
  base0B: "#568c3b",
  base0C: "#2d8f6f",
  base0D: "#257fad",
  base0E: "#5d5db1",
  base0F: "#b72dd2"
};

// node_modules/base16/src/atelier-seaside.js
var atelier_seaside_default = {
  scheme: "atelier seaside",
  author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/seaside/)",
  base00: "#131513",
  base01: "#242924",
  base02: "#5e6e5e",
  base03: "#687d68",
  base04: "#809980",
  base05: "#8ca68c",
  base06: "#cfe8cf",
  base07: "#f0fff0",
  base08: "#e6193c",
  base09: "#87711d",
  base0A: "#c3c322",
  base0B: "#29a329",
  base0C: "#1999b3",
  base0D: "#3d62f5",
  base0E: "#ad2bee",
  base0F: "#e619c3"
};

// node_modules/base16/src/bespin.js
var bespin_default = {
  scheme: "bespin",
  author: "jan t. sott",
  base00: "#28211c",
  base01: "#36312e",
  base02: "#5e5d5c",
  base03: "#666666",
  base04: "#797977",
  base05: "#8a8986",
  base06: "#9d9b97",
  base07: "#baae9e",
  base08: "#cf6a4c",
  base09: "#cf7d34",
  base0A: "#f9ee98",
  base0B: "#54be0d",
  base0C: "#afc4db",
  base0D: "#5ea6ea",
  base0E: "#9b859d",
  base0F: "#937121"
};

// node_modules/base16/src/brewer.js
var brewer_default = {
  scheme: "brewer",
  author: "timothée poisot (http://github.com/tpoisot)",
  base00: "#0c0d0e",
  base01: "#2e2f30",
  base02: "#515253",
  base03: "#737475",
  base04: "#959697",
  base05: "#b7b8b9",
  base06: "#dadbdc",
  base07: "#fcfdfe",
  base08: "#e31a1c",
  base09: "#e6550d",
  base0A: "#dca060",
  base0B: "#31a354",
  base0C: "#80b1d3",
  base0D: "#3182bd",
  base0E: "#756bb1",
  base0F: "#b15928"
};

// node_modules/base16/src/bright.js
var bright_default = {
  scheme: "bright",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#000000",
  base01: "#303030",
  base02: "#505050",
  base03: "#b0b0b0",
  base04: "#d0d0d0",
  base05: "#e0e0e0",
  base06: "#f5f5f5",
  base07: "#ffffff",
  base08: "#fb0120",
  base09: "#fc6d24",
  base0A: "#fda331",
  base0B: "#a1c659",
  base0C: "#76c7b7",
  base0D: "#6fb3d2",
  base0E: "#d381c3",
  base0F: "#be643c"
};

// node_modules/base16/src/chalk.js
var chalk_default = {
  scheme: "chalk",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#151515",
  base01: "#202020",
  base02: "#303030",
  base03: "#505050",
  base04: "#b0b0b0",
  base05: "#d0d0d0",
  base06: "#e0e0e0",
  base07: "#f5f5f5",
  base08: "#fb9fb1",
  base09: "#eda987",
  base0A: "#ddb26f",
  base0B: "#acc267",
  base0C: "#12cfc0",
  base0D: "#6fc2ef",
  base0E: "#e1a3ee",
  base0F: "#deaf8f"
};

// node_modules/base16/src/codeschool.js
var codeschool_default = {
  scheme: "codeschool",
  author: "brettof86",
  base00: "#232c31",
  base01: "#1c3657",
  base02: "#2a343a",
  base03: "#3f4944",
  base04: "#84898c",
  base05: "#9ea7a6",
  base06: "#a7cfa3",
  base07: "#b5d8f6",
  base08: "#2a5491",
  base09: "#43820d",
  base0A: "#a03b1e",
  base0B: "#237986",
  base0C: "#b02f30",
  base0D: "#484d79",
  base0E: "#c59820",
  base0F: "#c98344"
};

// node_modules/base16/src/colors.js
var colors_default = {
  scheme: "colors",
  author: "mrmrs (http://clrs.cc)",
  base00: "#111111",
  base01: "#333333",
  base02: "#555555",
  base03: "#777777",
  base04: "#999999",
  base05: "#bbbbbb",
  base06: "#dddddd",
  base07: "#ffffff",
  base08: "#ff4136",
  base09: "#ff851b",
  base0A: "#ffdc00",
  base0B: "#2ecc40",
  base0C: "#7fdbff",
  base0D: "#0074d9",
  base0E: "#b10dc9",
  base0F: "#85144b"
};

// node_modules/base16/src/default.js
var default_default = {
  scheme: "default",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#181818",
  base01: "#282828",
  base02: "#383838",
  base03: "#585858",
  base04: "#b8b8b8",
  base05: "#d8d8d8",
  base06: "#e8e8e8",
  base07: "#f8f8f8",
  base08: "#ab4642",
  base09: "#dc9656",
  base0A: "#f7ca88",
  base0B: "#a1b56c",
  base0C: "#86c1b9",
  base0D: "#7cafc2",
  base0E: "#ba8baf",
  base0F: "#a16946"
};

// node_modules/base16/src/eighties.js
var eighties_default = {
  scheme: "eighties",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#2d2d2d",
  base01: "#393939",
  base02: "#515151",
  base03: "#747369",
  base04: "#a09f93",
  base05: "#d3d0c8",
  base06: "#e8e6df",
  base07: "#f2f0ec",
  base08: "#f2777a",
  base09: "#f99157",
  base0A: "#ffcc66",
  base0B: "#99cc99",
  base0C: "#66cccc",
  base0D: "#6699cc",
  base0E: "#cc99cc",
  base0F: "#d27b53"
};

// node_modules/base16/src/embers.js
var embers_default = {
  scheme: "embers",
  author: "jannik siebert (https://github.com/janniks)",
  base00: "#16130F",
  base01: "#2C2620",
  base02: "#433B32",
  base03: "#5A5047",
  base04: "#8A8075",
  base05: "#A39A90",
  base06: "#BEB6AE",
  base07: "#DBD6D1",
  base08: "#826D57",
  base09: "#828257",
  base0A: "#6D8257",
  base0B: "#57826D",
  base0C: "#576D82",
  base0D: "#6D5782",
  base0E: "#82576D",
  base0F: "#825757"
};

// node_modules/base16/src/flat.js
var flat_default = {
  scheme: "flat",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#2C3E50",
  base01: "#34495E",
  base02: "#7F8C8D",
  base03: "#95A5A6",
  base04: "#BDC3C7",
  base05: "#e0e0e0",
  base06: "#f5f5f5",
  base07: "#ECF0F1",
  base08: "#E74C3C",
  base09: "#E67E22",
  base0A: "#F1C40F",
  base0B: "#2ECC71",
  base0C: "#1ABC9C",
  base0D: "#3498DB",
  base0E: "#9B59B6",
  base0F: "#be643c"
};

// node_modules/base16/src/google.js
var google_default = {
  scheme: "google",
  author: "seth wright (http://sethawright.com)",
  base00: "#1d1f21",
  base01: "#282a2e",
  base02: "#373b41",
  base03: "#969896",
  base04: "#b4b7b4",
  base05: "#c5c8c6",
  base06: "#e0e0e0",
  base07: "#ffffff",
  base08: "#CC342B",
  base09: "#F96A38",
  base0A: "#FBA922",
  base0B: "#198844",
  base0C: "#3971ED",
  base0D: "#3971ED",
  base0E: "#A36AC7",
  base0F: "#3971ED"
};

// node_modules/base16/src/grayscale.js
var grayscale_default = {
  scheme: "grayscale",
  author: "alexandre gavioli (https://github.com/alexx2/)",
  base00: "#101010",
  base01: "#252525",
  base02: "#464646",
  base03: "#525252",
  base04: "#ababab",
  base05: "#b9b9b9",
  base06: "#e3e3e3",
  base07: "#f7f7f7",
  base08: "#7c7c7c",
  base09: "#999999",
  base0A: "#a0a0a0",
  base0B: "#8e8e8e",
  base0C: "#868686",
  base0D: "#686868",
  base0E: "#747474",
  base0F: "#5e5e5e"
};

// node_modules/base16/src/greenscreen.js
var greenscreen_default = {
  scheme: "green screen",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#001100",
  base01: "#003300",
  base02: "#005500",
  base03: "#007700",
  base04: "#009900",
  base05: "#00bb00",
  base06: "#00dd00",
  base07: "#00ff00",
  base08: "#007700",
  base09: "#009900",
  base0A: "#007700",
  base0B: "#00bb00",
  base0C: "#005500",
  base0D: "#009900",
  base0E: "#00bb00",
  base0F: "#005500"
};

// node_modules/base16/src/harmonic.js
var harmonic_default = {
  scheme: "harmonic16",
  author: "jannik siebert (https://github.com/janniks)",
  base00: "#0b1c2c",
  base01: "#223b54",
  base02: "#405c79",
  base03: "#627e99",
  base04: "#aabcce",
  base05: "#cbd6e2",
  base06: "#e5ebf1",
  base07: "#f7f9fb",
  base08: "#bf8b56",
  base09: "#bfbf56",
  base0A: "#8bbf56",
  base0B: "#56bf8b",
  base0C: "#568bbf",
  base0D: "#8b56bf",
  base0E: "#bf568b",
  base0F: "#bf5656"
};

// node_modules/base16/src/hopscotch.js
var hopscotch_default = {
  scheme: "hopscotch",
  author: "jan t. sott",
  base00: "#322931",
  base01: "#433b42",
  base02: "#5c545b",
  base03: "#797379",
  base04: "#989498",
  base05: "#b9b5b8",
  base06: "#d5d3d5",
  base07: "#ffffff",
  base08: "#dd464c",
  base09: "#fd8b19",
  base0A: "#fdcc59",
  base0B: "#8fc13e",
  base0C: "#149b93",
  base0D: "#1290bf",
  base0E: "#c85e7c",
  base0F: "#b33508"
};

// node_modules/base16/src/isotope.js
var isotope_default = {
  scheme: "isotope",
  author: "jan t. sott",
  base00: "#000000",
  base01: "#404040",
  base02: "#606060",
  base03: "#808080",
  base04: "#c0c0c0",
  base05: "#d0d0d0",
  base06: "#e0e0e0",
  base07: "#ffffff",
  base08: "#ff0000",
  base09: "#ff9900",
  base0A: "#ff0099",
  base0B: "#33ff00",
  base0C: "#00ffff",
  base0D: "#0066ff",
  base0E: "#cc00ff",
  base0F: "#3300ff"
};

// node_modules/base16/src/marrakesh.js
var marrakesh_default = {
  scheme: "marrakesh",
  author: "alexandre gavioli (http://github.com/alexx2/)",
  base00: "#201602",
  base01: "#302e00",
  base02: "#5f5b17",
  base03: "#6c6823",
  base04: "#86813b",
  base05: "#948e48",
  base06: "#ccc37a",
  base07: "#faf0a5",
  base08: "#c35359",
  base09: "#b36144",
  base0A: "#a88339",
  base0B: "#18974e",
  base0C: "#75a738",
  base0D: "#477ca1",
  base0E: "#8868b3",
  base0F: "#b3588e"
};

// node_modules/base16/src/mocha.js
var mocha_default = {
  scheme: "mocha",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#3B3228",
  base01: "#534636",
  base02: "#645240",
  base03: "#7e705a",
  base04: "#b8afad",
  base05: "#d0c8c6",
  base06: "#e9e1dd",
  base07: "#f5eeeb",
  base08: "#cb6077",
  base09: "#d28b71",
  base0A: "#f4bc87",
  base0B: "#beb55b",
  base0C: "#7bbda4",
  base0D: "#8ab3b5",
  base0E: "#a89bb9",
  base0F: "#bb9584"
};

// node_modules/base16/src/monokai.js
var monokai_default = {
  scheme: "monokai",
  author: "wimer hazenberg (http://www.monokai.nl)",
  base00: "#272822",
  base01: "#383830",
  base02: "#49483e",
  base03: "#75715e",
  base04: "#a59f85",
  base05: "#f8f8f2",
  base06: "#f5f4f1",
  base07: "#f9f8f5",
  base08: "#f92672",
  base09: "#fd971f",
  base0A: "#f4bf75",
  base0B: "#a6e22e",
  base0C: "#a1efe4",
  base0D: "#66d9ef",
  base0E: "#ae81ff",
  base0F: "#cc6633"
};

// node_modules/base16/src/ocean.js
var ocean_default = {
  scheme: "ocean",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#2b303b",
  base01: "#343d46",
  base02: "#4f5b66",
  base03: "#65737e",
  base04: "#a7adba",
  base05: "#c0c5ce",
  base06: "#dfe1e8",
  base07: "#eff1f5",
  base08: "#bf616a",
  base09: "#d08770",
  base0A: "#ebcb8b",
  base0B: "#a3be8c",
  base0C: "#96b5b4",
  base0D: "#8fa1b3",
  base0E: "#b48ead",
  base0F: "#ab7967"
};

// node_modules/base16/src/paraiso.js
var paraiso_default = {
  scheme: "paraiso",
  author: "jan t. sott",
  base00: "#2f1e2e",
  base01: "#41323f",
  base02: "#4f424c",
  base03: "#776e71",
  base04: "#8d8687",
  base05: "#a39e9b",
  base06: "#b9b6b0",
  base07: "#e7e9db",
  base08: "#ef6155",
  base09: "#f99b15",
  base0A: "#fec418",
  base0B: "#48b685",
  base0C: "#5bc4bf",
  base0D: "#06b6ef",
  base0E: "#815ba4",
  base0F: "#e96ba8"
};

// node_modules/base16/src/pop.js
var pop_default = {
  scheme: "pop",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#000000",
  base01: "#202020",
  base02: "#303030",
  base03: "#505050",
  base04: "#b0b0b0",
  base05: "#d0d0d0",
  base06: "#e0e0e0",
  base07: "#ffffff",
  base08: "#eb008a",
  base09: "#f29333",
  base0A: "#f8ca12",
  base0B: "#37b349",
  base0C: "#00aabb",
  base0D: "#0e5a94",
  base0E: "#b31e8d",
  base0F: "#7a2d00"
};

// node_modules/base16/src/railscasts.js
var railscasts_default = {
  scheme: "railscasts",
  author: "ryan bates (http://railscasts.com)",
  base00: "#2b2b2b",
  base01: "#272935",
  base02: "#3a4055",
  base03: "#5a647e",
  base04: "#d4cfc9",
  base05: "#e6e1dc",
  base06: "#f4f1ed",
  base07: "#f9f7f3",
  base08: "#da4939",
  base09: "#cc7833",
  base0A: "#ffc66d",
  base0B: "#a5c261",
  base0C: "#519f50",
  base0D: "#6d9cbe",
  base0E: "#b6b3eb",
  base0F: "#bc9458"
};

// node_modules/base16/src/shapeshifter.js
var shapeshifter_default = {
  scheme: "shapeshifter",
  author: "tyler benziger (http://tybenz.com)",
  base00: "#000000",
  base01: "#040404",
  base02: "#102015",
  base03: "#343434",
  base04: "#555555",
  base05: "#ababab",
  base06: "#e0e0e0",
  base07: "#f9f9f9",
  base08: "#e92f2f",
  base09: "#e09448",
  base0A: "#dddd13",
  base0B: "#0ed839",
  base0C: "#23edda",
  base0D: "#3b48e3",
  base0E: "#f996e2",
  base0F: "#69542d"
};

// node_modules/base16/src/solarized.js
var solarized_default = {
  scheme: "solarized",
  author: "ethan schoonover (http://ethanschoonover.com/solarized)",
  base00: "#002b36",
  base01: "#073642",
  base02: "#586e75",
  base03: "#657b83",
  base04: "#839496",
  base05: "#93a1a1",
  base06: "#eee8d5",
  base07: "#fdf6e3",
  base08: "#dc322f",
  base09: "#cb4b16",
  base0A: "#b58900",
  base0B: "#859900",
  base0C: "#2aa198",
  base0D: "#268bd2",
  base0E: "#6c71c4",
  base0F: "#d33682"
};

// node_modules/base16/src/summerfruit.js
var summerfruit_default = {
  scheme: "summerfruit",
  author: "christopher corley (http://cscorley.github.io/)",
  base00: "#151515",
  base01: "#202020",
  base02: "#303030",
  base03: "#505050",
  base04: "#B0B0B0",
  base05: "#D0D0D0",
  base06: "#E0E0E0",
  base07: "#FFFFFF",
  base08: "#FF0086",
  base09: "#FD8900",
  base0A: "#ABA800",
  base0B: "#00C918",
  base0C: "#1faaaa",
  base0D: "#3777E6",
  base0E: "#AD00A1",
  base0F: "#cc6633"
};

// node_modules/base16/src/tomorrow.js
var tomorrow_default = {
  scheme: "tomorrow",
  author: "chris kempson (http://chriskempson.com)",
  base00: "#1d1f21",
  base01: "#282a2e",
  base02: "#373b41",
  base03: "#969896",
  base04: "#b4b7b4",
  base05: "#c5c8c6",
  base06: "#e0e0e0",
  base07: "#ffffff",
  base08: "#cc6666",
  base09: "#de935f",
  base0A: "#f0c674",
  base0B: "#b5bd68",
  base0C: "#8abeb7",
  base0D: "#81a2be",
  base0E: "#b294bb",
  base0F: "#a3685a"
};

// node_modules/base16/src/tube.js
var tube_default = {
  scheme: "london tube",
  author: "jan t. sott",
  base00: "#231f20",
  base01: "#1c3f95",
  base02: "#5a5758",
  base03: "#737171",
  base04: "#959ca1",
  base05: "#d9d8d8",
  base06: "#e7e7e8",
  base07: "#ffffff",
  base08: "#ee2e24",
  base09: "#f386a1",
  base0A: "#ffd204",
  base0B: "#00853e",
  base0C: "#85cebc",
  base0D: "#009ddc",
  base0E: "#98005d",
  base0F: "#b06110"
};

// node_modules/base16/src/twilight.js
var twilight_default = {
  scheme: "twilight",
  author: "david hart (http://hart-dev.com)",
  base00: "#1e1e1e",
  base01: "#323537",
  base02: "#464b50",
  base03: "#5f5a60",
  base04: "#838184",
  base05: "#a7a7a7",
  base06: "#c3c3c3",
  base07: "#ffffff",
  base08: "#cf6a4c",
  base09: "#cda869",
  base0A: "#f9ee98",
  base0B: "#8f9d6a",
  base0C: "#afc4db",
  base0D: "#7587a6",
  base0E: "#9b859d",
  base0F: "#9b703f"
};

// node_modules/react-base16-styling/lib/esm/index.js
var import_color = __toESM(require_color());
var import_lodash = __toESM(require_lodash());

// node_modules/react-base16-styling/lib/esm/colorConverters.js
function yuv2rgb(yuv) {
  var y = yuv[0], u = yuv[1], v = yuv[2];
  var r, g, b;
  r = y * 1 + u * 0 + v * 1.13983;
  g = y * 1 + u * -0.39465 + v * -0.5806;
  b = y * 1 + u * 2.02311 + v * 0;
  r = Math.min(Math.max(0, r), 1);
  g = Math.min(Math.max(0, g), 1);
  b = Math.min(Math.max(0, b), 1);
  return [r * 255, g * 255, b * 255];
}
function rgb2yuv(rgb) {
  var r = rgb[0] / 255, g = rgb[1] / 255, b = rgb[2] / 255;
  var y = r * 0.299 + g * 0.587 + b * 0.114;
  var u = r * -0.14713 + g * -0.28886 + b * 0.436;
  var v = r * 0.615 + g * -0.51499 + b * -0.10001;
  return [y, u, v];
}

// node_modules/react-base16-styling/lib/esm/index.js
function ownKeys3(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread3(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys3(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys3(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
var DEFAULT_BASE16 = default_default;
var BASE16_KEYS = Object.keys(DEFAULT_BASE16);
var flip = function flip2(x) {
  return x < 0.25 ? 1 : x < 0.5 ? 0.9 - x : 1.1 - x;
};
var invertColor = function invertColor2(hexString) {
  var color = (0, import_color.default)(hexString);
  var _rgb2yuv = rgb2yuv(color.array()), _rgb2yuv2 = _slicedToArray(_rgb2yuv, 3), y = _rgb2yuv2[0], u = _rgb2yuv2[1], v = _rgb2yuv2[2];
  var flippedYuv = [flip(y), u, v];
  var rgb = yuv2rgb(flippedYuv);
  return import_color.default.rgb(rgb).hex();
};
var merger = function merger2(styling) {
  return function(prevStyling) {
    return {
      className: [prevStyling.className, styling.className].filter(Boolean).join(" "),
      style: _objectSpread3(_objectSpread3({}, prevStyling.style || {}), styling.style || {})
    };
  };
};
var mergeStyling = function mergeStyling2(customStyling, defaultStyling) {
  if (customStyling === void 0) {
    return defaultStyling;
  }
  if (defaultStyling === void 0) {
    return customStyling;
  }
  var customType = _typeof(customStyling);
  var defaultType = _typeof(defaultStyling);
  switch (customType) {
    case "string":
      switch (defaultType) {
        case "string":
          return [defaultStyling, customStyling].filter(Boolean).join(" ");
        case "object":
          return merger({
            className: customStyling,
            style: defaultStyling
          });
        case "function":
          return function(styling) {
            for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
              args[_key - 1] = arguments[_key];
            }
            return merger({
              className: customStyling
            })(defaultStyling.apply(void 0, [styling].concat(args)));
          };
      }
      break;
    case "object":
      switch (defaultType) {
        case "string":
          return merger({
            className: defaultStyling,
            style: customStyling
          });
        case "object":
          return _objectSpread3(_objectSpread3({}, defaultStyling), customStyling);
        case "function":
          return function(styling) {
            for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
              args[_key2 - 1] = arguments[_key2];
            }
            return merger({
              style: customStyling
            })(defaultStyling.apply(void 0, [styling].concat(args)));
          };
      }
      break;
    case "function":
      switch (defaultType) {
        case "string":
          return function(styling) {
            for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
              args[_key3 - 1] = arguments[_key3];
            }
            return customStyling.apply(void 0, [merger(styling)({
              className: defaultStyling
            })].concat(args));
          };
        case "object":
          return function(styling) {
            for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {
              args[_key4 - 1] = arguments[_key4];
            }
            return customStyling.apply(void 0, [merger(styling)({
              style: defaultStyling
            })].concat(args));
          };
        case "function":
          return function(styling) {
            for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {
              args[_key5 - 1] = arguments[_key5];
            }
            return customStyling.apply(void 0, [defaultStyling.apply(void 0, [styling].concat(args))].concat(args));
          };
      }
  }
};
var mergeStylings = function mergeStylings2(customStylings, defaultStylings) {
  var keys = Object.keys(defaultStylings);
  for (var key in customStylings) {
    if (keys.indexOf(key) === -1) keys.push(key);
  }
  return keys.reduce(function(mergedStyling, key2) {
    return mergedStyling[key2] = mergeStyling(customStylings[key2], defaultStylings[key2]), mergedStyling;
  }, {});
};
var getStylingByKeys = function getStylingByKeys2(mergedStyling, keys) {
  for (var _len6 = arguments.length, args = new Array(_len6 > 2 ? _len6 - 2 : 0), _key6 = 2; _key6 < _len6; _key6++) {
    args[_key6 - 2] = arguments[_key6];
  }
  if (keys === null) {
    return mergedStyling;
  }
  if (!Array.isArray(keys)) {
    keys = [keys];
  }
  var styles = keys.map(function(key) {
    return mergedStyling[key];
  }).filter(Boolean);
  var props = styles.reduce(function(obj, s) {
    if (typeof s === "string") {
      obj.className = [obj.className, s].filter(Boolean).join(" ");
    } else if (_typeof(s) === "object") {
      obj.style = _objectSpread3(_objectSpread3({}, obj.style), s);
    } else if (typeof s === "function") {
      obj = _objectSpread3(_objectSpread3({}, obj), s.apply(void 0, [obj].concat(args)));
    }
    return obj;
  }, {
    className: "",
    style: {}
  });
  if (!props.className) {
    delete props.className;
  }
  if (Object.keys(props.style).length === 0) {
    delete props.style;
  }
  return props;
};
var invertBase16Theme = function invertBase16Theme2(base16Theme) {
  return Object.keys(base16Theme).reduce(function(t, key) {
    return t[key] = /^base/.test(key) ? invertColor(base16Theme[key]) : key === "scheme" ? base16Theme[key] + ":inverted" : base16Theme[key], t;
  }, {});
};
var createStyling = (0, import_lodash.default)(function(getStylingFromBase16) {
  var options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  var themeOrStyling = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
  var _options$defaultBase = options.defaultBase16, defaultBase16 = _options$defaultBase === void 0 ? DEFAULT_BASE16 : _options$defaultBase, _options$base16Themes = options.base16Themes, base16Themes = _options$base16Themes === void 0 ? null : _options$base16Themes;
  var base16Theme = getBase16Theme(themeOrStyling, base16Themes);
  if (base16Theme) {
    themeOrStyling = _objectSpread3(_objectSpread3({}, base16Theme), themeOrStyling);
  }
  var theme = BASE16_KEYS.reduce(function(t, key) {
    return t[key] = themeOrStyling[key] || defaultBase16[key], t;
  }, {});
  var customStyling = Object.keys(themeOrStyling).reduce(function(s, key) {
    return BASE16_KEYS.indexOf(key) === -1 ? (s[key] = themeOrStyling[key], s) : s;
  }, {});
  var defaultStyling = getStylingFromBase16(theme);
  var mergedStyling = mergeStylings(customStyling, defaultStyling);
  for (var _len7 = arguments.length, args = new Array(_len7 > 3 ? _len7 - 3 : 0), _key7 = 3; _key7 < _len7; _key7++) {
    args[_key7 - 3] = arguments[_key7];
  }
  return (0, import_lodash.default)(getStylingByKeys, 2).apply(void 0, [mergedStyling].concat(args));
}, 3);
var isStylingConfig = function isStylingConfig2(theme) {
  return !!theme.extend;
};
var getBase16Theme = function getBase16Theme2(theme, base16Themes) {
  if (theme && isStylingConfig(theme) && theme.extend) {
    theme = theme.extend;
  }
  if (typeof theme === "string") {
    var _theme$split = theme.split(":"), _theme$split2 = _slicedToArray(_theme$split, 2), _themeName = _theme$split2[0], modifier = _theme$split2[1];
    if (base16Themes) {
      theme = base16Themes[_themeName];
    } else {
      theme = src_exports[_themeName];
    }
    if (modifier === "inverted") {
      theme = invertBase16Theme(theme);
    }
  }
  return theme && Object.prototype.hasOwnProperty.call(theme, "base00") ? theme : void 0;
};
var invertTheme = function invertTheme2(theme) {
  if (typeof theme === "string") {
    return "".concat(theme, ":inverted");
  }
  if (theme && isStylingConfig(theme) && theme.extend) {
    if (typeof theme.extend === "string") {
      return _objectSpread3(_objectSpread3({}, theme), {}, {
        extend: "".concat(theme.extend, ":inverted")
      });
    }
    return _objectSpread3(_objectSpread3({}, theme), {}, {
      extend: invertBase16Theme(theme.extend)
    });
  }
  if (theme) {
    return invertBase16Theme(theme);
  }
  return theme;
};

// node_modules/react-json-tree/lib/esm/themes/solarized.js
var solarized_default2 = {
  scheme: "solarized",
  author: "ethan schoonover (http://ethanschoonover.com/solarized)",
  base00: "#002b36",
  base01: "#073642",
  base02: "#586e75",
  base03: "#657b83",
  base04: "#839496",
  base05: "#93a1a1",
  base06: "#eee8d5",
  base07: "#fdf6e3",
  base08: "#dc322f",
  base09: "#cb4b16",
  base0A: "#b58900",
  base0B: "#859900",
  base0C: "#2aa198",
  base0D: "#268bd2",
  base0E: "#6c71c4",
  base0F: "#d33682"
};

// node_modules/react-json-tree/lib/esm/createStylingFromTheme.js
function ownKeys4(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread4(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys4(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys4(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
var colorMap = function colorMap2(theme) {
  return {
    BACKGROUND_COLOR: theme.base00,
    TEXT_COLOR: theme.base07,
    STRING_COLOR: theme.base0B,
    DATE_COLOR: theme.base0B,
    NUMBER_COLOR: theme.base09,
    BOOLEAN_COLOR: theme.base09,
    NULL_COLOR: theme.base08,
    UNDEFINED_COLOR: theme.base08,
    FUNCTION_COLOR: theme.base08,
    SYMBOL_COLOR: theme.base08,
    LABEL_COLOR: theme.base0D,
    ARROW_COLOR: theme.base0D,
    ITEM_STRING_COLOR: theme.base0B,
    ITEM_STRING_EXPANDED_COLOR: theme.base03
  };
};
var valueColorMap = function valueColorMap2(colors) {
  return {
    String: colors.STRING_COLOR,
    Date: colors.DATE_COLOR,
    Number: colors.NUMBER_COLOR,
    Boolean: colors.BOOLEAN_COLOR,
    Null: colors.NULL_COLOR,
    Undefined: colors.UNDEFINED_COLOR,
    Function: colors.FUNCTION_COLOR,
    Symbol: colors.SYMBOL_COLOR
  };
};
var getDefaultThemeStyling = function getDefaultThemeStyling2(theme) {
  var colors = colorMap(theme);
  return {
    tree: {
      border: 0,
      padding: 0,
      marginTop: "0.5em",
      marginBottom: "0.5em",
      marginLeft: "0.125em",
      marginRight: 0,
      listStyle: "none",
      MozUserSelect: "none",
      WebkitUserSelect: "none",
      backgroundColor: colors.BACKGROUND_COLOR
    },
    value: function value(_ref, nodeType, keyPath) {
      var style = _ref.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          paddingTop: "0.25em",
          paddingRight: 0,
          marginLeft: "0.875em",
          WebkitUserSelect: "text",
          MozUserSelect: "text",
          wordWrap: "break-word",
          paddingLeft: keyPath.length > 1 ? "2.125em" : "1.25em",
          textIndent: "-0.5em",
          wordBreak: "break-all"
        })
      };
    },
    label: {
      display: "inline-block",
      color: colors.LABEL_COLOR
    },
    valueLabel: {
      margin: "0 0.5em 0 0"
    },
    valueText: function valueText(_ref2, nodeType) {
      var style = _ref2.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          color: valueColorMap(colors)[nodeType]
        })
      };
    },
    itemRange: function itemRange(styling, expanded) {
      return {
        style: {
          paddingTop: expanded ? 0 : "0.25em",
          cursor: "pointer",
          color: colors.LABEL_COLOR
        }
      };
    },
    arrow: function arrow(_ref3, nodeType, expanded) {
      var style = _ref3.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          marginLeft: 0,
          transition: "150ms",
          WebkitTransition: "150ms",
          MozTransition: "150ms",
          WebkitTransform: expanded ? "rotateZ(90deg)" : "rotateZ(0deg)",
          MozTransform: expanded ? "rotateZ(90deg)" : "rotateZ(0deg)",
          transform: expanded ? "rotateZ(90deg)" : "rotateZ(0deg)",
          transformOrigin: "45% 50%",
          WebkitTransformOrigin: "45% 50%",
          MozTransformOrigin: "45% 50%",
          position: "relative",
          lineHeight: "1.1em",
          fontSize: "0.75em"
        })
      };
    },
    arrowContainer: function arrowContainer(_ref4, arrowStyle) {
      var style = _ref4.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          display: "inline-block",
          paddingRight: "0.5em",
          paddingLeft: arrowStyle === "double" ? "1em" : 0,
          cursor: "pointer"
        })
      };
    },
    arrowSign: {
      color: colors.ARROW_COLOR
    },
    arrowSignInner: {
      position: "absolute",
      top: 0,
      left: "-0.4em"
    },
    nestedNode: function nestedNode(_ref5, keyPath, nodeType, expanded, expandable) {
      var style = _ref5.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          position: "relative",
          paddingTop: "0.25em",
          marginLeft: keyPath.length > 1 ? "0.875em" : 0,
          paddingLeft: !expandable ? "1.125em" : 0
        })
      };
    },
    rootNode: {
      padding: 0,
      margin: 0
    },
    nestedNodeLabel: function nestedNodeLabel(_ref6, keyPath, nodeType, expanded, expandable) {
      var style = _ref6.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          margin: 0,
          padding: 0,
          WebkitUserSelect: expandable ? "inherit" : "text",
          MozUserSelect: expandable ? "inherit" : "text",
          cursor: expandable ? "pointer" : "default"
        })
      };
    },
    nestedNodeItemString: function nestedNodeItemString(_ref7, keyPath, nodeType, expanded) {
      var style = _ref7.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          paddingLeft: "0.5em",
          cursor: "default",
          color: expanded ? colors.ITEM_STRING_EXPANDED_COLOR : colors.ITEM_STRING_COLOR
        })
      };
    },
    nestedNodeItemType: {
      marginLeft: "0.3em",
      marginRight: "0.3em"
    },
    nestedNodeChildren: function nestedNodeChildren(_ref8, nodeType, expanded) {
      var style = _ref8.style;
      return {
        style: _objectSpread4(_objectSpread4({}, style), {}, {
          padding: 0,
          margin: 0,
          listStyle: "none",
          display: expanded ? "block" : "none"
        })
      };
    },
    rootNodeChildren: {
      padding: 0,
      margin: 0,
      listStyle: "none"
    }
  };
};
var createStylingFromTheme = createStyling(getDefaultThemeStyling, {
  defaultBase16: solarized_default2
});
var createStylingFromTheme_default = createStylingFromTheme;

// node_modules/react-json-tree/lib/esm/index.js
var _excluded4 = ["data", "keyPath", "postprocessValue", "hideRoot", "theme", "invertTheme"];
function _createSuper3(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct3();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived), result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
function _isNativeReflectConstruct3() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
function ownKeys5(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread5(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys5(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys5(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
var identity = function identity2(value) {
  return value;
};
var expandRootNode = function expandRootNode2(keyPath, data, level) {
  return level === 0;
};
var defaultItemString = function defaultItemString2(type, data, itemType, itemString) {
  return import_react9.default.createElement("span", null, itemType, " ", itemString);
};
var defaultLabelRenderer = function defaultLabelRenderer2(_ref) {
  var _ref2 = _slicedToArray(_ref, 1), label = _ref2[0];
  return import_react9.default.createElement("span", null, label, ":");
};
var noCustomNode = function noCustomNode2() {
  return false;
};
function checkLegacyTheming(theme, props) {
  var deprecatedStylingMethodsMap = {
    getArrowStyle: "arrow",
    getListStyle: "nestedNodeChildren",
    getItemStringStyle: "nestedNodeItemString",
    getLabelStyle: "label",
    getValueStyle: "valueText"
  };
  var deprecatedStylingMethods = Object.keys(deprecatedStylingMethodsMap).filter(function(name) {
    return props[name];
  });
  if (deprecatedStylingMethods.length > 0) {
    if (typeof theme === "string") {
      theme = {
        extend: theme
      };
    } else {
      theme = _objectSpread5({}, theme);
    }
    deprecatedStylingMethods.forEach(function(name) {
      console.error('Styling method "'.concat(name, '" is deprecated, use "theme" property instead'));
      theme[deprecatedStylingMethodsMap[name]] = function(_ref3) {
        var style = _ref3.style;
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        return {
          style: _objectSpread5(_objectSpread5({}, style), props[name].apply(props, args))
        };
      };
    });
  }
  return theme;
}
function getStateFromProps2(props) {
  var theme = checkLegacyTheming(props.theme, props);
  if (props.invertTheme) {
    theme = invertTheme(theme);
  }
  return {
    styling: createStylingFromTheme_default(theme)
  };
}
var JSONTree = function(_React$Component) {
  _inherits(JSONTree2, _React$Component);
  var _super = _createSuper3(JSONTree2);
  function JSONTree2(props) {
    var _this;
    _classCallCheck(this, JSONTree2);
    _this = _super.call(this, props);
    _this.state = getStateFromProps2(props);
    return _this;
  }
  _createClass(JSONTree2, [{
    key: "UNSAFE_componentWillReceiveProps",
    value: function UNSAFE_componentWillReceiveProps(nextProps) {
      var _this2 = this;
      if (["theme", "invertTheme"].find(function(k) {
        return nextProps[k] !== _this2.props[k];
      })) {
        this.setState(getStateFromProps2(nextProps));
      }
    }
  }, {
    key: "shouldComponentUpdate",
    value: function shouldComponentUpdate(nextProps) {
      var _this3 = this;
      return !!Object.keys(nextProps).find(function(k) {
        return k === "keyPath" ? nextProps[k].join("/") !== _this3.props[k].join("/") : nextProps[k] !== _this3.props[k];
      });
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props, value = _this$props.data, keyPath = _this$props.keyPath, postprocessValue = _this$props.postprocessValue, hideRoot = _this$props.hideRoot, theme = _this$props.theme, _ = _this$props.invertTheme, rest = _objectWithoutProperties(_this$props, _excluded4);
      var styling = this.state.styling;
      return import_react9.default.createElement("ul", styling("tree"), import_react9.default.createElement(JSONNode_default, _extends({}, _objectSpread5({
        postprocessValue,
        hideRoot,
        styling
      }, rest), {
        keyPath: hideRoot ? [] : keyPath,
        value: postprocessValue(value)
      })));
    }
  }]);
  return JSONTree2;
}(import_react9.default.Component);
_defineProperty(JSONTree, "propTypes", {
  data: import_prop_types8.default.any,
  hideRoot: import_prop_types8.default.bool,
  theme: import_prop_types8.default.oneOfType([import_prop_types8.default.object, import_prop_types8.default.string]),
  invertTheme: import_prop_types8.default.bool,
  keyPath: import_prop_types8.default.arrayOf(import_prop_types8.default.oneOfType([import_prop_types8.default.string, import_prop_types8.default.number])),
  postprocessValue: import_prop_types8.default.func,
  sortObjectKeys: import_prop_types8.default.oneOfType([import_prop_types8.default.func, import_prop_types8.default.bool])
});
_defineProperty(JSONTree, "defaultProps", {
  shouldExpandNode: expandRootNode,
  hideRoot: false,
  keyPath: ["root"],
  getItemString: defaultItemString,
  labelRenderer: defaultLabelRenderer,
  valueRenderer: identity,
  postprocessValue: identity,
  isCustomNode: noCustomNode,
  collectionLimit: 50,
  invertTheme: true
});
export {
  JSONTree
};
//# sourceMappingURL=react-json-tree.js.map

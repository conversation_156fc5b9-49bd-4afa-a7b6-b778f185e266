{"version": 3, "sources": ["../../escape-string-regexp/index.js", "../../@sindresorhus/transliterate/replacements.js", "../../@sindresorhus/transliterate/index.js", "../../@sindresorhus/slugify/overridable-replacements.js", "../../@sindresorhus/slugify/index.js"], "sourcesContent": ["export default function escapeStringRegexp(string) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\t// Escape characters with special meaning either inside or outside character sets.\n\t// Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n\treturn string\n\t\t.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n\t\t.replace(/-/g, '\\\\x2d');\n}\n", "const replacements = [\n\t// German umlauts\n\t['ß', 'ss'],\n\t['ẞ', 'Ss'],\n\t['ä', 'ae'],\n\t['Ä', 'Ae'],\n\t['ö', 'oe'],\n\t['Ö', 'Oe'],\n\t['ü', 'ue'],\n\t['Ü', 'Ue'],\n\n\t// Latin\n\t['À', 'A'],\n\t['Á', 'A'],\n\t['Â', 'A'],\n\t['Ã', 'A'],\n\t['Ä', 'Ae'],\n\t['Å', 'A'],\n\t['Æ', 'AE'],\n\t['Ç', 'C'],\n\t['È', 'E'],\n\t['É', 'E'],\n\t['Ê', 'E'],\n\t['Ë', 'E'],\n\t['Ì', 'I'],\n\t['Í', 'I'],\n\t['Î', 'I'],\n\t['Ï', 'I'],\n\t['Ð', 'D'],\n\t['Ñ', 'N'],\n\t['Ò', 'O'],\n\t['Ó', 'O'],\n\t['Ô', 'O'],\n\t['Õ', 'O'],\n\t['Ö', 'Oe'],\n\t['Ő', 'O'],\n\t['Ø', 'O'],\n\t['Ù', 'U'],\n\t['Ú', 'U'],\n\t['Û', 'U'],\n\t['Ü', 'Ue'],\n\t['Ű', 'U'],\n\t['Ý', 'Y'],\n\t['Þ', 'TH'],\n\t['ß', 'ss'],\n\t['à', 'a'],\n\t['á', 'a'],\n\t['â', 'a'],\n\t['ã', 'a'],\n\t['ä', 'ae'],\n\t['å', 'a'],\n\t['æ', 'ae'],\n\t['ç', 'c'],\n\t['è', 'e'],\n\t['é', 'e'],\n\t['ê', 'e'],\n\t['ë', 'e'],\n\t['ì', 'i'],\n\t['í', 'i'],\n\t['î', 'i'],\n\t['ï', 'i'],\n\t['ð', 'd'],\n\t['ñ', 'n'],\n\t['ò', 'o'],\n\t['ó', 'o'],\n\t['ô', 'o'],\n\t['õ', 'o'],\n\t['ö', 'oe'],\n\t['ő', 'o'],\n\t['ø', 'o'],\n\t['ù', 'u'],\n\t['ú', 'u'],\n\t['û', 'u'],\n\t['ü', 'ue'],\n\t['ű', 'u'],\n\t['ý', 'y'],\n\t['þ', 'th'],\n\t['ÿ', 'y'],\n\t['ẞ', 'SS'],\n\n\t// Vietnamese\n\t['à', 'a'],\n\t['À', 'A'],\n\t['á', 'a'],\n\t['Á', 'A'],\n\t['â', 'a'],\n\t['Â', 'A'],\n\t['ã', 'a'],\n\t['Ã', 'A'],\n\t['è', 'e'],\n\t['È', 'E'],\n\t['é', 'e'],\n\t['É', 'E'],\n\t['ê', 'e'],\n\t['Ê', 'E'],\n\t['ì', 'i'],\n\t['Ì', 'I'],\n\t['í', 'i'],\n\t['Í', 'I'],\n\t['ò', 'o'],\n\t['Ò', 'O'],\n\t['ó', 'o'],\n\t['Ó', 'O'],\n\t['ô', 'o'],\n\t['Ô', 'O'],\n\t['õ', 'o'],\n\t['Õ', 'O'],\n\t['ù', 'u'],\n\t['Ù', 'U'],\n\t['ú', 'u'],\n\t['Ú', 'U'],\n\t['ý', 'y'],\n\t['Ý', 'Y'],\n\t['ă', 'a'],\n\t['Ă', 'A'],\n\t['Đ', 'D'],\n\t['đ', 'd'],\n\t['ĩ', 'i'],\n\t['Ĩ', 'I'],\n\t['ũ', 'u'],\n\t['Ũ', 'U'],\n\t['ơ', 'o'],\n\t['Ơ', 'O'],\n\t['ư', 'u'],\n\t['Ư', 'U'],\n\t['ạ', 'a'],\n\t['Ạ', 'A'],\n\t['ả', 'a'],\n\t['Ả', 'A'],\n\t['ấ', 'a'],\n\t['Ấ', 'A'],\n\t['ầ', 'a'],\n\t['Ầ', 'A'],\n\t['ẩ', 'a'],\n\t['Ẩ', 'A'],\n\t['ẫ', 'a'],\n\t['Ẫ', 'A'],\n\t['ậ', 'a'],\n\t['Ậ', 'A'],\n\t['ắ', 'a'],\n\t['Ắ', 'A'],\n\t['ằ', 'a'],\n\t['Ằ', 'A'],\n\t['ẳ', 'a'],\n\t['Ẳ', 'A'],\n\t['ẵ', 'a'],\n\t['Ẵ', 'A'],\n\t['ặ', 'a'],\n\t['Ặ', 'A'],\n\t['ẹ', 'e'],\n\t['Ẹ', 'E'],\n\t['ẻ', 'e'],\n\t['Ẻ', 'E'],\n\t['ẽ', 'e'],\n\t['Ẽ', 'E'],\n\t['ế', 'e'],\n\t['Ế', 'E'],\n\t['ề', 'e'],\n\t['Ề', 'E'],\n\t['ể', 'e'],\n\t['Ể', 'E'],\n\t['ễ', 'e'],\n\t['Ễ', 'E'],\n\t['ệ', 'e'],\n\t['Ệ', 'E'],\n\t['ỉ', 'i'],\n\t['Ỉ', 'I'],\n\t['ị', 'i'],\n\t['Ị', 'I'],\n\t['ọ', 'o'],\n\t['Ọ', 'O'],\n\t['ỏ', 'o'],\n\t['Ỏ', 'O'],\n\t['ố', 'o'],\n\t['Ố', 'O'],\n\t['ồ', 'o'],\n\t['Ồ', 'O'],\n\t['ổ', 'o'],\n\t['Ổ', 'O'],\n\t['ỗ', 'o'],\n\t['Ỗ', 'O'],\n\t['ộ', 'o'],\n\t['Ộ', 'O'],\n\t['ớ', 'o'],\n\t['Ớ', 'O'],\n\t['ờ', 'o'],\n\t['Ờ', 'O'],\n\t['ở', 'o'],\n\t['Ở', 'O'],\n\t['ỡ', 'o'],\n\t['Ỡ', 'O'],\n\t['ợ', 'o'],\n\t['Ợ', 'O'],\n\t['ụ', 'u'],\n\t['Ụ', 'U'],\n\t['ủ', 'u'],\n\t['Ủ', 'U'],\n\t['ứ', 'u'],\n\t['Ứ', 'U'],\n\t['ừ', 'u'],\n\t['Ừ', 'U'],\n\t['ử', 'u'],\n\t['Ử', 'U'],\n\t['ữ', 'u'],\n\t['Ữ', 'U'],\n\t['ự', 'u'],\n\t['Ự', 'U'],\n\t['ỳ', 'y'],\n\t['Ỳ', 'Y'],\n\t['ỵ', 'y'],\n\t['Ỵ', 'Y'],\n\t['ỷ', 'y'],\n\t['Ỷ', 'Y'],\n\t['ỹ', 'y'],\n\t['Ỹ', 'Y'],\n\n\t// Arabic\n\t['ء', 'e'],\n\t['آ', 'a'],\n\t['أ', 'a'],\n\t['ؤ', 'w'],\n\t['إ', 'i'],\n\t['ئ', 'y'],\n\t['ا', 'a'],\n\t['ب', 'b'],\n\t['ة', 't'],\n\t['ت', 't'],\n\t['ث', 'th'],\n\t['ج', 'j'],\n\t['ح', 'h'],\n\t['خ', 'kh'],\n\t['د', 'd'],\n\t['ذ', 'dh'],\n\t['ر', 'r'],\n\t['ز', 'z'],\n\t['س', 's'],\n\t['ش', 'sh'],\n\t['ص', 's'],\n\t['ض', 'd'],\n\t['ط', 't'],\n\t['ظ', 'z'],\n\t['ع', 'e'],\n\t['غ', 'gh'],\n\t['ـ', '_'],\n\t['ف', 'f'],\n\t['ق', 'q'],\n\t['ك', 'k'],\n\t['ل', 'l'],\n\t['م', 'm'],\n\t['ن', 'n'],\n\t['ه', 'h'],\n\t['و', 'w'],\n\t['ى', 'a'],\n\t['ي', 'y'],\n\t['َ‎', 'a'],\n\t['ُ', 'u'],\n\t['ِ‎', 'i'],\n\t['٠', '0'],\n\t['١', '1'],\n\t['٢', '2'],\n\t['٣', '3'],\n\t['٤', '4'],\n\t['٥', '5'],\n\t['٦', '6'],\n\t['٧', '7'],\n\t['٨', '8'],\n\t['٩', '9'],\n\n\t// Persian / Farsi\n\t['چ', 'ch'],\n\t['ک', 'k'],\n\t['گ', 'g'],\n\t['پ', 'p'],\n\t['ژ', 'zh'],\n\t['ی', 'y'],\n\t['۰', '0'],\n\t['۱', '1'],\n\t['۲', '2'],\n\t['۳', '3'],\n\t['۴', '4'],\n\t['۵', '5'],\n\t['۶', '6'],\n\t['۷', '7'],\n\t['۸', '8'],\n\t['۹', '9'],\n\n\t// Pashto\n\t['ټ', 'p'],\n\t['ځ', 'z'],\n\t['څ', 'c'],\n\t['ډ', 'd'],\n\t['ﺫ', 'd'],\n\t['ﺭ', 'r'],\n\t['ړ', 'r'],\n\t['ﺯ', 'z'],\n\t['ږ', 'g'],\n\t['ښ', 'x'],\n\t['ګ', 'g'],\n\t['ڼ', 'n'],\n\t['ۀ', 'e'],\n\t['ې', 'e'],\n\t['ۍ', 'ai'],\n\n\t// Urdu\n\t['ٹ', 't'],\n\t['ڈ', 'd'],\n\t['ڑ', 'r'],\n\t['ں', 'n'],\n\t['ہ', 'h'],\n\t['ھ', 'h'],\n\t['ے', 'e'],\n\n\t// Russian\n\t['А', 'A'],\n\t['а', 'a'],\n\t['Б', 'B'],\n\t['б', 'b'],\n\t['В', 'V'],\n\t['в', 'v'],\n\t['Г', 'G'],\n\t['г', 'g'],\n\t['Д', 'D'],\n\t['д', 'd'],\n\t['ъе', 'ye'],\n\t['Ъе', 'Ye'],\n\t['ъЕ', 'yE'],\n\t['ЪЕ', 'YE'],\n\t['Е', 'E'],\n\t['е', 'e'],\n\t['Ё', 'Yo'],\n\t['ё', 'yo'],\n\t['Ж', 'Zh'],\n\t['ж', 'zh'],\n\t['З', 'Z'],\n\t['з', 'z'],\n\t['И', 'I'],\n\t['и', 'i'],\n\t['ый', 'iy'],\n\t['Ый', 'Iy'],\n\t['ЫЙ', 'IY'],\n\t['ыЙ', 'iY'],\n\t['Й', 'Y'],\n\t['й', 'y'],\n\t['К', 'K'],\n\t['к', 'k'],\n\t['Л', 'L'],\n\t['л', 'l'],\n\t['М', 'M'],\n\t['м', 'm'],\n\t['Н', 'N'],\n\t['н', 'n'],\n\t['О', 'O'],\n\t['о', 'o'],\n\t['П', 'P'],\n\t['п', 'p'],\n\t['Р', 'R'],\n\t['р', 'r'],\n\t['С', 'S'],\n\t['с', 's'],\n\t['Т', 'T'],\n\t['т', 't'],\n\t['У', 'U'],\n\t['у', 'u'],\n\t['Ф', 'F'],\n\t['ф', 'f'],\n\t['Х', 'Kh'],\n\t['х', 'kh'],\n\t['Ц', 'Ts'],\n\t['ц', 'ts'],\n\t['Ч', 'Ch'],\n\t['ч', 'ch'],\n\t['Ш', 'Sh'],\n\t['ш', 'sh'],\n\t['Щ', 'Sch'],\n\t['щ', 'sch'],\n\t['Ъ', ''],\n\t['ъ', ''],\n\t['Ы', 'Y'],\n\t['ы', 'y'],\n\t['Ь', ''],\n\t['ь', ''],\n\t['Э', 'E'],\n\t['э', 'e'],\n\t['Ю', 'Yu'],\n\t['ю', 'yu'],\n\t['Я', 'Ya'],\n\t['я', 'ya'],\n\n\t// Romanian\n\t['ă', 'a'],\n\t['Ă', 'A'],\n\t['ș', 's'],\n\t['Ș', 'S'],\n\t['ț', 't'],\n\t['Ț', 'T'],\n\t['ţ', 't'],\n\t['Ţ', 'T'],\n\n\t// Turkish\n\t['ş', 's'],\n\t['Ş', 'S'],\n\t['ç', 'c'],\n\t['Ç', 'C'],\n\t['ğ', 'g'],\n\t['Ğ', 'G'],\n\t['ı', 'i'],\n\t['İ', 'I'],\n\n\t// Armenian\n\t['ա', 'a'],\n\t['Ա', 'A'],\n\t['բ', 'b'],\n\t['Բ', 'B'],\n\t['գ', 'g'],\n\t['Գ', 'G'],\n\t['դ', 'd'],\n\t['Դ', 'D'],\n\t['ե', 'ye'],\n\t['Ե', 'Ye'],\n\t['զ', 'z'],\n\t['Զ', 'Z'],\n\t['է', 'e'],\n\t['Է', 'E'],\n\t['ը', 'y'],\n\t['Ը', 'Y'],\n\t['թ', 't'],\n\t['Թ', 'T'],\n\t['ժ', 'zh'],\n\t['Ժ', 'Zh'],\n\t['ի', 'i'],\n\t['Ի', 'I'],\n\t['լ', 'l'],\n\t['Լ', 'L'],\n\t['խ', 'kh'],\n\t['Խ', 'Kh'],\n\t['ծ', 'ts'],\n\t['Ծ', 'Ts'],\n\t['կ', 'k'],\n\t['Կ', 'K'],\n\t['հ', 'h'],\n\t['Հ', 'H'],\n\t['ձ', 'dz'],\n\t['Ձ', 'Dz'],\n\t['ղ', 'gh'],\n\t['Ղ', 'Gh'],\n\t['ճ', 'tch'],\n\t['Ճ', 'Tch'],\n\t['մ', 'm'],\n\t['Մ', 'M'],\n\t['յ', 'y'],\n\t['Յ', 'Y'],\n\t['ն', 'n'],\n\t['Ն', 'N'],\n\t['շ', 'sh'],\n\t['Շ', 'Sh'],\n\t['ո', 'vo'],\n\t['Ո', 'Vo'],\n\t['չ', 'ch'],\n\t['Չ', 'Ch'],\n\t['պ', 'p'],\n\t['Պ', 'P'],\n\t['ջ', 'j'],\n\t['Ջ', 'J'],\n\t['ռ', 'r'],\n\t['Ռ', 'R'],\n\t['ս', 's'],\n\t['Ս', 'S'],\n\t['վ', 'v'],\n\t['Վ', 'V'],\n\t['տ', 't'],\n\t['Տ', 'T'],\n\t['ր', 'r'],\n\t['Ր', 'R'],\n\t['ց', 'c'],\n\t['Ց', 'C'],\n\t['ու', 'u'],\n\t['ՈՒ', 'U'],\n\t['Ու', 'U'],\n\t['փ', 'p'],\n\t['Փ', 'P'],\n\t['ք', 'q'],\n\t['Ք', 'Q'],\n\t['օ', 'o'],\n\t['Օ', 'O'],\n\t['ֆ', 'f'],\n\t['Ֆ', 'F'],\n\t['և', 'yev'],\n\n\t// Georgian\n\t['ა', 'a'],\n\t['ბ', 'b'],\n\t['გ', 'g'],\n\t['დ', 'd'],\n\t['ე', 'e'],\n\t['ვ', 'v'],\n\t['ზ', 'z'],\n\t['თ', 't'],\n\t['ი', 'i'],\n\t['კ', 'k'],\n\t['ლ', 'l'],\n\t['მ', 'm'],\n\t['ნ', 'n'],\n\t['ო', 'o'],\n\t['პ', 'p'],\n\t['ჟ', 'zh'],\n\t['რ', 'r'],\n\t['ს', 's'],\n\t['ტ', 't'],\n\t['უ', 'u'],\n\t['ფ', 'ph'],\n\t['ქ', 'q'],\n\t['ღ', 'gh'],\n\t['ყ', 'k'],\n\t['შ', 'sh'],\n\t['ჩ', 'ch'],\n\t['ც', 'ts'],\n\t['ძ', 'dz'],\n\t['წ', 'ts'],\n\t['ჭ', 'tch'],\n\t['ხ', 'kh'],\n\t['ჯ', 'j'],\n\t['ჰ', 'h'],\n\n\t// Czech\n\t['č', 'c'],\n\t['ď', 'd'],\n\t['ě', 'e'],\n\t['ň', 'n'],\n\t['ř', 'r'],\n\t['š', 's'],\n\t['ť', 't'],\n\t['ů', 'u'],\n\t['ž', 'z'],\n\t['Č', 'C'],\n\t['Ď', 'D'],\n\t['Ě', 'E'],\n\t['Ň', 'N'],\n\t['Ř', 'R'],\n\t['Š', 'S'],\n\t['Ť', 'T'],\n\t['Ů', 'U'],\n\t['Ž', 'Z'],\n\n\t// Dhivehi\n\t['ހ', 'h'],\n\t['ށ', 'sh'],\n\t['ނ', 'n'],\n\t['ރ', 'r'],\n\t['ބ', 'b'],\n\t['ޅ', 'lh'],\n\t['ކ', 'k'],\n\t['އ', 'a'],\n\t['ވ', 'v'],\n\t['މ', 'm'],\n\t['ފ', 'f'],\n\t['ދ', 'dh'],\n\t['ތ', 'th'],\n\t['ލ', 'l'],\n\t['ގ', 'g'],\n\t['ޏ', 'gn'],\n\t['ސ', 's'],\n\t['ޑ', 'd'],\n\t['ޒ', 'z'],\n\t['ޓ', 't'],\n\t['ޔ', 'y'],\n\t['ޕ', 'p'],\n\t['ޖ', 'j'],\n\t['ޗ', 'ch'],\n\t['ޘ', 'tt'],\n\t['ޙ', 'hh'],\n\t['ޚ', 'kh'],\n\t['ޛ', 'th'],\n\t['ޜ', 'z'],\n\t['ޝ', 'sh'],\n\t['ޞ', 's'],\n\t['ޟ', 'd'],\n\t['ޠ', 't'],\n\t['ޡ', 'z'],\n\t['ޢ', 'a'],\n\t['ޣ', 'gh'],\n\t['ޤ', 'q'],\n\t['ޥ', 'w'],\n\t['ަ', 'a'],\n\t['ާ', 'aa'],\n\t['ި', 'i'],\n\t['ީ', 'ee'],\n\t['ު', 'u'],\n\t['ޫ', 'oo'],\n\t['ެ', 'e'],\n\t['ޭ', 'ey'],\n\t['ޮ', 'o'],\n\t['ޯ', 'oa'],\n\t['ް', ''],\n\n\t// Greek\n\t['α', 'a'],\n\t['β', 'v'],\n\t['γ', 'g'],\n\t['δ', 'd'],\n\t['ε', 'e'],\n\t['ζ', 'z'],\n\t['η', 'i'],\n\t['θ', 'th'],\n\t['ι', 'i'],\n\t['κ', 'k'],\n\t['λ', 'l'],\n\t['μ', 'm'],\n\t['ν', 'n'],\n\t['ξ', 'ks'],\n\t['ο', 'o'],\n\t['π', 'p'],\n\t['ρ', 'r'],\n\t['σ', 's'],\n\t['τ', 't'],\n\t['υ', 'y'],\n\t['φ', 'f'],\n\t['χ', 'x'],\n\t['ψ', 'ps'],\n\t['ω', 'o'],\n\t['ά', 'a'],\n\t['έ', 'e'],\n\t['ί', 'i'],\n\t['ό', 'o'],\n\t['ύ', 'y'],\n\t['ή', 'i'],\n\t['ώ', 'o'],\n\t['ς', 's'],\n\t['ϊ', 'i'],\n\t['ΰ', 'y'],\n\t['ϋ', 'y'],\n\t['ΐ', 'i'],\n\t['Α', 'A'],\n\t['Β', 'B'],\n\t['Γ', 'G'],\n\t['Δ', 'D'],\n\t['Ε', 'E'],\n\t['Ζ', 'Z'],\n\t['Η', 'I'],\n\t['Θ', 'TH'],\n\t['Ι', 'I'],\n\t['Κ', 'K'],\n\t['Λ', 'L'],\n\t['Μ', 'M'],\n\t['Ν', 'N'],\n\t['Ξ', 'KS'],\n\t['Ο', 'O'],\n\t['Π', 'P'],\n\t['Ρ', 'R'],\n\t['Σ', 'S'],\n\t['Τ', 'T'],\n\t['Υ', 'Y'],\n\t['Φ', 'F'],\n\t['Χ', 'X'],\n\t['Ψ', 'PS'],\n\t['Ω', 'O'],\n\t['Ά', 'A'],\n\t['Έ', 'E'],\n\t['Ί', 'I'],\n\t['Ό', 'O'],\n\t['Ύ', 'Y'],\n\t['Ή', 'I'],\n\t['Ώ', 'O'],\n\t['Ϊ', 'I'],\n\t['Ϋ', 'Y'],\n\n\t// Disabled as it conflicts with German and Latin.\n\t// Hungarian\n\t// ['ä', 'a'],\n\t// ['Ä', 'A'],\n\t// ['ö', 'o'],\n\t// ['Ö', 'O'],\n\t// ['ü', 'u'],\n\t// ['Ü', 'U'],\n\t// ['ű', 'u'],\n\t// ['Ű', 'U'],\n\n\t// Latvian\n\t['ā', 'a'],\n\t['ē', 'e'],\n\t['ģ', 'g'],\n\t['ī', 'i'],\n\t['ķ', 'k'],\n\t['ļ', 'l'],\n\t['ņ', 'n'],\n\t['ū', 'u'],\n\t['Ā', 'A'],\n\t['Ē', 'E'],\n\t['Ģ', 'G'],\n\t['Ī', 'I'],\n\t['Ķ', 'K'],\n\t['Ļ', 'L'],\n\t['Ņ', 'N'],\n\t['Ū', 'U'],\n\t['č', 'c'],\n\t['š', 's'],\n\t['ž', 'z'],\n\t['Č', 'C'],\n\t['Š', 'S'],\n\t['Ž', 'Z'],\n\n\t// Lithuanian\n\t['ą', 'a'],\n\t['č', 'c'],\n\t['ę', 'e'],\n\t['ė', 'e'],\n\t['į', 'i'],\n\t['š', 's'],\n\t['ų', 'u'],\n\t['ū', 'u'],\n\t['ž', 'z'],\n\t['Ą', 'A'],\n\t['Č', 'C'],\n\t['Ę', 'E'],\n\t['Ė', 'E'],\n\t['Į', 'I'],\n\t['Š', 'S'],\n\t['Ų', 'U'],\n\t['Ū', 'U'],\n\n\t// Macedonian\n\t['Ќ', 'Kj'],\n\t['ќ', 'kj'],\n\t['Љ', 'Lj'],\n\t['љ', 'lj'],\n\t['Њ', 'Nj'],\n\t['њ', 'nj'],\n\t['Тс', 'Ts'],\n\t['тс', 'ts'],\n\n\t// Polish\n\t['ą', 'a'],\n\t['ć', 'c'],\n\t['ę', 'e'],\n\t['ł', 'l'],\n\t['ń', 'n'],\n\t['ś', 's'],\n\t['ź', 'z'],\n\t['ż', 'z'],\n\t['Ą', 'A'],\n\t['Ć', 'C'],\n\t['Ę', 'E'],\n\t['Ł', 'L'],\n\t['Ń', 'N'],\n\t['Ś', 'S'],\n\t['Ź', 'Z'],\n\t['Ż', 'Z'],\n\n\t// Disabled as it conflicts with Vietnamese.\n\t// Serbian\n\t// ['љ', 'lj'],\n\t// ['њ', 'nj'],\n\t// ['Љ', 'Lj'],\n\t// ['Њ', 'Nj'],\n\t// ['đ', 'dj'],\n\t// ['Đ', 'Dj'],\n\t// ['ђ', 'dj'],\n\t// ['ј', 'j'],\n\t// ['ћ', 'c'],\n\t// ['џ', 'dz'],\n\t// ['Ђ', 'Dj'],\n\t// ['Ј', 'j'],\n\t// ['Ћ', 'C'],\n\t// ['Џ', 'Dz'],\n\n\t// Disabled as it conflicts with German and Latin.\n\t// Slovak\n\t// ['ä', 'a'],\n\t// ['Ä', 'A'],\n\t// ['ľ', 'l'],\n\t// ['ĺ', 'l'],\n\t// ['ŕ', 'r'],\n\t// ['Ľ', 'L'],\n\t// ['Ĺ', 'L'],\n\t// ['Ŕ', 'R'],\n\n\t// Disabled as it conflicts with German and Latin.\n\t// Swedish\n\t// ['å', 'o'],\n\t// ['Å', 'o'],\n\t// ['ä', 'a'],\n\t// ['Ä', 'A'],\n\t// ['ë', 'e'],\n\t// ['Ë', 'E'],\n\t// ['ö', 'o'],\n\t// ['Ö', 'O'],\n\n\t// Ukrainian\n\t['Є', 'Ye'],\n\t['І', 'I'],\n\t['Ї', 'Yi'],\n\t['Ґ', 'G'],\n\t['є', 'ye'],\n\t['і', 'i'],\n\t['ї', 'yi'],\n\t['ґ', 'g'],\n\n\t// Dutch\n\t['Ĳ', 'IJ'],\n\t['ĳ', 'ij'],\n\n\t// Danish\n\t// ['Æ', 'Ae'],\n\t// ['Ø', 'Oe'],\n\t// ['Å', 'Aa'],\n\t// ['æ', 'ae'],\n\t// ['ø', 'oe'],\n\t// ['å', 'aa']\n\n\t// Currencies\n\t['¢', 'c'],\n\t['¥', 'Y'],\n\t['߿', 'b'],\n\t['৳', 't'],\n\t['૱', 'Bo'],\n\t['฿', 'B'],\n\t['₠', 'CE'],\n\t['₡', 'C'],\n\t['₢', 'Cr'],\n\t['₣', 'F'],\n\t['₥', 'm'],\n\t['₦', 'N'],\n\t['₧', 'Pt'],\n\t['₨', 'Rs'],\n\t['₩', 'W'],\n\t['₫', 's'],\n\t['€', 'E'],\n\t['₭', 'K'],\n\t['₮', 'T'],\n\t['₯', 'Dp'],\n\t['₰', 'S'],\n\t['₱', 'P'],\n\t['₲', 'G'],\n\t['₳', 'A'],\n\t['₴', 'S'],\n\t['₵', 'C'],\n\t['₶', 'tt'],\n\t['₷', 'S'],\n\t['₸', 'T'],\n\t['₹', 'R'],\n\t['₺', 'L'],\n\t['₽', 'P'],\n\t['₿', 'B'],\n\t['﹩', '$'],\n\t['￠', 'c'],\n\t['￥', 'Y'],\n\t['￦', 'W'],\n\n\t// Latin\n\t['𝐀', 'A'],\n\t['𝐁', 'B'],\n\t['𝐂', 'C'],\n\t['𝐃', 'D'],\n\t['𝐄', 'E'],\n\t['𝐅', 'F'],\n\t['𝐆', 'G'],\n\t['𝐇', 'H'],\n\t['𝐈', 'I'],\n\t['𝐉', 'J'],\n\t['𝐊', 'K'],\n\t['𝐋', 'L'],\n\t['𝐌', 'M'],\n\t['𝐍', 'N'],\n\t['𝐎', 'O'],\n\t['𝐏', 'P'],\n\t['𝐐', 'Q'],\n\t['𝐑', 'R'],\n\t['𝐒', 'S'],\n\t['𝐓', 'T'],\n\t['𝐔', 'U'],\n\t['𝐕', 'V'],\n\t['𝐖', 'W'],\n\t['𝐗', 'X'],\n\t['𝐘', 'Y'],\n\t['𝐙', 'Z'],\n\t['𝐚', 'a'],\n\t['𝐛', 'b'],\n\t['𝐜', 'c'],\n\t['𝐝', 'd'],\n\t['𝐞', 'e'],\n\t['𝐟', 'f'],\n\t['𝐠', 'g'],\n\t['𝐡', 'h'],\n\t['𝐢', 'i'],\n\t['𝐣', 'j'],\n\t['𝐤', 'k'],\n\t['𝐥', 'l'],\n\t['𝐦', 'm'],\n\t['𝐧', 'n'],\n\t['𝐨', 'o'],\n\t['𝐩', 'p'],\n\t['𝐪', 'q'],\n\t['𝐫', 'r'],\n\t['𝐬', 's'],\n\t['𝐭', 't'],\n\t['𝐮', 'u'],\n\t['𝐯', 'v'],\n\t['𝐰', 'w'],\n\t['𝐱', 'x'],\n\t['𝐲', 'y'],\n\t['𝐳', 'z'],\n\t['𝐴', 'A'],\n\t['𝐵', 'B'],\n\t['𝐶', 'C'],\n\t['𝐷', 'D'],\n\t['𝐸', 'E'],\n\t['𝐹', 'F'],\n\t['𝐺', 'G'],\n\t['𝐻', 'H'],\n\t['𝐼', 'I'],\n\t['𝐽', 'J'],\n\t['𝐾', 'K'],\n\t['𝐿', 'L'],\n\t['𝑀', 'M'],\n\t['𝑁', 'N'],\n\t['𝑂', 'O'],\n\t['𝑃', 'P'],\n\t['𝑄', 'Q'],\n\t['𝑅', 'R'],\n\t['𝑆', 'S'],\n\t['𝑇', 'T'],\n\t['𝑈', 'U'],\n\t['𝑉', 'V'],\n\t['𝑊', 'W'],\n\t['𝑋', 'X'],\n\t['𝑌', 'Y'],\n\t['𝑍', 'Z'],\n\t['𝑎', 'a'],\n\t['𝑏', 'b'],\n\t['𝑐', 'c'],\n\t['𝑑', 'd'],\n\t['𝑒', 'e'],\n\t['𝑓', 'f'],\n\t['𝑔', 'g'],\n\t['𝑖', 'i'],\n\t['𝑗', 'j'],\n\t['𝑘', 'k'],\n\t['𝑙', 'l'],\n\t['𝑚', 'm'],\n\t['𝑛', 'n'],\n\t['𝑜', 'o'],\n\t['𝑝', 'p'],\n\t['𝑞', 'q'],\n\t['𝑟', 'r'],\n\t['𝑠', 's'],\n\t['𝑡', 't'],\n\t['𝑢', 'u'],\n\t['𝑣', 'v'],\n\t['𝑤', 'w'],\n\t['𝑥', 'x'],\n\t['𝑦', 'y'],\n\t['𝑧', 'z'],\n\t['𝑨', 'A'],\n\t['𝑩', 'B'],\n\t['𝑪', 'C'],\n\t['𝑫', 'D'],\n\t['𝑬', 'E'],\n\t['𝑭', 'F'],\n\t['𝑮', 'G'],\n\t['𝑯', 'H'],\n\t['𝑰', 'I'],\n\t['𝑱', 'J'],\n\t['𝑲', 'K'],\n\t['𝑳', 'L'],\n\t['𝑴', 'M'],\n\t['𝑵', 'N'],\n\t['𝑶', 'O'],\n\t['𝑷', 'P'],\n\t['𝑸', 'Q'],\n\t['𝑹', 'R'],\n\t['𝑺', 'S'],\n\t['𝑻', 'T'],\n\t['𝑼', 'U'],\n\t['𝑽', 'V'],\n\t['𝑾', 'W'],\n\t['𝑿', 'X'],\n\t['𝒀', 'Y'],\n\t['𝒁', 'Z'],\n\t['𝒂', 'a'],\n\t['𝒃', 'b'],\n\t['𝒄', 'c'],\n\t['𝒅', 'd'],\n\t['𝒆', 'e'],\n\t['𝒇', 'f'],\n\t['𝒈', 'g'],\n\t['𝒉', 'h'],\n\t['𝒊', 'i'],\n\t['𝒋', 'j'],\n\t['𝒌', 'k'],\n\t['𝒍', 'l'],\n\t['𝒎', 'm'],\n\t['𝒏', 'n'],\n\t['𝒐', 'o'],\n\t['𝒑', 'p'],\n\t['𝒒', 'q'],\n\t['𝒓', 'r'],\n\t['𝒔', 's'],\n\t['𝒕', 't'],\n\t['𝒖', 'u'],\n\t['𝒗', 'v'],\n\t['𝒘', 'w'],\n\t['𝒙', 'x'],\n\t['𝒚', 'y'],\n\t['𝒛', 'z'],\n\t['𝒜', 'A'],\n\t['𝒞', 'C'],\n\t['𝒟', 'D'],\n\t['𝒢', 'g'],\n\t['𝒥', 'J'],\n\t['𝒦', 'K'],\n\t['𝒩', 'N'],\n\t['𝒪', 'O'],\n\t['𝒫', 'P'],\n\t['𝒬', 'Q'],\n\t['𝒮', 'S'],\n\t['𝒯', 'T'],\n\t['𝒰', 'U'],\n\t['𝒱', 'V'],\n\t['𝒲', 'W'],\n\t['𝒳', 'X'],\n\t['𝒴', 'Y'],\n\t['𝒵', 'Z'],\n\t['𝒶', 'a'],\n\t['𝒷', 'b'],\n\t['𝒸', 'c'],\n\t['𝒹', 'd'],\n\t['𝒻', 'f'],\n\t['𝒽', 'h'],\n\t['𝒾', 'i'],\n\t['𝒿', 'j'],\n\t['𝓀', 'h'],\n\t['𝓁', 'l'],\n\t['𝓂', 'm'],\n\t['𝓃', 'n'],\n\t['𝓅', 'p'],\n\t['𝓆', 'q'],\n\t['𝓇', 'r'],\n\t['𝓈', 's'],\n\t['𝓉', 't'],\n\t['𝓊', 'u'],\n\t['𝓋', 'v'],\n\t['𝓌', 'w'],\n\t['𝓍', 'x'],\n\t['𝓎', 'y'],\n\t['𝓏', 'z'],\n\t['𝓐', 'A'],\n\t['𝓑', 'B'],\n\t['𝓒', 'C'],\n\t['𝓓', 'D'],\n\t['𝓔', 'E'],\n\t['𝓕', 'F'],\n\t['𝓖', 'G'],\n\t['𝓗', 'H'],\n\t['𝓘', 'I'],\n\t['𝓙', 'J'],\n\t['𝓚', 'K'],\n\t['𝓛', 'L'],\n\t['𝓜', 'M'],\n\t['𝓝', 'N'],\n\t['𝓞', 'O'],\n\t['𝓟', 'P'],\n\t['𝓠', 'Q'],\n\t['𝓡', 'R'],\n\t['𝓢', 'S'],\n\t['𝓣', 'T'],\n\t['𝓤', 'U'],\n\t['𝓥', 'V'],\n\t['𝓦', 'W'],\n\t['𝓧', 'X'],\n\t['𝓨', 'Y'],\n\t['𝓩', 'Z'],\n\t['𝓪', 'a'],\n\t['𝓫', 'b'],\n\t['𝓬', 'c'],\n\t['𝓭', 'd'],\n\t['𝓮', 'e'],\n\t['𝓯', 'f'],\n\t['𝓰', 'g'],\n\t['𝓱', 'h'],\n\t['𝓲', 'i'],\n\t['𝓳', 'j'],\n\t['𝓴', 'k'],\n\t['𝓵', 'l'],\n\t['𝓶', 'm'],\n\t['𝓷', 'n'],\n\t['𝓸', 'o'],\n\t['𝓹', 'p'],\n\t['𝓺', 'q'],\n\t['𝓻', 'r'],\n\t['𝓼', 's'],\n\t['𝓽', 't'],\n\t['𝓾', 'u'],\n\t['𝓿', 'v'],\n\t['𝔀', 'w'],\n\t['𝔁', 'x'],\n\t['𝔂', 'y'],\n\t['𝔃', 'z'],\n\t['𝔄', 'A'],\n\t['𝔅', 'B'],\n\t['𝔇', 'D'],\n\t['𝔈', 'E'],\n\t['𝔉', 'F'],\n\t['𝔊', 'G'],\n\t['𝔍', 'J'],\n\t['𝔎', 'K'],\n\t['𝔏', 'L'],\n\t['𝔐', 'M'],\n\t['𝔑', 'N'],\n\t['𝔒', 'O'],\n\t['𝔓', 'P'],\n\t['𝔔', 'Q'],\n\t['𝔖', 'S'],\n\t['𝔗', 'T'],\n\t['𝔘', 'U'],\n\t['𝔙', 'V'],\n\t['𝔚', 'W'],\n\t['𝔛', 'X'],\n\t['𝔜', 'Y'],\n\t['𝔞', 'a'],\n\t['𝔟', 'b'],\n\t['𝔠', 'c'],\n\t['𝔡', 'd'],\n\t['𝔢', 'e'],\n\t['𝔣', 'f'],\n\t['𝔤', 'g'],\n\t['𝔥', 'h'],\n\t['𝔦', 'i'],\n\t['𝔧', 'j'],\n\t['𝔨', 'k'],\n\t['𝔩', 'l'],\n\t['𝔪', 'm'],\n\t['𝔫', 'n'],\n\t['𝔬', 'o'],\n\t['𝔭', 'p'],\n\t['𝔮', 'q'],\n\t['𝔯', 'r'],\n\t['𝔰', 's'],\n\t['𝔱', 't'],\n\t['𝔲', 'u'],\n\t['𝔳', 'v'],\n\t['𝔴', 'w'],\n\t['𝔵', 'x'],\n\t['𝔶', 'y'],\n\t['𝔷', 'z'],\n\t['𝔸', 'A'],\n\t['𝔹', 'B'],\n\t['𝔻', 'D'],\n\t['𝔼', 'E'],\n\t['𝔽', 'F'],\n\t['𝔾', 'G'],\n\t['𝕀', 'I'],\n\t['𝕁', 'J'],\n\t['𝕂', 'K'],\n\t['𝕃', 'L'],\n\t['𝕄', 'M'],\n\t['𝕆', 'N'],\n\t['𝕊', 'S'],\n\t['𝕋', 'T'],\n\t['𝕌', 'U'],\n\t['𝕍', 'V'],\n\t['𝕎', 'W'],\n\t['𝕏', 'X'],\n\t['𝕐', 'Y'],\n\t['𝕒', 'a'],\n\t['𝕓', 'b'],\n\t['𝕔', 'c'],\n\t['𝕕', 'd'],\n\t['𝕖', 'e'],\n\t['𝕗', 'f'],\n\t['𝕘', 'g'],\n\t['𝕙', 'h'],\n\t['𝕚', 'i'],\n\t['𝕛', 'j'],\n\t['𝕜', 'k'],\n\t['𝕝', 'l'],\n\t['𝕞', 'm'],\n\t['𝕟', 'n'],\n\t['𝕠', 'o'],\n\t['𝕡', 'p'],\n\t['𝕢', 'q'],\n\t['𝕣', 'r'],\n\t['𝕤', 's'],\n\t['𝕥', 't'],\n\t['𝕦', 'u'],\n\t['𝕧', 'v'],\n\t['𝕨', 'w'],\n\t['𝕩', 'x'],\n\t['𝕪', 'y'],\n\t['𝕫', 'z'],\n\t['𝕬', 'A'],\n\t['𝕭', 'B'],\n\t['𝕮', 'C'],\n\t['𝕯', 'D'],\n\t['𝕰', 'E'],\n\t['𝕱', 'F'],\n\t['𝕲', 'G'],\n\t['𝕳', 'H'],\n\t['𝕴', 'I'],\n\t['𝕵', 'J'],\n\t['𝕶', 'K'],\n\t['𝕷', 'L'],\n\t['𝕸', 'M'],\n\t['𝕹', 'N'],\n\t['𝕺', 'O'],\n\t['𝕻', 'P'],\n\t['𝕼', 'Q'],\n\t['𝕽', 'R'],\n\t['𝕾', 'S'],\n\t['𝕿', 'T'],\n\t['𝖀', 'U'],\n\t['𝖁', 'V'],\n\t['𝖂', 'W'],\n\t['𝖃', 'X'],\n\t['𝖄', 'Y'],\n\t['𝖅', 'Z'],\n\t['𝖆', 'a'],\n\t['𝖇', 'b'],\n\t['𝖈', 'c'],\n\t['𝖉', 'd'],\n\t['𝖊', 'e'],\n\t['𝖋', 'f'],\n\t['𝖌', 'g'],\n\t['𝖍', 'h'],\n\t['𝖎', 'i'],\n\t['𝖏', 'j'],\n\t['𝖐', 'k'],\n\t['𝖑', 'l'],\n\t['𝖒', 'm'],\n\t['𝖓', 'n'],\n\t['𝖔', 'o'],\n\t['𝖕', 'p'],\n\t['𝖖', 'q'],\n\t['𝖗', 'r'],\n\t['𝖘', 's'],\n\t['𝖙', 't'],\n\t['𝖚', 'u'],\n\t['𝖛', 'v'],\n\t['𝖜', 'w'],\n\t['𝖝', 'x'],\n\t['𝖞', 'y'],\n\t['𝖟', 'z'],\n\t['𝖠', 'A'],\n\t['𝖡', 'B'],\n\t['𝖢', 'C'],\n\t['𝖣', 'D'],\n\t['𝖤', 'E'],\n\t['𝖥', 'F'],\n\t['𝖦', 'G'],\n\t['𝖧', 'H'],\n\t['𝖨', 'I'],\n\t['𝖩', 'J'],\n\t['𝖪', 'K'],\n\t['𝖫', 'L'],\n\t['𝖬', 'M'],\n\t['𝖭', 'N'],\n\t['𝖮', 'O'],\n\t['𝖯', 'P'],\n\t['𝖰', 'Q'],\n\t['𝖱', 'R'],\n\t['𝖲', 'S'],\n\t['𝖳', 'T'],\n\t['𝖴', 'U'],\n\t['𝖵', 'V'],\n\t['𝖶', 'W'],\n\t['𝖷', 'X'],\n\t['𝖸', 'Y'],\n\t['𝖹', 'Z'],\n\t['𝖺', 'a'],\n\t['𝖻', 'b'],\n\t['𝖼', 'c'],\n\t['𝖽', 'd'],\n\t['𝖾', 'e'],\n\t['𝖿', 'f'],\n\t['𝗀', 'g'],\n\t['𝗁', 'h'],\n\t['𝗂', 'i'],\n\t['𝗃', 'j'],\n\t['𝗄', 'k'],\n\t['𝗅', 'l'],\n\t['𝗆', 'm'],\n\t['𝗇', 'n'],\n\t['𝗈', 'o'],\n\t['𝗉', 'p'],\n\t['𝗊', 'q'],\n\t['𝗋', 'r'],\n\t['𝗌', 's'],\n\t['𝗍', 't'],\n\t['𝗎', 'u'],\n\t['𝗏', 'v'],\n\t['𝗐', 'w'],\n\t['𝗑', 'x'],\n\t['𝗒', 'y'],\n\t['𝗓', 'z'],\n\t['𝗔', 'A'],\n\t['𝗕', 'B'],\n\t['𝗖', 'C'],\n\t['𝗗', 'D'],\n\t['𝗘', 'E'],\n\t['𝗙', 'F'],\n\t['𝗚', 'G'],\n\t['𝗛', 'H'],\n\t['𝗜', 'I'],\n\t['𝗝', 'J'],\n\t['𝗞', 'K'],\n\t['𝗟', 'L'],\n\t['𝗠', 'M'],\n\t['𝗡', 'N'],\n\t['𝗢', 'O'],\n\t['𝗣', 'P'],\n\t['𝗤', 'Q'],\n\t['𝗥', 'R'],\n\t['𝗦', 'S'],\n\t['𝗧', 'T'],\n\t['𝗨', 'U'],\n\t['𝗩', 'V'],\n\t['𝗪', 'W'],\n\t['𝗫', 'X'],\n\t['𝗬', 'Y'],\n\t['𝗭', 'Z'],\n\t['𝗮', 'a'],\n\t['𝗯', 'b'],\n\t['𝗰', 'c'],\n\t['𝗱', 'd'],\n\t['𝗲', 'e'],\n\t['𝗳', 'f'],\n\t['𝗴', 'g'],\n\t['𝗵', 'h'],\n\t['𝗶', 'i'],\n\t['𝗷', 'j'],\n\t['𝗸', 'k'],\n\t['𝗹', 'l'],\n\t['𝗺', 'm'],\n\t['𝗻', 'n'],\n\t['𝗼', 'o'],\n\t['𝗽', 'p'],\n\t['𝗾', 'q'],\n\t['𝗿', 'r'],\n\t['𝘀', 's'],\n\t['𝘁', 't'],\n\t['𝘂', 'u'],\n\t['𝘃', 'v'],\n\t['𝘄', 'w'],\n\t['𝘅', 'x'],\n\t['𝘆', 'y'],\n\t['𝘇', 'z'],\n\t['𝘈', 'A'],\n\t['𝘉', 'B'],\n\t['𝘊', 'C'],\n\t['𝘋', 'D'],\n\t['𝘌', 'E'],\n\t['𝘍', 'F'],\n\t['𝘎', 'G'],\n\t['𝘏', 'H'],\n\t['𝘐', 'I'],\n\t['𝘑', 'J'],\n\t['𝘒', 'K'],\n\t['𝘓', 'L'],\n\t['𝘔', 'M'],\n\t['𝘕', 'N'],\n\t['𝘖', 'O'],\n\t['𝘗', 'P'],\n\t['𝘘', 'Q'],\n\t['𝘙', 'R'],\n\t['𝘚', 'S'],\n\t['𝘛', 'T'],\n\t['𝘜', 'U'],\n\t['𝘝', 'V'],\n\t['𝘞', 'W'],\n\t['𝘟', 'X'],\n\t['𝘠', 'Y'],\n\t['𝘡', 'Z'],\n\t['𝘢', 'a'],\n\t['𝘣', 'b'],\n\t['𝘤', 'c'],\n\t['𝘥', 'd'],\n\t['𝘦', 'e'],\n\t['𝘧', 'f'],\n\t['𝘨', 'g'],\n\t['𝘩', 'h'],\n\t['𝘪', 'i'],\n\t['𝘫', 'j'],\n\t['𝘬', 'k'],\n\t['𝘭', 'l'],\n\t['𝘮', 'm'],\n\t['𝘯', 'n'],\n\t['𝘰', 'o'],\n\t['𝘱', 'p'],\n\t['𝘲', 'q'],\n\t['𝘳', 'r'],\n\t['𝘴', 's'],\n\t['𝘵', 't'],\n\t['𝘶', 'u'],\n\t['𝘷', 'v'],\n\t['𝘸', 'w'],\n\t['𝘹', 'x'],\n\t['𝘺', 'y'],\n\t['𝘻', 'z'],\n\t['𝘼', 'A'],\n\t['𝘽', 'B'],\n\t['𝘾', 'C'],\n\t['𝘿', 'D'],\n\t['𝙀', 'E'],\n\t['𝙁', 'F'],\n\t['𝙂', 'G'],\n\t['𝙃', 'H'],\n\t['𝙄', 'I'],\n\t['𝙅', 'J'],\n\t['𝙆', 'K'],\n\t['𝙇', 'L'],\n\t['𝙈', 'M'],\n\t['𝙉', 'N'],\n\t['𝙊', 'O'],\n\t['𝙋', 'P'],\n\t['𝙌', 'Q'],\n\t['𝙍', 'R'],\n\t['𝙎', 'S'],\n\t['𝙏', 'T'],\n\t['𝙐', 'U'],\n\t['𝙑', 'V'],\n\t['𝙒', 'W'],\n\t['𝙓', 'X'],\n\t['𝙔', 'Y'],\n\t['𝙕', 'Z'],\n\t['𝙖', 'a'],\n\t['𝙗', 'b'],\n\t['𝙘', 'c'],\n\t['𝙙', 'd'],\n\t['𝙚', 'e'],\n\t['𝙛', 'f'],\n\t['𝙜', 'g'],\n\t['𝙝', 'h'],\n\t['𝙞', 'i'],\n\t['𝙟', 'j'],\n\t['𝙠', 'k'],\n\t['𝙡', 'l'],\n\t['𝙢', 'm'],\n\t['𝙣', 'n'],\n\t['𝙤', 'o'],\n\t['𝙥', 'p'],\n\t['𝙦', 'q'],\n\t['𝙧', 'r'],\n\t['𝙨', 's'],\n\t['𝙩', 't'],\n\t['𝙪', 'u'],\n\t['𝙫', 'v'],\n\t['𝙬', 'w'],\n\t['𝙭', 'x'],\n\t['𝙮', 'y'],\n\t['𝙯', 'z'],\n\t['𝙰', 'A'],\n\t['𝙱', 'B'],\n\t['𝙲', 'C'],\n\t['𝙳', 'D'],\n\t['𝙴', 'E'],\n\t['𝙵', 'F'],\n\t['𝙶', 'G'],\n\t['𝙷', 'H'],\n\t['𝙸', 'I'],\n\t['𝙹', 'J'],\n\t['𝙺', 'K'],\n\t['𝙻', 'L'],\n\t['𝙼', 'M'],\n\t['𝙽', 'N'],\n\t['𝙾', 'O'],\n\t['𝙿', 'P'],\n\t['𝚀', 'Q'],\n\t['𝚁', 'R'],\n\t['𝚂', 'S'],\n\t['𝚃', 'T'],\n\t['𝚄', 'U'],\n\t['𝚅', 'V'],\n\t['𝚆', 'W'],\n\t['𝚇', 'X'],\n\t['𝚈', 'Y'],\n\t['𝚉', 'Z'],\n\t['𝚊', 'a'],\n\t['𝚋', 'b'],\n\t['𝚌', 'c'],\n\t['𝚍', 'd'],\n\t['𝚎', 'e'],\n\t['𝚏', 'f'],\n\t['𝚐', 'g'],\n\t['𝚑', 'h'],\n\t['𝚒', 'i'],\n\t['𝚓', 'j'],\n\t['𝚔', 'k'],\n\t['𝚕', 'l'],\n\t['𝚖', 'm'],\n\t['𝚗', 'n'],\n\t['𝚘', 'o'],\n\t['𝚙', 'p'],\n\t['𝚚', 'q'],\n\t['𝚛', 'r'],\n\t['𝚜', 's'],\n\t['𝚝', 't'],\n\t['𝚞', 'u'],\n\t['𝚟', 'v'],\n\t['𝚠', 'w'],\n\t['𝚡', 'x'],\n\t['𝚢', 'y'],\n\t['𝚣', 'z'],\n\n\t// Dotless letters\n\t['𝚤', 'l'],\n\t['𝚥', 'j'],\n\n\t// Greek\n\t['𝛢', 'A'],\n\t['𝛣', 'B'],\n\t['𝛤', 'G'],\n\t['𝛥', 'D'],\n\t['𝛦', 'E'],\n\t['𝛧', 'Z'],\n\t['𝛨', 'I'],\n\t['𝛩', 'TH'],\n\t['𝛪', 'I'],\n\t['𝛫', 'K'],\n\t['𝛬', 'L'],\n\t['𝛭', 'M'],\n\t['𝛮', 'N'],\n\t['𝛯', 'KS'],\n\t['𝛰', 'O'],\n\t['𝛱', 'P'],\n\t['𝛲', 'R'],\n\t['𝛳', 'TH'],\n\t['𝛴', 'S'],\n\t['𝛵', 'T'],\n\t['𝛶', 'Y'],\n\t['𝛷', 'F'],\n\t['𝛸', 'x'],\n\t['𝛹', 'PS'],\n\t['𝛺', 'O'],\n\t['𝛻', 'D'],\n\t['𝛼', 'a'],\n\t['𝛽', 'b'],\n\t['𝛾', 'g'],\n\t['𝛿', 'd'],\n\t['𝜀', 'e'],\n\t['𝜁', 'z'],\n\t['𝜂', 'i'],\n\t['𝜃', 'th'],\n\t['𝜄', 'i'],\n\t['𝜅', 'k'],\n\t['𝜆', 'l'],\n\t['𝜇', 'm'],\n\t['𝜈', 'n'],\n\t['𝜉', 'ks'],\n\t['𝜊', 'o'],\n\t['𝜋', 'p'],\n\t['𝜌', 'r'],\n\t['𝜍', 's'],\n\t['𝜎', 's'],\n\t['𝜏', 't'],\n\t['𝜐', 'y'],\n\t['𝜑', 'f'],\n\t['𝜒', 'x'],\n\t['𝜓', 'ps'],\n\t['𝜔', 'o'],\n\t['𝜕', 'd'],\n\t['𝜖', 'E'],\n\t['𝜗', 'TH'],\n\t['𝜘', 'K'],\n\t['𝜙', 'f'],\n\t['𝜚', 'r'],\n\t['𝜛', 'p'],\n\t['𝜜', 'A'],\n\t['𝜝', 'V'],\n\t['𝜞', 'G'],\n\t['𝜟', 'D'],\n\t['𝜠', 'E'],\n\t['𝜡', 'Z'],\n\t['𝜢', 'I'],\n\t['𝜣', 'TH'],\n\t['𝜤', 'I'],\n\t['𝜥', 'K'],\n\t['𝜦', 'L'],\n\t['𝜧', 'M'],\n\t['𝜨', 'N'],\n\t['𝜩', 'KS'],\n\t['𝜪', 'O'],\n\t['𝜫', 'P'],\n\t['𝜬', 'S'],\n\t['𝜭', 'TH'],\n\t['𝜮', 'S'],\n\t['𝜯', 'T'],\n\t['𝜰', 'Y'],\n\t['𝜱', 'F'],\n\t['𝜲', 'X'],\n\t['𝜳', 'PS'],\n\t['𝜴', 'O'],\n\t['𝜵', 'D'],\n\t['𝜶', 'a'],\n\t['𝜷', 'v'],\n\t['𝜸', 'g'],\n\t['𝜹', 'd'],\n\t['𝜺', 'e'],\n\t['𝜻', 'z'],\n\t['𝜼', 'i'],\n\t['𝜽', 'th'],\n\t['𝜾', 'i'],\n\t['𝜿', 'k'],\n\t['𝝀', 'l'],\n\t['𝝁', 'm'],\n\t['𝝂', 'n'],\n\t['𝝃', 'ks'],\n\t['𝝄', 'o'],\n\t['𝝅', 'p'],\n\t['𝝆', 'r'],\n\t['𝝇', 's'],\n\t['𝝈', 's'],\n\t['𝝉', 't'],\n\t['𝝊', 'y'],\n\t['𝝋', 'f'],\n\t['𝝌', 'x'],\n\t['𝝍', 'ps'],\n\t['𝝎', 'o'],\n\t['𝝏', 'a'],\n\t['𝝐', 'e'],\n\t['𝝑', 'i'],\n\t['𝝒', 'k'],\n\t['𝝓', 'f'],\n\t['𝝔', 'r'],\n\t['𝝕', 'p'],\n\t['𝝖', 'A'],\n\t['𝝗', 'B'],\n\t['𝝘', 'G'],\n\t['𝝙', 'D'],\n\t['𝝚', 'E'],\n\t['𝝛', 'Z'],\n\t['𝝜', 'I'],\n\t['𝝝', 'TH'],\n\t['𝝞', 'I'],\n\t['𝝟', 'K'],\n\t['𝝠', 'L'],\n\t['𝝡', 'M'],\n\t['𝝢', 'N'],\n\t['𝝣', 'KS'],\n\t['𝝤', 'O'],\n\t['𝝥', 'P'],\n\t['𝝦', 'R'],\n\t['𝝧', 'TH'],\n\t['𝝨', 'S'],\n\t['𝝩', 'T'],\n\t['𝝪', 'Y'],\n\t['𝝫', 'F'],\n\t['𝝬', 'X'],\n\t['𝝭', 'PS'],\n\t['𝝮', 'O'],\n\t['𝝯', 'D'],\n\t['𝝰', 'a'],\n\t['𝝱', 'v'],\n\t['𝝲', 'g'],\n\t['𝝳', 'd'],\n\t['𝝴', 'e'],\n\t['𝝵', 'z'],\n\t['𝝶', 'i'],\n\t['𝝷', 'th'],\n\t['𝝸', 'i'],\n\t['𝝹', 'k'],\n\t['𝝺', 'l'],\n\t['𝝻', 'm'],\n\t['𝝼', 'n'],\n\t['𝝽', 'ks'],\n\t['𝝾', 'o'],\n\t['𝝿', 'p'],\n\t['𝞀', 'r'],\n\t['𝞁', 's'],\n\t['𝞂', 's'],\n\t['𝞃', 't'],\n\t['𝞄', 'y'],\n\t['𝞅', 'f'],\n\t['𝞆', 'x'],\n\t['𝞇', 'ps'],\n\t['𝞈', 'o'],\n\t['𝞉', 'a'],\n\t['𝞊', 'e'],\n\t['𝞋', 'i'],\n\t['𝞌', 'k'],\n\t['𝞍', 'f'],\n\t['𝞎', 'r'],\n\t['𝞏', 'p'],\n\t['𝞐', 'A'],\n\t['𝞑', 'V'],\n\t['𝞒', 'G'],\n\t['𝞓', 'D'],\n\t['𝞔', 'E'],\n\t['𝞕', 'Z'],\n\t['𝞖', 'I'],\n\t['𝞗', 'TH'],\n\t['𝞘', 'I'],\n\t['𝞙', 'K'],\n\t['𝞚', 'L'],\n\t['𝞛', 'M'],\n\t['𝞜', 'N'],\n\t['𝞝', 'KS'],\n\t['𝞞', 'O'],\n\t['𝞟', 'P'],\n\t['𝞠', 'S'],\n\t['𝞡', 'TH'],\n\t['𝞢', 'S'],\n\t['𝞣', 'T'],\n\t['𝞤', 'Y'],\n\t['𝞥', 'F'],\n\t['𝞦', 'X'],\n\t['𝞧', 'PS'],\n\t['𝞨', 'O'],\n\t['𝞩', 'D'],\n\t['𝞪', 'av'],\n\t['𝞫', 'g'],\n\t['𝞬', 'd'],\n\t['𝞭', 'e'],\n\t['𝞮', 'z'],\n\t['𝞯', 'i'],\n\t['𝞰', 'i'],\n\t['𝞱', 'th'],\n\t['𝞲', 'i'],\n\t['𝞳', 'k'],\n\t['𝞴', 'l'],\n\t['𝞵', 'm'],\n\t['𝞶', 'n'],\n\t['𝞷', 'ks'],\n\t['𝞸', 'o'],\n\t['𝞹', 'p'],\n\t['𝞺', 'r'],\n\t['𝞻', 's'],\n\t['𝞼', 's'],\n\t['𝞽', 't'],\n\t['𝞾', 'y'],\n\t['𝞿', 'f'],\n\t['𝟀', 'x'],\n\t['𝟁', 'ps'],\n\t['𝟂', 'o'],\n\t['𝟃', 'a'],\n\t['𝟄', 'e'],\n\t['𝟅', 'i'],\n\t['𝟆', 'k'],\n\t['𝟇', 'f'],\n\t['𝟈', 'r'],\n\t['𝟉', 'p'],\n\t['𝟊', 'F'],\n\t['𝟋', 'f'],\n\t['⒜', '(a)'],\n\t['⒝', '(b)'],\n\t['⒞', '(c)'],\n\t['⒟', '(d)'],\n\t['⒠', '(e)'],\n\t['⒡', '(f)'],\n\t['⒢', '(g)'],\n\t['⒣', '(h)'],\n\t['⒤', '(i)'],\n\t['⒥', '(j)'],\n\t['⒦', '(k)'],\n\t['⒧', '(l)'],\n\t['⒨', '(m)'],\n\t['⒩', '(n)'],\n\t['⒪', '(o)'],\n\t['⒫', '(p)'],\n\t['⒬', '(q)'],\n\t['⒭', '(r)'],\n\t['⒮', '(s)'],\n\t['⒯', '(t)'],\n\t['⒰', '(u)'],\n\t['⒱', '(v)'],\n\t['⒲', '(w)'],\n\t['⒳', '(x)'],\n\t['⒴', '(y)'],\n\t['⒵', '(z)'],\n\t['Ⓐ', '(A)'],\n\t['Ⓑ', '(B)'],\n\t['Ⓒ', '(C)'],\n\t['Ⓓ', '(D)'],\n\t['Ⓔ', '(E)'],\n\t['Ⓕ', '(F)'],\n\t['Ⓖ', '(G)'],\n\t['Ⓗ', '(H)'],\n\t['Ⓘ', '(I)'],\n\t['Ⓙ', '(J)'],\n\t['Ⓚ', '(K)'],\n\t['Ⓛ', '(L)'],\n\t['Ⓝ', '(N)'],\n\t['Ⓞ', '(O)'],\n\t['Ⓟ', '(P)'],\n\t['Ⓠ', '(Q)'],\n\t['Ⓡ', '(R)'],\n\t['Ⓢ', '(S)'],\n\t['Ⓣ', '(T)'],\n\t['Ⓤ', '(U)'],\n\t['Ⓥ', '(V)'],\n\t['Ⓦ', '(W)'],\n\t['Ⓧ', '(X)'],\n\t['Ⓨ', '(Y)'],\n\t['Ⓩ', '(Z)'],\n\t['ⓐ', '(a)'],\n\t['ⓑ', '(b)'],\n\t['ⓒ', '(b)'],\n\t['ⓓ', '(c)'],\n\t['ⓔ', '(e)'],\n\t['ⓕ', '(f)'],\n\t['ⓖ', '(g)'],\n\t['ⓗ', '(h)'],\n\t['ⓘ', '(i)'],\n\t['ⓙ', '(j)'],\n\t['ⓚ', '(k)'],\n\t['ⓛ', '(l)'],\n\t['ⓜ', '(m)'],\n\t['ⓝ', '(n)'],\n\t['ⓞ', '(o)'],\n\t['ⓟ', '(p)'],\n\t['ⓠ', '(q)'],\n\t['ⓡ', '(r)'],\n\t['ⓢ', '(s)'],\n\t['ⓣ', '(t)'],\n\t['ⓤ', '(u)'],\n\t['ⓥ', '(v)'],\n\t['ⓦ', '(w)'],\n\t['ⓧ', '(x)'],\n\t['ⓨ', '(y)'],\n\t['ⓩ', '(z)'],\n\n\t// Maltese\n\t['Ċ', 'C'],\n\t['ċ', 'c'],\n\t['Ġ', 'G'],\n\t['ġ', 'g'],\n\t['Ħ', 'H'],\n\t['ħ', 'h'],\n\t['Ż', 'Z'],\n\t['ż', 'z'],\n\n\t// Numbers\n\t['𝟎', '0'],\n\t['𝟏', '1'],\n\t['𝟐', '2'],\n\t['𝟑', '3'],\n\t['𝟒', '4'],\n\t['𝟓', '5'],\n\t['𝟔', '6'],\n\t['𝟕', '7'],\n\t['𝟖', '8'],\n\t['𝟗', '9'],\n\t['𝟘', '0'],\n\t['𝟙', '1'],\n\t['𝟚', '2'],\n\t['𝟛', '3'],\n\t['𝟜', '4'],\n\t['𝟝', '5'],\n\t['𝟞', '6'],\n\t['𝟟', '7'],\n\t['𝟠', '8'],\n\t['𝟡', '9'],\n\t['𝟢', '0'],\n\t['𝟣', '1'],\n\t['𝟤', '2'],\n\t['𝟥', '3'],\n\t['𝟦', '4'],\n\t['𝟧', '5'],\n\t['𝟨', '6'],\n\t['𝟩', '7'],\n\t['𝟪', '8'],\n\t['𝟫', '9'],\n\t['𝟬', '0'],\n\t['𝟭', '1'],\n\t['𝟮', '2'],\n\t['𝟯', '3'],\n\t['𝟰', '4'],\n\t['𝟱', '5'],\n\t['𝟲', '6'],\n\t['𝟳', '7'],\n\t['𝟴', '8'],\n\t['𝟵', '9'],\n\t['𝟶', '0'],\n\t['𝟷', '1'],\n\t['𝟸', '2'],\n\t['𝟹', '3'],\n\t['𝟺', '4'],\n\t['𝟻', '5'],\n\t['𝟼', '6'],\n\t['𝟽', '7'],\n\t['𝟾', '8'],\n\t['𝟿', '9'],\n\t['①', '1'],\n\t['②', '2'],\n\t['③', '3'],\n\t['④', '4'],\n\t['⑤', '5'],\n\t['⑥', '6'],\n\t['⑦', '7'],\n\t['⑧', '8'],\n\t['⑨', '9'],\n\t['⑩', '10'],\n\t['⑪', '11'],\n\t['⑫', '12'],\n\t['⑬', '13'],\n\t['⑭', '14'],\n\t['⑮', '15'],\n\t['⑯', '16'],\n\t['⑰', '17'],\n\t['⑱', '18'],\n\t['⑲', '19'],\n\t['⑳', '20'],\n\t['⑴', '1'],\n\t['⑵', '2'],\n\t['⑶', '3'],\n\t['⑷', '4'],\n\t['⑸', '5'],\n\t['⑹', '6'],\n\t['⑺', '7'],\n\t['⑻', '8'],\n\t['⑼', '9'],\n\t['⑽', '10'],\n\t['⑾', '11'],\n\t['⑿', '12'],\n\t['⒀', '13'],\n\t['⒁', '14'],\n\t['⒂', '15'],\n\t['⒃', '16'],\n\t['⒄', '17'],\n\t['⒅', '18'],\n\t['⒆', '19'],\n\t['⒇', '20'],\n\t['⒈', '1.'],\n\t['⒉', '2.'],\n\t['⒊', '3.'],\n\t['⒋', '4.'],\n\t['⒌', '5.'],\n\t['⒍', '6.'],\n\t['⒎', '7.'],\n\t['⒏', '8.'],\n\t['⒐', '9.'],\n\t['⒑', '10.'],\n\t['⒒', '11.'],\n\t['⒓', '12.'],\n\t['⒔', '13.'],\n\t['⒕', '14.'],\n\t['⒖', '15.'],\n\t['⒗', '16.'],\n\t['⒘', '17.'],\n\t['⒙', '18.'],\n\t['⒚', '19.'],\n\t['⒛', '20.'],\n\t['⓪', '0'],\n\t['⓫', '11'],\n\t['⓬', '12'],\n\t['⓭', '13'],\n\t['⓮', '14'],\n\t['⓯', '15'],\n\t['⓰', '16'],\n\t['⓱', '17'],\n\t['⓲', '18'],\n\t['⓳', '19'],\n\t['⓴', '20'],\n\t['⓵', '1'],\n\t['⓶', '2'],\n\t['⓷', '3'],\n\t['⓸', '4'],\n\t['⓹', '5'],\n\t['⓺', '6'],\n\t['⓻', '7'],\n\t['⓼', '8'],\n\t['⓽', '9'],\n\t['⓾', '10'],\n\t['⓿', '0'],\n\n\t// Punctuation\n\t['🙰', '&'],\n\t['🙱', '&'],\n\t['🙲', '&'],\n\t['🙳', '&'],\n\t['🙴', '&'],\n\t['🙵', '&'],\n\t['🙶', '\"'],\n\t['🙷', '\"'],\n\t['🙸', '\"'],\n\t['‽', '?!'],\n\t['🙹', '?!'],\n\t['🙺', '?!'],\n\t['🙻', '?!'],\n\t['🙼', '/'],\n\t['🙽', '\\\\'],\n\n\t// Alchemy\n\t['🜇', 'AR'],\n\t['🜈', 'V'],\n\t['🜉', 'V'],\n\t['🜆', 'VR'],\n\t['🜅', 'VF'],\n\t['🜩', '2'],\n\t['🜪', '5'],\n\t['🝡', 'f'],\n\t['🝢', 'W'],\n\t['🝣', 'U'],\n\t['🝧', 'V'],\n\t['🝨', 'T'],\n\t['🝪', 'V'],\n\t['🝫', 'MB'],\n\t['🝬', 'VB'],\n\t['🝲', '3B'],\n\t['🝳', '3B'],\n\n\t// Emojis\n\t['💯', '100'],\n\t['🔙', 'BACK'],\n\t['🔚', 'END'],\n\t['🔛', 'ON!'],\n\t['🔜', 'SOON'],\n\t['🔝', 'TOP'],\n\t['🔞', '18'],\n\t['🔤', 'abc'],\n\t['🔠', 'ABCD'],\n\t['🔡', 'abcd'],\n\t['🔢', '1234'],\n\t['🔣', 'T&@%'],\n\t['#️⃣', '#'],\n\t['*️⃣', '*'],\n\t['0️⃣', '0'],\n\t['1️⃣', '1'],\n\t['2️⃣', '2'],\n\t['3️⃣', '3'],\n\t['4️⃣', '4'],\n\t['5️⃣', '5'],\n\t['6️⃣', '6'],\n\t['7️⃣', '7'],\n\t['8️⃣', '8'],\n\t['9️⃣', '9'],\n\t['🔟', '10'],\n\t['🅰️', 'A'],\n\t['🅱️', 'B'],\n\t['🆎', 'AB'],\n\t['🆑', 'CL'],\n\t['🅾️', 'O'],\n\t['🅿', 'P'],\n\t['🆘', 'SOS'],\n\t['🅲', 'C'],\n\t['🅳', 'D'],\n\t['🅴', 'E'],\n\t['🅵', 'F'],\n\t['🅶', 'G'],\n\t['🅷', 'H'],\n\t['🅸', 'I'],\n\t['🅹', 'J'],\n\t['🅺', 'K'],\n\t['🅻', 'L'],\n\t['🅼', 'M'],\n\t['🅽', 'N'],\n\t['🆀', 'Q'],\n\t['🆁', 'R'],\n\t['🆂', 'S'],\n\t['🆃', 'T'],\n\t['🆄', 'U'],\n\t['🆅', 'V'],\n\t['🆆', 'W'],\n\t['🆇', 'X'],\n\t['🆈', 'Y'],\n\t['🆉', 'Z']\n];\n\nexport default replacements;\n", "import escapeStringRegexp from 'escape-string-regexp';\nimport builtinReplacements from './replacements.js';\n\nconst doCustomReplacements = (string, replacements) => {\n\tfor (const [key, value] of replacements) {\n\t\t// TODO: Use `String#replaceAll()` when targeting Node.js 16.\n\t\tstring = string.replace(new RegExp(escapeStringRegexp(key), 'g'), value);\n\t}\n\n\treturn string;\n};\n\nexport default function transliterate(string, options) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError(`Expected a string, got \\`${typeof string}\\``);\n\t}\n\n\toptions = {\n\t\tcustomReplacements: [],\n\t\t...options\n\t};\n\n\tconst customReplacements = new Map([\n\t\t...builtinReplacements,\n\t\t...options.customReplacements\n\t]);\n\n\tstring = string.normalize();\n\tstring = doCustomReplacements(string, customReplacements);\n\tstring = string.normalize('NFD').replace(/\\p{Diacritic}/gu, '').normalize();\n\n\treturn string;\n}\n", "const overridableReplacements = [\n\t['&', ' and '],\n\t['🦄', ' unicorn '],\n\t['♥', ' love ']\n];\n\nexport default overridableReplacements;\n", "import escapeStringRegexp from 'escape-string-regexp';\nimport transliterate from '@sindresorhus/transliterate';\nimport builtinOverridableReplacements from './overridable-replacements.js';\n\nconst decamelize = string => {\n\treturn string\n\t\t// Separate capitalized words.\n\t\t.replace(/([A-Z]{2,})(\\d+)/g, '$1 $2')\n\t\t.replace(/([a-z\\d]+)([A-Z]{2,})/g, '$1 $2')\n\n\t\t.replace(/([a-z\\d])([A-Z])/g, '$1 $2')\n\t\t// `[a-rt-z]` matches all lowercase characters except `s`.\n\t\t// This avoids matching plural acronyms like `APIs`.\n\t\t.replace(/([A-Z]+)([A-Z][a-rt-z\\d]+)/g, '$1 $2');\n};\n\nconst removeMootSeparators = (string, separator) => {\n\tconst escapedSeparator = escapeStringRegexp(separator);\n\n\treturn string\n\t\t.replace(new RegExp(`${escapedSeparator}{2,}`, 'g'), separator)\n\t\t.replace(new RegExp(`^${escapedSeparator}|${escapedSeparator}$`, 'g'), '');\n};\n\nconst buildPatternSlug = options => {\n\tlet negationSetPattern = 'a-z\\\\d';\n\tnegationSetPattern += options.lowercase ? '' : 'A-Z';\n\n\tif (options.preserveCharacters.length > 0) {\n\t\tfor (const character of options.preserveCharacters) {\n\t\t\tif (character === options.separator) {\n\t\t\t\tthrow new Error(`The separator character \\`${options.separator}\\` cannot be included in preserved characters: ${options.preserveCharacters}`);\n\t\t\t}\n\n\t\t\tnegationSetPattern += escapeStringRegexp(character);\n\t\t}\n\t}\n\n\treturn new RegExp(`[^${negationSetPattern}]+`, 'g');\n};\n\nexport default function slugify(string, options) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError(`Expected a string, got \\`${typeof string}\\``);\n\t}\n\n\toptions = {\n\t\tseparator: '-',\n\t\tlowercase: true,\n\t\tdecamelize: true,\n\t\tcustomReplacements: [],\n\t\tpreserveLeadingUnderscore: false,\n\t\tpreserveTrailingDash: false,\n\t\tpreserveCharacters: [],\n\t\t...options\n\t};\n\n\tconst shouldPrependUnderscore = options.preserveLeadingUnderscore && string.startsWith('_');\n\tconst shouldAppendDash = options.preserveTrailingDash && string.endsWith('-');\n\n\tconst customReplacements = new Map([\n\t\t...builtinOverridableReplacements,\n\t\t...options.customReplacements\n\t]);\n\n\tstring = transliterate(string, {customReplacements});\n\n\tif (options.decamelize) {\n\t\tstring = decamelize(string);\n\t}\n\n\tconst patternSlug = buildPatternSlug(options);\n\n\tif (options.lowercase) {\n\t\tstring = string.toLowerCase();\n\t}\n\n\t// Detect contractions/possessives by looking for any word followed by a `'t`\n\t// or `'s` in isolation and then remove it.\n\tstring = string.replace(/([a-zA-Z\\d]+)'([ts])(\\s|$)/g, '$1$2$3');\n\n\tstring = string.replace(patternSlug, options.separator);\n\tstring = string.replace(/\\\\/g, '');\n\n\tif (options.separator) {\n\t\tstring = removeMootSeparators(string, options.separator);\n\t}\n\n\tif (shouldPrependUnderscore) {\n\t\tstring = `_${string}`;\n\t}\n\n\tif (shouldAppendDash) {\n\t\tstring = `${string}-`;\n\t}\n\n\treturn string;\n}\n\nexport function slugifyWithCounter() {\n\tconst occurrences = new Map();\n\n\tconst countable = (string, options) => {\n\t\tstring = slugify(string, options);\n\n\t\tif (!string) {\n\t\t\treturn '';\n\t\t}\n\n\t\tconst stringLower = string.toLowerCase();\n\t\tconst numberless = occurrences.get(stringLower.replace(/(?:-\\d+?)+?$/, '')) || 0;\n\t\tconst counter = occurrences.get(stringLower);\n\t\toccurrences.set(stringLower, typeof counter === 'number' ? counter + 1 : 1);\n\t\tconst newCounter = occurrences.get(stringLower) || 2;\n\t\tif (newCounter >= 2 || numberless > 2) {\n\t\t\tstring = `${string}-${newCounter}`;\n\t\t}\n\n\t\treturn string;\n\t};\n\n\tcountable.reset = () => {\n\t\toccurrences.clear();\n\t};\n\n\treturn countable;\n}\n"], "mappings": ";;;AAAe,SAAR,mBAAoC,QAAQ;AAClD,MAAI,OAAO,WAAW,UAAU;AAC/B,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACxC;AAIA,SAAO,OACL,QAAQ,uBAAuB,MAAM,EACrC,QAAQ,MAAM,OAAO;AACxB;;;ACVA,IAAM,eAAe;AAAA;AAAA,EAEpB,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA;AAAA,EAGV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA;AAAA,EAGV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA;AAAA,EAGV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,EAAE;AAAA,EACR,CAAC,KAAK,EAAE;AAAA,EACR,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,EAAE;AAAA,EACR,CAAC,KAAK,EAAE;AAAA,EACR,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA;AAAA,EAGV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,KAAK;AAAA;AAAA,EAGX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,EAAE;AAAA;AAAA,EAGR,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA;AAAA,EAGX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0CT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA;AAAA,EAGV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA;AAAA,EAGV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA;AAAA,EAGX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,KAAK,GAAG;AAAA;AAAA,EAGT,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,KAAK,IAAI;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA;AAAA,EAGX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA;AAAA,EAGX,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,MAAM,MAAM;AAAA,EACb,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,MAAM,MAAM;AAAA,EACb,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,MAAM,MAAM;AAAA,EACb,CAAC,MAAM,MAAM;AAAA,EACb,CAAC,MAAM,MAAM;AAAA,EACb,CAAC,MAAM,MAAM;AAAA,EACb,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,MAAM,IAAI;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AACX;AAEA,IAAO,uBAAQ;;;ACpgEf,IAAM,uBAAuB,CAAC,QAAQA,kBAAiB;AACtD,aAAW,CAAC,KAAK,KAAK,KAAKA,eAAc;AAExC,aAAS,OAAO,QAAQ,IAAI,OAAO,mBAAmB,GAAG,GAAG,GAAG,GAAG,KAAK;AAAA,EACxE;AAEA,SAAO;AACR;AAEe,SAAR,cAA+B,QAAQ,SAAS;AACtD,MAAI,OAAO,WAAW,UAAU;AAC/B,UAAM,IAAI,UAAU,4BAA4B,OAAO,MAAM,IAAI;AAAA,EAClE;AAEA,YAAU;AAAA,IACT,oBAAoB,CAAC;AAAA,IACrB,GAAG;AAAA,EACJ;AAEA,QAAM,qBAAqB,IAAI,IAAI;AAAA,IAClC,GAAG;AAAA,IACH,GAAG,QAAQ;AAAA,EACZ,CAAC;AAED,WAAS,OAAO,UAAU;AAC1B,WAAS,qBAAqB,QAAQ,kBAAkB;AACxD,WAAS,OAAO,UAAU,KAAK,EAAE,QAAQ,mBAAmB,EAAE,EAAE,UAAU;AAE1E,SAAO;AACR;;;AChCA,IAAM,0BAA0B;AAAA,EAC/B,CAAC,KAAK,OAAO;AAAA,EACb,CAAC,MAAM,WAAW;AAAA,EAClB,CAAC,KAAK,QAAQ;AACf;AAEA,IAAO,mCAAQ;;;ACFf,IAAM,aAAa,YAAU;AAC5B,SAAO,OAEL,QAAQ,qBAAqB,OAAO,EACpC,QAAQ,0BAA0B,OAAO,EAEzC,QAAQ,qBAAqB,OAAO,EAGpC,QAAQ,+BAA+B,OAAO;AACjD;AAEA,IAAM,uBAAuB,CAAC,QAAQ,cAAc;AACnD,QAAM,mBAAmB,mBAAmB,SAAS;AAErD,SAAO,OACL,QAAQ,IAAI,OAAO,GAAG,gBAAgB,QAAQ,GAAG,GAAG,SAAS,EAC7D,QAAQ,IAAI,OAAO,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,GAAG,GAAG,EAAE;AAC3E;AAEA,IAAM,mBAAmB,aAAW;AACnC,MAAI,qBAAqB;AACzB,wBAAsB,QAAQ,YAAY,KAAK;AAE/C,MAAI,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,eAAW,aAAa,QAAQ,oBAAoB;AACnD,UAAI,cAAc,QAAQ,WAAW;AACpC,cAAM,IAAI,MAAM,6BAA6B,QAAQ,SAAS,kDAAkD,QAAQ,kBAAkB,EAAE;AAAA,MAC7I;AAEA,4BAAsB,mBAAmB,SAAS;AAAA,IACnD;AAAA,EACD;AAEA,SAAO,IAAI,OAAO,KAAK,kBAAkB,MAAM,GAAG;AACnD;AAEe,SAAR,QAAyB,QAAQ,SAAS;AAChD,MAAI,OAAO,WAAW,UAAU;AAC/B,UAAM,IAAI,UAAU,4BAA4B,OAAO,MAAM,IAAI;AAAA,EAClE;AAEA,YAAU;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,oBAAoB,CAAC;AAAA,IACrB,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,IACtB,oBAAoB,CAAC;AAAA,IACrB,GAAG;AAAA,EACJ;AAEA,QAAM,0BAA0B,QAAQ,6BAA6B,OAAO,WAAW,GAAG;AAC1F,QAAM,mBAAmB,QAAQ,wBAAwB,OAAO,SAAS,GAAG;AAE5E,QAAM,qBAAqB,IAAI,IAAI;AAAA,IAClC,GAAG;AAAA,IACH,GAAG,QAAQ;AAAA,EACZ,CAAC;AAED,WAAS,cAAc,QAAQ,EAAC,mBAAkB,CAAC;AAEnD,MAAI,QAAQ,YAAY;AACvB,aAAS,WAAW,MAAM;AAAA,EAC3B;AAEA,QAAM,cAAc,iBAAiB,OAAO;AAE5C,MAAI,QAAQ,WAAW;AACtB,aAAS,OAAO,YAAY;AAAA,EAC7B;AAIA,WAAS,OAAO,QAAQ,+BAA+B,QAAQ;AAE/D,WAAS,OAAO,QAAQ,aAAa,QAAQ,SAAS;AACtD,WAAS,OAAO,QAAQ,OAAO,EAAE;AAEjC,MAAI,QAAQ,WAAW;AACtB,aAAS,qBAAqB,QAAQ,QAAQ,SAAS;AAAA,EACxD;AAEA,MAAI,yBAAyB;AAC5B,aAAS,IAAI,MAAM;AAAA,EACpB;AAEA,MAAI,kBAAkB;AACrB,aAAS,GAAG,MAAM;AAAA,EACnB;AAEA,SAAO;AACR;AAEO,SAAS,qBAAqB;AACpC,QAAM,cAAc,oBAAI,IAAI;AAE5B,QAAM,YAAY,CAAC,QAAQ,YAAY;AACtC,aAAS,QAAQ,QAAQ,OAAO;AAEhC,QAAI,CAAC,QAAQ;AACZ,aAAO;AAAA,IACR;AAEA,UAAM,cAAc,OAAO,YAAY;AACvC,UAAM,aAAa,YAAY,IAAI,YAAY,QAAQ,gBAAgB,EAAE,CAAC,KAAK;AAC/E,UAAM,UAAU,YAAY,IAAI,WAAW;AAC3C,gBAAY,IAAI,aAAa,OAAO,YAAY,WAAW,UAAU,IAAI,CAAC;AAC1E,UAAM,aAAa,YAAY,IAAI,WAAW,KAAK;AACnD,QAAI,cAAc,KAAK,aAAa,GAAG;AACtC,eAAS,GAAG,MAAM,IAAI,UAAU;AAAA,IACjC;AAEA,WAAO;AAAA,EACR;AAEA,YAAU,QAAQ,MAAM;AACvB,gBAAY,MAAM;AAAA,EACnB;AAEA,SAAO;AACR;", "names": ["replacements"]}
{"version": 3, "sources": ["../../@react-aria/utils/dist/packages/@react-aria/utils/src/useLayoutEffect.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useEffectEvent.ts", "../../@react-aria/ssr/dist/packages/@react-aria/ssr/src/SSRProvider.tsx", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useId.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/chain.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/domHelpers.ts", "../../@react-stately/flags/dist/packages/@react-stately/flags/src/index.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/shadowdom/DOMFunctions.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/mergeProps.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/mergeRefs.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/focusWithoutScrolling.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/platform.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/openLink.tsx", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/runAfterTransition.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useGlobalListeners.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useObjectRef.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useSyncRef.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useViewportSize.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/isVirtualEvent.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/isFocusable.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useHover.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/utils.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/textSelection.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/context.ts", "../../@swc/helpers/esm/_class_apply_descriptor_get.js", "../../@swc/helpers/esm/_class_extract_field_descriptor.js", "../../@swc/helpers/esm/_class_private_field_get.js", "../../@swc/helpers/esm/_check_private_redeclaration.js", "../../@swc/helpers/esm/_class_private_field_init.js", "../../@swc/helpers/esm/_class_apply_descriptor_set.js", "../../@swc/helpers/esm/_class_private_field_set.js", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/usePress.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocusVisible.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/focusSafely.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocus.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/createEventHandler.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useKeyboard.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocusable.tsx", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/Pressable.tsx", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/PressResponder.tsx", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocusWithin.ts", "../../@react-aria/focus/dist/packages/@react-aria/focus/src/useFocusRing.ts", "../../@react-aria/focus/dist/packages/@react-aria/focus/src/FocusScope.tsx", "../../@tanstack/virtual-core/src/utils.ts", "../../@tanstack/virtual-core/src/index.ts", "../../@tanstack/react-virtual/src/index.tsx", "../../tabbable/src/index.js"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useSSRSafeId} from '@react-aria/ssr';\nimport {useValueEffect} from './';\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nexport let idsUpdaterMap: Map<string, { current: string | null }[]> = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet registry;\nif (typeof FinalizationRegistry !== 'undefined') {\n  registry = new FinalizationRegistry<string>((heldValue) => {\n    idsUpdaterMap.delete(heldValue);\n  });\n}\n\n/**\n * If a default is not provided, generate an id.\n * @param defaultId - Default component id.\n */\nexport function useId(defaultId?: string): string {\n  let [value, setValue] = useState(defaultId);\n  let nextId = useRef(null);\n\n  let res = useSSRSafeId(value);\n  let cleanupRef = useRef(null);\n\n  if (registry) {\n    registry.register(cleanupRef, res);\n  }\n\n  if (canUseDOM) {\n    const cacheIdRef = idsUpdaterMap.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) {\n      cacheIdRef.push(nextId);\n    } else {\n      idsUpdaterMap.set(res, [nextId]);\n    }\n  }\n\n  useLayoutEffect(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if (registry) {\n        registry.unregister(cleanupRef);\n      }\n      idsUpdaterMap.delete(r);\n    };\n  }, [res]);\n\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  useEffect(() => {\n    let newId = nextId.current;\n    if (newId) { setValue(newId); }\n\n    return () => {\n      if (newId) { nextId.current = null; }\n    };\n  });\n\n  return res;\n}\n\n/**\n * Merges two ids.\n * Different ids will trigger a side-effect and re-render components hooked up with `useId`.\n */\nexport function mergeIds(idA: string, idB: string): string {\n  if (idA === idB) {\n    return idA;\n  }\n\n  let setIdsA = idsUpdaterMap.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => (ref.current = idB));\n    return idB;\n  }\n\n  let setIdsB = idsUpdaterMap.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach((ref) => (ref.current = idA));\n    return idA;\n  }\n\n  return idB;\n}\n\n/**\n * Used to generate an id, and after render, check if that id is rendered so we know\n * if we can use it in places such as labelledby.\n * @param depArray - When to recalculate if the id is in the DOM.\n */\nexport function useSlotId(depArray: ReadonlyArray<any> = []): string {\n  let id = useId();\n  let [resolvedId, setResolvedId] = useValueEffect(id);\n  let updateId = useCallback(() => {\n    setResolvedId(function *() {\n      yield id;\n\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n\n  useLayoutEffect(updateId, [id, updateId, ...depArray]);\n\n  return resolvedId;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Calls all functions in the order they were chained with the same arguments.\n */\nexport function chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n", "export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n", "// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from './chain';\nimport clsx from 'clsx';\nimport {mergeIds} from './useId';\n\ninterface Props {\n  [key: string]: any\n}\n\ntype PropsArg = Props | null | undefined;\n\n// taken from: https://stackoverflow.com/questions/51603250/typescript-3-parameter-list-intersection-type/51604379#51604379\ntype TupleTypes<T> = { [P in keyof T]: T[P] } extends { [key: number]: infer V } ? NullToObject<V> : never;\ntype NullToObject<T> = T extends (null | undefined) ? {} : T;\n\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n/**\n * Merges multiple props objects together. Event handlers are chained,\n * classNames are combined, and ids are deduplicated - different ids\n * will trigger a side-effect and re-render components hooked up with `useId`.\n * For all other props, the last prop object overrides all previous ones.\n * @param args - Multiple sets of props to merge together.\n */\nexport function mergeProps<T extends PropsArg[]>(...args: T): UnionToIntersection<TupleTypes<T>> {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result: Props = {...args[0]};\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n\n      // Chain events\n      if (\n        typeof a === 'function' &&\n        typeof b === 'function' &&\n        // This is a lot faster than a regex.\n        key[0] === 'o' &&\n        key[1] === 'n' &&\n        key.charCodeAt(2) >= /* 'A' */ 65 &&\n        key.charCodeAt(2) <= /* 'Z' */ 90\n      ) {\n        result[key] = chain(a, b);\n\n        // Merge classnames, sometimes classNames are empty string which eval to false, so we just need to do a type check\n      } else if (\n        (key === 'className' || key === 'UNSAFE_className') &&\n        typeof a === 'string' &&\n        typeof b === 'string'\n      ) {\n        result[key] = clsx(a, b);\n      } else if (key === 'id' && a && b) {\n        result.id = mergeIds(a, b);\n        // Override others\n      } else {\n        result[key] = b !== undefined ? b : a;\n      }\n    }\n  }\n\n  return result as UnionToIntersection<TupleTypes<T>>;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, Ref} from 'react';\n\n/**\n * Merges multiple refs into one. Works with either callback or object refs.\n */\nexport function mergeRefs<T>(...refs: Array<Ref<T> | MutableRefObject<T> | null | undefined>): Ref<T> {\n  if (refs.length === 1 && refs[0]) {\n    return refs[0];\n  }\n\n  return (value: T | null) => {\n    let hasCleanup = false;\n\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, value);\n      hasCleanup ||= typeof cleanup == 'function';\n      return cleanup;\n    });\n\n    if (hasCleanup) {\n      return () => {\n        cleanups.forEach((cleanup, i) => {\n          if (typeof cleanup === 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        });\n      };\n    }\n  };\n}\n\nfunction setRef<T>(ref: Ref<T> | MutableRefObject<T> | null | undefined, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref != null) {\n    ref.current = value;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  return (\n    window.navigator['userAgentData']?.brands.some((brand: {brand: string, version: string}) => re.test(brand.brand))\n  ) ||\n  re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n  \n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {focusWithoutScrolling, isMac, isWebKit} from './index';\nimport {Href, LinkDOMProps, RouterOptions} from '@react-types/shared';\nimport {isFirefox, isIPad} from './platform';\nimport React, {createContext, DOMAttributes, JSX, ReactNode, useContext, useMemo} from 'react';\n\ninterface Router {\n  isNative: boolean,\n  open: (target: Element, modifiers: Modifiers, href: Href, routerOptions: RouterOptions | undefined) => void,\n  useHref: (href: Href) => string\n}\n\nconst RouterContext = createContext<Router>({\n  isNative: true,\n  open: openSyntheticLink,\n  useHref: (href) => href\n});\n\ninterface RouterProviderProps {\n  navigate: (path: Href, routerOptions: RouterOptions | undefined) => void,\n  useHref?: (href: Href) => string,\n  children: ReactNode\n}\n\n/**\n * A RouterProvider accepts a `navigate` function from a framework or client side router,\n * and provides it to all nested React Aria links to enable client side navigation.\n */\nexport function RouterProvider(props: RouterProviderProps): JSX.Element {\n  let {children, navigate, useHref} = props;\n\n  let ctx = useMemo(() => ({\n    isNative: false,\n    open: (target: Element, modifiers: Modifiers, href: Href, routerOptions: RouterOptions | undefined) => {\n      getSyntheticLink(target, link => {\n        if (shouldClientNavigate(link, modifiers)) {\n          navigate(href, routerOptions);\n        } else {\n          openLink(link, modifiers);\n        }\n      });\n    },\n    useHref: useHref || ((href) => href)\n  }), [navigate, useHref]);\n\n  return (\n    <RouterContext.Provider value={ctx}>\n      {children}\n    </RouterContext.Provider>\n  );\n}\n\nexport function useRouter(): Router {\n  return useContext(RouterContext);\n}\n\ninterface Modifiers {\n  metaKey?: boolean,\n  ctrlKey?: boolean,\n  altKey?: boolean,\n  shiftKey?: boolean\n}\n\nexport function shouldClientNavigate(link: HTMLAnchorElement, modifiers: Modifiers): boolean {\n  // Use getAttribute here instead of link.target. Firefox will default link.target to \"_parent\" when inside an iframe.\n  let target = link.getAttribute('target');\n  return (\n    (!target || target === '_self') &&\n    link.origin === location.origin &&\n    !link.hasAttribute('download') &&\n    !modifiers.metaKey && // open in new tab (mac)\n    !modifiers.ctrlKey && // open in new tab (windows)\n    !modifiers.altKey && // download\n    !modifiers.shiftKey\n  );\n}\n\nexport function openLink(target: HTMLAnchorElement, modifiers: Modifiers, setOpening = true): void {\n  let {metaKey, ctrlKey, altKey, shiftKey} = modifiers;\n\n  // Firefox does not recognize keyboard events as a user action by default, and the popup blocker\n  // will prevent links with target=\"_blank\" from opening. However, it does allow the event if the\n  // Command/Control key is held, which opens the link in a background tab. This seems like the best we can do.\n  // See https://bugzilla.mozilla.org/show_bug.cgi?id=257870 and https://bugzilla.mozilla.org/show_bug.cgi?id=746640.\n  if (isFirefox() && window.event?.type?.startsWith('key') && target.target === '_blank') {\n    if (isMac()) {\n      metaKey = true;\n    } else {\n      ctrlKey = true;\n    }\n  }\n\n  // WebKit does not support firing click events with modifier keys, but does support keyboard events.\n  // https://github.com/WebKit/WebKit/blob/c03d0ac6e6db178f90923a0a63080b5ca210d25f/Source/WebCore/html/HTMLAnchorElement.cpp#L184\n  let event = isWebKit() && isMac() && !isIPad() && process.env.NODE_ENV !== 'test'\n    // @ts-ignore - keyIdentifier is a non-standard property, but it's what webkit expects\n    ? new KeyboardEvent('keydown', {keyIdentifier: 'Enter', metaKey, ctrlKey, altKey, shiftKey})\n    : new MouseEvent('click', {metaKey, ctrlKey, altKey, shiftKey, bubbles: true, cancelable: true});\n  (openLink as any).isOpening = setOpening;\n  focusWithoutScrolling(target);\n  target.dispatchEvent(event);\n  (openLink as any).isOpening = false;\n}\n// https://github.com/parcel-bundler/parcel/issues/8724\n(openLink as any).isOpening = false;\n\nfunction getSyntheticLink(target: Element, open: (link: HTMLAnchorElement) => void) {\n  if (target instanceof HTMLAnchorElement) {\n    open(target);\n  } else if (target.hasAttribute('data-href')) {\n    let link = document.createElement('a');\n    link.href = target.getAttribute('data-href')!;\n    if (target.hasAttribute('data-target')) {\n      link.target = target.getAttribute('data-target')!;\n    }\n    if (target.hasAttribute('data-rel')) {\n      link.rel = target.getAttribute('data-rel')!;\n    }\n    if (target.hasAttribute('data-download')) {\n      link.download = target.getAttribute('data-download')!;\n    }\n    if (target.hasAttribute('data-ping')) {\n      link.ping = target.getAttribute('data-ping')!;\n    }\n    if (target.hasAttribute('data-referrer-policy')) {\n      link.referrerPolicy = target.getAttribute('data-referrer-policy')!;\n    }\n    target.appendChild(link);\n    open(link);\n    target.removeChild(link);\n  }\n}\n\nfunction openSyntheticLink(target: Element, modifiers: Modifiers) {\n  getSyntheticLink(target, link => openLink(link, modifiers));\n}\n\nexport function useSyntheticLinkProps(props: LinkDOMProps): DOMAttributes<HTMLElement> {\n  let router = useRouter();\n  const href = router.useHref(props.href ?? '');\n  return {\n    'data-href': props.href ? href : undefined,\n    'data-target': props.target,\n    'data-rel': props.rel,\n    'data-download': props.download,\n    'data-ping': props.ping,\n    'data-referrer-policy': props.referrerPolicy\n  } as DOMAttributes<HTMLElement>;\n}\n\n/** @deprecated - For backward compatibility. */\nexport function getSyntheticLinkProps(props: LinkDOMProps): DOMAttributes<HTMLElement> {\n  return {\n    'data-href': props.href,\n    'data-target': props.target,\n    'data-rel': props.rel,\n    'data-download': props.download,\n    'data-ping': props.ping,\n    'data-referrer-policy': props.referrerPolicy\n  } as DOMAttributes<HTMLElement>;\n}\n\nexport function useLinkProps(props?: LinkDOMProps): LinkDOMProps {\n  let router = useRouter();\n  const href = router.useHref(props?.href ?? '');\n  return {\n    href: props?.href ? href : undefined,\n    target: props?.target,\n    rel: props?.rel,\n    download: props?.download,\n    ping: props?.ping,\n    referrerPolicy: props?.referrerPolicy\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet transitionsByElement = new Map<EventTarget, Set<string>>();\n\n// A list of callbacks to call once there are no transitioning elements.\nlet transitionCallbacks = new Set<() => void>();\n\nfunction setupGlobalEvents() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  function isTransitionEvent(event: Event): event is TransitionEvent {\n    return 'propertyName' in event;\n  }\n\n  let onTransitionStart = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Add the transitioning property to the list for this element.\n    let transitions = transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      transitionsByElement.set(e.target, transitions);\n\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n\n    transitions.add(e.propertyName);\n  };\n\n  let onTransitionEnd = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Remove property from list of transitioning properties.\n    let properties = transitionsByElement.get(e.target);\n    if (!properties) {\n      return;\n    }\n\n    properties.delete(e.propertyName);\n\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      transitionsByElement.delete(e.target);\n    }\n\n    // If no transitioning elements, call all of the queued callbacks.\n    if (transitionsByElement.size === 0) {\n      for (let cb of transitionCallbacks) {\n        cb();\n      }\n\n      transitionCallbacks.clear();\n    }\n  };\n\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\n\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') {\n    setupGlobalEvents();\n  } else {\n    document.addEventListener('DOMContentLoaded', setupGlobalEvents);\n  }\n}\n\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction cleanupDetachedElements() {\n  for (const [eventTarget] of transitionsByElement) {\n    // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n    // the explicit instanceof check, since it may be different in different contexts.\n    if ('isConnected' in eventTarget && !eventTarget.isConnected) {\n      transitionsByElement.delete(eventTarget);\n    }\n  }\n}\n\nexport function runAfterTransition(fn: () => void): void {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if (transitionsByElement.size === 0) {\n      fn();\n    } else {\n      transitionCallbacks.add(fn);\n    }\n  });\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n", "/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, useCallback, useMemo, useRef} from 'react';\n\n/**\n * Offers an object ref for a given callback ref or an object ref. Especially\n * helfpul when passing forwarded refs (created using `React.forwardRef`) to\n * React Aria hooks.\n *\n * @param ref The original ref intended to be used.\n * @returns An object ref that updates the given ref.\n * @see https://react.dev/reference/react/forwardRef\n */\nexport function useObjectRef<T>(ref?: ((instance: T | null) => (() => void) | void) | MutableRefObject<T | null> | null): MutableRefObject<T | null> {\n  const objRef: MutableRefObject<T | null> = useRef<T>(null);\n  const cleanupRef: MutableRefObject<(() => void) | void> = useRef(undefined);\n\n  const refEffect = useCallback(\n    (instance: T | null) => {\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return () => {\n          if (typeof refCleanup === 'function') {\n            refCleanup();\n          } else {\n            refCallback(null);\n          }\n        };\n      } else if (ref) {\n        ref.current = instance;\n        return () => {\n          ref.current = null;\n        };\n      }\n    },\n    [ref]\n  );\n\n  return useMemo(\n    () => ({\n      get current() {\n        return objRef.current;\n      },\n      set current(value) {\n        objRef.current = value;\n        if (cleanupRef.current) {\n          cleanupRef.current();\n          cleanupRef.current = undefined;\n        }\n\n        if (value != null) {\n          cleanupRef.current = refEffect(value);\n        }\n      }\n    }),\n    [refEffect]\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from './';\n\ninterface ContextValue<T> {\n  ref?: MutableRefObject<T | null>\n}\n\n// Syncs ref from context with ref passed to hook\nexport function useSyncRef<T>(context?: ContextValue<T> | null, ref?: RefObject<T | null>): void {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) {\n          context.ref.current = null;\n        }\n      };\n    }\n  });\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\ninterface ViewportSize {\n  width: number,\n  height: number\n}\n\nlet visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\nexport function useViewportSize(): ViewportSize {\n  let isSSR = useIsSSR();\n  let [size, setSize] = useState(() => isSSR ? {width: 0, height: 0} : getViewportSize());\n\n  useEffect(() => {\n    // Use visualViewport api to track available height even on iOS virtual keyboard opening\n    let onResize = () => {\n      setSize(size => {\n        let newSize = getViewportSize();\n        if (newSize.width === size.width && newSize.height === size.height) {\n          return size;\n        }\n        return newSize;\n      });\n    };\n\n    if (!visualViewport) {\n      window.addEventListener('resize', onResize);\n    } else {\n      visualViewport.addEventListener('resize', onResize);\n    }\n\n    return () => {\n      if (!visualViewport) {\n        window.removeEventListener('resize', onResize);\n      } else {\n        visualViewport.removeEventListener('resize', onResize);\n      }\n    };\n  }, []);\n\n  return size;\n}\n\nfunction getViewportSize(): ViewportSize {\n  return {\n    width: (visualViewport && visualViewport?.width) || window.innerWidth,\n    height: (visualViewport && visualViewport?.height) || window.innerHeight\n  };\n}\n", "/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n", "const focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (typeof PointerEvent !== 'undefined') {\n    document.addEventListener('pointerup', handleGlobalPointerEvent);\n  } else if (process.env.NODE_ENV === 'test') {\n    document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerDocument, isIOS, runAfterTransition} from '@react-aria/utils';\n\n// Safari on iOS starts selecting text on long press. The only way to avoid this, it seems,\n// is to add user-select: none to the entire page. Adding it to the pressable element prevents\n// that element from being selected, but nearby elements may still receive selection. We add\n// user-select: none on touch start, and remove it again on touch end to prevent this.\n// This must be implemented using global state to avoid race conditions between multiple elements.\n\n// There are three possible states due to the delay before removing user-select: none after\n// pointer up. The 'default' state always transitions to the 'disabled' state, which transitions\n// to 'restoring'. The 'restoring' state can either transition back to 'disabled' or 'default'.\n\n// For non-iOS devices, we apply user-select: none to the pressed element instead to avoid possible\n// performance issues that arise from applying and removing user-select: none to the entire page\n// (see https://github.com/adobe/react-spectrum/issues/1609).\ntype State = 'default' | 'disabled' | 'restoring';\n\n// Note that state only matters here for iOS. Non-iOS gets user-select: none applied to the target element\n// rather than at the document level so we just need to apply/remove user-select: none for each pressed element individually\nlet state: State = 'default';\nlet savedUserSelect = '';\nlet modifiedElementMap = new WeakMap<Element, string>();\n\nexport function disableTextSelection(target?: Element): void {\n  if (isIOS()) {\n    if (state === 'default') {\n\n      const documentObject = getOwnerDocument(target);\n      savedUserSelect = documentObject.documentElement.style.webkitUserSelect;\n      documentObject.documentElement.style.webkitUserSelect = 'none';\n    }\n\n    state = 'disabled';\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, store the target's original user-select and change to user-select: none\n    // Ignore state since it doesn't apply for non iOS\n    let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n    modifiedElementMap.set(target, target.style[property]);\n    target.style[property] = 'none';\n  }\n}\n\nexport function restoreTextSelection(target?: Element): void {\n  if (isIOS()) {\n    // If the state is already default, there's nothing to do.\n    // If it is restoring, then there's no need to queue a second restore.\n    if (state !== 'disabled') {\n      return;\n    }\n\n    state = 'restoring';\n\n    // There appears to be a delay on iOS where selection still might occur\n    // after pointer up, so wait a bit before removing user-select.\n    setTimeout(() => {\n      // Wait for any CSS transitions to complete so we don't recompute style\n      // for the whole page in the middle of the animation and cause jank.\n      runAfterTransition(() => {\n        // Avoid race conditions\n        if (state === 'restoring') {\n\n          const documentObject = getOwnerDocument(target);\n          if (documentObject.documentElement.style.webkitUserSelect === 'none') {\n            documentObject.documentElement.style.webkitUserSelect = savedUserSelect || '';\n          }\n\n          savedUserSelect = '';\n          state = 'default';\n        }\n      });\n    }, 300);\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, restore the target's original user-select if any\n    // Ignore state since it doesn't apply for non iOS\n    if (target && modifiedElementMap.has(target)) {\n      let targetOldUserSelect = modifiedElementMap.get(target) as string;\n      let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n\n      if (target.style[property] === 'none') {\n        target.style[property] = targetOldUserSelect;\n      }\n\n      if (target.getAttribute('style') === '') {\n        target.removeAttribute('style');\n      }\n      modifiedElementMap.delete(target);\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {PressProps} from './usePress';\nimport React, {MutableRefObject} from 'react';\n\ninterface IPressResponderContext extends PressProps {\n  register(): void,\n  ref?: MutableRefObject<FocusableElement>\n}\n\nexport const PressResponderContext = React.createContext<IPressResponderContext>({register: () => {}});\nPressResponderContext.displayName = 'PressResponderContext';\n", "function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n", "function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n", "import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n", "function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n", "import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n", "function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n", "import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {\n  chain,\n  focusWithoutScrolling,\n  getEventTarget,\n  getOwnerDocument,\n  getOwnerWindow,\n  isMac,\n  isVirtualClick,\n  isVirtualPointerEvent,\n  mergeProps,\n  nodeContains,\n  openLink,\n  useEffectEvent,\n  useGlobalListeners,\n  useSyncRef\n} from '@react-aria/utils';\nimport {createSyntheticEvent, preventFocus, setEventTarget} from './utils';\nimport {disableTextSelection, restoreTextSelection} from './textSelection';\nimport {DOMAttributes, FocusableElement, PressEvent as IPressEvent, PointerType, PressEvents, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {PressResponderContext} from './context';\nimport {MouseEvent as RMouseEvent, TouchEvent as RTouchEvent, useContext, useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface PressProps extends PressEvents {\n  /** Whether the target is in a controlled press state (e.g. an overlay it triggers is open). */\n  isPressed?: boolean,\n  /** Whether the press events should be disabled. */\n  isDisabled?: boolean,\n  /** Whether the target should not receive focus on press. */\n  preventFocusOnPress?: boolean,\n  /**\n   * Whether press events should be canceled when the pointer leaves the target while pressed.\n   * By default, this is `false`, which means if the pointer returns back over the target while\n   * still pressed, onPressStart will be fired again. If set to `true`, the press is canceled\n   * when the pointer leaves the target and onPressStart will not be fired if the pointer returns.\n   */\n  shouldCancelOnPointerExit?: boolean,\n  /** Whether text selection should be enabled on the pressable element. */\n  allowTextSelectionOnPress?: boolean\n}\n\nexport interface PressHookProps extends PressProps {\n  /** A ref to the target element. */\n  ref?: RefObject<Element | null>\n}\n\ninterface PressState {\n  isPressed: boolean,\n  ignoreEmulatedMouseEvents: boolean,\n  didFirePressStart: boolean,\n  isTriggeringEvent: boolean,\n  activePointerId: any,\n  target: FocusableElement | null,\n  isOverTarget: boolean,\n  pointerType: PointerType | null,\n  userSelect?: string,\n  metaKeyEvents?: Map<string, KeyboardEvent>,\n  disposables: Array<() => void>\n}\n\ninterface EventBase {\n  currentTarget: EventTarget | null,\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean,\n  clientX?: number,\n  clientY?: number,\n  targetTouches?: Array<{clientX?: number, clientY?: number}>\n}\n\nexport interface PressResult {\n  /** Whether the target is currently pressed. */\n  isPressed: boolean,\n  /** Props to spread on the target element. */\n  pressProps: DOMAttributes\n}\n\nfunction usePressResponderContext(props: PressHookProps): PressHookProps {\n  // Consume context from <PressResponder> and merge with props.\n  let context = useContext(PressResponderContext);\n  if (context) {\n    let {register, ...contextProps} = context;\n    props = mergeProps(contextProps, props) as PressHookProps;\n    register();\n  }\n  useSyncRef(context, props.ref);\n\n  return props;\n}\n\nclass PressEvent implements IPressEvent {\n  type: IPressEvent['type'];\n  pointerType: PointerType;\n  target: Element;\n  shiftKey: boolean;\n  ctrlKey: boolean;\n  metaKey: boolean;\n  altKey: boolean;\n  x: number;\n  y: number;\n  #shouldStopPropagation = true;\n\n  constructor(type: IPressEvent['type'], pointerType: PointerType, originalEvent: EventBase, state?: PressState) {\n    let currentTarget = state?.target ?? originalEvent.currentTarget;\n    const rect: DOMRect | undefined = (currentTarget as Element)?.getBoundingClientRect();\n    let x, y = 0;\n    let clientX, clientY: number | null = null;\n    if (originalEvent.clientX != null && originalEvent.clientY != null) {\n      clientX = originalEvent.clientX;\n      clientY = originalEvent.clientY;\n    }\n    if (rect) {\n      if (clientX != null && clientY != null) {\n        x = clientX - rect.left;\n        y = clientY - rect.top;\n      } else {\n        x = rect.width / 2;\n        y = rect.height / 2;\n      }\n    }\n    this.type = type;\n    this.pointerType = pointerType;\n    this.target = originalEvent.currentTarget as Element;\n    this.shiftKey = originalEvent.shiftKey;\n    this.metaKey = originalEvent.metaKey;\n    this.ctrlKey = originalEvent.ctrlKey;\n    this.altKey = originalEvent.altKey;\n    this.x = x;\n    this.y = y;\n  }\n\n  continuePropagation() {\n    this.#shouldStopPropagation = false;\n  }\n\n  get shouldStopPropagation() {\n    return this.#shouldStopPropagation;\n  }\n}\n\nconst LINK_CLICKED = Symbol('linkClicked');\nconst STYLE_ID = 'react-aria-pressable-style';\nconst PRESSABLE_ATTRIBUTE = 'data-react-aria-pressable';\n\n/**\n * Handles press interactions across mouse, touch, keyboard, and screen readers.\n * It normalizes behavior across browsers and platforms, and handles many nuances\n * of dealing with pointer and keyboard events.\n */\nexport function usePress(props: PressHookProps): PressResult {\n  let {\n    onPress,\n    onPressChange,\n    onPressStart,\n    onPressEnd,\n    onPressUp,\n    onClick,\n    isDisabled,\n    isPressed: isPressedProp,\n    preventFocusOnPress,\n    shouldCancelOnPointerExit,\n    allowTextSelectionOnPress,\n    ref: domRef,\n    ...domProps\n  } = usePressResponderContext(props);\n\n  let [isPressed, setPressed] = useState(false);\n  let ref = useRef<PressState>({\n    isPressed: false,\n    ignoreEmulatedMouseEvents: false,\n    didFirePressStart: false,\n    isTriggeringEvent: false,\n    activePointerId: null,\n    target: null,\n    isOverTarget: false,\n    pointerType: null,\n    disposables: []\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let triggerPressStart = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled || state.didFirePressStart) {\n      return false;\n    }\n\n    let shouldStopPropagation = true;\n    state.isTriggeringEvent = true;\n    if (onPressStart) {\n      let event = new PressEvent('pressstart', pointerType, originalEvent);\n      onPressStart(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(true);\n    }\n\n    state.isTriggeringEvent = false;\n    state.didFirePressStart = true;\n    setPressed(true);\n    return shouldStopPropagation;\n  });\n\n  let triggerPressEnd = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, wasPressed = true) => {\n    let state = ref.current;\n    if (!state.didFirePressStart) {\n      return false;\n    }\n\n    state.didFirePressStart = false;\n    state.isTriggeringEvent = true;\n\n    let shouldStopPropagation = true;\n    if (onPressEnd) {\n      let event = new PressEvent('pressend', pointerType, originalEvent);\n      onPressEnd(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(false);\n    }\n\n    setPressed(false);\n\n    if (onPress && wasPressed && !isDisabled) {\n      let event = new PressEvent('press', pointerType, originalEvent);\n      onPress(event);\n      shouldStopPropagation &&= event.shouldStopPropagation;\n    }\n\n    state.isTriggeringEvent = false;\n    return shouldStopPropagation;\n  });\n\n  let triggerPressUp = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled) {\n      return false;\n    }\n\n    if (onPressUp) {\n      state.isTriggeringEvent = true;\n      let event = new PressEvent('pressup', pointerType, originalEvent);\n      onPressUp(event);\n      state.isTriggeringEvent = false;\n      return event.shouldStopPropagation;\n    }\n\n    return true;\n  });\n\n  let cancel = useEffectEvent((e: EventBase) => {\n    let state = ref.current;\n    if (state.isPressed && state.target) {\n      if (state.didFirePressStart && state.pointerType != null) {\n        triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n      }\n      state.isPressed = false;\n      state.isOverTarget = false;\n      state.activePointerId = null;\n      state.pointerType = null;\n      removeAllGlobalListeners();\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    }\n  });\n\n  let cancelOnPointerExit = useEffectEvent((e: EventBase) => {\n    if (shouldCancelOnPointerExit) {\n      cancel(e);\n    }\n  });\n\n  let triggerClick = useEffectEvent((e: RMouseEvent<FocusableElement>) => {\n    onClick?.(e);\n  });\n\n  let triggerSyntheticClick = useEffectEvent((e: KeyboardEvent | TouchEvent, target: FocusableElement) => {\n    // Some third-party libraries pass in onClick instead of onPress.\n    // Create a fake mouse event and trigger onClick as well.\n    // This matches the browser's native activation behavior for certain elements (e.g. button).\n    // https://html.spec.whatwg.org/#activation\n    // https://html.spec.whatwg.org/#fire-a-synthetic-pointer-event\n    if (onClick) {\n      let event = new MouseEvent('click', e);\n      setEventTarget(event, target);\n      onClick(createSyntheticEvent(event));\n    }\n  });\n\n  let pressProps = useMemo(() => {\n    let state = ref.current;\n    let pressProps: DOMAttributes = {\n      onKeyDown(e) {\n        if (isValidKeyboardEvent(e.nativeEvent, e.currentTarget) && nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          if (shouldPreventDefaultKeyboard(getEventTarget(e.nativeEvent), e.key)) {\n            e.preventDefault();\n          }\n\n          // If the event is repeating, it may have started on a different element\n          // after which focus moved to the current element. Ignore these events and\n          // only handle the first key down event.\n          let shouldStopPropagation = true;\n          if (!state.isPressed && !e.repeat) {\n            state.target = e.currentTarget;\n            state.isPressed = true;\n            state.pointerType = 'keyboard';\n            shouldStopPropagation = triggerPressStart(e, 'keyboard');\n\n            // Focus may move before the key up event, so register the event on the document\n            // instead of the same element where the key down event occurred. Make it capturing so that it will trigger\n            // before stopPropagation from useKeyboard on a child element may happen and thus we can still call triggerPress for the parent element.\n            let originalTarget = e.currentTarget;\n            let pressUp = (e) => {\n              if (isValidKeyboardEvent(e, originalTarget) && !e.repeat && nodeContains(originalTarget, getEventTarget(e)) && state.target) {\n                triggerPressUp(createEvent(state.target, e), 'keyboard');\n              }\n            };\n\n            addGlobalListener(getOwnerDocument(e.currentTarget), 'keyup', chain(pressUp, onKeyUp), true);\n          }\n\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n\n          // Keep track of the keydown events that occur while the Meta (e.g. Command) key is held.\n          // macOS has a bug where keyup events are not fired while the Meta key is down.\n          // When the Meta key itself is released we will get an event for that, and we'll act as if\n          // all of these other keys were released as well.\n          // https://bugs.chromium.org/p/chromium/issues/detail?id=1393524\n          // https://bugs.webkit.org/show_bug.cgi?id=55291\n          // https://bugzilla.mozilla.org/show_bug.cgi?id=1299553\n          if (e.metaKey && isMac()) {\n            state.metaKeyEvents?.set(e.key, e.nativeEvent);\n          }\n        } else if (e.key === 'Meta') {\n          state.metaKeyEvents = new Map();\n        }\n      },\n      onClick(e) {\n        if (e && !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e && e.button === 0 && !state.isTriggeringEvent && !(openLink as any).isOpening) {\n          let shouldStopPropagation = true;\n          if (isDisabled) {\n            e.preventDefault();\n          }\n          \n          // If triggered from a screen reader or by using element.click(),\n          // trigger as if it were a keyboard click.\n          if (!state.ignoreEmulatedMouseEvents && !state.isPressed && (state.pointerType === 'virtual' || isVirtualClick(e.nativeEvent))) {\n            let stopPressStart = triggerPressStart(e, 'virtual');\n            let stopPressUp = triggerPressUp(e, 'virtual');\n            let stopPressEnd = triggerPressEnd(e, 'virtual');\n            triggerClick(e);\n            shouldStopPropagation = stopPressStart && stopPressUp && stopPressEnd;\n          } else if (state.isPressed && state.pointerType !== 'keyboard') {\n            let pointerType = state.pointerType || (e.nativeEvent as PointerEvent).pointerType as PointerType || 'virtual';\n            let stopPressUp = triggerPressUp(createEvent(e.currentTarget, e), pointerType);\n            let stopPressEnd =  triggerPressEnd(createEvent(e.currentTarget, e), pointerType, true);\n            shouldStopPropagation = stopPressUp && stopPressEnd;\n            state.isOverTarget = false;\n            triggerClick(e);\n            cancel(e);\n          }\n\n          state.ignoreEmulatedMouseEvents = false;\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n        }\n      }\n    };\n\n    let onKeyUp = (e: KeyboardEvent) => {\n      if (state.isPressed && state.target && isValidKeyboardEvent(e, state.target)) {\n        if (shouldPreventDefaultKeyboard(getEventTarget(e), e.key)) {\n          e.preventDefault();\n        }\n\n        let target = getEventTarget(e);\n        let wasPressed = nodeContains(state.target, getEventTarget(e));\n        triggerPressEnd(createEvent(state.target, e), 'keyboard', wasPressed);\n        if (wasPressed) {\n          triggerSyntheticClick(e, state.target);\n        }\n        removeAllGlobalListeners();\n\n        // If a link was triggered with a key other than Enter, open the URL ourselves.\n        // This means the link has a role override, and the default browser behavior\n        // only applies when using the Enter key.\n        if (e.key !== 'Enter' && isHTMLAnchorLink(state.target) && nodeContains(state.target, target) && !e[LINK_CLICKED]) {\n          // Store a hidden property on the event so we only trigger link click once,\n          // even if there are multiple usePress instances attached to the element.\n          e[LINK_CLICKED] = true;\n          openLink(state.target, e, false);\n        }\n\n        state.isPressed = false;\n        state.metaKeyEvents?.delete(e.key);\n      } else if (e.key === 'Meta' && state.metaKeyEvents?.size) {\n        // If we recorded keydown events that occurred while the Meta key was pressed,\n        // and those haven't received keyup events already, fire keyup events ourselves.\n        // See comment above for more info about the macOS bug causing this.\n        let events = state.metaKeyEvents;\n        state.metaKeyEvents = undefined;\n        for (let event of events.values()) {\n          state.target?.dispatchEvent(new KeyboardEvent('keyup', event));\n        }\n      }\n    };\n\n    if (typeof PointerEvent !== 'undefined') {\n      pressProps.onPointerDown = (e) => {\n        // Only handle left clicks, and ignore events that bubbled through portals.\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // iOS safari fires pointer events from VoiceOver with incorrect coordinates/target.\n        // Ignore and let the onClick handler take care of it instead.\n        // https://bugs.webkit.org/show_bug.cgi?id=222627\n        // https://bugs.webkit.org/show_bug.cgi?id=223202\n        if (isVirtualPointerEvent(e.nativeEvent)) {\n          state.pointerType = 'virtual';\n          return;\n        }\n\n        state.pointerType = e.pointerType;\n\n        let shouldStopPropagation = true;\n        if (!state.isPressed) {\n          state.isPressed = true;\n          state.isOverTarget = true;\n          state.activePointerId = e.pointerId;\n          state.target = e.currentTarget as FocusableElement;\n\n          if (!allowTextSelectionOnPress) {\n            disableTextSelection(state.target);\n          }\n\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n\n          // Release pointer capture so that touch interactions can leave the original target.\n          // This enables onPointerLeave and onPointerEnter to fire.\n          let target = getEventTarget(e.nativeEvent);\n          if ('releasePointerCapture' in target) {\n            target.releasePointerCapture(e.pointerId);\n          }\n\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointerup', onPointerUp, false);\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointercancel', onPointerCancel, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseDown = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e.button === 0) {\n          if (preventFocusOnPress) {\n            let dispose = preventFocus(e.target as FocusableElement);\n            if (dispose) {\n              state.disposables.push(dispose);\n            }\n          }\n\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onPointerUp = (e) => {\n        // iOS fires pointerup with zero width and height, so check the pointerType recorded during pointerdown.\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent)) || state.pointerType === 'virtual') {\n          return;\n        }\n\n        // Only handle left clicks. If isPressed is true, delay until onClick.\n        if (e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || e.pointerType);\n        }\n      };\n\n      pressProps.onPointerEnter = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && !state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = true;\n          triggerPressStart(createEvent(state.target, e), state.pointerType);\n        }\n      };\n\n      pressProps.onPointerLeave = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.activePointerId && state.isPressed && e.button === 0 && state.target) {\n          if (nodeContains(state.target, getEventTarget(e)) && state.pointerType != null) {\n            // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n            // is mutated between onPointerUp and onClick, and is more compatible with third party libraries.\n            // https://github.com/adobe/react-spectrum/issues/1513\n            // https://issues.chromium.org/issues/40732224\n            // However, iOS and Android do not focus or fire onClick after a long press.\n            // We work around this by triggering a click ourselves after a timeout.\n            // This timeout is canceled during the click event in case the real one fires first.\n            // The timeout must be at least 32ms, because Safari on iOS delays the click event on\n            // non-form elements without certain ARIA roles (for hover emulation).\n            // https://github.com/WebKit/WebKit/blob/dccfae42bb29bd4bdef052e469f604a9387241c0/Source/WebKit/WebProcess/WebPage/ios/WebPageIOS.mm#L875-L892\n            let clicked = false;\n            let timeout = setTimeout(() => {\n              if (state.isPressed && state.target instanceof HTMLElement) {\n                if (clicked) {\n                  cancel(e);\n                } else {\n                  focusWithoutScrolling(state.target);\n                  state.target.click();\n                }\n              }\n            }, 80);\n            // Use a capturing listener to track if a click occurred.\n            // If stopPropagation is called it may never reach our handler.\n            addGlobalListener(e.currentTarget as Document, 'click', () => clicked = true, true);\n            state.disposables.push(() => clearTimeout(timeout));\n          } else {\n            cancel(e);\n          }\n\n          // Ignore subsequent onPointerLeave event before onClick on touch devices.\n          state.isOverTarget = false;\n        }\n      };\n\n      let onPointerCancel = (e: PointerEvent) => {\n        cancel(e);\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // Safari does not call onPointerCancel when a drag starts, whereas Chrome and Firefox do.\n        cancel(e);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      // NOTE: this fallback branch is entirely used by unit tests.\n      // All browsers now support pointer events, but JSDOM still does not.\n\n      pressProps.onMouseDown = (e) => {\n        // Only handle left clicks\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          e.stopPropagation();\n          return;\n        }\n\n        state.isPressed = true;\n        state.isOverTarget = true;\n        state.target = e.currentTarget;\n        state.pointerType = isVirtualClick(e.nativeEvent) ? 'virtual' : 'mouse';\n\n        // Flush sync so that focus moved during react re-renders occurs before we yield back to the browser.\n        let shouldStopPropagation = flushSync(() => triggerPressStart(e, state.pointerType!));\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        if (preventFocusOnPress) {\n          let dispose = preventFocus(e.target as FocusableElement);\n          if (dispose) {\n            state.disposables.push(dispose);\n          }\n        }\n\n        addGlobalListener(getOwnerDocument(e.currentTarget), 'mouseup', onMouseUp, false);\n      };\n\n      pressProps.onMouseEnter = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = true;\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseLeave = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(e, state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseUp = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.ignoreEmulatedMouseEvents && e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || 'mouse');\n        }\n      };\n\n      let onMouseUp = (e: MouseEvent) => {\n        // Only handle left clicks\n        if (e.button !== 0) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n          return;\n        }\n\n        if (state.target && state.target.contains(e.target as Element) && state.pointerType != null) {\n          // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n          // is mutated between onMouseUp and onClick, and is more compatible with third party libraries.\n        } else {\n          cancel(e);\n        }\n\n        state.isOverTarget = false;\n      };\n\n      pressProps.onTouchStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let touch = getTouchFromEvent(e.nativeEvent);\n        if (!touch) {\n          return;\n        }\n        state.activePointerId = touch.identifier;\n        state.ignoreEmulatedMouseEvents = true;\n        state.isOverTarget = true;\n        state.isPressed = true;\n        state.target = e.currentTarget;\n        state.pointerType = 'touch';\n\n        if (!allowTextSelectionOnPress) {\n          disableTextSelection(state.target);\n        }\n\n        let shouldStopPropagation = triggerPressStart(createTouchEvent(state.target, e), state.pointerType);\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        addGlobalListener(getOwnerWindow(e.currentTarget), 'scroll', onScroll, true);\n      };\n\n      pressProps.onTouchMove = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget)) {\n          if (!state.isOverTarget && state.pointerType != null) {\n            state.isOverTarget = true;\n            shouldStopPropagation = triggerPressStart(createTouchEvent(state.target!, e), state.pointerType);\n          }\n        } else if (state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n          cancelOnPointerExit(createTouchEvent(state.target!, e));\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onTouchEnd = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget) && state.pointerType != null) {\n          triggerPressUp(createTouchEvent(state.target!, e), state.pointerType);\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType);\n          triggerSyntheticClick(e.nativeEvent, state.target!);\n        } else if (state.isOverTarget && state.pointerType != null) {\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        state.isPressed = false;\n        state.activePointerId = null;\n        state.isOverTarget = false;\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.target && !allowTextSelectionOnPress) {\n          restoreTextSelection(state.target);\n        }\n        removeAllGlobalListeners();\n      };\n\n      pressProps.onTouchCancel = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        e.stopPropagation();\n        if (state.isPressed) {\n          cancel(createTouchEvent(state.target!, e));\n        }\n      };\n\n      let onScroll = (e: Event) => {\n        if (state.isPressed && nodeContains(getEventTarget(e), state.target)) {\n          cancel({\n            currentTarget: state.target,\n            shiftKey: false,\n            ctrlKey: false,\n            metaKey: false,\n            altKey: false\n          });\n        }\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        cancel(e);\n      };\n    }\n\n    return pressProps;\n  }, [\n    addGlobalListener,\n    isDisabled,\n    preventFocusOnPress,\n    removeAllGlobalListeners,\n    allowTextSelectionOnPress,\n    cancel,\n    cancelOnPointerExit,\n    triggerPressEnd,\n    triggerPressStart,\n    triggerPressUp,\n    triggerClick,\n    triggerSyntheticClick\n  ]);\n\n  // Avoid onClick delay for double tap to zoom by default.\n  useEffect(() => {\n    if (!domRef || process.env.NODE_ENV === 'test') {\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(domRef.current);\n    if (!ownerDocument || !ownerDocument.head || ownerDocument.getElementById(STYLE_ID)) {\n      return;\n    }\n\n    const style = ownerDocument.createElement('style');\n    style.id = STYLE_ID;\n    // touchAction: 'manipulation' is supposed to be equivalent, but in\n    // Safari it causes onPointerCancel not to fire on scroll.\n    // https://bugs.webkit.org/show_bug.cgi?id=240917\n    style.textContent = `\n@layer {\n  [${PRESSABLE_ATTRIBUTE}] {\n    touch-action: pan-x pan-y pinch-zoom;\n  }\n}\n    `.trim();\n    ownerDocument.head.prepend(style);\n  }, [domRef]);\n\n  // Remove user-select: none in case component unmounts immediately after pressStart\n  useEffect(() => {\n    let state = ref.current;\n    return () => {\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target ?? undefined);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    };\n  }, [allowTextSelectionOnPress]);\n\n  return {\n    isPressed: isPressedProp || isPressed,\n    pressProps: mergeProps(domProps, pressProps, {[PRESSABLE_ATTRIBUTE]: true})\n  };\n}\n\nfunction isHTMLAnchorLink(target: Element): target is HTMLAnchorElement {\n  return target.tagName === 'A' && target.hasAttribute('href');\n}\n\nfunction isValidKeyboardEvent(event: KeyboardEvent, currentTarget: Element): boolean {\n  const {key, code} = event;\n  const element = currentTarget as HTMLElement;\n  const role = element.getAttribute('role');\n  // Accessibility for keyboards. Space and Enter only.\n  // \"Spacebar\" is for IE 11\n  return (\n    (key === 'Enter' || key === ' ' || key === 'Spacebar' || code === 'Space') &&\n    !((element instanceof getOwnerWindow(element).HTMLInputElement && !isValidInputKey(element, key)) ||\n      element instanceof getOwnerWindow(element).HTMLTextAreaElement ||\n      element.isContentEditable) &&\n    // Links should only trigger with Enter key\n    !((role === 'link' || (!role && isHTMLAnchorLink(element))) && key !== 'Enter')\n  );\n}\n\nfunction getTouchFromEvent(event: TouchEvent): Touch | null {\n  const {targetTouches} = event;\n  if (targetTouches.length > 0) {\n    return targetTouches[0];\n  }\n  return null;\n}\n\nfunction getTouchById(\n  event: TouchEvent,\n  pointerId: null | number\n): null | Touch {\n  const changedTouches = event.changedTouches;\n  for (let i = 0; i < changedTouches.length; i++) {\n    const touch = changedTouches[i];\n    if (touch.identifier === pointerId) {\n      return touch;\n    }\n  }\n  return null;\n}\n\nfunction createTouchEvent(target: FocusableElement, e: RTouchEvent<FocusableElement>): EventBase {\n  let clientX = 0;\n  let clientY = 0;\n  if (e.targetTouches && e.targetTouches.length === 1) {\n    clientX = e.targetTouches[0].clientX;\n    clientY = e.targetTouches[0].clientY;\n  }\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\nfunction createEvent(target: FocusableElement, e: EventBase): EventBase {\n  let clientX = e.clientX;\n  let clientY = e.clientY;\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\ninterface Rect {\n  top: number,\n  right: number,\n  bottom: number,\n  left: number\n}\n\ninterface EventPoint {\n  clientX: number,\n  clientY: number,\n  width?: number,\n  height?: number,\n  radiusX?: number,\n  radiusY?: number\n}\n\nfunction getPointClientRect(point: EventPoint): Rect {\n  let offsetX = 0;\n  let offsetY = 0;\n  if (point.width !== undefined) {\n    offsetX = (point.width / 2);\n  } else if (point.radiusX !== undefined) {\n    offsetX = point.radiusX;\n  }\n  if (point.height !== undefined) {\n    offsetY = (point.height / 2);\n  } else if (point.radiusY !== undefined) {\n    offsetY = point.radiusY;\n  }\n\n  return {\n    top: point.clientY - offsetY,\n    right: point.clientX + offsetX,\n    bottom: point.clientY + offsetY,\n    left: point.clientX - offsetX\n  };\n}\n\nfunction areRectanglesOverlapping(a: Rect, b: Rect) {\n  // check if they cannot overlap on x axis\n  if (a.left > b.right || b.left > a.right) {\n    return false;\n  }\n  // check if they cannot overlap on y axis\n  if (a.top > b.bottom || b.top > a.bottom) {\n    return false;\n  }\n  return true;\n}\n\nfunction isOverTarget(point: EventPoint, target: Element) {\n  let rect = target.getBoundingClientRect();\n  let pointRect = getPointClientRect(point);\n  return areRectanglesOverlapping(rect, pointRect);\n}\n\nfunction shouldPreventDefaultUp(target: Element) {\n  if (target instanceof HTMLInputElement) {\n    return false;\n  }\n\n  if (target instanceof HTMLButtonElement) {\n    return target.type !== 'submit' && target.type !== 'reset';\n  }\n\n  if (isHTMLAnchorLink(target)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction shouldPreventDefaultKeyboard(target: Element, key: string) {\n  if (target instanceof HTMLInputElement) {\n    return !isValidInputKey(target, key);\n  }\n\n  return shouldPreventDefaultUp(target);\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\nfunction isValidInputKey(target: HTMLInputElement, key: string) {\n  // Only space should toggle checkboxes and radios, not enter.\n  return target.type === 'checkbox' || target.type === 'radio'\n    ? key === ' '\n    : nonTextInputTypes.has(target.type);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {\n  focusWithoutScrolling,\n  getActiveElement,\n  getOwnerDocument,\n  runAfterTransition\n} from '@react-aria/utils';\nimport {getInteractionModality} from './useFocusVisible';\n\n/**\n * A utility function that focuses an element while avoiding undesired side effects such\n * as page scrolling and screen reader issues with CSS transitions.\n */\nexport function focusSafely(element: FocusableElement): void {\n  // If the user is interacting with a virtual cursor, e.g. screen reader, then\n  // wait until after any animated transitions that are currently occurring on\n  // the page before shifting focus. This avoids issues with VoiceOver on iOS\n  // causing the page to scroll when moving focus if the element is transitioning\n  // from off the screen.\n  const ownerDocument = getOwnerDocument(element);\n  const activeElement = getActiveElement(ownerDocument);\n  if (getInteractionModality() === 'virtual') {\n    let lastFocusedElement = activeElement;\n    runAfterTransition(() => {\n      // If focus did not move and the element is still in the document, focus it.\n      if (getActiveElement(ownerDocument) === lastFocusedElement && element.isConnected) {\n        focusWithoutScrolling(element);\n      }\n    });\n  } else {\n    focusWithoutScrolling(element);\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BaseEvent} from '@react-types/shared';\nimport {SyntheticEvent} from 'react';\n\n/**\n * This function wraps a React event handler to make stopPropagation the default, and support continuePropagation instead.\n */\nexport function createEventHandler<T extends SyntheticEvent>(handler?: (e: BaseEvent<T>) => void): ((e: T) => void) | undefined {\n  if (!handler) {\n    return undefined;\n  }\n\n  let shouldStopPropagation = true;\n  return (e: T) => {\n    let event: BaseEvent<T> = {\n      ...e,\n      preventDefault() {\n        e.preventDefault();\n      },\n      isDefaultPrevented() {\n        return e.isDefaultPrevented();\n      },\n      stopPropagation() {\n        if (shouldStopPropagation && process.env.NODE_ENV !== 'production') {\n          console.error('stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.');\n        } else {\n          shouldStopPropagation = true;\n        }\n      },\n      continuePropagation() {\n        shouldStopPropagation = false;\n      },\n      isPropagationStopped() {\n        return shouldStopPropagation;\n      }\n    };\n\n    handler(event);\n\n    if (shouldStopPropagation) {\n      e.stopPropagation();\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {createEventHandler} from './createEventHandler';\nimport {DOMAttributes, KeyboardEvents} from '@react-types/shared';\n\nexport interface KeyboardProps extends KeyboardEvents {\n  /** Whether the keyboard events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface KeyboardResult {\n  /** Props to spread onto the target element. */\n  keyboardProps: DOMAttributes\n}\n\n/**\n * Handles keyboard interactions for a focusable element.\n */\nexport function useKeyboard(props: KeyboardProps): KeyboardResult {\n  return {\n    keyboardProps: props.isDisabled ? {} : {\n      onKeyDown: createEventHand<PERSON>(props.onKeyDown),\n      onKeyUp: createEvent<PERSON>and<PERSON>(props.onKeyUp)\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableDOMProps, FocusableElement, FocusableProps, RefObject} from '@react-types/shared';\nimport {focusSafely} from './';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport React, {ForwardedRef, forwardRef, MutableRefObject, ReactElement, ReactNode, useContext, useEffect, useRef} from 'react';\nimport {useFocus} from './useFocus';\nimport {useKeyboard} from './useKeyboard';\n\nexport interface FocusableOptions<T = FocusableElement> extends FocusableProps<T>, FocusableDOMProps {\n  /** Whether focus should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusableProviderProps extends DOMAttributes {\n  /** The child element to provide DOM props to. */\n  children?: ReactNode\n}\n\ninterface FocusableContextValue extends FocusableProviderProps {\n  ref?: MutableRefObject<FocusableElement | null>\n}\n\n// Exported for @react-aria/collections, which forwards this context.\n/** @private */\nexport let FocusableContext = React.createContext<FocusableContextValue | null>(null);\n\nfunction useFocusableContext(ref: RefObject<FocusableElement | null>): FocusableContextValue {\n  let context = useContext(FocusableContext) || {};\n  useSyncRef(context, ref);\n\n  // eslint-disable-next-line\n  let {ref: _, ...otherProps} = context;\n  return otherProps;\n}\n\n/**\n * Provides DOM props to the nearest focusable child.\n */\nexport const FocusableProvider = React.forwardRef(function FocusableProvider(props: FocusableProviderProps, ref: ForwardedRef<FocusableElement>) {\n  let {children, ...otherProps} = props;\n  let objRef = useObjectRef(ref);\n  let context = {\n    ...otherProps,\n    ref: objRef\n  };\n\n  return (\n    <FocusableContext.Provider value={context}>\n      {children}\n    </FocusableContext.Provider>\n  );\n});\n\nexport interface FocusableAria {\n  /** Props for the focusable element. */\n  focusableProps: DOMAttributes\n}\n\n/**\n * Used to make an element focusable and capable of auto focus.\n */\nexport function useFocusable<T extends FocusableElement = FocusableElement>(props: FocusableOptions<T>, domRef: RefObject<FocusableElement | null>): FocusableAria {\n  let {focusProps} = useFocus(props);\n  let {keyboardProps} = useKeyboard(props);\n  let interactions = mergeProps(focusProps, keyboardProps);\n  let domProps = useFocusableContext(domRef);\n  let interactionProps = props.isDisabled ? {} : domProps;\n  let autoFocusRef = useRef(props.autoFocus);\n\n  useEffect(() => {\n    if (autoFocusRef.current && domRef.current) {\n      focusSafely(domRef.current);\n    }\n    autoFocusRef.current = false;\n  }, [domRef]);\n\n  // Always set a tabIndex so that Safari allows focusing native buttons and inputs.\n  let tabIndex: number | undefined = props.excludeFromTabOrder ? -1 : 0;\n  if (props.isDisabled) {\n    tabIndex = undefined;\n  }\n\n  return {\n    focusableProps: mergeProps(\n      {\n        ...interactions,\n        tabIndex\n      },\n      interactionProps\n    )\n  };\n}\n\nexport interface FocusableComponentProps extends FocusableOptions {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Focusable = forwardRef(({children, ...props}: FocusableComponentProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Focusable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Focusable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary' &&\n      el.localName !== 'img' &&\n      el.localName !== 'svg'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Focusable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'tabpanel' &&\n        role !== 'textbox' &&\n        role !== 'treeitem' &&\n        // aria-describedby is also announced on these roles\n        role !== 'img' &&\n        role !== 'meter' &&\n        role !== 'progressbar'\n      ) {\n        console.warn(`<Focusable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement} from '@react-types/shared';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef} from '@react-aria/utils';\nimport {PressProps, usePress} from './usePress';\nimport React, {ForwardedRef, ReactElement, useEffect} from 'react';\nimport {useFocusable} from './useFocusable';\n\ninterface PressableProps extends PressProps {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Pressable = React.forwardRef(({children, ...props}: PressableProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {pressProps} = usePress({...props, ref});\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Pressable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Pressable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Pressable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'textbox' &&\n        role !== 'treeitem'\n      ) {\n        console.warn(`<Pressable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(pressProps, focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {mergeProps, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport {PressProps} from './usePress';\nimport {PressResponderContext} from './context';\nimport React, {ForwardedRef, JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\ninterface PressResponderProps extends PressProps {\n  children: ReactNode\n}\n\nexport const PressResponder = React.forwardRef(({children, ...props}: PressResponderProps, ref: ForwardedRef<FocusableElement>) => {\n  let isRegistered = useRef(false);\n  let prevContext = useContext(PressResponderContext);\n  ref = useObjectRef(ref || prevContext?.ref);\n  let context = mergeProps(prevContext || {}, {\n    ...props,\n    ref,\n    register() {\n      isRegistered.current = true;\n      if (prevContext) {\n        prevContext.register();\n      }\n    }\n  });\n\n  useSyncRef(prevContext, ref);\n\n  useEffect(() => {\n    if (!isRegistered.current) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'A PressResponder was rendered without a pressable child. ' +\n          'Either call the usePress hook, or wrap your DOM node with <Pressable> component.'\n        );\n      }\n      isRegistered.current = true; // only warn once in strict mode.\n    }\n  }, []);\n\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n});\n\nexport function ClearPressResponder({children}: {children: ReactNode}): JSX.Element {\n  let context = useMemo(() => ({register: () => {}}), []);\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {createSyntheticEvent, setEventTarget, useSyntheticBlurEvent} from './utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {FocusEvent, useCallback, useRef} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\n\nexport interface FocusWithinProps {\n  /** Whether the focus within events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when the target element or a descendant receives focus. */\n  onFocusWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the target element and all descendants lose focus. */\n  onBlurWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the the focus within state changes. */\n  onFocusWithinChange?: (isFocusWithin: boolean) => void\n}\n\nexport interface FocusWithinResult {\n  /** Props to spread onto the target element. */\n  focusWithinProps: DOMAttributes\n}\n\n/**\n * Handles focus events for the target and its descendants.\n */\nexport function useFocusWithin(props: FocusWithinProps): FocusWithinResult {\n  let {\n    isDisabled,\n    onBlurWithin,\n    onFocusWithin,\n    onFocusWithinChange\n  } = props;\n  let state = useRef({\n    isFocusWithin: false\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let onBlur = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !(e.currentTarget as Element).contains(e.relatedTarget as Element)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n\n      if (onBlurWithin) {\n        onBlurWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(false);\n      }\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n\n  let onSyntheticFocus = useSyntheticBlurEvent(onBlur);\n  let onFocus = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = getActiveElement(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusWithin) {\n        onFocusWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(true);\n      }\n\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !nodeContains(currentTarget, e.target as Element)) {\n          let nativeEvent = new ownerDocument.defaultView!.FocusEvent('blur', {relatedTarget: e.target});\n          setEventTarget(nativeEvent, currentTarget);\n          let event = createSyntheticEvent<FocusEvent>(nativeEvent);\n          onBlur(event);\n        }\n      }, {capture: true});\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n\n  if (isDisabled) {\n    return {\n      focusWithinProps: {\n        // These cannot be null, that would conflict in mergeProps\n        onFocus: undefined,\n        onBlur: undefined\n      }\n    };\n  }\n\n  return {\n    focusWithinProps: {\n      onFocus,\n      onBlur\n    }\n  };\n}\n", "import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  createShadowTreeWalker,\n  getActiveElement,\n  getEventTarget,\n  getOwnerDocument,\n  isAndroid,\n  isChrome,\n  isFocusable,\n  isTabbable,\n  ShadowTreeWalker,\n  useLayoutEffect\n} from '@react-aria/utils';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport {isElementVisible} from './isElementVisible';\nimport React, {JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\nexport interface FocusScopeProps {\n  /** The contents of the focus scope. */\n  children: ReactNode,\n\n  /**\n   * Whether to contain focus inside the scope, so users cannot\n   * move focus outside, for example in a modal dialog.\n   */\n  contain?: boolean,\n\n  /**\n   * Whether to restore focus back to the element that was focused\n   * when the focus scope mounted, after the focus scope unmounts.\n   */\n  restoreFocus?: boolean,\n\n  /** Whether to auto focus the first focusable element in the focus scope on mount. */\n  autoFocus?: boolean\n}\n\nexport interface FocusManagerOptions {\n  /** The element to start searching from. The currently focused element by default. */\n  from?: Element,\n  /** Whether to only include tabbable elements, or all focusable elements. */\n  tabbable?: boolean,\n  /** Whether focus should wrap around when it reaches the end of the scope. */\n  wrap?: boolean,\n  /** A callback that determines whether the given element is focused. */\n  accept?: (node: Element) => boolean\n}\n\nexport interface FocusManager {\n  /** Moves focus to the next focusable or tabbable element in the focus scope. */\n  focusNext(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the previous focusable or tabbable element in the focus scope. */\n  focusPrevious(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the first focusable or tabbable element in the focus scope. */\n  focusFirst(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the last focusable or tabbable element in the focus scope. */\n  focusLast(opts?: FocusManagerOptions): FocusableElement | null\n}\n\ntype ScopeRef = RefObject<Element[] | null> | null;\ninterface IFocusContext {\n  focusManager: FocusManager,\n  parentNode: TreeNode | null\n}\n\nconst FocusContext = React.createContext<IFocusContext | null>(null);\nconst RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\n\nlet activeScope: ScopeRef = null;\n\n// This is a hacky DOM-based implementation of a FocusScope until this RFC lands in React:\n// https://github.com/reactjs/rfcs/pull/109\n\n/**\n * A FocusScope manages focus for its descendants. It supports containing focus inside\n * the scope, restoring focus to the previously focused element on unmount, and auto\n * focusing children on mount. It also acts as a container for a programmatic focus\n * management interface that can be used to move focus forward and back in response\n * to user events.\n */\nexport function FocusScope(props: FocusScopeProps): JSX.Element {\n  let {children, contain, restoreFocus, autoFocus} = props;\n  let startRef = useRef<HTMLSpanElement>(null);\n  let endRef = useRef<HTMLSpanElement>(null);\n  let scopeRef = useRef<Element[]>([]);\n  let {parentNode} = useContext(FocusContext) || {};\n\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = useMemo(() => new TreeNode({scopeRef}), [scopeRef]);\n\n  useLayoutEffect(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || focusScopeTree.root;\n    if (focusScopeTree.getTreeNode(parent.scopeRef) && activeScope && !isAncestorScope(activeScope, parent.scopeRef)) {\n      let activeNode = focusScopeTree.getTreeNode(activeScope);\n      if (activeNode) {\n        parent = activeNode;\n      }\n    }\n\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    focusScopeTree.addNode(node);\n  }, [node, parentNode]);\n\n  useLayoutEffect(() => {\n    let node = focusScopeTree.getTreeNode(scopeRef);\n    if (node) {\n      node.contain = !!contain;\n    }\n  }, [contain]);\n\n  useLayoutEffect(() => {\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = startRef.current?.nextSibling!;\n    let nodes: Element[] = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node as Element);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling as Element;\n    }\n\n    scopeRef.current = nodes;\n\n    return () => {\n      for (let node of nodes) {\n        node.removeEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      }\n    };\n  }, [children]);\n\n  useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  useFocusContainment(scopeRef, contain);\n  useRestoreFocus(scopeRef, restoreFocus, contain);\n  useAutoFocus(scopeRef, autoFocus);\n\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  useEffect(() => {\n    const activeElement = getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope: TreeNode | null = null;\n\n    if (isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of focusScopeTree.traverse()) {\n        if (node.scopeRef && isElementInScope(activeElement, node.scopeRef.current)) {\n          scope = node;\n        }\n      }\n\n      if (scope === focusScopeTree.getTreeNode(scopeRef)) {\n        activeScope = scope.scopeRef;\n      }\n    }\n  }, [scopeRef]);\n\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  useLayoutEffect(() => {\n    return () => {\n      // Scope may have been re-parented.\n      let parentScope = focusScopeTree.getTreeNode(scopeRef)?.parent?.scopeRef ?? null;\n\n      if (\n        (scopeRef === activeScope || isAncestorScope(scopeRef, activeScope)) &&\n        (!parentScope || focusScopeTree.getTreeNode(parentScope))\n      ) {\n        activeScope = parentScope;\n      }\n      focusScopeTree.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n\n  let focusManager = useMemo(() => createFocusManagerForScope(scopeRef), []);\n  let value = useMemo(() => ({\n    focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n\n  return (\n    <FocusContext.Provider value={value}>\n      <span data-focus-scope-start hidden ref={startRef} />\n      {children}\n      <span data-focus-scope-end hidden ref={endRef} />\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Returns a FocusManager interface for the parent FocusScope.\n * A FocusManager can be used to programmatically move focus within\n * a FocusScope, e.g. in response to user events like keyboard navigation.\n */\nexport function useFocusManager(): FocusManager | undefined {\n  return useContext(FocusContext)?.focusManager;\n}\n\nfunction createFocusManagerForScope(scopeRef: React.RefObject<Element[] | null>): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[0].previousElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[scope.length - 1].nextElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node  : sentinel;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode() as FocusableElement;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[0].previousElementSibling!;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling!;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    }\n  };\n}\n\nfunction getScopeRoot(scope: Element[]) {\n  return scope[0].parentElement!;\n}\n\nfunction shouldContainFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return true;\n}\n\nfunction useFocusContainment(scopeRef: RefObject<Element[] | null>, contain?: boolean) {\n  let focusedNode = useRef<FocusableElement>(undefined);\n\n  let raf = useRef<ReturnType<typeof requestAnimationFrame>>(undefined);\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = (e) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = getActiveElement(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !isElementInScope(focusedElement, scope)) {\n        return;\n      }\n\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable: true}, scope);\n      if (!focusedElement) {\n        return;\n      }\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling! : scope[0].previousElementSibling!;\n        nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      }\n\n      e.preventDefault();\n      if (nextElement) {\n        focusElement(nextElement, true);\n      }\n    };\n\n    let onFocus: EventListener = (e) => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) && isElementInScope(getEventTarget(e) as Element, scopeRef.current)) {\n        activeScope = scopeRef;\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      } else if (shouldContainFocus(scopeRef) && !isElementInChildScope(getEventTarget(e) as Element, scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) {\n          focusedNode.current.focus();\n        } else if (activeScope && activeScope.current) {\n          focusFirstInScope(activeScope.current);\n        }\n      } else if (shouldContainFocus(scopeRef)) {\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      }\n    };\n\n    let onBlur: EventListener = (e) => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = getInteractionModality();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && isAndroid() && isChrome();\n\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = getActiveElement(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && shouldContainFocus(scopeRef) && !isElementInChildScope(activeElement, scopeRef)) {\n          activeScope = scopeRef;\n          let target = getEventTarget(e) as FocusableElement;\n          if (target && target.isConnected) {\n            focusedNode.current = target;\n            focusedNode.current?.focus();\n          } else if (activeScope.current) {\n            focusFirstInScope(activeScope.current);\n          }\n        }\n      });\n    };\n\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope?.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope?.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n\n  useLayoutEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, [raf]);\n}\n\nfunction isElementInAnyScope(element: Element) {\n  return isElementInChildScope(element);\n}\n\nfunction isElementInScope(element?: Element | null, scope?: Element[] | null) {\n  if (!element) {\n    return false;\n  }\n  if (!scope) {\n    return false;\n  }\n  return scope.some(node => node.contains(element));\n}\n\nfunction isElementInChildScope(element: Element, scope: ScopeRef = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) {\n    return true;\n  }\n\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {scopeRef: s} of focusScopeTree.traverse(focusScopeTree.getTreeNode(scope))) {\n    if (s && isElementInScope(element, s.current)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** @private */\nexport function isElementInChildOfActiveScope(element: Element): boolean {\n  return isElementInChildScope(element, activeScope);\n}\n\nfunction isAncestorScope(ancestor: ScopeRef, scope: ScopeRef) {\n  let parent = focusScopeTree.getTreeNode(scope)?.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) {\n      return true;\n    }\n    parent = parent.parent;\n  }\n  return false;\n}\n\nfunction focusElement(element: FocusableElement | null, scroll = false) {\n  if (element != null && !scroll) {\n    try {\n      focusSafely(element);\n    } catch {\n      // ignore\n    }\n  } else if (element != null) {\n    try {\n      element.focus();\n    } catch {\n      // ignore\n    }\n  }\n}\n\nfunction getFirstInScope(scope: Element[], tabbable = true) {\n  let sentinel = scope[0].previousElementSibling!;\n  let scopeRoot = getScopeRoot(scope);\n  let walker = getFocusableTreeWalker(scopeRoot, {tabbable}, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = getScopeRoot(scope);\n    walker = getFocusableTreeWalker(scopeRoot, {tabbable: false}, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n\n  return nextNode as FocusableElement;\n}\n\nfunction focusFirstInScope(scope: Element[], tabbable:boolean = true) {\n  focusElement(getFirstInScope(scope, tabbable));\n}\n\nfunction useAutoFocus(scopeRef: RefObject<Element[] | null>, autoFocus?: boolean) {\n  const autoFocusRef = React.useRef(autoFocus);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      activeScope = scopeRef;\n      const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!isElementInScope(getActiveElement(ownerDocument), activeScope.current) && scopeRef.current) {\n        focusFirstInScope(scopeRef.current);\n      }\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\n\nfunction useActiveScopeTracker(scopeRef: RefObject<Element[] | null>, restore?: boolean, contain?: boolean) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  useLayoutEffect(() => {\n    if (restore || contain) {\n      return;\n    }\n\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    let onFocus = (e) => {\n      let target = getEventTarget(e) as Element;\n      if (isElementInScope(target, scopeRef.current)) {\n        activeScope = scopeRef;\n      } else if (!isElementInAnyScope(target)) {\n        activeScope = null;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\n\nfunction shouldRestoreFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return scope?.scopeRef === scopeRef;\n}\n\nfunction useRestoreFocus(scopeRef: RefObject<Element[] | null>, restoreFocus?: boolean, contain?: boolean) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = useRef(typeof document !== 'undefined' ? getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined)) as FocusableElement : null);\n\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) {\n      return;\n    }\n\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) &&\n        isElementInScope(getActiveElement(ownerDocument), scopeRef.current)\n      ) {\n        activeScope = scopeRef;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = ownerDocument.activeElement as FocusableElement;\n      if (!isElementInChildScope(focusedElement, scopeRef) || !shouldRestoreFocus(scopeRef)) {\n        return;\n      }\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = getFocusableTreeWalker(ownerDocument.body, {tabbable: true});\n\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do {\n          nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n        } while (isElementInChildScope(nextElement, scopeRef));\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) {\n          focusElement(nextElement, true);\n        } else {\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!isElementInAnyScope(nodeToRestore)) {\n            focusedElement.blur();\n          } else {\n            focusElement(nodeToRestore, true);\n          }\n        }\n      }\n    };\n\n    if (!contain) {\n      ownerDocument.addEventListener('keydown', onKeyDown as EventListener, true);\n    }\n\n    return () => {\n      if (!contain) {\n        ownerDocument.removeEventListener('keydown', onKeyDown as EventListener, true);\n      }\n    };\n  }, [scopeRef, restoreFocus, contain]);\n\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    let treeNode = focusScopeTree.getTreeNode(scopeRef);\n    if (!treeNode) {\n      return;\n    }\n    treeNode.nodeToRestore = nodeToRestoreRef.current ?? undefined;\n    return () => {\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = getActiveElement(ownerDocument);\n      if (\n        restoreFocus\n        && nodeToRestore\n        && (\n          ((activeElement && isElementInChildScope(activeElement, scopeRef)) || (activeElement === ownerDocument.body && shouldRestoreFocus(scopeRef)))\n        )\n      ) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = focusScopeTree.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && focusScopeTree.getTreeNode(treeNode.scopeRef)) {\n                let node = getFirstInScope(treeNode.scopeRef.current, true);\n                restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\n\nfunction restoreFocusToElement(node: FocusableElement) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent(RESTORE_FOCUS_EVENT, {bubbles: true, cancelable: true}))) {\n    focusElement(node);\n  }\n}\n\n/**\n * Create a [TreeWalker]{@link https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker}\n * that matches all focusable/tabbable elements.\n */\nexport function getFocusableTreeWalker(root: Element, opts?: FocusManagerOptions, scope?: Element[]): ShadowTreeWalker | TreeWalker {\n  let filter = opts?.tabbable ? isTabbable : isFocusable;\n\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = root?.nodeType === Node.ELEMENT_NODE ? (root as Element) : null;\n\n  // Determine the document to use\n  let doc = getOwnerDocument(rootElement);\n\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = createShadowTreeWalker(\n    doc,\n    root || doc,\n    NodeFilter.SHOW_ELEMENT,\n    {\n      acceptNode(node) {\n        // Skip nodes inside the starting node.\n        if (opts?.from?.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n\n        if (filter(node as Element)\n          && isElementVisible(node as Element)\n          && (!scope || isElementInScope(node as Element, scope))\n          && (!opts?.accept || opts.accept(node as Element))\n        ) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n\n        return NodeFilter.FILTER_SKIP;\n      }\n    }\n  );\n\n  if (opts?.from) {\n    walker.currentNode = opts.from;\n  }\n\n  return walker;\n}\n\n/**\n * Creates a FocusManager object that can be used to move focus within an element.\n */\nexport function createFocusManager(ref: RefObject<Element | null>, defaultOptions: FocusManagerOptions = {}): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      }\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      } else {\n        let next = last(walker);\n        if (next) {\n          focusElement(next, true);\n        }\n        return next ?? null;\n      }\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = last(walker);\n        if (!lastNode) {\n          // couldn't wrap\n          return null;\n        }\n        previousNode = lastNode;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode ?? null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let next = last(walker);\n      if (next) {\n        focusElement(next, true);\n      }\n      return next ?? null;\n    }\n  };\n}\n\nfunction last(walker: ShadowTreeWalker | TreeWalker) {\n  let next: FocusableElement | undefined = undefined;\n  let last: FocusableElement;\n  do {\n    last = walker.lastChild() as FocusableElement;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n\n\nclass Tree {\n  root: TreeNode;\n  private fastMap = new Map<ScopeRef, TreeNode>();\n\n  constructor() {\n    this.root = new TreeNode({scopeRef: null});\n    this.fastMap.set(null, this.root);\n  }\n\n  get size() {\n    return this.fastMap.size;\n  }\n\n  getTreeNode(data: ScopeRef) {\n    return this.fastMap.get(data);\n  }\n\n  addTreeNode(scopeRef: ScopeRef, parent: ScopeRef, nodeToRestore?: FocusableElement) {\n    let parentNode = this.fastMap.get(parent ?? null);\n    if (!parentNode) {\n      return;\n    }\n    let node = new TreeNode({scopeRef});\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) {\n      node.nodeToRestore = nodeToRestore;\n    }\n  }\n\n  addNode(node: TreeNode) {\n    this.fastMap.set(node.scopeRef, node);\n  }\n\n  removeTreeNode(scopeRef: ScopeRef) {\n    // never remove the root\n    if (scopeRef === null) {\n      return;\n    }\n    let node = this.fastMap.get(scopeRef);\n    if (!node) {\n      return;\n    }\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) {\n      if (\n        current !== node &&\n        node.nodeToRestore &&\n        current.nodeToRestore &&\n        node.scopeRef &&\n        node.scopeRef.current &&\n        isElementInScope(current.nodeToRestore, node.scopeRef.current)\n      ) {\n        current.nodeToRestore = node.nodeToRestore;\n      }\n    }\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) {\n        children.forEach(child => parentNode && parentNode.addChild(child));\n      }\n    }\n\n    this.fastMap.delete(node.scopeRef);\n  }\n\n  // Pre Order Depth First\n  *traverse(node: TreeNode = this.root): Generator<TreeNode> {\n    if (node.scopeRef != null) {\n      yield node;\n    }\n    if (node.children.size > 0) {\n      for (let child of node.children) {\n        yield* this.traverse(child);\n      }\n    }\n  }\n\n  clone(): Tree {\n    let newTree = new Tree();\n    for (let node of this.traverse()) {\n      newTree.addTreeNode(node.scopeRef, node.parent?.scopeRef ?? null, node.nodeToRestore);\n    }\n    return newTree;\n  }\n}\n\nclass TreeNode {\n  public scopeRef: ScopeRef;\n  public nodeToRestore?: FocusableElement;\n  public parent?: TreeNode;\n  public children: Set<TreeNode> = new Set();\n  public contain = false;\n\n  constructor(props: {scopeRef: ScopeRef}) {\n    this.scopeRef = props.scopeRef;\n  }\n  addChild(node: TreeNode) {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node: TreeNode) {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n}\n\nexport let focusScopeTree = new Tree();\n", "export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) < 1.01\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n", "import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nconst getRect = (element: HTMLElement): Rect => {\n  const { offsetWidth, offsetHeight } = element\n  return { width: offsetWidth, height: offsetHeight }\n}\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(getRect(element as unknown as HTMLElement))\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const run = () => {\n      const entry = entries[0]\n      if (entry?.borderBoxSize) {\n        const box = entry.borderBoxSize[0]\n        if (box) {\n          handler({ width: box.inlineSize, height: box.blockSize })\n          return\n        }\n      }\n      handler(getRect(element as unknown as HTMLElement))\n    }\n\n    instance.options.useAnimationFrameWithResizeObserver\n      ? requestAnimationFrame(run)\n      : run()\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n\n  return (element as unknown as HTMLElement)[\n    instance.options.horizontal ? 'offsetWidth' : 'offsetHeight'\n  ]\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n  useAnimationFrameWithResizeObserver?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          const run = () => {\n            this._measureElement(entry.target as TItemElement, entry)\n          }\n          this.options.useAnimationFrameWithResizeObserver\n            ? requestAnimationFrame(run)\n            : run()\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: false,\n      useAnimationFrameWithResizeObserver: false,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [\n      this.getMeasurements(),\n      this.getSize(),\n      this.getScrollOffset(),\n      this.options.lanes,\n    ],\n    (measurements, outerSize, scrollOffset, lanes) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n              lanes,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex])\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getVirtualIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (\n    toOffset: number,\n    align: ScrollAlignment,\n    itemSize = 0,\n  ) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      align = toOffset >= scrollOffset + size ? 'end' : 'start'\n    }\n\n    if (align === 'center') {\n      // When aligning to a particular item (e.g. with scrollToIndex),\n      // adjust offset by the size of the item to center on the item\n      toOffset += (itemSize - size) / 2\n    } else if (align === 'end') {\n      toOffset -= size\n    }\n\n    const maxOffset = this.getTotalSize() + this.options.scrollMargin - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const toOffset =\n      align === 'end'\n        ? item.end + this.options.scrollPaddingEnd\n        : item.start - this.options.scrollPaddingStart\n\n    return [\n      this.getOffsetForAlignment(toOffset, align, item.size),\n      align,\n    ] as const\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    let attempts = 0\n    const maxAttempts = 10\n\n    const tryScroll = (currentAlign: ScrollAlignment) => {\n      if (!this.targetWindow) return\n\n      const offsetInfo = this.getOffsetForIndex(index, currentAlign)\n      if (!offsetInfo) {\n        console.warn('Failed to get offset for index:', index)\n        return\n      }\n      const [offset, align] = offsetInfo\n      this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n      this.targetWindow.requestAnimationFrame(() => {\n        const currentOffset = this.getScrollOffset()\n        const afterInfo = this.getOffsetForIndex(index, align)\n        if (!afterInfo) {\n          console.warn('Failed to get offset for index:', index)\n          return\n        }\n\n        if (!approxEqual(afterInfo[0], currentOffset)) {\n          scheduleRetry(align)\n        }\n      })\n    }\n\n    const scheduleRetry = (align: ScrollAlignment) => {\n      if (!this.targetWindow) return\n\n      attempts++\n      if (attempts < maxAttempts) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('Schedule retry', attempts, maxAttempts)\n        }\n        this.targetWindow.requestAnimationFrame(() => tryScroll(align))\n      } else {\n        console.warn(\n          `Failed to scroll to index ${index} after ${maxAttempts} attempts.`,\n        )\n      }\n    }\n\n    tryScroll(initialAlign)\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    // If there is only one lane, use the last measurement's end\n    // Otherwise find the maximum end value among all measurements\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else if (this.options.lanes === 1) {\n      end = measurements[measurements.length - 1]?.end ?? 0\n    } else {\n      const endByLane = Array<number | null>(this.options.lanes).fill(null)\n      let endIndex = measurements.length - 1\n      while (endIndex >= 0 && endByLane.some((val) => val === null)) {\n        const item = measurements[endIndex]!\n        if (endByLane[item.lane] === null) {\n          endByLane[item.lane] = item.end\n        }\n\n        endIndex--\n      }\n\n      end = Math.max(...endByLane.filter((val): val is number => val !== null))\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n  lanes: number\n}) {\n  const lastIndex = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  // handle case when item count is less than or equal to lanes\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex,\n    }\n  }\n\n  let startIndex = findNearestBinarySearch(\n    0,\n    lastIndex,\n    getOffset,\n    scrollOffset,\n  )\n  let endIndex = startIndex\n\n  if (lanes === 1) {\n    while (\n      endIndex < lastIndex &&\n      measurements[endIndex]!.end < scrollOffset + outerSize\n    ) {\n      endIndex++\n    }\n  } else if (lanes > 1) {\n    // Expand forward until we include the visible items from all lanes\n    // which are closer to the end of the virtualizer window\n    const endPerLane = Array(lanes).fill(0)\n    while (\n      endIndex < lastIndex &&\n      endPerLane.some((pos) => pos < scrollOffset + outerSize)\n    ) {\n      const item = measurements[endIndex]!\n      endPerLane[item.lane] = item.end\n      endIndex++\n    }\n\n    // Expand backward until we include all lanes' visible items\n    // closer to the top\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize)\n    while (startIndex >= 0 && startPerLane.some((pos) => pos >= scrollOffset)) {\n      const item = measurements[startIndex]!\n      startPerLane[item.lane] = item.start\n      startIndex--\n    }\n\n    // Align startIndex to the beginning of its lane\n    startIndex = Math.max(0, startIndex - (startIndex % lanes))\n    // Align endIndex to the end of its lane\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - (endIndex % lanes)))\n  }\n\n  return { startIndex, endIndex }\n}\n", "import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\n\nexport * from '@tanstack/virtual-core'\n\nconst useIsomorphicLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: VirtualizerOptions<TScrollElement, TItemElement>,\n): Virtualizer<TScrollElement, TItemElement> {\n  const rerender = React.useReducer(() => ({}), {})[1]\n\n  const resolvedOptions: VirtualizerOptions<TScrollElement, TItemElement> = {\n    ...options,\n    onChange: (instance, sync) => {\n      if (sync) {\n        flushSync(rerender)\n      } else {\n        rerender()\n      }\n      options.onChange?.(instance, sync)\n    },\n  }\n\n  const [instance] = React.useState(\n    () => new Virtualizer<TScrollElement, TItemElement>(resolvedOptions),\n  )\n\n  instance.setOptions(resolvedOptions)\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount()\n  }, [])\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate()\n  })\n\n  return instance\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: PartialKeys<\n    VirtualizerOptions<TScrollElement, TItemElement>,\n    'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n  >,\n): Virtualizer<TScrollElement, TItemElement> {\n  return useVirtualizerBase<TScrollElement, TItemElement>({\n    observeElementRect: observeElementRect,\n    observeElementOffset: observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options,\n  })\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: PartialKeys<\n    VirtualizerOptions<Window, TItemElement>,\n    | 'getScrollElement'\n    | 'observeElementRect'\n    | 'observeElementOffset'\n    | 'scrollToFn'\n  >,\n): Virtualizer<Window, TItemElement> {\n  return useVirtualizerBase<Window, TItemElement>({\n    getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => (typeof document !== 'undefined' ? window.scrollY : 0),\n    ...options,\n  })\n}\n", "// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n"], "mappings": ";;;;;;;;;;;;;;;AAiBO,IAAM,4CAAkB,OAAO,aAAa,eAC/C,GAAA,aAAAA,SAAM,kBACN,MAAA;AAAO;;;;ACJJ,SAAS,0CAAmC,IAAM;AACvD,QAAM,OAAM,GAAA,cAAAC,QAA6B,IAAA;AACzC,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,UAAU;EAChB,GAAG;IAAC;GAAG;AAEP,UAAO,GAAA,cAAAC,aAAe,IAAI,SAAA;AACxB,UAAM,IAAI,IAAI;AACd,WAAO,MAAA,QAAA,MAAA,SAAA,SAAA,EAAA,GAAO,IAAA;EAChB,GAAG,CAAA,CAAE;AACP;;;;;;;;;;ACQA,IAAM,uCAAkC;EACtC,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,IAAA,CAAA;EAC1C,SAAS;AACX;AAEA,IAAM,oCAAa,GAAA,cAAAC,SAAM,cAA+B,oCAAA;AACxD,IAAM,sCAAe,GAAA,cAAAA,SAAM,cAAc,KAAA;AAwDzC,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS,aAAa;AAG/B,IAAI,qCAAe,oBAAI,QAAA;AAEvB,SAAS,iCAAW,aAAa,OAAK;AACpC,MAAI,OAAM,GAAA,cAAAC,YAAW,gCAAA;AACrB,MAAI,OAAM,GAAA,cAAAC,QAAsB,IAAA;AAEhC,MAAI,IAAI,YAAY,QAAQ,CAAC,YAAY;QAWpB,6EAAA;AAAnB,QAAI,gBAAe,6DAAA,GAAA,cAAAC,SAAM,wDAAkD,QAAxD,8DAAA,SAAA,UAAA,8EAAA,0DAA0D,uBAAiB,QAA3E,gFAAA,SAAA,SAAA,4EAA6E;AAChG,QAAI,cAAc;AAChB,UAAI,qBAAqB,mCAAa,IAAI,YAAA;AAC1C,UAAI,sBAAsB;AAExB,2CAAa,IAAI,cAAc;UAC7B,IAAI,IAAI;UACR,OAAO,aAAa;QACtB,CAAA;eACS,aAAa,kBAAkB,mBAAmB,OAAO;AAIlE,YAAI,UAAU,mBAAmB;AACjC,2CAAa,OAAO,YAAA;MACtB;IACF;AAGA,QAAI,UAAU,EAAE,IAAI;EACtB;AAGA,SAAO,IAAI;AACb;AAEA,SAAS,yCAAmB,WAAkB;AAC5C,MAAI,OAAM,GAAA,cAAAF,YAAW,gCAAA;AAIrB,MAAI,QAAQ,wCAAkB,CAAC,mCAAa,KAC1C,SAAQ,KAAK,iJAAA;AAGf,MAAI,UAAU,iCAAW,CAAC,CAAC,SAAA;AAC3B,MAAI,SAAS,QAAQ,wCAAkB,QAAkC,eAAe,aAAa,IAAI,MAAM;AAC/G,SAAO,aAAa,GAAG,MAAA,IAAU,OAAA;AACnC;AAEA,SAAS,yCAAmB,WAAkB;AAC5C,MAAI,MAAK,GAAA,cAAAE,SAAM,MAAK;AACpB,MAAI,CAAC,MAAA,KAAU,GAAA,cAAAC,UAAS,0CAAA,CAAA;AACxB,MAAI,SAAS,UAAU,QAAkC,eAAe,aAAa,qCAAe,MAAM;AAC1G,SAAO,aAAa,GAAG,MAAA,IAAU,EAAA;AACnC;AAIO,IAAM,4CAAe,QAAO,GAAA,cAAAD,SAAM,OAAA,MAAa,aAAa,2CAAqB;AAExF,SAAS,oCAAA;AACP,SAAO;AACT;AAEA,SAAS,0CAAA;AACP,SAAO;AACT;AAGA,SAAS,gCAAU,eAAyB;AAE1C,SAAO,MAAA;EAAO;AAChB;AAOO,SAAS,4CAAA;AAEd,MAAI,QAAO,GAAA,cAAAA,SAAM,sBAAA,MAA4B,WAC3C,SAAO,GAAA,cAAAA,SAAM,sBAAA,EAAwB,iCAAW,mCAAa,uCAAA;AAI/D,UAAO,GAAA,cAAAF,YAAW,kCAAA;AACpB;;;AClLA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS,aAAa;AAGxB,IAAI,4CAA2D,oBAAI,IAAA;AAI1E,IAAI;AACJ,IAAI,OAAO,yBAAyB,YAClC,kCAAW,IAAI,qBAA6B,CAAC,cAAA;AAC3C,4CAAc,OAAO,SAAA;AACvB,CAAA;AAyDK,SAAS,0CAAS,KAAa,KAAW;AAC/C,MAAI,QAAQ,IACV,QAAO;AAGT,MAAI,UAAU,0CAAc,IAAI,GAAA;AAChC,MAAI,SAAS;AACX,YAAQ,QAAQ,CAAA,QAAQ,IAAI,UAAU,GAAA;AACtC,WAAO;EACT;AAEA,MAAI,UAAU,0CAAc,IAAI,GAAA;AAChC,MAAI,SAAS;AACX,YAAQ,QAAQ,CAAC,QAAS,IAAI,UAAU,GAAA;AACxC,WAAO;EACT;AAEA,SAAO;AACT;;;AC5FO,SAAS,6CAAS,WAAgB;AACvC,SAAO,IAAI,SAAA;AACT,aAAS,YAAY,UACnB,KAAI,OAAO,aAAa,WACtB,UAAA,GAAY,IAAA;EAGlB;AACF;;;ACvBO,IAAM,4CAAmB,CAAC,OAAA;MACxB;AAAP,UAAO,oBAAA,OAAA,QAAA,OAAA,SAAA,SAAA,GAAI,mBAAa,QAAjB,sBAAA,SAAA,oBAAqB;AAC9B;AAEO,IAAM,4CAAiB,CAC5B,OAAA;AAEA,MAAI,MAAM,YAAY,MAAM,GAAG,WAAW,GACxC,QAAO;AAGT,QAAM,MAAM,0CAAiB,EAAA;AAC7B,SAAO,IAAI,eAAe;AAC5B;AAKA,SAAS,6BAAO,OAAc;AAC5B,SAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAQ,MAAe,aAAa;AACxC;AAKO,SAAS,0CAAa,MAAiB;AAC5C,SAAO,6BAAO,IAAA,KACZ,KAAK,aAAa,KAAK,0BACvB,UAAU;AACd;;;ACnBA,IAAI,mCAAa;AAcV,SAAS,4CAAA;AACd,SAAO;AACT;;;ACrBO,SAAS,0CACd,MACA,WAAkC;AAElC,MAAI,EAAC,GAAA,2CAAQ,EACX,QAAO,aAAa,OAAO,KAAK,SAAS,SAAA,IAAa;AAGxD,MAAI,CAAC,QAAQ,CAAC,UACZ,QAAO;AAGT,MAAI,cAAqD;AAEzD,SAAO,gBAAgB,MAAM;AAC3B,QAAI,gBAAgB,KAClB,QAAO;AAGT,QAAK,YAAgC,YAAY,UAC9C,YAAgC;AAEjC,oBAAe,YAAgC,aAAc;cACpD,GAAA,2CAAa,WAAA;AAEtB,oBAAc,YAAY;QAE1B,eAAc,YAAY;EAE9B;AAEA,SAAO;AACT;AAKO,IAAM,4CAAmB,CAAC,MAAgB,aAAQ;MAOvD;AANA,MAAI,EAAC,GAAA,2CAAQ,EACX,QAAO,IAAI;AAEb,MAAI,gBAAgC,IAAI;AAExC,SAAO,iBAAiB,gBAAgB,mBACxC,4BAAA,cAAc,gBAAU,QAAxB,8BAAA,SAAA,SAAA,0BAA0B,eACxB,iBAAgB,cAAc,WAAW;AAG3C,SAAO;AACT;AAKO,SAAS,0CAAgC,OAAQ;AACtD,OAAI,GAAA,2CAAQ,KAAQ,MAAM,OAAuB,YAAY;AAC3D,QAAI,MAAM,aACR,QAAO,MAAM,aAAY,EAAG,CAAA;EAEhC;AACA,SAAO,MAAM;AACf;;;AClCO,SAAS,6CAAoC,MAAO;AAGzD,MAAI,SAAgB;IAAC,GAAG,KAAK,CAAA;EAAE;AAC/B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,QAAQ,KAAK,CAAA;AACjB,aAAS,OAAO,OAAO;AACrB,UAAI,IAAI,OAAO,GAAA;AACf,UAAI,IAAI,MAAM,GAAA;AAGd,UACE,OAAO,MAAM,cACb,OAAO,MAAM;MAEb,IAAI,CAAA,MAAO,OACX,IAAI,CAAA,MAAO,OACX,IAAI,WAAW,CAAA;MAAgB,MAC/B,IAAI,WAAW,CAAA;MAAgB,GAE/B,QAAO,GAAA,KAAO,GAAA,2CAAM,GAAG,CAAA;gBAItB,QAAQ,eAAe,QAAQ,uBAChC,OAAO,MAAM,YACb,OAAO,MAAM,SAEb,QAAO,GAAA,KAAO,GAAA,cAAK,GAAG,CAAA;eACb,QAAQ,QAAQ,KAAK,EAC9B,QAAO,MAAK,GAAA,2CAAS,GAAG,CAAA;UAGxB,QAAO,GAAA,IAAO,MAAM,SAAY,IAAI;IAExC;EACF;AAEA,SAAO;AACT;;;ACzDO,SAAS,6CAAgB,MAA4D;AAC1F,MAAI,KAAK,WAAW,KAAK,KAAK,CAAA,EAC5B,QAAO,KAAK,CAAA;AAGd,SAAO,CAAC,UAAA;AACN,QAAI,aAAa;AAEjB,UAAM,WAAW,KAAK,IAAI,CAAA,QAAA;AACxB,YAAM,UAAU,6BAAO,KAAK,KAAA;AAC5B,qBAAA,aAAe,OAAO,WAAW;AACjC,aAAO;IACT,CAAA;AAEA,QAAI,WACF,QAAO,MAAA;AACL,eAAS,QAAQ,CAAC,SAAS,MAAA;AACzB,YAAI,OAAO,YAAY,WACrB,SAAA;YAEA,8BAAO,KAAK,CAAA,GAAI,IAAA;MAEpB,CAAA;IACF;EAEJ;AACF;AAEA,SAAS,6BAAU,KAAsD,OAAQ;AAC/E,MAAI,OAAO,QAAQ,WACjB,QAAO,IAAI,KAAA;WACF,OAAO,KAChB,KAAI,UAAU;AAElB;;;ACrBO,SAAS,0CAAsB,SAAyB;AAC7D,MAAI,4CAAA,EACF,SAAQ,MAAM;IAAC,eAAe;EAAI,CAAA;OAC7B;AACL,QAAI,qBAAqB,4CAAsB,OAAA;AAC/C,YAAQ,MAAK;AACb,gDAAsB,kBAAA;EACxB;AACF;AAEA,IAAI,oDAA8C;AAClD,SAAS,8CAAA;AACP,MAAI,qDAA+B,MAAM;AACvC,wDAA8B;AAC9B,QAAI;AACF,UAAI,YAAY,SAAS,cAAc,KAAA;AACvC,gBAAU,MAAM;QACd,IAAI,gBAAgB;AAClB,8DAA8B;AAC9B,iBAAO;QACT;MACF,CAAA;IACF,QAAQ;IAER;EACF;AAEA,SAAO;AACT;AAEA,SAAS,4CAAsB,SAAyB;AACtD,MAAI,SAAS,QAAQ;AACrB,MAAI,qBAA0C,CAAA;AAC9C,MAAI,uBAAuB,SAAS,oBAAoB,SAAS;AAEjE,SAAO,kBAAkB,eAAe,WAAW,sBAAsB;AACvE,QACE,OAAO,eAAe,OAAO,gBAC7B,OAAO,cAAc,OAAO,YAE5B,oBAAmB,KAAK;MACtB,SAAS;MACT,WAAW,OAAO;MAClB,YAAY,OAAO;IACrB,CAAA;AAEF,aAAS,OAAO;EAClB;AAEA,MAAI,gCAAgC,YAClC,oBAAmB,KAAK;IACtB,SAAS;IACT,WAAW,qBAAqB;IAChC,YAAY,qBAAqB;EACnC,CAAA;AAGF,SAAO;AACT;AAEA,SAAS,4CAAsB,oBAAuC;AACpE,WAAS,EAAA,SAAQ,WAAW,WAAY,KAAK,oBAAoB;AAC/D,YAAQ,YAAY;AACpB,YAAQ,aAAa;EACvB;AACF;;;ACnFA,SAAS,oCAAc,IAAU;MAK7B;AAJF,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa,KACvD,QAAO;AAET,WACE,kCAAA,OAAO,UAAU,eAAA,OAAgB,QAAjC,oCAAA,SAAA,SAAA,gCAAmC,OAAO,KAAK,CAAC,UAA4C,GAAG,KAAK,MAAM,KAAK,CAAA,MAEjH,GAAG,KAAK,OAAO,UAAU,SAAS;AACpC;AAEA,SAAS,mCAAa,IAAU;MAElB;AADZ,SAAO,OAAO,WAAW,eAAe,OAAO,aAAa,OACxD,GAAG,OAAK,kCAAA,OAAO,UAAU,eAAA,OAAgB,QAAjC,oCAAA,SAAA,SAAA,gCAAmC,aAAY,OAAO,UAAU,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,IAAiB;AAC/B,MAAI,MACF,QAAO;AAGT,MAAI,MAAsB;AAC1B,SAAO,MAAA;AACL,QAAI,OAAO,KACT,OAAM,GAAA;AAER,WAAO;EACT;AACF;AAEO,IAAM,4CAAQ,6BAAO,WAAA;AAC1B,SAAO,mCAAa,OAAA;AACtB,CAAA;AAEO,IAAM,2CAAW,6BAAO,WAAA;AAC7B,SAAO,mCAAa,UAAA;AACtB,CAAA;AAEO,IAAM,4CAAS,6BAAO,WAAA;AAC3B,SAAO,mCAAa,QAAA;EAEjB,0CAAA,KAAW,UAAU,iBAAiB;AAC3C,CAAA;AAEO,IAAM,4CAAQ,6BAAO,WAAA;AAC1B,SAAO,yCAAA,KAAc,0CAAA;AACvB,CAAA;AAEO,IAAM,4CAAgB,6BAAO,WAAA;AAClC,SAAO,0CAAA,KAAW,0CAAA;AACpB,CAAA;AAEO,IAAM,4CAAW,6BAAO,WAAA;AAC7B,SAAO,oCAAc,cAAA,KAAmB,CAAC,0CAAA;AAC3C,CAAA;AAEO,IAAM,4CAAW,6BAAO,WAAA;AAC7B,SAAO,oCAAc,SAAA;AACvB,CAAA;AAEO,IAAM,4CAAY,6BAAO,WAAA;AAC9B,SAAO,oCAAc,UAAA;AACvB,CAAA;AAEO,IAAM,4CAAY,6BAAO,WAAA;AAC9B,SAAO,oCAAc,UAAA;AACvB,CAAA;;;;ACvDA,IAAM,uCAAgB,GAAA,cAAAI,eAAsB;EAC1C,UAAU;EACV,MAAM;EACN,SAAS,CAAC,SAAS;AACrB,CAAA;AA6DO,SAAS,0CAAS,QAA2B,WAAsB,aAAa,MAAI;MAOtE,oBAAA;AANnB,MAAI,EAAA,SAAQ,SAAS,QAAQ,SAAU,IAAI;AAM3C,OAAI,GAAA,2CAAQ,OAAO,gBAAA,OAAO,WAAK,QAAZ,kBAAA,SAAA,UAAA,qBAAA,cAAc,UAAI,QAAlB,uBAAA,SAAA,SAAA,mBAAoB,WAAW,KAAA,MAAU,OAAO,WAAW,UAAA;AAC5E,SAAI,GAAA,2CAAI,EACN,WAAU;QAEV,WAAU;;AAMd,MAAI,SAAQ,GAAA,2CAAO,MAAO,GAAA,2CAAI,KAAO,EAAC,GAAA,2CAAK,KAAO,OAE9C,IAAI,cAAc,WAAW;IAAC,eAAe;;;;;EAA2C,CAAA,IACxF,IAAI,WAAW,SAAS;;;;;IAAqC,SAAS;IAAM,YAAY;EAAI,CAAA;AAC/F,4CAAiB,YAAY;AAC9B,GAAA,GAAA,2CAAsB,MAAA;AACtB,SAAO,cAAc,KAAA;AACpB,4CAAiB,YAAY;AAChC;AAEC,0CAAiB,YAAY;AAE9B,SAAS,uCAAiB,QAAiB,MAAuC;AAChF,MAAI,kBAAkB,kBACpB,MAAK,MAAA;WACI,OAAO,aAAa,WAAA,GAAc;AAC3C,QAAI,OAAO,SAAS,cAAc,GAAA;AAClC,SAAK,OAAO,OAAO,aAAa,WAAA;AAChC,QAAI,OAAO,aAAa,aAAA,EACtB,MAAK,SAAS,OAAO,aAAa,aAAA;AAEpC,QAAI,OAAO,aAAa,UAAA,EACtB,MAAK,MAAM,OAAO,aAAa,UAAA;AAEjC,QAAI,OAAO,aAAa,eAAA,EACtB,MAAK,WAAW,OAAO,aAAa,eAAA;AAEtC,QAAI,OAAO,aAAa,WAAA,EACtB,MAAK,OAAO,OAAO,aAAa,WAAA;AAElC,QAAI,OAAO,aAAa,sBAAA,EACtB,MAAK,iBAAiB,OAAO,aAAa,sBAAA;AAE5C,WAAO,YAAY,IAAA;AACnB,SAAK,IAAA;AACL,WAAO,YAAY,IAAA;EACrB;AACF;AAEA,SAAS,wCAAkB,QAAiB,WAAoB;AAC9D,yCAAiB,QAAQ,CAAA,SAAQ,0CAAS,MAAM,SAAA,CAAA;AAClD;;;AChIA,IAAI,6CAAuB,oBAAI,IAAA;AAG/B,IAAI,4CAAsB,oBAAI,IAAA;AAE9B,SAAS,0CAAA;AACP,MAAI,OAAO,WAAW,YACpB;AAGF,WAAS,kBAAkB,OAAY;AACrC,WAAO,kBAAkB;EAC3B;AAEA,MAAI,oBAAoB,CAAC,MAAA;AACvB,QAAI,CAAC,kBAAkB,CAAA,KAAM,CAAC,EAAE,OAC9B;AAGF,QAAI,cAAc,2CAAqB,IAAI,EAAE,MAAM;AACnD,QAAI,CAAC,aAAa;AAChB,oBAAc,oBAAI,IAAA;AAClB,iDAAqB,IAAI,EAAE,QAAQ,WAAA;AAKnC,QAAE,OAAO,iBAAiB,oBAAoB,iBAAiB;QAC7D,MAAM;MACR,CAAA;IACF;AAEA,gBAAY,IAAI,EAAE,YAAY;EAChC;AAEA,MAAI,kBAAkB,CAAC,MAAA;AACrB,QAAI,CAAC,kBAAkB,CAAA,KAAM,CAAC,EAAE,OAC9B;AAGF,QAAI,aAAa,2CAAqB,IAAI,EAAE,MAAM;AAClD,QAAI,CAAC,WACH;AAGF,eAAW,OAAO,EAAE,YAAY;AAGhC,QAAI,WAAW,SAAS,GAAG;AACzB,QAAE,OAAO,oBAAoB,oBAAoB,eAAA;AACjD,iDAAqB,OAAO,EAAE,MAAM;IACtC;AAGA,QAAI,2CAAqB,SAAS,GAAG;AACnC,eAAS,MAAM,0CACb,IAAA;AAGF,gDAAoB,MAAK;IAC3B;EACF;AAEA,WAAS,KAAK,iBAAiB,iBAAiB,iBAAA;AAChD,WAAS,KAAK,iBAAiB,iBAAiB,eAAA;AAClD;AAEA,IAAI,OAAO,aAAa,aAAA;AACtB,MAAI,SAAS,eAAe,UAC1B,yCAAA;MAEA,UAAS,iBAAiB,oBAAoB,uCAAA;;AASlD,SAAS,gDAAA;AACP,aAAW,CAAC,WAAA,KAAgB;AAG1B,QAAI,iBAAiB,eAAe,CAAC,YAAY,YAC/C,4CAAqB,OAAO,WAAA;AAGlC;AAEO,SAAS,0CAAmB,IAAc;AAE/C,wBAAsB,MAAA;AACpB,kDAAA;AAGA,QAAI,2CAAqB,SAAS,EAChC,IAAA;QAEA,2CAAoB,IAAI,EAAA;EAE5B,CAAA;AACF;;;;;;;ACjGO,SAAS,4CAAA;AACd,MAAI,mBAAkB,GAAA,cAAAC,QAAO,oBAAI,IAAA,CAAA;AACjC,MAAI,qBAAoB,GAAA,cAAAC,aAAY,CAAC,aAAa,MAAM,UAAU,YAAA;AAEhE,QAAI,MAAK,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,QAAO,IAAI,SAAA;AAC3B,sBAAgB,QAAQ,OAAO,QAAA;AAC/B,eAAA,GAAY,IAAA;IACd,IAAI;AACJ,oBAAgB,QAAQ,IAAI,UAAU;;;;;IAA+B,CAAA;AACrE,gBAAY,iBAAiB,MAAM,IAAI,OAAA;EACzC,GAAG,CAAA,CAAE;AACL,MAAI,wBAAuB,GAAA,cAAAA,aAAY,CAAC,aAAa,MAAM,UAAU,YAAA;QAC1D;AAAT,QAAI,OAAK,+BAAA,gBAAgB,QAAQ,IAAI,QAAA,OAAA,QAA5B,iCAAA,SAAA,SAAA,6BAAuC,OAAM;AACtD,gBAAY,oBAAoB,MAAM,IAAI,OAAA;AAC1C,oBAAgB,QAAQ,OAAO,QAAA;EACjC,GAAG,CAAA,CAAE;AACL,MAAI,4BAA2B,GAAA,cAAAA,aAAY,MAAA;AACzC,oBAAgB,QAAQ,QAAQ,CAAC,OAAO,QAAA;AACtC,2BAAqB,MAAM,aAAa,MAAM,MAAM,KAAK,MAAM,OAAO;IACxE,CAAA;EACF,GAAG;IAAC;GAAqB;AAGzB,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,WAAO;EACT,GAAG;IAAC;GAAyB;AAE7B,SAAO;;;;EAAkE;AAC3E;;;;AC5BO,SAAS,0CAAgB,KAAuF;AACrH,QAAM,UAAqC,GAAA,cAAAC,QAAU,IAAA;AACrD,QAAM,cAAoD,GAAA,cAAAA,QAAO,MAAA;AAEjE,QAAM,aAAY,GAAA,cAAAC,aAChB,CAAC,aAAA;AACC,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,cAAc;AACpB,YAAM,aAAa,YAAY,QAAA;AAC/B,aAAO,MAAA;AACL,YAAI,OAAO,eAAe,WACxB,YAAA;YAEA,aAAY,IAAA;MAEhB;IACF,WAAW,KAAK;AACd,UAAI,UAAU;AACd,aAAO,MAAA;AACL,YAAI,UAAU;MAChB;IACF;EACF,GACA;IAAC;GAAI;AAGP,UAAO,GAAA,cAAAC,SACL,OAAO;IACL,IAAI,UAAU;AACZ,aAAO,OAAO;IAChB;IACA,IAAI,QAAQ,OAAO;AACjB,aAAO,UAAU;AACjB,UAAI,WAAW,SAAS;AACtB,mBAAW,QAAO;AAClB,mBAAW,UAAU;MACvB;AAEA,UAAI,SAAS,KACX,YAAW,UAAU,UAAU,KAAA;IAEnC;EACF,IACA;IAAC;GAAU;AAEf;;;;;;;;;;;;AC/CO,SAAS,0CAAc,SAAkC,KAAyB;AACvF,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,WAAW,QAAQ,OAAO,KAAK;AACjC,cAAQ,IAAI,UAAU,IAAI;AAC1B,aAAO,MAAA;AACL,YAAI,QAAQ,IACV,SAAQ,IAAI,UAAU;MAE1B;IACF;EACF,CAAA;AACF;;;;ACZA,IAAI,uCAAiB,OAAO,aAAa,eAAe,OAAO;;;;;;;;;ACKxD,SAAS,0CAAe,OAAgC;AAE7D,MAAK,MAAc,mBAAmB,KAAK,MAAM,UAC/C,QAAO;AAMT,OAAI,GAAA,2CAAQ,KAAQ,MAAuB,YACzC,QAAO,MAAM,SAAS,WAAW,MAAM,YAAY;AAGrD,SAAO,MAAM,WAAW,KAAK,CAAE,MAAuB;AACxD;AAEO,SAAS,0CAAsB,OAAmB;AAOvD,SACG,EAAC,GAAA,2CAAQ,KAAO,MAAM,UAAU,KAAK,MAAM,WAAW,KACtD,MAAM,UAAU,KACf,MAAM,WAAW,KACjB,MAAM,aAAa,KACnB,MAAM,WAAW,KACjB,MAAM,gBAAgB;AAG5B;;;;;;;;;;;;;;;;;;;;;;ACzDA,IAAM,0CAAoB;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF,IAAM,mDAA6B,wCAAkB,KAAK,iBAAA,IAAqB;AAE/E,wCAAkB,KAAK,iDAAA;AACvB,IAAM,kDAA4B,wCAAkB,KAAK,sCAAA;AAElD,SAAS,0CAAY,SAAgB;AAC1C,SAAO,QAAQ,QAAQ,gDAAA;AACzB;;;;;;;ACYA,IAAI,wDAAkC;AACtC,IAAI,mCAAa;AAEjB,SAAS,2DAAA;AACP,0DAAkC;AAMlC,aAAW,MAAA;AACT,4DAAkC;EACpC,GAAG,EAAA;AACL;AAEA,SAAS,+CAAyB,GAAC;AACjC,MAAI,EAAE,gBAAgB,QACpB,0DAAA;AAEJ;AAEA,SAAS,+CAAA;AACP,MAAI,OAAO,aAAa,YACtB;AAGF,MAAI,OAAO,iBAAiB,YAC1B,UAAS,iBAAiB,aAAa,8CAAA;WAC9B,MACT,UAAS,iBAAiB,YAAY,wDAAA;AAGxC;AACA,SAAO,MAAA;AACL;AACA,QAAI,mCAAa,EACf;AAGF,QAAI,OAAO,iBAAiB,YAC1B,UAAS,oBAAoB,aAAa,8CAAA;aACjC,MACT,UAAS,oBAAoB,YAAY,wDAAA;EAE7C;AACF;AAMO,SAAS,0CAAS,OAAiB;AACxC,MAAI,EAAA,cACU,eACC,YACH,WACA,IACR;AAEJ,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,SAAQ,GAAA,eAAAC,QAAO;IACjB,WAAW;IACX,2BAA2B;IAC3B,aAAa;IACb,QAAQ;EACV,CAAA,EAAG;AAEH,GAAA,GAAA,eAAAC,WAAU,8CAAwB,CAAA,CAAE;AACpC,MAAI,EAAA,mBAAkB,yBAA0B,KAAI,GAAA,2CAAiB;AAErE,MAAI,EAAA,YAAW,gBAAiB,KAAI,GAAA,eAAAC,SAAQ,MAAA;AAC1C,QAAI,oBAAoB,CAAC,OAAO,gBAAA;AAC9B,YAAM,cAAc;AACpB,UAAI,cAAc,gBAAgB,WAAW,MAAM,aAAa,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,EACxG;AAGF,YAAM,YAAY;AAClB,UAAI,SAAS,MAAM;AACnB,YAAM,SAAS;AAMf,yBAAkB,GAAA,2CAAiB,MAAM,MAAM,GAAG,eAAe,CAAA,MAAA;AAC/D,YAAI,MAAM,aAAa,MAAM,UAAU,EAAC,GAAA,2CAAa,MAAM,QAAQ,EAAE,MAAM,EACzE,CAAAC,iBAAgB,GAAG,EAAE,WAAW;MAEpC,GAAG;QAAC,SAAS;MAAI,CAAA;AAEjB,UAAI,aACF,cAAa;QACX,MAAM;;;MAGR,CAAA;AAGF,UAAI,cACF,eAAc,IAAA;AAGhB,iBAAW,IAAA;IACb;AAEA,QAAIA,mBAAkB,CAAC,OAAO,gBAAA;AAC5B,UAAI,SAAS,MAAM;AACnB,YAAM,cAAc;AACpB,YAAM,SAAS;AAEf,UAAI,gBAAgB,WAAW,CAAC,MAAM,aAAa,CAAC,OAClD;AAGF,YAAM,YAAY;AAClB,+BAAA;AAEA,UAAI,WACF,YAAW;QACT,MAAM;;;MAGR,CAAA;AAGF,UAAI,cACF,eAAc,KAAA;AAGhB,iBAAW,KAAA;IACb;AAEA,QAAIC,cAA4B,CAAC;AAEjC,QAAI,OAAO,iBAAiB,aAAa;AACvC,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,yDAAmC,EAAE,gBAAgB,QACvD;AAGF,0BAAkB,GAAG,EAAE,WAAW;MACpC;AAEA,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,CAAC,cAAc,EAAE,cAAc,SAAS,EAAE,MAAM,EAClD,CAAAD,iBAAgB,GAAG,EAAE,WAAW;MAEpC;IACF,WAAW,OAAiC;AAC1C,MAAAC,YAAW,eAAe,MAAA;AACxB,cAAM,4BAA4B;MACpC;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,CAAC,MAAM,6BAA6B,CAAC,sDACvC,mBAAkB,GAAG,OAAA;AAGvB,cAAM,4BAA4B;MACpC;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,CAAC,cAAc,EAAE,cAAc,SAAS,EAAE,MAAM,EAClD,CAAAD,iBAAgB,GAAG,OAAA;MAEvB;IACF;AACA,WAAO;kBAACC;uBAAYD;IAAe;EACrC,GAAG;IAAC;IAAc;IAAe;IAAY;IAAY;IAAO;IAAmB;GAAyB;AAE5G,GAAA,GAAA,eAAAF,WAAU,MAAA;AAGR,QAAI,WACF,iBAAgB;MAAC,eAAe,MAAM;IAAM,GAAG,MAAM,WAAW;EAGpE,GAAG;IAAC;GAAW;AAEf,SAAO;;;EAGP;AACF;;;;AC1MO,SAAS,yCAA+C,aAAkB;AAC/E,MAAI,QAAQ;AACZ,QAAM,cAAc;AACpB,QAAM,qBAAqB,MAAM,MAAM;AAEvC,QAAM,uBAAuB,MAAO,MAAc;AAClD,QAAM,UAAU,MAAA;EAAO;AACvB,SAAO;AACT;AAEO,SAAS,0CAAe,OAAc,QAAe;AAC1D,SAAO,eAAe,OAAO,UAAU;IAAC,OAAO;EAAM,CAAA;AACrD,SAAO,eAAe,OAAO,iBAAiB;IAAC,OAAO;EAAM,CAAA;AAC9D;AAEO,SAAS,0CAAwD,QAA4C;AAClH,MAAI,YAAW,GAAA,eAAAI,QAAO;IACpB,WAAW;IACX,UAAU;EACZ,CAAA;AAIA,GAAA,GAAA,2CAAgB,MAAA;AACd,UAAM,QAAQ,SAAS;AACvB,WAAO,MAAA;AACL,UAAI,MAAM,UAAU;AAClB,cAAM,SAAS,WAAU;AACzB,cAAM,WAAW;MACnB;IACF;EACF,GAAG,CAAA,CAAE;AAEL,MAAI,gBAAe,GAAA,2CAAe,CAAC,MAAA;AACjC,eAAA,QAAA,WAAA,SAAA,SAAA,OAAS,CAAA;EACX,CAAA;AAGA,UAAO,GAAA,eAAAC,aAAY,CAAC,MAAA;AAKlB,QACE,EAAE,kBAAkB,qBACpB,EAAE,kBAAkB,oBACpB,EAAE,kBAAkB,uBACpB,EAAE,kBAAkB,mBACpB;AACA,eAAS,QAAQ,YAAY;AAE7B,UAAI,SAAS,EAAE;AACf,UAAI,gBAA2D,CAACC,OAAA;AAC9D,iBAAS,QAAQ,YAAY;AAE7B,YAAI,OAAO,UAAU;AAEnB,cAAI,QAAQ,yCAA8CA,EAAA;AAC1D,uBAAa,KAAA;QACf;AAGA,YAAI,SAAS,QAAQ,UAAU;AAC7B,mBAAS,QAAQ,SAAS,WAAU;AACpC,mBAAS,QAAQ,WAAW;QAC9B;MACF;AAEA,aAAO,iBAAiB,YAAY,eAAe;QAAC,MAAM;MAAI,CAAA;AAE9D,eAAS,QAAQ,WAAW,IAAI,iBAAiB,MAAA;AAC/C,YAAI,SAAS,QAAQ,aAAa,OAAO,UAAU;cACjD;WAAA,6BAAA,SAAS,QAAQ,cAAQ,QAAzB,+BAAA,SAAA,SAAA,2BAA2B,WAAU;AACrC,cAAI,kBAAkB,WAAW,SAAS,gBAAgB,OAAO,SAAS;AAC1E,iBAAO,cAAc,IAAI,WAAW,QAAQ;YAAC,eAAe;UAAe,CAAA,CAAA;AAC3E,iBAAO,cAAc,IAAI,WAAW,YAAY;YAAC,SAAS;YAAM,eAAe;UAAe,CAAA,CAAA;QAChG;MACF,CAAA;AAEA,eAAS,QAAQ,SAAS,QAAQ,QAAQ;QAAC,YAAY;QAAM,iBAAiB;UAAC;;MAAW,CAAA;IAC5F;EACF,GAAG;IAAC;GAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,QAA+B;AAE1D,SAAO,UAAU,EAAC,GAAA,2CAAY,MAAA,EAC5B,UAAS,OAAO;AAGlB,MAAIC,WAAS,GAAA,2CAAe,MAAA;AAC5B,MAAI,gBAAgBA,QAAO,SAAS;AACpC,MAAI,CAAC,iBAAiB,kBAAkB,OACtC;AAGF,8CAAmB;AACnB,MAAI,eAAe;AACnB,MAAI,SAAS,CAAC,MAAA;AACZ,QAAI,EAAE,WAAW,iBAAiB,aAChC,GAAE,yBAAwB;EAE9B;AAEA,MAAI,aAAa,CAAC,MAAA;AAChB,QAAI,EAAE,WAAW,iBAAiB,cAAc;AAC9C,QAAE,yBAAwB;AAI1B,UAAI,CAAC,UAAU,CAAC,cAAc;AAC5B,uBAAe;AACf,SAAA,GAAA,2CAAsB,aAAA;AACtB,gBAAA;MACF;IACF;EACF;AAEA,MAAI,UAAU,CAAC,MAAA;AACb,QAAI,EAAE,WAAW,UAAU,aACzB,GAAE,yBAAwB;EAE9B;AAEA,MAAI,YAAY,CAAC,MAAA;AACf,QAAI,EAAE,WAAW,UAAU,cAAc;AACvC,QAAE,yBAAwB;AAE1B,UAAI,CAAC,cAAc;AACjB,uBAAe;AACf,SAAA,GAAA,2CAAsB,aAAA;AACtB,gBAAA;MACF;IACF;EACF;AAEA,EAAAA,QAAO,iBAAiB,QAAQ,QAAQ,IAAA;AACxC,EAAAA,QAAO,iBAAiB,YAAY,YAAY,IAAA;AAChD,EAAAA,QAAO,iBAAiB,WAAW,WAAW,IAAA;AAC9C,EAAAA,QAAO,iBAAiB,SAAS,SAAS,IAAA;AAE1C,MAAI,UAAU,MAAA;AACZ,yBAAqB,GAAA;AACrB,IAAAA,QAAO,oBAAoB,QAAQ,QAAQ,IAAA;AAC3C,IAAAA,QAAO,oBAAoB,YAAY,YAAY,IAAA;AACnD,IAAAA,QAAO,oBAAoB,WAAW,WAAW,IAAA;AACjD,IAAAA,QAAO,oBAAoB,SAAS,SAAS,IAAA;AAC7C,gDAAmB;AACnB,mBAAe;EACjB;AAEA,MAAI,MAAM,sBAAsB,OAAA;AAChC,SAAO;AACT;;;AClJA,IAAI,8BAAe;AACnB,IAAI,wCAAkB;AACtB,IAAI,2CAAqB,oBAAI,QAAA;AAEtB,SAAS,0CAAqB,QAAgB;AACnD,OAAI,GAAA,2CAAI,GAAK;AACX,QAAI,gCAAU,WAAW;AAEvB,YAAM,kBAAiB,GAAA,2CAAiB,MAAA;AACxC,8CAAkB,eAAe,gBAAgB,MAAM;AACvD,qBAAe,gBAAgB,MAAM,mBAAmB;IAC1D;AAEA,kCAAQ;EACV,WAAW,kBAAkB,eAAe,kBAAkB,YAAY;AAGxE,QAAI,WAAW,gBAAgB,OAAO,QAAQ,eAAe;AAC7D,6CAAmB,IAAI,QAAQ,OAAO,MAAM,QAAA,CAAS;AACrD,WAAO,MAAM,QAAA,IAAY;EAC3B;AACF;AAEO,SAAS,0CAAqB,QAAgB;AACnD,OAAI,GAAA,2CAAI,GAAK;AAGX,QAAI,gCAAU,WACZ;AAGF,kCAAQ;AAIR,eAAW,MAAA;AAGT,OAAA,GAAA,2CAAmB,MAAA;AAEjB,YAAI,gCAAU,aAAa;AAEzB,gBAAM,kBAAiB,GAAA,2CAAiB,MAAA;AACxC,cAAI,eAAe,gBAAgB,MAAM,qBAAqB,OAC5D,gBAAe,gBAAgB,MAAM,mBAAmB,yCAAmB;AAG7E,kDAAkB;AAClB,wCAAQ;QACV;MACF,CAAA;IACF,GAAG,GAAA;EACL,WAAW,kBAAkB,eAAe,kBAAkB,YAG5D;AAAA,QAAI,UAAU,yCAAmB,IAAI,MAAA,GAAS;AAC5C,UAAI,sBAAsB,yCAAmB,IAAI,MAAA;AACjD,UAAI,WAAW,gBAAgB,OAAO,QAAQ,eAAe;AAE7D,UAAI,OAAO,MAAM,QAAA,MAAc,OAC7B,QAAO,MAAM,QAAA,IAAY;AAG3B,UAAI,OAAO,aAAa,OAAA,MAAa,GACnC,QAAO,gBAAgB,OAAA;AAEzB,+CAAmB,OAAO,MAAA;IAC5B;EAAA;AAEJ;;;;AC/EO,IAAM,6CAAwB,GAAA,eAAAC,SAAM,cAAsC;EAAC,UAAU,MAAA;EAAO;AAAC,CAAA;AACpG,0CAAsB,cAAc;;;ACtBpC,SAAS,4BAA4B,UAAU,YAAY;AACvD,MAAI,WAAW,IAAK,QAAO,WAAW,IAAI,KAAK,QAAQ;AAEvD,SAAO,WAAW;AACtB;;;ACJA,SAAS,gCAAgC,UAAU,YAAY,QAAQ;AACnE,MAAI,CAAC,WAAW,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,kBAAkB,SAAS,gCAAgC;AAE9G,SAAO,WAAW,IAAI,QAAQ;AAClC;;;ACDA,SAAS,yBAAyB,UAAU,YAAY;AACpD,MAAI,aAAa,gCAAgC,UAAU,YAAY,KAAK;AAC5E,SAAO,4BAA4B,UAAU,UAAU;AAC3D;;;ACNA,SAAS,6BAA6B,KAAK,mBAAmB;AAC1D,MAAI,kBAAkB,IAAI,GAAG,GAAG;AAC5B,UAAM,IAAI,UAAU,gEAAgE;AAAA,EACxF;AACJ;;;ACFA,SAAS,0BAA0B,KAAK,YAAY,OAAO;AACvD,+BAA6B,KAAK,UAAU;AAC5C,aAAW,IAAI,KAAK,KAAK;AAC7B;;;ACLA,SAAS,4BAA4B,UAAU,YAAY,OAAO;AAC9D,MAAI,WAAW,IAAK,YAAW,IAAI,KAAK,UAAU,KAAK;AAAA,OAClD;AACD,QAAI,CAAC,WAAW,UAAU;AAItB,YAAM,IAAI,UAAU,0CAA0C;AAAA,IAClE;AACA,eAAW,QAAQ;AAAA,EACvB;AACJ;;;ACRA,SAAS,yBAAyB,UAAU,YAAY,OAAO;AAC3D,MAAI,aAAa,gCAAgC,UAAU,YAAY,KAAK;AAC5E,8BAA4B,UAAU,YAAY,KAAK;AACvD,SAAO;AACX;;;;;ACwFA,SAAS,+CAAyB,OAAqB;AAErD,MAAI,WAAU,GAAA,eAAAC,aAAW,GAAA,0CAAoB;AAC7C,MAAI,SAAS;AACX,QAAI,EAAA,UAAW,GAAG,aAAA,IAAgB;AAClC,aAAQ,GAAA,2CAAW,cAAc,KAAA;AACjC,aAAA;EACF;AACA,GAAA,GAAA,2CAAW,SAAS,MAAM,GAAG;AAE7B,SAAO;AACT;IAYE,+CAAA,oBAAA,QAAA;AAVF,IAAM,mCAAN,MAAM;EAyCJ,sBAAsB;wCACf,8CAAyB,KAAA;EAChC;EAEA,IAAI,wBAAwB;AAC1B,YAAA,GAAA,0BAAO,MAAK,4CAAA;EACd;EAnCA,YAAY,MAA2B,aAA0B,eAA0B,OAAoB;AAF/G,KAAA,GAAA,2BAAA,MAAA,8CAAA;;aAAA;;wCAAA,8CAAyB,IAAA;QAGH;AAApB,QAAI,iBAAgB,gBAAA,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,YAAM,QAAb,kBAAA,SAAA,gBAAiB,cAAc;AACnD,UAAM,OAA6B,kBAAA,QAAA,kBAAA,SAAA,SAAA,cAA2B,sBAAqB;AACnF,QAAI,GAAG,IAAI;AACX,QAAI,SAAS,UAAyB;AACtC,QAAI,cAAc,WAAW,QAAQ,cAAc,WAAW,MAAM;AAClE,gBAAU,cAAc;AACxB,gBAAU,cAAc;IAC1B;AACA,QAAI,MAAA;AACF,UAAI,WAAW,QAAQ,WAAW,MAAM;AACtC,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,KAAK;MACrB,OAAO;AACL,YAAI,KAAK,QAAQ;AACjB,YAAI,KAAK,SAAS;MACpB;;AAEF,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,SAAS,cAAc;AAC5B,SAAK,WAAW,cAAc;AAC9B,SAAK,UAAU,cAAc;AAC7B,SAAK,UAAU,cAAc;AAC7B,SAAK,SAAS,cAAc;AAC5B,SAAK,IAAI;AACT,SAAK,IAAI;EACX;AASF;AAEA,IAAM,qCAAe,OAAO,aAAA;AAC5B,IAAM,iCAAW;AACjB,IAAM,4CAAsB;AAOrB,SAAS,0CAAS,OAAqB;AAC5C,MAAI,EAAA,SACK,eACM,cACD,YACF,WACD,SACF,YAEP,WAAW,eAAa,qBACL,2BACM,2BAEzB,KAAK,QACL,GAAG,SAAA,IACD,+CAAyB,KAAA;AAE7B,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,OAAM,GAAA,eAAAC,QAAmB;IAC3B,WAAW;IACX,2BAA2B;IAC3B,mBAAmB;IACnB,mBAAmB;IACnB,iBAAiB;IACjB,QAAQ;IACR,cAAc;IACd,aAAa;IACb,aAAa,CAAA;EACf,CAAA;AAEA,MAAI,EAAA,mBAAkB,yBAA0B,KAAI,GAAA,2CAAiB;AAErE,MAAI,qBAAoB,GAAA,2CAAe,CAAC,eAA0B,gBAAA;AAChE,QAAI,QAAQ,IAAI;AAChB,QAAI,cAAc,MAAM,kBACtB,QAAO;AAGT,QAAI,wBAAwB;AAC5B,UAAM,oBAAoB;AAC1B,QAAI,cAAc;AAChB,UAAI,QAAQ,IAAI,iCAAW,cAAc,aAAa,aAAA;AACtD,mBAAa,KAAA;AACb,8BAAwB,MAAM;IAChC;AAEA,QAAI,cACF,eAAc,IAAA;AAGhB,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB;AAC1B,eAAW,IAAA;AACX,WAAO;EACT,CAAA;AAEA,MAAI,mBAAkB,GAAA,2CAAe,CAAC,eAA0B,aAA0B,aAAa,SAAI;AACzG,QAAI,QAAQ,IAAI;AAChB,QAAI,CAAC,MAAM,kBACT,QAAO;AAGT,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB;AAE1B,QAAI,wBAAwB;AAC5B,QAAI,YAAY;AACd,UAAI,QAAQ,IAAI,iCAAW,YAAY,aAAa,aAAA;AACpD,iBAAW,KAAA;AACX,8BAAwB,MAAM;IAChC;AAEA,QAAI,cACF,eAAc,KAAA;AAGhB,eAAW,KAAA;AAEX,QAAI,WAAW,cAAc,CAAC,YAAY;AACxC,UAAI,QAAQ,IAAI,iCAAW,SAAS,aAAa,aAAA;AACjD,cAAQ,KAAA;AACR,gCAAA,wBAA0B,MAAM;IAClC;AAEA,UAAM,oBAAoB;AAC1B,WAAO;EACT,CAAA;AAEA,MAAI,kBAAiB,GAAA,2CAAe,CAAC,eAA0B,gBAAA;AAC7D,QAAI,QAAQ,IAAI;AAChB,QAAI,WACF,QAAO;AAGT,QAAI,WAAW;AACb,YAAM,oBAAoB;AAC1B,UAAI,QAAQ,IAAI,iCAAW,WAAW,aAAa,aAAA;AACnD,gBAAU,KAAA;AACV,YAAM,oBAAoB;AAC1B,aAAO,MAAM;IACf;AAEA,WAAO;EACT,CAAA;AAEA,MAAI,UAAS,GAAA,2CAAe,CAAC,MAAA;AAC3B,QAAI,QAAQ,IAAI;AAChB,QAAI,MAAM,aAAa,MAAM,QAAQ;AACnC,UAAI,MAAM,qBAAqB,MAAM,eAAe,KAClD,iBAAgB,kCAAY,MAAM,QAAQ,CAAA,GAAI,MAAM,aAAa,KAAA;AAEnE,YAAM,YAAY;AAClB,YAAM,eAAe;AACrB,YAAM,kBAAkB;AACxB,YAAM,cAAc;AACpB,+BAAA;AACA,UAAI,CAAC,0BACH,EAAA,GAAA,2CAAqB,MAAM,MAAM;AAEnC,eAAS,WAAW,MAAM,YACxB,SAAA;AAEF,YAAM,cAAc,CAAA;IACtB;EACF,CAAA;AAEA,MAAI,uBAAsB,GAAA,2CAAe,CAAC,MAAA;AACxC,QAAI,0BACF,QAAO,CAAA;EAEX,CAAA;AAEA,MAAI,gBAAe,GAAA,2CAAe,CAAC,MAAA;AACjC,gBAAA,QAAA,YAAA,SAAA,SAAA,QAAU,CAAA;EACZ,CAAA;AAEA,MAAI,yBAAwB,GAAA,2CAAe,CAAC,GAA+B,WAAA;AAMzE,QAAI,SAAS;AACX,UAAI,QAAQ,IAAI,WAAW,SAAS,CAAA;AACpC,OAAA,GAAA,2CAAe,OAAO,MAAA;AACtB,eAAQ,GAAA,0CAAqB,KAAA,CAAA;IAC/B;EACF,CAAA;AAEA,MAAI,cAAa,GAAA,eAAAC,SAAQ,MAAA;AACvB,QAAI,QAAQ,IAAI;AAChB,QAAIC,cAA4B;MAC9B,UAAU,GAAC;AACT,YAAI,2CAAqB,EAAE,aAAa,EAAE,aAAa,MAAK,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,GAAI;cAwCtH;AAvCF,cAAI,oDAA6B,GAAA,2CAAe,EAAE,WAAW,GAAG,EAAE,GAAG,EACnE,GAAE,eAAc;AAMlB,cAAI,wBAAwB;AAC5B,cAAI,CAAC,MAAM,aAAa,CAAC,EAAE,QAAQ;AACjC,kBAAM,SAAS,EAAE;AACjB,kBAAM,YAAY;AAClB,kBAAM,cAAc;AACpB,oCAAwB,kBAAkB,GAAG,UAAA;AAK7C,gBAAI,iBAAiB,EAAE;AACvB,gBAAI,UAAU,CAACC,OAAA;AACb,kBAAI,2CAAqBA,IAAG,cAAA,KAAmB,CAACA,GAAE,WAAU,GAAA,2CAAa,iBAAgB,GAAA,2CAAeA,EAAA,CAAA,KAAO,MAAM,OACnH,gBAAe,kCAAY,MAAM,QAAQA,EAAA,GAAI,UAAA;YAEjD;AAEA,+BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,UAAS,GAAA,2CAAM,SAAS,OAAA,GAAU,IAAA;UACzF;AAEA,cAAI,sBACF,GAAE,gBAAe;AAUnB,cAAI,EAAE,YAAW,GAAA,2CAAI,EAAA,EACnB,uBAAA,MAAM,mBAAa,QAAnB,yBAAA,SAAA,SAAA,qBAAqB,IAAI,EAAE,KAAK,EAAE,WAAW;QAEjD,WAAW,EAAE,QAAQ,OACnB,OAAM,gBAAgB,oBAAI,IAAA;MAE9B;MACA,QAAQ,GAAC;AACP,YAAI,KAAK,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAClE;AAGF,YAAI,KAAK,EAAE,WAAW,KAAK,CAAC,MAAM,qBAAqB,EAAE,GAAA,2CAAiB,WAAW;AACnF,cAAI,wBAAwB;AAC5B,cAAI,WACF,GAAE,eAAc;AAKlB,cAAI,CAAC,MAAM,6BAA6B,CAAC,MAAM,cAAc,MAAM,gBAAgB,cAAa,GAAA,2CAAe,EAAE,WAAW,IAAI;AAC9H,gBAAI,iBAAiB,kBAAkB,GAAG,SAAA;AAC1C,gBAAI,cAAc,eAAe,GAAG,SAAA;AACpC,gBAAI,eAAe,gBAAgB,GAAG,SAAA;AACtC,yBAAa,CAAA;AACb,oCAAwB,kBAAkB,eAAe;UAC3D,WAAW,MAAM,aAAa,MAAM,gBAAgB,YAAY;AAC9D,gBAAI,cAAc,MAAM,eAAgB,EAAE,YAA6B,eAA8B;AACrG,gBAAI,cAAc,eAAe,kCAAY,EAAE,eAAe,CAAA,GAAI,WAAA;AAClE,gBAAI,eAAgB,gBAAgB,kCAAY,EAAE,eAAe,CAAA,GAAI,aAAa,IAAA;AAClF,oCAAwB,eAAe;AACvC,kBAAM,eAAe;AACrB,yBAAa,CAAA;AACb,mBAAO,CAAA;UACT;AAEA,gBAAM,4BAA4B;AAClC,cAAI,sBACF,GAAE,gBAAe;QAErB;MACF;IACF;AAEA,QAAI,UAAU,CAAC,MAAA;UA0BkB;AAzB/B,UAAI,MAAM,aAAa,MAAM,UAAU,2CAAqB,GAAG,MAAM,MAAM,GAAG;YAwB5E;AAvBA,YAAI,oDAA6B,GAAA,2CAAe,CAAA,GAAI,EAAE,GAAG,EACvD,GAAE,eAAc;AAGlB,YAAI,UAAS,GAAA,2CAAe,CAAA;AAC5B,YAAI,cAAa,GAAA,2CAAa,MAAM,SAAQ,GAAA,2CAAe,CAAA,CAAA;AAC3D,wBAAgB,kCAAY,MAAM,QAAQ,CAAA,GAAI,YAAY,UAAA;AAC1D,YAAI,WACF,uBAAsB,GAAG,MAAM,MAAM;AAEvC,iCAAA;AAKA,YAAI,EAAE,QAAQ,WAAW,uCAAiB,MAAM,MAAM,MAAK,GAAA,2CAAa,MAAM,QAAQ,MAAA,KAAW,CAAC,EAAE,kCAAA,GAAe;AAGjH,YAAE,kCAAA,IAAgB;AAClB,WAAA,GAAA,2CAAS,MAAM,QAAQ,GAAG,KAAA;QAC5B;AAEA,cAAM,YAAY;SAClB,wBAAA,MAAM,mBAAa,QAAnB,0BAAA,SAAA,SAAA,sBAAqB,OAAO,EAAE,GAAG;MACnC,WAAW,EAAE,QAAQ,YAAU,uBAAA,MAAM,mBAAa,QAAnB,yBAAA,SAAA,SAAA,qBAAqB,OAAM;YAOtD;AAHF,YAAI,SAAS,MAAM;AACnB,cAAM,gBAAgB;AACtB,iBAAS,SAAS,OAAO,OAAM,EAAA,EAC7B,gBAAA,MAAM,YAAM,QAAZ,kBAAA,SAAA,SAAA,cAAc,cAAc,IAAI,cAAc,SAAS,KAAA,CAAA;MAE3D;IACF;AAEA,QAAI,OAAO,iBAAiB,aAAa;AACvC,MAAAD,YAAW,gBAAgB,CAAC,MAAA;AAE1B,YAAI,EAAE,WAAW,KAAK,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC/E;AAOF,aAAI,GAAA,2CAAsB,EAAE,WAAW,GAAG;AACxC,gBAAM,cAAc;AACpB;QACF;AAEA,cAAM,cAAc,EAAE;AAEtB,YAAI,wBAAwB;AAC5B,YAAI,CAAC,MAAM,WAAW;AACpB,gBAAM,YAAY;AAClB,gBAAM,eAAe;AACrB,gBAAM,kBAAkB,EAAE;AAC1B,gBAAM,SAAS,EAAE;AAEjB,cAAI,CAAC,0BACH,EAAA,GAAA,2CAAqB,MAAM,MAAM;AAGnC,kCAAwB,kBAAkB,GAAG,MAAM,WAAW;AAI9D,cAAI,UAAS,GAAA,2CAAe,EAAE,WAAW;AACzC,cAAI,2BAA2B,OAC7B,QAAO,sBAAsB,EAAE,SAAS;AAG1C,6BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,aAAa,aAAa,KAAA;AAC/E,6BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,iBAAiB,iBAAiB,KAAA;QACzF;AAEA,YAAI,sBACF,GAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,EAAE,WAAW,GAAG;AAClB,cAAI,qBAAqB;AACvB,gBAAI,WAAU,GAAA,2CAAa,EAAE,MAAM;AACnC,gBAAI,QACF,OAAM,YAAY,KAAK,OAAA;UAE3B;AAEA,YAAE,gBAAe;QACnB;MACF;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AAExB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,KAAM,MAAM,gBAAgB,UACzF;AAIF,YAAI,EAAE,WAAW,KAAK,CAAC,MAAM,UAC3B,gBAAe,GAAG,MAAM,eAAe,EAAE,WAAW;MAExD;AAEA,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,EAAE,cAAc,MAAM,mBAAmB,MAAM,UAAU,CAAC,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAC7G,gBAAM,eAAe;AACrB,4BAAkB,kCAAY,MAAM,QAAQ,CAAA,GAAI,MAAM,WAAW;QACnE;MACF;AAEA,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,EAAE,cAAc,MAAM,mBAAmB,MAAM,UAAU,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAC5G,gBAAM,eAAe;AACrB,0BAAgB,kCAAY,MAAM,QAAQ,CAAA,GAAI,MAAM,aAAa,KAAA;AACjE,8BAAoB,CAAA;QACtB;MACF;AAEA,UAAI,cAAc,CAAC,MAAA;AACjB,YAAI,EAAE,cAAc,MAAM,mBAAmB,MAAM,aAAa,EAAE,WAAW,KAAK,MAAM,QAAQ;AAC9F,eAAI,GAAA,2CAAa,MAAM,SAAQ,GAAA,2CAAe,CAAA,CAAA,KAAO,MAAM,eAAe,MAAM;AAW9E,gBAAI,UAAU;AACd,gBAAI,UAAU,WAAW,MAAA;AACvB,kBAAI,MAAM,aAAa,MAAM,kBAAkB,aAAA;AAC7C,oBAAI,QACF,QAAO,CAAA;qBACF;AACL,mBAAA,GAAA,2CAAsB,MAAM,MAAM;AAClC,wBAAM,OAAO,MAAK;gBACpB;;YAEJ,GAAG,EAAA;AAGH,8BAAkB,EAAE,eAA2B,SAAS,MAAM,UAAU,MAAM,IAAA;AAC9E,kBAAM,YAAY,KAAK,MAAM,aAAa,OAAA,CAAA;UAC5C,MACE,QAAO,CAAA;AAIT,gBAAM,eAAe;QACvB;MACF;AAEA,UAAI,kBAAkB,CAAC,MAAA;AACrB,eAAO,CAAA;MACT;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAIF,eAAO,CAAA;MACT;IACF,WAAW,OAAiC;AAI1C,MAAAA,YAAW,cAAc,CAAC,MAAA;AAExB,YAAI,EAAE,WAAW,KAAK,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC/E;AAGF,YAAI,MAAM,2BAA2B;AACnC,YAAE,gBAAe;AACjB;QACF;AAEA,cAAM,YAAY;AAClB,cAAM,eAAe;AACrB,cAAM,SAAS,EAAE;AACjB,cAAM,eAAc,GAAA,2CAAe,EAAE,WAAW,IAAI,YAAY;AAGhE,YAAI,yBAAwB,GAAA,kBAAAE,WAAU,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAA;AAClF,YAAI,sBACF,GAAE,gBAAe;AAGnB,YAAI,qBAAqB;AACvB,cAAI,WAAU,GAAA,2CAAa,EAAE,MAAM;AACnC,cAAI,QACF,OAAM,YAAY,KAAK,OAAA;QAE3B;AAEA,2BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,WAAW,WAAW,KAAA;MAC7E;AAEA,MAAAF,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,wBAAwB;AAC5B,YAAI,MAAM,aAAa,CAAC,MAAM,6BAA6B,MAAM,eAAe,MAAM;AACpF,gBAAM,eAAe;AACrB,kCAAwB,kBAAkB,GAAG,MAAM,WAAW;QAChE;AAEA,YAAI,sBACF,GAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,wBAAwB;AAC5B,YAAI,MAAM,aAAa,CAAC,MAAM,6BAA6B,MAAM,eAAe,MAAM;AACpF,gBAAM,eAAe;AACrB,kCAAwB,gBAAgB,GAAG,MAAM,aAAa,KAAA;AAC9D,8BAAoB,CAAA;QACtB;AAEA,YAAI,sBACF,GAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,YAAY,CAAC,MAAA;AACtB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,CAAC,MAAM,6BAA6B,EAAE,WAAW,KAAK,CAAC,MAAM,UAC/D,gBAAe,GAAG,MAAM,eAAe,OAAA;MAE3C;AAEA,UAAI,YAAY,CAAC,MAAA;AAEf,YAAI,EAAE,WAAW,EACf;AAGF,YAAI,MAAM,2BAA2B;AACnC,gBAAM,4BAA4B;AAClC;QACF;AAEA,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,EAAE,MAAM,KAAgB,MAAM,eAAe,KAAA;YAIrF,QAAO,CAAA;AAGT,cAAM,eAAe;MACvB;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,QAAQ,wCAAkB,EAAE,WAAW;AAC3C,YAAI,CAAC,MACH;AAEF,cAAM,kBAAkB,MAAM;AAC9B,cAAM,4BAA4B;AAClC,cAAM,eAAe;AACrB,cAAM,YAAY;AAClB,cAAM,SAAS,EAAE;AACjB,cAAM,cAAc;AAEpB,YAAI,CAAC,0BACH,EAAA,GAAA,2CAAqB,MAAM,MAAM;AAGnC,YAAI,wBAAwB,kBAAkB,uCAAiB,MAAM,QAAQ,CAAA,GAAI,MAAM,WAAW;AAClG,YAAI,sBACF,GAAE,gBAAe;AAGnB,2BAAkB,GAAA,2CAAe,EAAE,aAAa,GAAG,UAAU,UAAU,IAAA;MACzE;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,CAAC,MAAM,WAAW;AACpB,YAAE,gBAAe;AACjB;QACF;AAEA,YAAI,QAAQ,mCAAa,EAAE,aAAa,MAAM,eAAe;AAC7D,YAAI,wBAAwB;AAC5B,YAAI,SAAS,mCAAa,OAAO,EAAE,aAAa,GAC9C;AAAA,cAAI,CAAC,MAAM,gBAAgB,MAAM,eAAe,MAAM;AACpD,kBAAM,eAAe;AACrB,oCAAwB,kBAAkB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,WAAW;UACjG;QAAA,WACS,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAC1D,gBAAM,eAAe;AACrB,kCAAwB,gBAAgB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,aAAa,KAAA;AAC/F,8BAAoB,uCAAiB,MAAM,QAAS,CAAA,CAAA;QACtD;AAEA,YAAI,sBACF,GAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,aAAa,CAAC,MAAA;AACvB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,YAAI,CAAC,MAAM,WAAW;AACpB,YAAE,gBAAe;AACjB;QACF;AAEA,YAAI,QAAQ,mCAAa,EAAE,aAAa,MAAM,eAAe;AAC7D,YAAI,wBAAwB;AAC5B,YAAI,SAAS,mCAAa,OAAO,EAAE,aAAa,KAAK,MAAM,eAAe,MAAM;AAC9E,yBAAe,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,WAAW;AACpE,kCAAwB,gBAAgB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,WAAW;AAC7F,gCAAsB,EAAE,aAAa,MAAM,MAAM;QACnD,WAAW,MAAM,gBAAgB,MAAM,eAAe,KACpD,yBAAwB,gBAAgB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,aAAa,KAAA;AAGjG,YAAI,sBACF,GAAE,gBAAe;AAGnB,cAAM,YAAY;AAClB,cAAM,kBAAkB;AACxB,cAAM,eAAe;AACrB,cAAM,4BAA4B;AAClC,YAAI,MAAM,UAAU,CAAC,0BACnB,EAAA,GAAA,2CAAqB,MAAM,MAAM;AAEnC,iCAAA;MACF;AAEA,MAAAA,YAAW,gBAAgB,CAAC,MAAA;AAC1B,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,UAAE,gBAAe;AACjB,YAAI,MAAM,UACR,QAAO,uCAAiB,MAAM,QAAS,CAAA,CAAA;MAE3C;AAEA,UAAI,WAAW,CAAC,MAAA;AACd,YAAI,MAAM,cAAa,GAAA,4CAAa,GAAA,2CAAe,CAAA,GAAI,MAAM,MAAM,EACjE,QAAO;UACL,eAAe,MAAM;UACrB,UAAU;UACV,SAAS;UACT,SAAS;UACT,QAAQ;QACV,CAAA;MAEJ;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,EAC7D;AAGF,eAAO,CAAA;MACT;IACF;AAEA,WAAOA;EACT,GAAG;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;GACD;AAGD,GAAA,GAAA,eAAAG,WAAU,MAAA;AACR,QAAI,CAAC,UAAU,MACb;AAGF,UAAM,iBAAgB,GAAA,2CAAiB,OAAO,OAAO;AACrD,QAAI,CAAC,iBAAiB,CAAC,cAAc,QAAQ,cAAc,eAAe,8BAAA,EACxE;AAGF,UAAM,QAAQ,cAAc,cAAc,OAAA;AAC1C,UAAM,KAAK;AAIX,UAAM,cAAc;;KAEnB,yCAAA;;;;MAIC,KAAI;AACN,kBAAc,KAAK,QAAQ,KAAA;EAC7B,GAAG;IAAC;GAAO;AAGX,GAAA,GAAA,eAAAA,WAAU,MAAA;AACR,QAAI,QAAQ,IAAI;AAChB,WAAO,MAAA;UAEkB;AADvB,UAAI,CAAC,0BACH,EAAA,GAAA,4CAAqB,gBAAA,MAAM,YAAM,QAAZ,kBAAA,SAAA,gBAAgB,MAAA;AAEvC,eAAS,WAAW,MAAM,YACxB,SAAA;AAEF,YAAM,cAAc,CAAA;IACtB;EACF,GAAG;IAAC;GAA0B;AAE9B,SAAO;IACL,WAAW,iBAAiB;IAC5B,aAAY,GAAA,2CAAW,UAAU,YAAY;MAAC,CAAC,yCAAA,GAAsB;IAAI,CAAA;EAC3E;AACF;AAEA,SAAS,uCAAiB,QAAe;AACvC,SAAO,OAAO,YAAY,OAAO,OAAO,aAAa,MAAA;AACvD;AAEA,SAAS,2CAAqB,OAAsB,eAAsB;AACxE,QAAM,EAAA,KAAI,KAAM,IAAI;AACpB,QAAM,UAAU;AAChB,QAAM,OAAO,QAAQ,aAAa,MAAA;AAGlC,UACG,QAAQ,WAAW,QAAQ,OAAO,QAAQ,cAAc,SAAS,YAClE,EAAG,oBAAmB,GAAA,2CAAe,OAAA,EAAS,oBAAoB,CAAC,sCAAgB,SAAS,GAAA,KAC1F,oBAAmB,GAAA,2CAAe,OAAA,EAAS,uBAC3C,QAAQ;EAEV,GAAG,SAAS,UAAW,CAAC,QAAQ,uCAAiB,OAAA,MAAc,QAAQ;AAE3E;AA0CA,SAAS,kCAAY,QAA0B,GAAY;AACzD,MAAI,UAAU,EAAE;AAChB,MAAI,UAAU,EAAE;AAChB,SAAO;IACL,eAAe;IACf,UAAU,EAAE;IACZ,SAAS,EAAE;IACX,SAAS,EAAE;IACX,QAAQ,EAAE;;;EAGZ;AACF;AA0DA,SAAS,6CAAuB,QAAe;AAC7C,MAAI,kBAAkB,iBACpB,QAAO;AAGT,MAAI,kBAAkB,kBACpB,QAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAGrD,MAAI,uCAAiB,MAAA,EACnB,QAAO;AAGT,SAAO;AACT;AAEA,SAAS,mDAA6B,QAAiB,KAAW;AAChE,MAAI,kBAAkB,iBACpB,QAAO,CAAC,sCAAgB,QAAQ,GAAA;AAGlC,SAAO,6CAAuB,MAAA;AAChC;AAEA,IAAM,0CAAoB,oBAAI,IAAI;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAED,SAAS,sCAAgB,QAA0B,KAAW;AAE5D,SAAO,OAAO,SAAS,cAAc,OAAO,SAAS,UACjD,QAAQ,MACR,wCAAkB,IAAI,OAAO,IAAI;AACvC;;;;ACt+BA,IAAI,wCAAmC;AACvC,IAAI,uCAAiB,oBAAI,IAAA;AAIlB,IAAI,4CAA0B,oBAAI,IAAA;AACzC,IAAI,4CAAsB;AAC1B,IAAI,iDAA2B;AAG/B,IAAM,iDAA2B;EAC/B,KAAK;EACL,QAAQ;AACV;AAEA,SAAS,4CAAsB,UAAoB,GAAe;AAChE,WAAS,WAAW,qCAClB,SAAQ,UAAU,CAAA;AAEtB;AAKA,SAAS,iCAAW,GAAgB;AAElC,SAAO,EAAE,EAAE,WAAY,EAAC,GAAA,2CAAI,KAAO,EAAE,UAAW,EAAE,WAAW,EAAE,QAAQ,aAAa,EAAE,QAAQ,WAAW,EAAE,QAAQ;AACrH;AAGA,SAAS,0CAAoB,GAAgB;AAC3C,8CAAsB;AACtB,MAAI,iCAAW,CAAA,GAAI;AACjB,4CAAkB;AAClB,gDAAsB,YAAY,CAAA;EACpC;AACF;AAEA,SAAS,yCAAmB,GAA4B;AACtD,0CAAkB;AAClB,MAAI,EAAE,SAAS,eAAe,EAAE,SAAS,eAAe;AACtD,gDAAsB;AACtB,gDAAsB,WAAW,CAAA;EACnC;AACF;AAEA,SAAS,uCAAiB,GAAa;AACrC,OAAI,GAAA,2CAAe,CAAA,GAAI;AACrB,gDAAsB;AACtB,4CAAkB;EACpB;AACF;AAEA,SAAS,uCAAiB,GAAa;AAIrC,MAAI,EAAE,WAAW,UAAU,EAAE,WAAW,aAAY,GAAA,8CAAoB,CAAC,EAAE,UACzE;AAKF,MAAI,CAAC,6CAAuB,CAAC,gDAA0B;AACrD,4CAAkB;AAClB,gDAAsB,WAAW,CAAA;EACnC;AAEA,8CAAsB;AACtB,mDAA2B;AAC7B;AAEA,SAAS,yCAAA;AACP,MAAI,GAAA,0CACF;AAKF,8CAAsB;AACtB,mDAA2B;AAC7B;AAKA,SAAS,6CAAuB,SAA4B;AAC1D,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,0CAAwB,KAAI,GAAA,2CAAe,OAAA,CAAA,EACjH;AAGF,QAAM,gBAAe,GAAA,2CAAe,OAAA;AACpC,QAAM,kBAAiB,GAAA,2CAAiB,OAAA;AAMxC,MAAI,QAAQ,aAAa,YAAY,UAAU;AAC/C,eAAa,YAAY,UAAU,QAAQ,WAAA;AACzC,gDAAsB;AACtB,UAAM,MAAM,MAAM,SAAA;EACpB;AAEA,iBAAe,iBAAiB,WAAW,2CAAqB,IAAA;AAChE,iBAAe,iBAAiB,SAAS,2CAAqB,IAAA;AAC9D,iBAAe,iBAAiB,SAAS,wCAAkB,IAAA;AAI3D,eAAa,iBAAiB,SAAS,wCAAkB,IAAA;AACzD,eAAa,iBAAiB,QAAQ,wCAAkB,KAAA;AAExD,MAAI,OAAO,iBAAiB,aAAa;AACvC,mBAAe,iBAAiB,eAAe,0CAAoB,IAAA;AACnE,mBAAe,iBAAiB,eAAe,0CAAoB,IAAA;AACnE,mBAAe,iBAAiB,aAAa,0CAAoB,IAAA;EACnE,WAAW,OAAiC;AAC1C,mBAAe,iBAAiB,aAAa,0CAAoB,IAAA;AACjE,mBAAe,iBAAiB,aAAa,0CAAoB,IAAA;AACjE,mBAAe,iBAAiB,WAAW,0CAAoB,IAAA;EACjE;AAGA,eAAa,iBAAiB,gBAAgB,MAAA;AAC5C,sDAA4B,OAAA;EAC9B,GAAG;IAAC,MAAM;EAAI,CAAA;AAEd,4CAAwB,IAAI,cAAc;;EAAM,CAAA;AAClD;AAEA,IAAM,oDAA8B,CAAC,SAAS,iBAAA;AAC5C,QAAM,gBAAe,GAAA,2CAAe,OAAA;AACpC,QAAM,kBAAiB,GAAA,2CAAiB,OAAA;AACxC,MAAI,aACF,gBAAe,oBAAoB,oBAAoB,YAAA;AAEzD,MAAI,CAAC,0CAAwB,IAAI,YAAA,EAC/B;AAEF,eAAa,YAAY,UAAU,QAAQ,0CAAwB,IAAI,YAAA,EAAe;AAEtF,iBAAe,oBAAoB,WAAW,2CAAqB,IAAA;AACnE,iBAAe,oBAAoB,SAAS,2CAAqB,IAAA;AACjE,iBAAe,oBAAoB,SAAS,wCAAkB,IAAA;AAE9D,eAAa,oBAAoB,SAAS,wCAAkB,IAAA;AAC5D,eAAa,oBAAoB,QAAQ,wCAAkB,KAAA;AAE3D,MAAI,OAAO,iBAAiB,aAAa;AACvC,mBAAe,oBAAoB,eAAe,0CAAoB,IAAA;AACtE,mBAAe,oBAAoB,eAAe,0CAAoB,IAAA;AACtE,mBAAe,oBAAoB,aAAa,0CAAoB,IAAA;EACtE,WAAW,OAAiC;AAC1C,mBAAe,oBAAoB,aAAa,0CAAoB,IAAA;AACpE,mBAAe,oBAAoB,aAAa,0CAAoB,IAAA;AACpE,mBAAe,oBAAoB,WAAW,0CAAoB,IAAA;EACpE;AAEA,4CAAwB,OAAO,YAAA;AACjC;AAmBO,SAAS,0CAAuB,SAA4B;AACjE,QAAM,kBAAiB,GAAA,2CAAiB,OAAA;AACxC,MAAI;AACJ,MAAI,eAAe,eAAe,UAChC,8CAAuB,OAAA;OAClB;AACL,mBAAe,MAAA;AACb,mDAAuB,OAAA;IACzB;AACA,mBAAe,iBAAiB,oBAAoB,YAAA;EACtD;AAEA,SAAO,MAAM,kDAA4B,SAAS,YAAA;AACpD;AAIA,IAAI,OAAO,aAAa,YACtB,2CAAA;AAMK,SAAS,4CAAA;AACd,SAAO,0CAAoB;AAC7B;AAEO,SAAS,4CAAA;AACd,SAAO;AACT;AA4BA,IAAM,0CAAoB,oBAAI,IAAI;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAMD,SAAS,2CAAqB,aAAsB,UAAoB,GAAe;AACrF,MAAI,aAAW,GAAA,2CAAiB,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM;AACzC,QAAM,oBAAoB,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,mBAAmB;AAClH,QAAM,uBAAuB,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,sBAAsB;AACxH,QAAM,eAAe,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,cAAc;AACxG,QAAM,iBAAiB,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,gBAAgB;AAI5G,gBAAc,eACX,UAAS,yBAAyB,qBAAqB,CAAC,wCAAkB,IAAI,UAAS,cAAc,IAAI,KAC1G,UAAS,yBAAyB,wBACjC,UAAS,yBAAyB,gBAAgB,UAAS,cAAc;AAC5E,SAAO,EAAE,eAAe,aAAa,cAAc,aAAa,kBAAkB,CAAC,+CAAyB,EAAE,GAAG;AACnH;AAkBO,SAAS,0CAAwB,IAAyB,MAA0B,MAA8B;AACvH,+CAAA;AAEA,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,UAAU,CAAC,UAAoB,MAAA;AAEjC,UAAI,CAAC,2CAAqB,CAAC,EAAE,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,cAAc,UAAU,CAAA,EACzD;AAEF,SAAG,0CAAA,CAAA;IACL;AACA,yCAAe,IAAI,OAAA;AACnB,WAAO,MAAA;AACL,2CAAe,OAAO,OAAA;IACxB;EAEF,GAAG,IAAA;AACL;;;AC3TO,SAAS,0CAAY,SAAyB;AAMnD,QAAM,iBAAgB,GAAA,2CAAiB,OAAA;AACvC,QAAM,iBAAgB,GAAA,2CAAiB,aAAA;AACvC,OAAI,GAAA,2CAAqB,MAAQ,WAAW;AAC1C,QAAI,qBAAqB;AACzB,KAAA,GAAA,2CAAmB,MAAA;AAEjB,WAAI,GAAA,2CAAiB,aAAA,MAAmB,sBAAsB,QAAQ,YACpE,EAAA,GAAA,2CAAsB,OAAA;IAE1B,CAAA;EACF,MACE,EAAA,GAAA,2CAAsB,OAAA;AAE1B;;;;ACRO,SAAS,0CAA6D,OAAyB;AACpG,MAAI,EAAA,YAEF,SAAS,aACT,QAAQ,YAAU,cACL,IACX;AAEJ,QAAM,UAAuC,GAAA,eAAAC,aAAY,CAAC,MAAA;AACxD,QAAI,EAAE,WAAW,EAAE,eAAe;AAChC,UAAI,WACF,YAAW,CAAA;AAGb,UAAI,cACF,eAAc,KAAA;AAGhB,aAAO;IACT;EACF,GAAG;IAAC;IAAY;GAAc;AAG9B,QAAM,oBAAmB,GAAA,2CAA8B,MAAA;AAEvD,QAAM,WAAyC,GAAA,eAAAA,aAAY,CAAC,MAAA;AAI1D,UAAM,iBAAgB,GAAA,2CAAiB,EAAE,MAAM;AAC/C,UAAM,gBAAgB,iBAAgB,GAAA,2CAAiB,aAAA,KAAiB,GAAA,2CAAe;AACvF,QAAI,EAAE,WAAW,EAAE,iBAAiB,mBAAkB,GAAA,2CAAe,EAAE,WAAW,GAAG;AACnF,UAAI,YACF,aAAY,CAAA;AAGd,UAAI,cACF,eAAc,IAAA;AAGhB,uBAAiB,CAAA;IACnB;EACF,GAAG;IAAC;IAAe;IAAa;GAAiB;AAEjD,SAAO;IACL,YAAY;MACV,SAAU,CAAC,eAAe,eAAe,iBAAiB,cAAe,UAAU;MACnF,QAAS,CAAC,eAAe,cAAc,iBAAkB,SAAS;IACpE;EACF;AACF;;;ACpEO,SAAS,0CAA6C,SAAmC;AAC9F,MAAI,CAAC,QACH,QAAO;AAGT,MAAI,wBAAwB;AAC5B,SAAO,CAAC,MAAA;AACN,QAAI,QAAsB;MACxB,GAAG;MACH,iBAAA;AACE,UAAE,eAAc;MAClB;MACA,qBAAA;AACE,eAAO,EAAE,mBAAkB;MAC7B;MACA,kBAAA;AACE,YAAI,yBAAyB,KAC3B,SAAQ,MAAM,sIAAA;YAEd,yBAAwB;MAE5B;MACA,sBAAA;AACE,gCAAwB;MAC1B;MACA,uBAAA;AACE,eAAO;MACT;IACF;AAEA,YAAQ,KAAA;AAER,QAAI,sBACF,GAAE,gBAAe;EAErB;AACF;;;AC1BO,SAAS,0CAAY,OAAoB;AAC9C,SAAO;IACL,eAAe,MAAM,aAAa,CAAC,IAAI;MACrC,YAAW,GAAA,2CAAmB,MAAM,SAAS;MAC7C,UAAS,GAAA,2CAAmB,MAAM,OAAO;IAC3C;EACF;AACF;;;;ACAO,IAAI,6CAAmB,GAAA,eAAAC,SAAM,cAA4C,IAAA;AAEhF,SAAS,0CAAoB,KAAuC;AAClE,MAAI,WAAU,GAAA,eAAAC,YAAW,yCAAA,KAAqB,CAAC;AAC/C,GAAA,GAAA,2CAAW,SAAS,GAAA;AAGpB,MAAI,EAAC,KAAK,GAAG,GAAG,WAAA,IAAc;AAC9B,SAAO;AACT;AAKO,IAAM,4CAAoB,GAAA,eAAAD,SAAM,WAAW,SAAS,kBAAkB,OAA+B,KAAmC;AAC7I,MAAI,EAAA,UAAW,GAAG,WAAA,IAAc;AAChC,MAAI,UAAS,GAAA,2CAAa,GAAA;AAC1B,MAAI,UAAU;IACZ,GAAG;IACH,KAAK;EACP;AAEA,UACE,GAAA,eAAAA,SAAA,cAAC,0CAAiB,UAAQ;IAAC,OAAO;KAC/B,QAAA;AAGP,CAAA;AAUO,SAAS,0CAA4D,OAA4B,QAA0C;AAChJ,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS,KAAA;AAC5B,MAAI,EAAA,cAAc,KAAI,GAAA,2CAAY,KAAA;AAClC,MAAI,gBAAe,GAAA,2CAAW,YAAY,aAAA;AAC1C,MAAI,WAAW,0CAAoB,MAAA;AACnC,MAAI,mBAAmB,MAAM,aAAa,CAAC,IAAI;AAC/C,MAAI,gBAAe,GAAA,eAAAE,QAAO,MAAM,SAAS;AAEzC,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,aAAa,WAAW,OAAO,QACjC,EAAA,GAAA,2CAAY,OAAO,OAAO;AAE5B,iBAAa,UAAU;EACzB,GAAG;IAAC;GAAO;AAGX,MAAI,WAA+B,MAAM,sBAAsB,KAAK;AACpE,MAAI,MAAM,WACR,YAAW;AAGb,SAAO;IACL,iBAAgB,GAAA,2CACd;MACE,GAAG;;IAEL,GACA,gBAAA;EAEJ;AACF;AAMO,IAAM,6CAAY,GAAA,eAAAC,YAAW,CAAC,EAAA,UAAW,GAAG,MAAA,GAAiC,QAAA;AAClF,SAAM,GAAA,2CAAa,GAAA;AACnB,MAAI,EAAA,eAAe,IAAI,0CAAa,OAAO,GAAA;AAC3C,MAAI,SAAQ,GAAA,eAAAJ,SAAM,SAAS,KAAK,QAAA;AAEhC,GAAA,GAAA,eAAAG,WAAU,MAAA;AACR,QAAI,MACF;AAGF,QAAI,KAAK,IAAI;AACb,QAAI,CAAC,MAAM,EAAE,eAAc,GAAA,2CAAe,EAAA,EAAI,UAAU;AACtD,cAAQ,MAAM,0DAAA;AACd;IACF;AAEA,QAAI,CAAC,MAAM,cAAc,EAAC,GAAA,2CAAY,EAAA,GAAK;AACzC,cAAQ,KAAK,yFAAA;AACb;IACF;AAEA,QACE,GAAG,cAAc,YACjB,GAAG,cAAc,WACjB,GAAG,cAAc,YACjB,GAAG,cAAc,cACjB,GAAG,cAAc,OACjB,GAAG,cAAc,UACjB,GAAG,cAAc,aACjB,GAAG,cAAc,SACjB,GAAG,cAAc,OACjB;AACA,UAAI,OAAO,GAAG,aAAa,MAAA;AAC3B,UAAI,CAAC,KACH,SAAQ,KAAK,uDAAA;;;QAGb,SAAS,iBACT,SAAS,YACT,SAAS,cACT,SAAS,cACT,SAAS,cACT,SAAS,UACT,SAAS,cACT,SAAS,sBACT,SAAS,mBACT,SAAS,YACT,SAAS,WACT,SAAS,eACT,SAAS,eACT,SAAS,YACT,SAAS,gBACT,SAAS,YACT,SAAS,SACT,SAAS,cACT,SAAS,aACT,SAAS;QAET,SAAS,SACT,SAAS,WACT,SAAS;OAET,SAAQ,KAAK,8DAA8D,IAAA,IAAQ;IAEvF;EACF,GAAG;IAAC;IAAK,MAAM;GAAW;AAG1B,MAAI,WAAW,UAAS,GAAA,eAAAH,SAAM,SAAS,EAAA,IAAM,KAAK,MAAM,MAAM,MAAM,MAAM;AAE1E,UAAO,GAAA,eAAAA,SAAM,aACX,OACA;IACE,IAAG,GAAA,2CAAW,gBAAgB,MAAM,KAAK;;IAEzC,MAAK,GAAA,2CAAU,UAAU,GAAA;EAC3B,CAAA;AAEJ,CAAA;;;;ACpKO,IAAM,6CAAY,GAAA,eAAAK,SAAM,WAAW,CAAC,EAAA,UAAW,GAAG,MAAA,GAAwB,QAAA;AAC/E,SAAM,GAAA,2CAAa,GAAA;AACnB,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS;IAAC,GAAG;;EAAU,CAAA;AAC1C,MAAI,EAAA,eAAe,KAAI,GAAA,2CAAa,OAAO,GAAA;AAC3C,MAAI,SAAQ,GAAA,eAAAA,SAAM,SAAS,KAAK,QAAA;AAEhC,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,MACF;AAGF,QAAI,KAAK,IAAI;AACb,QAAI,CAAC,MAAM,EAAE,eAAc,GAAA,2CAAe,EAAA,EAAI,UAAU;AACtD,cAAQ,MAAM,0DAAA;AACd;IACF;AAEA,QAAI,CAAC,MAAM,cAAc,EAAC,GAAA,2CAAY,EAAA,GAAK;AACzC,cAAQ,KAAK,yFAAA;AACb;IACF;AAEA,QACE,GAAG,cAAc,YACjB,GAAG,cAAc,WACjB,GAAG,cAAc,YACjB,GAAG,cAAc,cACjB,GAAG,cAAc,OACjB,GAAG,cAAc,UACjB,GAAG,cAAc,WACjB;AACA,UAAI,OAAO,GAAG,aAAa,MAAA;AAC3B,UAAI,CAAC,KACH,SAAQ,KAAK,uDAAA;;;QAGb,SAAS,iBACT,SAAS,YACT,SAAS,cACT,SAAS,cACT,SAAS,cACT,SAAS,UACT,SAAS,cACT,SAAS,sBACT,SAAS,mBACT,SAAS,YACT,SAAS,WACT,SAAS,eACT,SAAS,eACT,SAAS,YACT,SAAS,gBACT,SAAS,YACT,SAAS,SACT,SAAS,aACT,SAAS;OAET,SAAQ,KAAK,8DAA8D,IAAA,IAAQ;IAEvF;EACF,GAAG;IAAC;IAAK,MAAM;GAAW;AAG1B,MAAI,WAAW,UAAS,GAAA,eAAAD,SAAM,SAAS,EAAA,IAAM,KAAK,MAAM,MAAM,MAAM,MAAM;AAE1E,UAAO,GAAA,eAAAA,SAAM,aACX,OACA;IACE,IAAG,GAAA,2CAAW,YAAY,gBAAgB,MAAM,KAAK;;IAErD,MAAK,GAAA,2CAAU,UAAU,GAAA;EAC3B,CAAA;AAEJ,CAAA;;;;ACxEO,IAAM,6CAAiB,GAAA,eAAAE,SAAM,WAAW,CAAC,EAAA,UAAW,GAAG,MAAA,GAA6B,QAAA;AACzF,MAAI,gBAAe,GAAA,eAAAC,QAAO,KAAA;AAC1B,MAAI,eAAc,GAAA,eAAAC,aAAW,GAAA,0CAAoB;AACjD,SAAM,GAAA,2CAAa,QAAO,gBAAA,QAAA,gBAAA,SAAA,SAAA,YAAa,IAAG;AAC1C,MAAI,WAAU,GAAA,2CAAW,eAAe,CAAC,GAAG;IAC1C,GAAG;;IAEH,WAAA;AACE,mBAAa,UAAU;AACvB,UAAI,YACF,aAAY,SAAQ;IAExB;EACF,CAAA;AAEA,GAAA,GAAA,2CAAW,aAAa,GAAA;AAExB,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,CAAC,aAAa,SAAS;AACzB,UAAI,KACF,SAAQ,KACN,2IAAA;AAIJ,mBAAa,UAAU;IACzB;EACF,GAAG,CAAA,CAAE;AAEL,UACE,GAAA,eAAAH,SAAA,eAAC,GAAA,2CAAsB,UAAQ;IAAC,OAAO;KACpC,QAAA;AAGP,CAAA;;;;ACfO,SAAS,0CAAe,OAAuB;AACpD,MAAI,EAAA,YACQ,cACE,eACC,oBACM,IACjB;AACJ,MAAI,SAAQ,GAAA,eAAAI,QAAO;IACjB,eAAe;EACjB,CAAA;AAEA,MAAI,EAAA,mBAAkB,yBAA0B,KAAI,GAAA,2CAAiB;AAErE,MAAI,UAAS,GAAA,eAAAC,aAAY,CAAC,MAAA;AAExB,QAAI,CAAC,EAAE,cAAc,SAAS,EAAE,MAAM,EACpC;AAMF,QAAI,MAAM,QAAQ,iBAAiB,CAAE,EAAE,cAA0B,SAAS,EAAE,aAAa,GAAc;AACrG,YAAM,QAAQ,gBAAgB;AAC9B,+BAAA;AAEA,UAAI,aACF,cAAa,CAAA;AAGf,UAAI,oBACF,qBAAoB,KAAA;IAExB;EACF,GAAG;IAAC;IAAc;IAAqB;IAAO;GAAyB;AAEvE,MAAI,oBAAmB,GAAA,2CAAsB,MAAA;AAC7C,MAAI,WAAU,GAAA,eAAAA,aAAY,CAAC,MAAA;AAEzB,QAAI,CAAC,EAAE,cAAc,SAAS,EAAE,MAAM,EACpC;AAKF,UAAM,iBAAgB,GAAA,2CAAiB,EAAE,MAAM;AAC/C,UAAM,iBAAgB,GAAA,2CAAiB,aAAA;AACvC,QAAI,CAAC,MAAM,QAAQ,iBAAiB,mBAAkB,GAAA,2CAAe,EAAE,WAAW,GAAG;AACnF,UAAI,cACF,eAAc,CAAA;AAGhB,UAAI,oBACF,qBAAoB,IAAA;AAGtB,YAAM,QAAQ,gBAAgB;AAC9B,uBAAiB,CAAA;AAKjB,UAAI,gBAAgB,EAAE;AACtB,wBAAkB,eAAe,SAAS,CAAAC,OAAA;AACxC,YAAI,MAAM,QAAQ,iBAAiB,EAAC,GAAA,2CAAa,eAAeA,GAAE,MAAM,GAAc;AACpF,cAAI,cAAc,IAAI,cAAc,YAAa,WAAW,QAAQ;YAAC,eAAeA,GAAE;UAAM,CAAA;AAC5F,WAAA,GAAA,2CAAe,aAAa,aAAA;AAC5B,cAAI,SAAQ,GAAA,0CAAiC,WAAA;AAC7C,iBAAO,KAAA;QACT;MACF,GAAG;QAAC,SAAS;MAAI,CAAA;IACnB;EACF,GAAG;IAAC;IAAe;IAAqB;IAAkB;IAAmB;GAAO;AAEpF,MAAI,WACF,QAAO;IACL,kBAAkB;;MAEhB,SAAS;MACT,QAAQ;IACV;EACF;AAGF,SAAO;IACL,kBAAkB;;;IAGlB;EACF;AACF;;;;;;;;;;;;;;;;AC/FO,SAAS,0CAAa,QAA4B,CAAC,GAAC;AACzD,MAAI,EAAA,YACU,OAAA,aACD,OACL,IACJ;AACJ,MAAI,SAAQ,GAAA,eAAAC,QAAO;IACjB,WAAW;IACX,gBAAgB,cAAa,GAAA,2CAAa;EAC5C,CAAA;AACA,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,CAAC,qBAAqB,eAAA,KAAmB,GAAA,eAAAA,UAAS,MAAM,MAAM,QAAQ,aAAa,MAAM,QAAQ,cAAc;AAEnH,MAAI,eAAc,GAAA,eAAAC,aAAY,MAAM,gBAAgB,MAAM,QAAQ,aAAa,MAAM,QAAQ,cAAc,GAAG,CAAA,CAAE;AAEhH,MAAI,iBAAgB,GAAA,eAAAA,aAAY,CAAAC,eAAA;AAC9B,UAAM,QAAQ,YAAYA;AAC1B,eAAWA,UAAA;AACX,gBAAA;EACF,GAAG;IAAC;GAAY;AAEhB,GAAA,GAAA,2CAAwB,CAAC,mBAAA;AACvB,UAAM,QAAQ,iBAAiB;AAC/B,gBAAA;EACF,GAAG,CAAA,GAAI;;EAAY,CAAA;AAEnB,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS;IAC1B,YAAY;;EAEd,CAAA;AAEA,MAAI,EAAA,iBAAiB,KAAI,GAAA,2CAAe;IACtC,YAAY,CAAC;IACb,qBAAqB;EACvB,CAAA;AAEA,SAAO;;IAEL,gBAAgB;IAChB,YAAY,SAAS,mBAAmB;EAC1C;AACF;;;;ACAA,IAAM,sCAAe,GAAA,eAAAC,SAAM,cAAoC,IAAA;AAoV/D,SAAS,uCAAiB,SAA0B,OAAwB;AAC1E,MAAI,CAAC,QACH,QAAO;AAET,MAAI,CAAC,MACH,QAAO;AAET,SAAO,MAAM,KAAK,CAAA,SAAQ,KAAK,SAAS,OAAA,CAAA;AAC1C;AAscA,IAAM,6BAAN,MAAM,4BAAA;EASJ,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,MAAgB;AAC1B,WAAO,KAAK,QAAQ,IAAI,IAAA;EAC1B;EAEA,YAAY,UAAoB,QAAkB,eAAkC;AAClF,QAAI,aAAa,KAAK,QAAQ,IAAI,WAAA,QAAA,WAAA,SAAA,SAAU,IAAA;AAC5C,QAAI,CAAC,WACH;AAEF,QAAI,OAAO,IAAI,+BAAS;;IAAS,CAAA;AACjC,eAAW,SAAS,IAAA;AACpB,SAAK,SAAS;AACd,SAAK,QAAQ,IAAI,UAAU,IAAA;AAC3B,QAAI,cACF,MAAK,gBAAgB;EAEzB;EAEA,QAAQ,MAAgB;AACtB,SAAK,QAAQ,IAAI,KAAK,UAAU,IAAA;EAClC;EAEA,eAAe,UAAoB;AAEjC,QAAI,aAAa,KACf;AAEF,QAAI,OAAO,KAAK,QAAQ,IAAI,QAAA;AAC5B,QAAI,CAAC,KACH;AAEF,QAAI,aAAa,KAAK;AAGtB,aAAS,WAAW,KAAK,SAAQ,EAC/B,KACE,YAAY,QACZ,KAAK,iBACL,QAAQ,iBACR,KAAK,YACL,KAAK,SAAS,WACd,uCAAiB,QAAQ,eAAe,KAAK,SAAS,OAAO,EAE7D,SAAQ,gBAAgB,KAAK;AAGjC,QAAI,WAAW,KAAK;AACpB,QAAI,YAAY;AACd,iBAAW,YAAY,IAAA;AACvB,UAAI,SAAS,OAAO,EAClB,UAAS,QAAQ,CAAA,UAAS,cAAc,WAAW,SAAS,KAAA,CAAA;IAEhE;AAEA,SAAK,QAAQ,OAAO,KAAK,QAAQ;EACnC;;EAGA,CAAC,SAAS,OAAiB,KAAK,MAA2B;AACzD,QAAI,KAAK,YAAY,KACnB,OAAM;AAER,QAAI,KAAK,SAAS,OAAO,EACvB,UAAS,SAAS,KAAK,SACrB,QAAO,KAAK,SAAS,KAAA;EAG3B;EAEA,QAAc;QAGyB;AAFrC,QAAI,UAAU,IAAI,4BAAA;QAEmB;AADrC,aAAS,QAAQ,KAAK,SAAQ,EAC5B,SAAQ,YAAY,KAAK,WAAU,yBAAA,eAAA,KAAK,YAAM,QAAX,iBAAA,SAAA,SAAA,aAAa,cAAQ,QAArB,0BAAA,SAAA,wBAAyB,MAAM,KAAK,aAAa;AAEtF,WAAO;EACT;EApFA,cAAc;SAFN,UAAU,oBAAI,IAAA;AAGpB,SAAK,OAAO,IAAI,+BAAS;MAAC,UAAU;IAAI,CAAA;AACxC,SAAK,QAAQ,IAAI,MAAM,KAAK,IAAI;EAClC;AAkFF;AAEA,IAAM,iCAAN,MAAM;EAUJ,SAAS,MAAgB;AACvB,SAAK,SAAS,IAAI,IAAA;AAClB,SAAK,SAAS;EAChB;EACA,YAAY,MAAgB;AAC1B,SAAK,SAAS,OAAO,IAAA;AACrB,SAAK,SAAS;EAChB;EAVA,YAAY,OAA6B;SAHlC,WAA0B,oBAAI,IAAA;SAC9B,UAAU;AAGf,SAAK,WAAW,MAAM;EACxB;AASF;AAEO,IAAI,4CAAiB,IAAI,2BAAA;;;;;;;;;;;;;AC19BhB,SAAA,KACd,SACA,IACA,MAMA;AACI,MAAA,OAAO,KAAK,eAAe,CAAC;AAC5B,MAAA;AAEJ,WAAS,mBAA4B;AAbvB,QAAA,IAAA,IAAA,IAAA;AAcR,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,WAAU,KAAK,IAAI;AAEnD,UAAM,UAAU,QAAQ;AAExB,UAAM,cACJ,QAAQ,WAAW,KAAK,UACxB,QAAQ,KAAK,CAAC,KAAU,UAAkB,KAAK,KAAK,MAAM,GAAG;AAE/D,QAAI,CAAC,aAAa;AACT,aAAA;IAAA;AAGF,WAAA;AAEH,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,cAAa,KAAK,IAAI;AAE7C,aAAA,GAAG,GAAG,OAAO;AAEtB,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,IAAgB;AACxB,YAAA,aAAa,KAAK,OAAO,KAAK,IAAA,IAAQ,WAAY,GAAG,IAAI;AACzD,YAAA,gBAAgB,KAAK,OAAO,KAAK,IAAA,IAAQ,cAAe,GAAG,IAAI;AACrE,YAAM,sBAAsB,gBAAgB;AAEtC,YAAA,MAAM,CAAC,KAAsB,QAAgB;AACjD,cAAM,OAAO,GAAG;AACT,eAAA,IAAI,SAAS,KAAK;AACvB,gBAAM,MAAM;QAAA;AAEP,eAAA;MACT;AAEQ,cAAA;QACN,OAAO,IAAI,eAAe,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC;QACnD;;;yBAGiB,KAAK;UAChB;UACA,KAAK,IAAI,MAAM,MAAM,qBAAqB,GAAG;QAC9C,CAAA;QACL,QAAA,OAAA,SAAA,KAAM;MACR;IAAA;AAGF,KAAA,KAAA,QAAA,OAAA,SAAA,KAAM,aAAN,OAAA,SAAA,GAAA,KAAA,MAAiB,MAAA;AAEV,WAAA;EAAA;AAIQ,mBAAA,aAAa,CAAC,YAAwB;AAC9C,WAAA;EACT;AAEO,SAAA;AACT;AAEgB,SAAA,aAAgB,OAAsB,KAAiB;AACrE,MAAI,UAAU,QAAW;AACjB,UAAA,IAAI,MAAM,uBAAuB,MAAM,KAAK,GAAG,KAAK,EAAE,EAAE;EAAA,OACzD;AACE,WAAA;EAAA;AAEX;AAEa,IAAA,cAAc,CAAC,GAAW,MAAc,KAAK,IAAI,IAAI,CAAC,IAAI;AAEhE,IAAM,WAAW,CACtB,cACA,IACA,OACG;AACC,MAAA;AACJ,SAAO,YAAwB,MAAkB;AAC/C,iBAAa,aAAa,SAAS;AACvB,gBAAA,aAAa,WAAW,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE;EACpE;AACF;;;ACnDA,IAAM,UAAU,CAAC,YAA+B;AACxC,QAAA,EAAE,aAAa,aAAA,IAAiB;AACtC,SAAO,EAAE,OAAO,aAAa,QAAQ,aAAa;AACpD;AAEa,IAAA,sBAAsB,CAAC,UAAkB;AAEzC,IAAA,wBAAwB,CAAC,UAAiB;AACrD,QAAM,QAAQ,KAAK,IAAI,MAAM,aAAa,MAAM,UAAU,CAAC;AACrD,QAAA,MAAM,KAAK,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,QAAQ,CAAC;AAErE,QAAM,MAAM,CAAC;AAEb,WAAS,IAAI,OAAO,KAAK,KAAK,KAAK;AACjC,QAAI,KAAK,CAAC;EAAA;AAGL,SAAA;AACT;AAEa,IAAA,qBAAqB,CAChC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGI,QAAA,UAAU,CAAC,SAAe;AACxB,UAAA,EAAE,OAAO,OAAA,IAAW;AACvB,OAAA,EAAE,OAAO,KAAK,MAAM,KAAK,GAAG,QAAQ,KAAK,MAAM,MAAM,EAAA,CAAG;EAC7D;AAEQ,UAAA,QAAQ,OAAiC,CAAC;AAE9C,MAAA,CAAC,aAAa,gBAAgB;AAChC,WAAO,MAAM;IAAC;EAAA;AAGhB,QAAM,WAAW,IAAI,aAAa,eAAe,CAAC,YAAY;AAC5D,UAAM,MAAM,MAAM;AACV,YAAA,QAAQ,QAAQ,CAAC;AACvB,UAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,cAAA,MAAM,MAAM,cAAc,CAAC;AACjC,YAAI,KAAK;AACP,kBAAQ,EAAE,OAAO,IAAI,YAAY,QAAQ,IAAI,UAAA,CAAW;AACxD;QAAA;MACF;AAEM,cAAA,QAAQ,OAAiC,CAAC;IACpD;AAEA,aAAS,QAAQ,sCACb,sBAAsB,GAAG,IACzB,IAAI;EAAA,CACT;AAED,WAAS,QAAQ,SAAS,EAAE,KAAK,aAAA,CAAc;AAE/C,SAAO,MAAM;AACX,aAAS,UAAU,OAAO;EAC5B;AACF;AAEA,IAAM,0BAA0B;EAC9B,SAAS;AACX;AAuBA,IAAM,oBACJ,OAAO,UAAU,cAAc,OAAO,iBAAiB;AAI5C,IAAA,uBAAuB,CAClC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGF,MAAI,SAAS;AACb,QAAM,WACJ,SAAS,QAAQ,qBAAqB,oBAClC,MAAM,SACN;IACE;IACA,MAAM;AACJ,SAAG,QAAQ,KAAK;IAClB;IACA,SAAS,QAAQ;EACnB;AAEA,QAAA,gBAAgB,CAAC,gBAAyB,MAAM;AACpD,UAAM,EAAE,YAAY,MAAM,IAAI,SAAS;AAC9B,aAAA,aACL,QAAQ,YAAY,KAAM,SAAS,MAAO,KAC1C,QAAQ,WAAW;AACd,aAAA;AACT,OAAG,QAAQ,WAAW;EACxB;AACM,QAAA,UAAU,cAAc,IAAI;AAC5B,QAAA,aAAa,cAAc,KAAK;AAC3B,aAAA;AAEH,UAAA,iBAAiB,UAAU,SAAS,uBAAuB;AAC7D,QAAA,yBACJ,SAAS,QAAQ,qBAAqB;AACxC,MAAI,wBAAwB;AAClB,YAAA,iBAAiB,aAAa,YAAY,uBAAuB;EAAA;AAE3E,SAAO,MAAM;AACH,YAAA,oBAAoB,UAAU,OAAO;AAC7C,QAAI,wBAAwB;AAClB,cAAA,oBAAoB,aAAa,UAAU;IAAA;EAEvD;AACF;AAkDO,IAAM,iBAAiB,CAC5B,SACA,OACA,aACG;AACH,MAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,UAAA,MAAM,MAAM,cAAc,CAAC;AACjC,QAAI,KAAK;AACP,YAAM,OAAO,KAAK;QAChB,IAAI,SAAS,QAAQ,aAAa,eAAe,WAAW;MAC9D;AACO,aAAA;IAAA;EACT;AAGF,SAAQ,QACN,SAAS,QAAQ,aAAa,gBAAgB,cAChD;AACF;AAkBa,IAAA,gBAAgB,CAC3B,QACA;EACE,cAAc;EACd;AACF,GACA,aACG;;AACH,QAAM,WAAW,SAAS;AAE1B,GAAA,MAAA,KAAA,SAAS,kBAAT,OAAA,SAAA,GAAwB,aAAxB,OAAA,SAAA,GAAA,KAAA,IAAmC;IACjC,CAAC,SAAS,QAAQ,aAAa,SAAS,KAAK,GAAG;IAChD;EAAA,CAAA;AAEJ;AA0DO,IAAM,cAAN,MAGL;EAyDA,YAAY,MAAwD;AAxDpE,SAAQ,SAAqC,CAAC;AAEP,SAAA,gBAAA;AACa,SAAA,eAAA;AACtC,SAAA,cAAA;AACd,SAAA,oBAAwC,CAAC;AACjC,SAAA,gBAAA,oBAAoB,IAAiB;AAC7C,SAAQ,8BAA6C,CAAC;AAC5B,SAAA,aAAA;AACI,SAAA,eAAA;AACY,SAAA,kBAAA;AAC1C,SAAQ,oBAAoB;AAQ5B,SAAA,gBAAA,oBAAoB,IAAuB;AAC3C,SAAQ,WAAkB,uBAAA;AACxB,UAAI,MAA6B;AAEjC,YAAM,MAAM,MAAM;AAChB,YAAI,KAAK;AACA,iBAAA;QAAA;AAGT,YAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,gBAAgB;AACpD,iBAAA;QAAA;AAGT,eAAQ,MAAM,IAAI,KAAK,aAAa,eAAe,CAAC,YAAY;AACtD,kBAAA,QAAQ,CAAC,UAAU;AACzB,kBAAM,MAAM,MAAM;AACX,mBAAA,gBAAgB,MAAM,QAAwB,KAAK;YAC1D;AACA,iBAAK,QAAQ,sCACT,sBAAsB,GAAG,IACzB,IAAI;UAAA,CACT;QAAA,CACF;MACH;AAEO,aAAA;QACL,YAAY,MAAM;;AAChB,WAAA,KAAA,IAAA,MAAA,OAAA,SAAA,GAAO,WAAA;AACD,gBAAA;QACR;QACA,SAAS,CAAC,WAAA;;AACR,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,QAAQ,QAAQ,EAAE,KAAK,aAAA,CAAA;;QAChC,WAAW,CAAC,WAAA;;AAAoB,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,UAAU,MAAA;QAAA;MACnD;IAAA,GACC;AACsD,SAAA,QAAA;AAMzD,SAAA,aAAa,CAACC,UAA2D;AAChE,aAAA,QAAQA,KAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAI,OAAO,UAAU,YAAa,QAAQA,MAAa,GAAG;MAAA,CAC3D;AAED,WAAK,UAAU;QACb,OAAO;QACP,eAAe;QACf,UAAU;QACV,cAAc;QACd,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU,MAAM;QAAC;QACjB;QACA,aAAa,EAAE,OAAO,GAAG,QAAQ,EAAE;QACnC,cAAc;QACd,KAAK;QACL,gBAAgB;QAChB,0BAA0B,CAAC;QAC3B,OAAO;QACP,uBAAuB;QACvB,SAAS;QACT,OAAO;QACP,mBAAmB;QACnB,qCAAqC;QACrC,GAAGA;MACL;IACF;AAEQ,SAAA,SAAS,CAAC,SAAkB;;AAC7B,OAAA,MAAA,KAAA,KAAA,SAAQ,aAAR,OAAA,SAAA,GAAA,KAAA,IAAmB,MAAM,IAAA;IAChC;AAEA,SAAQ,cAAc;MACpB,MAAM;AACJ,aAAK,eAAe;AAEb,eAAA;UACL,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QACrC;MACF;MACA,CAAC,gBAAgB;AACf,aAAK,OAAO,WAAW;MACzB;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;QAC1B,aAAa;UACX,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QAAA;MACrC;IAEJ;AAEA,SAAQ,UAAU,MAAM;AACjB,WAAA,OAAO,OAAO,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAA,CAAI;AAC/C,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,WAAW;AACzB,WAAK,gBAAgB;AACrB,WAAK,eAAe;IACtB;AAEA,SAAA,YAAY,MAAM;AAChB,aAAO,MAAM;AACX,aAAK,QAAQ;MACf;IACF;AAEA,SAAA,cAAc,MAAM;;AAClB,YAAM,gBAAgB,KAAK,QAAQ,UAC/B,KAAK,QAAQ,iBAAA,IACb;AAEA,UAAA,KAAK,kBAAkB,eAAe;AACxC,aAAK,QAAQ;AAEb,YAAI,CAAC,eAAe;AAClB,eAAK,YAAY;AACjB;QAAA;AAGF,aAAK,gBAAgB;AAErB,YAAI,KAAK,iBAAiB,mBAAmB,KAAK,eAAe;AAC1D,eAAA,eAAe,KAAK,cAAc,cAAc;QAAA,OAChD;AACA,eAAA,iBAAe,KAAA,KAAK,kBAAL,OAAA,SAAA,GAAoB,WAAU;QAAA;AAG/C,aAAA,cAAc,QAAQ,CAAC,WAAW;AAChC,eAAA,SAAS,QAAQ,MAAM;QAAA,CAC7B;AAEI,aAAA,gBAAgB,KAAK,gBAAA,GAAmB;UAC3C,aAAa;UACb,UAAU;QAAA,CACX;AAED,aAAK,OAAO;UACV,KAAK,QAAQ,mBAAmB,MAAM,CAAC,SAAS;AAC9C,iBAAK,aAAa;AAClB,iBAAK,YAAY;UAClB,CAAA;QACH;AAEA,aAAK,OAAO;UACV,KAAK,QAAQ,qBAAqB,MAAM,CAAC,QAAQ,gBAAgB;AAC/D,iBAAK,oBAAoB;AACzB,iBAAK,kBAAkB,cACnB,KAAK,gBAAA,IAAoB,SACvB,YACA,aACF;AACJ,iBAAK,eAAe;AACpB,iBAAK,cAAc;AAEnB,iBAAK,YAAY;UAClB,CAAA;QACH;MAAA;IAEJ;AAEA,SAAQ,UAAU,MAAM;AAClB,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,aAAa;AACX,eAAA;MAAA;AAGT,WAAK,aAAa,KAAK,cAAc,KAAK,QAAQ;AAElD,aAAO,KAAK,WAAW,KAAK,QAAQ,aAAa,UAAU,QAAQ;IACrE;AAEA,SAAQ,kBAAkB,MAAM;AAC1B,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,eAAe;AACb,eAAA;MAAA;AAGT,WAAK,eACH,KAAK,iBACJ,OAAO,KAAK,QAAQ,kBAAkB,aACnC,KAAK,QAAQ,cAAc,IAC3B,KAAK,QAAQ;AAEnB,aAAO,KAAK;IACd;AAEQ,SAAA,yBAAyB,CAC/B,cACA,UACG;AACG,YAAA,4BAAA,oBAAgC,IAAkB;AAClD,YAAA,uBAAA,oBAA2B,IAAyB;AAC1D,eAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK;AAC7B,cAAA,cAAc,aAAa,CAAC;AAElC,YAAI,0BAA0B,IAAI,YAAY,IAAI,GAAG;AACnD;QAAA;AAGF,cAAM,8BAA8B,qBAAqB;UACvD,YAAY;QACd;AACA,YACE,+BAA+B,QAC/B,YAAY,MAAM,4BAA4B,KAC9C;AACqB,+BAAA,IAAI,YAAY,MAAM,WAAW;QAC7C,WAAA,YAAY,MAAM,4BAA4B,KAAK;AAClC,oCAAA,IAAI,YAAY,MAAM,IAAI;QAAA;AAGtD,YAAI,0BAA0B,SAAS,KAAK,QAAQ,OAAO;AACzD;QAAA;MACF;AAGF,aAAO,qBAAqB,SAAS,KAAK,QAAQ,QAC9C,MAAM,KAAK,qBAAqB,OAAA,CAAQ,EAAE,KAAK,CAAC,GAAG,MAAM;AACnD,YAAA,EAAE,QAAQ,EAAE,KAAK;AACZ,iBAAA,EAAE,QAAQ,EAAE;QAAA;AAGd,eAAA,EAAE,MAAM,EAAE;MAAA,CAClB,EAAE,CAAC,IACJ;IACN;AAEA,SAAQ,wBAAwB;MAC9B,MAAM;QACJ,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;MACf;MACA,CAAC,OAAO,cAAc,cAAc,YAAY,YAAY;AAC1D,aAAK,8BAA8B,CAAC;AAC7B,eAAA;UACL;UACA;UACA;UACA;UACA;QACF;MACF;MACA;QACE,KAAK;MAAA;IAET;AAEA,SAAQ,kBAAkB;MACxB,MAAM,CAAC,KAAK,sBAAA,GAAyB,KAAK,aAAa;MACvD,CACE,EAAE,OAAO,cAAc,cAAc,YAAY,QAAA,GACjD,kBACG;AACH,YAAI,CAAC,SAAS;AACZ,eAAK,oBAAoB,CAAC;AAC1B,eAAK,cAAc,MAAM;AACzB,iBAAO,CAAC;QAAA;AAGN,YAAA,KAAK,kBAAkB,WAAW,GAAG;AAClC,eAAA,oBAAoB,KAAK,QAAQ;AACjC,eAAA,kBAAkB,QAAQ,CAAC,SAAS;AACvC,iBAAK,cAAc,IAAI,KAAK,KAAK,KAAK,IAAI;UAAA,CAC3C;QAAA;AAGG,cAAA,MACJ,KAAK,4BAA4B,SAAS,IACtC,KAAK,IAAI,GAAG,KAAK,2BAA2B,IAC5C;AACN,aAAK,8BAA8B,CAAC;AAEpC,cAAM,eAAe,KAAK,kBAAkB,MAAM,GAAG,GAAG;AAExD,iBAAS,IAAI,KAAK,IAAI,OAAO,KAAK;AAC1B,gBAAA,MAAM,WAAW,CAAC;AAExB,gBAAM,sBACJ,KAAK,QAAQ,UAAU,IACnB,aAAa,IAAI,CAAC,IAClB,KAAK,uBAAuB,cAAc,CAAC;AAEjD,gBAAM,QAAQ,sBACV,oBAAoB,MAAM,KAAK,QAAQ,MACvC,eAAe;AAEb,gBAAA,eAAe,cAAc,IAAI,GAAG;AACpC,gBAAA,OACJ,OAAO,iBAAiB,WACpB,eACA,KAAK,QAAQ,aAAa,CAAC;AAEjC,gBAAM,MAAM,QAAQ;AAEpB,gBAAM,OAAO,sBACT,oBAAoB,OACpB,IAAI,KAAK,QAAQ;AAErB,uBAAa,CAAC,IAAI;YAChB,OAAO;YACP;YACA;YACA;YACA;YACA;UACF;QAAA;AAGF,aAAK,oBAAoB;AAElB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEiB,SAAA,iBAAA;MACf,MAAM;QACJ,KAAK,gBAAgB;QACrB,KAAK,QAAQ;QACb,KAAK,gBAAgB;QACrB,KAAK,QAAQ;MACf;MACA,CAAC,cAAc,WAAW,cAAc,UAAU;AAChD,eAAQ,KAAK,QACX,aAAa,SAAS,KAAK,YAAY,IACnC,eAAe;UACb;UACA;UACA;UACA;QACD,CAAA,IACD;MACR;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEoB,SAAA,oBAAA;MAClB,MAAM;AACJ,YAAI,aAA4B;AAChC,YAAI,WAA0B;AACxB,cAAA,QAAQ,KAAK,eAAe;AAClC,YAAI,OAAO;AACT,uBAAa,MAAM;AACnB,qBAAW,MAAM;QAAA;AAEnB,aAAK,YAAY,WAAW,CAAC,KAAK,aAAa,YAAY,QAAQ,CAAC;AAC7D,eAAA;UACL,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb;UACA;QACF;MACF;MACA,CAAC,gBAAgB,UAAU,OAAO,YAAY,aAAa;AACzD,eAAO,eAAe,QAAQ,aAAa,OACvC,CAAA,IACA,eAAe;UACb;UACA;UACA;UACA;QAAA,CACD;MACP;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,mBAAmB,CAAC,SAAuB;AACnC,YAAA,gBAAgB,KAAK,QAAQ;AAC7B,YAAA,WAAW,KAAK,aAAa,aAAa;AAEhD,UAAI,CAAC,UAAU;AACL,gBAAA;UACN,2BAA2B,aAAa;QAC1C;AACO,eAAA;MAAA;AAGF,aAAA,SAAS,UAAU,EAAE;IAC9B;AAEQ,SAAA,kBAAkB,CACxB,MACA,UACG;AACG,YAAA,QAAQ,KAAK,iBAAiB,IAAI;AAClC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,KAAK,cAAc,IAAI,GAAG;AAE3C,UAAI,aAAa,MAAM;AACrB,YAAI,UAAU;AACP,eAAA,SAAS,UAAU,QAAQ;QAAA;AAE7B,aAAA,SAAS,QAAQ,IAAI;AACrB,aAAA,cAAc,IAAI,KAAK,IAAI;MAAA;AAGlC,UAAI,KAAK,aAAa;AACf,aAAA,WAAW,OAAO,KAAK,QAAQ,eAAe,MAAM,OAAO,IAAI,CAAC;MAAA;IAEzE;AAEa,SAAA,aAAA,CAAC,OAAe,SAAiB;AACtC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,WAAW,KAAK,cAAc,IAAI,KAAK,GAAG,KAAK,KAAK;AAC1D,YAAM,QAAQ,OAAO;AAErB,UAAI,UAAU,GAAG;AACf,YACE,KAAK,+CAA+C,SAChD,KAAK,2CAA2C,MAAM,OAAO,IAAI,IACjE,KAAK,QAAQ,KAAK,gBAAgB,IAAI,KAAK,mBAC/C;AACA,cAA6C,KAAK,QAAQ,OAAO;AACvD,oBAAA,KAAK,cAAc,KAAK;UAAA;AAG7B,eAAA,gBAAgB,KAAK,gBAAA,GAAmB;YAC3C,aAAc,KAAK,qBAAqB;YACxC,UAAU;UAAA,CACX;QAAA;AAGE,aAAA,4BAA4B,KAAK,KAAK,KAAK;AAC3C,aAAA,gBAAgB,IAAI,IAAI,KAAK,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AAEnE,aAAK,OAAO,KAAK;MAAA;IAErB;AAEA,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,UAAI,CAAC,MAAM;AACT,aAAK,cAAc,QAAQ,CAAC,QAAQ,QAAQ;AACtC,cAAA,CAAC,OAAO,aAAa;AAClB,iBAAA,SAAS,UAAU,MAAM;AACzB,iBAAA,cAAc,OAAO,GAAG;UAAA;QAC/B,CACD;AACD;MAAA;AAGG,WAAA,gBAAgB,MAAM,MAAS;IACtC;AAEkB,SAAA,kBAAA;MAChB,MAAM,CAAC,KAAK,kBAAqB,GAAA,KAAK,gBAAA,CAAiB;MACvD,CAAC,SAAS,iBAAiB;AACzB,cAAM,eAAmC,CAAC;AAE1C,iBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAC5C,gBAAA,IAAI,QAAQ,CAAC;AACb,gBAAA,cAAc,aAAa,CAAC;AAElC,uBAAa,KAAK,WAAW;QAAA;AAGxB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,0BAA0B,CAAC,WAAmB;AACtC,YAAA,eAAe,KAAK,gBAAgB;AACtC,UAAA,aAAa,WAAW,GAAG;AACtB,eAAA;MAAA;AAEF,aAAA;QACL,aACE;UACE;UACA,aAAa,SAAS;UACtB,CAAC,UAAkB,aAAa,aAAa,KAAK,CAAC,EAAE;UACrD;QAEJ,CAAA;MACF;IACF;AAEA,SAAA,wBAAwB,CACtB,UACA,OACA,WAAW,MACR;AACG,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACZ,gBAAA,YAAY,eAAe,OAAO,QAAQ;MAAA;AAGpD,UAAI,UAAU,UAAU;AAGtB,qBAAa,WAAW,QAAQ;MAAA,WACvB,UAAU,OAAO;AACd,oBAAA;MAAA;AAGd,YAAM,YAAY,KAAK,aAAA,IAAiB,KAAK,QAAQ,eAAe;AAEpE,aAAO,KAAK,IAAI,KAAK,IAAI,WAAW,QAAQ,GAAG,CAAC;IAClD;AAEoB,SAAA,oBAAA,CAAC,OAAe,QAAyB,WAAW;AAC9D,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAErD,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACF,eAAA;MAAA;AAGH,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACpB,YAAI,KAAK,OAAO,eAAe,OAAO,KAAK,QAAQ,kBAAkB;AAC3D,kBAAA;QAAA,WACC,KAAK,SAAS,eAAe,KAAK,QAAQ,oBAAoB;AAC/D,kBAAA;QAAA,OACH;AACE,iBAAA,CAAC,cAAc,KAAK;QAAA;MAC7B;AAGI,YAAA,WACJ,UAAU,QACN,KAAK,MAAM,KAAK,QAAQ,mBACxB,KAAK,QAAQ,KAAK,QAAQ;AAEzB,aAAA;QACL,KAAK,sBAAsB,UAAU,OAAO,KAAK,IAAI;QACrD;MACF;IACF;AAEA,SAAQ,gBAAgB,MAAM,KAAK,cAAc,OAAO;AAEvC,SAAA,iBAAA,CACf,UACA,EAAE,QAAQ,SAAS,SAAS,IAA2B,CAAA,MACpD;AACH,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,sBAAsB,UAAU,KAAK,GAAG;QAChE,aAAa;QACb;MAAA,CACD;IACH;AAEgB,SAAA,gBAAA,CACd,OACA,EAAE,OAAO,eAAe,QAAQ,SAAmC,IAAA,CAAA,MAChE;AACH,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGM,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAE3D,UAAI,WAAW;AACf,YAAM,cAAc;AAEd,YAAA,YAAY,CAAC,iBAAkC;AAC/C,YAAA,CAAC,KAAK,aAAc;AAExB,cAAM,aAAa,KAAK,kBAAkB,OAAO,YAAY;AAC7D,YAAI,CAAC,YAAY;AACP,kBAAA,KAAK,mCAAmC,KAAK;AACrD;QAAA;AAEI,cAAA,CAAC,QAAQ,KAAK,IAAI;AACxB,aAAK,gBAAgB,QAAQ,EAAE,aAAa,QAAW,SAAA,CAAU;AAE5D,aAAA,aAAa,sBAAsB,MAAM;AACtC,gBAAA,gBAAgB,KAAK,gBAAgB;AAC3C,gBAAM,YAAY,KAAK,kBAAkB,OAAO,KAAK;AACrD,cAAI,CAAC,WAAW;AACN,oBAAA,KAAK,mCAAmC,KAAK;AACrD;UAAA;AAGF,cAAI,CAAC,YAAY,UAAU,CAAC,GAAG,aAAa,GAAG;AAC7C,0BAAc,KAAK;UAAA;QACrB,CACD;MACH;AAEM,YAAA,gBAAgB,CAAC,UAA2B;AAC5C,YAAA,CAAC,KAAK,aAAc;AAExB;AACA,YAAI,WAAW,aAAa;AAC1B,cAA6C,KAAK,QAAQ,OAAO;AACvD,oBAAA,KAAK,kBAAkB,UAAU,WAAW;UAAA;AAEtD,eAAK,aAAa,sBAAsB,MAAM,UAAU,KAAK,CAAC;QAAA,OACzD;AACG,kBAAA;YACN,6BAA6B,KAAK,UAAU,WAAW;UACzD;QAAA;MAEJ;AAEA,gBAAU,YAAY;IACxB;AAEA,SAAA,WAAW,CAAC,OAAe,EAAE,SAAS,IAA2B,CAAA,MAAO;AACtE,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,gBAAgB,IAAI,OAAO;QACnD,aAAa;QACb;MAAA,CACD;IACH;AAEA,SAAA,eAAe,MAAM;;AACb,YAAA,eAAe,KAAK,gBAAgB;AAEtC,UAAA;AAIA,UAAA,aAAa,WAAW,GAAG;AAC7B,cAAM,KAAK,QAAQ;MACV,WAAA,KAAK,QAAQ,UAAU,GAAG;AACnC,gBAAM,KAAA,aAAa,aAAa,SAAS,CAAC,MAApC,OAAA,SAAA,GAAuC,QAAO;MAAA,OAC/C;AACL,cAAM,YAAY,MAAqB,KAAK,QAAQ,KAAK,EAAE,KAAK,IAAI;AAChE,YAAA,WAAW,aAAa,SAAS;AAC9B,eAAA,YAAY,KAAK,UAAU,KAAK,CAAC,QAAQ,QAAQ,IAAI,GAAG;AACvD,gBAAA,OAAO,aAAa,QAAQ;AAClC,cAAI,UAAU,KAAK,IAAI,MAAM,MAAM;AACvB,sBAAA,KAAK,IAAI,IAAI,KAAK;UAAA;AAG9B;QAAA;AAGI,cAAA,KAAK,IAAI,GAAG,UAAU,OAAO,CAAC,QAAuB,QAAQ,IAAI,CAAC;MAAA;AAG1E,aAAO,KAAK;QACV,MAAM,KAAK,QAAQ,eAAe,KAAK,QAAQ;QAC/C;MACF;IACF;AAEQ,SAAA,kBAAkB,CACxB,QACA;MACE;MACA;IAAA,MAKC;AACH,WAAK,QAAQ,WAAW,QAAQ,EAAE,UAAU,YAAA,GAAe,IAAI;IACjE;AAEA,SAAA,UAAU,MAAM;AACT,WAAA,gBAAA,oBAAoB,IAAI;AAC7B,WAAK,OAAO,KAAK;IACnB;AA3pBE,SAAK,WAAW,IAAI;EAAA;AA4pBxB;AAEA,IAAM,0BAA0B,CAC9B,KACA,MACA,iBACA,UACG;AACH,SAAO,OAAO,MAAM;AACZ,UAAA,UAAW,MAAM,QAAQ,IAAK;AAC9B,UAAA,eAAe,gBAAgB,MAAM;AAE3C,QAAI,eAAe,OAAO;AACxB,YAAM,SAAS;IAAA,WACN,eAAe,OAAO;AAC/B,aAAO,SAAS;IAAA,OACX;AACE,aAAA;IAAA;EACT;AAGF,MAAI,MAAM,GAAG;AACX,WAAO,MAAM;EAAA,OACR;AACE,WAAA;EAAA;AAEX;AAEA,SAAS,eAAe;EACtB;EACA;EACA;EACA;AACF,GAKG;AACK,QAAA,YAAY,aAAa,SAAS;AACxC,QAAM,YAAY,CAAC,UAAkB,aAAa,KAAK,EAAG;AAGtD,MAAA,aAAa,UAAU,OAAO;AACzB,WAAA;MACL,YAAY;MACZ,UAAU;IACZ;EAAA;AAGF,MAAI,aAAa;IACf;IACA;IACA;IACA;EACF;AACA,MAAI,WAAW;AAEf,MAAI,UAAU,GAAG;AACf,WACE,WAAW,aACX,aAAa,QAAQ,EAAG,MAAM,eAAe,WAC7C;AACA;IAAA;EACF,WACS,QAAQ,GAAG;AAGpB,UAAM,aAAa,MAAM,KAAK,EAAE,KAAK,CAAC;AAEpC,WAAA,WAAW,aACX,WAAW,KAAK,CAAC,QAAQ,MAAM,eAAe,SAAS,GACvD;AACM,YAAA,OAAO,aAAa,QAAQ;AACvB,iBAAA,KAAK,IAAI,IAAI,KAAK;AAC7B;IAAA;AAKF,UAAM,eAAe,MAAM,KAAK,EAAE,KAAK,eAAe,SAAS;AACxD,WAAA,cAAc,KAAK,aAAa,KAAK,CAAC,QAAQ,OAAO,YAAY,GAAG;AACnE,YAAA,OAAO,aAAa,UAAU;AACvB,mBAAA,KAAK,IAAI,IAAI,KAAK;AAC/B;IAAA;AAIF,iBAAa,KAAK,IAAI,GAAG,aAAc,aAAa,KAAM;AAE1D,eAAW,KAAK,IAAI,WAAW,YAAY,QAAQ,IAAK,WAAW,MAAO;EAAA;AAGrE,SAAA,EAAE,YAAY,SAAS;AAChC;;;ACxoCA,IAAM,4BACJ,OAAO,aAAa,cAAoB,wBAAwB;AAElE,SAAS,mBAIP,SAC2C;AACrC,QAAA,WAAiB,iBAAW,OAAO,CAAA,IAAK,CAAA,CAAE,EAAE,CAAC;AAEnD,QAAM,kBAAoE;IACxE,GAAG;IACH,UAAU,CAACC,WAAU,SAAS;;AAC5B,UAAI,MAAM;AACR,yCAAU,QAAQ;MAAA,OACb;AACI,iBAAA;MAAA;AAEH,OAAA,KAAA,QAAA,aAAA,OAAA,SAAA,GAAA,KAAA,SAAWA,WAAU,IAAA;IAAI;EAErC;AAEM,QAAA,CAAC,QAAQ,IAAU;IACvB,MAAM,IAAI,YAA0C,eAAe;EACrE;AAEA,WAAS,WAAW,eAAe;AAEnC,4BAA0B,MAAM;AAC9B,WAAO,SAAS,UAAU;EAC5B,GAAG,CAAA,CAAE;AAEL,4BAA0B,MAAM;AAC9B,WAAO,SAAS,YAAY;EAAA,CAC7B;AAEM,SAAA;AACT;AAEO,SAAS,eAId,SAI2C;AAC3C,SAAO,mBAAiD;IACtD;IACA;IACA,YAAY;IACZ,GAAG;EAAA,CACJ;AACH;;;ACjEA,IAAMC,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAioBzC,IAAMC,6BAA6CC,mBAChDC,OAAO,QAAQ,EACfC,KAAK,GAAG;", "names": ["$HgANd$react", "$lmaYr$useRef", "$lmaYr$useCallback", "$670gB$react", "$670gB$useContext", "$670gB$useRef", "$670gB$react", "$670gB$useState", "$g3jFn$createContext", "$lPAwt$useRef", "$lPAwt$useCallback", "$lPAwt$useEffect", "$gbmns$useRef", "$gbmns$useCallback", "$gbmns$useMemo", "$AWxnT$useState", "$AWxnT$useRef", "$AWxnT$useEffect", "$AWxnT$useMemo", "triggerHoverEnd", "hoverProps", "$6dfIe$useRef", "$6dfIe$useCallback", "e", "window", "$3aeG1$react", "$7mdmh$useContext", "$7mdmh$useState", "$7mdmh$useRef", "$7mdmh$useMemo", "pressProps", "e", "$7mdmh$flushSync", "$7mdmh$useEffect", "$28AnR$useEffect", "$hf0lj$useCallback", "$fcPuG$react", "$fcPuG$useContext", "$fcPuG$useRef", "$fcPuG$useEffect", "$fcPuG$forwardRef", "$hhDyF$react", "$hhDyF$useEffect", "$87RPk$react", "$87RPk$useRef", "$87RPk$useContext", "$87RPk$useEffect", "$3b9Q0$useRef", "$3b9Q0$useCallback", "e", "$isWE5$useRef", "$isWE5$useState", "$isWE5$useCallback", "isFocused", "$cgawC$react", "opts", "instance", "candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "focusableCandidateSelector", "candidateSelectors", "concat", "join"]}
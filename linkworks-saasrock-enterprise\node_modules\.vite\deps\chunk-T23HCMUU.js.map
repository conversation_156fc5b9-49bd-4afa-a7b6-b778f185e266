{"version": 3, "sources": ["../../swr/dist/index/index.mjs", "../../swr/dist/_internal/config-context-client-BoS53ST9.mjs", "../../swr/dist/_internal/events.mjs", "../../dequal/lite/index.mjs", "../../swr/dist/_internal/constants.mjs", "../../swr/dist/_internal/index.mjs"], "sourcesContent": ["import React, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { OBJECT as OBJECT$1, SWRConfig as SWRConfig$1, defaultConfig, with<PERSON>rgs, SWRGlobalState, serialize as serialize$1, createCacheHel<PERSON>, isUndefined as isUndefined$1, UNDEFINED as UNDEFINED$1, isPromiseLike, getTimestamp, isFunction as isFunction$1, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from '../_internal/index.mjs';\nexport { mutate, preload, useSWRConfig } from '../_internal/index.mjs';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = React.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize$1(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = isUndefined$1(fallbackData) ? isUndefined$1(config.fallback) ? UNDEFINED$1 : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined$1(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined$1(cachedData) ? fallback && isPromiseLike(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined$1(cachedData) ? isUndefined$1(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined$1(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined$1(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined$1(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined$1(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined$1(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined$1(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined$1(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED$1;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined$1(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction$1(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined$1(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED$1, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            // Performance optimization: if a request is already in progress for this key,\n            // skip the revalidation to avoid redundant work\n            if (!FETCH[key]) {\n                if (isUndefined$1(data) || IS_SERVER) {\n                    // Revalidate immediately.\n                    softRevalidate();\n                } else {\n                    // Delay the revalidate if we have data to return so we won't block\n                    // rendering.\n                    rAF(softRevalidate);\n                }\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction$1(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined$1(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined$1(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined$1(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined$1(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = OBJECT$1.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n", "'use client';\nimport React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement } from 'react';\nimport * as revalidateEvents from './events.mjs';\nimport { dequal } from 'dequal/lite';\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](revalidateEvents.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        let isError = false;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n                isError = true;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n                isError = true;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (isError) throw error;\n                return data;\n            } else if (isError && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!isError) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (isError) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\nexport { noop as A, isPromiseLike as B, IS_REACT_LEGACY as I, OBJECT as O, SWRConfigContext as S, UNDEFINED as U, isFunction as a, SWRGlobalState as b, cache as c, defaultConfig as d, isUndefined as e, mergeConfigs as f, SWRConfig as g, initCache as h, isWindowDefined as i, mutate as j, compare as k, stableHash as l, mergeObjects as m, internalMutate as n, getTimestamp as o, preset as p, defaultConfigOptions as q, IS_SERVER as r, serialize as s, rAF as t, useIsomorphicLayoutEffect as u, slowConnection as v, isDocumentDefined as w, isLegacyDeno as x, hasRequestAnimationFrame as y, createCacheHelper as z };\n", "const FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nexport { ERROR_REVALIDATE_EVENT, FOCUS_EVENT, MUTATE_EVENT, RECONNECT_EVENT };\n", "var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "const INFINITE_PREFIX = '$inf$';\n\nexport { INFINITE_PREFIX };\n", "import { i as isWindowDefined, a as isFunction, m as mergeObjects, S as SWRConfigContext, d as defaultConfig, s as serialize, b as SWRGlobalState, c as cache, e as isUndefined, f as mergeConfigs } from './config-context-client-BoS53ST9.mjs';\nexport { I as IS_REACT_LEGACY, r as IS_SERVER, O as OBJECT, g as SWRConfig, U as UNDEFINED, k as compare, z as createCacheHelper, q as defaultConfigOptions, o as getTimestamp, y as hasRequestAnimationFrame, h as initCache, n as internalMutate, w as isDocumentDefined, x as isLegacyDeno, B as isPromiseLike, j as mutate, A as noop, p as preset, t as rAF, v as slowConnection, l as stableHash, u as useIsomorphicLayoutEffect } from './config-context-client-BoS53ST9.mjs';\nimport * as revalidateEvents from './events.mjs';\nexport { revalidateEvents };\nimport { INFINITE_PREFIX } from './constants.mjs';\nexport { INFINITE_PREFIX } from './constants.mjs';\nimport React, { useContext } from 'react';\nexport * from './types.mjs';\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            if (key.startsWith(INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { SWRGlobalState, cache, defaultConfig, isFunction, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, normalize, preload, serialize, subscribeCallback, useSWRConfig, withArgs, withMiddleware };\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,gBAAmE;AACnE,kBAAqC;;;ACArC,mBAA6G;;;ACD7G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAM,cAAc;AACpB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,yBAAyB;;;ACH/B,IAAI,MAAM,OAAO,UAAU;AAEpB,SAAS,OAAO,KAAK,KAAK;AAChC,MAAI,MAAM;AACV,MAAI,QAAQ,IAAK,QAAO;AAExB,MAAI,OAAO,QAAQ,OAAK,IAAI,iBAAiB,IAAI,aAAa;AAC7D,QAAI,SAAS,KAAM,QAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ;AACxD,QAAI,SAAS,OAAQ,QAAO,IAAI,SAAS,MAAM,IAAI,SAAS;AAE5D,QAAI,SAAS,OAAO;AACnB,WAAK,MAAI,IAAI,YAAY,IAAI,QAAQ;AACpC,eAAO,SAAS,OAAO,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,MAC5C;AACA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;AACrC,YAAM;AACN,WAAK,QAAQ,KAAK;AACjB,YAAI,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,KAAK,KAAK,IAAI,EAAG,QAAO;AACjE,YAAI,EAAE,QAAQ,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,EAAG,QAAO;AAAA,MAC7D;AACA,aAAO,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,IACpC;AAAA,EACD;AAEA,SAAO,QAAQ,OAAO,QAAQ;AAC/B;;;AFtBA,IAAM,iBAAiB,oBAAI,QAAQ;AAGnC,IAAM,OAAO,MAAI;AAAC;AAKlB,IAAM;AAAA;AAAA,EAA8B,KAAK;AAAA;AACzC,IAAM,SAAS;AACf,IAAM,cAAc,CAAC,MAAI,MAAM;AAC/B,IAAM,aAAa,CAAC,MAAI,OAAO,KAAK;AACpC,IAAM,eAAe,CAAC,GAAG,OAAK;AAAA,EACtB,GAAG;AAAA,EACH,GAAG;AACP;AACJ,IAAM,gBAAgB,CAAC,MAAI,WAAW,EAAE,IAAI;AAE5C,IAAM,cAAc,CAAC;AACrB,IAAM,gBAAgB,CAAC;AACvB,IAAM,gBAAgB;AAEtB,IAAM,kBAAkB,OAAO,UAAU;AACzC,IAAM,oBAAoB,OAAO,YAAY;AAC7C,IAAM,eAAe,mBAAmB,UAAU;AAClD,IAAM,2BAA2B,MAAI,mBAAmB,OAAO,OAAO,uBAAuB,KAAK;AAClG,IAAM,oBAAoB,CAACC,QAAO,QAAM;AACpC,QAAM,QAAQ,eAAe,IAAIA,MAAK;AACtC,SAAO;AAAA;AAAA,IAEH,MAAI,CAAC,YAAY,GAAG,KAAKA,OAAM,IAAI,GAAG,KAAK;AAAA;AAAA,IAE3C,CAAC,SAAO;AACJ,UAAI,CAAC,YAAY,GAAG,GAAG;AACnB,cAAM,OAAOA,OAAM,IAAI,GAAG;AAG1B,YAAI,EAAE,OAAO,gBAAgB;AACzB,wBAAc,GAAG,IAAI;AAAA,QACzB;AACA,cAAM,CAAC,EAAE,KAAK,aAAa,MAAM,IAAI,GAAG,QAAQ,WAAW;AAAA,MAC/D;AAAA,IACJ;AAAA;AAAA,IAEA,MAAM,CAAC;AAAA;AAAA,IAEP,MAAI;AACA,UAAI,CAAC,YAAY,GAAG,GAAG;AAEnB,YAAI,OAAO,cAAe,QAAO,cAAc,GAAG;AAAA,MACtD;AAEA,aAAO,CAAC,YAAY,GAAG,KAAKA,OAAM,IAAI,GAAG,KAAK;AAAA,IAClD;AAAA,EACJ;AACJ;AASI,IAAI,SAAS;AACjB,IAAM,WAAW,MAAI;AAErB,IAAM,CAAC,eAAe,cAAc,IAAI,mBAAmB,OAAO,mBAAmB;AAAA,EACjF,OAAO,iBAAiB,KAAK,MAAM;AAAA,EACnC,OAAO,oBAAoB,KAAK,MAAM;AAC1C,IAAI;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,YAAY,MAAI;AAClB,QAAM,kBAAkB,qBAAqB,SAAS;AACtD,SAAO,YAAY,eAAe,KAAK,oBAAoB;AAC/D;AACA,IAAM,YAAY,CAAC,aAAW;AAE1B,MAAI,mBAAmB;AACnB,aAAS,iBAAiB,oBAAoB,QAAQ;AAAA,EAC1D;AACA,gBAAc,SAAS,QAAQ;AAC/B,SAAO,MAAI;AACP,QAAI,mBAAmB;AACnB,eAAS,oBAAoB,oBAAoB,QAAQ;AAAA,IAC7D;AACA,mBAAe,SAAS,QAAQ;AAAA,EACpC;AACJ;AACA,IAAM,gBAAgB,CAAC,aAAW;AAE9B,QAAM,WAAW,MAAI;AACjB,aAAS;AACT,aAAS;AAAA,EACb;AAEA,QAAM,YAAY,MAAI;AAClB,aAAS;AAAA,EACb;AACA,gBAAc,UAAU,QAAQ;AAChC,gBAAc,WAAW,SAAS;AAClC,SAAO,MAAI;AACP,mBAAe,UAAU,QAAQ;AACjC,mBAAe,WAAW,SAAS;AAAA,EACvC;AACJ;AACA,IAAM,SAAS;AAAA,EACX;AAAA,EACA;AACJ;AACA,IAAM,uBAAuB;AAAA,EACzB;AAAA,EACA;AACJ;AAEA,IAAM,kBAAkB,CAAC,aAAAC,QAAM;AAC/B,IAAM,YAAY,CAAC,mBAAmB;AAEtC,IAAM,MAAM,CAAC,MAAI,yBAAyB,IAAI,OAAO,uBAAuB,EAAE,CAAC,IAAI,WAAW,GAAG,CAAC;AAIlG,IAAM,4BAA4B,YAAY,yBAAY;AAE1D,IAAM,sBAAsB,OAAO,cAAc,eAAe,UAAU;AAE1E,IAAM,iBAAiB,CAAC,aAAa,wBAAwB;AAAA,EACzD;AAAA,EACA;AACJ,EAAE,SAAS,oBAAoB,aAAa,KAAK,oBAAoB;AAMrE,IAAM,QAAQ,oBAAI,QAAQ;AAC1B,IAAM,cAAc,CAAC,UAAQ,OAAO,UAAU,SAAS,KAAK,KAAK;AACjE,IAAM,mBAAmB,CAAC,UAAU,SAAO,aAAa,WAAW,IAAI;AAEvE,IAAI,UAAU;AASd,IAAM,aAAa,CAAC,QAAM;AACtB,QAAM,OAAO,OAAO;AACpB,QAAM,WAAW,YAAY,GAAG;AAChC,QAAM,SAAS,iBAAiB,UAAU,MAAM;AAChD,QAAM,UAAU,iBAAiB,UAAU,QAAQ;AACnD,QAAM,gBAAgB,iBAAiB,UAAU,QAAQ;AACzD,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,SAAS;AAG5C,aAAS,MAAM,IAAI,GAAG;AACtB,QAAI,OAAQ,QAAO;AAInB,aAAS,EAAE,UAAU;AACrB,UAAM,IAAI,KAAK,MAAM;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,eAAS;AACT,WAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,SAAQ;AACvC,kBAAU,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,MACvC;AACA,YAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AAEf,eAAS;AACT,YAAM,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK;AACnC,aAAM,CAAC,YAAY,QAAQ,KAAK,IAAI,CAAC,GAAE;AACnC,YAAI,CAAC,YAAY,IAAI,KAAK,CAAC,GAAG;AAC1B,oBAAU,QAAQ,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,QACrD;AAAA,MACJ;AACA,YAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,aAAS,SAAS,IAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,SAAS,IAAI,QAAQ,WAAW,KAAK,UAAU,GAAG,IAAI,KAAK;AAAA,EACvH;AACA,SAAO;AACX;AAEA,IAAM,YAAY,CAAC,QAAM;AACrB,MAAI,WAAW,GAAG,GAAG;AACjB,QAAI;AACA,YAAM,IAAI;AAAA,IACd,SAAS,KAAK;AAEV,YAAM;AAAA,IACV;AAAA,EACJ;AAGA,QAAM,OAAO;AAEb,QAAM,OAAO,OAAO,WAAW,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,SAAS,OAAO,WAAW,GAAG,IAAI;AACjG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAGA,IAAI,cAAc;AAClB,IAAM,eAAe,MAAI,EAAE;AAE3B,eAAe,kBAAkB,MAAM;AACnC,QAAM,CAACD,QAAO,MAAM,OAAO,KAAK,IAAI;AAGpC,QAAM,UAAU,aAAa;AAAA,IACzB,eAAe;AAAA,IACf,cAAc;AAAA,EAClB,GAAG,OAAO,UAAU,YAAY;AAAA,IAC5B,YAAY;AAAA,EAChB,IAAI,SAAS,CAAC,CAAC;AACf,MAAI,gBAAgB,QAAQ;AAC5B,QAAM,wBAAwB,QAAQ;AACtC,MAAI,iBAAiB,QAAQ;AAC7B,QAAM,kBAAkB,CAAC,UAAQ;AAC7B,WAAO,OAAO,0BAA0B,aAAa,sBAAsB,KAAK,IAAI,0BAA0B;AAAA,EAClH;AACA,QAAM,eAAe,QAAQ;AAG7B,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,YAAY;AAClB,UAAM,cAAc,CAAC;AACrB,UAAM,KAAKA,OAAM,KAAK;AACtB,eAAW,OAAO,IAAG;AACjB;AAAA;AAAA,QACA,CAAC,iBAAiB,KAAK,GAAG,KAAK,UAAUA,OAAM,IAAI,GAAG,EAAE,EAAE;AAAA,QAAG;AACzD,oBAAY,KAAK,GAAG;AAAA,MACxB;AAAA,IACJ;AACA,WAAO,QAAQ,IAAI,YAAY,IAAI,WAAW,CAAC;AAAA,EACnD;AACA,SAAO,YAAY,IAAI;AACvB,iBAAe,YAAY,IAAI;AAE3B,UAAM,CAAC,GAAG,IAAI,UAAU,EAAE;AAC1B,QAAI,CAAC,IAAK;AACV,UAAM,CAAC,KAAK,GAAG,IAAI,kBAAkBA,QAAO,GAAG;AAC/C,UAAM,CAAC,oBAAoB,UAAU,OAAO,OAAO,IAAI,eAAe,IAAIA,MAAK;AAC/E,UAAM,kBAAkB,MAAI;AACxB,YAAM,eAAe,mBAAmB,GAAG;AAC3C,YAAM,aAAa,WAAW,QAAQ,UAAU,IAAI,QAAQ,WAAW,IAAI,EAAE,MAAM,EAAE,IAAI,QAAQ,eAAe;AAChH,UAAI,YAAY;AAGZ,eAAO,MAAM,GAAG;AAChB,eAAO,QAAQ,GAAG;AAClB,YAAI,gBAAgB,aAAa,CAAC,GAAG;AACjC,iBAAO,aAAa,CAAC,EAAmB,YAAY,EAAE,KAAK,MAAI,IAAI,EAAE,IAAI;AAAA,QAC7E;AAAA,MACJ;AACA,aAAO,IAAI,EAAE;AAAA,IACjB;AAEA,QAAI,KAAK,SAAS,GAAG;AAEjB,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,OAAO;AACX,QAAI;AACJ,QAAI,UAAU;AAEd,UAAM,mBAAmB,aAAa;AACtC,aAAS,GAAG,IAAI;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,UAAM,oBAAoB,CAAC,YAAY,cAAc;AACrD,UAAM,QAAQ,IAAI;AAIlB,UAAM,gBAAgB,MAAM;AAC5B,UAAM,cAAc,MAAM;AAC1B,UAAM,gBAAgB,YAAY,WAAW,IAAI,gBAAgB;AAEjE,QAAI,mBAAmB;AACnB,uBAAiB,WAAW,cAAc,IAAI,eAAe,eAAe,aAAa,IAAI;AAE7F,UAAI;AAAA,QACA,MAAM;AAAA,QACN,IAAI;AAAA,MACR,CAAC;AAAA,IACL;AACA,QAAI,WAAW,IAAI,GAAG;AAElB,UAAI;AACA,eAAO,KAAK,aAAa;AAAA,MAC7B,SAAS,KAAK;AAEV,gBAAQ;AACR,kBAAU;AAAA,MACd;AAAA,IACJ;AAEA,QAAI,QAAQ,cAAc,IAAI,GAAG;AAG7B,aAAO,MAAM,KAAK,MAAM,CAAC,QAAM;AAC3B,gBAAQ;AACR,kBAAU;AAAA,MACd,CAAC;AAID,UAAI,qBAAqB,SAAS,GAAG,EAAE,CAAC,GAAG;AACvC,YAAI,QAAS,OAAM;AACnB,eAAO;AAAA,MACX,WAAW,WAAW,qBAAqB,gBAAgB,KAAK,GAAG;AAG/D,wBAAgB;AAEhB,YAAI;AAAA,UACA,MAAM;AAAA,UACN,IAAI;AAAA,QACR,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,QAAI,eAAe;AACf,UAAI,CAAC,SAAS;AAEV,YAAI,WAAW,aAAa,GAAG;AAC3B,gBAAM,qBAAqB,cAAc,MAAM,aAAa;AAC5D,cAAI;AAAA,YACA,MAAM;AAAA,YACN,OAAO;AAAA,YACP,IAAI;AAAA,UACR,CAAC;AAAA,QACL,OAAO;AAEH,cAAI;AAAA,YACA;AAAA,YACA,OAAO;AAAA,YACP,IAAI;AAAA,UACR,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,GAAG,EAAE,CAAC,IAAI,aAAa;AAEhC,YAAQ,QAAQ,gBAAgB,CAAC,EAAE,KAAK,MAAI;AAGxC,UAAI;AAAA,QACA,IAAI;AAAA,MACR,CAAC;AAAA,IACL,CAAC;AAED,QAAI,SAAS;AACT,UAAI,aAAc,OAAM;AACxB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,oBAAoB,CAAC,cAAc,SAAO;AAC5C,aAAU,OAAO,cAAa;AAC1B,QAAI,aAAa,GAAG,EAAE,CAAC,EAAG,cAAa,GAAG,EAAE,CAAC,EAAE,IAAI;AAAA,EACvD;AACJ;AACA,IAAM,YAAY,CAAC,UAAU,YAAU;AAMnC,MAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,aAAa,sBAAsB,OAAO;AAGvD,UAAM,qBAAqB,uBAAO,OAAO,IAAI;AAC7C,UAAME,UAAS,eAAe,KAAK,WAAW,QAAQ;AACtD,QAAI,UAAU;AACd,UAAM,gBAAgB,uBAAO,OAAO,IAAI;AACxC,UAAM,YAAY,CAAC,KAAK,aAAW;AAC/B,YAAM,OAAO,cAAc,GAAG,KAAK,CAAC;AACpC,oBAAc,GAAG,IAAI;AACrB,WAAK,KAAK,QAAQ;AAClB,aAAO,MAAI,KAAK,OAAO,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,IACpD;AACA,UAAM,SAAS,CAAC,KAAK,OAAO,SAAO;AAC/B,eAAS,IAAI,KAAK,KAAK;AACvB,YAAM,OAAO,cAAc,GAAG;AAC9B,UAAI,MAAM;AACN,mBAAW,MAAM,MAAK;AAClB,aAAG,OAAO,IAAI;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,eAAe,MAAI;AACrB,UAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAE/B,uBAAe,IAAI,UAAU;AAAA,UACzB;AAAA,UACA,uBAAO,OAAO,IAAI;AAAA,UAClB,uBAAO,OAAO,IAAI;AAAA,UAClB,uBAAO,OAAO,IAAI;AAAA,UAClBA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,YAAI,CAAC,WAAW;AAOZ,gBAAM,eAAe,KAAK,UAAU,WAAW,KAAK,WAAW,kBAAkB,KAAK,WAAW,oBAAqC,WAAW,CAAC,CAAC;AACnJ,gBAAM,mBAAmB,KAAK,cAAc,WAAW,KAAK,WAAW,kBAAkB,KAAK,WAAW,oBAAqC,eAAe,CAAC,CAAC;AAC/J,oBAAU,MAAI;AACV,4BAAgB,aAAa;AAC7B,gCAAoB,iBAAiB;AAIrC,2BAAe,OAAO,QAAQ;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,iBAAa;AAMb,WAAO;AAAA,MACH;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,eAAe,IAAI,QAAQ,EAAE,CAAC;AAAA,EAClC;AACJ;AAGA,IAAM,eAAe,CAAC,GAAG,IAAI,QAAQ,YAAY,SAAO;AACpD,QAAM,gBAAgB,OAAO;AAC7B,QAAM,oBAAoB,KAAK;AAE/B,QAAM,UAAU,CAAC,GAAG,KAAK,OAAO,IAAI,QAAQ,MAAM,oBAAoB,IAAI,oBAAoB,OAAO,OAAO;AAC5G,MAAI,CAAC,YAAY,aAAa,KAAK,oBAAoB,eAAe;AAClE;AAAA,EACJ;AACA,aAAW,YAAY,SAAS,IAAI;AACxC;AACA,IAAM,UAAU;AAEhB,IAAM,CAAC,OAAO,MAAM,IAAI,UAAU,oBAAI,IAAI,CAAC;AAE3C,IAAM,gBAAgB;AAAA,EAAa;AAAA;AAAA,IAE/B,eAAe;AAAA,IACf,WAAW;AAAA,IACX,SAAS;AAAA,IACT;AAAA,IACA,aAAa;AAAA;AAAA,IAEb,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA;AAAA,IAEpB,oBAAoB,iBAAiB,MAAQ;AAAA,IAC7C,uBAAuB,IAAI;AAAA,IAC3B,kBAAkB,IAAI;AAAA,IACtB,gBAAgB,iBAAiB,MAAO;AAAA;AAAA,IAExC;AAAA,IACA,UAAU,MAAI;AAAA,IACd;AAAA,IACA;AAAA,IACA,UAAU,CAAC;AAAA,EACf;AAAA;AAAA,EACA;AAAM;AAEN,IAAM,eAAe,CAAC,GAAG,MAAI;AAEzB,QAAM,IAAI,aAAa,GAAG,CAAC;AAE3B,MAAI,GAAG;AACH,UAAM,EAAE,KAAK,IAAI,UAAU,GAAG,IAAI;AAClC,UAAM,EAAE,KAAK,IAAI,UAAU,GAAG,IAAI;AAClC,QAAI,MAAM,IAAI;AACV,QAAE,MAAM,GAAG,OAAO,EAAE;AAAA,IACxB;AACA,QAAI,MAAM,IAAI;AACV,QAAE,WAAW,aAAa,IAAI,EAAE;AAAA,IACpC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,uBAAmB,4BAAc,CAAC,CAAC;AACzC,IAAM,YAAY,CAAC,UAAQ;AACvB,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,mBAAe,yBAAW,gBAAgB;AAChD,QAAM,qBAAqB,WAAW,KAAK;AAC3C,QAAM,aAAS,sBAAQ,MAAI,qBAAqB,MAAM,YAAY,IAAI,OAAO;AAAA,IACzE;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,qBAAiB,sBAAQ,MAAI,qBAAqB,SAAS,aAAa,cAAc,MAAM,GAAG;AAAA,IACjG;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,UAAU,OAAO;AAElC,QAAM,sBAAkB,qBAAO,SAAS;AACxC,MAAI,YAAY,CAAC,gBAAgB,SAAS;AACtC,oBAAgB,UAAU,UAAU,SAAS,eAAe,SAAS,KAAK,GAAG,MAAM;AAAA,EACvF;AACA,QAAM,eAAe,gBAAgB;AAErC,MAAI,cAAc;AACd,mBAAe,QAAQ,aAAa,CAAC;AACrC,mBAAe,SAAS,aAAa,CAAC;AAAA,EAC1C;AAEA,4BAA0B,MAAI;AAC1B,QAAI,cAAc;AACd,mBAAa,CAAC,KAAK,aAAa,CAAC,EAAE;AACnC,aAAO,aAAa,CAAC;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,aAAO,4BAAc,iBAAiB,UAAU,aAAa,OAAO;AAAA,IAChE,OAAO;AAAA,EACX,CAAC,CAAC;AACN;;;AGnjBA,IAAM,kBAAkB;;;ACMxB,IAAAC,gBAAkC;AAIlC,IAAM,iBAAiB,mBAAmB,OAAO;AACjD,IAAM,MAAM,iBAAiB,OAAO,uBAAuB,CAAC;AAC5D,IAAM,gBAAgB,MAAI;AACtB,MAAI,gBAAgB;AAEhB,WAAO,yBAAyB,cAAAC;AAAA,EACpC;AACJ;AAEA,IAAM,YAAY,CAAC,SAAO;AACtB,SAAO,WAAW,KAAK,CAAC,CAAC,IAAI;AAAA,IACzB,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,IACN,KAAK,CAAC,KAAK,CAAC;AAAA,EAChB,IAAI;AAAA,IACA,KAAK,CAAC;AAAA,IACN;AAAA,KACC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;AAAA,EAC/C;AACJ;AAEA,IAAM,eAAe,MAAI;AACrB,SAAO,aAAa,mBAAe,0BAAW,gBAAgB,CAAC;AACnE;AAEA,IAAM,UAAU,CAAC,MAAM,YAAU;AAC7B,QAAM,CAAC,KAAK,KAAK,IAAI,UAAU,IAAI;AACnC,QAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAEhD,MAAI,QAAQ,GAAG,EAAG,QAAO,QAAQ,GAAG;AACpC,QAAM,MAAM,QAAQ,KAAK;AACzB,UAAQ,GAAG,IAAI;AACf,SAAO;AACX;AACA,IAAM,aAAa,CAAC,eAAa,CAAC,MAAM,UAAU,WAAS;AAEnD,QAAM,UAAU,aAAa,IAAI,SAAO;AACpC,UAAM,CAAC,GAAG,IAAI,UAAU,IAAI;AAC5B,UAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAChD,QAAI,IAAI,WAAW,eAAe,GAAG;AAGjC,aAAO,SAAS,GAAG,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,QAAQ,GAAG;AACvB,QAAI,YAAY,GAAG,EAAG,QAAO,SAAS,GAAG,IAAI;AAC7C,WAAO,QAAQ,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO,WAAW,MAAM,SAAS,MAAM;AAC3C;AAEJ,IAAM,sBAAsB,IAAI,OAAO,UAAU;AAIjD,IAAM,WAAW,CAAC,SAAO;AACrB,SAAO,SAAS,cAAc,MAAM;AAEhC,UAAM,iBAAiB,aAAa;AAEpC,UAAM,CAAC,KAAK,IAAI,OAAO,IAAI,UAAU,IAAI;AAEzC,UAAM,SAAS,aAAa,gBAAgB,OAAO;AAEnD,QAAI,OAAO;AACX,UAAM,EAAE,KAAAC,KAAI,IAAI;AAChB,UAAMC,eAAcD,QAAO,CAAC,GAAG,OAAO,mBAAmB;AACzD,aAAQ,IAAIC,YAAW,QAAQ,OAAK;AAChC,aAAOA,YAAW,CAAC,EAAE,IAAI;AAAA,IAC7B;AACA,WAAO,KAAK,KAAK,MAAM,OAAO,WAAW,MAAM,MAAM;AAAA,EACzD;AACJ;AAIA,IAAM,oBAAoB,CAAC,KAAK,WAAW,aAAW;AAClD,QAAM,oBAAoB,UAAU,GAAG,MAAM,UAAU,GAAG,IAAI,CAAC;AAC/D,oBAAkB,KAAK,QAAQ;AAC/B,SAAO,MAAI;AACP,UAAM,QAAQ,kBAAkB,QAAQ,QAAQ;AAChD,QAAI,SAAS,GAAG;AAEZ,wBAAkB,KAAK,IAAI,kBAAkB,kBAAkB,SAAS,CAAC;AACzE,wBAAkB,IAAI;AAAA,IAC1B;AAAA,EACJ;AACJ;AAGA,IAAM,iBAAiB,CAACC,SAAQD,gBAAa;AACzC,SAAO,IAAI,SAAO;AACd,UAAM,CAAC,KAAK,IAAI,MAAM,IAAI,UAAU,IAAI;AACxC,UAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,OAAOA,WAAU;AACjD,WAAOC,QAAO,KAAK,IAAI;AAAA,MACnB,GAAG;AAAA,MACH,KAAK;AAAA,IACT,CAAC;AAAA,EACL;AACJ;AAEA,cAAc;;;AL1Gd,IAAMC,QAAO,MAAI;AAAC;AAKlB,IAAMC;AAAA;AAAA,EAA8BD,MAAK;AAAA;AACzC,IAAME,UAAS;AACf,IAAMC,eAAc,CAAC,MAAI,MAAMF;AAC/B,IAAMG,cAAa,CAAC,MAAI,OAAO,KAAK;AAMpC,IAAMC,SAAQ,oBAAI,QAAQ;AAC1B,IAAMC,eAAc,CAAC,UAAQJ,QAAO,UAAU,SAAS,KAAK,KAAK;AACjE,IAAMK,oBAAmB,CAAC,UAAU,SAAO,aAAa,WAAW,IAAI;AAEvE,IAAIC,WAAU;AASd,IAAMC,cAAa,CAAC,QAAM;AACtB,QAAM,OAAO,OAAO;AACpB,QAAM,WAAWH,aAAY,GAAG;AAChC,QAAM,SAASC,kBAAiB,UAAU,MAAM;AAChD,QAAM,UAAUA,kBAAiB,UAAU,QAAQ;AACnD,QAAM,gBAAgBA,kBAAiB,UAAU,QAAQ;AACzD,MAAI;AACJ,MAAI;AACJ,MAAIL,QAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,SAAS;AAG5C,aAASG,OAAM,IAAI,GAAG;AACtB,QAAI,OAAQ,QAAO;AAInB,aAAS,EAAEG,WAAU;AACrB,IAAAH,OAAM,IAAI,KAAK,MAAM;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,eAAS;AACT,WAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,SAAQ;AACvC,kBAAUI,YAAW,IAAI,KAAK,CAAC,IAAI;AAAA,MACvC;AACA,MAAAJ,OAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe;AAEf,eAAS;AACT,YAAM,OAAOH,QAAO,KAAK,GAAG,EAAE,KAAK;AACnC,aAAM,CAACC,aAAY,QAAQ,KAAK,IAAI,CAAC,GAAE;AACnC,YAAI,CAACA,aAAY,IAAI,KAAK,CAAC,GAAG;AAC1B,oBAAU,QAAQ,MAAMM,YAAW,IAAI,KAAK,CAAC,IAAI;AAAA,QACrD;AAAA,MACJ;AACA,MAAAJ,OAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,aAAS,SAAS,IAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,SAAS,IAAI,QAAQ,WAAW,KAAK,UAAU,GAAG,IAAI,KAAK;AAAA,EACvH;AACA,SAAO;AACX;AAEA,IAAMK,aAAY,CAAC,QAAM;AACrB,MAAIN,YAAW,GAAG,GAAG;AACjB,QAAI;AACA,YAAM,IAAI;AAAA,IACd,SAAS,KAAK;AAEV,YAAM;AAAA,IACV;AAAA,EACJ;AAGA,QAAM,OAAO;AAEb,QAAM,OAAO,OAAO,WAAW,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,SAAS,OAAOK,YAAW,GAAG,IAAI;AACjG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,qBAAqB,CAAC,QAAMC,WAAU,GAAG,EAAE,CAAC;AAGlD,IAAMC,OAAM,cAAAC,QAAM;AAAA;AAAA;AAAA;AAAA,CAIjB,CAAC,aAAW;AACT,UAAO,SAAS,QAAO;AAAA,IACnB,KAAK;AACD,YAAM;AAAA,IACV,KAAK;AACD,aAAO,SAAS;AAAA,IACpB,KAAK;AACD,YAAM,SAAS;AAAA,IACnB;AACI,eAAS,SAAS;AAClB,eAAS,KAAK,CAAC,MAAI;AACf,iBAAS,SAAS;AAClB,iBAAS,QAAQ;AAAA,MACrB,GAAG,CAAC,MAAI;AACJ,iBAAS,SAAS;AAClB,iBAAS,SAAS;AAAA,MACtB,CAAC;AACD,YAAM;AAAA,EACd;AACJ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AACZ;AACA,IAAM,gBAAgB,CAAC,MAAM,SAAS,WAAS;AAC3C,QAAM,EAAE,OAAAC,QAAO,SAAAC,UAAS,UAAU,cAAc,mBAAmB,mBAAmB,iBAAiB,mBAAmB,oBAAoB,iBAAiB,IAAI;AACnK,QAAM,CAAC,oBAAoB,UAAU,OAAO,OAAO,IAAI,eAAe,IAAID,MAAK;AAK/E,QAAM,CAAC,KAAK,KAAK,IAAI,UAAY,IAAI;AAErC,QAAM,wBAAoB,sBAAO,KAAK;AAGtC,QAAM,mBAAe,sBAAO,KAAK;AAEjC,QAAM,aAAS,sBAAO,GAAG;AACzB,QAAM,iBAAa,sBAAO,OAAO;AACjC,QAAM,gBAAY,sBAAO,MAAM;AAC/B,QAAM,YAAY,MAAI,UAAU;AAChC,QAAM,WAAW,MAAI,UAAU,EAAE,UAAU,KAAK,UAAU,EAAE,SAAS;AACrE,QAAM,CAAC,UAAU,UAAU,gBAAgB,eAAe,IAAI,kBAAkBA,QAAO,GAAG;AAC1F,QAAM,wBAAoB,sBAAO,CAAC,CAAC,EAAE;AAGrC,QAAM,WAAW,YAAc,YAAY,IAAI,YAAc,OAAO,QAAQ,IAAI,YAAc,OAAO,SAAS,GAAG,IAAI;AACrH,QAAM,UAAU,CAAC,MAAM,YAAU;AAC7B,eAAU,KAAK,mBAAkB;AAC7B,YAAM,IAAI;AACV,UAAI,MAAM,QAAQ;AACd,YAAI,CAACC,SAAQ,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG;AAC/B,cAAI,CAAC,YAAc,KAAK,CAAC,CAAC,GAAG;AACzB,mBAAO;AAAA,UACX;AACA,cAAI,CAACA,SAAQ,cAAc,QAAQ,CAAC,CAAC,GAAG;AACpC,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,YAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG;AACxB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,kBAAc,uBAAQ,MAAI;AAC5B,UAAM,sBAAsB,MAAI;AAC5B,UAAI,CAAC,IAAK,QAAO;AACjB,UAAI,CAAC,QAAS,QAAO;AAErB,UAAI,CAAC,YAAc,iBAAiB,EAAG,QAAO;AAE9C,UAAI,UAAU,EAAE,SAAS,EAAG,QAAO;AACnC,UAAI,SAAU,QAAO;AACrB,aAAO,sBAAsB;AAAA,IACjC,GAAG;AAEH,UAAM,mBAAmB,CAAC,UAAQ;AAE9B,YAAM,WAAW,aAAa,KAAK;AACnC,aAAO,SAAS;AAChB,UAAI,CAAC,oBAAoB;AACrB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,cAAc;AAAA,QACd,WAAW;AAAA,QACX,GAAG;AAAA,MACP;AAAA,IACJ;AACA,UAAMC,cAAa,SAAS;AAC5B,UAAM,cAAc,gBAAgB;AACpC,UAAM,iBAAiB,iBAAiBA,WAAU;AAClD,UAAM,iBAAiBA,gBAAe,cAAc,iBAAiB,iBAAiB,WAAW;AAIjG,QAAI,oBAAoB;AACxB,WAAO;AAAA,MACH,MAAI;AACA,cAAM,cAAc,iBAAiB,SAAS,CAAC;AAC/C,cAAM,gBAAgB,QAAQ,aAAa,iBAAiB;AAC5D,YAAI,eAAe;AAWf,4BAAkB,OAAO,YAAY;AACrC,4BAAkB,YAAY,YAAY;AAC1C,4BAAkB,eAAe,YAAY;AAC7C,4BAAkB,QAAQ,YAAY;AACtC,iBAAO;AAAA,QACX,OAAO;AACH,8BAAoB;AACpB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,MAAI;AAAA,IACR;AAAA,EAEJ,GAAG;AAAA,IACCF;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,aAAS,sCAAqB;AAAA,IAAY,CAAC,aAAW,eAAe,KAAK,CAAC,SAAS,SAAO;AACzF,UAAI,CAAC,QAAQ,MAAM,OAAO,EAAG,UAAS;AAAA,IAC1C,CAAC;AAAA;AAAA,IACL;AAAA,MACIA;AAAA,MACA;AAAA,IACJ;AAAA,EAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAClC,QAAM,iBAAiB,CAAC,kBAAkB;AAC1C,QAAM,iBAAiB,mBAAmB,GAAG,KAAK,mBAAmB,GAAG,EAAE,SAAS;AACnF,QAAM,aAAa,OAAO;AAC1B,QAAM,OAAO,YAAc,UAAU,IAAI,YAAY,cAAc,QAAQ,IAAIF,KAAI,QAAQ,IAAI,WAAW;AAC1G,QAAM,QAAQ,OAAO;AAErB,QAAM,mBAAe,sBAAO,IAAI;AAChC,QAAM,eAAe,mBAAmB,YAAc,UAAU,IAAI,YAAc,aAAa,OAAO,IAAI,OAAO,aAAa,UAAU,aAAa;AAIrJ,QAAM,+BAA+B,MAAI;AAErC,QAAI,kBAAkB,CAAC,YAAc,KAAK,EAAG,QAAO;AAEpD,QAAI,kBAAkB,CAAC,YAAc,iBAAiB,EAAG,QAAO;AAEhE,QAAI,UAAU,EAAE,SAAS,EAAG,QAAO;AAInC,QAAI,SAAU,QAAO,YAAc,IAAI,IAAI,QAAQ;AAGnD,WAAO,YAAc,IAAI,KAAK;AAAA,EAClC,GAAG;AAGH,QAAM,yBAAyB,CAAC,EAAE,OAAO,WAAW,kBAAkB;AACtE,QAAM,eAAe,YAAc,OAAO,YAAY,IAAI,yBAAyB,OAAO;AAC1F,QAAM,YAAY,YAAc,OAAO,SAAS,IAAI,yBAAyB,OAAO;AAGpF,QAAM,iBAAa;AAAA,IAAY,OAAO,mBAAiB;AACnD,YAAM,iBAAiB,WAAW;AAClC,UAAI,CAAC,OAAO,CAAC,kBAAkB,aAAa,WAAW,UAAU,EAAE,SAAS,GAAG;AAC3E,eAAO;AAAA,MACX;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,UAAU;AACd,YAAM,OAAO,kBAAkB,CAAC;AAGhC,YAAM,wBAAwB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK;AAWlD,YAAM,oBAAoB,MAAI;AAC3B,YAAI,iBAAiB;AACjB,iBAAO,CAAC,aAAa,WAAW,QAAQ,OAAO,WAAW,kBAAkB;AAAA,QAChF;AACA,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAEA,YAAM,aAAa;AAAA,QACf,cAAc;AAAA,QACd,WAAW;AAAA,MACf;AACA,YAAM,8BAA8B,MAAI;AACpC,iBAAS,UAAU;AAAA,MACvB;AACA,YAAM,eAAe,MAAI;AAErB,cAAM,cAAc,MAAM,GAAG;AAC7B,YAAI,eAAe,YAAY,CAAC,MAAM,SAAS;AAC3C,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,MACJ;AAEA,YAAM,eAAe;AAAA,QACjB,cAAc;AAAA,MAClB;AAGA,UAAI,YAAc,SAAS,EAAE,IAAI,GAAG;AAChC,qBAAa,YAAY;AAAA,MAC7B;AACA,UAAI;AACA,YAAI,uBAAuB;AACvB,mBAAS,YAAY;AAGrB,cAAI,OAAO,kBAAkB,YAAc,SAAS,EAAE,IAAI,GAAG;AACzD,uBAAW,MAAI;AACX,kBAAI,WAAW,kBAAkB,GAAG;AAChC,0BAAU,EAAE,cAAc,KAAK,MAAM;AAAA,cACzC;AAAA,YACJ,GAAG,OAAO,cAAc;AAAA,UAC5B;AAGA,gBAAM,GAAG,IAAI;AAAA,YACT,eAAe,KAAK;AAAA,YACpB,aAAa;AAAA,UACjB;AAAA,QACJ;AAGA;AACA,SAAC,SAAS,OAAO,IAAI,MAAM,GAAG;AAC9B,kBAAU,MAAM;AAChB,YAAI,uBAAuB;AAGvB,qBAAW,cAAc,OAAO,gBAAgB;AAAA,QACpD;AAOA,YAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE,CAAC,MAAM,SAAS;AAC1C,cAAI,uBAAuB;AACvB,gBAAI,kBAAkB,GAAG;AACrB,wBAAU,EAAE,YAAY,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,mBAAW,QAAQ;AAanB,cAAM,eAAe,SAAS,GAAG;AACjC,YAAI,CAAC,YAAc,YAAY;AAAA,SAC9B,WAAW,aAAa,CAAC;AAAA,QAC1B,WAAW,aAAa,CAAC;AAAA,QACzB,aAAa,CAAC,MAAM,IAAI;AACpB,sCAA4B;AAC5B,cAAI,uBAAuB;AACvB,gBAAI,kBAAkB,GAAG;AACrB,wBAAU,EAAE,YAAY,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAGA,cAAM,YAAY,SAAS,EAAE;AAG7B,mBAAW,OAAOG,SAAQ,WAAW,OAAO,IAAI,YAAY;AAE5D,YAAI,uBAAuB;AACvB,cAAI,kBAAkB,GAAG;AACrB,sBAAU,EAAE,UAAU,SAAS,KAAK,MAAM;AAAA,UAC9C;AAAA,QACJ;AAAA,MACJ,SAAS,KAAK;AACV,qBAAa;AACb,cAAM,gBAAgB,UAAU;AAChC,cAAM,EAAE,mBAAmB,IAAI;AAE/B,YAAI,CAAC,cAAc,SAAS,GAAG;AAE3B,qBAAW,QAAQ;AAGnB,cAAI,yBAAyB,kBAAkB,GAAG;AAC9C,0BAAc,QAAQ,KAAK,KAAK,aAAa;AAC7C,gBAAI,uBAAuB,QAAQ,WAAa,kBAAkB,KAAK,mBAAmB,GAAG,GAAG;AAC5F,kBAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,UAAU,EAAE,yBAAyB,SAAS,GAAG;AAIpF,8BAAc,aAAa,KAAK,KAAK,eAAe,CAAC,UAAQ;AACzD,wBAAM,eAAe,mBAAmB,GAAG;AAC3C,sBAAI,gBAAgB,aAAa,CAAC,GAAG;AACjC,iCAAa,CAAC,EAAE,eAAiB,wBAAwB,KAAK;AAAA,kBAClE;AAAA,gBACJ,GAAG;AAAA,kBACC,aAAa,KAAK,cAAc,KAAK;AAAA,kBACrC,QAAQ;AAAA,gBACZ,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,gBAAU;AAEV,kCAA4B;AAC5B,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA;AAAA,MACI;AAAA,MACAD;AAAA,IACJ;AAAA,EAAC;AAGD,QAAM,kBAAc;AAAA;AAAA,IACpB,IAAI,SAAO;AACP,aAAO,eAAeA,QAAO,OAAO,SAAS,GAAG,IAAI;AAAA,IACxD;AAAA;AAAA,IACA,CAAC;AAAA,EAAC;AAEF,4BAA0B,MAAI;AAC1B,eAAW,UAAU;AACrB,cAAU,UAAU;AAGpB,QAAI,CAAC,YAAc,UAAU,GAAG;AAC5B,mBAAa,UAAU;AAAA,IAC3B;AAAA,EACJ,CAAC;AAED,4BAA0B,MAAI;AAC1B,QAAI,CAAC,IAAK;AACV,UAAM,iBAAiB,WAAW,KAAK,WAAa,WAAW;AAC/D,QAAI,yBAAyB;AAC7B,QAAI,UAAU,EAAE,mBAAmB;AAC/B,YAAM,UAAU,KAAK,IAAI;AACzB,+BAAyB,UAAU,UAAU,EAAE;AAAA,IACnD;AAGA,UAAM,eAAe,CAAC,MAAM,OAAO,CAAC,MAAI;AACpC,UAAI,QAAQ,eAAiB,aAAa;AACtC,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,UAAU,EAAE,qBAAqB,MAAM,0BAA0B,SAAS,GAAG;AAC7E,mCAAyB,MAAM,UAAU,EAAE;AAC3C,yBAAe;AAAA,QACnB;AAAA,MACJ,WAAW,QAAQ,eAAiB,iBAAiB;AACjD,YAAI,UAAU,EAAE,yBAAyB,SAAS,GAAG;AACjD,yBAAe;AAAA,QACnB;AAAA,MACJ,WAAW,QAAQ,eAAiB,cAAc;AAC9C,eAAO,WAAW;AAAA,MACtB,WAAW,QAAQ,eAAiB,wBAAwB;AACxD,eAAO,WAAW,IAAI;AAAA,MAC1B;AACA;AAAA,IACJ;AACA,UAAM,cAAc,kBAAkB,KAAK,oBAAoB,YAAY;AAE3E,iBAAa,UAAU;AACvB,WAAO,UAAU;AACjB,sBAAkB,UAAU;AAE5B,aAAS;AAAA,MACL,IAAI;AAAA,IACR,CAAC;AAED,QAAI,6BAA6B;AAG7B,UAAI,CAAC,MAAM,GAAG,GAAG;AACb,YAAI,YAAc,IAAI,KAAK,WAAW;AAElC,yBAAe;AAAA,QACnB,OAAO;AAGH,cAAI,cAAc;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,MAAI;AAEP,mBAAa,UAAU;AACvB,kBAAY;AAAA,IAChB;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AAED,4BAA0B,MAAI;AAC1B,QAAI;AACJ,aAAS,OAAO;AAGZ,YAAM,WAAW,WAAa,eAAe,IAAI,gBAAgB,SAAS,EAAE,IAAI,IAAI;AAIpF,UAAI,YAAY,UAAU,IAAI;AAC1B,gBAAQ,WAAW,SAAS,QAAQ;AAAA,MACxC;AAAA,IACJ;AACA,aAAS,UAAU;AAGf,UAAI,CAAC,SAAS,EAAE,UAAU,qBAAqB,UAAU,EAAE,UAAU,OAAO,sBAAsB,UAAU,EAAE,SAAS,IAAI;AACvH,mBAAW,WAAW,EAAE,KAAK,IAAI;AAAA,MACrC,OAAO;AAEH,aAAK;AAAA,MACT;AAAA,IACJ;AACA,SAAK;AACL,WAAO,MAAI;AACP,UAAI,OAAO;AACP,qBAAa,KAAK;AAClB,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,mCAAc,YAAY;AAK1B,MAAI,YAAY,YAAc,IAAI,KAAK,KAAK;AAIxC,QAAI,CAAC,mBAAmB,WAAW;AAC/B,YAAM,IAAI,MAAM,uDAAuD;AAAA,IAC3E;AAEA,eAAW,UAAU;AACrB,cAAU,UAAU;AACpB,iBAAa,UAAU;AACvB,UAAM,MAAM,QAAQ,GAAG;AACvB,QAAI,CAAC,YAAc,GAAG,GAAG;AACrB,YAAM,UAAU,YAAY,GAAG;AAC/B,MAAAF,KAAI,OAAO;AAAA,IACf;AACA,QAAI,YAAc,KAAK,GAAG;AACtB,YAAM,UAAU,WAAW,WAAW;AACtC,UAAI,CAAC,YAAc,YAAY,GAAG;AAC9B,gBAAQ,SAAS;AACjB,gBAAQ,QAAQ;AAAA,MACpB;AACA,MAAAA,KAAI,OAAO;AAAA,IACf,OAAO;AACH,YAAM;AAAA,IACV;AAAA,EACJ;AACA,QAAM,cAAc;AAAA,IAChB,QAAQ;AAAA,IACR,IAAI,OAAQ;AACR,wBAAkB,OAAO;AACzB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,QAAS;AACT,wBAAkB,QAAQ;AAC1B,aAAO;AAAA,IACX;AAAA,IACA,IAAI,eAAgB;AAChB,wBAAkB,eAAe;AACjC,aAAO;AAAA,IACX;AAAA,IACA,IAAI,YAAa;AACb,wBAAkB,YAAY;AAC9B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAMK,aAAY,OAAS,eAAe,WAAa,gBAAgB;AAAA,EACnE,OAAO;AACX,CAAC;AAeG,IAAM,SAAS,SAAS,aAAa;", "names": ["import_react", "cache", "React", "mutate", "import_react", "React", "use", "middleware", "useSWR", "noop", "UNDEFINED", "OBJECT", "isUndefined", "isFunction", "table", "getTypeName", "isObjectTypeName", "counter", "stableHash", "serialize", "use", "React", "cache", "compare", "cachedData", "SWRConfig"]}
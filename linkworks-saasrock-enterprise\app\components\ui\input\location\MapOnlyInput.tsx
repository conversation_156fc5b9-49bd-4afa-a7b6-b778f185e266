import { forwardRef, Ref, useImperativeHandle, useRef, useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { cn } from "~/lib/utils";
import HintTooltip from "../../tooltips/HintTooltip";
import { MapPin, Loader2, Move, Filter, Shield } from "lucide-react";
import { useGoogleMaps } from "~/hooks/useGoogleMaps";
import { useMapZoomPersistence } from "~/hooks/useMapZoomPersistence";
import { LocationData } from "~/types/location";
import LocationHelper from "~/utils/helpers/LocationHelper";

export interface RefMapOnlyInput {
  input: HTMLInputElement | null;
}

export interface MapOnlyInputProps {
  name?: string;
  title?: string;
  value?: LocationData;
  defaultValue?: LocationData;
  onChange?: (value: LocationData | undefined) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: string;
  mapHeight?: string;
  zoom?: number;
  columns?: number;
  group?: string;
  // Security & Validation
  rateLimit?: number;
  validatePlaceDetails?: boolean;
  storePlaceId?: boolean;
  // Boundary filters
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  strictBounds?: boolean;
  // Custom styling
  customMarkerIcon?: string;
  markerColor?: string;
  // Enhanced features
  enableReverseGeocoding?: boolean;
}

const MapOnlyInput = (
  {
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    mapHeight = "300px",
    zoom = 15,
    columns,
    group,
    // Security & Validation defaults
    rateLimit = 10,
    validatePlaceDetails = true,
    storePlaceId = true,
    // Boundary filters
    bounds,
    strictBounds = false,
    // Custom styling
    customMarkerIcon,
    markerColor = "#EA4335",
    // Enhanced features
    enableReverseGeocoding = true,
  }: MapOnlyInputProps,
  ref: Ref<RefMapOnlyInput>
) => {
  const { t } = useTranslation();
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerRef = useRef<any>(null);
  const placesServiceRef = useRef<any>(null);
  const dragIndicatorRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);
  const requestCountRef = useRef<number>(0);

  useImperativeHandle(ref, () => ({ input: null }), []);

  const [actualValue, setActualValue] = useState<LocationData | undefined>(
    value || defaultValue
  );

  const { isLoaded: isGoogleMapsLoaded, isLoading, error } = useGoogleMaps();

  // Zoom persistence management
  const zoomPersistence = useMapZoomPersistence({
    componentId: `map-only-${name || 'default'}`,
    defaultZoom: zoom,
    enablePersistence: true
  });

  // Update map location function (will be defined after createDraggableMarker)
  const updateMapLocationRef = useRef<((location: LocationData, forceStreetZoom?: boolean) => void) | null>(null);

  // Sync with external value changes (only when truly different and not being dragged)
  useEffect(() => {
    // Only update if the value is genuinely different (not just a reference change)
    const valueChanged = JSON.stringify(value) !== JSON.stringify(actualValue);
    if (valueChanged && !isDraggingRef.current) {
      setActualValue(value);
      if (value && mapInstanceRef.current && updateMapLocationRef.current) {
        updateMapLocationRef.current(value, false); // Don't force zoom on external value changes
      }
    }
  }, [value]);

  // Enhanced place details fetching
  const fetchPlaceDetails = useCallback(async (placeId: string): Promise<any> => {
    if (!validatePlaceDetails || !placesServiceRef.current) return null;

    return new Promise((resolve, reject) => {
      placesServiceRef.current.getDetails(
        {
          placeId,
          fields: [
            'place_id',
            'formatted_address',
            'geometry',
            'address_components',
            'name',
            'types',
            'business_status',
            'rating',
            'user_ratings_total',
            'website',
            'formatted_phone_number'
          ]
        },
        (place: any, status: any) => {
          if (status === (window as any).google.maps.places.PlacesServiceStatus.OK) {
            resolve(place);
          } else {
            reject(new Error(`Place details fetch failed: ${status}`));
          }
        }
      );
    });
  }, [validatePlaceDetails]);

  const updateDragIndicator = useCallback((isDragging: boolean) => {
    if (dragIndicatorRef.current) {
      const indicator = dragIndicatorRef.current;
      if (isDragging) {
        indicator.className = indicator.className.replace('text-muted-foreground', 'text-primary border-primary shadow-lg scale-105');
        indicator.innerHTML = `
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span class="font-medium">Adjusting location...</span>
          </div>
        `;
      } else {
        indicator.className = indicator.className.replace('text-primary border-primary shadow-lg scale-105', 'text-muted-foreground');
        indicator.innerHTML = `
          <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
            </svg>
            <span>Drag marker to adjust</span>
          </div>
        `;
      }
    }
  }, []);

  const createDraggableMarker = useCallback((position: { lat: number; lng: number }, title: string) => {
    if (!mapInstanceRef.current) return null;

    const markerOptions: any = {
      position,
      map: mapInstanceRef.current,
      title,
      animation: (window as any).google.maps.Animation.DROP,
      draggable: true,
      cursor: 'grab',
      optimized: false,
      zIndex: 1000,
    };

    // Add custom marker icon if provided
    if (customMarkerIcon) {
      markerOptions.icon = {
        url: customMarkerIcon,
        scaledSize: new (window as any).google.maps.Size(32, 32),
        anchor: new (window as any).google.maps.Point(16, 32),
      };
    } else if (markerColor !== "#EA4335") {
      // Use custom color for default marker
      markerOptions.icon = {
        path: (window as any).google.maps.SymbolPath.CIRCLE,
        fillColor: markerColor,
        fillOpacity: 1,
        strokeColor: '#FFFFFF',
        strokeWeight: 2,
        scale: 8,
      };
    }

    const marker = new (window as any).google.maps.Marker(markerOptions);

    // Add drag start listener - use direct DOM manipulation to avoid re-renders
    marker.addListener("dragstart", () => {
      isDraggingRef.current = true;
      marker.setAnimation(null);
      marker.setOpacity(0.8);
      marker.setCursor('grabbing');
      updateDragIndicator(true);
    });

    // Add hover effects
    marker.addListener("mouseover", () => {
      if (!isDraggingRef.current) {
        marker.setOpacity(0.9);
        marker.setCursor('grab');
      }
    });

    marker.addListener("mouseout", () => {
      if (!isDraggingRef.current) {
        marker.setOpacity(1.0);
      }
    });

    // Add drag end listener
    marker.addListener("dragend", (event: any) => {
      isDraggingRef.current = false;
      marker.setOpacity(1.0);
      marker.setCursor('grab');
      updateDragIndicator(false);
      
      if (event.latLng) {
        const lat = event.latLng.lat();
        const lng = event.latLng.lng();

        // Enhanced reverse geocoding
        if (enableReverseGeocoding) {
          const geocoder = new (window as any).google.maps.Geocoder();
          geocoder.geocode({
            location: { lat, lng },
            language: 'en'
          }, async (results: any, status: any) => {
            if (status === "OK" && results && results[0]) {
              // Use LocationHelper to extract enhanced address components
              let locationData = LocationHelper.extractAddressFromGeocodingResult(results[0], lat, lng);

              // Override placeId storage based on component setting
              if (!storePlaceId) {
                locationData.placeId = undefined;
              }

              // Fetch detailed place information if enabled and placeId is available
              if (validatePlaceDetails && results[0].place_id) {
                try {
                  const detailedPlace = await fetchPlaceDetails(results[0].place_id);
                  if (detailedPlace) {
                    locationData.name = detailedPlace.name;
                    locationData.types = detailedPlace.types;
                    locationData.businessStatus = detailedPlace.business_status;
                    locationData.rating = detailedPlace.rating;
                    locationData.website = detailedPlace.website;
                    locationData.phoneNumber = detailedPlace.formatted_phone_number;
                  }
                } catch (error) {
                  console.warn("Failed to fetch place details during reverse geocoding:", error);
                }
              }

              setActualValue(locationData);
              if (onChange) {
                onChange(locationData);
              }
            }
          });
        }
      }
    });

    return marker;
  }, [updateDragIndicator, onChange, customMarkerIcon, markerColor, enableReverseGeocoding, storePlaceId, validatePlaceDetails, fetchPlaceDetails]);

  const clearMarker = useCallback(() => {
    if (markerRef.current) {
      // Clear all event listeners
      (window as any).google?.maps?.event?.clearInstanceListeners(markerRef.current);
      // Remove marker from map
      markerRef.current.setMap(null);
      markerRef.current = null;
    }
    isDraggingRef.current = false;
    updateDragIndicator(false);
  }, [updateDragIndicator]);

  // Update map location function
  const updateMapLocation = useCallback((location: LocationData, forceStreetZoom: boolean = true) => {
    if (!mapInstanceRef.current) return;

    const position = { lat: location.lat, lng: location.lng };

    // Update map center
    mapInstanceRef.current.setCenter(position);

    // Only update zoom if user hasn't manually adjusted it or if we're forcing street zoom
    if (forceStreetZoom && !zoomPersistence.isUserAdjusted()) {
      const targetZoom = zoomPersistence.STREET_LEVEL_ZOOM;
      mapInstanceRef.current.setZoom(targetZoom);
      zoomPersistence.updateZoom(targetZoom);
    } else if (!zoomPersistence.isUserAdjusted()) {
      // Use current zoom or default to street level
      const targetZoom = zoomPersistence.getCurrentZoom() || zoomPersistence.STREET_LEVEL_ZOOM;
      mapInstanceRef.current.setZoom(targetZoom);
      zoomPersistence.updateZoom(targetZoom);
    }
  }, [zoomPersistence]);

  // Set the updateMapLocation function reference
  useEffect(() => {
    updateMapLocationRef.current = updateMapLocation;
  }, [updateMapLocation]);

  // Initialize map
  useEffect(() => {
    if (!isGoogleMapsLoaded || !mapRef.current) return;

    const defaultLocation = actualValue || { lat: 17.3850, lng: 78.4867 }; // Default to Hyderabad, India

    // Initialize zoom level with persistence
    const initialZoom = zoomPersistence.initializeZoom(!!actualValue);

    const mapOptions: any = {
      center: { lat: defaultLocation.lat, lng: defaultLocation.lng },
      zoom: initialZoom,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      zoomControl: true,
      gestureHandling: "cooperative",
      styles: [
        {
          featureType: "poi",
          elementType: "labels",
          stylers: [{ visibility: "off" }]
        }
      ]
    };

    // No map restrictions - allow global navigation

    const map = new (window as any).google.maps.Map(mapRef.current, mapOptions);

    // Initialize Places Service for place details
    if (validatePlaceDetails) {
      placesServiceRef.current = new (window as any).google.maps.places.PlacesService(map);
    }

    mapInstanceRef.current = map;

    // Add zoom change listener to track user adjustments
    map.addListener("zoom_changed", () => {
      const newZoom = map.getZoom();
      if (newZoom !== zoomPersistence.getCurrentZoom()) {
        zoomPersistence.updateZoom(newZoom, true); // Mark as user-adjusted
      }
    });

    // Add drag end listener for map to get center location
    map.addListener("dragend", () => {
      const center = map.getCenter();
      if (center) {
        const lat = center.lat();
        const lng = center.lng();

        // Reverse geocode to get address
        const geocoder = new (window as any).google.maps.Geocoder();
        geocoder.geocode({
          location: { lat, lng },
          language: 'en'
        }, async (results: any, status: any) => {
          if (status === "OK" && results && results[0]) {
            // Use LocationHelper to extract enhanced address components
            let locationData = LocationHelper.extractAddressFromGeocodingResult(results[0], lat, lng);

            // Override placeId storage based on component setting
            if (!storePlaceId) {
              locationData.placeId = undefined;
            }

            setActualValue(locationData);
            if (onChange) {
              onChange(locationData);
            }

            // Apply street-level zoom for new location selection via drag
            if (!zoomPersistence.isUserAdjusted()) {
              const streetZoom = zoomPersistence.STREET_LEVEL_ZOOM;
              map.setZoom(streetZoom);
              zoomPersistence.updateZoom(streetZoom);
            }
          }
        });
      }
    });

    // Set initial map center if value exists
    if (actualValue && updateMapLocationRef.current) {
      updateMapLocationRef.current(actualValue, false); // Don't force zoom on initial load
    }

    return () => {
      clearMarker();
      if (mapInstanceRef.current) {
        // Cleanup map event listeners
        (window as any).google?.maps?.event?.clearInstanceListeners(mapInstanceRef.current);
      }
    };
  }, [isGoogleMapsLoaded, onChange, actualValue, zoom, validatePlaceDetails, storePlaceId, createDraggableMarker, clearMarker]);

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <div className="flex justify-between space-x-2 items-center mb-1">
          <label htmlFor={name} className="mb-1 flex justify-between space-x-2 truncate text-xs font-medium">
            {title}
            {required && <span className="ml-1 text-red-500">*</span>}
          </label>

          <div className="flex items-center space-x-1">
            {hint && <HintTooltip text={hint} />}
          </div>
        </div>
      )}

      {/* Hidden input for form submission - JSON data */}
      {name && (
        <input
          type="hidden"
          name={name}
          value={actualValue ? JSON.stringify(actualValue) : ""}
          required={required}
        />
      )}

      {/* Map Container */}
      <div className="relative mt-3">
        <div
          ref={mapRef}
          style={{ height: mapHeight }}
          className="w-full rounded-xl border border-gray-200 bg-gray-50 shadow-sm overflow-hidden"
        />
        {(!isGoogleMapsLoaded || isLoading) && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm rounded-xl">
            <div className="flex flex-col items-center space-y-3">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <div className="absolute inset-0 h-8 w-8 rounded-full border-2 border-primary/20"></div>
              </div>
              <span className="text-sm font-medium text-gray-700">Loading interactive map...</span>
            </div>
          </div>
        )}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/95 backdrop-blur-sm rounded-xl">
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-red-50 rounded-full flex items-center justify-center">
                <MapPin className="w-8 h-8 text-red-500" />
              </div>
              <p className="text-sm font-semibold text-red-800 mb-2">Map unavailable</p>
              <p className="text-xs text-red-600 max-w-xs">{error}</p>
            </div>
          </div>
        )}

        {isGoogleMapsLoaded && !error && (
          <>
            <div className="absolute top-3 left-3 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-600 border border-gray-200 shadow-sm">
              <div className="flex items-center space-x-1">
                <Move className="w-3 h-3" />
                <span>Drag map to select location</span>
              </div>
            </div>

            {/* Fixed center pointer - Red pin */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-10">
              <div className="relative">
                <MapPin className="w-8 h-8 text-red-500 drop-shadow-lg fill-red-500" />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-white rounded-full border border-red-600"></div>
              </div>
            </div>
          </>
        )}






      </div>

      {help && (
        <div className="mt-1">
          <div className="text-xs text-muted-foreground">{help}</div>
        </div>
      )}
    </div>
  );
};

export default forwardRef(MapOnlyInput);

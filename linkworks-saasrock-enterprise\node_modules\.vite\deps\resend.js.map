{"version": 3, "sources": ["../../js-beautify/js/src/core/output.js", "../../js-beautify/js/src/core/token.js", "../../js-beautify/js/src/javascript/acorn.js", "../../js-beautify/js/src/core/options.js", "../../js-beautify/js/src/javascript/options.js", "../../js-beautify/js/src/core/inputscanner.js", "../../js-beautify/js/src/core/tokenstream.js", "../../js-beautify/js/src/core/pattern.js", "../../js-beautify/js/src/core/whitespacepattern.js", "../../js-beautify/js/src/core/tokenizer.js", "../../js-beautify/js/src/core/directives.js", "../../js-beautify/js/src/core/templatablepattern.js", "../../js-beautify/js/src/javascript/tokenizer.js", "../../js-beautify/js/src/javascript/beautifier.js", "../../js-beautify/js/src/javascript/index.js", "../../js-beautify/js/src/css/options.js", "../../js-beautify/js/src/css/beautifier.js", "../../js-beautify/js/src/css/index.js", "../../js-beautify/js/src/html/options.js", "../../js-beautify/js/src/html/tokenizer.js", "../../js-beautify/js/src/html/beautifier.js", "../../js-beautify/js/src/html/index.js", "../../js-beautify/js/src/index.js", "../../js-beautify/js/index.js", "../../is-whitespace/index.js", "../../is-extendable/index.js", "../../extend-shallow/index.js", "../../is-buffer/index.js", "../../kind-of/index.js", "../../condense-newlines/index.js", "../../pretty/index.js", "../../@react-email/render/dist/index.mjs", "../../resend/dist/index.mjs"], "sourcesContent": ["/*jshint node:true */\n/*\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPY<PERSON>GHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nfunction OutputLine(parent) {\n  this.__parent = parent;\n  this.__character_count = 0;\n  // use indent_count as a marker for this.__lines that have preserved indentation\n  this.__indent_count = -1;\n  this.__alignment_count = 0;\n  this.__wrap_point_index = 0;\n  this.__wrap_point_character_count = 0;\n  this.__wrap_point_indent_count = -1;\n  this.__wrap_point_alignment_count = 0;\n\n  this.__items = [];\n}\n\nOutputLine.prototype.clone_empty = function() {\n  var line = new OutputLine(this.__parent);\n  line.set_indent(this.__indent_count, this.__alignment_count);\n  return line;\n};\n\nOutputLine.prototype.item = function(index) {\n  if (index < 0) {\n    return this.__items[this.__items.length + index];\n  } else {\n    return this.__items[index];\n  }\n};\n\nOutputLine.prototype.has_match = function(pattern) {\n  for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n    if (this.__items[lastCheckedOutput].match(pattern)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nOutputLine.prototype.set_indent = function(indent, alignment) {\n  if (this.is_empty()) {\n    this.__indent_count = indent || 0;\n    this.__alignment_count = alignment || 0;\n    this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n  }\n};\n\nOutputLine.prototype._set_wrap_point = function() {\n  if (this.__parent.wrap_line_length) {\n    this.__wrap_point_index = this.__items.length;\n    this.__wrap_point_character_count = this.__character_count;\n    this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n    this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n  }\n};\n\nOutputLine.prototype._should_wrap = function() {\n  return this.__wrap_point_index &&\n    this.__character_count > this.__parent.wrap_line_length &&\n    this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n};\n\nOutputLine.prototype._allow_wrap = function() {\n  if (this._should_wrap()) {\n    this.__parent.add_new_line();\n    var next = this.__parent.current_line;\n    next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n    next.__items = this.__items.slice(this.__wrap_point_index);\n    this.__items = this.__items.slice(0, this.__wrap_point_index);\n\n    next.__character_count += this.__character_count - this.__wrap_point_character_count;\n    this.__character_count = this.__wrap_point_character_count;\n\n    if (next.__items[0] === \" \") {\n      next.__items.splice(0, 1);\n      next.__character_count -= 1;\n    }\n    return true;\n  }\n  return false;\n};\n\nOutputLine.prototype.is_empty = function() {\n  return this.__items.length === 0;\n};\n\nOutputLine.prototype.last = function() {\n  if (!this.is_empty()) {\n    return this.__items[this.__items.length - 1];\n  } else {\n    return null;\n  }\n};\n\nOutputLine.prototype.push = function(item) {\n  this.__items.push(item);\n  var last_newline_index = item.lastIndexOf('\\n');\n  if (last_newline_index !== -1) {\n    this.__character_count = item.length - last_newline_index;\n  } else {\n    this.__character_count += item.length;\n  }\n};\n\nOutputLine.prototype.pop = function() {\n  var item = null;\n  if (!this.is_empty()) {\n    item = this.__items.pop();\n    this.__character_count -= item.length;\n  }\n  return item;\n};\n\n\nOutputLine.prototype._remove_indent = function() {\n  if (this.__indent_count > 0) {\n    this.__indent_count -= 1;\n    this.__character_count -= this.__parent.indent_size;\n  }\n};\n\nOutputLine.prototype._remove_wrap_indent = function() {\n  if (this.__wrap_point_indent_count > 0) {\n    this.__wrap_point_indent_count -= 1;\n  }\n};\nOutputLine.prototype.trim = function() {\n  while (this.last() === ' ') {\n    this.__items.pop();\n    this.__character_count -= 1;\n  }\n};\n\nOutputLine.prototype.toString = function() {\n  var result = '';\n  if (this.is_empty()) {\n    if (this.__parent.indent_empty_lines) {\n      result = this.__parent.get_indent_string(this.__indent_count);\n    }\n  } else {\n    result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n    result += this.__items.join('');\n  }\n  return result;\n};\n\nfunction IndentStringCache(options, baseIndentString) {\n  this.__cache = [''];\n  this.__indent_size = options.indent_size;\n  this.__indent_string = options.indent_char;\n  if (!options.indent_with_tabs) {\n    this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n  }\n\n  // Set to null to continue support for auto detection of base indent\n  baseIndentString = baseIndentString || '';\n  if (options.indent_level > 0) {\n    baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n  }\n\n  this.__base_string = baseIndentString;\n  this.__base_string_length = baseIndentString.length;\n}\n\nIndentStringCache.prototype.get_indent_size = function(indent, column) {\n  var result = this.__base_string_length;\n  column = column || 0;\n  if (indent < 0) {\n    result = 0;\n  }\n  result += indent * this.__indent_size;\n  result += column;\n  return result;\n};\n\nIndentStringCache.prototype.get_indent_string = function(indent_level, column) {\n  var result = this.__base_string;\n  column = column || 0;\n  if (indent_level < 0) {\n    indent_level = 0;\n    result = '';\n  }\n  column += indent_level * this.__indent_size;\n  this.__ensure_cache(column);\n  result += this.__cache[column];\n  return result;\n};\n\nIndentStringCache.prototype.__ensure_cache = function(column) {\n  while (column >= this.__cache.length) {\n    this.__add_column();\n  }\n};\n\nIndentStringCache.prototype.__add_column = function() {\n  var column = this.__cache.length;\n  var indent = 0;\n  var result = '';\n  if (this.__indent_size && column >= this.__indent_size) {\n    indent = Math.floor(column / this.__indent_size);\n    column -= indent * this.__indent_size;\n    result = new Array(indent + 1).join(this.__indent_string);\n  }\n  if (column) {\n    result += new Array(column + 1).join(' ');\n  }\n\n  this.__cache.push(result);\n};\n\nfunction Output(options, baseIndentString) {\n  this.__indent_cache = new IndentStringCache(options, baseIndentString);\n  this.raw = false;\n  this._end_with_newline = options.end_with_newline;\n  this.indent_size = options.indent_size;\n  this.wrap_line_length = options.wrap_line_length;\n  this.indent_empty_lines = options.indent_empty_lines;\n  this.__lines = [];\n  this.previous_line = null;\n  this.current_line = null;\n  this.next_line = new OutputLine(this);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n  // initialize\n  this.__add_outputline();\n}\n\nOutput.prototype.__add_outputline = function() {\n  this.previous_line = this.current_line;\n  this.current_line = this.next_line.clone_empty();\n  this.__lines.push(this.current_line);\n};\n\nOutput.prototype.get_line_number = function() {\n  return this.__lines.length;\n};\n\nOutput.prototype.get_indent_string = function(indent, column) {\n  return this.__indent_cache.get_indent_string(indent, column);\n};\n\nOutput.prototype.get_indent_size = function(indent, column) {\n  return this.__indent_cache.get_indent_size(indent, column);\n};\n\nOutput.prototype.is_empty = function() {\n  return !this.previous_line && this.current_line.is_empty();\n};\n\nOutput.prototype.add_new_line = function(force_newline) {\n  // never newline at the start of file\n  // otherwise, newline only if we didn't just add one or we're forced\n  if (this.is_empty() ||\n    (!force_newline && this.just_added_newline())) {\n    return false;\n  }\n\n  // if raw output is enabled, don't print additional newlines,\n  // but still return True as though you had\n  if (!this.raw) {\n    this.__add_outputline();\n  }\n  return true;\n};\n\nOutput.prototype.get_code = function(eol) {\n  this.trim(true);\n\n  // handle some edge cases where the last tokens\n  // has text that ends with newline(s)\n  var last_item = this.current_line.pop();\n  if (last_item) {\n    if (last_item[last_item.length - 1] === '\\n') {\n      last_item = last_item.replace(/\\n+$/g, '');\n    }\n    this.current_line.push(last_item);\n  }\n\n  if (this._end_with_newline) {\n    this.__add_outputline();\n  }\n\n  var sweet_code = this.__lines.join('\\n');\n\n  if (eol !== '\\n') {\n    sweet_code = sweet_code.replace(/[\\n]/g, eol);\n  }\n  return sweet_code;\n};\n\nOutput.prototype.set_wrap_point = function() {\n  this.current_line._set_wrap_point();\n};\n\nOutput.prototype.set_indent = function(indent, alignment) {\n  indent = indent || 0;\n  alignment = alignment || 0;\n\n  // Next line stores alignment values\n  this.next_line.set_indent(indent, alignment);\n\n  // Never indent your first output indent at the start of the file\n  if (this.__lines.length > 1) {\n    this.current_line.set_indent(indent, alignment);\n    return true;\n  }\n\n  this.current_line.set_indent();\n  return false;\n};\n\nOutput.prototype.add_raw_token = function(token) {\n  for (var x = 0; x < token.newlines; x++) {\n    this.__add_outputline();\n  }\n  this.current_line.set_indent(-1);\n  this.current_line.push(token.whitespace_before);\n  this.current_line.push(token.text);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n};\n\nOutput.prototype.add_token = function(printable_token) {\n  this.__add_space_before_token();\n  this.current_line.push(printable_token);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = this.current_line._allow_wrap();\n};\n\nOutput.prototype.__add_space_before_token = function() {\n  if (this.space_before_token && !this.just_added_newline()) {\n    if (!this.non_breaking_space) {\n      this.set_wrap_point();\n    }\n    this.current_line.push(' ');\n  }\n};\n\nOutput.prototype.remove_indent = function(index) {\n  var output_length = this.__lines.length;\n  while (index < output_length) {\n    this.__lines[index]._remove_indent();\n    index++;\n  }\n  this.current_line._remove_wrap_indent();\n};\n\nOutput.prototype.trim = function(eat_newlines) {\n  eat_newlines = (eat_newlines === undefined) ? false : eat_newlines;\n\n  this.current_line.trim();\n\n  while (eat_newlines && this.__lines.length > 1 &&\n    this.current_line.is_empty()) {\n    this.__lines.pop();\n    this.current_line = this.__lines[this.__lines.length - 1];\n    this.current_line.trim();\n  }\n\n  this.previous_line = this.__lines.length > 1 ?\n    this.__lines[this.__lines.length - 2] : null;\n};\n\nOutput.prototype.just_added_newline = function() {\n  return this.current_line.is_empty();\n};\n\nOutput.prototype.just_added_blankline = function() {\n  return this.is_empty() ||\n    (this.current_line.is_empty() && this.previous_line.is_empty());\n};\n\nOutput.prototype.ensure_empty_line_above = function(starts_with, ends_with) {\n  var index = this.__lines.length - 2;\n  while (index >= 0) {\n    var potentialEmptyLine = this.__lines[index];\n    if (potentialEmptyLine.is_empty()) {\n      break;\n    } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 &&\n      potentialEmptyLine.item(-1) !== ends_with) {\n      this.__lines.splice(index + 1, 0, new OutputLine(this));\n      this.previous_line = this.__lines[this.__lines.length - 2];\n      break;\n    }\n    index--;\n  }\n};\n\nmodule.exports.Output = Output;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nfunction Token(type, text, newlines, whitespace_before) {\n  this.type = type;\n  this.text = text;\n\n  // comments_before are\n  // comments that have a new line before them\n  // and may or may not have a newline after\n  // this is a set of comments before\n  this.comments_before = null; /* inline comment*/\n\n\n  // this.comments_after =  new TokenStream(); // no new line before and newline after\n  this.newlines = newlines || 0;\n  this.whitespace_before = whitespace_before || '';\n  this.parent = null;\n  this.next = null;\n  this.previous = null;\n  this.opened = null;\n  this.closed = null;\n  this.directives = null;\n}\n\n\nmodule.exports.Token = Token;\n", "/* jshint node: true, curly: false */\n// Parts of this section of code is taken from acorn.\n//\n// Acorn was written by <PERSON><PERSON> and released under an MIT\n// license. The Unicode regexps (for identifiers and whitespace) were\n// taken from [Esprima](http://esprima.org) by Ariya Hidayat.\n//\n// Git repositories for Acorn are available at\n//\n//     http://marijnhaverbeke.nl/git/acorn\n//     https://github.com/marijnh/acorn.git\n\n// ## Character categories\n\n\n'use strict';\n\n// acorn used char codes to squeeze the last bit of performance out\n// Beautifier is okay without that, so we're using regex\n// permit # (23), $ (36), and @ (64). @ is used in ES7 decorators.\n// 65 through 91 are uppercase letters.\n// permit _ (95).\n// 97 through 123 are lowercase letters.\nvar baseASCIIidentifierStartChars = \"\\\\x23\\\\x24\\\\x40\\\\x41-\\\\x5a\\\\x5f\\\\x61-\\\\x7a\";\n\n// inside an identifier @ is not allowed but 0-9 are.\nvar baseASCIIidentifierChars = \"\\\\x24\\\\x30-\\\\x39\\\\x41-\\\\x5a\\\\x5f\\\\x61-\\\\x7a\";\n\n// Big ugly regular expressions that match characters in the\n// whitespace, identifier, and identifier-start categories. These\n// are only applied when a character is found to actually have a\n// code point above 128.\nvar nonASCIIidentifierStartChars = \"\\\\xaa\\\\xb5\\\\xba\\\\xc0-\\\\xd6\\\\xd8-\\\\xf6\\\\xf8-\\\\u02c1\\\\u02c6-\\\\u02d1\\\\u02e0-\\\\u02e4\\\\u02ec\\\\u02ee\\\\u0370-\\\\u0374\\\\u0376\\\\u0377\\\\u037a-\\\\u037d\\\\u0386\\\\u0388-\\\\u038a\\\\u038c\\\\u038e-\\\\u03a1\\\\u03a3-\\\\u03f5\\\\u03f7-\\\\u0481\\\\u048a-\\\\u0527\\\\u0531-\\\\u0556\\\\u0559\\\\u0561-\\\\u0587\\\\u05d0-\\\\u05ea\\\\u05f0-\\\\u05f2\\\\u0620-\\\\u064a\\\\u066e\\\\u066f\\\\u0671-\\\\u06d3\\\\u06d5\\\\u06e5\\\\u06e6\\\\u06ee\\\\u06ef\\\\u06fa-\\\\u06fc\\\\u06ff\\\\u0710\\\\u0712-\\\\u072f\\\\u074d-\\\\u07a5\\\\u07b1\\\\u07ca-\\\\u07ea\\\\u07f4\\\\u07f5\\\\u07fa\\\\u0800-\\\\u0815\\\\u081a\\\\u0824\\\\u0828\\\\u0840-\\\\u0858\\\\u08a0\\\\u08a2-\\\\u08ac\\\\u0904-\\\\u0939\\\\u093d\\\\u0950\\\\u0958-\\\\u0961\\\\u0971-\\\\u0977\\\\u0979-\\\\u097f\\\\u0985-\\\\u098c\\\\u098f\\\\u0990\\\\u0993-\\\\u09a8\\\\u09aa-\\\\u09b0\\\\u09b2\\\\u09b6-\\\\u09b9\\\\u09bd\\\\u09ce\\\\u09dc\\\\u09dd\\\\u09df-\\\\u09e1\\\\u09f0\\\\u09f1\\\\u0a05-\\\\u0a0a\\\\u0a0f\\\\u0a10\\\\u0a13-\\\\u0a28\\\\u0a2a-\\\\u0a30\\\\u0a32\\\\u0a33\\\\u0a35\\\\u0a36\\\\u0a38\\\\u0a39\\\\u0a59-\\\\u0a5c\\\\u0a5e\\\\u0a72-\\\\u0a74\\\\u0a85-\\\\u0a8d\\\\u0a8f-\\\\u0a91\\\\u0a93-\\\\u0aa8\\\\u0aaa-\\\\u0ab0\\\\u0ab2\\\\u0ab3\\\\u0ab5-\\\\u0ab9\\\\u0abd\\\\u0ad0\\\\u0ae0\\\\u0ae1\\\\u0b05-\\\\u0b0c\\\\u0b0f\\\\u0b10\\\\u0b13-\\\\u0b28\\\\u0b2a-\\\\u0b30\\\\u0b32\\\\u0b33\\\\u0b35-\\\\u0b39\\\\u0b3d\\\\u0b5c\\\\u0b5d\\\\u0b5f-\\\\u0b61\\\\u0b71\\\\u0b83\\\\u0b85-\\\\u0b8a\\\\u0b8e-\\\\u0b90\\\\u0b92-\\\\u0b95\\\\u0b99\\\\u0b9a\\\\u0b9c\\\\u0b9e\\\\u0b9f\\\\u0ba3\\\\u0ba4\\\\u0ba8-\\\\u0baa\\\\u0bae-\\\\u0bb9\\\\u0bd0\\\\u0c05-\\\\u0c0c\\\\u0c0e-\\\\u0c10\\\\u0c12-\\\\u0c28\\\\u0c2a-\\\\u0c33\\\\u0c35-\\\\u0c39\\\\u0c3d\\\\u0c58\\\\u0c59\\\\u0c60\\\\u0c61\\\\u0c85-\\\\u0c8c\\\\u0c8e-\\\\u0c90\\\\u0c92-\\\\u0ca8\\\\u0caa-\\\\u0cb3\\\\u0cb5-\\\\u0cb9\\\\u0cbd\\\\u0cde\\\\u0ce0\\\\u0ce1\\\\u0cf1\\\\u0cf2\\\\u0d05-\\\\u0d0c\\\\u0d0e-\\\\u0d10\\\\u0d12-\\\\u0d3a\\\\u0d3d\\\\u0d4e\\\\u0d60\\\\u0d61\\\\u0d7a-\\\\u0d7f\\\\u0d85-\\\\u0d96\\\\u0d9a-\\\\u0db1\\\\u0db3-\\\\u0dbb\\\\u0dbd\\\\u0dc0-\\\\u0dc6\\\\u0e01-\\\\u0e30\\\\u0e32\\\\u0e33\\\\u0e40-\\\\u0e46\\\\u0e81\\\\u0e82\\\\u0e84\\\\u0e87\\\\u0e88\\\\u0e8a\\\\u0e8d\\\\u0e94-\\\\u0e97\\\\u0e99-\\\\u0e9f\\\\u0ea1-\\\\u0ea3\\\\u0ea5\\\\u0ea7\\\\u0eaa\\\\u0eab\\\\u0ead-\\\\u0eb0\\\\u0eb2\\\\u0eb3\\\\u0ebd\\\\u0ec0-\\\\u0ec4\\\\u0ec6\\\\u0edc-\\\\u0edf\\\\u0f00\\\\u0f40-\\\\u0f47\\\\u0f49-\\\\u0f6c\\\\u0f88-\\\\u0f8c\\\\u1000-\\\\u102a\\\\u103f\\\\u1050-\\\\u1055\\\\u105a-\\\\u105d\\\\u1061\\\\u1065\\\\u1066\\\\u106e-\\\\u1070\\\\u1075-\\\\u1081\\\\u108e\\\\u10a0-\\\\u10c5\\\\u10c7\\\\u10cd\\\\u10d0-\\\\u10fa\\\\u10fc-\\\\u1248\\\\u124a-\\\\u124d\\\\u1250-\\\\u1256\\\\u1258\\\\u125a-\\\\u125d\\\\u1260-\\\\u1288\\\\u128a-\\\\u128d\\\\u1290-\\\\u12b0\\\\u12b2-\\\\u12b5\\\\u12b8-\\\\u12be\\\\u12c0\\\\u12c2-\\\\u12c5\\\\u12c8-\\\\u12d6\\\\u12d8-\\\\u1310\\\\u1312-\\\\u1315\\\\u1318-\\\\u135a\\\\u1380-\\\\u138f\\\\u13a0-\\\\u13f4\\\\u1401-\\\\u166c\\\\u166f-\\\\u167f\\\\u1681-\\\\u169a\\\\u16a0-\\\\u16ea\\\\u16ee-\\\\u16f0\\\\u1700-\\\\u170c\\\\u170e-\\\\u1711\\\\u1720-\\\\u1731\\\\u1740-\\\\u1751\\\\u1760-\\\\u176c\\\\u176e-\\\\u1770\\\\u1780-\\\\u17b3\\\\u17d7\\\\u17dc\\\\u1820-\\\\u1877\\\\u1880-\\\\u18a8\\\\u18aa\\\\u18b0-\\\\u18f5\\\\u1900-\\\\u191c\\\\u1950-\\\\u196d\\\\u1970-\\\\u1974\\\\u1980-\\\\u19ab\\\\u19c1-\\\\u19c7\\\\u1a00-\\\\u1a16\\\\u1a20-\\\\u1a54\\\\u1aa7\\\\u1b05-\\\\u1b33\\\\u1b45-\\\\u1b4b\\\\u1b83-\\\\u1ba0\\\\u1bae\\\\u1baf\\\\u1bba-\\\\u1be5\\\\u1c00-\\\\u1c23\\\\u1c4d-\\\\u1c4f\\\\u1c5a-\\\\u1c7d\\\\u1ce9-\\\\u1cec\\\\u1cee-\\\\u1cf1\\\\u1cf5\\\\u1cf6\\\\u1d00-\\\\u1dbf\\\\u1e00-\\\\u1f15\\\\u1f18-\\\\u1f1d\\\\u1f20-\\\\u1f45\\\\u1f48-\\\\u1f4d\\\\u1f50-\\\\u1f57\\\\u1f59\\\\u1f5b\\\\u1f5d\\\\u1f5f-\\\\u1f7d\\\\u1f80-\\\\u1fb4\\\\u1fb6-\\\\u1fbc\\\\u1fbe\\\\u1fc2-\\\\u1fc4\\\\u1fc6-\\\\u1fcc\\\\u1fd0-\\\\u1fd3\\\\u1fd6-\\\\u1fdb\\\\u1fe0-\\\\u1fec\\\\u1ff2-\\\\u1ff4\\\\u1ff6-\\\\u1ffc\\\\u2071\\\\u207f\\\\u2090-\\\\u209c\\\\u2102\\\\u2107\\\\u210a-\\\\u2113\\\\u2115\\\\u2119-\\\\u211d\\\\u2124\\\\u2126\\\\u2128\\\\u212a-\\\\u212d\\\\u212f-\\\\u2139\\\\u213c-\\\\u213f\\\\u2145-\\\\u2149\\\\u214e\\\\u2160-\\\\u2188\\\\u2c00-\\\\u2c2e\\\\u2c30-\\\\u2c5e\\\\u2c60-\\\\u2ce4\\\\u2ceb-\\\\u2cee\\\\u2cf2\\\\u2cf3\\\\u2d00-\\\\u2d25\\\\u2d27\\\\u2d2d\\\\u2d30-\\\\u2d67\\\\u2d6f\\\\u2d80-\\\\u2d96\\\\u2da0-\\\\u2da6\\\\u2da8-\\\\u2dae\\\\u2db0-\\\\u2db6\\\\u2db8-\\\\u2dbe\\\\u2dc0-\\\\u2dc6\\\\u2dc8-\\\\u2dce\\\\u2dd0-\\\\u2dd6\\\\u2dd8-\\\\u2dde\\\\u2e2f\\\\u3005-\\\\u3007\\\\u3021-\\\\u3029\\\\u3031-\\\\u3035\\\\u3038-\\\\u303c\\\\u3041-\\\\u3096\\\\u309d-\\\\u309f\\\\u30a1-\\\\u30fa\\\\u30fc-\\\\u30ff\\\\u3105-\\\\u312d\\\\u3131-\\\\u318e\\\\u31a0-\\\\u31ba\\\\u31f0-\\\\u31ff\\\\u3400-\\\\u4db5\\\\u4e00-\\\\u9fcc\\\\ua000-\\\\ua48c\\\\ua4d0-\\\\ua4fd\\\\ua500-\\\\ua60c\\\\ua610-\\\\ua61f\\\\ua62a\\\\ua62b\\\\ua640-\\\\ua66e\\\\ua67f-\\\\ua697\\\\ua6a0-\\\\ua6ef\\\\ua717-\\\\ua71f\\\\ua722-\\\\ua788\\\\ua78b-\\\\ua78e\\\\ua790-\\\\ua793\\\\ua7a0-\\\\ua7aa\\\\ua7f8-\\\\ua801\\\\ua803-\\\\ua805\\\\ua807-\\\\ua80a\\\\ua80c-\\\\ua822\\\\ua840-\\\\ua873\\\\ua882-\\\\ua8b3\\\\ua8f2-\\\\ua8f7\\\\ua8fb\\\\ua90a-\\\\ua925\\\\ua930-\\\\ua946\\\\ua960-\\\\ua97c\\\\ua984-\\\\ua9b2\\\\ua9cf\\\\uaa00-\\\\uaa28\\\\uaa40-\\\\uaa42\\\\uaa44-\\\\uaa4b\\\\uaa60-\\\\uaa76\\\\uaa7a\\\\uaa80-\\\\uaaaf\\\\uaab1\\\\uaab5\\\\uaab6\\\\uaab9-\\\\uaabd\\\\uaac0\\\\uaac2\\\\uaadb-\\\\uaadd\\\\uaae0-\\\\uaaea\\\\uaaf2-\\\\uaaf4\\\\uab01-\\\\uab06\\\\uab09-\\\\uab0e\\\\uab11-\\\\uab16\\\\uab20-\\\\uab26\\\\uab28-\\\\uab2e\\\\uabc0-\\\\uabe2\\\\uac00-\\\\ud7a3\\\\ud7b0-\\\\ud7c6\\\\ud7cb-\\\\ud7fb\\\\uf900-\\\\ufa6d\\\\ufa70-\\\\ufad9\\\\ufb00-\\\\ufb06\\\\ufb13-\\\\ufb17\\\\ufb1d\\\\ufb1f-\\\\ufb28\\\\ufb2a-\\\\ufb36\\\\ufb38-\\\\ufb3c\\\\ufb3e\\\\ufb40\\\\ufb41\\\\ufb43\\\\ufb44\\\\ufb46-\\\\ufbb1\\\\ufbd3-\\\\ufd3d\\\\ufd50-\\\\ufd8f\\\\ufd92-\\\\ufdc7\\\\ufdf0-\\\\ufdfb\\\\ufe70-\\\\ufe74\\\\ufe76-\\\\ufefc\\\\uff21-\\\\uff3a\\\\uff41-\\\\uff5a\\\\uff66-\\\\uffbe\\\\uffc2-\\\\uffc7\\\\uffca-\\\\uffcf\\\\uffd2-\\\\uffd7\\\\uffda-\\\\uffdc\";\nvar nonASCIIidentifierChars = \"\\\\u0300-\\\\u036f\\\\u0483-\\\\u0487\\\\u0591-\\\\u05bd\\\\u05bf\\\\u05c1\\\\u05c2\\\\u05c4\\\\u05c5\\\\u05c7\\\\u0610-\\\\u061a\\\\u0620-\\\\u0649\\\\u0672-\\\\u06d3\\\\u06e7-\\\\u06e8\\\\u06fb-\\\\u06fc\\\\u0730-\\\\u074a\\\\u0800-\\\\u0814\\\\u081b-\\\\u0823\\\\u0825-\\\\u0827\\\\u0829-\\\\u082d\\\\u0840-\\\\u0857\\\\u08e4-\\\\u08fe\\\\u0900-\\\\u0903\\\\u093a-\\\\u093c\\\\u093e-\\\\u094f\\\\u0951-\\\\u0957\\\\u0962-\\\\u0963\\\\u0966-\\\\u096f\\\\u0981-\\\\u0983\\\\u09bc\\\\u09be-\\\\u09c4\\\\u09c7\\\\u09c8\\\\u09d7\\\\u09df-\\\\u09e0\\\\u0a01-\\\\u0a03\\\\u0a3c\\\\u0a3e-\\\\u0a42\\\\u0a47\\\\u0a48\\\\u0a4b-\\\\u0a4d\\\\u0a51\\\\u0a66-\\\\u0a71\\\\u0a75\\\\u0a81-\\\\u0a83\\\\u0abc\\\\u0abe-\\\\u0ac5\\\\u0ac7-\\\\u0ac9\\\\u0acb-\\\\u0acd\\\\u0ae2-\\\\u0ae3\\\\u0ae6-\\\\u0aef\\\\u0b01-\\\\u0b03\\\\u0b3c\\\\u0b3e-\\\\u0b44\\\\u0b47\\\\u0b48\\\\u0b4b-\\\\u0b4d\\\\u0b56\\\\u0b57\\\\u0b5f-\\\\u0b60\\\\u0b66-\\\\u0b6f\\\\u0b82\\\\u0bbe-\\\\u0bc2\\\\u0bc6-\\\\u0bc8\\\\u0bca-\\\\u0bcd\\\\u0bd7\\\\u0be6-\\\\u0bef\\\\u0c01-\\\\u0c03\\\\u0c46-\\\\u0c48\\\\u0c4a-\\\\u0c4d\\\\u0c55\\\\u0c56\\\\u0c62-\\\\u0c63\\\\u0c66-\\\\u0c6f\\\\u0c82\\\\u0c83\\\\u0cbc\\\\u0cbe-\\\\u0cc4\\\\u0cc6-\\\\u0cc8\\\\u0cca-\\\\u0ccd\\\\u0cd5\\\\u0cd6\\\\u0ce2-\\\\u0ce3\\\\u0ce6-\\\\u0cef\\\\u0d02\\\\u0d03\\\\u0d46-\\\\u0d48\\\\u0d57\\\\u0d62-\\\\u0d63\\\\u0d66-\\\\u0d6f\\\\u0d82\\\\u0d83\\\\u0dca\\\\u0dcf-\\\\u0dd4\\\\u0dd6\\\\u0dd8-\\\\u0ddf\\\\u0df2\\\\u0df3\\\\u0e34-\\\\u0e3a\\\\u0e40-\\\\u0e45\\\\u0e50-\\\\u0e59\\\\u0eb4-\\\\u0eb9\\\\u0ec8-\\\\u0ecd\\\\u0ed0-\\\\u0ed9\\\\u0f18\\\\u0f19\\\\u0f20-\\\\u0f29\\\\u0f35\\\\u0f37\\\\u0f39\\\\u0f41-\\\\u0f47\\\\u0f71-\\\\u0f84\\\\u0f86-\\\\u0f87\\\\u0f8d-\\\\u0f97\\\\u0f99-\\\\u0fbc\\\\u0fc6\\\\u1000-\\\\u1029\\\\u1040-\\\\u1049\\\\u1067-\\\\u106d\\\\u1071-\\\\u1074\\\\u1082-\\\\u108d\\\\u108f-\\\\u109d\\\\u135d-\\\\u135f\\\\u170e-\\\\u1710\\\\u1720-\\\\u1730\\\\u1740-\\\\u1750\\\\u1772\\\\u1773\\\\u1780-\\\\u17b2\\\\u17dd\\\\u17e0-\\\\u17e9\\\\u180b-\\\\u180d\\\\u1810-\\\\u1819\\\\u1920-\\\\u192b\\\\u1930-\\\\u193b\\\\u1951-\\\\u196d\\\\u19b0-\\\\u19c0\\\\u19c8-\\\\u19c9\\\\u19d0-\\\\u19d9\\\\u1a00-\\\\u1a15\\\\u1a20-\\\\u1a53\\\\u1a60-\\\\u1a7c\\\\u1a7f-\\\\u1a89\\\\u1a90-\\\\u1a99\\\\u1b46-\\\\u1b4b\\\\u1b50-\\\\u1b59\\\\u1b6b-\\\\u1b73\\\\u1bb0-\\\\u1bb9\\\\u1be6-\\\\u1bf3\\\\u1c00-\\\\u1c22\\\\u1c40-\\\\u1c49\\\\u1c5b-\\\\u1c7d\\\\u1cd0-\\\\u1cd2\\\\u1d00-\\\\u1dbe\\\\u1e01-\\\\u1f15\\\\u200c\\\\u200d\\\\u203f\\\\u2040\\\\u2054\\\\u20d0-\\\\u20dc\\\\u20e1\\\\u20e5-\\\\u20f0\\\\u2d81-\\\\u2d96\\\\u2de0-\\\\u2dff\\\\u3021-\\\\u3028\\\\u3099\\\\u309a\\\\ua640-\\\\ua66d\\\\ua674-\\\\ua67d\\\\ua69f\\\\ua6f0-\\\\ua6f1\\\\ua7f8-\\\\ua800\\\\ua806\\\\ua80b\\\\ua823-\\\\ua827\\\\ua880-\\\\ua881\\\\ua8b4-\\\\ua8c4\\\\ua8d0-\\\\ua8d9\\\\ua8f3-\\\\ua8f7\\\\ua900-\\\\ua909\\\\ua926-\\\\ua92d\\\\ua930-\\\\ua945\\\\ua980-\\\\ua983\\\\ua9b3-\\\\ua9c0\\\\uaa00-\\\\uaa27\\\\uaa40-\\\\uaa41\\\\uaa4c-\\\\uaa4d\\\\uaa50-\\\\uaa59\\\\uaa7b\\\\uaae0-\\\\uaae9\\\\uaaf2-\\\\uaaf3\\\\uabc0-\\\\uabe1\\\\uabec\\\\uabed\\\\uabf0-\\\\uabf9\\\\ufb20-\\\\ufb28\\\\ufe00-\\\\ufe0f\\\\ufe20-\\\\ufe26\\\\ufe33\\\\ufe34\\\\ufe4d-\\\\ufe4f\\\\uff10-\\\\uff19\\\\uff3f\";\n//var nonASCIIidentifierStart = new RegExp(\"[\" + nonASCIIidentifierStartChars + \"]\");\n//var nonASCIIidentifier = new RegExp(\"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\");\n\nvar unicodeEscapeOrCodePoint = \"\\\\\\\\u[0-9a-fA-F]{4}|\\\\\\\\u\\\\{[0-9a-fA-F]+\\\\}\";\nvar identifierStart = \"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierStartChars + nonASCIIidentifierStartChars + \"])\";\nvar identifierChars = \"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierChars + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"])*\";\n\nexports.identifier = new RegExp(identifierStart + identifierChars, 'g');\nexports.identifierStart = new RegExp(identifierStart);\nexports.identifierMatch = new RegExp(\"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierChars + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"])+\");\n\nvar nonASCIIwhitespace = /[\\u1680\\u180e\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff]/; // jshint ignore:line\n\n// Whether a single character denotes a newline.\n\nexports.newline = /[\\n\\r\\u2028\\u2029]/;\n\n// Matches a whole line break (where CRLF is considered a single\n// line break). Used to count lines.\n\n// in javascript, these two differ\n// in python they are the same, different methods are called on them\nexports.lineBreak = new RegExp('\\r\\n|' + exports.newline.source);\nexports.allLineBreaks = new RegExp(exports.lineBreak.source, 'g');\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nfunction Options(options, merge_child_field) {\n  this.raw_options = _mergeOpts(options, merge_child_field);\n\n  // Support passing the source text back with no change\n  this.disabled = this._get_boolean('disabled');\n\n  this.eol = this._get_characters('eol', 'auto');\n  this.end_with_newline = this._get_boolean('end_with_newline');\n  this.indent_size = this._get_number('indent_size', 4);\n  this.indent_char = this._get_characters('indent_char', ' ');\n  this.indent_level = this._get_number('indent_level');\n\n  this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n  this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n  if (!this.preserve_newlines) {\n    this.max_preserve_newlines = 0;\n  }\n\n  this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n  if (this.indent_with_tabs) {\n    this.indent_char = '\\t';\n\n    // indent_size behavior changed after 1.8.6\n    // It used to be that indent_size would be\n    // set to 1 for indent_with_tabs. That is no longer needed and\n    // actually doesn't make sense - why not use spaces? Further,\n    // that might produce unexpected behavior - tabs being used\n    // for single-column alignment. So, when indent_with_tabs is true\n    // and indent_size is 1, reset indent_size to 4.\n    if (this.indent_size === 1) {\n      this.indent_size = 4;\n    }\n  }\n\n  // Backwards compat with 1.3.x\n  this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n\n  this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n  // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n  // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n  // other values ignored\n  this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n}\n\nOptions.prototype._get_array = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || [];\n  if (typeof option_value === 'object') {\n    if (option_value !== null && typeof option_value.concat === 'function') {\n      result = option_value.concat();\n    }\n  } else if (typeof option_value === 'string') {\n    result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n  }\n  return result;\n};\n\nOptions.prototype._get_boolean = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = option_value === undefined ? !!default_value : !!option_value;\n  return result;\n};\n\nOptions.prototype._get_characters = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || '';\n  if (typeof option_value === 'string') {\n    result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n  }\n  return result;\n};\n\nOptions.prototype._get_number = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  default_value = parseInt(default_value, 10);\n  if (isNaN(default_value)) {\n    default_value = 0;\n  }\n  var result = parseInt(option_value, 10);\n  if (isNaN(result)) {\n    result = default_value;\n  }\n  return result;\n};\n\nOptions.prototype._get_selection = function(name, selection_list, default_value) {\n  var result = this._get_selection_list(name, selection_list, default_value);\n  if (result.length !== 1) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result[0];\n};\n\n\nOptions.prototype._get_selection_list = function(name, selection_list, default_value) {\n  if (!selection_list || selection_list.length === 0) {\n    throw new Error(\"Selection list cannot be empty.\");\n  }\n\n  default_value = default_value || [selection_list[0]];\n  if (!this._is_valid_selection(default_value, selection_list)) {\n    throw new Error(\"Invalid Default Value!\");\n  }\n\n  var result = this._get_array(name, default_value);\n  if (!this._is_valid_selection(result, selection_list)) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result;\n};\n\nOptions.prototype._is_valid_selection = function(result, selection_list) {\n  return result.length && selection_list.length &&\n    !result.some(function(item) { return selection_list.indexOf(item) === -1; });\n};\n\n\n// merges child options up with the parent options object\n// Example: obj = {a: 1, b: {a: 2}}\n//          mergeOpts(obj, 'b')\n//\n//          Returns: {a: 2}\nfunction _mergeOpts(allOptions, childFieldName) {\n  var finalOpts = {};\n  allOptions = _normalizeOpts(allOptions);\n  var name;\n\n  for (name in allOptions) {\n    if (name !== childFieldName) {\n      finalOpts[name] = allOptions[name];\n    }\n  }\n\n  //merge in the per type settings for the childFieldName\n  if (childFieldName && allOptions[childFieldName]) {\n    for (name in allOptions[childFieldName]) {\n      finalOpts[name] = allOptions[childFieldName][name];\n    }\n  }\n  return finalOpts;\n}\n\nfunction _normalizeOpts(options) {\n  var convertedOpts = {};\n  var key;\n\n  for (key in options) {\n    var newKey = key.replace(/-/g, \"_\");\n    convertedOpts[newKey] = options[key];\n  }\n  return convertedOpts;\n}\n\nmodule.exports.Options = Options;\nmodule.exports.normalizeOpts = _normalizeOpts;\nmodule.exports.mergeOpts = _mergeOpts;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar BaseOptions = require('../core/options').Options;\n\nvar validPositionValues = ['before-newline', 'after-newline', 'preserve-newline'];\n\nfunction Options(options) {\n  BaseOptions.call(this, options, 'js');\n\n  // compatibility, re\n  var raw_brace_style = this.raw_options.brace_style || null;\n  if (raw_brace_style === \"expand-strict\") { //graceful handling of deprecated option\n    this.raw_options.brace_style = \"expand\";\n  } else if (raw_brace_style === \"collapse-preserve-inline\") { //graceful handling of deprecated option\n    this.raw_options.brace_style = \"collapse,preserve-inline\";\n  } else if (this.raw_options.braces_on_own_line !== undefined) { //graceful handling of deprecated option\n    this.raw_options.brace_style = this.raw_options.braces_on_own_line ? \"expand\" : \"collapse\";\n    // } else if (!raw_brace_style) { //Nothing exists to set it\n    //   raw_brace_style = \"collapse\";\n  }\n\n  //preserve-inline in delimited string will trigger brace_preserve_inline, everything\n  //else is considered a brace_style and the last one only will have an effect\n\n  var brace_style_split = this._get_selection_list('brace_style', ['collapse', 'expand', 'end-expand', 'none', 'preserve-inline']);\n\n  this.brace_preserve_inline = false; //Defaults in case one or other was not specified in meta-option\n  this.brace_style = \"collapse\";\n\n  for (var bs = 0; bs < brace_style_split.length; bs++) {\n    if (brace_style_split[bs] === \"preserve-inline\") {\n      this.brace_preserve_inline = true;\n    } else {\n      this.brace_style = brace_style_split[bs];\n    }\n  }\n\n  this.unindent_chained_methods = this._get_boolean('unindent_chained_methods');\n  this.break_chained_methods = this._get_boolean('break_chained_methods');\n  this.space_in_paren = this._get_boolean('space_in_paren');\n  this.space_in_empty_paren = this._get_boolean('space_in_empty_paren');\n  this.jslint_happy = this._get_boolean('jslint_happy');\n  this.space_after_anon_function = this._get_boolean('space_after_anon_function');\n  this.space_after_named_function = this._get_boolean('space_after_named_function');\n  this.keep_array_indentation = this._get_boolean('keep_array_indentation');\n  this.space_before_conditional = this._get_boolean('space_before_conditional', true);\n  this.unescape_strings = this._get_boolean('unescape_strings');\n  this.e4x = this._get_boolean('e4x');\n  this.comma_first = this._get_boolean('comma_first');\n  this.operator_position = this._get_selection('operator_position', validPositionValues);\n\n  // For testing of beautify preserve:start directive\n  this.test_output_raw = this._get_boolean('test_output_raw');\n\n  // force this._options.space_after_anon_function to true if this._options.jslint_happy\n  if (this.jslint_happy) {\n    this.space_after_anon_function = true;\n  }\n\n}\nOptions.prototype = new BaseOptions();\n\n\n\nmodule.exports.Options = Options;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n\nfunction InputScanner(input_string) {\n  this.__input = input_string || '';\n  this.__input_length = this.__input.length;\n  this.__position = 0;\n}\n\nInputScanner.prototype.restart = function() {\n  this.__position = 0;\n};\n\nInputScanner.prototype.back = function() {\n  if (this.__position > 0) {\n    this.__position -= 1;\n  }\n};\n\nInputScanner.prototype.hasNext = function() {\n  return this.__position < this.__input_length;\n};\n\nInputScanner.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__input.charAt(this.__position);\n    this.__position += 1;\n  }\n  return val;\n};\n\nInputScanner.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__input_length) {\n    val = this.__input.charAt(index);\n  }\n  return val;\n};\n\n// This is a JavaScript only helper function (not in python)\n// Javascript doesn't have a match method\n// and not all implementation support \"sticky\" flag.\n// If they do not support sticky then both this.match() and this.test() method\n// must get the match and check the index of the match.\n// If sticky is supported and set, this method will use it.\n// Otherwise it will check that global is set, and fall back to the slower method.\nInputScanner.prototype.__match = function(pattern, index) {\n  pattern.lastIndex = index;\n  var pattern_match = pattern.exec(this.__input);\n\n  if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n    if (pattern_match.index !== index) {\n      pattern_match = null;\n    }\n  }\n\n  return pattern_match;\n};\n\nInputScanner.prototype.test = function(pattern, index) {\n  index = index || 0;\n  index += this.__position;\n\n  if (index >= 0 && index < this.__input_length) {\n    return !!this.__match(pattern, index);\n  } else {\n    return false;\n  }\n};\n\nInputScanner.prototype.testChar = function(pattern, index) {\n  // test one character regex match\n  var val = this.peek(index);\n  pattern.lastIndex = 0;\n  return val !== null && pattern.test(val);\n};\n\nInputScanner.prototype.match = function(pattern) {\n  var pattern_match = this.__match(pattern, this.__position);\n  if (pattern_match) {\n    this.__position += pattern_match[0].length;\n  } else {\n    pattern_match = null;\n  }\n  return pattern_match;\n};\n\nInputScanner.prototype.read = function(starting_pattern, until_pattern, until_after) {\n  var val = '';\n  var match;\n  if (starting_pattern) {\n    match = this.match(starting_pattern);\n    if (match) {\n      val += match[0];\n    }\n  }\n  if (until_pattern && (match || !starting_pattern)) {\n    val += this.readUntil(until_pattern, until_after);\n  }\n  return val;\n};\n\nInputScanner.prototype.readUntil = function(pattern, until_after) {\n  var val = '';\n  var match_index = this.__position;\n  pattern.lastIndex = this.__position;\n  var pattern_match = pattern.exec(this.__input);\n  if (pattern_match) {\n    match_index = pattern_match.index;\n    if (until_after) {\n      match_index += pattern_match[0].length;\n    }\n  } else {\n    match_index = this.__input_length;\n  }\n\n  val = this.__input.substring(this.__position, match_index);\n  this.__position = match_index;\n  return val;\n};\n\nInputScanner.prototype.readUntilAfter = function(pattern) {\n  return this.readUntil(pattern, true);\n};\n\nInputScanner.prototype.get_regexp = function(pattern, match_from) {\n  var result = null;\n  var flags = 'g';\n  if (match_from && regexp_has_sticky) {\n    flags = 'y';\n  }\n  // strings are converted to regexp\n  if (typeof pattern === \"string\" && pattern !== '') {\n    // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n    result = new RegExp(pattern, flags);\n  } else if (pattern) {\n    result = new RegExp(pattern.source, flags);\n  }\n  return result;\n};\n\nInputScanner.prototype.get_literal_regexp = function(literal_string) {\n  return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n};\n\n/* css beautifier legacy helpers */\nInputScanner.prototype.peekUntilAfter = function(pattern) {\n  var start = this.__position;\n  var val = this.readUntilAfter(pattern);\n  this.__position = start;\n  return val;\n};\n\nInputScanner.prototype.lookBack = function(testVal) {\n  var start = this.__position - 1;\n  return start >= testVal.length && this.__input.substring(start - testVal.length, start)\n    .toLowerCase() === testVal;\n};\n\nmodule.exports.InputScanner = InputScanner;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nfunction TokenStream(parent_token) {\n  // private\n  this.__tokens = [];\n  this.__tokens_length = this.__tokens.length;\n  this.__position = 0;\n  this.__parent_token = parent_token;\n}\n\nTokenStream.prototype.restart = function() {\n  this.__position = 0;\n};\n\nTokenStream.prototype.isEmpty = function() {\n  return this.__tokens_length === 0;\n};\n\nTokenStream.prototype.hasNext = function() {\n  return this.__position < this.__tokens_length;\n};\n\nTokenStream.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__tokens[this.__position];\n    this.__position += 1;\n  }\n  return val;\n};\n\nTokenStream.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__tokens_length) {\n    val = this.__tokens[index];\n  }\n  return val;\n};\n\nTokenStream.prototype.add = function(token) {\n  if (this.__parent_token) {\n    token.parent = this.__parent_token;\n  }\n  this.__tokens.push(token);\n  this.__tokens_length += 1;\n};\n\nmodule.exports.TokenStream = TokenStream;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nfunction Pattern(input_scanner, parent) {\n  this._input = input_scanner;\n  this._starting_pattern = null;\n  this._match_pattern = null;\n  this._until_pattern = null;\n  this._until_after = false;\n\n  if (parent) {\n    this._starting_pattern = this._input.get_regexp(parent._starting_pattern, true);\n    this._match_pattern = this._input.get_regexp(parent._match_pattern, true);\n    this._until_pattern = this._input.get_regexp(parent._until_pattern);\n    this._until_after = parent._until_after;\n  }\n}\n\nPattern.prototype.read = function() {\n  var result = this._input.read(this._starting_pattern);\n  if (!this._starting_pattern || result) {\n    result += this._input.read(this._match_pattern, this._until_pattern, this._until_after);\n  }\n  return result;\n};\n\nPattern.prototype.read_match = function() {\n  return this._input.match(this._match_pattern);\n};\n\nPattern.prototype.until_after = function(pattern) {\n  var result = this._create();\n  result._until_after = true;\n  result._until_pattern = this._input.get_regexp(pattern);\n  result._update();\n  return result;\n};\n\nPattern.prototype.until = function(pattern) {\n  var result = this._create();\n  result._until_after = false;\n  result._until_pattern = this._input.get_regexp(pattern);\n  result._update();\n  return result;\n};\n\nPattern.prototype.starting_with = function(pattern) {\n  var result = this._create();\n  result._starting_pattern = this._input.get_regexp(pattern, true);\n  result._update();\n  return result;\n};\n\nPattern.prototype.matching = function(pattern) {\n  var result = this._create();\n  result._match_pattern = this._input.get_regexp(pattern, true);\n  result._update();\n  return result;\n};\n\nPattern.prototype._create = function() {\n  return new Pattern(this._input, this);\n};\n\nPattern.prototype._update = function() {};\n\nmodule.exports.Pattern = Pattern;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Pattern = require('../core/pattern').Pattern;\n\nfunction WhitespacePattern(input_scanner, parent) {\n  Pattern.call(this, input_scanner, parent);\n  if (parent) {\n    this._line_regexp = this._input.get_regexp(parent._line_regexp);\n  } else {\n    this.__set_whitespace_patterns('', '');\n  }\n\n  this.newline_count = 0;\n  this.whitespace_before_token = '';\n}\nWhitespacePattern.prototype = new Pattern();\n\nWhitespacePattern.prototype.__set_whitespace_patterns = function(whitespace_chars, newline_chars) {\n  whitespace_chars += '\\\\t ';\n  newline_chars += '\\\\n\\\\r';\n\n  this._match_pattern = this._input.get_regexp(\n    '[' + whitespace_chars + newline_chars + ']+', true);\n  this._newline_regexp = this._input.get_regexp(\n    '\\\\r\\\\n|[' + newline_chars + ']');\n};\n\nWhitespacePattern.prototype.read = function() {\n  this.newline_count = 0;\n  this.whitespace_before_token = '';\n\n  var resulting_string = this._input.read(this._match_pattern);\n  if (resulting_string === ' ') {\n    this.whitespace_before_token = ' ';\n  } else if (resulting_string) {\n    var matches = this.__split(this._newline_regexp, resulting_string);\n    this.newline_count = matches.length - 1;\n    this.whitespace_before_token = matches[this.newline_count];\n  }\n\n  return resulting_string;\n};\n\nWhitespacePattern.prototype.matching = function(whitespace_chars, newline_chars) {\n  var result = this._create();\n  result.__set_whitespace_patterns(whitespace_chars, newline_chars);\n  result._update();\n  return result;\n};\n\nWhitespacePattern.prototype._create = function() {\n  return new WhitespacePattern(this._input, this);\n};\n\nWhitespacePattern.prototype.__split = function(regexp, input_string) {\n  regexp.lastIndex = 0;\n  var start_index = 0;\n  var result = [];\n  var next_match = regexp.exec(input_string);\n  while (next_match) {\n    result.push(input_string.substring(start_index, next_match.index));\n    start_index = next_match.index + next_match[0].length;\n    next_match = regexp.exec(input_string);\n  }\n\n  if (start_index < input_string.length) {\n    result.push(input_string.substring(start_index, input_string.length));\n  } else {\n    result.push('');\n  }\n\n  return result;\n};\n\n\n\nmodule.exports.WhitespacePattern = WhitespacePattern;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar InputScanner = require('../core/inputscanner').InputScanner;\nvar Token = require('../core/token').Token;\nvar TokenStream = require('../core/tokenstream').TokenStream;\nvar WhitespacePattern = require('./whitespacepattern').WhitespacePattern;\n\nvar TOKEN = {\n  START: 'TK_START',\n  RAW: 'TK_RAW',\n  EOF: 'TK_EOF'\n};\n\nvar Tokenizer = function(input_string, options) {\n  this._input = new InputScanner(input_string);\n  this._options = options || {};\n  this.__tokens = null;\n\n  this._patterns = {};\n  this._patterns.whitespace = new WhitespacePattern(this._input);\n};\n\nTokenizer.prototype.tokenize = function() {\n  this._input.restart();\n  this.__tokens = new TokenStream();\n\n  this._reset();\n\n  var current;\n  var previous = new Token(TOKEN.START, '');\n  var open_token = null;\n  var open_stack = [];\n  var comments = new TokenStream();\n\n  while (previous.type !== TOKEN.EOF) {\n    current = this._get_next_token(previous, open_token);\n    while (this._is_comment(current)) {\n      comments.add(current);\n      current = this._get_next_token(previous, open_token);\n    }\n\n    if (!comments.isEmpty()) {\n      current.comments_before = comments;\n      comments = new TokenStream();\n    }\n\n    current.parent = open_token;\n\n    if (this._is_opening(current)) {\n      open_stack.push(open_token);\n      open_token = current;\n    } else if (open_token && this._is_closing(current, open_token)) {\n      current.opened = open_token;\n      open_token.closed = current;\n      open_token = open_stack.pop();\n      current.parent = open_token;\n    }\n\n    current.previous = previous;\n    previous.next = current;\n\n    this.__tokens.add(current);\n    previous = current;\n  }\n\n  return this.__tokens;\n};\n\n\nTokenizer.prototype._is_first_token = function() {\n  return this.__tokens.isEmpty();\n};\n\nTokenizer.prototype._reset = function() {};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  this._readWhitespace();\n  var resulting_string = this._input.read(/.+/g);\n  if (resulting_string) {\n    return this._create_token(TOKEN.RAW, resulting_string);\n  } else {\n    return this._create_token(TOKEN.EOF, '');\n  }\n};\n\nTokenizer.prototype._is_comment = function(current_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._is_opening = function(current_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._create_token = function(type, text) {\n  var token = new Token(type, text,\n    this._patterns.whitespace.newline_count,\n    this._patterns.whitespace.whitespace_before_token);\n  return token;\n};\n\nTokenizer.prototype._readWhitespace = function() {\n  return this._patterns.whitespace.read();\n};\n\n\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nfunction Directives(start_block_pattern, end_block_pattern) {\n  start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n  end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n  this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n  this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n\n  this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n}\n\nDirectives.prototype.get_directives = function(text) {\n  if (!text.match(this.__directives_block_pattern)) {\n    return null;\n  }\n\n  var directives = {};\n  this.__directive_pattern.lastIndex = 0;\n  var directive_match = this.__directive_pattern.exec(text);\n\n  while (directive_match) {\n    directives[directive_match[1]] = directive_match[2];\n    directive_match = this.__directive_pattern.exec(text);\n  }\n\n  return directives;\n};\n\nDirectives.prototype.readIgnored = function(input) {\n  return input.readUntilAfter(this.__directives_end_ignore_pattern);\n};\n\n\nmodule.exports.Directives = Directives;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Pattern = require('./pattern').Pattern;\n\n\nvar template_names = {\n  django: false,\n  erb: false,\n  handlebars: false,\n  php: false,\n  smarty: false,\n  angular: false\n};\n\n// This lets templates appear anywhere we would do a readUntil\n// The cost is higher but it is pay to play.\nfunction TemplatablePattern(input_scanner, parent) {\n  Pattern.call(this, input_scanner, parent);\n  this.__template_pattern = null;\n  this._disabled = Object.assign({}, template_names);\n  this._excluded = Object.assign({}, template_names);\n\n  if (parent) {\n    this.__template_pattern = this._input.get_regexp(parent.__template_pattern);\n    this._excluded = Object.assign(this._excluded, parent._excluded);\n    this._disabled = Object.assign(this._disabled, parent._disabled);\n  }\n  var pattern = new Pattern(input_scanner);\n  this.__patterns = {\n    handlebars_comment: pattern.starting_with(/{{!--/).until_after(/--}}/),\n    handlebars_unescaped: pattern.starting_with(/{{{/).until_after(/}}}/),\n    handlebars: pattern.starting_with(/{{/).until_after(/}}/),\n    php: pattern.starting_with(/<\\?(?:[= ]|php)/).until_after(/\\?>/),\n    erb: pattern.starting_with(/<%[^%]/).until_after(/[^%]%>/),\n    // django coflicts with handlebars a bit.\n    django: pattern.starting_with(/{%/).until_after(/%}/),\n    django_value: pattern.starting_with(/{{/).until_after(/}}/),\n    django_comment: pattern.starting_with(/{#/).until_after(/#}/),\n    smarty: pattern.starting_with(/{(?=[^}{\\s\\n])/).until_after(/[^\\s\\n]}/),\n    smarty_comment: pattern.starting_with(/{\\*/).until_after(/\\*}/),\n    smarty_literal: pattern.starting_with(/{literal}/).until_after(/{\\/literal}/)\n  };\n}\nTemplatablePattern.prototype = new Pattern();\n\nTemplatablePattern.prototype._create = function() {\n  return new TemplatablePattern(this._input, this);\n};\n\nTemplatablePattern.prototype._update = function() {\n  this.__set_templated_pattern();\n};\n\nTemplatablePattern.prototype.disable = function(language) {\n  var result = this._create();\n  result._disabled[language] = true;\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.read_options = function(options) {\n  var result = this._create();\n  for (var language in template_names) {\n    result._disabled[language] = options.templating.indexOf(language) === -1;\n  }\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.exclude = function(language) {\n  var result = this._create();\n  result._excluded[language] = true;\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.read = function() {\n  var result = '';\n  if (this._match_pattern) {\n    result = this._input.read(this._starting_pattern);\n  } else {\n    result = this._input.read(this._starting_pattern, this.__template_pattern);\n  }\n  var next = this._read_template();\n  while (next) {\n    if (this._match_pattern) {\n      next += this._input.read(this._match_pattern);\n    } else {\n      next += this._input.readUntil(this.__template_pattern);\n    }\n    result += next;\n    next = this._read_template();\n  }\n\n  if (this._until_after) {\n    result += this._input.readUntilAfter(this._until_pattern);\n  }\n  return result;\n};\n\nTemplatablePattern.prototype.__set_templated_pattern = function() {\n  var items = [];\n\n  if (!this._disabled.php) {\n    items.push(this.__patterns.php._starting_pattern.source);\n  }\n  if (!this._disabled.handlebars) {\n    items.push(this.__patterns.handlebars._starting_pattern.source);\n  }\n  if (!this._disabled.angular) {\n    // Handlebars ('{{' and '}}') are also special tokens in Angular)\n    items.push(this.__patterns.handlebars._starting_pattern.source);\n  }\n  if (!this._disabled.erb) {\n    items.push(this.__patterns.erb._starting_pattern.source);\n  }\n  if (!this._disabled.django) {\n    items.push(this.__patterns.django._starting_pattern.source);\n    // The starting pattern for django is more complex because it has different\n    // patterns for value, comment, and other sections\n    items.push(this.__patterns.django_value._starting_pattern.source);\n    items.push(this.__patterns.django_comment._starting_pattern.source);\n  }\n  if (!this._disabled.smarty) {\n    items.push(this.__patterns.smarty._starting_pattern.source);\n  }\n\n  if (this._until_pattern) {\n    items.push(this._until_pattern.source);\n  }\n  this.__template_pattern = this._input.get_regexp('(?:' + items.join('|') + ')');\n};\n\nTemplatablePattern.prototype._read_template = function() {\n  var resulting_string = '';\n  var c = this._input.peek();\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    //if we're in a comment, do something special\n    // We treat all comments as literals, even more than preformatted tags\n    // we just look for the appropriate close tag\n    if (!this._disabled.php && !this._excluded.php && peek1 === '?') {\n      resulting_string = resulting_string ||\n        this.__patterns.php.read();\n    }\n    if (!this._disabled.erb && !this._excluded.erb && peek1 === '%') {\n      resulting_string = resulting_string ||\n        this.__patterns.erb.read();\n    }\n  } else if (c === '{') {\n    if (!this._disabled.handlebars && !this._excluded.handlebars) {\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars_comment.read();\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars_unescaped.read();\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars.read();\n    }\n    if (!this._disabled.django) {\n      // django coflicts with handlebars a bit.\n      if (!this._excluded.django && !this._excluded.handlebars) {\n        resulting_string = resulting_string ||\n          this.__patterns.django_value.read();\n      }\n      if (!this._excluded.django) {\n        resulting_string = resulting_string ||\n          this.__patterns.django_comment.read();\n        resulting_string = resulting_string ||\n          this.__patterns.django.read();\n      }\n    }\n    if (!this._disabled.smarty) {\n      // smarty cannot be enabled with django or handlebars enabled\n      if (this._disabled.django && this._disabled.handlebars) {\n        resulting_string = resulting_string ||\n          this.__patterns.smarty_comment.read();\n        resulting_string = resulting_string ||\n          this.__patterns.smarty_literal.read();\n        resulting_string = resulting_string ||\n          this.__patterns.smarty.read();\n      }\n    }\n  }\n  return resulting_string;\n};\n\n\nmodule.exports.TemplatablePattern = TemplatablePattern;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar InputScanner = require('../core/inputscanner').InputScanner;\nvar BaseTokenizer = require('../core/tokenizer').Tokenizer;\nvar BASETOKEN = require('../core/tokenizer').TOKEN;\nvar Directives = require('../core/directives').Directives;\nvar acorn = require('./acorn');\nvar Pattern = require('../core/pattern').Pattern;\nvar TemplatablePattern = require('../core/templatablepattern').TemplatablePattern;\n\n\nfunction in_array(what, arr) {\n  return arr.indexOf(what) !== -1;\n}\n\n\nvar TOKEN = {\n  START_EXPR: 'TK_START_EXPR',\n  END_EXPR: 'TK_END_EXPR',\n  START_BLOCK: 'TK_START_BLOCK',\n  END_BLOCK: 'TK_END_BLOCK',\n  WORD: 'TK_WORD',\n  RESERVED: 'TK_RESERVED',\n  SEMICOLON: 'TK_SEMICOLON',\n  STRING: 'TK_STRING',\n  EQUALS: 'TK_EQUALS',\n  OPERATOR: 'TK_OPERATOR',\n  COMMA: 'TK_COMMA',\n  BLOCK_COMMENT: 'TK_BLOCK_COMMENT',\n  COMMENT: 'TK_COMMENT',\n  DOT: 'TK_DOT',\n  UNKNOWN: 'TK_UNKNOWN',\n  START: BASETOKEN.START,\n  RAW: BASETOKEN.RAW,\n  EOF: BASETOKEN.EOF\n};\n\n\nvar directives_core = new Directives(/\\/\\*/, /\\*\\//);\n\nvar number_pattern = /0[xX][0123456789abcdefABCDEF_]*n?|0[oO][01234567_]*n?|0[bB][01_]*n?|\\d[\\d_]*n|(?:\\.\\d[\\d_]*|\\d[\\d_]*\\.?[\\d_]*)(?:[eE][+-]?[\\d_]+)?/;\n\nvar digit = /[0-9]/;\n\n// Dot \".\" must be distinguished from \"...\" and decimal\nvar dot_pattern = /[^\\d\\.]/;\n\nvar positionable_operators = (\n  \">>> === !== &&= ??= ||= \" +\n  \"<< && >= ** != == <= >> || ?? |> \" +\n  \"< / - + > : & % ? ^ | *\").split(' ');\n\n// IMPORTANT: this must be sorted longest to shortest or tokenizing many not work.\n// Also, you must update possitionable operators separately from punct\nvar punct =\n  \">>>= \" +\n  \"... >>= <<= === >>> !== **= &&= ??= ||= \" +\n  \"=> ^= :: /= << <= == && -= >= >> != -- += ** || ?? ++ %= &= *= |= |> \" +\n  \"= ! ? > < : / ^ - + * & % ~ |\";\n\npunct = punct.replace(/[-[\\]{}()*+?.,\\\\^$|#]/g, \"\\\\$&\");\n// ?. but not if followed by a number \npunct = '\\\\?\\\\.(?!\\\\d) ' + punct;\npunct = punct.replace(/ /g, '|');\n\nvar punct_pattern = new RegExp(punct);\n\n// words which should always start on new line.\nvar line_starters = 'continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export'.split(',');\nvar reserved_words = line_starters.concat(['do', 'in', 'of', 'else', 'get', 'set', 'new', 'catch', 'finally', 'typeof', 'yield', 'async', 'await', 'from', 'as', 'class', 'extends']);\nvar reserved_word_pattern = new RegExp('^(?:' + reserved_words.join('|') + ')$');\n\n// var template_pattern = /(?:(?:<\\?php|<\\?=)[\\s\\S]*?\\?>)|(?:<%[\\s\\S]*?%>)/g;\n\nvar in_html_comment;\n\nvar Tokenizer = function(input_string, options) {\n  BaseTokenizer.call(this, input_string, options);\n\n  this._patterns.whitespace = this._patterns.whitespace.matching(\n    /\\u00A0\\u1680\\u180e\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff/.source,\n    /\\u2028\\u2029/.source);\n\n  var pattern_reader = new Pattern(this._input);\n  var templatable = new TemplatablePattern(this._input)\n    .read_options(this._options);\n\n  this.__patterns = {\n    template: templatable,\n    identifier: templatable.starting_with(acorn.identifier).matching(acorn.identifierMatch),\n    number: pattern_reader.matching(number_pattern),\n    punct: pattern_reader.matching(punct_pattern),\n    // comment ends just before nearest linefeed or end of file\n    comment: pattern_reader.starting_with(/\\/\\//).until(/[\\n\\r\\u2028\\u2029]/),\n    //  /* ... */ comment ends with nearest */ or end of file\n    block_comment: pattern_reader.starting_with(/\\/\\*/).until_after(/\\*\\//),\n    html_comment_start: pattern_reader.matching(/<!--/),\n    html_comment_end: pattern_reader.matching(/-->/),\n    include: pattern_reader.starting_with(/#include/).until_after(acorn.lineBreak),\n    shebang: pattern_reader.starting_with(/#!/).until_after(acorn.lineBreak),\n    xml: pattern_reader.matching(/[\\s\\S]*?<(\\/?)([-a-zA-Z:0-9_.]+|{[^}]+?}|!\\[CDATA\\[[^\\]]*?\\]\\]|)(\\s*{[^}]+?}|\\s+[-a-zA-Z:0-9_.]+|\\s+[-a-zA-Z:0-9_.]+\\s*=\\s*('[^']*'|\"[^\"]*\"|{([^{}]|{[^}]+?})+?}))*\\s*(\\/?)\\s*>/),\n    single_quote: templatable.until(/['\\\\\\n\\r\\u2028\\u2029]/),\n    double_quote: templatable.until(/[\"\\\\\\n\\r\\u2028\\u2029]/),\n    template_text: templatable.until(/[`\\\\$]/),\n    template_expression: templatable.until(/[`}\\\\]/)\n  };\n\n};\nTokenizer.prototype = new BaseTokenizer();\n\nTokenizer.prototype._is_comment = function(current_token) {\n  return current_token.type === TOKEN.COMMENT || current_token.type === TOKEN.BLOCK_COMMENT || current_token.type === TOKEN.UNKNOWN;\n};\n\nTokenizer.prototype._is_opening = function(current_token) {\n  return current_token.type === TOKEN.START_BLOCK || current_token.type === TOKEN.START_EXPR;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) {\n  return (current_token.type === TOKEN.END_BLOCK || current_token.type === TOKEN.END_EXPR) &&\n    (open_token && (\n      (current_token.text === ']' && open_token.text === '[') ||\n      (current_token.text === ')' && open_token.text === '(') ||\n      (current_token.text === '}' && open_token.text === '{')));\n};\n\nTokenizer.prototype._reset = function() {\n  in_html_comment = false;\n};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  var token = null;\n  this._readWhitespace();\n  var c = this._input.peek();\n\n  if (c === null) {\n    return this._create_token(TOKEN.EOF, '');\n  }\n\n  token = token || this._read_non_javascript(c);\n  token = token || this._read_string(c);\n  token = token || this._read_pair(c, this._input.peek(1)); // Issue #2062 hack for record type '#{'\n  token = token || this._read_word(previous_token);\n  token = token || this._read_singles(c);\n  token = token || this._read_comment(c);\n  token = token || this._read_regexp(c, previous_token);\n  token = token || this._read_xml(c, previous_token);\n  token = token || this._read_punctuation();\n  token = token || this._create_token(TOKEN.UNKNOWN, this._input.next());\n\n  return token;\n};\n\nTokenizer.prototype._read_word = function(previous_token) {\n  var resulting_string;\n  resulting_string = this.__patterns.identifier.read();\n  if (resulting_string !== '') {\n    resulting_string = resulting_string.replace(acorn.allLineBreaks, '\\n');\n    if (!(previous_token.type === TOKEN.DOT ||\n        (previous_token.type === TOKEN.RESERVED && (previous_token.text === 'set' || previous_token.text === 'get'))) &&\n      reserved_word_pattern.test(resulting_string)) {\n      if ((resulting_string === 'in' || resulting_string === 'of') &&\n        (previous_token.type === TOKEN.WORD || previous_token.type === TOKEN.STRING)) { // hack for 'in' and 'of' operators\n        return this._create_token(TOKEN.OPERATOR, resulting_string);\n      }\n      return this._create_token(TOKEN.RESERVED, resulting_string);\n    }\n    return this._create_token(TOKEN.WORD, resulting_string);\n  }\n\n  resulting_string = this.__patterns.number.read();\n  if (resulting_string !== '') {\n    return this._create_token(TOKEN.WORD, resulting_string);\n  }\n};\n\nTokenizer.prototype._read_singles = function(c) {\n  var token = null;\n  if (c === '(' || c === '[') {\n    token = this._create_token(TOKEN.START_EXPR, c);\n  } else if (c === ')' || c === ']') {\n    token = this._create_token(TOKEN.END_EXPR, c);\n  } else if (c === '{') {\n    token = this._create_token(TOKEN.START_BLOCK, c);\n  } else if (c === '}') {\n    token = this._create_token(TOKEN.END_BLOCK, c);\n  } else if (c === ';') {\n    token = this._create_token(TOKEN.SEMICOLON, c);\n  } else if (c === '.' && dot_pattern.test(this._input.peek(1))) {\n    token = this._create_token(TOKEN.DOT, c);\n  } else if (c === ',') {\n    token = this._create_token(TOKEN.COMMA, c);\n  }\n\n  if (token) {\n    this._input.next();\n  }\n  return token;\n};\n\nTokenizer.prototype._read_pair = function(c, d) {\n  var token = null;\n  if (c === '#' && d === '{') {\n    token = this._create_token(TOKEN.START_BLOCK, c + d);\n  }\n\n  if (token) {\n    this._input.next();\n    this._input.next();\n  }\n  return token;\n};\n\nTokenizer.prototype._read_punctuation = function() {\n  var resulting_string = this.__patterns.punct.read();\n\n  if (resulting_string !== '') {\n    if (resulting_string === '=') {\n      return this._create_token(TOKEN.EQUALS, resulting_string);\n    } else if (resulting_string === '?.') {\n      return this._create_token(TOKEN.DOT, resulting_string);\n    } else {\n      return this._create_token(TOKEN.OPERATOR, resulting_string);\n    }\n  }\n};\n\nTokenizer.prototype._read_non_javascript = function(c) {\n  var resulting_string = '';\n\n  if (c === '#') {\n    if (this._is_first_token()) {\n      resulting_string = this.__patterns.shebang.read();\n\n      if (resulting_string) {\n        return this._create_token(TOKEN.UNKNOWN, resulting_string.trim() + '\\n');\n      }\n    }\n\n    // handles extendscript #includes\n    resulting_string = this.__patterns.include.read();\n\n    if (resulting_string) {\n      return this._create_token(TOKEN.UNKNOWN, resulting_string.trim() + '\\n');\n    }\n\n    c = this._input.next();\n\n    // Spidermonkey-specific sharp variables for circular references. Considered obsolete.\n    var sharp = '#';\n    if (this._input.hasNext() && this._input.testChar(digit)) {\n      do {\n        c = this._input.next();\n        sharp += c;\n      } while (this._input.hasNext() && c !== '#' && c !== '=');\n      if (c === '#') {\n        //\n      } else if (this._input.peek() === '[' && this._input.peek(1) === ']') {\n        sharp += '[]';\n        this._input.next();\n        this._input.next();\n      } else if (this._input.peek() === '{' && this._input.peek(1) === '}') {\n        sharp += '{}';\n        this._input.next();\n        this._input.next();\n      }\n      return this._create_token(TOKEN.WORD, sharp);\n    }\n\n    this._input.back();\n\n  } else if (c === '<' && this._is_first_token()) {\n    resulting_string = this.__patterns.html_comment_start.read();\n    if (resulting_string) {\n      while (this._input.hasNext() && !this._input.testChar(acorn.newline)) {\n        resulting_string += this._input.next();\n      }\n      in_html_comment = true;\n      return this._create_token(TOKEN.COMMENT, resulting_string);\n    }\n  } else if (in_html_comment && c === '-') {\n    resulting_string = this.__patterns.html_comment_end.read();\n    if (resulting_string) {\n      in_html_comment = false;\n      return this._create_token(TOKEN.COMMENT, resulting_string);\n    }\n  }\n\n  return null;\n};\n\nTokenizer.prototype._read_comment = function(c) {\n  var token = null;\n  if (c === '/') {\n    var comment = '';\n    if (this._input.peek(1) === '*') {\n      // peek for comment /* ... */\n      comment = this.__patterns.block_comment.read();\n      var directives = directives_core.get_directives(comment);\n      if (directives && directives.ignore === 'start') {\n        comment += directives_core.readIgnored(this._input);\n      }\n      comment = comment.replace(acorn.allLineBreaks, '\\n');\n      token = this._create_token(TOKEN.BLOCK_COMMENT, comment);\n      token.directives = directives;\n    } else if (this._input.peek(1) === '/') {\n      // peek for comment // ...\n      comment = this.__patterns.comment.read();\n      token = this._create_token(TOKEN.COMMENT, comment);\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._read_string = function(c) {\n  if (c === '`' || c === \"'\" || c === '\"') {\n    var resulting_string = this._input.next();\n    this.has_char_escapes = false;\n\n    if (c === '`') {\n      resulting_string += this._read_string_recursive('`', true, '${');\n    } else {\n      resulting_string += this._read_string_recursive(c);\n    }\n\n    if (this.has_char_escapes && this._options.unescape_strings) {\n      resulting_string = unescape_string(resulting_string);\n    }\n\n    if (this._input.peek() === c) {\n      resulting_string += this._input.next();\n    }\n\n    resulting_string = resulting_string.replace(acorn.allLineBreaks, '\\n');\n\n    return this._create_token(TOKEN.STRING, resulting_string);\n  }\n\n  return null;\n};\n\nTokenizer.prototype._allow_regexp_or_xml = function(previous_token) {\n  // regex and xml can only appear in specific locations during parsing\n  return (previous_token.type === TOKEN.RESERVED && in_array(previous_token.text, ['return', 'case', 'throw', 'else', 'do', 'typeof', 'yield'])) ||\n    (previous_token.type === TOKEN.END_EXPR && previous_token.text === ')' &&\n      previous_token.opened.previous.type === TOKEN.RESERVED && in_array(previous_token.opened.previous.text, ['if', 'while', 'for'])) ||\n    (in_array(previous_token.type, [TOKEN.COMMENT, TOKEN.START_EXPR, TOKEN.START_BLOCK, TOKEN.START,\n      TOKEN.END_BLOCK, TOKEN.OPERATOR, TOKEN.EQUALS, TOKEN.EOF, TOKEN.SEMICOLON, TOKEN.COMMA\n    ]));\n};\n\nTokenizer.prototype._read_regexp = function(c, previous_token) {\n\n  if (c === '/' && this._allow_regexp_or_xml(previous_token)) {\n    // handle regexp\n    //\n    var resulting_string = this._input.next();\n    var esc = false;\n\n    var in_char_class = false;\n    while (this._input.hasNext() &&\n      ((esc || in_char_class || this._input.peek() !== c) &&\n        !this._input.testChar(acorn.newline))) {\n      resulting_string += this._input.peek();\n      if (!esc) {\n        esc = this._input.peek() === '\\\\';\n        if (this._input.peek() === '[') {\n          in_char_class = true;\n        } else if (this._input.peek() === ']') {\n          in_char_class = false;\n        }\n      } else {\n        esc = false;\n      }\n      this._input.next();\n    }\n\n    if (this._input.peek() === c) {\n      resulting_string += this._input.next();\n\n      // regexps may have modifiers /regexp/MOD , so fetch those, too\n      // Only [gim] are valid, but if the user puts in garbage, do what we can to take it.\n      resulting_string += this._input.read(acorn.identifier);\n    }\n    return this._create_token(TOKEN.STRING, resulting_string);\n  }\n  return null;\n};\n\nTokenizer.prototype._read_xml = function(c, previous_token) {\n\n  if (this._options.e4x && c === \"<\" && this._allow_regexp_or_xml(previous_token)) {\n    var xmlStr = '';\n    var match = this.__patterns.xml.read_match();\n    // handle e4x xml literals\n    //\n    if (match) {\n      // Trim root tag to attempt to\n      var rootTag = match[2].replace(/^{\\s+/, '{').replace(/\\s+}$/, '}');\n      var isCurlyRoot = rootTag.indexOf('{') === 0;\n      var depth = 0;\n      while (match) {\n        var isEndTag = !!match[1];\n        var tagName = match[2];\n        var isSingletonTag = (!!match[match.length - 1]) || (tagName.slice(0, 8) === \"![CDATA[\");\n        if (!isSingletonTag &&\n          (tagName === rootTag || (isCurlyRoot && tagName.replace(/^{\\s+/, '{').replace(/\\s+}$/, '}')))) {\n          if (isEndTag) {\n            --depth;\n          } else {\n            ++depth;\n          }\n        }\n        xmlStr += match[0];\n        if (depth <= 0) {\n          break;\n        }\n        match = this.__patterns.xml.read_match();\n      }\n      // if we didn't close correctly, keep unformatted.\n      if (!match) {\n        xmlStr += this._input.match(/[\\s\\S]*/g)[0];\n      }\n      xmlStr = xmlStr.replace(acorn.allLineBreaks, '\\n');\n      return this._create_token(TOKEN.STRING, xmlStr);\n    }\n  }\n\n  return null;\n};\n\nfunction unescape_string(s) {\n  // You think that a regex would work for this\n  // return s.replace(/\\\\x([0-9a-f]{2})/gi, function(match, val) {\n  //         return String.fromCharCode(parseInt(val, 16));\n  //     })\n  // However, dealing with '\\xff', '\\\\xff', '\\\\\\xff' makes this more fun.\n  var out = '',\n    escaped = 0;\n\n  var input_scan = new InputScanner(s);\n  var matched = null;\n\n  while (input_scan.hasNext()) {\n    // Keep any whitespace, non-slash characters\n    // also keep slash pairs.\n    matched = input_scan.match(/([\\s]|[^\\\\]|\\\\\\\\)+/g);\n\n    if (matched) {\n      out += matched[0];\n    }\n\n    if (input_scan.peek() === '\\\\') {\n      input_scan.next();\n      if (input_scan.peek() === 'x') {\n        matched = input_scan.match(/x([0-9A-Fa-f]{2})/g);\n      } else if (input_scan.peek() === 'u') {\n        matched = input_scan.match(/u([0-9A-Fa-f]{4})/g);\n        if (!matched) {\n          matched = input_scan.match(/u\\{([0-9A-Fa-f]+)\\}/g);\n        }\n      } else {\n        out += '\\\\';\n        if (input_scan.hasNext()) {\n          out += input_scan.next();\n        }\n        continue;\n      }\n\n      // If there's some error decoding, return the original string\n      if (!matched) {\n        return s;\n      }\n\n      escaped = parseInt(matched[1], 16);\n\n      if (escaped > 0x7e && escaped <= 0xff && matched[0].indexOf('x') === 0) {\n        // we bail out on \\x7f..\\xff,\n        // leaving whole string escaped,\n        // as it's probably completely binary\n        return s;\n      } else if (escaped >= 0x00 && escaped < 0x20) {\n        // leave 0x00...0x1f escaped\n        out += '\\\\' + matched[0];\n      } else if (escaped > 0x10FFFF) {\n        // If the escape sequence is out of bounds, keep the original sequence and continue conversion\n        out += '\\\\' + matched[0];\n      } else if (escaped === 0x22 || escaped === 0x27 || escaped === 0x5c) {\n        // single-quote, apostrophe, backslash - escape these\n        out += '\\\\' + String.fromCharCode(escaped);\n      } else {\n        out += String.fromCharCode(escaped);\n      }\n    }\n  }\n\n  return out;\n}\n\n// handle string\n//\nTokenizer.prototype._read_string_recursive = function(delimiter, allow_unescaped_newlines, start_sub) {\n  var current_char;\n  var pattern;\n  if (delimiter === '\\'') {\n    pattern = this.__patterns.single_quote;\n  } else if (delimiter === '\"') {\n    pattern = this.__patterns.double_quote;\n  } else if (delimiter === '`') {\n    pattern = this.__patterns.template_text;\n  } else if (delimiter === '}') {\n    pattern = this.__patterns.template_expression;\n  }\n\n  var resulting_string = pattern.read();\n  var next = '';\n  while (this._input.hasNext()) {\n    next = this._input.next();\n    if (next === delimiter ||\n      (!allow_unescaped_newlines && acorn.newline.test(next))) {\n      this._input.back();\n      break;\n    } else if (next === '\\\\' && this._input.hasNext()) {\n      current_char = this._input.peek();\n\n      if (current_char === 'x' || current_char === 'u') {\n        this.has_char_escapes = true;\n      } else if (current_char === '\\r' && this._input.peek(1) === '\\n') {\n        this._input.next();\n      }\n      next += this._input.next();\n    } else if (start_sub) {\n      if (start_sub === '${' && next === '$' && this._input.peek() === '{') {\n        next += this._input.next();\n      }\n\n      if (start_sub === next) {\n        if (delimiter === '`') {\n          next += this._read_string_recursive('}', allow_unescaped_newlines, '`');\n        } else {\n          next += this._read_string_recursive('`', allow_unescaped_newlines, '${');\n        }\n        if (this._input.hasNext()) {\n          next += this._input.next();\n        }\n      }\n    }\n    next += pattern.read();\n    resulting_string += next;\n  }\n\n  return resulting_string;\n};\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\nmodule.exports.positionable_operators = positionable_operators.slice();\nmodule.exports.line_starters = line_starters.slice();\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Output = require('../core/output').Output;\nvar Token = require('../core/token').Token;\nvar acorn = require('./acorn');\nvar Options = require('./options').Options;\nvar Tokenizer = require('./tokenizer').Tokenizer;\nvar line_starters = require('./tokenizer').line_starters;\nvar positionable_operators = require('./tokenizer').positionable_operators;\nvar TOKEN = require('./tokenizer').TOKEN;\n\n\nfunction in_array(what, arr) {\n  return arr.indexOf(what) !== -1;\n}\n\nfunction ltrim(s) {\n  return s.replace(/^\\s+/g, '');\n}\n\nfunction generateMapFromStrings(list) {\n  var result = {};\n  for (var x = 0; x < list.length; x++) {\n    // make the mapped names underscored instead of dash\n    result[list[x].replace(/-/g, '_')] = list[x];\n  }\n  return result;\n}\n\nfunction reserved_word(token, word) {\n  return token && token.type === TOKEN.RESERVED && token.text === word;\n}\n\nfunction reserved_array(token, words) {\n  return token && token.type === TOKEN.RESERVED && in_array(token.text, words);\n}\n// Unsure of what they mean, but they work. Worth cleaning up in future.\nvar special_words = ['case', 'return', 'do', 'if', 'throw', 'else', 'await', 'break', 'continue', 'async'];\n\nvar validPositionValues = ['before-newline', 'after-newline', 'preserve-newline'];\n\n// Generate map from array\nvar OPERATOR_POSITION = generateMapFromStrings(validPositionValues);\n\nvar OPERATOR_POSITION_BEFORE_OR_PRESERVE = [OPERATOR_POSITION.before_newline, OPERATOR_POSITION.preserve_newline];\n\nvar MODE = {\n  BlockStatement: 'BlockStatement', // 'BLOCK'\n  Statement: 'Statement', // 'STATEMENT'\n  ObjectLiteral: 'ObjectLiteral', // 'OBJECT',\n  ArrayLiteral: 'ArrayLiteral', //'[EXPRESSION]',\n  ForInitializer: 'ForInitializer', //'(FOR-EXPRESSION)',\n  Conditional: 'Conditional', //'(COND-EXPRESSION)',\n  Expression: 'Expression' //'(EXPRESSION)'\n};\n\nfunction remove_redundant_indentation(output, frame) {\n  // This implementation is effective but has some issues:\n  //     - can cause line wrap to happen too soon due to indent removal\n  //           after wrap points are calculated\n  // These issues are minor compared to ugly indentation.\n\n  if (frame.multiline_frame ||\n    frame.mode === MODE.ForInitializer ||\n    frame.mode === MODE.Conditional) {\n    return;\n  }\n\n  // remove one indent from each line inside this section\n  output.remove_indent(frame.start_line_index);\n}\n\n// we could use just string.split, but\n// IE doesn't like returning empty strings\nfunction split_linebreaks(s) {\n  //return s.split(/\\x0d\\x0a|\\x0a/);\n\n  s = s.replace(acorn.allLineBreaks, '\\n');\n  var out = [],\n    idx = s.indexOf(\"\\n\");\n  while (idx !== -1) {\n    out.push(s.substring(0, idx));\n    s = s.substring(idx + 1);\n    idx = s.indexOf(\"\\n\");\n  }\n  if (s.length) {\n    out.push(s);\n  }\n  return out;\n}\n\nfunction is_array(mode) {\n  return mode === MODE.ArrayLiteral;\n}\n\nfunction is_expression(mode) {\n  return in_array(mode, [MODE.Expression, MODE.ForInitializer, MODE.Conditional]);\n}\n\nfunction all_lines_start_with(lines, c) {\n  for (var i = 0; i < lines.length; i++) {\n    var line = lines[i].trim();\n    if (line.charAt(0) !== c) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction each_line_matches_indent(lines, indent) {\n  var i = 0,\n    len = lines.length,\n    line;\n  for (; i < len; i++) {\n    line = lines[i];\n    // allow empty lines to pass through\n    if (line && line.indexOf(indent) !== 0) {\n      return false;\n    }\n  }\n  return true;\n}\n\n\nfunction Beautifier(source_text, options) {\n  options = options || {};\n  this._source_text = source_text || '';\n\n  this._output = null;\n  this._tokens = null;\n  this._last_last_text = null;\n  this._flags = null;\n  this._previous_flags = null;\n\n  this._flag_store = null;\n  this._options = new Options(options);\n}\n\nBeautifier.prototype.create_flags = function(flags_base, mode) {\n  var next_indent_level = 0;\n  if (flags_base) {\n    next_indent_level = flags_base.indentation_level;\n    if (!this._output.just_added_newline() &&\n      flags_base.line_indent_level > next_indent_level) {\n      next_indent_level = flags_base.line_indent_level;\n    }\n  }\n\n  var next_flags = {\n    mode: mode,\n    parent: flags_base,\n    last_token: flags_base ? flags_base.last_token : new Token(TOKEN.START_BLOCK, ''), // last token text\n    last_word: flags_base ? flags_base.last_word : '', // last TOKEN.WORD passed\n    declaration_statement: false,\n    declaration_assignment: false,\n    multiline_frame: false,\n    inline_frame: false,\n    if_block: false,\n    else_block: false,\n    class_start_block: false, // class A { INSIDE HERE } or class B extends C { INSIDE HERE }\n    do_block: false,\n    do_while: false,\n    import_block: false,\n    in_case_statement: false, // switch(..){ INSIDE HERE }\n    in_case: false, // we're on the exact line with \"case 0:\"\n    case_body: false, // the indented case-action block\n    case_block: false, // the indented case-action block is wrapped with {}\n    indentation_level: next_indent_level,\n    alignment: 0,\n    line_indent_level: flags_base ? flags_base.line_indent_level : next_indent_level,\n    start_line_index: this._output.get_line_number(),\n    ternary_depth: 0\n  };\n  return next_flags;\n};\n\nBeautifier.prototype._reset = function(source_text) {\n  var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n\n  this._last_last_text = ''; // pre-last token text\n  this._output = new Output(this._options, baseIndentString);\n\n  // If testing the ignore directive, start with output disable set to true\n  this._output.raw = this._options.test_output_raw;\n\n\n  // Stack of parsing/formatting states, including MODE.\n  // We tokenize, parse, and output in an almost purely a forward-only stream of token input\n  // and formatted output.  This makes the beautifier less accurate than full parsers\n  // but also far more tolerant of syntax errors.\n  //\n  // For example, the default mode is MODE.BlockStatement. If we see a '{' we push a new frame of type\n  // MODE.BlockStatement on the the stack, even though it could be object literal.  If we later\n  // encounter a \":\", we'll switch to to MODE.ObjectLiteral.  If we then see a \";\",\n  // most full parsers would die, but the beautifier gracefully falls back to\n  // MODE.BlockStatement and continues on.\n  this._flag_store = [];\n  this.set_mode(MODE.BlockStatement);\n  var tokenizer = new Tokenizer(source_text, this._options);\n  this._tokens = tokenizer.tokenize();\n  return source_text;\n};\n\nBeautifier.prototype.beautify = function() {\n  // if disabled, return the input unchanged.\n  if (this._options.disabled) {\n    return this._source_text;\n  }\n\n  var sweet_code;\n  var source_text = this._reset(this._source_text);\n\n  var eol = this._options.eol;\n  if (this._options.eol === 'auto') {\n    eol = '\\n';\n    if (source_text && acorn.lineBreak.test(source_text || '')) {\n      eol = source_text.match(acorn.lineBreak)[0];\n    }\n  }\n\n  var current_token = this._tokens.next();\n  while (current_token) {\n    this.handle_token(current_token);\n\n    this._last_last_text = this._flags.last_token.text;\n    this._flags.last_token = current_token;\n\n    current_token = this._tokens.next();\n  }\n\n  sweet_code = this._output.get_code(eol);\n\n  return sweet_code;\n};\n\nBeautifier.prototype.handle_token = function(current_token, preserve_statement_flags) {\n  if (current_token.type === TOKEN.START_EXPR) {\n    this.handle_start_expr(current_token);\n  } else if (current_token.type === TOKEN.END_EXPR) {\n    this.handle_end_expr(current_token);\n  } else if (current_token.type === TOKEN.START_BLOCK) {\n    this.handle_start_block(current_token);\n  } else if (current_token.type === TOKEN.END_BLOCK) {\n    this.handle_end_block(current_token);\n  } else if (current_token.type === TOKEN.WORD) {\n    this.handle_word(current_token);\n  } else if (current_token.type === TOKEN.RESERVED) {\n    this.handle_word(current_token);\n  } else if (current_token.type === TOKEN.SEMICOLON) {\n    this.handle_semicolon(current_token);\n  } else if (current_token.type === TOKEN.STRING) {\n    this.handle_string(current_token);\n  } else if (current_token.type === TOKEN.EQUALS) {\n    this.handle_equals(current_token);\n  } else if (current_token.type === TOKEN.OPERATOR) {\n    this.handle_operator(current_token);\n  } else if (current_token.type === TOKEN.COMMA) {\n    this.handle_comma(current_token);\n  } else if (current_token.type === TOKEN.BLOCK_COMMENT) {\n    this.handle_block_comment(current_token, preserve_statement_flags);\n  } else if (current_token.type === TOKEN.COMMENT) {\n    this.handle_comment(current_token, preserve_statement_flags);\n  } else if (current_token.type === TOKEN.DOT) {\n    this.handle_dot(current_token);\n  } else if (current_token.type === TOKEN.EOF) {\n    this.handle_eof(current_token);\n  } else if (current_token.type === TOKEN.UNKNOWN) {\n    this.handle_unknown(current_token, preserve_statement_flags);\n  } else {\n    this.handle_unknown(current_token, preserve_statement_flags);\n  }\n};\n\nBeautifier.prototype.handle_whitespace_and_comments = function(current_token, preserve_statement_flags) {\n  var newlines = current_token.newlines;\n  var keep_whitespace = this._options.keep_array_indentation && is_array(this._flags.mode);\n\n  if (current_token.comments_before) {\n    var comment_token = current_token.comments_before.next();\n    while (comment_token) {\n      // The cleanest handling of inline comments is to treat them as though they aren't there.\n      // Just continue formatting and the behavior should be logical.\n      // Also ignore unknown tokens.  Again, this should result in better behavior.\n      this.handle_whitespace_and_comments(comment_token, preserve_statement_flags);\n      this.handle_token(comment_token, preserve_statement_flags);\n      comment_token = current_token.comments_before.next();\n    }\n  }\n\n  if (keep_whitespace) {\n    for (var i = 0; i < newlines; i += 1) {\n      this.print_newline(i > 0, preserve_statement_flags);\n    }\n  } else {\n    if (this._options.max_preserve_newlines && newlines > this._options.max_preserve_newlines) {\n      newlines = this._options.max_preserve_newlines;\n    }\n\n    if (this._options.preserve_newlines) {\n      if (newlines > 1) {\n        this.print_newline(false, preserve_statement_flags);\n        for (var j = 1; j < newlines; j += 1) {\n          this.print_newline(true, preserve_statement_flags);\n        }\n      }\n    }\n  }\n\n};\n\nvar newline_restricted_tokens = ['async', 'break', 'continue', 'return', 'throw', 'yield'];\n\nBeautifier.prototype.allow_wrap_or_preserved_newline = function(current_token, force_linewrap) {\n  force_linewrap = (force_linewrap === undefined) ? false : force_linewrap;\n\n  // Never wrap the first token on a line\n  if (this._output.just_added_newline()) {\n    return;\n  }\n\n  var shouldPreserveOrForce = (this._options.preserve_newlines && current_token.newlines) || force_linewrap;\n  var operatorLogicApplies = in_array(this._flags.last_token.text, positionable_operators) ||\n    in_array(current_token.text, positionable_operators);\n\n  if (operatorLogicApplies) {\n    var shouldPrintOperatorNewline = (\n        in_array(this._flags.last_token.text, positionable_operators) &&\n        in_array(this._options.operator_position, OPERATOR_POSITION_BEFORE_OR_PRESERVE)\n      ) ||\n      in_array(current_token.text, positionable_operators);\n    shouldPreserveOrForce = shouldPreserveOrForce && shouldPrintOperatorNewline;\n  }\n\n  if (shouldPreserveOrForce) {\n    this.print_newline(false, true);\n  } else if (this._options.wrap_line_length) {\n    if (reserved_array(this._flags.last_token, newline_restricted_tokens)) {\n      // These tokens should never have a newline inserted\n      // between them and the following expression.\n      return;\n    }\n    this._output.set_wrap_point();\n  }\n};\n\nBeautifier.prototype.print_newline = function(force_newline, preserve_statement_flags) {\n  if (!preserve_statement_flags) {\n    if (this._flags.last_token.text !== ';' && this._flags.last_token.text !== ',' && this._flags.last_token.text !== '=' && (this._flags.last_token.type !== TOKEN.OPERATOR || this._flags.last_token.text === '--' || this._flags.last_token.text === '++')) {\n      var next_token = this._tokens.peek();\n      while (this._flags.mode === MODE.Statement &&\n        !(this._flags.if_block && reserved_word(next_token, 'else')) &&\n        !this._flags.do_block) {\n        this.restore_mode();\n      }\n    }\n  }\n\n  if (this._output.add_new_line(force_newline)) {\n    this._flags.multiline_frame = true;\n  }\n};\n\nBeautifier.prototype.print_token_line_indentation = function(current_token) {\n  if (this._output.just_added_newline()) {\n    if (this._options.keep_array_indentation &&\n      current_token.newlines &&\n      (current_token.text === '[' || is_array(this._flags.mode))) {\n      this._output.current_line.set_indent(-1);\n      this._output.current_line.push(current_token.whitespace_before);\n      this._output.space_before_token = false;\n    } else if (this._output.set_indent(this._flags.indentation_level, this._flags.alignment)) {\n      this._flags.line_indent_level = this._flags.indentation_level;\n    }\n  }\n};\n\nBeautifier.prototype.print_token = function(current_token) {\n  if (this._output.raw) {\n    this._output.add_raw_token(current_token);\n    return;\n  }\n\n  if (this._options.comma_first && current_token.previous && current_token.previous.type === TOKEN.COMMA &&\n    this._output.just_added_newline()) {\n    if (this._output.previous_line.last() === ',') {\n      var popped = this._output.previous_line.pop();\n      // if the comma was already at the start of the line,\n      // pull back onto that line and reprint the indentation\n      if (this._output.previous_line.is_empty()) {\n        this._output.previous_line.push(popped);\n        this._output.trim(true);\n        this._output.current_line.pop();\n        this._output.trim();\n      }\n\n      // add the comma in front of the next token\n      this.print_token_line_indentation(current_token);\n      this._output.add_token(',');\n      this._output.space_before_token = true;\n    }\n  }\n\n  this.print_token_line_indentation(current_token);\n  this._output.non_breaking_space = true;\n  this._output.add_token(current_token.text);\n  if (this._output.previous_token_wrapped) {\n    this._flags.multiline_frame = true;\n  }\n};\n\nBeautifier.prototype.indent = function() {\n  this._flags.indentation_level += 1;\n  this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n};\n\nBeautifier.prototype.deindent = function() {\n  if (this._flags.indentation_level > 0 &&\n    ((!this._flags.parent) || this._flags.indentation_level > this._flags.parent.indentation_level)) {\n    this._flags.indentation_level -= 1;\n    this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n  }\n};\n\nBeautifier.prototype.set_mode = function(mode) {\n  if (this._flags) {\n    this._flag_store.push(this._flags);\n    this._previous_flags = this._flags;\n  } else {\n    this._previous_flags = this.create_flags(null, mode);\n  }\n\n  this._flags = this.create_flags(this._previous_flags, mode);\n  this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n};\n\n\nBeautifier.prototype.restore_mode = function() {\n  if (this._flag_store.length > 0) {\n    this._previous_flags = this._flags;\n    this._flags = this._flag_store.pop();\n    if (this._previous_flags.mode === MODE.Statement) {\n      remove_redundant_indentation(this._output, this._previous_flags);\n    }\n    this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n  }\n};\n\nBeautifier.prototype.start_of_object_property = function() {\n  return this._flags.parent.mode === MODE.ObjectLiteral && this._flags.mode === MODE.Statement && (\n    (this._flags.last_token.text === ':' && this._flags.ternary_depth === 0) || (reserved_array(this._flags.last_token, ['get', 'set'])));\n};\n\nBeautifier.prototype.start_of_statement = function(current_token) {\n  var start = false;\n  start = start || reserved_array(this._flags.last_token, ['var', 'let', 'const']) && current_token.type === TOKEN.WORD;\n  start = start || reserved_word(this._flags.last_token, 'do');\n  start = start || (!(this._flags.parent.mode === MODE.ObjectLiteral && this._flags.mode === MODE.Statement)) && reserved_array(this._flags.last_token, newline_restricted_tokens) && !current_token.newlines;\n  start = start || reserved_word(this._flags.last_token, 'else') &&\n    !(reserved_word(current_token, 'if') && !current_token.comments_before);\n  start = start || (this._flags.last_token.type === TOKEN.END_EXPR && (this._previous_flags.mode === MODE.ForInitializer || this._previous_flags.mode === MODE.Conditional));\n  start = start || (this._flags.last_token.type === TOKEN.WORD && this._flags.mode === MODE.BlockStatement &&\n    !this._flags.in_case &&\n    !(current_token.text === '--' || current_token.text === '++') &&\n    this._last_last_text !== 'function' &&\n    current_token.type !== TOKEN.WORD && current_token.type !== TOKEN.RESERVED);\n  start = start || (this._flags.mode === MODE.ObjectLiteral && (\n    (this._flags.last_token.text === ':' && this._flags.ternary_depth === 0) || reserved_array(this._flags.last_token, ['get', 'set'])));\n\n  if (start) {\n    this.set_mode(MODE.Statement);\n    this.indent();\n\n    this.handle_whitespace_and_comments(current_token, true);\n\n    // Issue #276:\n    // If starting a new statement with [if, for, while, do], push to a new line.\n    // if (a) if (b) if(c) d(); else e(); else f();\n    if (!this.start_of_object_property()) {\n      this.allow_wrap_or_preserved_newline(current_token,\n        reserved_array(current_token, ['do', 'for', 'if', 'while']));\n    }\n    return true;\n  }\n  return false;\n};\n\nBeautifier.prototype.handle_start_expr = function(current_token) {\n  // The conditional starts the statement if appropriate.\n  if (!this.start_of_statement(current_token)) {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  var next_mode = MODE.Expression;\n  if (current_token.text === '[') {\n\n    if (this._flags.last_token.type === TOKEN.WORD || this._flags.last_token.text === ')') {\n      // this is array index specifier, break immediately\n      // a[x], fn()[x]\n      if (reserved_array(this._flags.last_token, line_starters)) {\n        this._output.space_before_token = true;\n      }\n      this.print_token(current_token);\n      this.set_mode(next_mode);\n      this.indent();\n      if (this._options.space_in_paren) {\n        this._output.space_before_token = true;\n      }\n      return;\n    }\n\n    next_mode = MODE.ArrayLiteral;\n    if (is_array(this._flags.mode)) {\n      if (this._flags.last_token.text === '[' ||\n        (this._flags.last_token.text === ',' && (this._last_last_text === ']' || this._last_last_text === '}'))) {\n        // ], [ goes to new line\n        // }, [ goes to new line\n        if (!this._options.keep_array_indentation) {\n          this.print_newline();\n        }\n      }\n    }\n\n    if (!in_array(this._flags.last_token.type, [TOKEN.START_EXPR, TOKEN.END_EXPR, TOKEN.WORD, TOKEN.OPERATOR, TOKEN.DOT])) {\n      this._output.space_before_token = true;\n    }\n  } else {\n    if (this._flags.last_token.type === TOKEN.RESERVED) {\n      if (this._flags.last_token.text === 'for') {\n        this._output.space_before_token = this._options.space_before_conditional;\n        next_mode = MODE.ForInitializer;\n      } else if (in_array(this._flags.last_token.text, ['if', 'while', 'switch'])) {\n        this._output.space_before_token = this._options.space_before_conditional;\n        next_mode = MODE.Conditional;\n      } else if (in_array(this._flags.last_word, ['await', 'async'])) {\n        // Should be a space between await and an IIFE, or async and an arrow function\n        this._output.space_before_token = true;\n      } else if (this._flags.last_token.text === 'import' && current_token.whitespace_before === '') {\n        this._output.space_before_token = false;\n      } else if (in_array(this._flags.last_token.text, line_starters) || this._flags.last_token.text === 'catch') {\n        this._output.space_before_token = true;\n      }\n    } else if (this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n      // Support of this kind of newline preservation.\n      // a = (b &&\n      //     (c || d));\n      if (!this.start_of_object_property()) {\n        this.allow_wrap_or_preserved_newline(current_token);\n      }\n    } else if (this._flags.last_token.type === TOKEN.WORD) {\n      this._output.space_before_token = false;\n\n      // function name() vs function name ()\n      // function* name() vs function* name ()\n      // async name() vs async name ()\n      // In ES6, you can also define the method properties of an object\n      // var obj = {a: function() {}}\n      // It can be abbreviated\n      // var obj = {a() {}}\n      // var obj = { a() {}} vs var obj = { a () {}}\n      // var obj = { * a() {}} vs var obj = { * a () {}}\n      var peek_back_two = this._tokens.peek(-3);\n      if (this._options.space_after_named_function && peek_back_two) {\n        // peek starts at next character so -1 is current token\n        var peek_back_three = this._tokens.peek(-4);\n        if (reserved_array(peek_back_two, ['async', 'function']) ||\n          (peek_back_two.text === '*' && reserved_array(peek_back_three, ['async', 'function']))) {\n          this._output.space_before_token = true;\n        } else if (this._flags.mode === MODE.ObjectLiteral) {\n          if ((peek_back_two.text === '{' || peek_back_two.text === ',') ||\n            (peek_back_two.text === '*' && (peek_back_three.text === '{' || peek_back_three.text === ','))) {\n            this._output.space_before_token = true;\n          }\n        } else if (this._flags.parent && this._flags.parent.class_start_block) {\n          this._output.space_before_token = true;\n        }\n      }\n    } else {\n      // Support preserving wrapped arrow function expressions\n      // a.b('c',\n      //     () => d.e\n      // )\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n\n    // function() vs function ()\n    // yield*() vs yield* ()\n    // function*() vs function* ()\n    if ((this._flags.last_token.type === TOKEN.RESERVED && (this._flags.last_word === 'function' || this._flags.last_word === 'typeof')) ||\n      (this._flags.last_token.text === '*' &&\n        (in_array(this._last_last_text, ['function', 'yield']) ||\n          (this._flags.mode === MODE.ObjectLiteral && in_array(this._last_last_text, ['{', ',']))))) {\n      this._output.space_before_token = this._options.space_after_anon_function;\n    }\n  }\n\n  if (this._flags.last_token.text === ';' || this._flags.last_token.type === TOKEN.START_BLOCK) {\n    this.print_newline();\n  } else if (this._flags.last_token.type === TOKEN.END_EXPR || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.END_BLOCK || this._flags.last_token.text === '.' || this._flags.last_token.type === TOKEN.COMMA) {\n    // do nothing on (( and )( and ][ and ]( and .(\n    // TODO: Consider whether forcing this is required.  Review failing tests when removed.\n    this.allow_wrap_or_preserved_newline(current_token, current_token.newlines);\n  }\n\n  this.print_token(current_token);\n  this.set_mode(next_mode);\n  if (this._options.space_in_paren) {\n    this._output.space_before_token = true;\n  }\n\n  // In all cases, if we newline while inside an expression it should be indented.\n  this.indent();\n};\n\nBeautifier.prototype.handle_end_expr = function(current_token) {\n  // statements inside expressions are not valid syntax, but...\n  // statements must all be closed when their container closes\n  while (this._flags.mode === MODE.Statement) {\n    this.restore_mode();\n  }\n\n  this.handle_whitespace_and_comments(current_token);\n\n  if (this._flags.multiline_frame) {\n    this.allow_wrap_or_preserved_newline(current_token,\n      current_token.text === ']' && is_array(this._flags.mode) && !this._options.keep_array_indentation);\n  }\n\n  if (this._options.space_in_paren) {\n    if (this._flags.last_token.type === TOKEN.START_EXPR && !this._options.space_in_empty_paren) {\n      // () [] no inner space in empty parens like these, ever, ref #320\n      this._output.trim();\n      this._output.space_before_token = false;\n    } else {\n      this._output.space_before_token = true;\n    }\n  }\n  this.deindent();\n  this.print_token(current_token);\n  this.restore_mode();\n\n  remove_redundant_indentation(this._output, this._previous_flags);\n\n  // do {} while () // no statement required after\n  if (this._flags.do_while && this._previous_flags.mode === MODE.Conditional) {\n    this._previous_flags.mode = MODE.Expression;\n    this._flags.do_block = false;\n    this._flags.do_while = false;\n\n  }\n};\n\nBeautifier.prototype.handle_start_block = function(current_token) {\n  this.handle_whitespace_and_comments(current_token);\n\n  // Check if this is should be treated as a ObjectLiteral\n  var next_token = this._tokens.peek();\n  var second_token = this._tokens.peek(1);\n  if (this._flags.last_word === 'switch' && this._flags.last_token.type === TOKEN.END_EXPR) {\n    this.set_mode(MODE.BlockStatement);\n    this._flags.in_case_statement = true;\n  } else if (this._flags.case_body) {\n    this.set_mode(MODE.BlockStatement);\n  } else if (second_token && (\n      (in_array(second_token.text, [':', ',']) && in_array(next_token.type, [TOKEN.STRING, TOKEN.WORD, TOKEN.RESERVED])) ||\n      (in_array(next_token.text, ['get', 'set', '...']) && in_array(second_token.type, [TOKEN.WORD, TOKEN.RESERVED]))\n    )) {\n    // We don't support TypeScript,but we didn't break it for a very long time.\n    // We'll try to keep not breaking it.\n    if (in_array(this._last_last_text, ['class', 'interface']) && !in_array(second_token.text, [':', ','])) {\n      this.set_mode(MODE.BlockStatement);\n    } else {\n      this.set_mode(MODE.ObjectLiteral);\n    }\n  } else if (this._flags.last_token.type === TOKEN.OPERATOR && this._flags.last_token.text === '=>') {\n    // arrow function: (param1, paramN) => { statements }\n    this.set_mode(MODE.BlockStatement);\n  } else if (in_array(this._flags.last_token.type, [TOKEN.EQUALS, TOKEN.START_EXPR, TOKEN.COMMA, TOKEN.OPERATOR]) ||\n    reserved_array(this._flags.last_token, ['return', 'throw', 'import', 'default'])\n  ) {\n    // Detecting shorthand function syntax is difficult by scanning forward,\n    //     so check the surrounding context.\n    // If the block is being returned, imported, export default, passed as arg,\n    //     assigned with = or assigned in a nested object, treat as an ObjectLiteral.\n    this.set_mode(MODE.ObjectLiteral);\n  } else {\n    this.set_mode(MODE.BlockStatement);\n  }\n\n  if (this._flags.last_token) {\n    if (reserved_array(this._flags.last_token.previous, ['class', 'extends'])) {\n      this._flags.class_start_block = true;\n    }\n  }\n\n  var empty_braces = !next_token.comments_before && next_token.text === '}';\n  var empty_anonymous_function = empty_braces && this._flags.last_word === 'function' &&\n    this._flags.last_token.type === TOKEN.END_EXPR;\n\n  if (this._options.brace_preserve_inline) // check for inline, set inline_frame if so\n  {\n    // search forward for a newline wanted inside this block\n    var index = 0;\n    var check_token = null;\n    this._flags.inline_frame = true;\n    do {\n      index += 1;\n      check_token = this._tokens.peek(index - 1);\n      if (check_token.newlines) {\n        this._flags.inline_frame = false;\n        break;\n      }\n    } while (check_token.type !== TOKEN.EOF &&\n      !(check_token.type === TOKEN.END_BLOCK && check_token.opened === current_token));\n  }\n\n  if ((this._options.brace_style === \"expand\" ||\n      (this._options.brace_style === \"none\" && current_token.newlines)) &&\n    !this._flags.inline_frame) {\n    if (this._flags.last_token.type !== TOKEN.OPERATOR &&\n      (empty_anonymous_function ||\n        this._flags.last_token.type === TOKEN.EQUALS ||\n        (reserved_array(this._flags.last_token, special_words) && this._flags.last_token.text !== 'else'))) {\n      this._output.space_before_token = true;\n    } else {\n      this.print_newline(false, true);\n    }\n  } else { // collapse || inline_frame\n    if (is_array(this._previous_flags.mode) && (this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.COMMA)) {\n      if (this._flags.last_token.type === TOKEN.COMMA || this._options.space_in_paren) {\n        this._output.space_before_token = true;\n      }\n\n      if (this._flags.last_token.type === TOKEN.COMMA || (this._flags.last_token.type === TOKEN.START_EXPR && this._flags.inline_frame)) {\n        this.allow_wrap_or_preserved_newline(current_token);\n        this._previous_flags.multiline_frame = this._previous_flags.multiline_frame || this._flags.multiline_frame;\n        this._flags.multiline_frame = false;\n      }\n    }\n    if (this._flags.last_token.type !== TOKEN.OPERATOR && this._flags.last_token.type !== TOKEN.START_EXPR) {\n      if (in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.SEMICOLON]) && !this._flags.inline_frame) {\n        this.print_newline();\n      } else {\n        this._output.space_before_token = true;\n      }\n    }\n  }\n  this.print_token(current_token);\n  this.indent();\n\n  // Except for specific cases, open braces are followed by a new line.\n  if (!empty_braces && !(this._options.brace_preserve_inline && this._flags.inline_frame)) {\n    this.print_newline();\n  }\n};\n\nBeautifier.prototype.handle_end_block = function(current_token) {\n  // statements must all be closed when their container closes\n  this.handle_whitespace_and_comments(current_token);\n\n  while (this._flags.mode === MODE.Statement) {\n    this.restore_mode();\n  }\n\n  var empty_braces = this._flags.last_token.type === TOKEN.START_BLOCK;\n\n  if (this._flags.inline_frame && !empty_braces) { // try inline_frame (only set if this._options.braces-preserve-inline) first\n    this._output.space_before_token = true;\n  } else if (this._options.brace_style === \"expand\") {\n    if (!empty_braces) {\n      this.print_newline();\n    }\n  } else {\n    // skip {}\n    if (!empty_braces) {\n      if (is_array(this._flags.mode) && this._options.keep_array_indentation) {\n        // we REALLY need a newline here, but newliner would skip that\n        this._options.keep_array_indentation = false;\n        this.print_newline();\n        this._options.keep_array_indentation = true;\n\n      } else {\n        this.print_newline();\n      }\n    }\n  }\n  this.restore_mode();\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_word = function(current_token) {\n  if (current_token.type === TOKEN.RESERVED) {\n    if (in_array(current_token.text, ['set', 'get']) && this._flags.mode !== MODE.ObjectLiteral) {\n      current_token.type = TOKEN.WORD;\n    } else if (current_token.text === 'import' && in_array(this._tokens.peek().text, ['(', '.'])) {\n      current_token.type = TOKEN.WORD;\n    } else if (in_array(current_token.text, ['as', 'from']) && !this._flags.import_block) {\n      current_token.type = TOKEN.WORD;\n    } else if (this._flags.mode === MODE.ObjectLiteral) {\n      var next_token = this._tokens.peek();\n      if (next_token.text === ':') {\n        current_token.type = TOKEN.WORD;\n      }\n    }\n  }\n\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n    if (reserved_array(this._flags.last_token, ['var', 'let', 'const']) && current_token.type === TOKEN.WORD) {\n      this._flags.declaration_statement = true;\n    }\n  } else if (current_token.newlines && !is_expression(this._flags.mode) &&\n    (this._flags.last_token.type !== TOKEN.OPERATOR || (this._flags.last_token.text === '--' || this._flags.last_token.text === '++')) &&\n    this._flags.last_token.type !== TOKEN.EQUALS &&\n    (this._options.preserve_newlines || !reserved_array(this._flags.last_token, ['var', 'let', 'const', 'set', 'get']))) {\n    this.handle_whitespace_and_comments(current_token);\n    this.print_newline();\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  if (this._flags.do_block && !this._flags.do_while) {\n    if (reserved_word(current_token, 'while')) {\n      // do {} ## while ()\n      this._output.space_before_token = true;\n      this.print_token(current_token);\n      this._output.space_before_token = true;\n      this._flags.do_while = true;\n      return;\n    } else {\n      // do {} should always have while as the next word.\n      // if we don't see the expected while, recover\n      this.print_newline();\n      this._flags.do_block = false;\n    }\n  }\n\n  // if may be followed by else, or not\n  // Bare/inline ifs are tricky\n  // Need to unwind the modes correctly: if (a) if (b) c(); else d(); else e();\n  if (this._flags.if_block) {\n    if (!this._flags.else_block && reserved_word(current_token, 'else')) {\n      this._flags.else_block = true;\n    } else {\n      while (this._flags.mode === MODE.Statement) {\n        this.restore_mode();\n      }\n      this._flags.if_block = false;\n      this._flags.else_block = false;\n    }\n  }\n\n  if (this._flags.in_case_statement && reserved_array(current_token, ['case', 'default'])) {\n    this.print_newline();\n    if (!this._flags.case_block && (this._flags.case_body || this._options.jslint_happy)) {\n      // switch cases following one another\n      this.deindent();\n    }\n    this._flags.case_body = false;\n\n    this.print_token(current_token);\n    this._flags.in_case = true;\n    return;\n  }\n\n  if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n    if (!this.start_of_object_property() && !(\n        // start of object property is different for numeric values with +/- prefix operators\n        in_array(this._flags.last_token.text, ['+', '-']) && this._last_last_text === ':' && this._flags.parent.mode === MODE.ObjectLiteral)) {\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n  }\n\n  if (reserved_word(current_token, 'function')) {\n    if (in_array(this._flags.last_token.text, ['}', ';']) ||\n      (this._output.just_added_newline() && !(in_array(this._flags.last_token.text, ['(', '[', '{', ':', '=', ',']) || this._flags.last_token.type === TOKEN.OPERATOR))) {\n      // make sure there is a nice clean space of at least one blank line\n      // before a new function definition\n      if (!this._output.just_added_blankline() && !current_token.comments_before) {\n        this.print_newline();\n        this.print_newline(true);\n      }\n    }\n    if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD) {\n      if (reserved_array(this._flags.last_token, ['get', 'set', 'new', 'export']) ||\n        reserved_array(this._flags.last_token, newline_restricted_tokens)) {\n        this._output.space_before_token = true;\n      } else if (reserved_word(this._flags.last_token, 'default') && this._last_last_text === 'export') {\n        this._output.space_before_token = true;\n      } else if (this._flags.last_token.text === 'declare') {\n        // accomodates Typescript declare function formatting\n        this._output.space_before_token = true;\n      } else {\n        this.print_newline();\n      }\n    } else if (this._flags.last_token.type === TOKEN.OPERATOR || this._flags.last_token.text === '=') {\n      // foo = function\n      this._output.space_before_token = true;\n    } else if (!this._flags.multiline_frame && (is_expression(this._flags.mode) || is_array(this._flags.mode))) {\n      // (function\n    } else {\n      this.print_newline();\n    }\n\n    this.print_token(current_token);\n    this._flags.last_word = current_token.text;\n    return;\n  }\n\n  var prefix = 'NONE';\n\n  if (this._flags.last_token.type === TOKEN.END_BLOCK) {\n\n    if (this._previous_flags.inline_frame) {\n      prefix = 'SPACE';\n    } else if (!reserved_array(current_token, ['else', 'catch', 'finally', 'from'])) {\n      prefix = 'NEWLINE';\n    } else {\n      if (this._options.brace_style === \"expand\" ||\n        this._options.brace_style === \"end-expand\" ||\n        (this._options.brace_style === \"none\" && current_token.newlines)) {\n        prefix = 'NEWLINE';\n      } else {\n        prefix = 'SPACE';\n        this._output.space_before_token = true;\n      }\n    }\n  } else if (this._flags.last_token.type === TOKEN.SEMICOLON && this._flags.mode === MODE.BlockStatement) {\n    // TODO: Should this be for STATEMENT as well?\n    prefix = 'NEWLINE';\n  } else if (this._flags.last_token.type === TOKEN.SEMICOLON && is_expression(this._flags.mode)) {\n    prefix = 'SPACE';\n  } else if (this._flags.last_token.type === TOKEN.STRING) {\n    prefix = 'NEWLINE';\n  } else if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD ||\n    (this._flags.last_token.text === '*' &&\n      (in_array(this._last_last_text, ['function', 'yield']) ||\n        (this._flags.mode === MODE.ObjectLiteral && in_array(this._last_last_text, ['{', ',']))))) {\n    prefix = 'SPACE';\n  } else if (this._flags.last_token.type === TOKEN.START_BLOCK) {\n    if (this._flags.inline_frame) {\n      prefix = 'SPACE';\n    } else {\n      prefix = 'NEWLINE';\n    }\n  } else if (this._flags.last_token.type === TOKEN.END_EXPR) {\n    this._output.space_before_token = true;\n    prefix = 'NEWLINE';\n  }\n\n  if (reserved_array(current_token, line_starters) && this._flags.last_token.text !== ')') {\n    if (this._flags.inline_frame || this._flags.last_token.text === 'else' || this._flags.last_token.text === 'export') {\n      prefix = 'SPACE';\n    } else {\n      prefix = 'NEWLINE';\n    }\n\n  }\n\n  if (reserved_array(current_token, ['else', 'catch', 'finally'])) {\n    if ((!(this._flags.last_token.type === TOKEN.END_BLOCK && this._previous_flags.mode === MODE.BlockStatement) ||\n        this._options.brace_style === \"expand\" ||\n        this._options.brace_style === \"end-expand\" ||\n        (this._options.brace_style === \"none\" && current_token.newlines)) &&\n      !this._flags.inline_frame) {\n      this.print_newline();\n    } else {\n      this._output.trim(true);\n      var line = this._output.current_line;\n      // If we trimmed and there's something other than a close block before us\n      // put a newline back in.  Handles '} // comment' scenario.\n      if (line.last() !== '}') {\n        this.print_newline();\n      }\n      this._output.space_before_token = true;\n    }\n  } else if (prefix === 'NEWLINE') {\n    if (reserved_array(this._flags.last_token, special_words)) {\n      // no newline between 'return nnn'\n      this._output.space_before_token = true;\n    } else if (this._flags.last_token.text === 'declare' && reserved_array(current_token, ['var', 'let', 'const'])) {\n      // accomodates Typescript declare formatting\n      this._output.space_before_token = true;\n    } else if (this._flags.last_token.type !== TOKEN.END_EXPR) {\n      if ((this._flags.last_token.type !== TOKEN.START_EXPR || !reserved_array(current_token, ['var', 'let', 'const'])) && this._flags.last_token.text !== ':') {\n        // no need to force newline on 'var': for (var x = 0...)\n        if (reserved_word(current_token, 'if') && reserved_word(current_token.previous, 'else')) {\n          // no newline for } else if {\n          this._output.space_before_token = true;\n        } else {\n          this.print_newline();\n        }\n      }\n    } else if (reserved_array(current_token, line_starters) && this._flags.last_token.text !== ')') {\n      this.print_newline();\n    }\n  } else if (this._flags.multiline_frame && is_array(this._flags.mode) && this._flags.last_token.text === ',' && this._last_last_text === '}') {\n    this.print_newline(); // }, in lists get a newline treatment\n  } else if (prefix === 'SPACE') {\n    this._output.space_before_token = true;\n  }\n  if (current_token.previous && (current_token.previous.type === TOKEN.WORD || current_token.previous.type === TOKEN.RESERVED)) {\n    this._output.space_before_token = true;\n  }\n  this.print_token(current_token);\n  this._flags.last_word = current_token.text;\n\n  if (current_token.type === TOKEN.RESERVED) {\n    if (current_token.text === 'do') {\n      this._flags.do_block = true;\n    } else if (current_token.text === 'if') {\n      this._flags.if_block = true;\n    } else if (current_token.text === 'import') {\n      this._flags.import_block = true;\n    } else if (this._flags.import_block && reserved_word(current_token, 'from')) {\n      this._flags.import_block = false;\n    }\n  }\n};\n\nBeautifier.prototype.handle_semicolon = function(current_token) {\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n    // Semicolon can be the start (and end) of a statement\n    this._output.space_before_token = false;\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  var next_token = this._tokens.peek();\n  while (this._flags.mode === MODE.Statement &&\n    !(this._flags.if_block && reserved_word(next_token, 'else')) &&\n    !this._flags.do_block) {\n    this.restore_mode();\n  }\n\n  // hacky but effective for the moment\n  if (this._flags.import_block) {\n    this._flags.import_block = false;\n  }\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_string = function(current_token) {\n  if (current_token.text.startsWith(\"`\") && current_token.newlines === 0 && current_token.whitespace_before === '' && (current_token.previous.text === ')' || this._flags.last_token.type === TOKEN.WORD)) {\n    //Conditional for detectign backtick strings\n  } else if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n    // One difference - strings want at least a space before\n    this._output.space_before_token = true;\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n    if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD || this._flags.inline_frame) {\n      this._output.space_before_token = true;\n    } else if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n      if (!this.start_of_object_property()) {\n        this.allow_wrap_or_preserved_newline(current_token);\n      }\n    } else if ((current_token.text.startsWith(\"`\") && this._flags.last_token.type === TOKEN.END_EXPR && (current_token.previous.text === ']' || current_token.previous.text === ')') && current_token.newlines === 0)) {\n      this._output.space_before_token = true;\n    } else {\n      this.print_newline();\n    }\n  }\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_equals = function(current_token) {\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  if (this._flags.declaration_statement) {\n    // just got an '=' in a var-line, different formatting/line-breaking, etc will now be done\n    this._flags.declaration_assignment = true;\n  }\n  this._output.space_before_token = true;\n  this.print_token(current_token);\n  this._output.space_before_token = true;\n};\n\nBeautifier.prototype.handle_comma = function(current_token) {\n  this.handle_whitespace_and_comments(current_token, true);\n\n  this.print_token(current_token);\n  this._output.space_before_token = true;\n  if (this._flags.declaration_statement) {\n    if (is_expression(this._flags.parent.mode)) {\n      // do not break on comma, for(var a = 1, b = 2)\n      this._flags.declaration_assignment = false;\n    }\n\n    if (this._flags.declaration_assignment) {\n      this._flags.declaration_assignment = false;\n      this.print_newline(false, true);\n    } else if (this._options.comma_first) {\n      // for comma-first, we want to allow a newline before the comma\n      // to turn into a newline after the comma, which we will fixup later\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n  } else if (this._flags.mode === MODE.ObjectLiteral ||\n    (this._flags.mode === MODE.Statement && this._flags.parent.mode === MODE.ObjectLiteral)) {\n    if (this._flags.mode === MODE.Statement) {\n      this.restore_mode();\n    }\n\n    if (!this._flags.inline_frame) {\n      this.print_newline();\n    }\n  } else if (this._options.comma_first) {\n    // EXPR or DO_BLOCK\n    // for comma-first, we want to allow a newline before the comma\n    // to turn into a newline after the comma, which we will fixup later\n    this.allow_wrap_or_preserved_newline(current_token);\n  }\n};\n\nBeautifier.prototype.handle_operator = function(current_token) {\n  var isGeneratorAsterisk = current_token.text === '*' &&\n    (reserved_array(this._flags.last_token, ['function', 'yield']) ||\n      (in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.COMMA, TOKEN.END_BLOCK, TOKEN.SEMICOLON]))\n    );\n  var isUnary = in_array(current_token.text, ['-', '+']) && (\n    in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.START_EXPR, TOKEN.EQUALS, TOKEN.OPERATOR]) ||\n    in_array(this._flags.last_token.text, line_starters) ||\n    this._flags.last_token.text === ','\n  );\n\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n  } else {\n    var preserve_statement_flags = !isGeneratorAsterisk;\n    this.handle_whitespace_and_comments(current_token, preserve_statement_flags);\n  }\n\n  // hack for actionscript's import .*;\n  if (current_token.text === '*' && this._flags.last_token.type === TOKEN.DOT) {\n    this.print_token(current_token);\n    return;\n  }\n\n  if (current_token.text === '::') {\n    // no spaces around exotic namespacing syntax operator\n    this.print_token(current_token);\n    return;\n  }\n\n  if (in_array(current_token.text, ['-', '+']) && this.start_of_object_property()) {\n    // numeric value with +/- symbol in front as a property\n    this.print_token(current_token);\n    return;\n  }\n\n  // Allow line wrapping between operators when operator_position is\n  //   set to before or preserve\n  if (this._flags.last_token.type === TOKEN.OPERATOR && in_array(this._options.operator_position, OPERATOR_POSITION_BEFORE_OR_PRESERVE)) {\n    this.allow_wrap_or_preserved_newline(current_token);\n  }\n\n  if (current_token.text === ':' && this._flags.in_case) {\n    this.print_token(current_token);\n\n    this._flags.in_case = false;\n    this._flags.case_body = true;\n    if (this._tokens.peek().type !== TOKEN.START_BLOCK) {\n      this.indent();\n      this.print_newline();\n      this._flags.case_block = false;\n    } else {\n      this._flags.case_block = true;\n      this._output.space_before_token = true;\n    }\n    return;\n  }\n\n  var space_before = true;\n  var space_after = true;\n  var in_ternary = false;\n  if (current_token.text === ':') {\n    if (this._flags.ternary_depth === 0) {\n      // Colon is invalid javascript outside of ternary and object, but do our best to guess what was meant.\n      space_before = false;\n    } else {\n      this._flags.ternary_depth -= 1;\n      in_ternary = true;\n    }\n  } else if (current_token.text === '?') {\n    this._flags.ternary_depth += 1;\n  }\n\n  // let's handle the operator_position option prior to any conflicting logic\n  if (!isUnary && !isGeneratorAsterisk && this._options.preserve_newlines && in_array(current_token.text, positionable_operators)) {\n    var isColon = current_token.text === ':';\n    var isTernaryColon = (isColon && in_ternary);\n    var isOtherColon = (isColon && !in_ternary);\n\n    switch (this._options.operator_position) {\n      case OPERATOR_POSITION.before_newline:\n        // if the current token is : and it's not a ternary statement then we set space_before to false\n        this._output.space_before_token = !isOtherColon;\n\n        this.print_token(current_token);\n\n        if (!isColon || isTernaryColon) {\n          this.allow_wrap_or_preserved_newline(current_token);\n        }\n\n        this._output.space_before_token = true;\n        return;\n\n      case OPERATOR_POSITION.after_newline:\n        // if the current token is anything but colon, or (via deduction) it's a colon and in a ternary statement,\n        //   then print a newline.\n\n        this._output.space_before_token = true;\n\n        if (!isColon || isTernaryColon) {\n          if (this._tokens.peek().newlines) {\n            this.print_newline(false, true);\n          } else {\n            this.allow_wrap_or_preserved_newline(current_token);\n          }\n        } else {\n          this._output.space_before_token = false;\n        }\n\n        this.print_token(current_token);\n\n        this._output.space_before_token = true;\n        return;\n\n      case OPERATOR_POSITION.preserve_newline:\n        if (!isOtherColon) {\n          this.allow_wrap_or_preserved_newline(current_token);\n        }\n\n        // if we just added a newline, or the current token is : and it's not a ternary statement,\n        //   then we set space_before to false\n        space_before = !(this._output.just_added_newline() || isOtherColon);\n\n        this._output.space_before_token = space_before;\n        this.print_token(current_token);\n        this._output.space_before_token = true;\n        return;\n    }\n  }\n\n  if (isGeneratorAsterisk) {\n    this.allow_wrap_or_preserved_newline(current_token);\n    space_before = false;\n    var next_token = this._tokens.peek();\n    space_after = next_token && in_array(next_token.type, [TOKEN.WORD, TOKEN.RESERVED]);\n  } else if (current_token.text === '...') {\n    this.allow_wrap_or_preserved_newline(current_token);\n    space_before = this._flags.last_token.type === TOKEN.START_BLOCK;\n    space_after = false;\n  } else if (in_array(current_token.text, ['--', '++', '!', '~']) || isUnary) {\n    // unary operators (and binary +/- pretending to be unary) special cases\n    if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR) {\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n\n    space_before = false;\n    space_after = false;\n\n    // http://www.ecma-international.org/ecma-262/5.1/#sec-7.9.1\n    // if there is a newline between -- or ++ and anything else we should preserve it.\n    if (current_token.newlines && (current_token.text === '--' || current_token.text === '++' || current_token.text === '~')) {\n      var new_line_needed = reserved_array(this._flags.last_token, special_words) && current_token.newlines;\n      if (new_line_needed && (this._previous_flags.if_block || this._previous_flags.else_block)) {\n        this.restore_mode();\n      }\n      this.print_newline(new_line_needed, true);\n    }\n\n    if (this._flags.last_token.text === ';' && is_expression(this._flags.mode)) {\n      // for (;; ++i)\n      //        ^^^\n      space_before = true;\n    }\n\n    if (this._flags.last_token.type === TOKEN.RESERVED) {\n      space_before = true;\n    } else if (this._flags.last_token.type === TOKEN.END_EXPR) {\n      space_before = !(this._flags.last_token.text === ']' && (current_token.text === '--' || current_token.text === '++'));\n    } else if (this._flags.last_token.type === TOKEN.OPERATOR) {\n      // a++ + ++b;\n      // a - -b\n      space_before = in_array(current_token.text, ['--', '-', '++', '+']) && in_array(this._flags.last_token.text, ['--', '-', '++', '+']);\n      // + and - are not unary when preceeded by -- or ++ operator\n      // a-- + b\n      // a * +b\n      // a - -b\n      if (in_array(current_token.text, ['+', '-']) && in_array(this._flags.last_token.text, ['--', '++'])) {\n        space_after = true;\n      }\n    }\n\n\n    if (((this._flags.mode === MODE.BlockStatement && !this._flags.inline_frame) || this._flags.mode === MODE.Statement) &&\n      (this._flags.last_token.text === '{' || this._flags.last_token.text === ';')) {\n      // { foo; --i }\n      // foo(); --bar;\n      this.print_newline();\n    }\n  }\n\n  this._output.space_before_token = this._output.space_before_token || space_before;\n  this.print_token(current_token);\n  this._output.space_before_token = space_after;\n};\n\nBeautifier.prototype.handle_block_comment = function(current_token, preserve_statement_flags) {\n  if (this._output.raw) {\n    this._output.add_raw_token(current_token);\n    if (current_token.directives && current_token.directives.preserve === 'end') {\n      // If we're testing the raw output behavior, do not allow a directive to turn it off.\n      this._output.raw = this._options.test_output_raw;\n    }\n    return;\n  }\n\n  if (current_token.directives) {\n    this.print_newline(false, preserve_statement_flags);\n    this.print_token(current_token);\n    if (current_token.directives.preserve === 'start') {\n      this._output.raw = true;\n    }\n    this.print_newline(false, true);\n    return;\n  }\n\n  // inline block\n  if (!acorn.newline.test(current_token.text) && !current_token.newlines) {\n    this._output.space_before_token = true;\n    this.print_token(current_token);\n    this._output.space_before_token = true;\n    return;\n  } else {\n    this.print_block_commment(current_token, preserve_statement_flags);\n  }\n};\n\nBeautifier.prototype.print_block_commment = function(current_token, preserve_statement_flags) {\n  var lines = split_linebreaks(current_token.text);\n  var j; // iterator for this case\n  var javadoc = false;\n  var starless = false;\n  var lastIndent = current_token.whitespace_before;\n  var lastIndentLength = lastIndent.length;\n\n  // block comment starts with a new line\n  this.print_newline(false, preserve_statement_flags);\n\n  // first line always indented\n  this.print_token_line_indentation(current_token);\n  this._output.add_token(lines[0]);\n  this.print_newline(false, preserve_statement_flags);\n\n\n  if (lines.length > 1) {\n    lines = lines.slice(1);\n    javadoc = all_lines_start_with(lines, '*');\n    starless = each_line_matches_indent(lines, lastIndent);\n\n    if (javadoc) {\n      this._flags.alignment = 1;\n    }\n\n    for (j = 0; j < lines.length; j++) {\n      if (javadoc) {\n        // javadoc: reformat and re-indent\n        this.print_token_line_indentation(current_token);\n        this._output.add_token(ltrim(lines[j]));\n      } else if (starless && lines[j]) {\n        // starless: re-indent non-empty content, avoiding trim\n        this.print_token_line_indentation(current_token);\n        this._output.add_token(lines[j].substring(lastIndentLength));\n      } else {\n        // normal comments output raw\n        this._output.current_line.set_indent(-1);\n        this._output.add_token(lines[j]);\n      }\n\n      // for comments on their own line or  more than one line, make sure there's a new line after\n      this.print_newline(false, preserve_statement_flags);\n    }\n\n    this._flags.alignment = 0;\n  }\n};\n\n\nBeautifier.prototype.handle_comment = function(current_token, preserve_statement_flags) {\n  if (current_token.newlines) {\n    this.print_newline(false, preserve_statement_flags);\n  } else {\n    this._output.trim(true);\n  }\n\n  this._output.space_before_token = true;\n  this.print_token(current_token);\n  this.print_newline(false, preserve_statement_flags);\n};\n\nBeautifier.prototype.handle_dot = function(current_token) {\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n  } else {\n    this.handle_whitespace_and_comments(current_token, true);\n  }\n\n  if (this._flags.last_token.text.match('^[0-9]+$')) {\n    this._output.space_before_token = true;\n  }\n\n  if (reserved_array(this._flags.last_token, special_words)) {\n    this._output.space_before_token = false;\n  } else {\n    // allow preserved newlines before dots in general\n    // force newlines on dots after close paren when break_chained - for bar().baz()\n    this.allow_wrap_or_preserved_newline(current_token,\n      this._flags.last_token.text === ')' && this._options.break_chained_methods);\n  }\n\n  // Only unindent chained method dot if this dot starts a new line.\n  // Otherwise the automatic extra indentation removal will handle the over indent\n  if (this._options.unindent_chained_methods && this._output.just_added_newline()) {\n    this.deindent();\n  }\n\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_unknown = function(current_token, preserve_statement_flags) {\n  this.print_token(current_token);\n\n  if (current_token.text[current_token.text.length - 1] === '\\n') {\n    this.print_newline(false, preserve_statement_flags);\n  }\n};\n\nBeautifier.prototype.handle_eof = function(current_token) {\n  // Unwind any open statements\n  while (this._flags.mode === MODE.Statement) {\n    this.restore_mode();\n  }\n  this.handle_whitespace_and_comments(current_token);\n};\n\nmodule.exports.Beautifier = Beautifier;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Beautifier = require('./beautifier').Beautifier,\n  Options = require('./options').Options;\n\nfunction js_beautify(js_source_text, options) {\n  var beautifier = new Beautifier(js_source_text, options);\n  return beautifier.beautify();\n}\n\nmodule.exports = js_beautify;\nmodule.exports.defaultOptions = function() {\n  return new Options();\n};\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar BaseOptions = require('../core/options').Options;\n\nfunction Options(options) {\n  BaseOptions.call(this, options, 'css');\n\n  this.selector_separator_newline = this._get_boolean('selector_separator_newline', true);\n  this.newline_between_rules = this._get_boolean('newline_between_rules', true);\n  var space_around_selector_separator = this._get_boolean('space_around_selector_separator');\n  this.space_around_combinator = this._get_boolean('space_around_combinator') || space_around_selector_separator;\n\n  var brace_style_split = this._get_selection_list('brace_style', ['collapse', 'expand', 'end-expand', 'none', 'preserve-inline']);\n  this.brace_style = 'collapse';\n  for (var bs = 0; bs < brace_style_split.length; bs++) {\n    if (brace_style_split[bs] !== 'expand') {\n      // default to collapse, as only collapse|expand is implemented for now\n      this.brace_style = 'collapse';\n    } else {\n      this.brace_style = brace_style_split[bs];\n    }\n  }\n}\nOptions.prototype = new BaseOptions();\n\n\n\nmodule.exports.Options = Options;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Options = require('./options').Options;\nvar Output = require('../core/output').Output;\nvar InputScanner = require('../core/inputscanner').InputScanner;\nvar Directives = require('../core/directives').Directives;\n\nvar directives_core = new Directives(/\\/\\*/, /\\*\\//);\n\nvar lineBreak = /\\r\\n|[\\r\\n]/;\nvar allLineBreaks = /\\r\\n|[\\r\\n]/g;\n\n// tokenizer\nvar whitespaceChar = /\\s/;\nvar whitespacePattern = /(?:\\s|\\n)+/g;\nvar block_comment_pattern = /\\/\\*(?:[\\s\\S]*?)((?:\\*\\/)|$)/g;\nvar comment_pattern = /\\/\\/(?:[^\\n\\r\\u2028\\u2029]*)/g;\n\nfunction Beautifier(source_text, options) {\n  this._source_text = source_text || '';\n  // Allow the setting of language/file-type specific options\n  // with inheritance of overall settings\n  this._options = new Options(options);\n  this._ch = null;\n  this._input = null;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/At-rule\n  this.NESTED_AT_RULE = {\n    \"page\": true,\n    \"font-face\": true,\n    \"keyframes\": true,\n    // also in CONDITIONAL_GROUP_RULE below\n    \"media\": true,\n    \"supports\": true,\n    \"document\": true\n  };\n  this.CONDITIONAL_GROUP_RULE = {\n    \"media\": true,\n    \"supports\": true,\n    \"document\": true\n  };\n  this.NON_SEMICOLON_NEWLINE_PROPERTY = [\n    \"grid-template-areas\",\n    \"grid-template\"\n  ];\n\n}\n\nBeautifier.prototype.eatString = function(endChars) {\n  var result = '';\n  this._ch = this._input.next();\n  while (this._ch) {\n    result += this._ch;\n    if (this._ch === \"\\\\\") {\n      result += this._input.next();\n    } else if (endChars.indexOf(this._ch) !== -1 || this._ch === \"\\n\") {\n      break;\n    }\n    this._ch = this._input.next();\n  }\n  return result;\n};\n\n// Skips any white space in the source text from the current position.\n// When allowAtLeastOneNewLine is true, will output new lines for each\n// newline character found; if the user has preserve_newlines off, only\n// the first newline will be output\nBeautifier.prototype.eatWhitespace = function(allowAtLeastOneNewLine) {\n  var result = whitespaceChar.test(this._input.peek());\n  var newline_count = 0;\n  while (whitespaceChar.test(this._input.peek())) {\n    this._ch = this._input.next();\n    if (allowAtLeastOneNewLine && this._ch === '\\n') {\n      if (newline_count === 0 || newline_count < this._options.max_preserve_newlines) {\n        newline_count++;\n        this._output.add_new_line(true);\n      }\n    }\n  }\n  return result;\n};\n\n// Nested pseudo-class if we are insideRule\n// and the next special character found opens\n// a new block\nBeautifier.prototype.foundNestedPseudoClass = function() {\n  var openParen = 0;\n  var i = 1;\n  var ch = this._input.peek(i);\n  while (ch) {\n    if (ch === \"{\") {\n      return true;\n    } else if (ch === '(') {\n      // pseudoclasses can contain ()\n      openParen += 1;\n    } else if (ch === ')') {\n      if (openParen === 0) {\n        return false;\n      }\n      openParen -= 1;\n    } else if (ch === \";\" || ch === \"}\") {\n      return false;\n    }\n    i++;\n    ch = this._input.peek(i);\n  }\n  return false;\n};\n\nBeautifier.prototype.print_string = function(output_string) {\n  this._output.set_indent(this._indentLevel);\n  this._output.non_breaking_space = true;\n  this._output.add_token(output_string);\n};\n\nBeautifier.prototype.preserveSingleSpace = function(isAfterSpace) {\n  if (isAfterSpace) {\n    this._output.space_before_token = true;\n  }\n};\n\nBeautifier.prototype.indent = function() {\n  this._indentLevel++;\n};\n\nBeautifier.prototype.outdent = function() {\n  if (this._indentLevel > 0) {\n    this._indentLevel--;\n  }\n};\n\n/*_____________________--------------------_____________________*/\n\nBeautifier.prototype.beautify = function() {\n  if (this._options.disabled) {\n    return this._source_text;\n  }\n\n  var source_text = this._source_text;\n  var eol = this._options.eol;\n  if (eol === 'auto') {\n    eol = '\\n';\n    if (source_text && lineBreak.test(source_text || '')) {\n      eol = source_text.match(lineBreak)[0];\n    }\n  }\n\n\n  // HACK: newline parsing inconsistent. This brute force normalizes the this._input.\n  source_text = source_text.replace(allLineBreaks, '\\n');\n\n  // reset\n  var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n\n  this._output = new Output(this._options, baseIndentString);\n  this._input = new InputScanner(source_text);\n  this._indentLevel = 0;\n  this._nestedLevel = 0;\n\n  this._ch = null;\n  var parenLevel = 0;\n\n  var insideRule = false;\n  // This is the value side of a property value pair (blue in the following ex)\n  // label { content: blue }\n  var insidePropertyValue = false;\n  var enteringConditionalGroup = false;\n  var insideNonNestedAtRule = false;\n  var insideScssMap = false;\n  var topCharacter = this._ch;\n  var insideNonSemiColonValues = false;\n  var whitespace;\n  var isAfterSpace;\n  var previous_ch;\n\n  while (true) {\n    whitespace = this._input.read(whitespacePattern);\n    isAfterSpace = whitespace !== '';\n    previous_ch = topCharacter;\n    this._ch = this._input.next();\n    if (this._ch === '\\\\' && this._input.hasNext()) {\n      this._ch += this._input.next();\n    }\n    topCharacter = this._ch;\n\n    if (!this._ch) {\n      break;\n    } else if (this._ch === '/' && this._input.peek() === '*') {\n      // /* css comment */\n      // Always start block comments on a new line.\n      // This handles scenarios where a block comment immediately\n      // follows a property definition on the same line or where\n      // minified code is being beautified.\n      this._output.add_new_line();\n      this._input.back();\n\n      var comment = this._input.read(block_comment_pattern);\n\n      // Handle ignore directive\n      var directives = directives_core.get_directives(comment);\n      if (directives && directives.ignore === 'start') {\n        comment += directives_core.readIgnored(this._input);\n      }\n\n      this.print_string(comment);\n\n      // Ensures any new lines following the comment are preserved\n      this.eatWhitespace(true);\n\n      // Block comments are followed by a new line so they don't\n      // share a line with other properties\n      this._output.add_new_line();\n    } else if (this._ch === '/' && this._input.peek() === '/') {\n      // // single line comment\n      // Preserves the space before a comment\n      // on the same line as a rule\n      this._output.space_before_token = true;\n      this._input.back();\n      this.print_string(this._input.read(comment_pattern));\n\n      // Ensures any new lines following the comment are preserved\n      this.eatWhitespace(true);\n    } else if (this._ch === '$') {\n      this.preserveSingleSpace(isAfterSpace);\n\n      this.print_string(this._ch);\n\n      // strip trailing space, if present, for hash property checks\n      var variable = this._input.peekUntilAfter(/[: ,;{}()[\\]\\/='\"]/g);\n\n      if (variable.match(/[ :]$/)) {\n        // we have a variable or pseudo-class, add it and insert one space before continuing\n        variable = this.eatString(\": \").replace(/\\s+$/, '');\n        this.print_string(variable);\n        this._output.space_before_token = true;\n      }\n\n      // might be sass variable\n      if (parenLevel === 0 && variable.indexOf(':') !== -1) {\n        insidePropertyValue = true;\n        this.indent();\n      }\n    } else if (this._ch === '@') {\n      this.preserveSingleSpace(isAfterSpace);\n\n      // deal with less property mixins @{...}\n      if (this._input.peek() === '{') {\n        this.print_string(this._ch + this.eatString('}'));\n      } else {\n        this.print_string(this._ch);\n\n        // strip trailing space, if present, for hash property checks\n        var variableOrRule = this._input.peekUntilAfter(/[: ,;{}()[\\]\\/='\"]/g);\n\n        if (variableOrRule.match(/[ :]$/)) {\n          // we have a variable or pseudo-class, add it and insert one space before continuing\n          variableOrRule = this.eatString(\": \").replace(/\\s+$/, '');\n          this.print_string(variableOrRule);\n          this._output.space_before_token = true;\n        }\n\n        // might be less variable\n        if (parenLevel === 0 && variableOrRule.indexOf(':') !== -1) {\n          insidePropertyValue = true;\n          this.indent();\n\n          // might be a nesting at-rule\n        } else if (variableOrRule in this.NESTED_AT_RULE) {\n          this._nestedLevel += 1;\n          if (variableOrRule in this.CONDITIONAL_GROUP_RULE) {\n            enteringConditionalGroup = true;\n          }\n\n          // might be a non-nested at-rule\n        } else if (parenLevel === 0 && !insidePropertyValue) {\n          insideNonNestedAtRule = true;\n        }\n      }\n    } else if (this._ch === '#' && this._input.peek() === '{') {\n      this.preserveSingleSpace(isAfterSpace);\n      this.print_string(this._ch + this.eatString('}'));\n    } else if (this._ch === '{') {\n      if (insidePropertyValue) {\n        insidePropertyValue = false;\n        this.outdent();\n      }\n\n      // non nested at rule becomes nested\n      insideNonNestedAtRule = false;\n\n      // when entering conditional groups, only rulesets are allowed\n      if (enteringConditionalGroup) {\n        enteringConditionalGroup = false;\n        insideRule = (this._indentLevel >= this._nestedLevel);\n      } else {\n        // otherwise, declarations are also allowed\n        insideRule = (this._indentLevel >= this._nestedLevel - 1);\n      }\n      if (this._options.newline_between_rules && insideRule) {\n        if (this._output.previous_line && this._output.previous_line.item(-1) !== '{') {\n          this._output.ensure_empty_line_above('/', ',');\n        }\n      }\n\n      this._output.space_before_token = true;\n\n      // The difference in print_string and indent order is necessary to indent the '{' correctly\n      if (this._options.brace_style === 'expand') {\n        this._output.add_new_line();\n        this.print_string(this._ch);\n        this.indent();\n        this._output.set_indent(this._indentLevel);\n      } else {\n        // inside mixin and first param is object\n        if (previous_ch === '(') {\n          this._output.space_before_token = false;\n        } else if (previous_ch !== ',') {\n          this.indent();\n        }\n        this.print_string(this._ch);\n      }\n\n      this.eatWhitespace(true);\n      this._output.add_new_line();\n    } else if (this._ch === '}') {\n      this.outdent();\n      this._output.add_new_line();\n      if (previous_ch === '{') {\n        this._output.trim(true);\n      }\n\n      if (insidePropertyValue) {\n        this.outdent();\n        insidePropertyValue = false;\n      }\n      this.print_string(this._ch);\n      insideRule = false;\n      if (this._nestedLevel) {\n        this._nestedLevel--;\n      }\n\n      this.eatWhitespace(true);\n      this._output.add_new_line();\n\n      if (this._options.newline_between_rules && !this._output.just_added_blankline()) {\n        if (this._input.peek() !== '}') {\n          this._output.add_new_line(true);\n        }\n      }\n      if (this._input.peek() === ')') {\n        this._output.trim(true);\n        if (this._options.brace_style === \"expand\") {\n          this._output.add_new_line(true);\n        }\n      }\n    } else if (this._ch === \":\") {\n\n      for (var i = 0; i < this.NON_SEMICOLON_NEWLINE_PROPERTY.length; i++) {\n        if (this._input.lookBack(this.NON_SEMICOLON_NEWLINE_PROPERTY[i])) {\n          insideNonSemiColonValues = true;\n          break;\n        }\n      }\n\n      if ((insideRule || enteringConditionalGroup) && !(this._input.lookBack(\"&\") || this.foundNestedPseudoClass()) && !this._input.lookBack(\"(\") && !insideNonNestedAtRule && parenLevel === 0) {\n        // 'property: value' delimiter\n        // which could be in a conditional group query\n\n        this.print_string(':');\n        if (!insidePropertyValue) {\n          insidePropertyValue = true;\n          this._output.space_before_token = true;\n          this.eatWhitespace(true);\n          this.indent();\n        }\n      } else {\n        // sass/less parent reference don't use a space\n        // sass nested pseudo-class don't use a space\n\n        // preserve space before pseudoclasses/pseudoelements, as it means \"in any child\"\n        if (this._input.lookBack(\" \")) {\n          this._output.space_before_token = true;\n        }\n        if (this._input.peek() === \":\") {\n          // pseudo-element\n          this._ch = this._input.next();\n          this.print_string(\"::\");\n        } else {\n          // pseudo-class\n          this.print_string(':');\n        }\n      }\n    } else if (this._ch === '\"' || this._ch === '\\'') {\n      var preserveQuoteSpace = previous_ch === '\"' || previous_ch === '\\'';\n      this.preserveSingleSpace(preserveQuoteSpace || isAfterSpace);\n      this.print_string(this._ch + this.eatString(this._ch));\n      this.eatWhitespace(true);\n    } else if (this._ch === ';') {\n      insideNonSemiColonValues = false;\n      if (parenLevel === 0) {\n        if (insidePropertyValue) {\n          this.outdent();\n          insidePropertyValue = false;\n        }\n        insideNonNestedAtRule = false;\n        this.print_string(this._ch);\n        this.eatWhitespace(true);\n\n        // This maintains single line comments on the same\n        // line. Block comments are also affected, but\n        // a new line is always output before one inside\n        // that section\n        if (this._input.peek() !== '/') {\n          this._output.add_new_line();\n        }\n      } else {\n        this.print_string(this._ch);\n        this.eatWhitespace(true);\n        this._output.space_before_token = true;\n      }\n    } else if (this._ch === '(') { // may be a url\n      if (this._input.lookBack(\"url\")) {\n        this.print_string(this._ch);\n        this.eatWhitespace();\n        parenLevel++;\n        this.indent();\n        this._ch = this._input.next();\n        if (this._ch === ')' || this._ch === '\"' || this._ch === '\\'') {\n          this._input.back();\n        } else if (this._ch) {\n          this.print_string(this._ch + this.eatString(')'));\n          if (parenLevel) {\n            parenLevel--;\n            this.outdent();\n          }\n        }\n      } else {\n        var space_needed = false;\n        if (this._input.lookBack(\"with\")) {\n          // look back is not an accurate solution, we need tokens to confirm without whitespaces\n          space_needed = true;\n        }\n        this.preserveSingleSpace(isAfterSpace || space_needed);\n        this.print_string(this._ch);\n\n        // handle scss/sass map\n        if (insidePropertyValue && previous_ch === \"$\" && this._options.selector_separator_newline) {\n          this._output.add_new_line();\n          insideScssMap = true;\n        } else {\n          this.eatWhitespace();\n          parenLevel++;\n          this.indent();\n        }\n      }\n    } else if (this._ch === ')') {\n      if (parenLevel) {\n        parenLevel--;\n        this.outdent();\n      }\n      if (insideScssMap && this._input.peek() === \";\" && this._options.selector_separator_newline) {\n        insideScssMap = false;\n        this.outdent();\n        this._output.add_new_line();\n      }\n      this.print_string(this._ch);\n    } else if (this._ch === ',') {\n      this.print_string(this._ch);\n      this.eatWhitespace(true);\n      if (this._options.selector_separator_newline && (!insidePropertyValue || insideScssMap) && parenLevel === 0 && !insideNonNestedAtRule) {\n        this._output.add_new_line();\n      } else {\n        this._output.space_before_token = true;\n      }\n    } else if ((this._ch === '>' || this._ch === '+' || this._ch === '~') && !insidePropertyValue && parenLevel === 0) {\n      //handle combinator spacing\n      if (this._options.space_around_combinator) {\n        this._output.space_before_token = true;\n        this.print_string(this._ch);\n        this._output.space_before_token = true;\n      } else {\n        this.print_string(this._ch);\n        this.eatWhitespace();\n        // squash extra whitespace\n        if (this._ch && whitespaceChar.test(this._ch)) {\n          this._ch = '';\n        }\n      }\n    } else if (this._ch === ']') {\n      this.print_string(this._ch);\n    } else if (this._ch === '[') {\n      this.preserveSingleSpace(isAfterSpace);\n      this.print_string(this._ch);\n    } else if (this._ch === '=') { // no whitespace before or after\n      this.eatWhitespace();\n      this.print_string('=');\n      if (whitespaceChar.test(this._ch)) {\n        this._ch = '';\n      }\n    } else if (this._ch === '!' && !this._input.lookBack(\"\\\\\")) { // !important\n      this._output.space_before_token = true;\n      this.print_string(this._ch);\n    } else {\n      var preserveAfterSpace = previous_ch === '\"' || previous_ch === '\\'';\n      this.preserveSingleSpace(preserveAfterSpace || isAfterSpace);\n      this.print_string(this._ch);\n\n      if (!this._output.just_added_newline() && this._input.peek() === '\\n' && insideNonSemiColonValues) {\n        this._output.add_new_line();\n      }\n    }\n  }\n\n  var sweetCode = this._output.get_code(eol);\n\n  return sweetCode;\n};\n\nmodule.exports.Beautifier = Beautifier;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Beautifier = require('./beautifier').Beautifier,\n  Options = require('./options').Options;\n\nfunction css_beautify(source_text, options) {\n  var beautifier = new Beautifier(source_text, options);\n  return beautifier.beautify();\n}\n\nmodule.exports = css_beautify;\nmodule.exports.defaultOptions = function() {\n  return new Options();\n};\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar BaseOptions = require('../core/options').Options;\n\nfunction Options(options) {\n  BaseOptions.call(this, options, 'html');\n  if (this.templating.length === 1 && this.templating[0] === 'auto') {\n    this.templating = ['django', 'erb', 'handlebars', 'php'];\n  }\n\n  this.indent_inner_html = this._get_boolean('indent_inner_html');\n  this.indent_body_inner_html = this._get_boolean('indent_body_inner_html', true);\n  this.indent_head_inner_html = this._get_boolean('indent_head_inner_html', true);\n\n  this.indent_handlebars = this._get_boolean('indent_handlebars', true);\n  this.wrap_attributes = this._get_selection('wrap_attributes',\n    ['auto', 'force', 'force-aligned', 'force-expand-multiline', 'aligned-multiple', 'preserve', 'preserve-aligned']);\n  this.wrap_attributes_min_attrs = this._get_number('wrap_attributes_min_attrs', 2);\n  this.wrap_attributes_indent_size = this._get_number('wrap_attributes_indent_size', this.indent_size);\n  this.extra_liners = this._get_array('extra_liners', ['head', 'body', '/html']);\n\n  // Block vs inline elements\n  // https://developer.mozilla.org/en-US/docs/Web/HTML/Block-level_elements\n  // https://developer.mozilla.org/en-US/docs/Web/HTML/Inline_elements\n  // https://www.w3.org/TR/html5/dom.html#phrasing-content\n  this.inline = this._get_array('inline', [\n    'a', 'abbr', 'area', 'audio', 'b', 'bdi', 'bdo', 'br', 'button', 'canvas', 'cite',\n    'code', 'data', 'datalist', 'del', 'dfn', 'em', 'embed', 'i', 'iframe', 'img',\n    'input', 'ins', 'kbd', 'keygen', 'label', 'map', 'mark', 'math', 'meter', 'noscript',\n    'object', 'output', 'progress', 'q', 'ruby', 's', 'samp', /* 'script', */ 'select', 'small',\n    'span', 'strong', 'sub', 'sup', 'svg', 'template', 'textarea', 'time', 'u', 'var',\n    'video', 'wbr', 'text',\n    // obsolete inline tags\n    'acronym', 'big', 'strike', 'tt'\n  ]);\n  this.inline_custom_elements = this._get_boolean('inline_custom_elements', true);\n  this.void_elements = this._get_array('void_elements', [\n    // HTLM void elements - aka self-closing tags - aka singletons\n    // https://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n    'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'keygen',\n    'link', 'menuitem', 'meta', 'param', 'source', 'track', 'wbr',\n    // NOTE: Optional tags are too complex for a simple list\n    // they are hard coded in _do_optional_end_element\n\n    // Doctype and xml elements\n    '!doctype', '?xml',\n\n    // obsolete tags\n    // basefont: https://www.computerhope.com/jargon/h/html-basefont-tag.htm\n    // isndex: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/isindex\n    'basefont', 'isindex'\n  ]);\n  this.unformatted = this._get_array('unformatted', []);\n  this.content_unformatted = this._get_array('content_unformatted', [\n    'pre', 'textarea'\n  ]);\n  this.unformatted_content_delimiter = this._get_characters('unformatted_content_delimiter');\n  this.indent_scripts = this._get_selection('indent_scripts', ['normal', 'keep', 'separate']);\n\n}\nOptions.prototype = new BaseOptions();\n\n\n\nmodule.exports.Options = Options;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar BaseTokenizer = require('../core/tokenizer').Tokenizer;\nvar BASETOKEN = require('../core/tokenizer').TOKEN;\nvar Directives = require('../core/directives').Directives;\nvar TemplatablePattern = require('../core/templatablepattern').TemplatablePattern;\nvar Pattern = require('../core/pattern').Pattern;\n\nvar TOKEN = {\n  TAG_OPEN: 'TK_TAG_OPEN',\n  TAG_CLOSE: 'TK_TAG_CLOSE',\n  CONTROL_FLOW_OPEN: 'TK_CONTROL_FLOW_OPEN',\n  CONTROL_FLOW_CLOSE: 'TK_CONTROL_FLOW_CLOSE',\n  ATTRIBUTE: 'TK_ATTRIBUTE',\n  EQUALS: 'TK_EQUALS',\n  VALUE: 'TK_VALUE',\n  COMMENT: 'TK_COMMENT',\n  TEXT: 'TK_TEXT',\n  UNKNOWN: 'TK_UNKNOWN',\n  START: BASETOKEN.START,\n  RAW: BASETOKEN.RAW,\n  EOF: BASETOKEN.EOF\n};\n\nvar directives_core = new Directives(/<\\!--/, /-->/);\n\nvar Tokenizer = function(input_string, options) {\n  BaseTokenizer.call(this, input_string, options);\n  this._current_tag_name = '';\n\n  // Words end at whitespace or when a tag starts\n  // if we are indenting handlebars, they are considered tags\n  var templatable_reader = new TemplatablePattern(this._input).read_options(this._options);\n  var pattern_reader = new Pattern(this._input);\n\n  this.__patterns = {\n    word: templatable_reader.until(/[\\n\\r\\t <]/),\n    word_control_flow_close_excluded: templatable_reader.until(/[\\n\\r\\t <}]/),\n    single_quote: templatable_reader.until_after(/'/),\n    double_quote: templatable_reader.until_after(/\"/),\n    attribute: templatable_reader.until(/[\\n\\r\\t =>]|\\/>/),\n    element_name: templatable_reader.until(/[\\n\\r\\t >\\/]/),\n\n    angular_control_flow_start: pattern_reader.matching(/\\@[a-zA-Z]+[^({]*[({]/),\n    handlebars_comment: pattern_reader.starting_with(/{{!--/).until_after(/--}}/),\n    handlebars: pattern_reader.starting_with(/{{/).until_after(/}}/),\n    handlebars_open: pattern_reader.until(/[\\n\\r\\t }]/),\n    handlebars_raw_close: pattern_reader.until(/}}/),\n    comment: pattern_reader.starting_with(/<!--/).until_after(/-->/),\n    cdata: pattern_reader.starting_with(/<!\\[CDATA\\[/).until_after(/]]>/),\n    // https://en.wikipedia.org/wiki/Conditional_comment\n    conditional_comment: pattern_reader.starting_with(/<!\\[/).until_after(/]>/),\n    processing: pattern_reader.starting_with(/<\\?/).until_after(/\\?>/)\n  };\n\n  if (this._options.indent_handlebars) {\n    this.__patterns.word = this.__patterns.word.exclude('handlebars');\n    this.__patterns.word_control_flow_close_excluded = this.__patterns.word_control_flow_close_excluded.exclude('handlebars');\n  }\n\n  this._unformatted_content_delimiter = null;\n\n  if (this._options.unformatted_content_delimiter) {\n    var literal_regexp = this._input.get_literal_regexp(this._options.unformatted_content_delimiter);\n    this.__patterns.unformatted_content_delimiter =\n      pattern_reader.matching(literal_regexp)\n      .until_after(literal_regexp);\n  }\n};\nTokenizer.prototype = new BaseTokenizer();\n\nTokenizer.prototype._is_comment = function(current_token) { // jshint unused:false\n  return false; //current_token.type === TOKEN.COMMENT || current_token.type === TOKEN.UNKNOWN;\n};\n\nTokenizer.prototype._is_opening = function(current_token) {\n  return current_token.type === TOKEN.TAG_OPEN || current_token.type === TOKEN.CONTROL_FLOW_OPEN;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) {\n  return (current_token.type === TOKEN.TAG_CLOSE &&\n    (open_token && (\n      ((current_token.text === '>' || current_token.text === '/>') && open_token.text[0] === '<') ||\n      (current_token.text === '}}' && open_token.text[0] === '{' && open_token.text[1] === '{')))\n  ) || (current_token.type === TOKEN.CONTROL_FLOW_CLOSE &&\n    (current_token.text === '}' && open_token.text.endsWith('{')));\n};\n\nTokenizer.prototype._reset = function() {\n  this._current_tag_name = '';\n};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  var token = null;\n  this._readWhitespace();\n  var c = this._input.peek();\n\n  if (c === null) {\n    return this._create_token(TOKEN.EOF, '');\n  }\n\n  token = token || this._read_open_handlebars(c, open_token);\n  token = token || this._read_attribute(c, previous_token, open_token);\n  token = token || this._read_close(c, open_token);\n  token = token || this._read_script_and_style(c, previous_token);\n  token = token || this._read_control_flows(c, open_token);\n  token = token || this._read_raw_content(c, previous_token, open_token);\n  token = token || this._read_content_word(c, open_token);\n  token = token || this._read_comment_or_cdata(c);\n  token = token || this._read_processing(c);\n  token = token || this._read_open(c, open_token);\n  token = token || this._create_token(TOKEN.UNKNOWN, this._input.next());\n\n  return token;\n};\n\nTokenizer.prototype._read_comment_or_cdata = function(c) { // jshint unused:false\n  var token = null;\n  var resulting_string = null;\n  var directives = null;\n\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    // We treat all comments as literals, even more than preformatted tags\n    // we only look for the appropriate closing marker\n    if (peek1 === '!') {\n      resulting_string = this.__patterns.comment.read();\n\n      // only process directive on html comments\n      if (resulting_string) {\n        directives = directives_core.get_directives(resulting_string);\n        if (directives && directives.ignore === 'start') {\n          resulting_string += directives_core.readIgnored(this._input);\n        }\n      } else {\n        resulting_string = this.__patterns.cdata.read();\n      }\n    }\n\n    if (resulting_string) {\n      token = this._create_token(TOKEN.COMMENT, resulting_string);\n      token.directives = directives;\n    }\n  }\n\n  return token;\n};\n\nTokenizer.prototype._read_processing = function(c) { // jshint unused:false\n  var token = null;\n  var resulting_string = null;\n  var directives = null;\n\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    if (peek1 === '!' || peek1 === '?') {\n      resulting_string = this.__patterns.conditional_comment.read();\n      resulting_string = resulting_string || this.__patterns.processing.read();\n    }\n\n    if (resulting_string) {\n      token = this._create_token(TOKEN.COMMENT, resulting_string);\n      token.directives = directives;\n    }\n  }\n\n  return token;\n};\n\nTokenizer.prototype._read_open = function(c, open_token) {\n  var resulting_string = null;\n  var token = null;\n  if (!open_token || open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n    if (c === '<') {\n\n      resulting_string = this._input.next();\n      if (this._input.peek() === '/') {\n        resulting_string += this._input.next();\n      }\n      resulting_string += this.__patterns.element_name.read();\n      token = this._create_token(TOKEN.TAG_OPEN, resulting_string);\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._read_open_handlebars = function(c, open_token) {\n  var resulting_string = null;\n  var token = null;\n  if (!open_token || open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n    if ((this._options.templating.includes('angular') || this._options.indent_handlebars) && c === '{' && this._input.peek(1) === '{') {\n      if (this._options.indent_handlebars && this._input.peek(2) === '!') {\n        resulting_string = this.__patterns.handlebars_comment.read();\n        resulting_string = resulting_string || this.__patterns.handlebars.read();\n        token = this._create_token(TOKEN.COMMENT, resulting_string);\n      } else {\n        resulting_string = this.__patterns.handlebars_open.read();\n        token = this._create_token(TOKEN.TAG_OPEN, resulting_string);\n      }\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._read_control_flows = function(c, open_token) {\n  var resulting_string = '';\n  var token = null;\n  // Only check for control flows if angular templating is set\n  if (!this._options.templating.includes('angular')) {\n    return token;\n  }\n\n  if (c === '@') {\n    resulting_string = this.__patterns.angular_control_flow_start.read();\n    if (resulting_string === '') {\n      return token;\n    }\n\n    var opening_parentheses_count = resulting_string.endsWith('(') ? 1 : 0;\n    var closing_parentheses_count = 0;\n    // The opening brace of the control flow is where the number of opening and closing parentheses equal\n    // e.g. @if({value: true} !== null) { \n    while (!(resulting_string.endsWith('{') && opening_parentheses_count === closing_parentheses_count)) {\n      var next_char = this._input.next();\n      if (next_char === null) {\n        break;\n      } else if (next_char === '(') {\n        opening_parentheses_count++;\n      } else if (next_char === ')') {\n        closing_parentheses_count++;\n      }\n      resulting_string += next_char;\n    }\n    token = this._create_token(TOKEN.CONTROL_FLOW_OPEN, resulting_string);\n  } else if (c === '}' && open_token && open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n    resulting_string = this._input.next();\n    token = this._create_token(TOKEN.CONTROL_FLOW_CLOSE, resulting_string);\n  }\n  return token;\n};\n\n\nTokenizer.prototype._read_close = function(c, open_token) {\n  var resulting_string = null;\n  var token = null;\n  if (open_token && open_token.type === TOKEN.TAG_OPEN) {\n    if (open_token.text[0] === '<' && (c === '>' || (c === '/' && this._input.peek(1) === '>'))) {\n      resulting_string = this._input.next();\n      if (c === '/') { //  for close tag \"/>\"\n        resulting_string += this._input.next();\n      }\n      token = this._create_token(TOKEN.TAG_CLOSE, resulting_string);\n    } else if (open_token.text[0] === '{' && c === '}' && this._input.peek(1) === '}') {\n      this._input.next();\n      this._input.next();\n      token = this._create_token(TOKEN.TAG_CLOSE, '}}');\n    }\n  }\n\n  return token;\n};\n\nTokenizer.prototype._read_attribute = function(c, previous_token, open_token) {\n  var token = null;\n  var resulting_string = '';\n  if (open_token && open_token.text[0] === '<') {\n\n    if (c === '=') {\n      token = this._create_token(TOKEN.EQUALS, this._input.next());\n    } else if (c === '\"' || c === \"'\") {\n      var content = this._input.next();\n      if (c === '\"') {\n        content += this.__patterns.double_quote.read();\n      } else {\n        content += this.__patterns.single_quote.read();\n      }\n      token = this._create_token(TOKEN.VALUE, content);\n    } else {\n      resulting_string = this.__patterns.attribute.read();\n\n      if (resulting_string) {\n        if (previous_token.type === TOKEN.EQUALS) {\n          token = this._create_token(TOKEN.VALUE, resulting_string);\n        } else {\n          token = this._create_token(TOKEN.ATTRIBUTE, resulting_string);\n        }\n      }\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._is_content_unformatted = function(tag_name) {\n  // void_elements have no content and so cannot have unformatted content\n  // script and style tags should always be read as unformatted content\n  // finally content_unformatted and unformatted element contents are unformatted\n  return this._options.void_elements.indexOf(tag_name) === -1 &&\n    (this._options.content_unformatted.indexOf(tag_name) !== -1 ||\n      this._options.unformatted.indexOf(tag_name) !== -1);\n};\n\nTokenizer.prototype._read_raw_content = function(c, previous_token, open_token) { // jshint unused:false\n  var resulting_string = '';\n  if (open_token && open_token.text[0] === '{') {\n    resulting_string = this.__patterns.handlebars_raw_close.read();\n  } else if (previous_token.type === TOKEN.TAG_CLOSE &&\n    previous_token.opened.text[0] === '<' && previous_token.text[0] !== '/') {\n    // ^^ empty tag has no content \n    var tag_name = previous_token.opened.text.substr(1).toLowerCase();\n    if (this._is_content_unformatted(tag_name)) {\n\n      resulting_string = this._input.readUntil(new RegExp('</' + tag_name + '[\\\\n\\\\r\\\\t ]*?>', 'ig'));\n    }\n  }\n\n  if (resulting_string) {\n    return this._create_token(TOKEN.TEXT, resulting_string);\n  }\n\n  return null;\n};\n\nTokenizer.prototype._read_script_and_style = function(c, previous_token) { // jshint unused:false \n  if (previous_token.type === TOKEN.TAG_CLOSE && previous_token.opened.text[0] === '<' && previous_token.text[0] !== '/') {\n    var tag_name = previous_token.opened.text.substr(1).toLowerCase();\n    if (tag_name === 'script' || tag_name === 'style') {\n      // Script and style tags are allowed to have comments wrapping their content\n      // or just have regular content.\n      var token = this._read_comment_or_cdata(c);\n      if (token) {\n        token.type = TOKEN.TEXT;\n        return token;\n      }\n      var resulting_string = this._input.readUntil(new RegExp('</' + tag_name + '[\\\\n\\\\r\\\\t ]*?>', 'ig'));\n      if (resulting_string) {\n        return this._create_token(TOKEN.TEXT, resulting_string);\n      }\n    }\n  }\n  return null;\n};\n\nTokenizer.prototype._read_content_word = function(c, open_token) {\n  var resulting_string = '';\n  if (this._options.unformatted_content_delimiter) {\n    if (c === this._options.unformatted_content_delimiter[0]) {\n      resulting_string = this.__patterns.unformatted_content_delimiter.read();\n    }\n  }\n\n  if (!resulting_string) {\n    resulting_string = (open_token && open_token.type === TOKEN.CONTROL_FLOW_OPEN) ? this.__patterns.word_control_flow_close_excluded.read() : this.__patterns.word.read();\n  }\n  if (resulting_string) {\n    return this._create_token(TOKEN.TEXT, resulting_string);\n  }\n  return null;\n};\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Options = require('../html/options').Options;\nvar Output = require('../core/output').Output;\nvar Tokenizer = require('../html/tokenizer').Tokenizer;\nvar TOKEN = require('../html/tokenizer').TOKEN;\n\nvar lineBreak = /\\r\\n|[\\r\\n]/;\nvar allLineBreaks = /\\r\\n|[\\r\\n]/g;\n\nvar Printer = function(options, base_indent_string) { //handles input/output and some other printing functions\n\n  this.indent_level = 0;\n  this.alignment_size = 0;\n  this.max_preserve_newlines = options.max_preserve_newlines;\n  this.preserve_newlines = options.preserve_newlines;\n\n  this._output = new Output(options, base_indent_string);\n\n};\n\nPrinter.prototype.current_line_has_match = function(pattern) {\n  return this._output.current_line.has_match(pattern);\n};\n\nPrinter.prototype.set_space_before_token = function(value, non_breaking) {\n  this._output.space_before_token = value;\n  this._output.non_breaking_space = non_breaking;\n};\n\nPrinter.prototype.set_wrap_point = function() {\n  this._output.set_indent(this.indent_level, this.alignment_size);\n  this._output.set_wrap_point();\n};\n\n\nPrinter.prototype.add_raw_token = function(token) {\n  this._output.add_raw_token(token);\n};\n\nPrinter.prototype.print_preserved_newlines = function(raw_token) {\n  var newlines = 0;\n  if (raw_token.type !== TOKEN.TEXT && raw_token.previous.type !== TOKEN.TEXT) {\n    newlines = raw_token.newlines ? 1 : 0;\n  }\n\n  if (this.preserve_newlines) {\n    newlines = raw_token.newlines < this.max_preserve_newlines + 1 ? raw_token.newlines : this.max_preserve_newlines + 1;\n  }\n  for (var n = 0; n < newlines; n++) {\n    this.print_newline(n > 0);\n  }\n\n  return newlines !== 0;\n};\n\nPrinter.prototype.traverse_whitespace = function(raw_token) {\n  if (raw_token.whitespace_before || raw_token.newlines) {\n    if (!this.print_preserved_newlines(raw_token)) {\n      this._output.space_before_token = true;\n    }\n    return true;\n  }\n  return false;\n};\n\nPrinter.prototype.previous_token_wrapped = function() {\n  return this._output.previous_token_wrapped;\n};\n\nPrinter.prototype.print_newline = function(force) {\n  this._output.add_new_line(force);\n};\n\nPrinter.prototype.print_token = function(token) {\n  if (token.text) {\n    this._output.set_indent(this.indent_level, this.alignment_size);\n    this._output.add_token(token.text);\n  }\n};\n\nPrinter.prototype.indent = function() {\n  this.indent_level++;\n};\n\nPrinter.prototype.deindent = function() {\n  if (this.indent_level > 0) {\n    this.indent_level--;\n    this._output.set_indent(this.indent_level, this.alignment_size);\n  }\n};\n\nPrinter.prototype.get_full_indent = function(level) {\n  level = this.indent_level + (level || 0);\n  if (level < 1) {\n    return '';\n  }\n\n  return this._output.get_indent_string(level);\n};\n\nvar get_type_attribute = function(start_token) {\n  var result = null;\n  var raw_token = start_token.next;\n\n  // Search attributes for a type attribute\n  while (raw_token.type !== TOKEN.EOF && start_token.closed !== raw_token) {\n    if (raw_token.type === TOKEN.ATTRIBUTE && raw_token.text === 'type') {\n      if (raw_token.next && raw_token.next.type === TOKEN.EQUALS &&\n        raw_token.next.next && raw_token.next.next.type === TOKEN.VALUE) {\n        result = raw_token.next.next.text;\n      }\n      break;\n    }\n    raw_token = raw_token.next;\n  }\n\n  return result;\n};\n\nvar get_custom_beautifier_name = function(tag_check, raw_token) {\n  var typeAttribute = null;\n  var result = null;\n\n  if (!raw_token.closed) {\n    return null;\n  }\n\n  if (tag_check === 'script') {\n    typeAttribute = 'text/javascript';\n  } else if (tag_check === 'style') {\n    typeAttribute = 'text/css';\n  }\n\n  typeAttribute = get_type_attribute(raw_token) || typeAttribute;\n\n  // For script and style tags that have a type attribute, only enable custom beautifiers for matching values\n  // For those without a type attribute use default;\n  if (typeAttribute.search('text/css') > -1) {\n    result = 'css';\n  } else if (typeAttribute.search(/module|((text|application|dojo)\\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\\+)?json|method|aspect))/) > -1) {\n    result = 'javascript';\n  } else if (typeAttribute.search(/(text|application|dojo)\\/(x-)?(html)/) > -1) {\n    result = 'html';\n  } else if (typeAttribute.search(/test\\/null/) > -1) {\n    // Test only mime-type for testing the beautifier when null is passed as beautifing function\n    result = 'null';\n  }\n\n  return result;\n};\n\nfunction in_array(what, arr) {\n  return arr.indexOf(what) !== -1;\n}\n\nfunction TagFrame(parent, parser_token, indent_level) {\n  this.parent = parent || null;\n  this.tag = parser_token ? parser_token.tag_name : '';\n  this.indent_level = indent_level || 0;\n  this.parser_token = parser_token || null;\n}\n\nfunction TagStack(printer) {\n  this._printer = printer;\n  this._current_frame = null;\n}\n\nTagStack.prototype.get_parser_token = function() {\n  return this._current_frame ? this._current_frame.parser_token : null;\n};\n\nTagStack.prototype.record_tag = function(parser_token) { //function to record a tag and its parent in this.tags Object\n  var new_frame = new TagFrame(this._current_frame, parser_token, this._printer.indent_level);\n  this._current_frame = new_frame;\n};\n\nTagStack.prototype._try_pop_frame = function(frame) { //function to retrieve the opening tag to the corresponding closer\n  var parser_token = null;\n\n  if (frame) {\n    parser_token = frame.parser_token;\n    this._printer.indent_level = frame.indent_level;\n    this._current_frame = frame.parent;\n  }\n\n  return parser_token;\n};\n\nTagStack.prototype._get_frame = function(tag_list, stop_list) { //function to retrieve the opening tag to the corresponding closer\n  var frame = this._current_frame;\n\n  while (frame) { //till we reach '' (the initial value);\n    if (tag_list.indexOf(frame.tag) !== -1) { //if this is it use it\n      break;\n    } else if (stop_list && stop_list.indexOf(frame.tag) !== -1) {\n      frame = null;\n      break;\n    }\n    frame = frame.parent;\n  }\n\n  return frame;\n};\n\nTagStack.prototype.try_pop = function(tag, stop_list) { //function to retrieve the opening tag to the corresponding closer\n  var frame = this._get_frame([tag], stop_list);\n  return this._try_pop_frame(frame);\n};\n\nTagStack.prototype.indent_to_tag = function(tag_list) {\n  var frame = this._get_frame(tag_list);\n  if (frame) {\n    this._printer.indent_level = frame.indent_level;\n  }\n};\n\nfunction Beautifier(source_text, options, js_beautify, css_beautify) {\n  //Wrapper function to invoke all the necessary constructors and deal with the output.\n  this._source_text = source_text || '';\n  options = options || {};\n  this._js_beautify = js_beautify;\n  this._css_beautify = css_beautify;\n  this._tag_stack = null;\n\n  // Allow the setting of language/file-type specific options\n  // with inheritance of overall settings\n  var optionHtml = new Options(options, 'html');\n\n  this._options = optionHtml;\n\n  this._is_wrap_attributes_force = this._options.wrap_attributes.substr(0, 'force'.length) === 'force';\n  this._is_wrap_attributes_force_expand_multiline = (this._options.wrap_attributes === 'force-expand-multiline');\n  this._is_wrap_attributes_force_aligned = (this._options.wrap_attributes === 'force-aligned');\n  this._is_wrap_attributes_aligned_multiple = (this._options.wrap_attributes === 'aligned-multiple');\n  this._is_wrap_attributes_preserve = this._options.wrap_attributes.substr(0, 'preserve'.length) === 'preserve';\n  this._is_wrap_attributes_preserve_aligned = (this._options.wrap_attributes === 'preserve-aligned');\n}\n\nBeautifier.prototype.beautify = function() {\n\n  // if disabled, return the input unchanged.\n  if (this._options.disabled) {\n    return this._source_text;\n  }\n\n  var source_text = this._source_text;\n  var eol = this._options.eol;\n  if (this._options.eol === 'auto') {\n    eol = '\\n';\n    if (source_text && lineBreak.test(source_text)) {\n      eol = source_text.match(lineBreak)[0];\n    }\n  }\n\n  // HACK: newline parsing inconsistent. This brute force normalizes the input.\n  source_text = source_text.replace(allLineBreaks, '\\n');\n\n  var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n\n  var last_token = {\n    text: '',\n    type: ''\n  };\n\n  var last_tag_token = new TagOpenParserToken(this._options);\n\n  var printer = new Printer(this._options, baseIndentString);\n  var tokens = new Tokenizer(source_text, this._options).tokenize();\n\n  this._tag_stack = new TagStack(printer);\n\n  var parser_token = null;\n  var raw_token = tokens.next();\n  while (raw_token.type !== TOKEN.EOF) {\n\n    if (raw_token.type === TOKEN.TAG_OPEN || raw_token.type === TOKEN.COMMENT) {\n      parser_token = this._handle_tag_open(printer, raw_token, last_tag_token, last_token, tokens);\n      last_tag_token = parser_token;\n    } else if ((raw_token.type === TOKEN.ATTRIBUTE || raw_token.type === TOKEN.EQUALS || raw_token.type === TOKEN.VALUE) ||\n      (raw_token.type === TOKEN.TEXT && !last_tag_token.tag_complete)) {\n      parser_token = this._handle_inside_tag(printer, raw_token, last_tag_token, last_token);\n    } else if (raw_token.type === TOKEN.TAG_CLOSE) {\n      parser_token = this._handle_tag_close(printer, raw_token, last_tag_token);\n    } else if (raw_token.type === TOKEN.TEXT) {\n      parser_token = this._handle_text(printer, raw_token, last_tag_token);\n    } else if (raw_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n      parser_token = this._handle_control_flow_open(printer, raw_token);\n    } else if (raw_token.type === TOKEN.CONTROL_FLOW_CLOSE) {\n      parser_token = this._handle_control_flow_close(printer, raw_token);\n    } else {\n      // This should never happen, but if it does. Print the raw token\n      printer.add_raw_token(raw_token);\n    }\n\n    last_token = parser_token;\n\n    raw_token = tokens.next();\n  }\n  var sweet_code = printer._output.get_code(eol);\n\n  return sweet_code;\n};\n\nBeautifier.prototype._handle_control_flow_open = function(printer, raw_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n  printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  if (raw_token.newlines) {\n    printer.print_preserved_newlines(raw_token);\n  } else {\n    printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  }\n  printer.print_token(raw_token);\n  printer.indent();\n  return parser_token;\n};\n\nBeautifier.prototype._handle_control_flow_close = function(printer, raw_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n\n  printer.deindent();\n  if (raw_token.newlines) {\n    printer.print_preserved_newlines(raw_token);\n  } else {\n    printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  }\n  printer.print_token(raw_token);\n  return parser_token;\n};\n\nBeautifier.prototype._handle_tag_close = function(printer, raw_token, last_tag_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n  printer.alignment_size = 0;\n  last_tag_token.tag_complete = true;\n\n  printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  if (last_tag_token.is_unformatted) {\n    printer.add_raw_token(raw_token);\n  } else {\n    if (last_tag_token.tag_start_char === '<') {\n      printer.set_space_before_token(raw_token.text[0] === '/', true); // space before />, no space before >\n      if (this._is_wrap_attributes_force_expand_multiline && last_tag_token.has_wrapped_attrs) {\n        printer.print_newline(false);\n      }\n    }\n    printer.print_token(raw_token);\n\n  }\n\n  if (last_tag_token.indent_content &&\n    !(last_tag_token.is_unformatted || last_tag_token.is_content_unformatted)) {\n    printer.indent();\n\n    // only indent once per opened tag\n    last_tag_token.indent_content = false;\n  }\n\n  if (!last_tag_token.is_inline_element &&\n    !(last_tag_token.is_unformatted || last_tag_token.is_content_unformatted)) {\n    printer.set_wrap_point();\n  }\n\n  return parser_token;\n};\n\nBeautifier.prototype._handle_inside_tag = function(printer, raw_token, last_tag_token, last_token) {\n  var wrapped = last_tag_token.has_wrapped_attrs;\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n\n  printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  if (last_tag_token.is_unformatted) {\n    printer.add_raw_token(raw_token);\n  } else if (last_tag_token.tag_start_char === '{' && raw_token.type === TOKEN.TEXT) {\n    // For the insides of handlebars allow newlines or a single space between open and contents\n    if (printer.print_preserved_newlines(raw_token)) {\n      raw_token.newlines = 0;\n      printer.add_raw_token(raw_token);\n    } else {\n      printer.print_token(raw_token);\n    }\n  } else {\n    if (raw_token.type === TOKEN.ATTRIBUTE) {\n      printer.set_space_before_token(true);\n    } else if (raw_token.type === TOKEN.EQUALS) { //no space before =\n      printer.set_space_before_token(false);\n    } else if (raw_token.type === TOKEN.VALUE && raw_token.previous.type === TOKEN.EQUALS) { //no space before value\n      printer.set_space_before_token(false);\n    }\n\n    if (raw_token.type === TOKEN.ATTRIBUTE && last_tag_token.tag_start_char === '<') {\n      if (this._is_wrap_attributes_preserve || this._is_wrap_attributes_preserve_aligned) {\n        printer.traverse_whitespace(raw_token);\n        wrapped = wrapped || raw_token.newlines !== 0;\n      }\n\n      // Wrap for 'force' options, and if the number of attributes is at least that specified in 'wrap_attributes_min_attrs':\n      // 1. always wrap the second and beyond attributes\n      // 2. wrap the first attribute only if 'force-expand-multiline' is specified\n      if (this._is_wrap_attributes_force &&\n        last_tag_token.attr_count >= this._options.wrap_attributes_min_attrs &&\n        (last_token.type !== TOKEN.TAG_OPEN || // ie. second attribute and beyond\n          this._is_wrap_attributes_force_expand_multiline)) {\n        printer.print_newline(false);\n        wrapped = true;\n      }\n    }\n    printer.print_token(raw_token);\n    wrapped = wrapped || printer.previous_token_wrapped();\n    last_tag_token.has_wrapped_attrs = wrapped;\n  }\n  return parser_token;\n};\n\nBeautifier.prototype._handle_text = function(printer, raw_token, last_tag_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: 'TK_CONTENT'\n  };\n  if (last_tag_token.custom_beautifier_name) { //check if we need to format javascript\n    this._print_custom_beatifier_text(printer, raw_token, last_tag_token);\n  } else if (last_tag_token.is_unformatted || last_tag_token.is_content_unformatted) {\n    printer.add_raw_token(raw_token);\n  } else {\n    printer.traverse_whitespace(raw_token);\n    printer.print_token(raw_token);\n  }\n  return parser_token;\n};\n\nBeautifier.prototype._print_custom_beatifier_text = function(printer, raw_token, last_tag_token) {\n  var local = this;\n  if (raw_token.text !== '') {\n\n    var text = raw_token.text,\n      _beautifier,\n      script_indent_level = 1,\n      pre = '',\n      post = '';\n    if (last_tag_token.custom_beautifier_name === 'javascript' && typeof this._js_beautify === 'function') {\n      _beautifier = this._js_beautify;\n    } else if (last_tag_token.custom_beautifier_name === 'css' && typeof this._css_beautify === 'function') {\n      _beautifier = this._css_beautify;\n    } else if (last_tag_token.custom_beautifier_name === 'html') {\n      _beautifier = function(html_source, options) {\n        var beautifier = new Beautifier(html_source, options, local._js_beautify, local._css_beautify);\n        return beautifier.beautify();\n      };\n    }\n\n    if (this._options.indent_scripts === \"keep\") {\n      script_indent_level = 0;\n    } else if (this._options.indent_scripts === \"separate\") {\n      script_indent_level = -printer.indent_level;\n    }\n\n    var indentation = printer.get_full_indent(script_indent_level);\n\n    // if there is at least one empty line at the end of this text, strip it\n    // we'll be adding one back after the text but before the containing tag.\n    text = text.replace(/\\n[ \\t]*$/, '');\n\n    // Handle the case where content is wrapped in a comment or cdata.\n    if (last_tag_token.custom_beautifier_name !== 'html' &&\n      text[0] === '<' && text.match(/^(<!--|<!\\[CDATA\\[)/)) {\n      var matched = /^(<!--[^\\n]*|<!\\[CDATA\\[)(\\n?)([ \\t\\n]*)([\\s\\S]*)(-->|]]>)$/.exec(text);\n\n      // if we start to wrap but don't finish, print raw\n      if (!matched) {\n        printer.add_raw_token(raw_token);\n        return;\n      }\n\n      pre = indentation + matched[1] + '\\n';\n      text = matched[4];\n      if (matched[5]) {\n        post = indentation + matched[5];\n      }\n\n      // if there is at least one empty line at the end of this text, strip it\n      // we'll be adding one back after the text but before the containing tag.\n      text = text.replace(/\\n[ \\t]*$/, '');\n\n      if (matched[2] || matched[3].indexOf('\\n') !== -1) {\n        // if the first line of the non-comment text has spaces\n        // use that as the basis for indenting in null case.\n        matched = matched[3].match(/[ \\t]+$/);\n        if (matched) {\n          raw_token.whitespace_before = matched[0];\n        }\n      }\n    }\n\n    if (text) {\n      if (_beautifier) {\n\n        // call the Beautifier if avaliable\n        var Child_options = function() {\n          this.eol = '\\n';\n        };\n        Child_options.prototype = this._options.raw_options;\n        var child_options = new Child_options();\n        text = _beautifier(indentation + text, child_options);\n      } else {\n        // simply indent the string otherwise\n        var white = raw_token.whitespace_before;\n        if (white) {\n          text = text.replace(new RegExp('\\n(' + white + ')?', 'g'), '\\n');\n        }\n\n        text = indentation + text.replace(/\\n/g, '\\n' + indentation);\n      }\n    }\n\n    if (pre) {\n      if (!text) {\n        text = pre + post;\n      } else {\n        text = pre + text + '\\n' + post;\n      }\n    }\n\n    printer.print_newline(false);\n    if (text) {\n      raw_token.text = text;\n      raw_token.whitespace_before = '';\n      raw_token.newlines = 0;\n      printer.add_raw_token(raw_token);\n      printer.print_newline(true);\n    }\n  }\n};\n\nBeautifier.prototype._handle_tag_open = function(printer, raw_token, last_tag_token, last_token, tokens) {\n  var parser_token = this._get_tag_open_token(raw_token);\n\n  if ((last_tag_token.is_unformatted || last_tag_token.is_content_unformatted) &&\n    !last_tag_token.is_empty_element &&\n    raw_token.type === TOKEN.TAG_OPEN && !parser_token.is_start_tag) {\n    // End element tags for unformatted or content_unformatted elements\n    // are printed raw to keep any newlines inside them exactly the same.\n    printer.add_raw_token(raw_token);\n    parser_token.start_tag_token = this._tag_stack.try_pop(parser_token.tag_name);\n  } else {\n    printer.traverse_whitespace(raw_token);\n    this._set_tag_position(printer, raw_token, parser_token, last_tag_token, last_token);\n    if (!parser_token.is_inline_element) {\n      printer.set_wrap_point();\n    }\n    printer.print_token(raw_token);\n  }\n\n  // count the number of attributes\n  if (parser_token.is_start_tag && this._is_wrap_attributes_force) {\n    var peek_index = 0;\n    var peek_token;\n    do {\n      peek_token = tokens.peek(peek_index);\n      if (peek_token.type === TOKEN.ATTRIBUTE) {\n        parser_token.attr_count += 1;\n      }\n      peek_index += 1;\n    } while (peek_token.type !== TOKEN.EOF && peek_token.type !== TOKEN.TAG_CLOSE);\n  }\n\n  //indent attributes an auto, forced, aligned or forced-align line-wrap\n  if (this._is_wrap_attributes_force_aligned || this._is_wrap_attributes_aligned_multiple || this._is_wrap_attributes_preserve_aligned) {\n    parser_token.alignment_size = raw_token.text.length + 1;\n  }\n\n  if (!parser_token.tag_complete && !parser_token.is_unformatted) {\n    printer.alignment_size = parser_token.alignment_size;\n  }\n\n  return parser_token;\n};\n\nvar TagOpenParserToken = function(options, parent, raw_token) {\n  this.parent = parent || null;\n  this.text = '';\n  this.type = 'TK_TAG_OPEN';\n  this.tag_name = '';\n  this.is_inline_element = false;\n  this.is_unformatted = false;\n  this.is_content_unformatted = false;\n  this.is_empty_element = false;\n  this.is_start_tag = false;\n  this.is_end_tag = false;\n  this.indent_content = false;\n  this.multiline_content = false;\n  this.custom_beautifier_name = null;\n  this.start_tag_token = null;\n  this.attr_count = 0;\n  this.has_wrapped_attrs = false;\n  this.alignment_size = 0;\n  this.tag_complete = false;\n  this.tag_start_char = '';\n  this.tag_check = '';\n\n  if (!raw_token) {\n    this.tag_complete = true;\n  } else {\n    var tag_check_match;\n\n    this.tag_start_char = raw_token.text[0];\n    this.text = raw_token.text;\n\n    if (this.tag_start_char === '<') {\n      tag_check_match = raw_token.text.match(/^<([^\\s>]*)/);\n      this.tag_check = tag_check_match ? tag_check_match[1] : '';\n    } else {\n      tag_check_match = raw_token.text.match(/^{{~?(?:[\\^]|#\\*?)?([^\\s}]+)/);\n      this.tag_check = tag_check_match ? tag_check_match[1] : '';\n\n      // handle \"{{#> myPartial}}\" or \"{{~#> myPartial}}\"\n      if ((raw_token.text.startsWith('{{#>') || raw_token.text.startsWith('{{~#>')) && this.tag_check[0] === '>') {\n        if (this.tag_check === '>' && raw_token.next !== null) {\n          this.tag_check = raw_token.next.text.split(' ')[0];\n        } else {\n          this.tag_check = raw_token.text.split('>')[1];\n        }\n      }\n    }\n\n    this.tag_check = this.tag_check.toLowerCase();\n\n    if (raw_token.type === TOKEN.COMMENT) {\n      this.tag_complete = true;\n    }\n\n    this.is_start_tag = this.tag_check.charAt(0) !== '/';\n    this.tag_name = !this.is_start_tag ? this.tag_check.substr(1) : this.tag_check;\n    this.is_end_tag = !this.is_start_tag ||\n      (raw_token.closed && raw_token.closed.text === '/>');\n\n    // if whitespace handler ~ included (i.e. {{~#if true}}), handlebars tags start at pos 3 not pos 2\n    var handlebar_starts = 2;\n    if (this.tag_start_char === '{' && this.text.length >= 3) {\n      if (this.text.charAt(2) === '~') {\n        handlebar_starts = 3;\n      }\n    }\n\n    // handlebars tags that don't start with # or ^ are single_tags, and so also start and end.\n    // if they start with # or ^, they are still considered single tags if indenting of handlebars is set to false\n    this.is_end_tag = this.is_end_tag ||\n      (this.tag_start_char === '{' && (!options.indent_handlebars || this.text.length < 3 || (/[^#\\^]/.test(this.text.charAt(handlebar_starts)))));\n  }\n};\n\nBeautifier.prototype._get_tag_open_token = function(raw_token) { //function to get a full tag and parse its type\n  var parser_token = new TagOpenParserToken(this._options, this._tag_stack.get_parser_token(), raw_token);\n\n  parser_token.alignment_size = this._options.wrap_attributes_indent_size;\n\n  parser_token.is_end_tag = parser_token.is_end_tag ||\n    in_array(parser_token.tag_check, this._options.void_elements);\n\n  parser_token.is_empty_element = parser_token.tag_complete ||\n    (parser_token.is_start_tag && parser_token.is_end_tag);\n\n  parser_token.is_unformatted = !parser_token.tag_complete && in_array(parser_token.tag_check, this._options.unformatted);\n  parser_token.is_content_unformatted = !parser_token.is_empty_element && in_array(parser_token.tag_check, this._options.content_unformatted);\n  parser_token.is_inline_element = in_array(parser_token.tag_name, this._options.inline) || (this._options.inline_custom_elements && parser_token.tag_name.includes(\"-\")) || parser_token.tag_start_char === '{';\n\n  return parser_token;\n};\n\nBeautifier.prototype._set_tag_position = function(printer, raw_token, parser_token, last_tag_token, last_token) {\n\n  if (!parser_token.is_empty_element) {\n    if (parser_token.is_end_tag) { //this tag is a double tag so check for tag-ending\n      parser_token.start_tag_token = this._tag_stack.try_pop(parser_token.tag_name); //remove it and all ancestors\n    } else { // it's a start-tag\n      // check if this tag is starting an element that has optional end element\n      // and do an ending needed\n      if (this._do_optional_end_element(parser_token)) {\n        if (!parser_token.is_inline_element) {\n          printer.print_newline(false);\n        }\n      }\n\n      this._tag_stack.record_tag(parser_token); //push it on the tag stack\n\n      if ((parser_token.tag_name === 'script' || parser_token.tag_name === 'style') &&\n        !(parser_token.is_unformatted || parser_token.is_content_unformatted)) {\n        parser_token.custom_beautifier_name = get_custom_beautifier_name(parser_token.tag_check, raw_token);\n      }\n    }\n  }\n\n  if (in_array(parser_token.tag_check, this._options.extra_liners)) { //check if this double needs an extra line\n    printer.print_newline(false);\n    if (!printer._output.just_added_blankline()) {\n      printer.print_newline(true);\n    }\n  }\n\n  if (parser_token.is_empty_element) { //if this tag name is a single tag type (either in the list or has a closing /)\n\n    // if you hit an else case, reset the indent level if you are inside an:\n    // 'if', 'unless', or 'each' block.\n    if (parser_token.tag_start_char === '{' && parser_token.tag_check === 'else') {\n      this._tag_stack.indent_to_tag(['if', 'unless', 'each']);\n      parser_token.indent_content = true;\n      // Don't add a newline if opening {{#if}} tag is on the current line\n      var foundIfOnCurrentLine = printer.current_line_has_match(/{{#if/);\n      if (!foundIfOnCurrentLine) {\n        printer.print_newline(false);\n      }\n    }\n\n    // Don't add a newline before elements that should remain where they are.\n    if (parser_token.tag_name === '!--' && last_token.type === TOKEN.TAG_CLOSE &&\n      last_tag_token.is_end_tag && parser_token.text.indexOf('\\n') === -1) {\n      //Do nothing. Leave comments on same line.\n    } else {\n      if (!(parser_token.is_inline_element || parser_token.is_unformatted)) {\n        printer.print_newline(false);\n      }\n      this._calcluate_parent_multiline(printer, parser_token);\n    }\n  } else if (parser_token.is_end_tag) { //this tag is a double tag so check for tag-ending\n    var do_end_expand = false;\n\n    // deciding whether a block is multiline should not be this hard\n    do_end_expand = parser_token.start_tag_token && parser_token.start_tag_token.multiline_content;\n    do_end_expand = do_end_expand || (!parser_token.is_inline_element &&\n      !(last_tag_token.is_inline_element || last_tag_token.is_unformatted) &&\n      !(last_token.type === TOKEN.TAG_CLOSE && parser_token.start_tag_token === last_tag_token) &&\n      last_token.type !== 'TK_CONTENT'\n    );\n\n    if (parser_token.is_content_unformatted || parser_token.is_unformatted) {\n      do_end_expand = false;\n    }\n\n    if (do_end_expand) {\n      printer.print_newline(false);\n    }\n  } else { // it's a start-tag\n    parser_token.indent_content = !parser_token.custom_beautifier_name;\n\n    if (parser_token.tag_start_char === '<') {\n      if (parser_token.tag_name === 'html') {\n        parser_token.indent_content = this._options.indent_inner_html;\n      } else if (parser_token.tag_name === 'head') {\n        parser_token.indent_content = this._options.indent_head_inner_html;\n      } else if (parser_token.tag_name === 'body') {\n        parser_token.indent_content = this._options.indent_body_inner_html;\n      }\n    }\n\n    if (!(parser_token.is_inline_element || parser_token.is_unformatted) &&\n      (last_token.type !== 'TK_CONTENT' || parser_token.is_content_unformatted)) {\n      printer.print_newline(false);\n    }\n\n    this._calcluate_parent_multiline(printer, parser_token);\n  }\n};\n\nBeautifier.prototype._calcluate_parent_multiline = function(printer, parser_token) {\n  if (parser_token.parent && printer._output.just_added_newline() &&\n    !((parser_token.is_inline_element || parser_token.is_unformatted) && parser_token.parent.is_inline_element)) {\n    parser_token.parent.multiline_content = true;\n  }\n};\n\n//To be used for <p> tag special case:\nvar p_closers = ['address', 'article', 'aside', 'blockquote', 'details', 'div', 'dl', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hr', 'main', 'menu', 'nav', 'ol', 'p', 'pre', 'section', 'table', 'ul'];\nvar p_parent_excludes = ['a', 'audio', 'del', 'ins', 'map', 'noscript', 'video'];\n\nBeautifier.prototype._do_optional_end_element = function(parser_token) {\n  var result = null;\n  // NOTE: cases of \"if there is no more content in the parent element\"\n  // are handled automatically by the beautifier.\n  // It assumes parent or ancestor close tag closes all children.\n  // https://www.w3.org/TR/html5/syntax.html#optional-tags\n  if (parser_token.is_empty_element || !parser_token.is_start_tag || !parser_token.parent) {\n    return;\n\n  }\n\n  if (parser_token.tag_name === 'body') {\n    // A head element’s end tag may be omitted if the head element is not immediately followed by a space character or a comment.\n    result = result || this._tag_stack.try_pop('head');\n\n    //} else if (parser_token.tag_name === 'body') {\n    // DONE: A body element’s end tag may be omitted if the body element is not immediately followed by a comment.\n\n  } else if (parser_token.tag_name === 'li') {\n    // An li element’s end tag may be omitted if the li element is immediately followed by another li element or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('li', ['ol', 'ul', 'menu']);\n\n  } else if (parser_token.tag_name === 'dd' || parser_token.tag_name === 'dt') {\n    // A dd element’s end tag may be omitted if the dd element is immediately followed by another dd element or a dt element, or if there is no more content in the parent element.\n    // A dt element’s end tag may be omitted if the dt element is immediately followed by another dt element or a dd element.\n    result = result || this._tag_stack.try_pop('dt', ['dl']);\n    result = result || this._tag_stack.try_pop('dd', ['dl']);\n\n\n  } else if (parser_token.parent.tag_name === 'p' && p_closers.indexOf(parser_token.tag_name) !== -1) {\n    // IMPORTANT: this else-if works because p_closers has no overlap with any other element we look for in this method\n    // check for the parent element is an HTML element that is not an <a>, <audio>, <del>, <ins>, <map>, <noscript>, or <video> element,  or an autonomous custom element.\n    // To do this right, this needs to be coded as an inclusion of the inverse of the exclusion above.\n    // But to start with (if we ignore \"autonomous custom elements\") the exclusion would be fine.\n    var p_parent = parser_token.parent.parent;\n    if (!p_parent || p_parent_excludes.indexOf(p_parent.tag_name) === -1) {\n      result = result || this._tag_stack.try_pop('p');\n    }\n  } else if (parser_token.tag_name === 'rp' || parser_token.tag_name === 'rt') {\n    // An rt element’s end tag may be omitted if the rt element is immediately followed by an rt or rp element, or if there is no more content in the parent element.\n    // An rp element’s end tag may be omitted if the rp element is immediately followed by an rt or rp element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('rt', ['ruby', 'rtc']);\n    result = result || this._tag_stack.try_pop('rp', ['ruby', 'rtc']);\n\n  } else if (parser_token.tag_name === 'optgroup') {\n    // An optgroup element’s end tag may be omitted if the optgroup element is immediately followed by another optgroup element, or if there is no more content in the parent element.\n    // An option element’s end tag may be omitted if the option element is immediately followed by another option element, or if it is immediately followed by an optgroup element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('optgroup', ['select']);\n    //result = result || this._tag_stack.try_pop('option', ['select']);\n\n  } else if (parser_token.tag_name === 'option') {\n    // An option element’s end tag may be omitted if the option element is immediately followed by another option element, or if it is immediately followed by an optgroup element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('option', ['select', 'datalist', 'optgroup']);\n\n  } else if (parser_token.tag_name === 'colgroup') {\n    // DONE: A colgroup element’s end tag may be omitted if the colgroup element is not immediately followed by a space character or a comment.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n\n  } else if (parser_token.tag_name === 'thead') {\n    // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n    result = result || this._tag_stack.try_pop('colgroup', ['table']);\n\n    //} else if (parser_token.tag_name === 'caption') {\n    // DONE: A caption element’s end tag may be omitted if the caption element is not immediately followed by a space character or a comment.\n\n  } else if (parser_token.tag_name === 'tbody' || parser_token.tag_name === 'tfoot') {\n    // A thead element’s end tag may be omitted if the thead element is immediately followed by a tbody or tfoot element.\n    // A tbody element’s end tag may be omitted if the tbody element is immediately followed by a tbody or tfoot element, or if there is no more content in the parent element.\n    // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n    result = result || this._tag_stack.try_pop('colgroup', ['table']);\n    result = result || this._tag_stack.try_pop('thead', ['table']);\n    result = result || this._tag_stack.try_pop('tbody', ['table']);\n\n    //} else if (parser_token.tag_name === 'tfoot') {\n    // DONE: A tfoot element’s end tag may be omitted if there is no more content in the parent element.\n\n  } else if (parser_token.tag_name === 'tr') {\n    // A tr element’s end tag may be omitted if the tr element is immediately followed by another tr element, or if there is no more content in the parent element.\n    // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n    result = result || this._tag_stack.try_pop('colgroup', ['table']);\n    result = result || this._tag_stack.try_pop('tr', ['table', 'thead', 'tbody', 'tfoot']);\n\n  } else if (parser_token.tag_name === 'th' || parser_token.tag_name === 'td') {\n    // A td element’s end tag may be omitted if the td element is immediately followed by a td or th element, or if there is no more content in the parent element.\n    // A th element’s end tag may be omitted if the th element is immediately followed by a td or th element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('td', ['table', 'thead', 'tbody', 'tfoot', 'tr']);\n    result = result || this._tag_stack.try_pop('th', ['table', 'thead', 'tbody', 'tfoot', 'tr']);\n  }\n\n  // Start element omission not handled currently\n  // A head element’s start tag may be omitted if the element is empty, or if the first thing inside the head element is an element.\n  // A tbody element’s start tag may be omitted if the first thing inside the tbody element is a tr element, and if the element is not immediately preceded by a tbody, thead, or tfoot element whose end tag has been omitted. (It can’t be omitted if the element is empty.)\n  // A colgroup element’s start tag may be omitted if the first thing inside the colgroup element is a col element, and if the element is not immediately preceded by another colgroup element whose end tag has been omitted. (It can’t be omitted if the element is empty.)\n\n  // Fix up the parent of the parser token\n  parser_token.parent = this._tag_stack.get_parser_token();\n\n  return result;\n};\n\nmodule.exports.Beautifier = Beautifier;\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar Beautifier = require('./beautifier').Beautifier,\n  Options = require('./options').Options;\n\nfunction style_html(html_source, options, js_beautify, css_beautify) {\n  var beautifier = new Beautifier(html_source, options, js_beautify, css_beautify);\n  return beautifier.beautify();\n}\n\nmodule.exports = style_html;\nmodule.exports.defaultOptions = function() {\n  return new Options();\n};\n", "/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n'use strict';\n\nvar js_beautify = require('./javascript/index');\nvar css_beautify = require('./css/index');\nvar html_beautify = require('./html/index');\n\nfunction style_html(html_source, options, js, css) {\n  js = js || js_beautify;\n  css = css || css_beautify;\n  return html_beautify(html_source, options, js, css);\n}\nstyle_html.defaultOptions = html_beautify.defaultOptions;\n\nmodule.exports.js = js_beautify;\nmodule.exports.css = css_beautify;\nmodule.exports.html = style_html;\n", "/*jshint node:true */\n/* globals define */\n/*\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n*/\n\n'use strict';\n\n/**\nThe following batches are equivalent:\n\nvar beautify_js = require('js-beautify');\nvar beautify_js = require('js-beautify').js;\nvar beautify_js = require('js-beautify').js_beautify;\n\nvar beautify_css = require('js-beautify').css;\nvar beautify_css = require('js-beautify').css_beautify;\n\nvar beautify_html = require('js-beautify').html;\nvar beautify_html = require('js-beautify').html_beautify;\n\nAll methods returned accept two arguments, the source string and an options object.\n**/\n\nfunction get_beautify(js_beautify, css_beautify, html_beautify) {\n  // the default is js\n  var beautify = function(src, config) {\n    return js_beautify.js_beautify(src, config);\n  };\n\n  // short aliases\n  beautify.js = js_beautify.js_beautify;\n  beautify.css = css_beautify.css_beautify;\n  beautify.html = html_beautify.html_beautify;\n\n  // legacy aliases\n  beautify.js_beautify = js_beautify.js_beautify;\n  beautify.css_beautify = css_beautify.css_beautify;\n  beautify.html_beautify = html_beautify.html_beautify;\n\n  return beautify;\n}\n\nif (typeof define === \"function\" && define.amd) {\n  // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n  define([\n    \"./lib/beautify\",\n    \"./lib/beautify-css\",\n    \"./lib/beautify-html\"\n  ], function(js_beautify, css_beautify, html_beautify) {\n    return get_beautify(js_beautify, css_beautify, html_beautify);\n  });\n} else {\n  (function(mod) {\n    var beautifier = require('./src/index');\n    beautifier.js_beautify = beautifier.js;\n    beautifier.css_beautify = beautifier.css;\n    beautifier.html_beautify = beautifier.html;\n\n    mod.exports = get_beautify(beautifier, beautifier, beautifier);\n\n  })(module);\n}", "/*!\n * is-whitespace <https://github.com/jonschlinkert/is-whitespace>\n *\n * Copyright (c) 2014-2015, <PERSON>.\n * Licensed under the MIT License.\n */\n\n'use strict';\n\nvar cache;\n\nmodule.exports = function isWhitespace(str) {\n  return (typeof str === 'string') && regex().test(str);\n};\n\nfunction regex() {\n  // ensure that runtime compilation only happens once\n  return cache || (cache = new RegExp('^[\\\\s\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF\"]+$'));\n}\n", "/*!\n * is-extendable <https://github.com/jonschlinkert/is-extendable>\n *\n * Copyright (c) 2015, <PERSON>.\n * Licensed under the MIT License.\n */\n\n'use strict';\n\nmodule.exports = function isExtendable(val) {\n  return typeof val !== 'undefined' && val !== null\n    && (typeof val === 'object' || typeof val === 'function');\n};\n", "'use strict';\n\nvar isObject = require('is-extendable');\n\nmodule.exports = function extend(o/*, objects*/) {\n  if (!isObject(o)) { o = {}; }\n\n  var len = arguments.length;\n  for (var i = 1; i < len; i++) {\n    var obj = arguments[i];\n\n    if (isObject(obj)) {\n      assign(o, obj);\n    }\n  }\n  return o;\n};\n\nfunction assign(a, b) {\n  for (var key in b) {\n    if (hasOwn(b, key)) {\n      a[key] = b[key];\n    }\n  }\n}\n\n/**\n * Returns true if the given `key` is an own property of `obj`.\n */\n\nfunction hasOwn(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n", "/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\n// The _isBuffer check is for Safari 5-7 support, because it's missing\n// Object.prototype.constructor. Remove this eventually\nmodule.exports = function (obj) {\n  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)\n}\n\nfunction isBuffer (obj) {\n  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n\n// For Node v0.10 support. Remove this eventually.\nfunction isSlowBuffer (obj) {\n  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))\n}\n", "var isBuffer = require('is-buffer');\nvar toString = Object.prototype.toString;\n\n/**\n * Get the native `typeof` a value.\n *\n * @param  {*} `val`\n * @return {*} Native javascript type\n */\n\nmodule.exports = function kindOf(val) {\n  // primitivies\n  if (typeof val === 'undefined') {\n    return 'undefined';\n  }\n  if (val === null) {\n    return 'null';\n  }\n  if (val === true || val === false || val instanceof Boolean) {\n    return 'boolean';\n  }\n  if (typeof val === 'string' || val instanceof String) {\n    return 'string';\n  }\n  if (typeof val === 'number' || val instanceof Number) {\n    return 'number';\n  }\n\n  // functions\n  if (typeof val === 'function' || val instanceof Function) {\n    return 'function';\n  }\n\n  // array\n  if (typeof Array.isArray !== 'undefined' && Array.isArray(val)) {\n    return 'array';\n  }\n\n  // check for instances of RegExp and Date before calling `toString`\n  if (val instanceof RegExp) {\n    return 'regexp';\n  }\n  if (val instanceof Date) {\n    return 'date';\n  }\n\n  // other objects\n  var type = toString.call(val);\n\n  if (type === '[object RegExp]') {\n    return 'regexp';\n  }\n  if (type === '[object Date]') {\n    return 'date';\n  }\n  if (type === '[object Arguments]') {\n    return 'arguments';\n  }\n  if (type === '[object Error]') {\n    return 'error';\n  }\n\n  // buffer\n  if (isBuffer(val)) {\n    return 'buffer';\n  }\n\n  // es6: Map, WeakMap, Set, WeakSet\n  if (type === '[object Set]') {\n    return 'set';\n  }\n  if (type === '[object WeakSet]') {\n    return 'weakset';\n  }\n  if (type === '[object Map]') {\n    return 'map';\n  }\n  if (type === '[object WeakMap]') {\n    return 'weakmap';\n  }\n  if (type === '[object Symbol]') {\n    return 'symbol';\n  }\n\n  // typed arrays\n  if (type === '[object Int8Array]') {\n    return 'int8array';\n  }\n  if (type === '[object Uint8Array]') {\n    return 'uint8array';\n  }\n  if (type === '[object Uint8ClampedArray]') {\n    return 'uint8clampedarray';\n  }\n  if (type === '[object Int16Array]') {\n    return 'int16array';\n  }\n  if (type === '[object Uint16Array]') {\n    return 'uint16array';\n  }\n  if (type === '[object Int32Array]') {\n    return 'int32array';\n  }\n  if (type === '[object Uint32Array]') {\n    return 'uint32array';\n  }\n  if (type === '[object Float32Array]') {\n    return 'float32array';\n  }\n  if (type === '[object Float64Array]') {\n    return 'float64array';\n  }\n\n  // must be a plain object\n  return 'object';\n};\n", "/*!\n * condense-newlines <https://github.com/jonschlinkert/condense-newlines>\n *\n * Copyright (c) 2014 <PERSON>, contributors.\n * Licensed under the MIT License\n */\n\n'use strict';\n\nvar isWhitespace = require('is-whitespace');\nvar extend = require('extend-shallow');\nvar typeOf = require('kind-of');\n\nmodule.exports = function(str, options) {\n  var opts = extend({}, options);\n  var sep = opts.sep || '\\n\\n';\n  var min = opts.min;\n  var re;\n\n  if (typeof min === 'number' && min !== 2) {\n    re = new RegExp('(\\\\r\\\\n|\\\\n|\\\\u2424) {' + min + ',}');\n  }\n  if (typeof re === 'undefined') {\n    re = opts.regex || /(\\r\\n|\\n|\\u2424){2,}/g;\n  }\n\n  // if a line is 100% whitespace it will be trimmed, so that\n  // later we can condense newlines correctly\n  if (opts.keepWhitespace !== true) {\n    str = str.split('\\n').map(function(line) {\n      return isWhitespace(line) ? line.trim() : line;\n    }).join('\\n');\n  }\n\n  str = trailingNewline(str, opts);\n  return str.replace(re, sep);\n};\n\nfunction trailingNewline(str, options) {\n  var val = options.trailingNewline;\n  if (val === false) {\n    return str;\n  }\n\n  switch (typeOf(val)) {\n    case 'string':\n      str = str.replace(/\\s+$/, options.trailingNewline);\n      break;\n    case 'function':\n      str = options.trailingNewline(str);\n      break;\n    case 'undefined':\n    case 'boolean':\n    default: {\n      str = str.replace(/\\s+$/, '\\n');\n      break;\n    }\n  }\n  return str;\n}\n", "/*!\n * pretty <https://github.com/jonschlinkert/pretty>\n *\n * Copyright (c) 2013-2015, 2017, <PERSON>.\n * Released under the MIT License.\n */\n\n'use strict';\n\nvar beautify = require('js-beautify');\nvar condense = require('condense-newlines');\nvar extend = require('extend-shallow');\nvar defaults = {\n  unformatted: ['code', 'pre', 'em', 'strong', 'span'],\n  indent_inner_html: true,\n  indent_char: ' ',\n  indent_size: 2,\n  sep: '\\n'\n};\n\nmodule.exports = function pretty(str, options) {\n  var opts = extend({}, defaults, options);\n  str = beautify.html(str, opts);\n\n  if (opts.ocd === true) {\n    if (opts.newlines) opts.sep = opts.newlines;\n    return ocd(str, opts);\n  }\n\n  return str;\n};\n\nfunction ocd(str, options) {\n  // Normalize and condense all newlines\n  return condense(str, options)\n    // Remove empty whitespace the top of a file.\n    .replace(/^\\s+/g, '')\n    // Remove extra whitespace from eof\n    .replace(/\\s+$/g, '\\n')\n\n    // Add a space above each comment\n    .replace(/(\\s*<!--)/g, '\\n$1')\n    // Bring closing comments up to the same line as closing tag.\n    .replace(/>(\\s*)(?=<!--\\s*\\/)/g, '> ');\n}\n", "var __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/render.ts\nimport * as ReactDomServer from \"react-dom/server\";\nimport { convert } from \"html-to-text\";\nimport pretty from \"pretty\";\nvar render = (component, options) => {\n  if (options == null ? void 0 : options.plainText) {\n    return renderAsPlainText(component, options);\n  }\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const markup = ReactDomServer.renderToStaticMarkup(component);\n  const document = `${doctype}${markup}`;\n  if (options && options.pretty) {\n    return pretty(document);\n  }\n  return document;\n};\nvar renderAsPlainText = (component, _options) => {\n  return convert(ReactDomServer.renderToStaticMarkup(component), {\n    selectors: [\n      { selector: \"img\", format: \"skip\" },\n      { selector: \"#__react-email-preview\", format: \"skip\" }\n    ]\n  });\n};\n\n// src/render-async.ts\nimport { convert as convert2 } from \"html-to-text\";\nimport pretty2 from \"pretty\";\nvar readStream = (readableStream) => __async(void 0, null, function* () {\n  const reader = readableStream.getReader();\n  const chunks = [];\n  while (true) {\n    const { value, done } = yield reader.read();\n    if (done) {\n      break;\n    }\n    chunks.push(value);\n  }\n  return chunks.map((chunk) => new TextDecoder(\"utf-8\").decode(chunk)).join(\"\");\n});\nvar renderAsync = (component, options) => __async(void 0, null, function* () {\n  const reactDOMServer = (yield import(\"react-dom/server\")).default;\n  const renderToStream = reactDOMServer.renderToReadableStream || reactDOMServer.renderToString || reactDOMServer.renderToPipeableStream;\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const readableStream = yield renderToStream(component);\n  const html = typeof readableStream === \"string\" ? readableStream : yield readStream(readableStream);\n  if (options == null ? void 0 : options.plainText) {\n    return convert2(html, {\n      selectors: [\n        { selector: \"img\", format: \"skip\" },\n        { selector: \"#__react-email-preview\", format: \"skip\" }\n      ]\n    });\n  }\n  const document = `${doctype}${html}`;\n  if (options == null ? void 0 : options.pretty) {\n    return pretty2(document);\n  }\n  return document;\n});\nexport {\n  render,\n  renderAsync\n};\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// package.json\nvar version = \"2.1.0\";\n\n// src/api-keys/api-keys.ts\nvar ApiKeys = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/api-keys\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/api-keys\");\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/api-keys/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/audiences/audiences.ts\nvar Audiences = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/audiences\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/audiences\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/batch/batch.ts\nimport { renderAsync } from \"@react-email/render\";\nvar Batch = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      for (const email of payload) {\n        if (email.react) {\n          email.html = yield renderAsync(email.react);\n          delete email.react;\n        }\n      }\n      const data = yield this.resend.post(\n        \"/emails/batch\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n};\n\n// src/contacts/contacts.ts\nvar Contacts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        `/audiences/${payload.audience_id}/contacts`,\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list(_0) {\n    return __async(this, arguments, function* ({\n      audience_id\n    }) {\n      const data = yield this.resend.get(\n        `/audiences/${audience_id}/contacts`\n      );\n      return data;\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* ({\n      audience_id,\n      id\n    }) {\n      const data = yield this.resend.get(\n        `/audiences/${audience_id}/contacts/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/audiences/${payload.audience_id}/contacts/${payload.id}`,\n        payload\n      );\n      return data;\n    });\n  }\n  remove(_0) {\n    return __async(this, arguments, function* ({\n      audience_id,\n      id\n    }) {\n      const data = yield this.resend.delete(\n        `/audiences/${audience_id}/contacts/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/domains/domains.ts\nvar Domains = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/domains\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/domains\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  verify(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/domains/${id}/verify`\n      );\n      return data;\n    });\n  }\n};\n\n// src/emails/emails.ts\nimport { renderAsync as renderAsync2 } from \"@react-email/render\";\nvar Emails = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        payload.html = yield renderAsync2(payload.react);\n        delete payload.react;\n      }\n      const data = yield this.resend.post(\n        \"/emails\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/emails/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/guards.ts\nvar isResendErrorResponse = (response) => {\n  if (typeof response !== \"object\" || response === null) {\n    return false;\n  }\n  const error = response;\n  if (typeof error !== \"object\" || error === null) {\n    return false;\n  }\n  const { message, name } = error;\n  return typeof message === \"string\" && typeof name === \"string\";\n};\n\n// src/resend.ts\nvar defaultBaseUrl = \"https://api.resend.com\";\nvar defaultUserAgent = `resend-node:${version}`;\nvar baseUrl = typeof process !== \"undefined\" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;\nvar userAgent = typeof process !== \"undefined\" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;\nvar Resend = class {\n  constructor(key) {\n    this.key = key;\n    this.apiKeys = new ApiKeys(this);\n    this.audiences = new Audiences(this);\n    this.batch = new Batch(this);\n    this.contacts = new Contacts(this);\n    this.domains = new Domains(this);\n    this.emails = new Emails(this);\n    if (!key) {\n      if (typeof process !== \"undefined\" && process.env) {\n        this.key = process.env.RESEND_API_KEY;\n      }\n      if (!this.key) {\n        throw new Error(\n          'Missing API key. Pass it to the constructor `new Resend(\"re_123\")`'\n        );\n      }\n    }\n    this.headers = new Headers({\n      Authorization: `Bearer ${this.key}`,\n      \"User-Agent\": userAgent,\n      \"Content-Type\": \"application/json\"\n    });\n  }\n  fetchRequest(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const response = yield fetch(`${baseUrl}${path}`, options);\n      if (!response.ok) {\n        const error = yield response.json();\n        if (isResendErrorResponse(error)) {\n          return { data: null, error };\n        }\n        return { data: null, error };\n      }\n      const data = yield response.json();\n      return { data, error: null };\n    });\n  }\n  post(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"POST\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"GET\",\n        headers: this.headers\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  put(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PUT\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  patch(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PATCH\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  delete(path, query) {\n    return __async(this, null, function* () {\n      const requestOptions = {\n        method: \"DELETE\",\n        headers: this.headers,\n        body: JSON.stringify(query)\n      };\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n};\nexport {\n  Resend\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AA6BA,aAAS,WAAW,QAAQ;AAC1B,WAAK,WAAW;AAChB,WAAK,oBAAoB;AAEzB,WAAK,iBAAiB;AACtB,WAAK,oBAAoB;AACzB,WAAK,qBAAqB;AAC1B,WAAK,+BAA+B;AACpC,WAAK,4BAA4B;AACjC,WAAK,+BAA+B;AAEpC,WAAK,UAAU,CAAC;AAAA,IAClB;AAEA,eAAW,UAAU,cAAc,WAAW;AAC5C,UAAI,OAAO,IAAI,WAAW,KAAK,QAAQ;AACvC,WAAK,WAAW,KAAK,gBAAgB,KAAK,iBAAiB;AAC3D,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,OAAO,SAAS,OAAO;AAC1C,UAAI,QAAQ,GAAG;AACb,eAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,KAAK;AAAA,MACjD,OAAO;AACL,eAAO,KAAK,QAAQ,KAAK;AAAA,MAC3B;AAAA,IACF;AAEA,eAAW,UAAU,YAAY,SAAS,SAAS;AACjD,eAAS,oBAAoB,KAAK,QAAQ,SAAS,GAAG,qBAAqB,GAAG,qBAAqB;AACjG,YAAI,KAAK,QAAQ,iBAAiB,EAAE,MAAM,OAAO,GAAG;AAClD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,aAAa,SAAS,QAAQ,WAAW;AAC5D,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,iBAAiB,UAAU;AAChC,aAAK,oBAAoB,aAAa;AACtC,aAAK,oBAAoB,KAAK,SAAS,gBAAgB,KAAK,gBAAgB,KAAK,iBAAiB;AAAA,MACpG;AAAA,IACF;AAEA,eAAW,UAAU,kBAAkB,WAAW;AAChD,UAAI,KAAK,SAAS,kBAAkB;AAClC,aAAK,qBAAqB,KAAK,QAAQ;AACvC,aAAK,+BAA+B,KAAK;AACzC,aAAK,4BAA4B,KAAK,SAAS,UAAU;AACzD,aAAK,+BAA+B,KAAK,SAAS,UAAU;AAAA,MAC9D;AAAA,IACF;AAEA,eAAW,UAAU,eAAe,WAAW;AAC7C,aAAO,KAAK,sBACV,KAAK,oBAAoB,KAAK,SAAS,oBACvC,KAAK,+BAA+B,KAAK,SAAS,UAAU;AAAA,IAChE;AAEA,eAAW,UAAU,cAAc,WAAW;AAC5C,UAAI,KAAK,aAAa,GAAG;AACvB,aAAK,SAAS,aAAa;AAC3B,YAAI,OAAO,KAAK,SAAS;AACzB,aAAK,WAAW,KAAK,2BAA2B,KAAK,4BAA4B;AACjF,aAAK,UAAU,KAAK,QAAQ,MAAM,KAAK,kBAAkB;AACzD,aAAK,UAAU,KAAK,QAAQ,MAAM,GAAG,KAAK,kBAAkB;AAE5D,aAAK,qBAAqB,KAAK,oBAAoB,KAAK;AACxD,aAAK,oBAAoB,KAAK;AAE9B,YAAI,KAAK,QAAQ,CAAC,MAAM,KAAK;AAC3B,eAAK,QAAQ,OAAO,GAAG,CAAC;AACxB,eAAK,qBAAqB;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,WAAW,WAAW;AACzC,aAAO,KAAK,QAAQ,WAAW;AAAA,IACjC;AAEA,eAAW,UAAU,OAAO,WAAW;AACrC,UAAI,CAAC,KAAK,SAAS,GAAG;AACpB,eAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AAAA,MAC7C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,eAAW,UAAU,OAAO,SAAS,MAAM;AACzC,WAAK,QAAQ,KAAK,IAAI;AACtB,UAAI,qBAAqB,KAAK,YAAY,IAAI;AAC9C,UAAI,uBAAuB,IAAI;AAC7B,aAAK,oBAAoB,KAAK,SAAS;AAAA,MACzC,OAAO;AACL,aAAK,qBAAqB,KAAK;AAAA,MACjC;AAAA,IACF;AAEA,eAAW,UAAU,MAAM,WAAW;AACpC,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,SAAS,GAAG;AACpB,eAAO,KAAK,QAAQ,IAAI;AACxB,aAAK,qBAAqB,KAAK;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AAGA,eAAW,UAAU,iBAAiB,WAAW;AAC/C,UAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAK,kBAAkB;AACvB,aAAK,qBAAqB,KAAK,SAAS;AAAA,MAC1C;AAAA,IACF;AAEA,eAAW,UAAU,sBAAsB,WAAW;AACpD,UAAI,KAAK,4BAA4B,GAAG;AACtC,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AACA,eAAW,UAAU,OAAO,WAAW;AACrC,aAAO,KAAK,KAAK,MAAM,KAAK;AAC1B,aAAK,QAAQ,IAAI;AACjB,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,eAAW,UAAU,WAAW,WAAW;AACzC,UAAI,SAAS;AACb,UAAI,KAAK,SAAS,GAAG;AACnB,YAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAS,KAAK,SAAS,kBAAkB,KAAK,cAAc;AAAA,QAC9D;AAAA,MACF,OAAO;AACL,iBAAS,KAAK,SAAS,kBAAkB,KAAK,gBAAgB,KAAK,iBAAiB;AACpF,kBAAU,KAAK,QAAQ,KAAK,EAAE;AAAA,MAChC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,kBAAkB,SAAS,kBAAkB;AACpD,WAAK,UAAU,CAAC,EAAE;AAClB,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,kBAAkB,QAAQ;AAC/B,UAAI,CAAC,QAAQ,kBAAkB;AAC7B,aAAK,kBAAkB,IAAI,MAAM,QAAQ,cAAc,CAAC,EAAE,KAAK,QAAQ,WAAW;AAAA,MACpF;AAGA,yBAAmB,oBAAoB;AACvC,UAAI,QAAQ,eAAe,GAAG;AAC5B,2BAAmB,IAAI,MAAM,QAAQ,eAAe,CAAC,EAAE,KAAK,KAAK,eAAe;AAAA,MAClF;AAEA,WAAK,gBAAgB;AACrB,WAAK,uBAAuB,iBAAiB;AAAA,IAC/C;AAEA,sBAAkB,UAAU,kBAAkB,SAAS,QAAQ,QAAQ;AACrE,UAAI,SAAS,KAAK;AAClB,eAAS,UAAU;AACnB,UAAI,SAAS,GAAG;AACd,iBAAS;AAAA,MACX;AACA,gBAAU,SAAS,KAAK;AACxB,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,sBAAkB,UAAU,oBAAoB,SAAS,cAAc,QAAQ;AAC7E,UAAI,SAAS,KAAK;AAClB,eAAS,UAAU;AACnB,UAAI,eAAe,GAAG;AACpB,uBAAe;AACf,iBAAS;AAAA,MACX;AACA,gBAAU,eAAe,KAAK;AAC9B,WAAK,eAAe,MAAM;AAC1B,gBAAU,KAAK,QAAQ,MAAM;AAC7B,aAAO;AAAA,IACT;AAEA,sBAAkB,UAAU,iBAAiB,SAAS,QAAQ;AAC5D,aAAO,UAAU,KAAK,QAAQ,QAAQ;AACpC,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAEA,sBAAkB,UAAU,eAAe,WAAW;AACpD,UAAI,SAAS,KAAK,QAAQ;AAC1B,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,KAAK,iBAAiB,UAAU,KAAK,eAAe;AACtD,iBAAS,KAAK,MAAM,SAAS,KAAK,aAAa;AAC/C,kBAAU,SAAS,KAAK;AACxB,iBAAS,IAAI,MAAM,SAAS,CAAC,EAAE,KAAK,KAAK,eAAe;AAAA,MAC1D;AACA,UAAI,QAAQ;AACV,kBAAU,IAAI,MAAM,SAAS,CAAC,EAAE,KAAK,GAAG;AAAA,MAC1C;AAEA,WAAK,QAAQ,KAAK,MAAM;AAAA,IAC1B;AAEA,aAAS,OAAO,SAAS,kBAAkB;AACzC,WAAK,iBAAiB,IAAI,kBAAkB,SAAS,gBAAgB;AACrE,WAAK,MAAM;AACX,WAAK,oBAAoB,QAAQ;AACjC,WAAK,cAAc,QAAQ;AAC3B,WAAK,mBAAmB,QAAQ;AAChC,WAAK,qBAAqB,QAAQ;AAClC,WAAK,UAAU,CAAC;AAChB,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,YAAY,IAAI,WAAW,IAAI;AACpC,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,yBAAyB;AAE9B,WAAK,iBAAiB;AAAA,IACxB;AAEA,WAAO,UAAU,mBAAmB,WAAW;AAC7C,WAAK,gBAAgB,KAAK;AAC1B,WAAK,eAAe,KAAK,UAAU,YAAY;AAC/C,WAAK,QAAQ,KAAK,KAAK,YAAY;AAAA,IACrC;AAEA,WAAO,UAAU,kBAAkB,WAAW;AAC5C,aAAO,KAAK,QAAQ;AAAA,IACtB;AAEA,WAAO,UAAU,oBAAoB,SAAS,QAAQ,QAAQ;AAC5D,aAAO,KAAK,eAAe,kBAAkB,QAAQ,MAAM;AAAA,IAC7D;AAEA,WAAO,UAAU,kBAAkB,SAAS,QAAQ,QAAQ;AAC1D,aAAO,KAAK,eAAe,gBAAgB,QAAQ,MAAM;AAAA,IAC3D;AAEA,WAAO,UAAU,WAAW,WAAW;AACrC,aAAO,CAAC,KAAK,iBAAiB,KAAK,aAAa,SAAS;AAAA,IAC3D;AAEA,WAAO,UAAU,eAAe,SAAS,eAAe;AAGtD,UAAI,KAAK,SAAS,KACf,CAAC,iBAAiB,KAAK,mBAAmB,GAAI;AAC/C,eAAO;AAAA,MACT;AAIA,UAAI,CAAC,KAAK,KAAK;AACb,aAAK,iBAAiB;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,WAAW,SAAS,KAAK;AACxC,WAAK,KAAK,IAAI;AAId,UAAI,YAAY,KAAK,aAAa,IAAI;AACtC,UAAI,WAAW;AACb,YAAI,UAAU,UAAU,SAAS,CAAC,MAAM,MAAM;AAC5C,sBAAY,UAAU,QAAQ,SAAS,EAAE;AAAA,QAC3C;AACA,aAAK,aAAa,KAAK,SAAS;AAAA,MAClC;AAEA,UAAI,KAAK,mBAAmB;AAC1B,aAAK,iBAAiB;AAAA,MACxB;AAEA,UAAI,aAAa,KAAK,QAAQ,KAAK,IAAI;AAEvC,UAAI,QAAQ,MAAM;AAChB,qBAAa,WAAW,QAAQ,SAAS,GAAG;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,iBAAiB,WAAW;AAC3C,WAAK,aAAa,gBAAgB;AAAA,IACpC;AAEA,WAAO,UAAU,aAAa,SAAS,QAAQ,WAAW;AACxD,eAAS,UAAU;AACnB,kBAAY,aAAa;AAGzB,WAAK,UAAU,WAAW,QAAQ,SAAS;AAG3C,UAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,aAAK,aAAa,WAAW,QAAQ,SAAS;AAC9C,eAAO;AAAA,MACT;AAEA,WAAK,aAAa,WAAW;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,gBAAgB,SAAS,OAAO;AAC/C,eAAS,IAAI,GAAG,IAAI,MAAM,UAAU,KAAK;AACvC,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,aAAa,WAAW,EAAE;AAC/B,WAAK,aAAa,KAAK,MAAM,iBAAiB;AAC9C,WAAK,aAAa,KAAK,MAAM,IAAI;AACjC,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,yBAAyB;AAAA,IAChC;AAEA,WAAO,UAAU,YAAY,SAAS,iBAAiB;AACrD,WAAK,yBAAyB;AAC9B,WAAK,aAAa,KAAK,eAAe;AACtC,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,yBAAyB,KAAK,aAAa,YAAY;AAAA,IAC9D;AAEA,WAAO,UAAU,2BAA2B,WAAW;AACrD,UAAI,KAAK,sBAAsB,CAAC,KAAK,mBAAmB,GAAG;AACzD,YAAI,CAAC,KAAK,oBAAoB;AAC5B,eAAK,eAAe;AAAA,QACtB;AACA,aAAK,aAAa,KAAK,GAAG;AAAA,MAC5B;AAAA,IACF;AAEA,WAAO,UAAU,gBAAgB,SAAS,OAAO;AAC/C,UAAI,gBAAgB,KAAK,QAAQ;AACjC,aAAO,QAAQ,eAAe;AAC5B,aAAK,QAAQ,KAAK,EAAE,eAAe;AACnC;AAAA,MACF;AACA,WAAK,aAAa,oBAAoB;AAAA,IACxC;AAEA,WAAO,UAAU,OAAO,SAAS,cAAc;AAC7C,qBAAgB,iBAAiB,SAAa,QAAQ;AAEtD,WAAK,aAAa,KAAK;AAEvB,aAAO,gBAAgB,KAAK,QAAQ,SAAS,KAC3C,KAAK,aAAa,SAAS,GAAG;AAC9B,aAAK,QAAQ,IAAI;AACjB,aAAK,eAAe,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AACxD,aAAK,aAAa,KAAK;AAAA,MACzB;AAEA,WAAK,gBAAgB,KAAK,QAAQ,SAAS,IACzC,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI;AAAA,IAC5C;AAEA,WAAO,UAAU,qBAAqB,WAAW;AAC/C,aAAO,KAAK,aAAa,SAAS;AAAA,IACpC;AAEA,WAAO,UAAU,uBAAuB,WAAW;AACjD,aAAO,KAAK,SAAS,KAClB,KAAK,aAAa,SAAS,KAAK,KAAK,cAAc,SAAS;AAAA,IACjE;AAEA,WAAO,UAAU,0BAA0B,SAAS,aAAa,WAAW;AAC1E,UAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,aAAO,SAAS,GAAG;AACjB,YAAI,qBAAqB,KAAK,QAAQ,KAAK;AAC3C,YAAI,mBAAmB,SAAS,GAAG;AACjC;AAAA,QACF,WAAW,mBAAmB,KAAK,CAAC,EAAE,QAAQ,WAAW,MAAM,KAC7D,mBAAmB,KAAK,EAAE,MAAM,WAAW;AAC3C,eAAK,QAAQ,OAAO,QAAQ,GAAG,GAAG,IAAI,WAAW,IAAI,CAAC;AACtD,eAAK,gBAAgB,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AACzD;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF;AAEA,WAAO,QAAQ,SAAS;AAAA;AAAA;;;AClaxB;AAAA;AAAA;AA8BA,aAAS,MAAM,MAAM,MAAM,UAAU,mBAAmB;AACtD,WAAK,OAAO;AACZ,WAAK,OAAO;AAMZ,WAAK,kBAAkB;AAIvB,WAAK,WAAW,YAAY;AAC5B,WAAK,oBAAoB,qBAAqB;AAC9C,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,aAAa;AAAA,IACpB;AAGA,WAAO,QAAQ,QAAQ;AAAA;AAAA;;;ACrDvB;AAAA;AAAA;AAuBA,QAAI,gCAAgC;AAGpC,QAAI,2BAA2B;AAM/B,QAAI,+BAA+B;AACnC,QAAI,0BAA0B;AAI9B,QAAI,2BAA2B;AAC/B,QAAI,kBAAkB,QAAQ,2BAA2B,OAAO,gCAAgC,+BAA+B;AAC/H,QAAI,kBAAkB,QAAQ,2BAA2B,OAAO,2BAA2B,+BAA+B,0BAA0B;AAEpJ,YAAQ,aAAa,IAAI,OAAO,kBAAkB,iBAAiB,GAAG;AACtE,YAAQ,kBAAkB,IAAI,OAAO,eAAe;AACpD,YAAQ,kBAAkB,IAAI,OAAO,QAAQ,2BAA2B,OAAO,2BAA2B,+BAA+B,0BAA0B,KAAK;AAMxK,YAAQ,UAAU;AAOlB,YAAQ,YAAY,IAAI,OAAO,UAAU,QAAQ,QAAQ,MAAM;AAC/D,YAAQ,gBAAgB,IAAI,OAAO,QAAQ,UAAU,QAAQ,GAAG;AAAA;AAAA;;;ACzDhE;AAAA;AAAA;AA8BA,aAAS,QAAQ,SAAS,mBAAmB;AAC3C,WAAK,cAAc,WAAW,SAAS,iBAAiB;AAGxD,WAAK,WAAW,KAAK,aAAa,UAAU;AAE5C,WAAK,MAAM,KAAK,gBAAgB,OAAO,MAAM;AAC7C,WAAK,mBAAmB,KAAK,aAAa,kBAAkB;AAC5D,WAAK,cAAc,KAAK,YAAY,eAAe,CAAC;AACpD,WAAK,cAAc,KAAK,gBAAgB,eAAe,GAAG;AAC1D,WAAK,eAAe,KAAK,YAAY,cAAc;AAEnD,WAAK,oBAAoB,KAAK,aAAa,qBAAqB,IAAI;AACpE,WAAK,wBAAwB,KAAK,YAAY,yBAAyB,KAAK;AAC5E,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,wBAAwB;AAAA,MAC/B;AAEA,WAAK,mBAAmB,KAAK,aAAa,oBAAoB,KAAK,gBAAgB,GAAI;AACvF,UAAI,KAAK,kBAAkB;AACzB,aAAK,cAAc;AASnB,YAAI,KAAK,gBAAgB,GAAG;AAC1B,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAGA,WAAK,mBAAmB,KAAK,YAAY,oBAAoB,KAAK,YAAY,UAAU,CAAC;AAEzF,WAAK,qBAAqB,KAAK,aAAa,oBAAoB;AAKhE,WAAK,aAAa,KAAK,oBAAoB,cAAc,CAAC,QAAQ,QAAQ,WAAW,UAAU,OAAO,cAAc,OAAO,QAAQ,GAAG,CAAC,MAAM,CAAC;AAAA,IAChJ;AAEA,YAAQ,UAAU,aAAa,SAAS,MAAM,eAAe;AAC3D,UAAI,eAAe,KAAK,YAAY,IAAI;AACxC,UAAI,SAAS,iBAAiB,CAAC;AAC/B,UAAI,OAAO,iBAAiB,UAAU;AACpC,YAAI,iBAAiB,QAAQ,OAAO,aAAa,WAAW,YAAY;AACtE,mBAAS,aAAa,OAAO;AAAA,QAC/B;AAAA,MACF,WAAW,OAAO,iBAAiB,UAAU;AAC3C,iBAAS,aAAa,MAAM,oBAAoB;AAAA,MAClD;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,eAAe,SAAS,MAAM,eAAe;AAC7D,UAAI,eAAe,KAAK,YAAY,IAAI;AACxC,UAAI,SAAS,iBAAiB,SAAY,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAC9D,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,kBAAkB,SAAS,MAAM,eAAe;AAChE,UAAI,eAAe,KAAK,YAAY,IAAI;AACxC,UAAI,SAAS,iBAAiB;AAC9B,UAAI,OAAO,iBAAiB,UAAU;AACpC,iBAAS,aAAa,QAAQ,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI,EAAE,QAAQ,OAAO,GAAI;AAAA,MACrF;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,cAAc,SAAS,MAAM,eAAe;AAC5D,UAAI,eAAe,KAAK,YAAY,IAAI;AACxC,sBAAgB,SAAS,eAAe,EAAE;AAC1C,UAAI,MAAM,aAAa,GAAG;AACxB,wBAAgB;AAAA,MAClB;AACA,UAAI,SAAS,SAAS,cAAc,EAAE;AACtC,UAAI,MAAM,MAAM,GAAG;AACjB,iBAAS;AAAA,MACX;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,iBAAiB,SAAS,MAAM,gBAAgB,eAAe;AAC/E,UAAI,SAAS,KAAK,oBAAoB,MAAM,gBAAgB,aAAa;AACzE,UAAI,OAAO,WAAW,GAAG;AACvB,cAAM,IAAI;AAAA,UACR,uCAAuC,OAAO,iDAC9C,iBAAiB,uBAAuB,KAAK,YAAY,IAAI,IAAI;AAAA,QAAG;AAAA,MACxE;AAEA,aAAO,OAAO,CAAC;AAAA,IACjB;AAGA,YAAQ,UAAU,sBAAsB,SAAS,MAAM,gBAAgB,eAAe;AACpF,UAAI,CAAC,kBAAkB,eAAe,WAAW,GAAG;AAClD,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAEA,sBAAgB,iBAAiB,CAAC,eAAe,CAAC,CAAC;AACnD,UAAI,CAAC,KAAK,oBAAoB,eAAe,cAAc,GAAG;AAC5D,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC1C;AAEA,UAAI,SAAS,KAAK,WAAW,MAAM,aAAa;AAChD,UAAI,CAAC,KAAK,oBAAoB,QAAQ,cAAc,GAAG;AACrD,cAAM,IAAI;AAAA,UACR,uCAAuC,OAAO,+CAC9C,iBAAiB,uBAAuB,KAAK,YAAY,IAAI,IAAI;AAAA,QAAG;AAAA,MACxE;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,sBAAsB,SAAS,QAAQ,gBAAgB;AACvE,aAAO,OAAO,UAAU,eAAe,UACrC,CAAC,OAAO,KAAK,SAAS,MAAM;AAAE,eAAO,eAAe,QAAQ,IAAI,MAAM;AAAA,MAAI,CAAC;AAAA,IAC/E;AAQA,aAAS,WAAW,YAAY,gBAAgB;AAC9C,UAAI,YAAY,CAAC;AACjB,mBAAa,eAAe,UAAU;AACtC,UAAI;AAEJ,WAAK,QAAQ,YAAY;AACvB,YAAI,SAAS,gBAAgB;AAC3B,oBAAU,IAAI,IAAI,WAAW,IAAI;AAAA,QACnC;AAAA,MACF;AAGA,UAAI,kBAAkB,WAAW,cAAc,GAAG;AAChD,aAAK,QAAQ,WAAW,cAAc,GAAG;AACvC,oBAAU,IAAI,IAAI,WAAW,cAAc,EAAE,IAAI;AAAA,QACnD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,SAAS;AAC/B,UAAI,gBAAgB,CAAC;AACrB,UAAI;AAEJ,WAAK,OAAO,SAAS;AACnB,YAAI,SAAS,IAAI,QAAQ,MAAM,GAAG;AAClC,sBAAc,MAAM,IAAI,QAAQ,GAAG;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,UAAU;AACzB,WAAO,QAAQ,gBAAgB;AAC/B,WAAO,QAAQ,YAAY;AAAA;AAAA;;;AChM3B,IAAAA,mBAAA;AAAA;AAAA;AA8BA,QAAI,cAAc,kBAA2B;AAE7C,QAAI,sBAAsB,CAAC,kBAAkB,iBAAiB,kBAAkB;AAEhF,aAAS,QAAQ,SAAS;AACxB,kBAAY,KAAK,MAAM,SAAS,IAAI;AAGpC,UAAI,kBAAkB,KAAK,YAAY,eAAe;AACtD,UAAI,oBAAoB,iBAAiB;AACvC,aAAK,YAAY,cAAc;AAAA,MACjC,WAAW,oBAAoB,4BAA4B;AACzD,aAAK,YAAY,cAAc;AAAA,MACjC,WAAW,KAAK,YAAY,uBAAuB,QAAW;AAC5D,aAAK,YAAY,cAAc,KAAK,YAAY,qBAAqB,WAAW;AAAA,MAGlF;AAKA,UAAI,oBAAoB,KAAK,oBAAoB,eAAe,CAAC,YAAY,UAAU,cAAc,QAAQ,iBAAiB,CAAC;AAE/H,WAAK,wBAAwB;AAC7B,WAAK,cAAc;AAEnB,eAAS,KAAK,GAAG,KAAK,kBAAkB,QAAQ,MAAM;AACpD,YAAI,kBAAkB,EAAE,MAAM,mBAAmB;AAC/C,eAAK,wBAAwB;AAAA,QAC/B,OAAO;AACL,eAAK,cAAc,kBAAkB,EAAE;AAAA,QACzC;AAAA,MACF;AAEA,WAAK,2BAA2B,KAAK,aAAa,0BAA0B;AAC5E,WAAK,wBAAwB,KAAK,aAAa,uBAAuB;AACtE,WAAK,iBAAiB,KAAK,aAAa,gBAAgB;AACxD,WAAK,uBAAuB,KAAK,aAAa,sBAAsB;AACpE,WAAK,eAAe,KAAK,aAAa,cAAc;AACpD,WAAK,4BAA4B,KAAK,aAAa,2BAA2B;AAC9E,WAAK,6BAA6B,KAAK,aAAa,4BAA4B;AAChF,WAAK,yBAAyB,KAAK,aAAa,wBAAwB;AACxE,WAAK,2BAA2B,KAAK,aAAa,4BAA4B,IAAI;AAClF,WAAK,mBAAmB,KAAK,aAAa,kBAAkB;AAC5D,WAAK,MAAM,KAAK,aAAa,KAAK;AAClC,WAAK,cAAc,KAAK,aAAa,aAAa;AAClD,WAAK,oBAAoB,KAAK,eAAe,qBAAqB,mBAAmB;AAGrF,WAAK,kBAAkB,KAAK,aAAa,iBAAiB;AAG1D,UAAI,KAAK,cAAc;AACrB,aAAK,4BAA4B;AAAA,MACnC;AAAA,IAEF;AACA,YAAQ,YAAY,IAAI,YAAY;AAIpC,WAAO,QAAQ,UAAU;AAAA;AAAA;;;AC5FzB;AAAA;AAAA;AA8BA,QAAI,oBAAoB,OAAO,UAAU,eAAe,QAAQ;AAEhE,aAAS,aAAa,cAAc;AAClC,WAAK,UAAU,gBAAgB;AAC/B,WAAK,iBAAiB,KAAK,QAAQ;AACnC,WAAK,aAAa;AAAA,IACpB;AAEA,iBAAa,UAAU,UAAU,WAAW;AAC1C,WAAK,aAAa;AAAA,IACpB;AAEA,iBAAa,UAAU,OAAO,WAAW;AACvC,UAAI,KAAK,aAAa,GAAG;AACvB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAEA,iBAAa,UAAU,UAAU,WAAW;AAC1C,aAAO,KAAK,aAAa,KAAK;AAAA,IAChC;AAEA,iBAAa,UAAU,OAAO,WAAW;AACvC,UAAI,MAAM;AACV,UAAI,KAAK,QAAQ,GAAG;AAClB,cAAM,KAAK,QAAQ,OAAO,KAAK,UAAU;AACzC,aAAK,cAAc;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,OAAO,SAAS,OAAO;AAC5C,UAAI,MAAM;AACV,cAAQ,SAAS;AACjB,eAAS,KAAK;AACd,UAAI,SAAS,KAAK,QAAQ,KAAK,gBAAgB;AAC7C,cAAM,KAAK,QAAQ,OAAO,KAAK;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AASA,iBAAa,UAAU,UAAU,SAAS,SAAS,OAAO;AACxD,cAAQ,YAAY;AACpB,UAAI,gBAAgB,QAAQ,KAAK,KAAK,OAAO;AAE7C,UAAI,iBAAiB,EAAE,qBAAqB,QAAQ,SAAS;AAC3D,YAAI,cAAc,UAAU,OAAO;AACjC,0BAAgB;AAAA,QAClB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,OAAO,SAAS,SAAS,OAAO;AACrD,cAAQ,SAAS;AACjB,eAAS,KAAK;AAEd,UAAI,SAAS,KAAK,QAAQ,KAAK,gBAAgB;AAC7C,eAAO,CAAC,CAAC,KAAK,QAAQ,SAAS,KAAK;AAAA,MACtC,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,iBAAa,UAAU,WAAW,SAAS,SAAS,OAAO;AAEzD,UAAI,MAAM,KAAK,KAAK,KAAK;AACzB,cAAQ,YAAY;AACpB,aAAO,QAAQ,QAAQ,QAAQ,KAAK,GAAG;AAAA,IACzC;AAEA,iBAAa,UAAU,QAAQ,SAAS,SAAS;AAC/C,UAAI,gBAAgB,KAAK,QAAQ,SAAS,KAAK,UAAU;AACzD,UAAI,eAAe;AACjB,aAAK,cAAc,cAAc,CAAC,EAAE;AAAA,MACtC,OAAO;AACL,wBAAgB;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,OAAO,SAAS,kBAAkB,eAAe,aAAa;AACnF,UAAI,MAAM;AACV,UAAI;AACJ,UAAI,kBAAkB;AACpB,gBAAQ,KAAK,MAAM,gBAAgB;AACnC,YAAI,OAAO;AACT,iBAAO,MAAM,CAAC;AAAA,QAChB;AAAA,MACF;AACA,UAAI,kBAAkB,SAAS,CAAC,mBAAmB;AACjD,eAAO,KAAK,UAAU,eAAe,WAAW;AAAA,MAClD;AACA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,YAAY,SAAS,SAAS,aAAa;AAChE,UAAI,MAAM;AACV,UAAI,cAAc,KAAK;AACvB,cAAQ,YAAY,KAAK;AACzB,UAAI,gBAAgB,QAAQ,KAAK,KAAK,OAAO;AAC7C,UAAI,eAAe;AACjB,sBAAc,cAAc;AAC5B,YAAI,aAAa;AACf,yBAAe,cAAc,CAAC,EAAE;AAAA,QAClC;AAAA,MACF,OAAO;AACL,sBAAc,KAAK;AAAA,MACrB;AAEA,YAAM,KAAK,QAAQ,UAAU,KAAK,YAAY,WAAW;AACzD,WAAK,aAAa;AAClB,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,iBAAiB,SAAS,SAAS;AACxD,aAAO,KAAK,UAAU,SAAS,IAAI;AAAA,IACrC;AAEA,iBAAa,UAAU,aAAa,SAAS,SAAS,YAAY;AAChE,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,cAAc,mBAAmB;AACnC,gBAAQ;AAAA,MACV;AAEA,UAAI,OAAO,YAAY,YAAY,YAAY,IAAI;AAEjD,iBAAS,IAAI,OAAO,SAAS,KAAK;AAAA,MACpC,WAAW,SAAS;AAClB,iBAAS,IAAI,OAAO,QAAQ,QAAQ,KAAK;AAAA,MAC3C;AACA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,qBAAqB,SAAS,gBAAgB;AACnE,aAAO,OAAO,eAAe,QAAQ,0BAA0B,MAAM,CAAC;AAAA,IACxE;AAGA,iBAAa,UAAU,iBAAiB,SAAS,SAAS;AACxD,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM,KAAK,eAAe,OAAO;AACrC,WAAK,aAAa;AAClB,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,WAAW,SAAS,SAAS;AAClD,UAAI,QAAQ,KAAK,aAAa;AAC9B,aAAO,SAAS,QAAQ,UAAU,KAAK,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,KAAK,EACnF,YAAY,MAAM;AAAA,IACvB;AAEA,WAAO,QAAQ,eAAe;AAAA;AAAA;;;AC/L9B;AAAA;AAAA;AA8BA,aAAS,YAAY,cAAc;AAEjC,WAAK,WAAW,CAAC;AACjB,WAAK,kBAAkB,KAAK,SAAS;AACrC,WAAK,aAAa;AAClB,WAAK,iBAAiB;AAAA,IACxB;AAEA,gBAAY,UAAU,UAAU,WAAW;AACzC,WAAK,aAAa;AAAA,IACpB;AAEA,gBAAY,UAAU,UAAU,WAAW;AACzC,aAAO,KAAK,oBAAoB;AAAA,IAClC;AAEA,gBAAY,UAAU,UAAU,WAAW;AACzC,aAAO,KAAK,aAAa,KAAK;AAAA,IAChC;AAEA,gBAAY,UAAU,OAAO,WAAW;AACtC,UAAI,MAAM;AACV,UAAI,KAAK,QAAQ,GAAG;AAClB,cAAM,KAAK,SAAS,KAAK,UAAU;AACnC,aAAK,cAAc;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,gBAAY,UAAU,OAAO,SAAS,OAAO;AAC3C,UAAI,MAAM;AACV,cAAQ,SAAS;AACjB,eAAS,KAAK;AACd,UAAI,SAAS,KAAK,QAAQ,KAAK,iBAAiB;AAC9C,cAAM,KAAK,SAAS,KAAK;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AAEA,gBAAY,UAAU,MAAM,SAAS,OAAO;AAC1C,UAAI,KAAK,gBAAgB;AACvB,cAAM,SAAS,KAAK;AAAA,MACtB;AACA,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,WAAO,QAAQ,cAAc;AAAA;AAAA;;;AC7E7B;AAAA;AAAA;AA8BA,aAAS,QAAQ,eAAe,QAAQ;AACtC,WAAK,SAAS;AACd,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;AACtB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AAEpB,UAAI,QAAQ;AACV,aAAK,oBAAoB,KAAK,OAAO,WAAW,OAAO,mBAAmB,IAAI;AAC9E,aAAK,iBAAiB,KAAK,OAAO,WAAW,OAAO,gBAAgB,IAAI;AACxE,aAAK,iBAAiB,KAAK,OAAO,WAAW,OAAO,cAAc;AAClE,aAAK,eAAe,OAAO;AAAA,MAC7B;AAAA,IACF;AAEA,YAAQ,UAAU,OAAO,WAAW;AAClC,UAAI,SAAS,KAAK,OAAO,KAAK,KAAK,iBAAiB;AACpD,UAAI,CAAC,KAAK,qBAAqB,QAAQ;AACrC,kBAAU,KAAK,OAAO,KAAK,KAAK,gBAAgB,KAAK,gBAAgB,KAAK,YAAY;AAAA,MACxF;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,aAAa,WAAW;AACxC,aAAO,KAAK,OAAO,MAAM,KAAK,cAAc;AAAA,IAC9C;AAEA,YAAQ,UAAU,cAAc,SAAS,SAAS;AAChD,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,eAAe;AACtB,aAAO,iBAAiB,KAAK,OAAO,WAAW,OAAO;AACtD,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,QAAQ,SAAS,SAAS;AAC1C,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,eAAe;AACtB,aAAO,iBAAiB,KAAK,OAAO,WAAW,OAAO;AACtD,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,gBAAgB,SAAS,SAAS;AAClD,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,oBAAoB,KAAK,OAAO,WAAW,SAAS,IAAI;AAC/D,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,WAAW,SAAS,SAAS;AAC7C,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,iBAAiB,KAAK,OAAO,WAAW,SAAS,IAAI;AAC5D,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,UAAU,WAAW;AACrC,aAAO,IAAI,QAAQ,KAAK,QAAQ,IAAI;AAAA,IACtC;AAEA,YAAQ,UAAU,UAAU,WAAW;AAAA,IAAC;AAExC,WAAO,QAAQ,UAAU;AAAA;AAAA;;;AC7FzB;AAAA;AAAA;AA8BA,QAAI,UAAU,kBAA2B;AAEzC,aAAS,kBAAkB,eAAe,QAAQ;AAChD,cAAQ,KAAK,MAAM,eAAe,MAAM;AACxC,UAAI,QAAQ;AACV,aAAK,eAAe,KAAK,OAAO,WAAW,OAAO,YAAY;AAAA,MAChE,OAAO;AACL,aAAK,0BAA0B,IAAI,EAAE;AAAA,MACvC;AAEA,WAAK,gBAAgB;AACrB,WAAK,0BAA0B;AAAA,IACjC;AACA,sBAAkB,YAAY,IAAI,QAAQ;AAE1C,sBAAkB,UAAU,4BAA4B,SAAS,kBAAkB,eAAe;AAChG,0BAAoB;AACpB,uBAAiB;AAEjB,WAAK,iBAAiB,KAAK,OAAO;AAAA,QAChC,MAAM,mBAAmB,gBAAgB;AAAA,QAAM;AAAA,MAAI;AACrD,WAAK,kBAAkB,KAAK,OAAO;AAAA,QACjC,aAAa,gBAAgB;AAAA,MAAG;AAAA,IACpC;AAEA,sBAAkB,UAAU,OAAO,WAAW;AAC5C,WAAK,gBAAgB;AACrB,WAAK,0BAA0B;AAE/B,UAAI,mBAAmB,KAAK,OAAO,KAAK,KAAK,cAAc;AAC3D,UAAI,qBAAqB,KAAK;AAC5B,aAAK,0BAA0B;AAAA,MACjC,WAAW,kBAAkB;AAC3B,YAAI,UAAU,KAAK,QAAQ,KAAK,iBAAiB,gBAAgB;AACjE,aAAK,gBAAgB,QAAQ,SAAS;AACtC,aAAK,0BAA0B,QAAQ,KAAK,aAAa;AAAA,MAC3D;AAEA,aAAO;AAAA,IACT;AAEA,sBAAkB,UAAU,WAAW,SAAS,kBAAkB,eAAe;AAC/E,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,0BAA0B,kBAAkB,aAAa;AAChE,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,sBAAkB,UAAU,UAAU,WAAW;AAC/C,aAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AAAA,IAChD;AAEA,sBAAkB,UAAU,UAAU,SAAS,QAAQ,cAAc;AACnE,aAAO,YAAY;AACnB,UAAI,cAAc;AAClB,UAAI,SAAS,CAAC;AACd,UAAI,aAAa,OAAO,KAAK,YAAY;AACzC,aAAO,YAAY;AACjB,eAAO,KAAK,aAAa,UAAU,aAAa,WAAW,KAAK,CAAC;AACjE,sBAAc,WAAW,QAAQ,WAAW,CAAC,EAAE;AAC/C,qBAAa,OAAO,KAAK,YAAY;AAAA,MACvC;AAEA,UAAI,cAAc,aAAa,QAAQ;AACrC,eAAO,KAAK,aAAa,UAAU,aAAa,aAAa,MAAM,CAAC;AAAA,MACtE,OAAO;AACL,eAAO,KAAK,EAAE;AAAA,MAChB;AAEA,aAAO;AAAA,IACT;AAIA,WAAO,QAAQ,oBAAoB;AAAA;AAAA;;;ACxGnC;AAAA;AAAA;AA8BA,QAAI,eAAe,uBAAgC;AACnD,QAAI,QAAQ,gBAAyB;AACrC,QAAI,cAAc,sBAA+B;AACjD,QAAI,oBAAoB,4BAA+B;AAEvD,QAAI,QAAQ;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAEA,QAAI,YAAY,SAAS,cAAc,SAAS;AAC9C,WAAK,SAAS,IAAI,aAAa,YAAY;AAC3C,WAAK,WAAW,WAAW,CAAC;AAC5B,WAAK,WAAW;AAEhB,WAAK,YAAY,CAAC;AAClB,WAAK,UAAU,aAAa,IAAI,kBAAkB,KAAK,MAAM;AAAA,IAC/D;AAEA,cAAU,UAAU,WAAW,WAAW;AACxC,WAAK,OAAO,QAAQ;AACpB,WAAK,WAAW,IAAI,YAAY;AAEhC,WAAK,OAAO;AAEZ,UAAI;AACJ,UAAI,WAAW,IAAI,MAAM,MAAM,OAAO,EAAE;AACxC,UAAI,aAAa;AACjB,UAAI,aAAa,CAAC;AAClB,UAAI,WAAW,IAAI,YAAY;AAE/B,aAAO,SAAS,SAAS,MAAM,KAAK;AAClC,kBAAU,KAAK,gBAAgB,UAAU,UAAU;AACnD,eAAO,KAAK,YAAY,OAAO,GAAG;AAChC,mBAAS,IAAI,OAAO;AACpB,oBAAU,KAAK,gBAAgB,UAAU,UAAU;AAAA,QACrD;AAEA,YAAI,CAAC,SAAS,QAAQ,GAAG;AACvB,kBAAQ,kBAAkB;AAC1B,qBAAW,IAAI,YAAY;AAAA,QAC7B;AAEA,gBAAQ,SAAS;AAEjB,YAAI,KAAK,YAAY,OAAO,GAAG;AAC7B,qBAAW,KAAK,UAAU;AAC1B,uBAAa;AAAA,QACf,WAAW,cAAc,KAAK,YAAY,SAAS,UAAU,GAAG;AAC9D,kBAAQ,SAAS;AACjB,qBAAW,SAAS;AACpB,uBAAa,WAAW,IAAI;AAC5B,kBAAQ,SAAS;AAAA,QACnB;AAEA,gBAAQ,WAAW;AACnB,iBAAS,OAAO;AAEhB,aAAK,SAAS,IAAI,OAAO;AACzB,mBAAW;AAAA,MACb;AAEA,aAAO,KAAK;AAAA,IACd;AAGA,cAAU,UAAU,kBAAkB,WAAW;AAC/C,aAAO,KAAK,SAAS,QAAQ;AAAA,IAC/B;AAEA,cAAU,UAAU,SAAS,WAAW;AAAA,IAAC;AAEzC,cAAU,UAAU,kBAAkB,SAAS,gBAAgB,YAAY;AACzE,WAAK,gBAAgB;AACrB,UAAI,mBAAmB,KAAK,OAAO,KAAK,KAAK;AAC7C,UAAI,kBAAkB;AACpB,eAAO,KAAK,cAAc,MAAM,KAAK,gBAAgB;AAAA,MACvD,OAAO;AACL,eAAO,KAAK,cAAc,MAAM,KAAK,EAAE;AAAA,MACzC;AAAA,IACF;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe;AACxD,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe;AACxD,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe,YAAY;AACpE,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,gBAAgB,SAAS,MAAM,MAAM;AACvD,UAAI,QAAQ,IAAI;AAAA,QAAM;AAAA,QAAM;AAAA,QAC1B,KAAK,UAAU,WAAW;AAAA,QAC1B,KAAK,UAAU,WAAW;AAAA,MAAuB;AACnD,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,kBAAkB,WAAW;AAC/C,aAAO,KAAK,UAAU,WAAW,KAAK;AAAA,IACxC;AAIA,WAAO,QAAQ,YAAY;AAC3B,WAAO,QAAQ,QAAQ;AAAA;AAAA;;;AC3IvB;AAAA;AAAA;AA8BA,aAAS,WAAW,qBAAqB,mBAAmB;AAC1D,4BAAsB,OAAO,wBAAwB,WAAW,sBAAsB,oBAAoB;AAC1G,0BAAoB,OAAO,sBAAsB,WAAW,oBAAoB,kBAAkB;AAClG,WAAK,6BAA6B,IAAI,OAAO,sBAAsB,0BAA0B,SAAS,mBAAmB,GAAG;AAC5H,WAAK,sBAAsB;AAE3B,WAAK,kCAAkC,IAAI,OAAO,sBAAsB,2BAA2B,SAAS,mBAAmB,GAAG;AAAA,IACpI;AAEA,eAAW,UAAU,iBAAiB,SAAS,MAAM;AACnD,UAAI,CAAC,KAAK,MAAM,KAAK,0BAA0B,GAAG;AAChD,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,CAAC;AAClB,WAAK,oBAAoB,YAAY;AACrC,UAAI,kBAAkB,KAAK,oBAAoB,KAAK,IAAI;AAExD,aAAO,iBAAiB;AACtB,mBAAW,gBAAgB,CAAC,CAAC,IAAI,gBAAgB,CAAC;AAClD,0BAAkB,KAAK,oBAAoB,KAAK,IAAI;AAAA,MACtD;AAEA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,cAAc,SAAS,OAAO;AACjD,aAAO,MAAM,eAAe,KAAK,+BAA+B;AAAA,IAClE;AAGA,WAAO,QAAQ,aAAa;AAAA;AAAA;;;AC7D5B;AAAA;AAAA;AA8BA,QAAI,UAAU,kBAAqB;AAGnC,QAAI,iBAAiB;AAAA,MACnB,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAIA,aAAS,mBAAmB,eAAe,QAAQ;AACjD,cAAQ,KAAK,MAAM,eAAe,MAAM;AACxC,WAAK,qBAAqB;AAC1B,WAAK,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AACjD,WAAK,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAEjD,UAAI,QAAQ;AACV,aAAK,qBAAqB,KAAK,OAAO,WAAW,OAAO,kBAAkB;AAC1E,aAAK,YAAY,OAAO,OAAO,KAAK,WAAW,OAAO,SAAS;AAC/D,aAAK,YAAY,OAAO,OAAO,KAAK,WAAW,OAAO,SAAS;AAAA,MACjE;AACA,UAAI,UAAU,IAAI,QAAQ,aAAa;AACvC,WAAK,aAAa;AAAA,QAChB,oBAAoB,QAAQ,cAAc,OAAO,EAAE,YAAY,MAAM;AAAA,QACrE,sBAAsB,QAAQ,cAAc,KAAK,EAAE,YAAY,KAAK;AAAA,QACpE,YAAY,QAAQ,cAAc,IAAI,EAAE,YAAY,IAAI;AAAA,QACxD,KAAK,QAAQ,cAAc,iBAAiB,EAAE,YAAY,KAAK;AAAA,QAC/D,KAAK,QAAQ,cAAc,QAAQ,EAAE,YAAY,QAAQ;AAAA;AAAA,QAEzD,QAAQ,QAAQ,cAAc,IAAI,EAAE,YAAY,IAAI;AAAA,QACpD,cAAc,QAAQ,cAAc,IAAI,EAAE,YAAY,IAAI;AAAA,QAC1D,gBAAgB,QAAQ,cAAc,IAAI,EAAE,YAAY,IAAI;AAAA,QAC5D,QAAQ,QAAQ,cAAc,gBAAgB,EAAE,YAAY,UAAU;AAAA,QACtE,gBAAgB,QAAQ,cAAc,KAAK,EAAE,YAAY,KAAK;AAAA,QAC9D,gBAAgB,QAAQ,cAAc,WAAW,EAAE,YAAY,aAAa;AAAA,MAC9E;AAAA,IACF;AACA,uBAAmB,YAAY,IAAI,QAAQ;AAE3C,uBAAmB,UAAU,UAAU,WAAW;AAChD,aAAO,IAAI,mBAAmB,KAAK,QAAQ,IAAI;AAAA,IACjD;AAEA,uBAAmB,UAAU,UAAU,WAAW;AAChD,WAAK,wBAAwB;AAAA,IAC/B;AAEA,uBAAmB,UAAU,UAAU,SAAS,UAAU;AACxD,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,UAAU,QAAQ,IAAI;AAC7B,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,uBAAmB,UAAU,eAAe,SAAS,SAAS;AAC5D,UAAI,SAAS,KAAK,QAAQ;AAC1B,eAAS,YAAY,gBAAgB;AACnC,eAAO,UAAU,QAAQ,IAAI,QAAQ,WAAW,QAAQ,QAAQ,MAAM;AAAA,MACxE;AACA,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,uBAAmB,UAAU,UAAU,SAAS,UAAU;AACxD,UAAI,SAAS,KAAK,QAAQ;AAC1B,aAAO,UAAU,QAAQ,IAAI;AAC7B,aAAO,QAAQ;AACf,aAAO;AAAA,IACT;AAEA,uBAAmB,UAAU,OAAO,WAAW;AAC7C,UAAI,SAAS;AACb,UAAI,KAAK,gBAAgB;AACvB,iBAAS,KAAK,OAAO,KAAK,KAAK,iBAAiB;AAAA,MAClD,OAAO;AACL,iBAAS,KAAK,OAAO,KAAK,KAAK,mBAAmB,KAAK,kBAAkB;AAAA,MAC3E;AACA,UAAI,OAAO,KAAK,eAAe;AAC/B,aAAO,MAAM;AACX,YAAI,KAAK,gBAAgB;AACvB,kBAAQ,KAAK,OAAO,KAAK,KAAK,cAAc;AAAA,QAC9C,OAAO;AACL,kBAAQ,KAAK,OAAO,UAAU,KAAK,kBAAkB;AAAA,QACvD;AACA,kBAAU;AACV,eAAO,KAAK,eAAe;AAAA,MAC7B;AAEA,UAAI,KAAK,cAAc;AACrB,kBAAU,KAAK,OAAO,eAAe,KAAK,cAAc;AAAA,MAC1D;AACA,aAAO;AAAA,IACT;AAEA,uBAAmB,UAAU,0BAA0B,WAAW;AAChE,UAAI,QAAQ,CAAC;AAEb,UAAI,CAAC,KAAK,UAAU,KAAK;AACvB,cAAM,KAAK,KAAK,WAAW,IAAI,kBAAkB,MAAM;AAAA,MACzD;AACA,UAAI,CAAC,KAAK,UAAU,YAAY;AAC9B,cAAM,KAAK,KAAK,WAAW,WAAW,kBAAkB,MAAM;AAAA,MAChE;AACA,UAAI,CAAC,KAAK,UAAU,SAAS;AAE3B,cAAM,KAAK,KAAK,WAAW,WAAW,kBAAkB,MAAM;AAAA,MAChE;AACA,UAAI,CAAC,KAAK,UAAU,KAAK;AACvB,cAAM,KAAK,KAAK,WAAW,IAAI,kBAAkB,MAAM;AAAA,MACzD;AACA,UAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,cAAM,KAAK,KAAK,WAAW,OAAO,kBAAkB,MAAM;AAG1D,cAAM,KAAK,KAAK,WAAW,aAAa,kBAAkB,MAAM;AAChE,cAAM,KAAK,KAAK,WAAW,eAAe,kBAAkB,MAAM;AAAA,MACpE;AACA,UAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,cAAM,KAAK,KAAK,WAAW,OAAO,kBAAkB,MAAM;AAAA,MAC5D;AAEA,UAAI,KAAK,gBAAgB;AACvB,cAAM,KAAK,KAAK,eAAe,MAAM;AAAA,MACvC;AACA,WAAK,qBAAqB,KAAK,OAAO,WAAW,QAAQ,MAAM,KAAK,GAAG,IAAI,GAAG;AAAA,IAChF;AAEA,uBAAmB,UAAU,iBAAiB,WAAW;AACvD,UAAI,mBAAmB;AACvB,UAAI,IAAI,KAAK,OAAO,KAAK;AACzB,UAAI,MAAM,KAAK;AACb,YAAI,QAAQ,KAAK,OAAO,KAAK,CAAC;AAI9B,YAAI,CAAC,KAAK,UAAU,OAAO,CAAC,KAAK,UAAU,OAAO,UAAU,KAAK;AAC/D,6BAAmB,oBACjB,KAAK,WAAW,IAAI,KAAK;AAAA,QAC7B;AACA,YAAI,CAAC,KAAK,UAAU,OAAO,CAAC,KAAK,UAAU,OAAO,UAAU,KAAK;AAC/D,6BAAmB,oBACjB,KAAK,WAAW,IAAI,KAAK;AAAA,QAC7B;AAAA,MACF,WAAW,MAAM,KAAK;AACpB,YAAI,CAAC,KAAK,UAAU,cAAc,CAAC,KAAK,UAAU,YAAY;AAC5D,6BAAmB,oBACjB,KAAK,WAAW,mBAAmB,KAAK;AAC1C,6BAAmB,oBACjB,KAAK,WAAW,qBAAqB,KAAK;AAC5C,6BAAmB,oBACjB,KAAK,WAAW,WAAW,KAAK;AAAA,QACpC;AACA,YAAI,CAAC,KAAK,UAAU,QAAQ;AAE1B,cAAI,CAAC,KAAK,UAAU,UAAU,CAAC,KAAK,UAAU,YAAY;AACxD,+BAAmB,oBACjB,KAAK,WAAW,aAAa,KAAK;AAAA,UACtC;AACA,cAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,+BAAmB,oBACjB,KAAK,WAAW,eAAe,KAAK;AACtC,+BAAmB,oBACjB,KAAK,WAAW,OAAO,KAAK;AAAA,UAChC;AAAA,QACF;AACA,YAAI,CAAC,KAAK,UAAU,QAAQ;AAE1B,cAAI,KAAK,UAAU,UAAU,KAAK,UAAU,YAAY;AACtD,+BAAmB,oBACjB,KAAK,WAAW,eAAe,KAAK;AACtC,+BAAmB,oBACjB,KAAK,WAAW,eAAe,KAAK;AACtC,+BAAmB,oBACjB,KAAK,WAAW,OAAO,KAAK;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAGA,WAAO,QAAQ,qBAAqB;AAAA;AAAA;;;ACvNpC,IAAAC,qBAAA;AAAA;AAAA;AA8BA,QAAI,eAAe,uBAAgC;AACnD,QAAI,gBAAgB,oBAA6B;AACjD,QAAI,YAAY,oBAA6B;AAC7C,QAAI,aAAa,qBAA8B;AAC/C,QAAI,QAAQ;AACZ,QAAI,UAAU,kBAA2B;AACzC,QAAI,qBAAqB,6BAAsC;AAG/D,aAAS,SAAS,MAAM,KAAK;AAC3B,aAAO,IAAI,QAAQ,IAAI,MAAM;AAAA,IAC/B;AAGA,QAAI,QAAQ;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,eAAe;AAAA,MACf,SAAS;AAAA,MACT,KAAK;AAAA,MACL,SAAS;AAAA,MACT,OAAO,UAAU;AAAA,MACjB,KAAK,UAAU;AAAA,MACf,KAAK,UAAU;AAAA,IACjB;AAGA,QAAI,kBAAkB,IAAI,WAAW,QAAQ,MAAM;AAEnD,QAAI,iBAAiB;AAErB,QAAI,QAAQ;AAGZ,QAAI,cAAc;AAElB,QAAI,yBACF,mFAE2B,MAAM,GAAG;AAItC,QAAI,QACF;AAKF,YAAQ,MAAM,QAAQ,0BAA0B,MAAM;AAEtD,YAAQ,mBAAmB;AAC3B,YAAQ,MAAM,QAAQ,MAAM,GAAG;AAE/B,QAAI,gBAAgB,IAAI,OAAO,KAAK;AAGpC,QAAI,gBAAgB,wGAAwG,MAAM,GAAG;AACrI,QAAI,iBAAiB,cAAc,OAAO,CAAC,MAAM,MAAM,MAAM,QAAQ,OAAO,OAAO,OAAO,SAAS,WAAW,UAAU,SAAS,SAAS,SAAS,QAAQ,MAAM,SAAS,SAAS,CAAC;AACpL,QAAI,wBAAwB,IAAI,OAAO,SAAS,eAAe,KAAK,GAAG,IAAI,IAAI;AAI/E,QAAI;AAEJ,QAAI,YAAY,SAAS,cAAc,SAAS;AAC9C,oBAAc,KAAK,MAAM,cAAc,OAAO;AAE9C,WAAK,UAAU,aAAa,KAAK,UAAU,WAAW;AAAA,QACpD,0DAA0D;AAAA,QAC1D,eAAe;AAAA,MAAM;AAEvB,UAAI,iBAAiB,IAAI,QAAQ,KAAK,MAAM;AAC5C,UAAI,cAAc,IAAI,mBAAmB,KAAK,MAAM,EACjD,aAAa,KAAK,QAAQ;AAE7B,WAAK,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,YAAY,YAAY,cAAc,MAAM,UAAU,EAAE,SAAS,MAAM,eAAe;AAAA,QACtF,QAAQ,eAAe,SAAS,cAAc;AAAA,QAC9C,OAAO,eAAe,SAAS,aAAa;AAAA;AAAA,QAE5C,SAAS,eAAe,cAAc,MAAM,EAAE,MAAM,oBAAoB;AAAA;AAAA,QAExE,eAAe,eAAe,cAAc,MAAM,EAAE,YAAY,MAAM;AAAA,QACtE,oBAAoB,eAAe,SAAS,MAAM;AAAA,QAClD,kBAAkB,eAAe,SAAS,KAAK;AAAA,QAC/C,SAAS,eAAe,cAAc,UAAU,EAAE,YAAY,MAAM,SAAS;AAAA,QAC7E,SAAS,eAAe,cAAc,IAAI,EAAE,YAAY,MAAM,SAAS;AAAA,QACvE,KAAK,eAAe,SAAS,iLAAiL;AAAA,QAC9M,cAAc,YAAY,MAAM,uBAAuB;AAAA,QACvD,cAAc,YAAY,MAAM,uBAAuB;AAAA,QACvD,eAAe,YAAY,MAAM,QAAQ;AAAA,QACzC,qBAAqB,YAAY,MAAM,QAAQ;AAAA,MACjD;AAAA,IAEF;AACA,cAAU,YAAY,IAAI,cAAc;AAExC,cAAU,UAAU,cAAc,SAAS,eAAe;AACxD,aAAO,cAAc,SAAS,MAAM,WAAW,cAAc,SAAS,MAAM,iBAAiB,cAAc,SAAS,MAAM;AAAA,IAC5H;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe;AACxD,aAAO,cAAc,SAAS,MAAM,eAAe,cAAc,SAAS,MAAM;AAAA,IAClF;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe,YAAY;AACpE,cAAQ,cAAc,SAAS,MAAM,aAAa,cAAc,SAAS,MAAM,cAC5E,eACE,cAAc,SAAS,OAAO,WAAW,SAAS,OAClD,cAAc,SAAS,OAAO,WAAW,SAAS,OAClD,cAAc,SAAS,OAAO,WAAW,SAAS;AAAA,IACzD;AAEA,cAAU,UAAU,SAAS,WAAW;AACtC,wBAAkB;AAAA,IACpB;AAEA,cAAU,UAAU,kBAAkB,SAAS,gBAAgB,YAAY;AACzE,UAAI,QAAQ;AACZ,WAAK,gBAAgB;AACrB,UAAI,IAAI,KAAK,OAAO,KAAK;AAEzB,UAAI,MAAM,MAAM;AACd,eAAO,KAAK,cAAc,MAAM,KAAK,EAAE;AAAA,MACzC;AAEA,cAAQ,SAAS,KAAK,qBAAqB,CAAC;AAC5C,cAAQ,SAAS,KAAK,aAAa,CAAC;AACpC,cAAQ,SAAS,KAAK,WAAW,GAAG,KAAK,OAAO,KAAK,CAAC,CAAC;AACvD,cAAQ,SAAS,KAAK,WAAW,cAAc;AAC/C,cAAQ,SAAS,KAAK,cAAc,CAAC;AACrC,cAAQ,SAAS,KAAK,cAAc,CAAC;AACrC,cAAQ,SAAS,KAAK,aAAa,GAAG,cAAc;AACpD,cAAQ,SAAS,KAAK,UAAU,GAAG,cAAc;AACjD,cAAQ,SAAS,KAAK,kBAAkB;AACxC,cAAQ,SAAS,KAAK,cAAc,MAAM,SAAS,KAAK,OAAO,KAAK,CAAC;AAErE,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,aAAa,SAAS,gBAAgB;AACxD,UAAI;AACJ,yBAAmB,KAAK,WAAW,WAAW,KAAK;AACnD,UAAI,qBAAqB,IAAI;AAC3B,2BAAmB,iBAAiB,QAAQ,MAAM,eAAe,IAAI;AACrE,YAAI,EAAE,eAAe,SAAS,MAAM,OAC/B,eAAe,SAAS,MAAM,aAAa,eAAe,SAAS,SAAS,eAAe,SAAS,WACvG,sBAAsB,KAAK,gBAAgB,GAAG;AAC9C,eAAK,qBAAqB,QAAQ,qBAAqB,UACpD,eAAe,SAAS,MAAM,QAAQ,eAAe,SAAS,MAAM,SAAS;AAC9E,mBAAO,KAAK,cAAc,MAAM,UAAU,gBAAgB;AAAA,UAC5D;AACA,iBAAO,KAAK,cAAc,MAAM,UAAU,gBAAgB;AAAA,QAC5D;AACA,eAAO,KAAK,cAAc,MAAM,MAAM,gBAAgB;AAAA,MACxD;AAEA,yBAAmB,KAAK,WAAW,OAAO,KAAK;AAC/C,UAAI,qBAAqB,IAAI;AAC3B,eAAO,KAAK,cAAc,MAAM,MAAM,gBAAgB;AAAA,MACxD;AAAA,IACF;AAEA,cAAU,UAAU,gBAAgB,SAAS,GAAG;AAC9C,UAAI,QAAQ;AACZ,UAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,gBAAQ,KAAK,cAAc,MAAM,YAAY,CAAC;AAAA,MAChD,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,gBAAQ,KAAK,cAAc,MAAM,UAAU,CAAC;AAAA,MAC9C,WAAW,MAAM,KAAK;AACpB,gBAAQ,KAAK,cAAc,MAAM,aAAa,CAAC;AAAA,MACjD,WAAW,MAAM,KAAK;AACpB,gBAAQ,KAAK,cAAc,MAAM,WAAW,CAAC;AAAA,MAC/C,WAAW,MAAM,KAAK;AACpB,gBAAQ,KAAK,cAAc,MAAM,WAAW,CAAC;AAAA,MAC/C,WAAW,MAAM,OAAO,YAAY,KAAK,KAAK,OAAO,KAAK,CAAC,CAAC,GAAG;AAC7D,gBAAQ,KAAK,cAAc,MAAM,KAAK,CAAC;AAAA,MACzC,WAAW,MAAM,KAAK;AACpB,gBAAQ,KAAK,cAAc,MAAM,OAAO,CAAC;AAAA,MAC3C;AAEA,UAAI,OAAO;AACT,aAAK,OAAO,KAAK;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,aAAa,SAAS,GAAG,GAAG;AAC9C,UAAI,QAAQ;AACZ,UAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,gBAAQ,KAAK,cAAc,MAAM,aAAa,IAAI,CAAC;AAAA,MACrD;AAEA,UAAI,OAAO;AACT,aAAK,OAAO,KAAK;AACjB,aAAK,OAAO,KAAK;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,oBAAoB,WAAW;AACjD,UAAI,mBAAmB,KAAK,WAAW,MAAM,KAAK;AAElD,UAAI,qBAAqB,IAAI;AAC3B,YAAI,qBAAqB,KAAK;AAC5B,iBAAO,KAAK,cAAc,MAAM,QAAQ,gBAAgB;AAAA,QAC1D,WAAW,qBAAqB,MAAM;AACpC,iBAAO,KAAK,cAAc,MAAM,KAAK,gBAAgB;AAAA,QACvD,OAAO;AACL,iBAAO,KAAK,cAAc,MAAM,UAAU,gBAAgB;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAEA,cAAU,UAAU,uBAAuB,SAAS,GAAG;AACrD,UAAI,mBAAmB;AAEvB,UAAI,MAAM,KAAK;AACb,YAAI,KAAK,gBAAgB,GAAG;AAC1B,6BAAmB,KAAK,WAAW,QAAQ,KAAK;AAEhD,cAAI,kBAAkB;AACpB,mBAAO,KAAK,cAAc,MAAM,SAAS,iBAAiB,KAAK,IAAI,IAAI;AAAA,UACzE;AAAA,QACF;AAGA,2BAAmB,KAAK,WAAW,QAAQ,KAAK;AAEhD,YAAI,kBAAkB;AACpB,iBAAO,KAAK,cAAc,MAAM,SAAS,iBAAiB,KAAK,IAAI,IAAI;AAAA,QACzE;AAEA,YAAI,KAAK,OAAO,KAAK;AAGrB,YAAI,QAAQ;AACZ,YAAI,KAAK,OAAO,QAAQ,KAAK,KAAK,OAAO,SAAS,KAAK,GAAG;AACxD,aAAG;AACD,gBAAI,KAAK,OAAO,KAAK;AACrB,qBAAS;AAAA,UACX,SAAS,KAAK,OAAO,QAAQ,KAAK,MAAM,OAAO,MAAM;AACrD,cAAI,MAAM,KAAK;AAAA,UAEf,WAAW,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AACpE,qBAAS;AACT,iBAAK,OAAO,KAAK;AACjB,iBAAK,OAAO,KAAK;AAAA,UACnB,WAAW,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AACpE,qBAAS;AACT,iBAAK,OAAO,KAAK;AACjB,iBAAK,OAAO,KAAK;AAAA,UACnB;AACA,iBAAO,KAAK,cAAc,MAAM,MAAM,KAAK;AAAA,QAC7C;AAEA,aAAK,OAAO,KAAK;AAAA,MAEnB,WAAW,MAAM,OAAO,KAAK,gBAAgB,GAAG;AAC9C,2BAAmB,KAAK,WAAW,mBAAmB,KAAK;AAC3D,YAAI,kBAAkB;AACpB,iBAAO,KAAK,OAAO,QAAQ,KAAK,CAAC,KAAK,OAAO,SAAS,MAAM,OAAO,GAAG;AACpE,gCAAoB,KAAK,OAAO,KAAK;AAAA,UACvC;AACA,4BAAkB;AAClB,iBAAO,KAAK,cAAc,MAAM,SAAS,gBAAgB;AAAA,QAC3D;AAAA,MACF,WAAW,mBAAmB,MAAM,KAAK;AACvC,2BAAmB,KAAK,WAAW,iBAAiB,KAAK;AACzD,YAAI,kBAAkB;AACpB,4BAAkB;AAClB,iBAAO,KAAK,cAAc,MAAM,SAAS,gBAAgB;AAAA,QAC3D;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,gBAAgB,SAAS,GAAG;AAC9C,UAAI,QAAQ;AACZ,UAAI,MAAM,KAAK;AACb,YAAI,UAAU;AACd,YAAI,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AAE/B,oBAAU,KAAK,WAAW,cAAc,KAAK;AAC7C,cAAI,aAAa,gBAAgB,eAAe,OAAO;AACvD,cAAI,cAAc,WAAW,WAAW,SAAS;AAC/C,uBAAW,gBAAgB,YAAY,KAAK,MAAM;AAAA,UACpD;AACA,oBAAU,QAAQ,QAAQ,MAAM,eAAe,IAAI;AACnD,kBAAQ,KAAK,cAAc,MAAM,eAAe,OAAO;AACvD,gBAAM,aAAa;AAAA,QACrB,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AAEtC,oBAAU,KAAK,WAAW,QAAQ,KAAK;AACvC,kBAAQ,KAAK,cAAc,MAAM,SAAS,OAAO;AAAA,QACnD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,eAAe,SAAS,GAAG;AAC7C,UAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,YAAI,mBAAmB,KAAK,OAAO,KAAK;AACxC,aAAK,mBAAmB;AAExB,YAAI,MAAM,KAAK;AACb,8BAAoB,KAAK,uBAAuB,KAAK,MAAM,IAAI;AAAA,QACjE,OAAO;AACL,8BAAoB,KAAK,uBAAuB,CAAC;AAAA,QACnD;AAEA,YAAI,KAAK,oBAAoB,KAAK,SAAS,kBAAkB;AAC3D,6BAAmB,gBAAgB,gBAAgB;AAAA,QACrD;AAEA,YAAI,KAAK,OAAO,KAAK,MAAM,GAAG;AAC5B,8BAAoB,KAAK,OAAO,KAAK;AAAA,QACvC;AAEA,2BAAmB,iBAAiB,QAAQ,MAAM,eAAe,IAAI;AAErE,eAAO,KAAK,cAAc,MAAM,QAAQ,gBAAgB;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,uBAAuB,SAAS,gBAAgB;AAElE,aAAQ,eAAe,SAAS,MAAM,YAAY,SAAS,eAAe,MAAM,CAAC,UAAU,QAAQ,SAAS,QAAQ,MAAM,UAAU,OAAO,CAAC,KACzI,eAAe,SAAS,MAAM,YAAY,eAAe,SAAS,OACjE,eAAe,OAAO,SAAS,SAAS,MAAM,YAAY,SAAS,eAAe,OAAO,SAAS,MAAM,CAAC,MAAM,SAAS,KAAK,CAAC,KAC/H,SAAS,eAAe,MAAM;AAAA,QAAC,MAAM;AAAA,QAAS,MAAM;AAAA,QAAY,MAAM;AAAA,QAAa,MAAM;AAAA,QACxF,MAAM;AAAA,QAAW,MAAM;AAAA,QAAU,MAAM;AAAA,QAAQ,MAAM;AAAA,QAAK,MAAM;AAAA,QAAW,MAAM;AAAA,MACnF,CAAC;AAAA,IACL;AAEA,cAAU,UAAU,eAAe,SAAS,GAAG,gBAAgB;AAE7D,UAAI,MAAM,OAAO,KAAK,qBAAqB,cAAc,GAAG;AAG1D,YAAI,mBAAmB,KAAK,OAAO,KAAK;AACxC,YAAI,MAAM;AAEV,YAAI,gBAAgB;AACpB,eAAO,KAAK,OAAO,QAAQ,OACvB,OAAO,iBAAiB,KAAK,OAAO,KAAK,MAAM,MAC/C,CAAC,KAAK,OAAO,SAAS,MAAM,OAAO,IAAI;AACzC,8BAAoB,KAAK,OAAO,KAAK;AACrC,cAAI,CAAC,KAAK;AACR,kBAAM,KAAK,OAAO,KAAK,MAAM;AAC7B,gBAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B,8BAAgB;AAAA,YAClB,WAAW,KAAK,OAAO,KAAK,MAAM,KAAK;AACrC,8BAAgB;AAAA,YAClB;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AACA,eAAK,OAAO,KAAK;AAAA,QACnB;AAEA,YAAI,KAAK,OAAO,KAAK,MAAM,GAAG;AAC5B,8BAAoB,KAAK,OAAO,KAAK;AAIrC,8BAAoB,KAAK,OAAO,KAAK,MAAM,UAAU;AAAA,QACvD;AACA,eAAO,KAAK,cAAc,MAAM,QAAQ,gBAAgB;AAAA,MAC1D;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,YAAY,SAAS,GAAG,gBAAgB;AAE1D,UAAI,KAAK,SAAS,OAAO,MAAM,OAAO,KAAK,qBAAqB,cAAc,GAAG;AAC/E,YAAI,SAAS;AACb,YAAI,QAAQ,KAAK,WAAW,IAAI,WAAW;AAG3C,YAAI,OAAO;AAET,cAAI,UAAU,MAAM,CAAC,EAAE,QAAQ,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG;AACjE,cAAI,cAAc,QAAQ,QAAQ,GAAG,MAAM;AAC3C,cAAI,QAAQ;AACZ,iBAAO,OAAO;AACZ,gBAAI,WAAW,CAAC,CAAC,MAAM,CAAC;AACxB,gBAAI,UAAU,MAAM,CAAC;AACrB,gBAAI,iBAAkB,CAAC,CAAC,MAAM,MAAM,SAAS,CAAC,KAAO,QAAQ,MAAM,GAAG,CAAC,MAAM;AAC7E,gBAAI,CAAC,mBACF,YAAY,WAAY,eAAe,QAAQ,QAAQ,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG,IAAK;AAC/F,kBAAI,UAAU;AACZ,kBAAE;AAAA,cACJ,OAAO;AACL,kBAAE;AAAA,cACJ;AAAA,YACF;AACA,sBAAU,MAAM,CAAC;AACjB,gBAAI,SAAS,GAAG;AACd;AAAA,YACF;AACA,oBAAQ,KAAK,WAAW,IAAI,WAAW;AAAA,UACzC;AAEA,cAAI,CAAC,OAAO;AACV,sBAAU,KAAK,OAAO,MAAM,UAAU,EAAE,CAAC;AAAA,UAC3C;AACA,mBAAS,OAAO,QAAQ,MAAM,eAAe,IAAI;AACjD,iBAAO,KAAK,cAAc,MAAM,QAAQ,MAAM;AAAA,QAChD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,GAAG;AAM1B,UAAI,MAAM,IACR,UAAU;AAEZ,UAAI,aAAa,IAAI,aAAa,CAAC;AACnC,UAAI,UAAU;AAEd,aAAO,WAAW,QAAQ,GAAG;AAG3B,kBAAU,WAAW,MAAM,qBAAqB;AAEhD,YAAI,SAAS;AACX,iBAAO,QAAQ,CAAC;AAAA,QAClB;AAEA,YAAI,WAAW,KAAK,MAAM,MAAM;AAC9B,qBAAW,KAAK;AAChB,cAAI,WAAW,KAAK,MAAM,KAAK;AAC7B,sBAAU,WAAW,MAAM,oBAAoB;AAAA,UACjD,WAAW,WAAW,KAAK,MAAM,KAAK;AACpC,sBAAU,WAAW,MAAM,oBAAoB;AAC/C,gBAAI,CAAC,SAAS;AACZ,wBAAU,WAAW,MAAM,sBAAsB;AAAA,YACnD;AAAA,UACF,OAAO;AACL,mBAAO;AACP,gBAAI,WAAW,QAAQ,GAAG;AACxB,qBAAO,WAAW,KAAK;AAAA,YACzB;AACA;AAAA,UACF;AAGA,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,UACT;AAEA,oBAAU,SAAS,QAAQ,CAAC,GAAG,EAAE;AAEjC,cAAI,UAAU,OAAQ,WAAW,OAAQ,QAAQ,CAAC,EAAE,QAAQ,GAAG,MAAM,GAAG;AAItE,mBAAO;AAAA,UACT,WAAW,WAAW,KAAQ,UAAU,IAAM;AAE5C,mBAAO,OAAO,QAAQ,CAAC;AAAA,UACzB,WAAW,UAAU,SAAU;AAE7B,mBAAO,OAAO,QAAQ,CAAC;AAAA,UACzB,WAAW,YAAY,MAAQ,YAAY,MAAQ,YAAY,IAAM;AAEnE,mBAAO,OAAO,OAAO,aAAa,OAAO;AAAA,UAC3C,OAAO;AACL,mBAAO,OAAO,aAAa,OAAO;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAIA,cAAU,UAAU,yBAAyB,SAAS,WAAW,0BAA0B,WAAW;AACpG,UAAI;AACJ,UAAI;AACJ,UAAI,cAAc,KAAM;AACtB,kBAAU,KAAK,WAAW;AAAA,MAC5B,WAAW,cAAc,KAAK;AAC5B,kBAAU,KAAK,WAAW;AAAA,MAC5B,WAAW,cAAc,KAAK;AAC5B,kBAAU,KAAK,WAAW;AAAA,MAC5B,WAAW,cAAc,KAAK;AAC5B,kBAAU,KAAK,WAAW;AAAA,MAC5B;AAEA,UAAI,mBAAmB,QAAQ,KAAK;AACpC,UAAI,OAAO;AACX,aAAO,KAAK,OAAO,QAAQ,GAAG;AAC5B,eAAO,KAAK,OAAO,KAAK;AACxB,YAAI,SAAS,aACV,CAAC,4BAA4B,MAAM,QAAQ,KAAK,IAAI,GAAI;AACzD,eAAK,OAAO,KAAK;AACjB;AAAA,QACF,WAAW,SAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG;AACjD,yBAAe,KAAK,OAAO,KAAK;AAEhC,cAAI,iBAAiB,OAAO,iBAAiB,KAAK;AAChD,iBAAK,mBAAmB;AAAA,UAC1B,WAAW,iBAAiB,QAAQ,KAAK,OAAO,KAAK,CAAC,MAAM,MAAM;AAChE,iBAAK,OAAO,KAAK;AAAA,UACnB;AACA,kBAAQ,KAAK,OAAO,KAAK;AAAA,QAC3B,WAAW,WAAW;AACpB,cAAI,cAAc,QAAQ,SAAS,OAAO,KAAK,OAAO,KAAK,MAAM,KAAK;AACpE,oBAAQ,KAAK,OAAO,KAAK;AAAA,UAC3B;AAEA,cAAI,cAAc,MAAM;AACtB,gBAAI,cAAc,KAAK;AACrB,sBAAQ,KAAK,uBAAuB,KAAK,0BAA0B,GAAG;AAAA,YACxE,OAAO;AACL,sBAAQ,KAAK,uBAAuB,KAAK,0BAA0B,IAAI;AAAA,YACzE;AACA,gBAAI,KAAK,OAAO,QAAQ,GAAG;AACzB,sBAAQ,KAAK,OAAO,KAAK;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,QAAQ,KAAK;AACrB,4BAAoB;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,YAAY;AAC3B,WAAO,QAAQ,QAAQ;AACvB,WAAO,QAAQ,yBAAyB,uBAAuB,MAAM;AACrE,WAAO,QAAQ,gBAAgB,cAAc,MAAM;AAAA;AAAA;;;ACzkBnD;AAAA;AAAA;AA8BA,QAAI,SAAS,iBAA0B;AACvC,QAAI,QAAQ,gBAAyB;AACrC,QAAI,QAAQ;AACZ,QAAI,UAAU,mBAAqB;AACnC,QAAI,YAAY,qBAAuB;AACvC,QAAI,gBAAgB,qBAAuB;AAC3C,QAAI,yBAAyB,qBAAuB;AACpD,QAAI,QAAQ,qBAAuB;AAGnC,aAAS,SAAS,MAAM,KAAK;AAC3B,aAAO,IAAI,QAAQ,IAAI,MAAM;AAAA,IAC/B;AAEA,aAAS,MAAM,GAAG;AAChB,aAAO,EAAE,QAAQ,SAAS,EAAE;AAAA,IAC9B;AAEA,aAAS,uBAAuB,MAAM;AACpC,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAEpC,eAAO,KAAK,CAAC,EAAE,QAAQ,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC;AAAA,MAC7C;AACA,aAAO;AAAA,IACT;AAEA,aAAS,cAAc,OAAO,MAAM;AAClC,aAAO,SAAS,MAAM,SAAS,MAAM,YAAY,MAAM,SAAS;AAAA,IAClE;AAEA,aAAS,eAAe,OAAO,OAAO;AACpC,aAAO,SAAS,MAAM,SAAS,MAAM,YAAY,SAAS,MAAM,MAAM,KAAK;AAAA,IAC7E;AAEA,QAAI,gBAAgB,CAAC,QAAQ,UAAU,MAAM,MAAM,SAAS,QAAQ,SAAS,SAAS,YAAY,OAAO;AAEzG,QAAI,sBAAsB,CAAC,kBAAkB,iBAAiB,kBAAkB;AAGhF,QAAI,oBAAoB,uBAAuB,mBAAmB;AAElE,QAAI,uCAAuC,CAAC,kBAAkB,gBAAgB,kBAAkB,gBAAgB;AAEhH,QAAI,OAAO;AAAA,MACT,gBAAgB;AAAA;AAAA,MAChB,WAAW;AAAA;AAAA,MACX,eAAe;AAAA;AAAA,MACf,cAAc;AAAA;AAAA,MACd,gBAAgB;AAAA;AAAA,MAChB,aAAa;AAAA;AAAA,MACb,YAAY;AAAA;AAAA,IACd;AAEA,aAAS,6BAA6B,QAAQ,OAAO;AAMnD,UAAI,MAAM,mBACR,MAAM,SAAS,KAAK,kBACpB,MAAM,SAAS,KAAK,aAAa;AACjC;AAAA,MACF;AAGA,aAAO,cAAc,MAAM,gBAAgB;AAAA,IAC7C;AAIA,aAAS,iBAAiB,GAAG;AAG3B,UAAI,EAAE,QAAQ,MAAM,eAAe,IAAI;AACvC,UAAI,MAAM,CAAC,GACT,MAAM,EAAE,QAAQ,IAAI;AACtB,aAAO,QAAQ,IAAI;AACjB,YAAI,KAAK,EAAE,UAAU,GAAG,GAAG,CAAC;AAC5B,YAAI,EAAE,UAAU,MAAM,CAAC;AACvB,cAAM,EAAE,QAAQ,IAAI;AAAA,MACtB;AACA,UAAI,EAAE,QAAQ;AACZ,YAAI,KAAK,CAAC;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AAEA,aAAS,SAAS,MAAM;AACtB,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,aAAS,cAAc,MAAM;AAC3B,aAAO,SAAS,MAAM,CAAC,KAAK,YAAY,KAAK,gBAAgB,KAAK,WAAW,CAAC;AAAA,IAChF;AAEA,aAAS,qBAAqB,OAAO,GAAG;AACtC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,OAAO,MAAM,CAAC,EAAE,KAAK;AACzB,YAAI,KAAK,OAAO,CAAC,MAAM,GAAG;AACxB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,yBAAyB,OAAO,QAAQ;AAC/C,UAAI,IAAI,GACN,MAAM,MAAM,QACZ;AACF,aAAO,IAAI,KAAK,KAAK;AACnB,eAAO,MAAM,CAAC;AAEd,YAAI,QAAQ,KAAK,QAAQ,MAAM,MAAM,GAAG;AACtC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAGA,aAAS,WAAW,aAAa,SAAS;AACxC,gBAAU,WAAW,CAAC;AACtB,WAAK,eAAe,eAAe;AAEnC,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,kBAAkB;AACvB,WAAK,SAAS;AACd,WAAK,kBAAkB;AAEvB,WAAK,cAAc;AACnB,WAAK,WAAW,IAAI,QAAQ,OAAO;AAAA,IACrC;AAEA,eAAW,UAAU,eAAe,SAAS,YAAY,MAAM;AAC7D,UAAI,oBAAoB;AACxB,UAAI,YAAY;AACd,4BAAoB,WAAW;AAC/B,YAAI,CAAC,KAAK,QAAQ,mBAAmB,KACnC,WAAW,oBAAoB,mBAAmB;AAClD,8BAAoB,WAAW;AAAA,QACjC;AAAA,MACF;AAEA,UAAI,aAAa;AAAA,QACf;AAAA,QACA,QAAQ;AAAA,QACR,YAAY,aAAa,WAAW,aAAa,IAAI,MAAM,MAAM,aAAa,EAAE;AAAA;AAAA,QAChF,WAAW,aAAa,WAAW,YAAY;AAAA;AAAA,QAC/C,uBAAuB;AAAA,QACvB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,mBAAmB;AAAA;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,QACd,mBAAmB;AAAA;AAAA,QACnB,SAAS;AAAA;AAAA,QACT,WAAW;AAAA;AAAA,QACX,YAAY;AAAA;AAAA,QACZ,mBAAmB;AAAA,QACnB,WAAW;AAAA,QACX,mBAAmB,aAAa,WAAW,oBAAoB;AAAA,QAC/D,kBAAkB,KAAK,QAAQ,gBAAgB;AAAA,QAC/C,eAAe;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,SAAS,SAAS,aAAa;AAClD,UAAI,mBAAmB,YAAY,MAAM,SAAS,EAAE,CAAC;AAErD,WAAK,kBAAkB;AACvB,WAAK,UAAU,IAAI,OAAO,KAAK,UAAU,gBAAgB;AAGzD,WAAK,QAAQ,MAAM,KAAK,SAAS;AAajC,WAAK,cAAc,CAAC;AACpB,WAAK,SAAS,KAAK,cAAc;AACjC,UAAI,YAAY,IAAI,UAAU,aAAa,KAAK,QAAQ;AACxD,WAAK,UAAU,UAAU,SAAS;AAClC,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,WAAW,WAAW;AAEzC,UAAI,KAAK,SAAS,UAAU;AAC1B,eAAO,KAAK;AAAA,MACd;AAEA,UAAI;AACJ,UAAI,cAAc,KAAK,OAAO,KAAK,YAAY;AAE/C,UAAI,MAAM,KAAK,SAAS;AACxB,UAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,cAAM;AACN,YAAI,eAAe,MAAM,UAAU,KAAK,eAAe,EAAE,GAAG;AAC1D,gBAAM,YAAY,MAAM,MAAM,SAAS,EAAE,CAAC;AAAA,QAC5C;AAAA,MACF;AAEA,UAAI,gBAAgB,KAAK,QAAQ,KAAK;AACtC,aAAO,eAAe;AACpB,aAAK,aAAa,aAAa;AAE/B,aAAK,kBAAkB,KAAK,OAAO,WAAW;AAC9C,aAAK,OAAO,aAAa;AAEzB,wBAAgB,KAAK,QAAQ,KAAK;AAAA,MACpC;AAEA,mBAAa,KAAK,QAAQ,SAAS,GAAG;AAEtC,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,eAAe,SAAS,eAAe,0BAA0B;AACpF,UAAI,cAAc,SAAS,MAAM,YAAY;AAC3C,aAAK,kBAAkB,aAAa;AAAA,MACtC,WAAW,cAAc,SAAS,MAAM,UAAU;AAChD,aAAK,gBAAgB,aAAa;AAAA,MACpC,WAAW,cAAc,SAAS,MAAM,aAAa;AACnD,aAAK,mBAAmB,aAAa;AAAA,MACvC,WAAW,cAAc,SAAS,MAAM,WAAW;AACjD,aAAK,iBAAiB,aAAa;AAAA,MACrC,WAAW,cAAc,SAAS,MAAM,MAAM;AAC5C,aAAK,YAAY,aAAa;AAAA,MAChC,WAAW,cAAc,SAAS,MAAM,UAAU;AAChD,aAAK,YAAY,aAAa;AAAA,MAChC,WAAW,cAAc,SAAS,MAAM,WAAW;AACjD,aAAK,iBAAiB,aAAa;AAAA,MACrC,WAAW,cAAc,SAAS,MAAM,QAAQ;AAC9C,aAAK,cAAc,aAAa;AAAA,MAClC,WAAW,cAAc,SAAS,MAAM,QAAQ;AAC9C,aAAK,cAAc,aAAa;AAAA,MAClC,WAAW,cAAc,SAAS,MAAM,UAAU;AAChD,aAAK,gBAAgB,aAAa;AAAA,MACpC,WAAW,cAAc,SAAS,MAAM,OAAO;AAC7C,aAAK,aAAa,aAAa;AAAA,MACjC,WAAW,cAAc,SAAS,MAAM,eAAe;AACrD,aAAK,qBAAqB,eAAe,wBAAwB;AAAA,MACnE,WAAW,cAAc,SAAS,MAAM,SAAS;AAC/C,aAAK,eAAe,eAAe,wBAAwB;AAAA,MAC7D,WAAW,cAAc,SAAS,MAAM,KAAK;AAC3C,aAAK,WAAW,aAAa;AAAA,MAC/B,WAAW,cAAc,SAAS,MAAM,KAAK;AAC3C,aAAK,WAAW,aAAa;AAAA,MAC/B,WAAW,cAAc,SAAS,MAAM,SAAS;AAC/C,aAAK,eAAe,eAAe,wBAAwB;AAAA,MAC7D,OAAO;AACL,aAAK,eAAe,eAAe,wBAAwB;AAAA,MAC7D;AAAA,IACF;AAEA,eAAW,UAAU,iCAAiC,SAAS,eAAe,0BAA0B;AACtG,UAAI,WAAW,cAAc;AAC7B,UAAI,kBAAkB,KAAK,SAAS,0BAA0B,SAAS,KAAK,OAAO,IAAI;AAEvF,UAAI,cAAc,iBAAiB;AACjC,YAAI,gBAAgB,cAAc,gBAAgB,KAAK;AACvD,eAAO,eAAe;AAIpB,eAAK,+BAA+B,eAAe,wBAAwB;AAC3E,eAAK,aAAa,eAAe,wBAAwB;AACzD,0BAAgB,cAAc,gBAAgB,KAAK;AAAA,QACrD;AAAA,MACF;AAEA,UAAI,iBAAiB;AACnB,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,eAAK,cAAc,IAAI,GAAG,wBAAwB;AAAA,QACpD;AAAA,MACF,OAAO;AACL,YAAI,KAAK,SAAS,yBAAyB,WAAW,KAAK,SAAS,uBAAuB;AACzF,qBAAW,KAAK,SAAS;AAAA,QAC3B;AAEA,YAAI,KAAK,SAAS,mBAAmB;AACnC,cAAI,WAAW,GAAG;AAChB,iBAAK,cAAc,OAAO,wBAAwB;AAClD,qBAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,mBAAK,cAAc,MAAM,wBAAwB;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IAEF;AAEA,QAAI,4BAA4B,CAAC,SAAS,SAAS,YAAY,UAAU,SAAS,OAAO;AAEzF,eAAW,UAAU,kCAAkC,SAAS,eAAe,gBAAgB;AAC7F,uBAAkB,mBAAmB,SAAa,QAAQ;AAG1D,UAAI,KAAK,QAAQ,mBAAmB,GAAG;AACrC;AAAA,MACF;AAEA,UAAI,wBAAyB,KAAK,SAAS,qBAAqB,cAAc,YAAa;AAC3F,UAAI,uBAAuB,SAAS,KAAK,OAAO,WAAW,MAAM,sBAAsB,KACrF,SAAS,cAAc,MAAM,sBAAsB;AAErD,UAAI,sBAAsB;AACxB,YAAI,6BACA,SAAS,KAAK,OAAO,WAAW,MAAM,sBAAsB,KAC5D,SAAS,KAAK,SAAS,mBAAmB,oCAAoC,KAEhF,SAAS,cAAc,MAAM,sBAAsB;AACrD,gCAAwB,yBAAyB;AAAA,MACnD;AAEA,UAAI,uBAAuB;AACzB,aAAK,cAAc,OAAO,IAAI;AAAA,MAChC,WAAW,KAAK,SAAS,kBAAkB;AACzC,YAAI,eAAe,KAAK,OAAO,YAAY,yBAAyB,GAAG;AAGrE;AAAA,QACF;AACA,aAAK,QAAQ,eAAe;AAAA,MAC9B;AAAA,IACF;AAEA,eAAW,UAAU,gBAAgB,SAAS,eAAe,0BAA0B;AACrF,UAAI,CAAC,0BAA0B;AAC7B,YAAI,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,QAAQ,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,QAAQ,KAAK,OAAO,WAAW,SAAS,OAAO;AACzP,cAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,iBAAO,KAAK,OAAO,SAAS,KAAK,aAC/B,EAAE,KAAK,OAAO,YAAY,cAAc,YAAY,MAAM,MAC1D,CAAC,KAAK,OAAO,UAAU;AACvB,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,QAAQ,aAAa,aAAa,GAAG;AAC5C,aAAK,OAAO,kBAAkB;AAAA,MAChC;AAAA,IACF;AAEA,eAAW,UAAU,+BAA+B,SAAS,eAAe;AAC1E,UAAI,KAAK,QAAQ,mBAAmB,GAAG;AACrC,YAAI,KAAK,SAAS,0BAChB,cAAc,aACb,cAAc,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI;AAC5D,eAAK,QAAQ,aAAa,WAAW,EAAE;AACvC,eAAK,QAAQ,aAAa,KAAK,cAAc,iBAAiB;AAC9D,eAAK,QAAQ,qBAAqB;AAAA,QACpC,WAAW,KAAK,QAAQ,WAAW,KAAK,OAAO,mBAAmB,KAAK,OAAO,SAAS,GAAG;AACxF,eAAK,OAAO,oBAAoB,KAAK,OAAO;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAEA,eAAW,UAAU,cAAc,SAAS,eAAe;AACzD,UAAI,KAAK,QAAQ,KAAK;AACpB,aAAK,QAAQ,cAAc,aAAa;AACxC;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,eAAe,cAAc,YAAY,cAAc,SAAS,SAAS,MAAM,SAC/F,KAAK,QAAQ,mBAAmB,GAAG;AACnC,YAAI,KAAK,QAAQ,cAAc,KAAK,MAAM,KAAK;AAC7C,cAAI,SAAS,KAAK,QAAQ,cAAc,IAAI;AAG5C,cAAI,KAAK,QAAQ,cAAc,SAAS,GAAG;AACzC,iBAAK,QAAQ,cAAc,KAAK,MAAM;AACtC,iBAAK,QAAQ,KAAK,IAAI;AACtB,iBAAK,QAAQ,aAAa,IAAI;AAC9B,iBAAK,QAAQ,KAAK;AAAA,UACpB;AAGA,eAAK,6BAA6B,aAAa;AAC/C,eAAK,QAAQ,UAAU,GAAG;AAC1B,eAAK,QAAQ,qBAAqB;AAAA,QACpC;AAAA,MACF;AAEA,WAAK,6BAA6B,aAAa;AAC/C,WAAK,QAAQ,qBAAqB;AAClC,WAAK,QAAQ,UAAU,cAAc,IAAI;AACzC,UAAI,KAAK,QAAQ,wBAAwB;AACvC,aAAK,OAAO,kBAAkB;AAAA,MAChC;AAAA,IACF;AAEA,eAAW,UAAU,SAAS,WAAW;AACvC,WAAK,OAAO,qBAAqB;AACjC,WAAK,QAAQ,WAAW,KAAK,OAAO,mBAAmB,KAAK,OAAO,SAAS;AAAA,IAC9E;AAEA,eAAW,UAAU,WAAW,WAAW;AACzC,UAAI,KAAK,OAAO,oBAAoB,MAChC,CAAC,KAAK,OAAO,UAAW,KAAK,OAAO,oBAAoB,KAAK,OAAO,OAAO,oBAAoB;AACjG,aAAK,OAAO,qBAAqB;AACjC,aAAK,QAAQ,WAAW,KAAK,OAAO,mBAAmB,KAAK,OAAO,SAAS;AAAA,MAC9E;AAAA,IACF;AAEA,eAAW,UAAU,WAAW,SAAS,MAAM;AAC7C,UAAI,KAAK,QAAQ;AACf,aAAK,YAAY,KAAK,KAAK,MAAM;AACjC,aAAK,kBAAkB,KAAK;AAAA,MAC9B,OAAO;AACL,aAAK,kBAAkB,KAAK,aAAa,MAAM,IAAI;AAAA,MACrD;AAEA,WAAK,SAAS,KAAK,aAAa,KAAK,iBAAiB,IAAI;AAC1D,WAAK,QAAQ,WAAW,KAAK,OAAO,mBAAmB,KAAK,OAAO,SAAS;AAAA,IAC9E;AAGA,eAAW,UAAU,eAAe,WAAW;AAC7C,UAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,aAAK,kBAAkB,KAAK;AAC5B,aAAK,SAAS,KAAK,YAAY,IAAI;AACnC,YAAI,KAAK,gBAAgB,SAAS,KAAK,WAAW;AAChD,uCAA6B,KAAK,SAAS,KAAK,eAAe;AAAA,QACjE;AACA,aAAK,QAAQ,WAAW,KAAK,OAAO,mBAAmB,KAAK,OAAO,SAAS;AAAA,MAC9E;AAAA,IACF;AAEA,eAAW,UAAU,2BAA2B,WAAW;AACzD,aAAO,KAAK,OAAO,OAAO,SAAS,KAAK,iBAAiB,KAAK,OAAO,SAAS,KAAK,cAChF,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,kBAAkB,KAAO,eAAe,KAAK,OAAO,YAAY,CAAC,OAAO,KAAK,CAAC;AAAA,IACtI;AAEA,eAAW,UAAU,qBAAqB,SAAS,eAAe;AAChE,UAAI,QAAQ;AACZ,cAAQ,SAAS,eAAe,KAAK,OAAO,YAAY,CAAC,OAAO,OAAO,OAAO,CAAC,KAAK,cAAc,SAAS,MAAM;AACjH,cAAQ,SAAS,cAAc,KAAK,OAAO,YAAY,IAAI;AAC3D,cAAQ,SAAU,EAAE,KAAK,OAAO,OAAO,SAAS,KAAK,iBAAiB,KAAK,OAAO,SAAS,KAAK,cAAe,eAAe,KAAK,OAAO,YAAY,yBAAyB,KAAK,CAAC,cAAc;AACnM,cAAQ,SAAS,cAAc,KAAK,OAAO,YAAY,MAAM,KAC3D,EAAE,cAAc,eAAe,IAAI,KAAK,CAAC,cAAc;AACzD,cAAQ,SAAU,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,gBAAgB,SAAS,KAAK,kBAAkB,KAAK,gBAAgB,SAAS,KAAK;AAC7J,cAAQ,SAAU,KAAK,OAAO,WAAW,SAAS,MAAM,QAAQ,KAAK,OAAO,SAAS,KAAK,kBACxF,CAAC,KAAK,OAAO,WACb,EAAE,cAAc,SAAS,QAAQ,cAAc,SAAS,SACxD,KAAK,oBAAoB,cACzB,cAAc,SAAS,MAAM,QAAQ,cAAc,SAAS,MAAM;AACpE,cAAQ,SAAU,KAAK,OAAO,SAAS,KAAK,kBACzC,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,kBAAkB,KAAM,eAAe,KAAK,OAAO,YAAY,CAAC,OAAO,KAAK,CAAC;AAEnI,UAAI,OAAO;AACT,aAAK,SAAS,KAAK,SAAS;AAC5B,aAAK,OAAO;AAEZ,aAAK,+BAA+B,eAAe,IAAI;AAKvD,YAAI,CAAC,KAAK,yBAAyB,GAAG;AACpC,eAAK;AAAA,YAAgC;AAAA,YACnC,eAAe,eAAe,CAAC,MAAM,OAAO,MAAM,OAAO,CAAC;AAAA,UAAC;AAAA,QAC/D;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,oBAAoB,SAAS,eAAe;AAE/D,UAAI,CAAC,KAAK,mBAAmB,aAAa,GAAG;AAC3C,aAAK,+BAA+B,aAAa;AAAA,MACnD;AAEA,UAAI,YAAY,KAAK;AACrB,UAAI,cAAc,SAAS,KAAK;AAE9B,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,QAAQ,KAAK,OAAO,WAAW,SAAS,KAAK;AAGrF,cAAI,eAAe,KAAK,OAAO,YAAY,aAAa,GAAG;AACzD,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AACA,eAAK,YAAY,aAAa;AAC9B,eAAK,SAAS,SAAS;AACvB,eAAK,OAAO;AACZ,cAAI,KAAK,SAAS,gBAAgB;AAChC,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AACA;AAAA,QACF;AAEA,oBAAY,KAAK;AACjB,YAAI,SAAS,KAAK,OAAO,IAAI,GAAG;AAC9B,cAAI,KAAK,OAAO,WAAW,SAAS,OACjC,KAAK,OAAO,WAAW,SAAS,QAAQ,KAAK,oBAAoB,OAAO,KAAK,oBAAoB,MAAO;AAGzG,gBAAI,CAAC,KAAK,SAAS,wBAAwB;AACzC,mBAAK,cAAc;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAEA,YAAI,CAAC,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,YAAY,MAAM,UAAU,MAAM,MAAM,MAAM,UAAU,MAAM,GAAG,CAAC,GAAG;AACrH,eAAK,QAAQ,qBAAqB;AAAA,QACpC;AAAA,MACF,OAAO;AACL,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AAClD,cAAI,KAAK,OAAO,WAAW,SAAS,OAAO;AACzC,iBAAK,QAAQ,qBAAqB,KAAK,SAAS;AAChD,wBAAY,KAAK;AAAA,UACnB,WAAW,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,SAAS,QAAQ,CAAC,GAAG;AAC3E,iBAAK,QAAQ,qBAAqB,KAAK,SAAS;AAChD,wBAAY,KAAK;AAAA,UACnB,WAAW,SAAS,KAAK,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,GAAG;AAE9D,iBAAK,QAAQ,qBAAqB;AAAA,UACpC,WAAW,KAAK,OAAO,WAAW,SAAS,YAAY,cAAc,sBAAsB,IAAI;AAC7F,iBAAK,QAAQ,qBAAqB;AAAA,UACpC,WAAW,SAAS,KAAK,OAAO,WAAW,MAAM,aAAa,KAAK,KAAK,OAAO,WAAW,SAAS,SAAS;AAC1G,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAAA,QACF,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AAIzG,cAAI,CAAC,KAAK,yBAAyB,GAAG;AACpC,iBAAK,gCAAgC,aAAa;AAAA,UACpD;AAAA,QACF,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,MAAM;AACrD,eAAK,QAAQ,qBAAqB;AAWlC,cAAI,gBAAgB,KAAK,QAAQ,KAAK,EAAE;AACxC,cAAI,KAAK,SAAS,8BAA8B,eAAe;AAE7D,gBAAI,kBAAkB,KAAK,QAAQ,KAAK,EAAE;AAC1C,gBAAI,eAAe,eAAe,CAAC,SAAS,UAAU,CAAC,KACpD,cAAc,SAAS,OAAO,eAAe,iBAAiB,CAAC,SAAS,UAAU,CAAC,GAAI;AACxF,mBAAK,QAAQ,qBAAqB;AAAA,YACpC,WAAW,KAAK,OAAO,SAAS,KAAK,eAAe;AAClD,kBAAK,cAAc,SAAS,OAAO,cAAc,SAAS,OACvD,cAAc,SAAS,QAAQ,gBAAgB,SAAS,OAAO,gBAAgB,SAAS,MAAO;AAChG,qBAAK,QAAQ,qBAAqB;AAAA,cACpC;AAAA,YACF,WAAW,KAAK,OAAO,UAAU,KAAK,OAAO,OAAO,mBAAmB;AACrE,mBAAK,QAAQ,qBAAqB;AAAA,YACpC;AAAA,UACF;AAAA,QACF,OAAO;AAKL,eAAK,gCAAgC,aAAa;AAAA,QACpD;AAKA,YAAK,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,OAAO,cAAc,cAAc,KAAK,OAAO,cAAc,aACvH,KAAK,OAAO,WAAW,SAAS,QAC9B,SAAS,KAAK,iBAAiB,CAAC,YAAY,OAAO,CAAC,KAClD,KAAK,OAAO,SAAS,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,CAAC,KAAK,GAAG,CAAC,IAAM;AAC/F,eAAK,QAAQ,qBAAqB,KAAK,SAAS;AAAA,QAClD;AAAA,MACF;AAEA,UAAI,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa;AAC5F,aAAK,cAAc;AAAA,MACrB,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,MAAM,OAAO;AAGtP,aAAK,gCAAgC,eAAe,cAAc,QAAQ;AAAA,MAC5E;AAEA,WAAK,YAAY,aAAa;AAC9B,WAAK,SAAS,SAAS;AACvB,UAAI,KAAK,SAAS,gBAAgB;AAChC,aAAK,QAAQ,qBAAqB;AAAA,MACpC;AAGA,WAAK,OAAO;AAAA,IACd;AAEA,eAAW,UAAU,kBAAkB,SAAS,eAAe;AAG7D,aAAO,KAAK,OAAO,SAAS,KAAK,WAAW;AAC1C,aAAK,aAAa;AAAA,MACpB;AAEA,WAAK,+BAA+B,aAAa;AAEjD,UAAI,KAAK,OAAO,iBAAiB;AAC/B,aAAK;AAAA,UAAgC;AAAA,UACnC,cAAc,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI,KAAK,CAAC,KAAK,SAAS;AAAA,QAAsB;AAAA,MACrG;AAEA,UAAI,KAAK,SAAS,gBAAgB;AAChC,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,CAAC,KAAK,SAAS,sBAAsB;AAE3F,eAAK,QAAQ,KAAK;AAClB,eAAK,QAAQ,qBAAqB;AAAA,QACpC,OAAO;AACL,eAAK,QAAQ,qBAAqB;AAAA,QACpC;AAAA,MACF;AACA,WAAK,SAAS;AACd,WAAK,YAAY,aAAa;AAC9B,WAAK,aAAa;AAElB,mCAA6B,KAAK,SAAS,KAAK,eAAe;AAG/D,UAAI,KAAK,OAAO,YAAY,KAAK,gBAAgB,SAAS,KAAK,aAAa;AAC1E,aAAK,gBAAgB,OAAO,KAAK;AACjC,aAAK,OAAO,WAAW;AACvB,aAAK,OAAO,WAAW;AAAA,MAEzB;AAAA,IACF;AAEA,eAAW,UAAU,qBAAqB,SAAS,eAAe;AAChE,WAAK,+BAA+B,aAAa;AAGjD,UAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,UAAI,eAAe,KAAK,QAAQ,KAAK,CAAC;AACtC,UAAI,KAAK,OAAO,cAAc,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AACxF,aAAK,SAAS,KAAK,cAAc;AACjC,aAAK,OAAO,oBAAoB;AAAA,MAClC,WAAW,KAAK,OAAO,WAAW;AAChC,aAAK,SAAS,KAAK,cAAc;AAAA,MACnC,WAAW,iBACN,SAAS,aAAa,MAAM,CAAC,KAAK,GAAG,CAAC,KAAK,SAAS,WAAW,MAAM,CAAC,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAQ,CAAC,KAC/G,SAAS,WAAW,MAAM,CAAC,OAAO,OAAO,KAAK,CAAC,KAAK,SAAS,aAAa,MAAM,CAAC,MAAM,MAAM,MAAM,QAAQ,CAAC,IAC5G;AAGH,YAAI,SAAS,KAAK,iBAAiB,CAAC,SAAS,WAAW,CAAC,KAAK,CAAC,SAAS,aAAa,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG;AACtG,eAAK,SAAS,KAAK,cAAc;AAAA,QACnC,OAAO;AACL,eAAK,SAAS,KAAK,aAAa;AAAA,QAClC;AAAA,MACF,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM;AAEjG,aAAK,SAAS,KAAK,cAAc;AAAA,MACnC,WAAW,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,QAAQ,MAAM,YAAY,MAAM,OAAO,MAAM,QAAQ,CAAC,KAC5G,eAAe,KAAK,OAAO,YAAY,CAAC,UAAU,SAAS,UAAU,SAAS,CAAC,GAC/E;AAKA,aAAK,SAAS,KAAK,aAAa;AAAA,MAClC,OAAO;AACL,aAAK,SAAS,KAAK,cAAc;AAAA,MACnC;AAEA,UAAI,KAAK,OAAO,YAAY;AAC1B,YAAI,eAAe,KAAK,OAAO,WAAW,UAAU,CAAC,SAAS,SAAS,CAAC,GAAG;AACzE,eAAK,OAAO,oBAAoB;AAAA,QAClC;AAAA,MACF;AAEA,UAAI,eAAe,CAAC,WAAW,mBAAmB,WAAW,SAAS;AACtE,UAAI,2BAA2B,gBAAgB,KAAK,OAAO,cAAc,cACvE,KAAK,OAAO,WAAW,SAAS,MAAM;AAExC,UAAI,KAAK,SAAS,uBAClB;AAEE,YAAI,QAAQ;AACZ,YAAI,cAAc;AAClB,aAAK,OAAO,eAAe;AAC3B,WAAG;AACD,mBAAS;AACT,wBAAc,KAAK,QAAQ,KAAK,QAAQ,CAAC;AACzC,cAAI,YAAY,UAAU;AACxB,iBAAK,OAAO,eAAe;AAC3B;AAAA,UACF;AAAA,QACF,SAAS,YAAY,SAAS,MAAM,OAClC,EAAE,YAAY,SAAS,MAAM,aAAa,YAAY,WAAW;AAAA,MACrE;AAEA,WAAK,KAAK,SAAS,gBAAgB,YAC9B,KAAK,SAAS,gBAAgB,UAAU,cAAc,aACzD,CAAC,KAAK,OAAO,cAAc;AAC3B,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,aACvC,4BACC,KAAK,OAAO,WAAW,SAAS,MAAM,UACrC,eAAe,KAAK,OAAO,YAAY,aAAa,KAAK,KAAK,OAAO,WAAW,SAAS,SAAU;AACtG,eAAK,QAAQ,qBAAqB;AAAA,QACpC,OAAO;AACL,eAAK,cAAc,OAAO,IAAI;AAAA,QAChC;AAAA,MACF,OAAO;AACL,YAAI,SAAS,KAAK,gBAAgB,IAAI,MAAM,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,KAAK,OAAO,WAAW,SAAS,MAAM,QAAQ;AAC5I,cAAI,KAAK,OAAO,WAAW,SAAS,MAAM,SAAS,KAAK,SAAS,gBAAgB;AAC/E,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAEA,cAAI,KAAK,OAAO,WAAW,SAAS,MAAM,SAAU,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,KAAK,OAAO,cAAe;AACjI,iBAAK,gCAAgC,aAAa;AAClD,iBAAK,gBAAgB,kBAAkB,KAAK,gBAAgB,mBAAmB,KAAK,OAAO;AAC3F,iBAAK,OAAO,kBAAkB;AAAA,UAChC;AAAA,QACF;AACA,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY;AACtG,cAAI,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,aAAa,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,cAAc;AAC5G,iBAAK,cAAc;AAAA,UACrB,OAAO;AACL,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AACA,WAAK,YAAY,aAAa;AAC9B,WAAK,OAAO;AAGZ,UAAI,CAAC,gBAAgB,EAAE,KAAK,SAAS,yBAAyB,KAAK,OAAO,eAAe;AACvF,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAEA,eAAW,UAAU,mBAAmB,SAAS,eAAe;AAE9D,WAAK,+BAA+B,aAAa;AAEjD,aAAO,KAAK,OAAO,SAAS,KAAK,WAAW;AAC1C,aAAK,aAAa;AAAA,MACpB;AAEA,UAAI,eAAe,KAAK,OAAO,WAAW,SAAS,MAAM;AAEzD,UAAI,KAAK,OAAO,gBAAgB,CAAC,cAAc;AAC7C,aAAK,QAAQ,qBAAqB;AAAA,MACpC,WAAW,KAAK,SAAS,gBAAgB,UAAU;AACjD,YAAI,CAAC,cAAc;AACjB,eAAK,cAAc;AAAA,QACrB;AAAA,MACF,OAAO;AAEL,YAAI,CAAC,cAAc;AACjB,cAAI,SAAS,KAAK,OAAO,IAAI,KAAK,KAAK,SAAS,wBAAwB;AAEtE,iBAAK,SAAS,yBAAyB;AACvC,iBAAK,cAAc;AACnB,iBAAK,SAAS,yBAAyB;AAAA,UAEzC,OAAO;AACL,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,WAAK,aAAa;AAClB,WAAK,YAAY,aAAa;AAAA,IAChC;AAEA,eAAW,UAAU,cAAc,SAAS,eAAe;AACzD,UAAI,cAAc,SAAS,MAAM,UAAU;AACzC,YAAI,SAAS,cAAc,MAAM,CAAC,OAAO,KAAK,CAAC,KAAK,KAAK,OAAO,SAAS,KAAK,eAAe;AAC3F,wBAAc,OAAO,MAAM;AAAA,QAC7B,WAAW,cAAc,SAAS,YAAY,SAAS,KAAK,QAAQ,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG;AAC5F,wBAAc,OAAO,MAAM;AAAA,QAC7B,WAAW,SAAS,cAAc,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,OAAO,cAAc;AACpF,wBAAc,OAAO,MAAM;AAAA,QAC7B,WAAW,KAAK,OAAO,SAAS,KAAK,eAAe;AAClD,cAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,cAAI,WAAW,SAAS,KAAK;AAC3B,0BAAc,OAAO,MAAM;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,mBAAmB,aAAa,GAAG;AAE1C,YAAI,eAAe,KAAK,OAAO,YAAY,CAAC,OAAO,OAAO,OAAO,CAAC,KAAK,cAAc,SAAS,MAAM,MAAM;AACxG,eAAK,OAAO,wBAAwB;AAAA,QACtC;AAAA,MACF,WAAW,cAAc,YAAY,CAAC,cAAc,KAAK,OAAO,IAAI,MACjE,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,OAAO,WAAW,SAAS,QAAQ,KAAK,OAAO,WAAW,SAAS,UAC5H,KAAK,OAAO,WAAW,SAAS,MAAM,WACrC,KAAK,SAAS,qBAAqB,CAAC,eAAe,KAAK,OAAO,YAAY,CAAC,OAAO,OAAO,SAAS,OAAO,KAAK,CAAC,IAAI;AACrH,aAAK,+BAA+B,aAAa;AACjD,aAAK,cAAc;AAAA,MACrB,OAAO;AACL,aAAK,+BAA+B,aAAa;AAAA,MACnD;AAEA,UAAI,KAAK,OAAO,YAAY,CAAC,KAAK,OAAO,UAAU;AACjD,YAAI,cAAc,eAAe,OAAO,GAAG;AAEzC,eAAK,QAAQ,qBAAqB;AAClC,eAAK,YAAY,aAAa;AAC9B,eAAK,QAAQ,qBAAqB;AAClC,eAAK,OAAO,WAAW;AACvB;AAAA,QACF,OAAO;AAGL,eAAK,cAAc;AACnB,eAAK,OAAO,WAAW;AAAA,QACzB;AAAA,MACF;AAKA,UAAI,KAAK,OAAO,UAAU;AACxB,YAAI,CAAC,KAAK,OAAO,cAAc,cAAc,eAAe,MAAM,GAAG;AACnE,eAAK,OAAO,aAAa;AAAA,QAC3B,OAAO;AACL,iBAAO,KAAK,OAAO,SAAS,KAAK,WAAW;AAC1C,iBAAK,aAAa;AAAA,UACpB;AACA,eAAK,OAAO,WAAW;AACvB,eAAK,OAAO,aAAa;AAAA,QAC3B;AAAA,MACF;AAEA,UAAI,KAAK,OAAO,qBAAqB,eAAe,eAAe,CAAC,QAAQ,SAAS,CAAC,GAAG;AACvF,aAAK,cAAc;AACnB,YAAI,CAAC,KAAK,OAAO,eAAe,KAAK,OAAO,aAAa,KAAK,SAAS,eAAe;AAEpF,eAAK,SAAS;AAAA,QAChB;AACA,aAAK,OAAO,YAAY;AAExB,aAAK,YAAY,aAAa;AAC9B,aAAK,OAAO,UAAU;AACtB;AAAA,MACF;AAEA,UAAI,KAAK,OAAO,WAAW,SAAS,MAAM,SAAS,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AACrM,YAAI,CAAC,KAAK,yBAAyB,KAAK;AAAA,SAEpC,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,oBAAoB,OAAO,KAAK,OAAO,OAAO,SAAS,KAAK,gBAAgB;AACxI,eAAK,gCAAgC,aAAa;AAAA,QACpD;AAAA,MACF;AAEA,UAAI,cAAc,eAAe,UAAU,GAAG;AAC5C,YAAI,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,KAAK,GAAG,CAAC,KACjD,KAAK,QAAQ,mBAAmB,KAAK,EAAE,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,OAAO,WAAW,SAAS,MAAM,WAAY;AAGnK,cAAI,CAAC,KAAK,QAAQ,qBAAqB,KAAK,CAAC,cAAc,iBAAiB;AAC1E,iBAAK,cAAc;AACnB,iBAAK,cAAc,IAAI;AAAA,UACzB;AAAA,QACF;AACA,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM,MAAM;AAChG,cAAI,eAAe,KAAK,OAAO,YAAY,CAAC,OAAO,OAAO,OAAO,QAAQ,CAAC,KACxE,eAAe,KAAK,OAAO,YAAY,yBAAyB,GAAG;AACnE,iBAAK,QAAQ,qBAAqB;AAAA,UACpC,WAAW,cAAc,KAAK,OAAO,YAAY,SAAS,KAAK,KAAK,oBAAoB,UAAU;AAChG,iBAAK,QAAQ,qBAAqB;AAAA,UACpC,WAAW,KAAK,OAAO,WAAW,SAAS,WAAW;AAEpD,iBAAK,QAAQ,qBAAqB;AAAA,UACpC,OAAO;AACL,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,KAAK;AAEhG,eAAK,QAAQ,qBAAqB;AAAA,QACpC,WAAW,CAAC,KAAK,OAAO,oBAAoB,cAAc,KAAK,OAAO,IAAI,KAAK,SAAS,KAAK,OAAO,IAAI,IAAI;AAAA,QAE5G,OAAO;AACL,eAAK,cAAc;AAAA,QACrB;AAEA,aAAK,YAAY,aAAa;AAC9B,aAAK,OAAO,YAAY,cAAc;AACtC;AAAA,MACF;AAEA,UAAI,SAAS;AAEb,UAAI,KAAK,OAAO,WAAW,SAAS,MAAM,WAAW;AAEnD,YAAI,KAAK,gBAAgB,cAAc;AACrC,mBAAS;AAAA,QACX,WAAW,CAAC,eAAe,eAAe,CAAC,QAAQ,SAAS,WAAW,MAAM,CAAC,GAAG;AAC/E,mBAAS;AAAA,QACX,OAAO;AACL,cAAI,KAAK,SAAS,gBAAgB,YAChC,KAAK,SAAS,gBAAgB,gBAC7B,KAAK,SAAS,gBAAgB,UAAU,cAAc,UAAW;AAClE,qBAAS;AAAA,UACX,OAAO;AACL,qBAAS;AACT,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAAA,QACF;AAAA,MACF,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,OAAO,SAAS,KAAK,gBAAgB;AAEtG,iBAAS;AAAA,MACX,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,cAAc,KAAK,OAAO,IAAI,GAAG;AAC7F,iBAAS;AAAA,MACX,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,QAAQ;AACvD,iBAAS;AAAA,MACX,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM,QAChG,KAAK,OAAO,WAAW,SAAS,QAC9B,SAAS,KAAK,iBAAiB,CAAC,YAAY,OAAO,CAAC,KAClD,KAAK,OAAO,SAAS,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,CAAC,KAAK,GAAG,CAAC,IAAM;AAC/F,iBAAS;AAAA,MACX,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa;AAC5D,YAAI,KAAK,OAAO,cAAc;AAC5B,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AACzD,aAAK,QAAQ,qBAAqB;AAClC,iBAAS;AAAA,MACX;AAEA,UAAI,eAAe,eAAe,aAAa,KAAK,KAAK,OAAO,WAAW,SAAS,KAAK;AACvF,YAAI,KAAK,OAAO,gBAAgB,KAAK,OAAO,WAAW,SAAS,UAAU,KAAK,OAAO,WAAW,SAAS,UAAU;AAClH,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MAEF;AAEA,UAAI,eAAe,eAAe,CAAC,QAAQ,SAAS,SAAS,CAAC,GAAG;AAC/D,aAAK,EAAE,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,KAAK,gBAAgB,SAAS,KAAK,mBACzF,KAAK,SAAS,gBAAgB,YAC9B,KAAK,SAAS,gBAAgB,gBAC7B,KAAK,SAAS,gBAAgB,UAAU,cAAc,aACzD,CAAC,KAAK,OAAO,cAAc;AAC3B,eAAK,cAAc;AAAA,QACrB,OAAO;AACL,eAAK,QAAQ,KAAK,IAAI;AACtB,cAAI,OAAO,KAAK,QAAQ;AAGxB,cAAI,KAAK,KAAK,MAAM,KAAK;AACvB,iBAAK,cAAc;AAAA,UACrB;AACA,eAAK,QAAQ,qBAAqB;AAAA,QACpC;AAAA,MACF,WAAW,WAAW,WAAW;AAC/B,YAAI,eAAe,KAAK,OAAO,YAAY,aAAa,GAAG;AAEzD,eAAK,QAAQ,qBAAqB;AAAA,QACpC,WAAW,KAAK,OAAO,WAAW,SAAS,aAAa,eAAe,eAAe,CAAC,OAAO,OAAO,OAAO,CAAC,GAAG;AAE9G,eAAK,QAAQ,qBAAqB;AAAA,QACpC,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AACzD,eAAK,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,CAAC,eAAe,eAAe,CAAC,OAAO,OAAO,OAAO,CAAC,MAAM,KAAK,OAAO,WAAW,SAAS,KAAK;AAExJ,gBAAI,cAAc,eAAe,IAAI,KAAK,cAAc,cAAc,UAAU,MAAM,GAAG;AAEvF,mBAAK,QAAQ,qBAAqB;AAAA,YACpC,OAAO;AACL,mBAAK,cAAc;AAAA,YACrB;AAAA,UACF;AAAA,QACF,WAAW,eAAe,eAAe,aAAa,KAAK,KAAK,OAAO,WAAW,SAAS,KAAK;AAC9F,eAAK,cAAc;AAAA,QACrB;AAAA,MACF,WAAW,KAAK,OAAO,mBAAmB,SAAS,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,oBAAoB,KAAK;AAC3I,aAAK,cAAc;AAAA,MACrB,WAAW,WAAW,SAAS;AAC7B,aAAK,QAAQ,qBAAqB;AAAA,MACpC;AACA,UAAI,cAAc,aAAa,cAAc,SAAS,SAAS,MAAM,QAAQ,cAAc,SAAS,SAAS,MAAM,WAAW;AAC5H,aAAK,QAAQ,qBAAqB;AAAA,MACpC;AACA,WAAK,YAAY,aAAa;AAC9B,WAAK,OAAO,YAAY,cAAc;AAEtC,UAAI,cAAc,SAAS,MAAM,UAAU;AACzC,YAAI,cAAc,SAAS,MAAM;AAC/B,eAAK,OAAO,WAAW;AAAA,QACzB,WAAW,cAAc,SAAS,MAAM;AACtC,eAAK,OAAO,WAAW;AAAA,QACzB,WAAW,cAAc,SAAS,UAAU;AAC1C,eAAK,OAAO,eAAe;AAAA,QAC7B,WAAW,KAAK,OAAO,gBAAgB,cAAc,eAAe,MAAM,GAAG;AAC3E,eAAK,OAAO,eAAe;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAEA,eAAW,UAAU,mBAAmB,SAAS,eAAe;AAC9D,UAAI,KAAK,mBAAmB,aAAa,GAAG;AAG1C,aAAK,QAAQ,qBAAqB;AAAA,MACpC,OAAO;AACL,aAAK,+BAA+B,aAAa;AAAA,MACnD;AAEA,UAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,aAAO,KAAK,OAAO,SAAS,KAAK,aAC/B,EAAE,KAAK,OAAO,YAAY,cAAc,YAAY,MAAM,MAC1D,CAAC,KAAK,OAAO,UAAU;AACvB,aAAK,aAAa;AAAA,MACpB;AAGA,UAAI,KAAK,OAAO,cAAc;AAC5B,aAAK,OAAO,eAAe;AAAA,MAC7B;AACA,WAAK,YAAY,aAAa;AAAA,IAChC;AAEA,eAAW,UAAU,gBAAgB,SAAS,eAAe;AAC3D,UAAI,cAAc,KAAK,WAAW,GAAG,KAAK,cAAc,aAAa,KAAK,cAAc,sBAAsB,OAAO,cAAc,SAAS,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,MAAM,OAAO;AAAA,MAEzM,WAAW,KAAK,mBAAmB,aAAa,GAAG;AAGjD,aAAK,QAAQ,qBAAqB;AAAA,MACpC,OAAO;AACL,aAAK,+BAA+B,aAAa;AACjD,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,KAAK,OAAO,WAAW,SAAS,MAAM,QAAQ,KAAK,OAAO,cAAc;AAC5H,eAAK,QAAQ,qBAAqB;AAAA,QACpC,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,SAAS,KAAK,OAAO,WAAW,SAAS,MAAM,cAAc,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AAC5M,cAAI,CAAC,KAAK,yBAAyB,GAAG;AACpC,iBAAK,gCAAgC,aAAa;AAAA,UACpD;AAAA,QACF,WAAY,cAAc,KAAK,WAAW,GAAG,KAAK,KAAK,OAAO,WAAW,SAAS,MAAM,aAAa,cAAc,SAAS,SAAS,OAAO,cAAc,SAAS,SAAS,QAAQ,cAAc,aAAa,GAAI;AACjN,eAAK,QAAQ,qBAAqB;AAAA,QACpC,OAAO;AACL,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AACA,WAAK,YAAY,aAAa;AAAA,IAChC;AAEA,eAAW,UAAU,gBAAgB,SAAS,eAAe;AAC3D,UAAI,KAAK,mBAAmB,aAAa,GAAG;AAAA,MAE5C,OAAO;AACL,aAAK,+BAA+B,aAAa;AAAA,MACnD;AAEA,UAAI,KAAK,OAAO,uBAAuB;AAErC,aAAK,OAAO,yBAAyB;AAAA,MACvC;AACA,WAAK,QAAQ,qBAAqB;AAClC,WAAK,YAAY,aAAa;AAC9B,WAAK,QAAQ,qBAAqB;AAAA,IACpC;AAEA,eAAW,UAAU,eAAe,SAAS,eAAe;AAC1D,WAAK,+BAA+B,eAAe,IAAI;AAEvD,WAAK,YAAY,aAAa;AAC9B,WAAK,QAAQ,qBAAqB;AAClC,UAAI,KAAK,OAAO,uBAAuB;AACrC,YAAI,cAAc,KAAK,OAAO,OAAO,IAAI,GAAG;AAE1C,eAAK,OAAO,yBAAyB;AAAA,QACvC;AAEA,YAAI,KAAK,OAAO,wBAAwB;AACtC,eAAK,OAAO,yBAAyB;AACrC,eAAK,cAAc,OAAO,IAAI;AAAA,QAChC,WAAW,KAAK,SAAS,aAAa;AAGpC,eAAK,gCAAgC,aAAa;AAAA,QACpD;AAAA,MACF,WAAW,KAAK,OAAO,SAAS,KAAK,iBAClC,KAAK,OAAO,SAAS,KAAK,aAAa,KAAK,OAAO,OAAO,SAAS,KAAK,eAAgB;AACzF,YAAI,KAAK,OAAO,SAAS,KAAK,WAAW;AACvC,eAAK,aAAa;AAAA,QACpB;AAEA,YAAI,CAAC,KAAK,OAAO,cAAc;AAC7B,eAAK,cAAc;AAAA,QACrB;AAAA,MACF,WAAW,KAAK,SAAS,aAAa;AAIpC,aAAK,gCAAgC,aAAa;AAAA,MACpD;AAAA,IACF;AAEA,eAAW,UAAU,kBAAkB,SAAS,eAAe;AAC7D,UAAI,sBAAsB,cAAc,SAAS,QAC9C,eAAe,KAAK,OAAO,YAAY,CAAC,YAAY,OAAO,CAAC,KAC1D,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,aAAa,MAAM,OAAO,MAAM,WAAW,MAAM,SAAS,CAAC;AAE7G,UAAI,UAAU,SAAS,cAAc,MAAM,CAAC,KAAK,GAAG,CAAC,MACnD,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,aAAa,MAAM,YAAY,MAAM,QAAQ,MAAM,QAAQ,CAAC,KACzG,SAAS,KAAK,OAAO,WAAW,MAAM,aAAa,KACnD,KAAK,OAAO,WAAW,SAAS;AAGlC,UAAI,KAAK,mBAAmB,aAAa,GAAG;AAAA,MAE5C,OAAO;AACL,YAAI,2BAA2B,CAAC;AAChC,aAAK,+BAA+B,eAAe,wBAAwB;AAAA,MAC7E;AAGA,UAAI,cAAc,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,MAAM,KAAK;AAC3E,aAAK,YAAY,aAAa;AAC9B;AAAA,MACF;AAEA,UAAI,cAAc,SAAS,MAAM;AAE/B,aAAK,YAAY,aAAa;AAC9B;AAAA,MACF;AAEA,UAAI,SAAS,cAAc,MAAM,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,yBAAyB,GAAG;AAE/E,aAAK,YAAY,aAAa;AAC9B;AAAA,MACF;AAIA,UAAI,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY,SAAS,KAAK,SAAS,mBAAmB,oCAAoC,GAAG;AACrI,aAAK,gCAAgC,aAAa;AAAA,MACpD;AAEA,UAAI,cAAc,SAAS,OAAO,KAAK,OAAO,SAAS;AACrD,aAAK,YAAY,aAAa;AAE9B,aAAK,OAAO,UAAU;AACtB,aAAK,OAAO,YAAY;AACxB,YAAI,KAAK,QAAQ,KAAK,EAAE,SAAS,MAAM,aAAa;AAClD,eAAK,OAAO;AACZ,eAAK,cAAc;AACnB,eAAK,OAAO,aAAa;AAAA,QAC3B,OAAO;AACL,eAAK,OAAO,aAAa;AACzB,eAAK,QAAQ,qBAAqB;AAAA,QACpC;AACA;AAAA,MACF;AAEA,UAAI,eAAe;AACnB,UAAI,cAAc;AAClB,UAAI,aAAa;AACjB,UAAI,cAAc,SAAS,KAAK;AAC9B,YAAI,KAAK,OAAO,kBAAkB,GAAG;AAEnC,yBAAe;AAAA,QACjB,OAAO;AACL,eAAK,OAAO,iBAAiB;AAC7B,uBAAa;AAAA,QACf;AAAA,MACF,WAAW,cAAc,SAAS,KAAK;AACrC,aAAK,OAAO,iBAAiB;AAAA,MAC/B;AAGA,UAAI,CAAC,WAAW,CAAC,uBAAuB,KAAK,SAAS,qBAAqB,SAAS,cAAc,MAAM,sBAAsB,GAAG;AAC/H,YAAI,UAAU,cAAc,SAAS;AACrC,YAAI,iBAAkB,WAAW;AACjC,YAAI,eAAgB,WAAW,CAAC;AAEhC,gBAAQ,KAAK,SAAS,mBAAmB;AAAA,UACvC,KAAK,kBAAkB;AAErB,iBAAK,QAAQ,qBAAqB,CAAC;AAEnC,iBAAK,YAAY,aAAa;AAE9B,gBAAI,CAAC,WAAW,gBAAgB;AAC9B,mBAAK,gCAAgC,aAAa;AAAA,YACpD;AAEA,iBAAK,QAAQ,qBAAqB;AAClC;AAAA,UAEF,KAAK,kBAAkB;AAIrB,iBAAK,QAAQ,qBAAqB;AAElC,gBAAI,CAAC,WAAW,gBAAgB;AAC9B,kBAAI,KAAK,QAAQ,KAAK,EAAE,UAAU;AAChC,qBAAK,cAAc,OAAO,IAAI;AAAA,cAChC,OAAO;AACL,qBAAK,gCAAgC,aAAa;AAAA,cACpD;AAAA,YACF,OAAO;AACL,mBAAK,QAAQ,qBAAqB;AAAA,YACpC;AAEA,iBAAK,YAAY,aAAa;AAE9B,iBAAK,QAAQ,qBAAqB;AAClC;AAAA,UAEF,KAAK,kBAAkB;AACrB,gBAAI,CAAC,cAAc;AACjB,mBAAK,gCAAgC,aAAa;AAAA,YACpD;AAIA,2BAAe,EAAE,KAAK,QAAQ,mBAAmB,KAAK;AAEtD,iBAAK,QAAQ,qBAAqB;AAClC,iBAAK,YAAY,aAAa;AAC9B,iBAAK,QAAQ,qBAAqB;AAClC;AAAA,QACJ;AAAA,MACF;AAEA,UAAI,qBAAqB;AACvB,aAAK,gCAAgC,aAAa;AAClD,uBAAe;AACf,YAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,sBAAc,cAAc,SAAS,WAAW,MAAM,CAAC,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,MACpF,WAAW,cAAc,SAAS,OAAO;AACvC,aAAK,gCAAgC,aAAa;AAClD,uBAAe,KAAK,OAAO,WAAW,SAAS,MAAM;AACrD,sBAAc;AAAA,MAChB,WAAW,SAAS,cAAc,MAAM,CAAC,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,SAAS;AAE1E,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,SAAS,KAAK,OAAO,WAAW,SAAS,MAAM,YAAY;AACnG,eAAK,gCAAgC,aAAa;AAAA,QACpD;AAEA,uBAAe;AACf,sBAAc;AAId,YAAI,cAAc,aAAa,cAAc,SAAS,QAAQ,cAAc,SAAS,QAAQ,cAAc,SAAS,MAAM;AACxH,cAAI,kBAAkB,eAAe,KAAK,OAAO,YAAY,aAAa,KAAK,cAAc;AAC7F,cAAI,oBAAoB,KAAK,gBAAgB,YAAY,KAAK,gBAAgB,aAAa;AACzF,iBAAK,aAAa;AAAA,UACpB;AACA,eAAK,cAAc,iBAAiB,IAAI;AAAA,QAC1C;AAEA,YAAI,KAAK,OAAO,WAAW,SAAS,OAAO,cAAc,KAAK,OAAO,IAAI,GAAG;AAG1E,yBAAe;AAAA,QACjB;AAEA,YAAI,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AAClD,yBAAe;AAAA,QACjB,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AACzD,yBAAe,EAAE,KAAK,OAAO,WAAW,SAAS,QAAQ,cAAc,SAAS,QAAQ,cAAc,SAAS;AAAA,QACjH,WAAW,KAAK,OAAO,WAAW,SAAS,MAAM,UAAU;AAGzD,yBAAe,SAAS,cAAc,MAAM,CAAC,MAAM,KAAK,MAAM,GAAG,CAAC,KAAK,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,KAAK,MAAM,GAAG,CAAC;AAKnI,cAAI,SAAS,cAAc,MAAM,CAAC,KAAK,GAAG,CAAC,KAAK,SAAS,KAAK,OAAO,WAAW,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG;AACnG,0BAAc;AAAA,UAChB;AAAA,QACF;AAGA,aAAM,KAAK,OAAO,SAAS,KAAK,kBAAkB,CAAC,KAAK,OAAO,gBAAiB,KAAK,OAAO,SAAS,KAAK,eACvG,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,OAAO,WAAW,SAAS,MAAM;AAG9E,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAEA,WAAK,QAAQ,qBAAqB,KAAK,QAAQ,sBAAsB;AACrE,WAAK,YAAY,aAAa;AAC9B,WAAK,QAAQ,qBAAqB;AAAA,IACpC;AAEA,eAAW,UAAU,uBAAuB,SAAS,eAAe,0BAA0B;AAC5F,UAAI,KAAK,QAAQ,KAAK;AACpB,aAAK,QAAQ,cAAc,aAAa;AACxC,YAAI,cAAc,cAAc,cAAc,WAAW,aAAa,OAAO;AAE3E,eAAK,QAAQ,MAAM,KAAK,SAAS;AAAA,QACnC;AACA;AAAA,MACF;AAEA,UAAI,cAAc,YAAY;AAC5B,aAAK,cAAc,OAAO,wBAAwB;AAClD,aAAK,YAAY,aAAa;AAC9B,YAAI,cAAc,WAAW,aAAa,SAAS;AACjD,eAAK,QAAQ,MAAM;AAAA,QACrB;AACA,aAAK,cAAc,OAAO,IAAI;AAC9B;AAAA,MACF;AAGA,UAAI,CAAC,MAAM,QAAQ,KAAK,cAAc,IAAI,KAAK,CAAC,cAAc,UAAU;AACtE,aAAK,QAAQ,qBAAqB;AAClC,aAAK,YAAY,aAAa;AAC9B,aAAK,QAAQ,qBAAqB;AAClC;AAAA,MACF,OAAO;AACL,aAAK,qBAAqB,eAAe,wBAAwB;AAAA,MACnE;AAAA,IACF;AAEA,eAAW,UAAU,uBAAuB,SAAS,eAAe,0BAA0B;AAC5F,UAAI,QAAQ,iBAAiB,cAAc,IAAI;AAC/C,UAAI;AACJ,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,aAAa,cAAc;AAC/B,UAAI,mBAAmB,WAAW;AAGlC,WAAK,cAAc,OAAO,wBAAwB;AAGlD,WAAK,6BAA6B,aAAa;AAC/C,WAAK,QAAQ,UAAU,MAAM,CAAC,CAAC;AAC/B,WAAK,cAAc,OAAO,wBAAwB;AAGlD,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,MAAM,CAAC;AACrB,kBAAU,qBAAqB,OAAO,GAAG;AACzC,mBAAW,yBAAyB,OAAO,UAAU;AAErD,YAAI,SAAS;AACX,eAAK,OAAO,YAAY;AAAA,QAC1B;AAEA,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,cAAI,SAAS;AAEX,iBAAK,6BAA6B,aAAa;AAC/C,iBAAK,QAAQ,UAAU,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,UACxC,WAAW,YAAY,MAAM,CAAC,GAAG;AAE/B,iBAAK,6BAA6B,aAAa;AAC/C,iBAAK,QAAQ,UAAU,MAAM,CAAC,EAAE,UAAU,gBAAgB,CAAC;AAAA,UAC7D,OAAO;AAEL,iBAAK,QAAQ,aAAa,WAAW,EAAE;AACvC,iBAAK,QAAQ,UAAU,MAAM,CAAC,CAAC;AAAA,UACjC;AAGA,eAAK,cAAc,OAAO,wBAAwB;AAAA,QACpD;AAEA,aAAK,OAAO,YAAY;AAAA,MAC1B;AAAA,IACF;AAGA,eAAW,UAAU,iBAAiB,SAAS,eAAe,0BAA0B;AACtF,UAAI,cAAc,UAAU;AAC1B,aAAK,cAAc,OAAO,wBAAwB;AAAA,MACpD,OAAO;AACL,aAAK,QAAQ,KAAK,IAAI;AAAA,MACxB;AAEA,WAAK,QAAQ,qBAAqB;AAClC,WAAK,YAAY,aAAa;AAC9B,WAAK,cAAc,OAAO,wBAAwB;AAAA,IACpD;AAEA,eAAW,UAAU,aAAa,SAAS,eAAe;AACxD,UAAI,KAAK,mBAAmB,aAAa,GAAG;AAAA,MAE5C,OAAO;AACL,aAAK,+BAA+B,eAAe,IAAI;AAAA,MACzD;AAEA,UAAI,KAAK,OAAO,WAAW,KAAK,MAAM,UAAU,GAAG;AACjD,aAAK,QAAQ,qBAAqB;AAAA,MACpC;AAEA,UAAI,eAAe,KAAK,OAAO,YAAY,aAAa,GAAG;AACzD,aAAK,QAAQ,qBAAqB;AAAA,MACpC,OAAO;AAGL,aAAK;AAAA,UAAgC;AAAA,UACnC,KAAK,OAAO,WAAW,SAAS,OAAO,KAAK,SAAS;AAAA,QAAqB;AAAA,MAC9E;AAIA,UAAI,KAAK,SAAS,4BAA4B,KAAK,QAAQ,mBAAmB,GAAG;AAC/E,aAAK,SAAS;AAAA,MAChB;AAEA,WAAK,YAAY,aAAa;AAAA,IAChC;AAEA,eAAW,UAAU,iBAAiB,SAAS,eAAe,0BAA0B;AACtF,WAAK,YAAY,aAAa;AAE9B,UAAI,cAAc,KAAK,cAAc,KAAK,SAAS,CAAC,MAAM,MAAM;AAC9D,aAAK,cAAc,OAAO,wBAAwB;AAAA,MACpD;AAAA,IACF;AAEA,eAAW,UAAU,aAAa,SAAS,eAAe;AAExD,aAAO,KAAK,OAAO,SAAS,KAAK,WAAW;AAC1C,aAAK,aAAa;AAAA,MACpB;AACA,WAAK,+BAA+B,aAAa;AAAA,IACnD;AAEA,WAAO,QAAQ,aAAa;AAAA;AAAA;;;ACv8C5B;AAAA;AAAA;AA8BA,QAAI,aAAa,qBAAwB;AAAzC,QACE,UAAU,mBAAqB;AAEjC,aAAS,YAAY,gBAAgB,SAAS;AAC5C,UAAI,aAAa,IAAI,WAAW,gBAAgB,OAAO;AACvD,aAAO,WAAW,SAAS;AAAA,IAC7B;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,iBAAiB,WAAW;AACzC,aAAO,IAAI,QAAQ;AAAA,IACrB;AAAA;AAAA;;;ACzCA,IAAAC,mBAAA;AAAA;AAAA;AA8BA,QAAI,cAAc,kBAA2B;AAE7C,aAAS,QAAQ,SAAS;AACxB,kBAAY,KAAK,MAAM,SAAS,KAAK;AAErC,WAAK,6BAA6B,KAAK,aAAa,8BAA8B,IAAI;AACtF,WAAK,wBAAwB,KAAK,aAAa,yBAAyB,IAAI;AAC5E,UAAI,kCAAkC,KAAK,aAAa,iCAAiC;AACzF,WAAK,0BAA0B,KAAK,aAAa,yBAAyB,KAAK;AAE/E,UAAI,oBAAoB,KAAK,oBAAoB,eAAe,CAAC,YAAY,UAAU,cAAc,QAAQ,iBAAiB,CAAC;AAC/H,WAAK,cAAc;AACnB,eAAS,KAAK,GAAG,KAAK,kBAAkB,QAAQ,MAAM;AACpD,YAAI,kBAAkB,EAAE,MAAM,UAAU;AAEtC,eAAK,cAAc;AAAA,QACrB,OAAO;AACL,eAAK,cAAc,kBAAkB,EAAE;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AACA,YAAQ,YAAY,IAAI,YAAY;AAIpC,WAAO,QAAQ,UAAU;AAAA;AAAA;;;ACvDzB,IAAAC,sBAAA;AAAA;AAAA;AA8BA,QAAI,UAAU,mBAAqB;AACnC,QAAI,SAAS,iBAA0B;AACvC,QAAI,eAAe,uBAAgC;AACnD,QAAI,aAAa,qBAA8B;AAE/C,QAAI,kBAAkB,IAAI,WAAW,QAAQ,MAAM;AAEnD,QAAI,YAAY;AAChB,QAAI,gBAAgB;AAGpB,QAAI,iBAAiB;AACrB,QAAI,oBAAoB;AACxB,QAAI,wBAAwB;AAC5B,QAAI,kBAAkB;AAEtB,aAAS,WAAW,aAAa,SAAS;AACxC,WAAK,eAAe,eAAe;AAGnC,WAAK,WAAW,IAAI,QAAQ,OAAO;AACnC,WAAK,MAAM;AACX,WAAK,SAAS;AAGd,WAAK,iBAAiB;AAAA,QACpB,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA;AAAA,QAEb,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,WAAK,yBAAyB;AAAA,QAC5B,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AACA,WAAK,iCAAiC;AAAA,QACpC;AAAA,QACA;AAAA,MACF;AAAA,IAEF;AAEA,eAAW,UAAU,YAAY,SAAS,UAAU;AAClD,UAAI,SAAS;AACb,WAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,aAAO,KAAK,KAAK;AACf,kBAAU,KAAK;AACf,YAAI,KAAK,QAAQ,MAAM;AACrB,oBAAU,KAAK,OAAO,KAAK;AAAA,QAC7B,WAAW,SAAS,QAAQ,KAAK,GAAG,MAAM,MAAM,KAAK,QAAQ,MAAM;AACjE;AAAA,QACF;AACA,aAAK,MAAM,KAAK,OAAO,KAAK;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AAMA,eAAW,UAAU,gBAAgB,SAAS,wBAAwB;AACpE,UAAI,SAAS,eAAe,KAAK,KAAK,OAAO,KAAK,CAAC;AACnD,UAAI,gBAAgB;AACpB,aAAO,eAAe,KAAK,KAAK,OAAO,KAAK,CAAC,GAAG;AAC9C,aAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,YAAI,0BAA0B,KAAK,QAAQ,MAAM;AAC/C,cAAI,kBAAkB,KAAK,gBAAgB,KAAK,SAAS,uBAAuB;AAC9E;AACA,iBAAK,QAAQ,aAAa,IAAI;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,eAAW,UAAU,yBAAyB,WAAW;AACvD,UAAI,YAAY;AAChB,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3B,aAAO,IAAI;AACT,YAAI,OAAO,KAAK;AACd,iBAAO;AAAA,QACT,WAAW,OAAO,KAAK;AAErB,uBAAa;AAAA,QACf,WAAW,OAAO,KAAK;AACrB,cAAI,cAAc,GAAG;AACnB,mBAAO;AAAA,UACT;AACA,uBAAa;AAAA,QACf,WAAW,OAAO,OAAO,OAAO,KAAK;AACnC,iBAAO;AAAA,QACT;AACA;AACA,aAAK,KAAK,OAAO,KAAK,CAAC;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,eAAe,SAAS,eAAe;AAC1D,WAAK,QAAQ,WAAW,KAAK,YAAY;AACzC,WAAK,QAAQ,qBAAqB;AAClC,WAAK,QAAQ,UAAU,aAAa;AAAA,IACtC;AAEA,eAAW,UAAU,sBAAsB,SAAS,cAAc;AAChE,UAAI,cAAc;AAChB,aAAK,QAAQ,qBAAqB;AAAA,MACpC;AAAA,IACF;AAEA,eAAW,UAAU,SAAS,WAAW;AACvC,WAAK;AAAA,IACP;AAEA,eAAW,UAAU,UAAU,WAAW;AACxC,UAAI,KAAK,eAAe,GAAG;AACzB,aAAK;AAAA,MACP;AAAA,IACF;AAIA,eAAW,UAAU,WAAW,WAAW;AACzC,UAAI,KAAK,SAAS,UAAU;AAC1B,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,cAAc,KAAK;AACvB,UAAI,MAAM,KAAK,SAAS;AACxB,UAAI,QAAQ,QAAQ;AAClB,cAAM;AACN,YAAI,eAAe,UAAU,KAAK,eAAe,EAAE,GAAG;AACpD,gBAAM,YAAY,MAAM,SAAS,EAAE,CAAC;AAAA,QACtC;AAAA,MACF;AAIA,oBAAc,YAAY,QAAQ,eAAe,IAAI;AAGrD,UAAI,mBAAmB,YAAY,MAAM,SAAS,EAAE,CAAC;AAErD,WAAK,UAAU,IAAI,OAAO,KAAK,UAAU,gBAAgB;AACzD,WAAK,SAAS,IAAI,aAAa,WAAW;AAC1C,WAAK,eAAe;AACpB,WAAK,eAAe;AAEpB,WAAK,MAAM;AACX,UAAI,aAAa;AAEjB,UAAI,aAAa;AAGjB,UAAI,sBAAsB;AAC1B,UAAI,2BAA2B;AAC/B,UAAI,wBAAwB;AAC5B,UAAI,gBAAgB;AACpB,UAAI,eAAe,KAAK;AACxB,UAAI,2BAA2B;AAC/B,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,aAAO,MAAM;AACX,qBAAa,KAAK,OAAO,KAAK,iBAAiB;AAC/C,uBAAe,eAAe;AAC9B,sBAAc;AACd,aAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,YAAI,KAAK,QAAQ,QAAQ,KAAK,OAAO,QAAQ,GAAG;AAC9C,eAAK,OAAO,KAAK,OAAO,KAAK;AAAA,QAC/B;AACA,uBAAe,KAAK;AAEpB,YAAI,CAAC,KAAK,KAAK;AACb;AAAA,QACF,WAAW,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,MAAM,KAAK;AAMzD,eAAK,QAAQ,aAAa;AAC1B,eAAK,OAAO,KAAK;AAEjB,cAAI,UAAU,KAAK,OAAO,KAAK,qBAAqB;AAGpD,cAAI,aAAa,gBAAgB,eAAe,OAAO;AACvD,cAAI,cAAc,WAAW,WAAW,SAAS;AAC/C,uBAAW,gBAAgB,YAAY,KAAK,MAAM;AAAA,UACpD;AAEA,eAAK,aAAa,OAAO;AAGzB,eAAK,cAAc,IAAI;AAIvB,eAAK,QAAQ,aAAa;AAAA,QAC5B,WAAW,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,MAAM,KAAK;AAIzD,eAAK,QAAQ,qBAAqB;AAClC,eAAK,OAAO,KAAK;AACjB,eAAK,aAAa,KAAK,OAAO,KAAK,eAAe,CAAC;AAGnD,eAAK,cAAc,IAAI;AAAA,QACzB,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,oBAAoB,YAAY;AAErC,eAAK,aAAa,KAAK,GAAG;AAG1B,cAAI,WAAW,KAAK,OAAO,eAAe,qBAAqB;AAE/D,cAAI,SAAS,MAAM,OAAO,GAAG;AAE3B,uBAAW,KAAK,UAAU,IAAI,EAAE,QAAQ,QAAQ,EAAE;AAClD,iBAAK,aAAa,QAAQ;AAC1B,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAGA,cAAI,eAAe,KAAK,SAAS,QAAQ,GAAG,MAAM,IAAI;AACpD,kCAAsB;AACtB,iBAAK,OAAO;AAAA,UACd;AAAA,QACF,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,oBAAoB,YAAY;AAGrC,cAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B,iBAAK,aAAa,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAAA,UAClD,OAAO;AACL,iBAAK,aAAa,KAAK,GAAG;AAG1B,gBAAI,iBAAiB,KAAK,OAAO,eAAe,qBAAqB;AAErE,gBAAI,eAAe,MAAM,OAAO,GAAG;AAEjC,+BAAiB,KAAK,UAAU,IAAI,EAAE,QAAQ,QAAQ,EAAE;AACxD,mBAAK,aAAa,cAAc;AAChC,mBAAK,QAAQ,qBAAqB;AAAA,YACpC;AAGA,gBAAI,eAAe,KAAK,eAAe,QAAQ,GAAG,MAAM,IAAI;AAC1D,oCAAsB;AACtB,mBAAK,OAAO;AAAA,YAGd,WAAW,kBAAkB,KAAK,gBAAgB;AAChD,mBAAK,gBAAgB;AACrB,kBAAI,kBAAkB,KAAK,wBAAwB;AACjD,2CAA2B;AAAA,cAC7B;AAAA,YAGF,WAAW,eAAe,KAAK,CAAC,qBAAqB;AACnD,sCAAwB;AAAA,YAC1B;AAAA,UACF;AAAA,QACF,WAAW,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,MAAM,KAAK;AACzD,eAAK,oBAAoB,YAAY;AACrC,eAAK,aAAa,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAAA,QAClD,WAAW,KAAK,QAAQ,KAAK;AAC3B,cAAI,qBAAqB;AACvB,kCAAsB;AACtB,iBAAK,QAAQ;AAAA,UACf;AAGA,kCAAwB;AAGxB,cAAI,0BAA0B;AAC5B,uCAA2B;AAC3B,yBAAc,KAAK,gBAAgB,KAAK;AAAA,UAC1C,OAAO;AAEL,yBAAc,KAAK,gBAAgB,KAAK,eAAe;AAAA,UACzD;AACA,cAAI,KAAK,SAAS,yBAAyB,YAAY;AACrD,gBAAI,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,cAAc,KAAK,EAAE,MAAM,KAAK;AAC7E,mBAAK,QAAQ,wBAAwB,KAAK,GAAG;AAAA,YAC/C;AAAA,UACF;AAEA,eAAK,QAAQ,qBAAqB;AAGlC,cAAI,KAAK,SAAS,gBAAgB,UAAU;AAC1C,iBAAK,QAAQ,aAAa;AAC1B,iBAAK,aAAa,KAAK,GAAG;AAC1B,iBAAK,OAAO;AACZ,iBAAK,QAAQ,WAAW,KAAK,YAAY;AAAA,UAC3C,OAAO;AAEL,gBAAI,gBAAgB,KAAK;AACvB,mBAAK,QAAQ,qBAAqB;AAAA,YACpC,WAAW,gBAAgB,KAAK;AAC9B,mBAAK,OAAO;AAAA,YACd;AACA,iBAAK,aAAa,KAAK,GAAG;AAAA,UAC5B;AAEA,eAAK,cAAc,IAAI;AACvB,eAAK,QAAQ,aAAa;AAAA,QAC5B,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,QAAQ;AACb,eAAK,QAAQ,aAAa;AAC1B,cAAI,gBAAgB,KAAK;AACvB,iBAAK,QAAQ,KAAK,IAAI;AAAA,UACxB;AAEA,cAAI,qBAAqB;AACvB,iBAAK,QAAQ;AACb,kCAAsB;AAAA,UACxB;AACA,eAAK,aAAa,KAAK,GAAG;AAC1B,uBAAa;AACb,cAAI,KAAK,cAAc;AACrB,iBAAK;AAAA,UACP;AAEA,eAAK,cAAc,IAAI;AACvB,eAAK,QAAQ,aAAa;AAE1B,cAAI,KAAK,SAAS,yBAAyB,CAAC,KAAK,QAAQ,qBAAqB,GAAG;AAC/E,gBAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B,mBAAK,QAAQ,aAAa,IAAI;AAAA,YAChC;AAAA,UACF;AACA,cAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B,iBAAK,QAAQ,KAAK,IAAI;AACtB,gBAAI,KAAK,SAAS,gBAAgB,UAAU;AAC1C,mBAAK,QAAQ,aAAa,IAAI;AAAA,YAChC;AAAA,UACF;AAAA,QACF,WAAW,KAAK,QAAQ,KAAK;AAE3B,mBAAS,IAAI,GAAG,IAAI,KAAK,+BAA+B,QAAQ,KAAK;AACnE,gBAAI,KAAK,OAAO,SAAS,KAAK,+BAA+B,CAAC,CAAC,GAAG;AAChE,yCAA2B;AAC3B;AAAA,YACF;AAAA,UACF;AAEA,eAAK,cAAc,6BAA6B,EAAE,KAAK,OAAO,SAAS,GAAG,KAAK,KAAK,uBAAuB,MAAM,CAAC,KAAK,OAAO,SAAS,GAAG,KAAK,CAAC,yBAAyB,eAAe,GAAG;AAIzL,iBAAK,aAAa,GAAG;AACrB,gBAAI,CAAC,qBAAqB;AACxB,oCAAsB;AACtB,mBAAK,QAAQ,qBAAqB;AAClC,mBAAK,cAAc,IAAI;AACvB,mBAAK,OAAO;AAAA,YACd;AAAA,UACF,OAAO;AAKL,gBAAI,KAAK,OAAO,SAAS,GAAG,GAAG;AAC7B,mBAAK,QAAQ,qBAAqB;AAAA,YACpC;AACA,gBAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAE9B,mBAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,mBAAK,aAAa,IAAI;AAAA,YACxB,OAAO;AAEL,mBAAK,aAAa,GAAG;AAAA,YACvB;AAAA,UACF;AAAA,QACF,WAAW,KAAK,QAAQ,OAAO,KAAK,QAAQ,KAAM;AAChD,cAAI,qBAAqB,gBAAgB,OAAO,gBAAgB;AAChE,eAAK,oBAAoB,sBAAsB,YAAY;AAC3D,eAAK,aAAa,KAAK,MAAM,KAAK,UAAU,KAAK,GAAG,CAAC;AACrD,eAAK,cAAc,IAAI;AAAA,QACzB,WAAW,KAAK,QAAQ,KAAK;AAC3B,qCAA2B;AAC3B,cAAI,eAAe,GAAG;AACpB,gBAAI,qBAAqB;AACvB,mBAAK,QAAQ;AACb,oCAAsB;AAAA,YACxB;AACA,oCAAwB;AACxB,iBAAK,aAAa,KAAK,GAAG;AAC1B,iBAAK,cAAc,IAAI;AAMvB,gBAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B,mBAAK,QAAQ,aAAa;AAAA,YAC5B;AAAA,UACF,OAAO;AACL,iBAAK,aAAa,KAAK,GAAG;AAC1B,iBAAK,cAAc,IAAI;AACvB,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAAA,QACF,WAAW,KAAK,QAAQ,KAAK;AAC3B,cAAI,KAAK,OAAO,SAAS,KAAK,GAAG;AAC/B,iBAAK,aAAa,KAAK,GAAG;AAC1B,iBAAK,cAAc;AACnB;AACA,iBAAK,OAAO;AACZ,iBAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,gBAAI,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK,QAAQ,KAAM;AAC7D,mBAAK,OAAO,KAAK;AAAA,YACnB,WAAW,KAAK,KAAK;AACnB,mBAAK,aAAa,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAChD,kBAAI,YAAY;AACd;AACA,qBAAK,QAAQ;AAAA,cACf;AAAA,YACF;AAAA,UACF,OAAO;AACL,gBAAI,eAAe;AACnB,gBAAI,KAAK,OAAO,SAAS,MAAM,GAAG;AAEhC,6BAAe;AAAA,YACjB;AACA,iBAAK,oBAAoB,gBAAgB,YAAY;AACrD,iBAAK,aAAa,KAAK,GAAG;AAG1B,gBAAI,uBAAuB,gBAAgB,OAAO,KAAK,SAAS,4BAA4B;AAC1F,mBAAK,QAAQ,aAAa;AAC1B,8BAAgB;AAAA,YAClB,OAAO;AACL,mBAAK,cAAc;AACnB;AACA,mBAAK,OAAO;AAAA,YACd;AAAA,UACF;AAAA,QACF,WAAW,KAAK,QAAQ,KAAK;AAC3B,cAAI,YAAY;AACd;AACA,iBAAK,QAAQ;AAAA,UACf;AACA,cAAI,iBAAiB,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,SAAS,4BAA4B;AAC3F,4BAAgB;AAChB,iBAAK,QAAQ;AACb,iBAAK,QAAQ,aAAa;AAAA,UAC5B;AACA,eAAK,aAAa,KAAK,GAAG;AAAA,QAC5B,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,aAAa,KAAK,GAAG;AAC1B,eAAK,cAAc,IAAI;AACvB,cAAI,KAAK,SAAS,+BAA+B,CAAC,uBAAuB,kBAAkB,eAAe,KAAK,CAAC,uBAAuB;AACrI,iBAAK,QAAQ,aAAa;AAAA,UAC5B,OAAO;AACL,iBAAK,QAAQ,qBAAqB;AAAA,UACpC;AAAA,QACF,YAAY,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK,QAAQ,QAAQ,CAAC,uBAAuB,eAAe,GAAG;AAEjH,cAAI,KAAK,SAAS,yBAAyB;AACzC,iBAAK,QAAQ,qBAAqB;AAClC,iBAAK,aAAa,KAAK,GAAG;AAC1B,iBAAK,QAAQ,qBAAqB;AAAA,UACpC,OAAO;AACL,iBAAK,aAAa,KAAK,GAAG;AAC1B,iBAAK,cAAc;AAEnB,gBAAI,KAAK,OAAO,eAAe,KAAK,KAAK,GAAG,GAAG;AAC7C,mBAAK,MAAM;AAAA,YACb;AAAA,UACF;AAAA,QACF,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,aAAa,KAAK,GAAG;AAAA,QAC5B,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,oBAAoB,YAAY;AACrC,eAAK,aAAa,KAAK,GAAG;AAAA,QAC5B,WAAW,KAAK,QAAQ,KAAK;AAC3B,eAAK,cAAc;AACnB,eAAK,aAAa,GAAG;AACrB,cAAI,eAAe,KAAK,KAAK,GAAG,GAAG;AACjC,iBAAK,MAAM;AAAA,UACb;AAAA,QACF,WAAW,KAAK,QAAQ,OAAO,CAAC,KAAK,OAAO,SAAS,IAAI,GAAG;AAC1D,eAAK,QAAQ,qBAAqB;AAClC,eAAK,aAAa,KAAK,GAAG;AAAA,QAC5B,OAAO;AACL,cAAI,qBAAqB,gBAAgB,OAAO,gBAAgB;AAChE,eAAK,oBAAoB,sBAAsB,YAAY;AAC3D,eAAK,aAAa,KAAK,GAAG;AAE1B,cAAI,CAAC,KAAK,QAAQ,mBAAmB,KAAK,KAAK,OAAO,KAAK,MAAM,QAAQ,0BAA0B;AACjG,iBAAK,QAAQ,aAAa;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,YAAY,KAAK,QAAQ,SAAS,GAAG;AAEzC,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,aAAa;AAAA;AAAA;;;ACliB5B;AAAA;AAAA;AA8BA,QAAI,aAAa,sBAAwB;AAAzC,QACE,UAAU,mBAAqB;AAEjC,aAAS,aAAa,aAAa,SAAS;AAC1C,UAAI,aAAa,IAAI,WAAW,aAAa,OAAO;AACpD,aAAO,WAAW,SAAS;AAAA,IAC7B;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,iBAAiB,WAAW;AACzC,aAAO,IAAI,QAAQ;AAAA,IACrB;AAAA;AAAA;;;ACzCA,IAAAC,mBAAA;AAAA;AAAA;AA8BA,QAAI,cAAc,kBAA2B;AAE7C,aAAS,QAAQ,SAAS;AACxB,kBAAY,KAAK,MAAM,SAAS,MAAM;AACtC,UAAI,KAAK,WAAW,WAAW,KAAK,KAAK,WAAW,CAAC,MAAM,QAAQ;AACjE,aAAK,aAAa,CAAC,UAAU,OAAO,cAAc,KAAK;AAAA,MACzD;AAEA,WAAK,oBAAoB,KAAK,aAAa,mBAAmB;AAC9D,WAAK,yBAAyB,KAAK,aAAa,0BAA0B,IAAI;AAC9E,WAAK,yBAAyB,KAAK,aAAa,0BAA0B,IAAI;AAE9E,WAAK,oBAAoB,KAAK,aAAa,qBAAqB,IAAI;AACpE,WAAK,kBAAkB,KAAK;AAAA,QAAe;AAAA,QACzC,CAAC,QAAQ,SAAS,iBAAiB,0BAA0B,oBAAoB,YAAY,kBAAkB;AAAA,MAAC;AAClH,WAAK,4BAA4B,KAAK,YAAY,6BAA6B,CAAC;AAChF,WAAK,8BAA8B,KAAK,YAAY,+BAA+B,KAAK,WAAW;AACnG,WAAK,eAAe,KAAK,WAAW,gBAAgB,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAM7E,WAAK,SAAS,KAAK,WAAW,UAAU;AAAA,QACtC;AAAA,QAAK;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAK;AAAA,QAAO;AAAA,QAAO;AAAA,QAAM;AAAA,QAAU;AAAA,QAAU;AAAA,QAC3E;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAO;AAAA,QAAO;AAAA,QAAM;AAAA,QAAS;AAAA,QAAK;AAAA,QAAU;AAAA,QACxE;AAAA,QAAS;AAAA,QAAO;AAAA,QAAO;AAAA,QAAU;AAAA,QAAS;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAS;AAAA,QAC1E;AAAA,QAAU;AAAA,QAAU;AAAA,QAAY;AAAA,QAAK;AAAA,QAAQ;AAAA,QAAK;AAAA;AAAA,QAAwB;AAAA,QAAU;AAAA,QACpF;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAY;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAK;AAAA,QAC5E;AAAA,QAAS;AAAA,QAAO;AAAA;AAAA,QAEhB;AAAA,QAAW;AAAA,QAAO;AAAA,QAAU;AAAA,MAC9B,CAAC;AACD,WAAK,yBAAyB,KAAK,aAAa,0BAA0B,IAAI;AAC9E,WAAK,gBAAgB,KAAK,WAAW,iBAAiB;AAAA;AAAA;AAAA,QAGpD;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAO;AAAA,QAAS;AAAA,QAAM;AAAA,QAAO;AAAA,QAAS;AAAA,QAC5D;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAU;AAAA,QAAS;AAAA;AAAA;AAAA;AAAA,QAKxD;AAAA,QAAY;AAAA;AAAA;AAAA;AAAA,QAKZ;AAAA,QAAY;AAAA,MACd,CAAC;AACD,WAAK,cAAc,KAAK,WAAW,eAAe,CAAC,CAAC;AACpD,WAAK,sBAAsB,KAAK,WAAW,uBAAuB;AAAA,QAChE;AAAA,QAAO;AAAA,MACT,CAAC;AACD,WAAK,gCAAgC,KAAK,gBAAgB,+BAA+B;AACzF,WAAK,iBAAiB,KAAK,eAAe,kBAAkB,CAAC,UAAU,QAAQ,UAAU,CAAC;AAAA,IAE5F;AACA,YAAQ,YAAY,IAAI,YAAY;AAIpC,WAAO,QAAQ,UAAU;AAAA;AAAA;;;AC5FzB,IAAAC,qBAAA;AAAA;AAAA;AA8BA,QAAI,gBAAgB,oBAA6B;AACjD,QAAI,YAAY,oBAA6B;AAC7C,QAAI,aAAa,qBAA8B;AAC/C,QAAI,qBAAqB,6BAAsC;AAC/D,QAAI,UAAU,kBAA2B;AAEzC,QAAI,QAAQ;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,UAAU;AAAA,MACjB,KAAK,UAAU;AAAA,MACf,KAAK,UAAU;AAAA,IACjB;AAEA,QAAI,kBAAkB,IAAI,WAAW,SAAS,KAAK;AAEnD,QAAI,YAAY,SAAS,cAAc,SAAS;AAC9C,oBAAc,KAAK,MAAM,cAAc,OAAO;AAC9C,WAAK,oBAAoB;AAIzB,UAAI,qBAAqB,IAAI,mBAAmB,KAAK,MAAM,EAAE,aAAa,KAAK,QAAQ;AACvF,UAAI,iBAAiB,IAAI,QAAQ,KAAK,MAAM;AAE5C,WAAK,aAAa;AAAA,QAChB,MAAM,mBAAmB,MAAM,YAAY;AAAA,QAC3C,kCAAkC,mBAAmB,MAAM,aAAa;AAAA,QACxE,cAAc,mBAAmB,YAAY,GAAG;AAAA,QAChD,cAAc,mBAAmB,YAAY,GAAG;AAAA,QAChD,WAAW,mBAAmB,MAAM,iBAAiB;AAAA,QACrD,cAAc,mBAAmB,MAAM,cAAc;AAAA,QAErD,4BAA4B,eAAe,SAAS,uBAAuB;AAAA,QAC3E,oBAAoB,eAAe,cAAc,OAAO,EAAE,YAAY,MAAM;AAAA,QAC5E,YAAY,eAAe,cAAc,IAAI,EAAE,YAAY,IAAI;AAAA,QAC/D,iBAAiB,eAAe,MAAM,YAAY;AAAA,QAClD,sBAAsB,eAAe,MAAM,IAAI;AAAA,QAC/C,SAAS,eAAe,cAAc,MAAM,EAAE,YAAY,KAAK;AAAA,QAC/D,OAAO,eAAe,cAAc,aAAa,EAAE,YAAY,KAAK;AAAA;AAAA,QAEpE,qBAAqB,eAAe,cAAc,MAAM,EAAE,YAAY,IAAI;AAAA,QAC1E,YAAY,eAAe,cAAc,KAAK,EAAE,YAAY,KAAK;AAAA,MACnE;AAEA,UAAI,KAAK,SAAS,mBAAmB;AACnC,aAAK,WAAW,OAAO,KAAK,WAAW,KAAK,QAAQ,YAAY;AAChE,aAAK,WAAW,mCAAmC,KAAK,WAAW,iCAAiC,QAAQ,YAAY;AAAA,MAC1H;AAEA,WAAK,iCAAiC;AAEtC,UAAI,KAAK,SAAS,+BAA+B;AAC/C,YAAI,iBAAiB,KAAK,OAAO,mBAAmB,KAAK,SAAS,6BAA6B;AAC/F,aAAK,WAAW,gCACd,eAAe,SAAS,cAAc,EACrC,YAAY,cAAc;AAAA,MAC/B;AAAA,IACF;AACA,cAAU,YAAY,IAAI,cAAc;AAExC,cAAU,UAAU,cAAc,SAAS,eAAe;AACxD,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe;AACxD,aAAO,cAAc,SAAS,MAAM,YAAY,cAAc,SAAS,MAAM;AAAA,IAC/E;AAEA,cAAU,UAAU,cAAc,SAAS,eAAe,YAAY;AACpE,aAAQ,cAAc,SAAS,MAAM,cAClC,gBACG,cAAc,SAAS,OAAO,cAAc,SAAS,SAAS,WAAW,KAAK,CAAC,MAAM,OACtF,cAAc,SAAS,QAAQ,WAAW,KAAK,CAAC,MAAM,OAAO,WAAW,KAAK,CAAC,MAAM,SACnF,cAAc,SAAS,MAAM,uBAChC,cAAc,SAAS,OAAO,WAAW,KAAK,SAAS,GAAG;AAAA,IAC/D;AAEA,cAAU,UAAU,SAAS,WAAW;AACtC,WAAK,oBAAoB;AAAA,IAC3B;AAEA,cAAU,UAAU,kBAAkB,SAAS,gBAAgB,YAAY;AACzE,UAAI,QAAQ;AACZ,WAAK,gBAAgB;AACrB,UAAI,IAAI,KAAK,OAAO,KAAK;AAEzB,UAAI,MAAM,MAAM;AACd,eAAO,KAAK,cAAc,MAAM,KAAK,EAAE;AAAA,MACzC;AAEA,cAAQ,SAAS,KAAK,sBAAsB,GAAG,UAAU;AACzD,cAAQ,SAAS,KAAK,gBAAgB,GAAG,gBAAgB,UAAU;AACnE,cAAQ,SAAS,KAAK,YAAY,GAAG,UAAU;AAC/C,cAAQ,SAAS,KAAK,uBAAuB,GAAG,cAAc;AAC9D,cAAQ,SAAS,KAAK,oBAAoB,GAAG,UAAU;AACvD,cAAQ,SAAS,KAAK,kBAAkB,GAAG,gBAAgB,UAAU;AACrE,cAAQ,SAAS,KAAK,mBAAmB,GAAG,UAAU;AACtD,cAAQ,SAAS,KAAK,uBAAuB,CAAC;AAC9C,cAAQ,SAAS,KAAK,iBAAiB,CAAC;AACxC,cAAQ,SAAS,KAAK,WAAW,GAAG,UAAU;AAC9C,cAAQ,SAAS,KAAK,cAAc,MAAM,SAAS,KAAK,OAAO,KAAK,CAAC;AAErE,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,yBAAyB,SAAS,GAAG;AACvD,UAAI,QAAQ;AACZ,UAAI,mBAAmB;AACvB,UAAI,aAAa;AAEjB,UAAI,MAAM,KAAK;AACb,YAAI,QAAQ,KAAK,OAAO,KAAK,CAAC;AAG9B,YAAI,UAAU,KAAK;AACjB,6BAAmB,KAAK,WAAW,QAAQ,KAAK;AAGhD,cAAI,kBAAkB;AACpB,yBAAa,gBAAgB,eAAe,gBAAgB;AAC5D,gBAAI,cAAc,WAAW,WAAW,SAAS;AAC/C,kCAAoB,gBAAgB,YAAY,KAAK,MAAM;AAAA,YAC7D;AAAA,UACF,OAAO;AACL,+BAAmB,KAAK,WAAW,MAAM,KAAK;AAAA,UAChD;AAAA,QACF;AAEA,YAAI,kBAAkB;AACpB,kBAAQ,KAAK,cAAc,MAAM,SAAS,gBAAgB;AAC1D,gBAAM,aAAa;AAAA,QACrB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,mBAAmB,SAAS,GAAG;AACjD,UAAI,QAAQ;AACZ,UAAI,mBAAmB;AACvB,UAAI,aAAa;AAEjB,UAAI,MAAM,KAAK;AACb,YAAI,QAAQ,KAAK,OAAO,KAAK,CAAC;AAC9B,YAAI,UAAU,OAAO,UAAU,KAAK;AAClC,6BAAmB,KAAK,WAAW,oBAAoB,KAAK;AAC5D,6BAAmB,oBAAoB,KAAK,WAAW,WAAW,KAAK;AAAA,QACzE;AAEA,YAAI,kBAAkB;AACpB,kBAAQ,KAAK,cAAc,MAAM,SAAS,gBAAgB;AAC1D,gBAAM,aAAa;AAAA,QACrB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,aAAa,SAAS,GAAG,YAAY;AACvD,UAAI,mBAAmB;AACvB,UAAI,QAAQ;AACZ,UAAI,CAAC,cAAc,WAAW,SAAS,MAAM,mBAAmB;AAC9D,YAAI,MAAM,KAAK;AAEb,6BAAmB,KAAK,OAAO,KAAK;AACpC,cAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B,gCAAoB,KAAK,OAAO,KAAK;AAAA,UACvC;AACA,8BAAoB,KAAK,WAAW,aAAa,KAAK;AACtD,kBAAQ,KAAK,cAAc,MAAM,UAAU,gBAAgB;AAAA,QAC7D;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,wBAAwB,SAAS,GAAG,YAAY;AAClE,UAAI,mBAAmB;AACvB,UAAI,QAAQ;AACZ,UAAI,CAAC,cAAc,WAAW,SAAS,MAAM,mBAAmB;AAC9D,aAAK,KAAK,SAAS,WAAW,SAAS,SAAS,KAAK,KAAK,SAAS,sBAAsB,MAAM,OAAO,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AACjI,cAAI,KAAK,SAAS,qBAAqB,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AAClE,+BAAmB,KAAK,WAAW,mBAAmB,KAAK;AAC3D,+BAAmB,oBAAoB,KAAK,WAAW,WAAW,KAAK;AACvE,oBAAQ,KAAK,cAAc,MAAM,SAAS,gBAAgB;AAAA,UAC5D,OAAO;AACL,+BAAmB,KAAK,WAAW,gBAAgB,KAAK;AACxD,oBAAQ,KAAK,cAAc,MAAM,UAAU,gBAAgB;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,sBAAsB,SAAS,GAAG,YAAY;AAChE,UAAI,mBAAmB;AACvB,UAAI,QAAQ;AAEZ,UAAI,CAAC,KAAK,SAAS,WAAW,SAAS,SAAS,GAAG;AACjD,eAAO;AAAA,MACT;AAEA,UAAI,MAAM,KAAK;AACb,2BAAmB,KAAK,WAAW,2BAA2B,KAAK;AACnE,YAAI,qBAAqB,IAAI;AAC3B,iBAAO;AAAA,QACT;AAEA,YAAI,4BAA4B,iBAAiB,SAAS,GAAG,IAAI,IAAI;AACrE,YAAI,4BAA4B;AAGhC,eAAO,EAAE,iBAAiB,SAAS,GAAG,KAAK,8BAA8B,4BAA4B;AACnG,cAAI,YAAY,KAAK,OAAO,KAAK;AACjC,cAAI,cAAc,MAAM;AACtB;AAAA,UACF,WAAW,cAAc,KAAK;AAC5B;AAAA,UACF,WAAW,cAAc,KAAK;AAC5B;AAAA,UACF;AACA,8BAAoB;AAAA,QACtB;AACA,gBAAQ,KAAK,cAAc,MAAM,mBAAmB,gBAAgB;AAAA,MACtE,WAAW,MAAM,OAAO,cAAc,WAAW,SAAS,MAAM,mBAAmB;AACjF,2BAAmB,KAAK,OAAO,KAAK;AACpC,gBAAQ,KAAK,cAAc,MAAM,oBAAoB,gBAAgB;AAAA,MACvE;AACA,aAAO;AAAA,IACT;AAGA,cAAU,UAAU,cAAc,SAAS,GAAG,YAAY;AACxD,UAAI,mBAAmB;AACvB,UAAI,QAAQ;AACZ,UAAI,cAAc,WAAW,SAAS,MAAM,UAAU;AACpD,YAAI,WAAW,KAAK,CAAC,MAAM,QAAQ,MAAM,OAAQ,MAAM,OAAO,KAAK,OAAO,KAAK,CAAC,MAAM,MAAO;AAC3F,6BAAmB,KAAK,OAAO,KAAK;AACpC,cAAI,MAAM,KAAK;AACb,gCAAoB,KAAK,OAAO,KAAK;AAAA,UACvC;AACA,kBAAQ,KAAK,cAAc,MAAM,WAAW,gBAAgB;AAAA,QAC9D,WAAW,WAAW,KAAK,CAAC,MAAM,OAAO,MAAM,OAAO,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK;AACjF,eAAK,OAAO,KAAK;AACjB,eAAK,OAAO,KAAK;AACjB,kBAAQ,KAAK,cAAc,MAAM,WAAW,IAAI;AAAA,QAClD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,kBAAkB,SAAS,GAAG,gBAAgB,YAAY;AAC5E,UAAI,QAAQ;AACZ,UAAI,mBAAmB;AACvB,UAAI,cAAc,WAAW,KAAK,CAAC,MAAM,KAAK;AAE5C,YAAI,MAAM,KAAK;AACb,kBAAQ,KAAK,cAAc,MAAM,QAAQ,KAAK,OAAO,KAAK,CAAC;AAAA,QAC7D,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,cAAI,UAAU,KAAK,OAAO,KAAK;AAC/B,cAAI,MAAM,KAAK;AACb,uBAAW,KAAK,WAAW,aAAa,KAAK;AAAA,UAC/C,OAAO;AACL,uBAAW,KAAK,WAAW,aAAa,KAAK;AAAA,UAC/C;AACA,kBAAQ,KAAK,cAAc,MAAM,OAAO,OAAO;AAAA,QACjD,OAAO;AACL,6BAAmB,KAAK,WAAW,UAAU,KAAK;AAElD,cAAI,kBAAkB;AACpB,gBAAI,eAAe,SAAS,MAAM,QAAQ;AACxC,sBAAQ,KAAK,cAAc,MAAM,OAAO,gBAAgB;AAAA,YAC1D,OAAO;AACL,sBAAQ,KAAK,cAAc,MAAM,WAAW,gBAAgB;AAAA,YAC9D;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,0BAA0B,SAAS,UAAU;AAI/D,aAAO,KAAK,SAAS,cAAc,QAAQ,QAAQ,MAAM,OACtD,KAAK,SAAS,oBAAoB,QAAQ,QAAQ,MAAM,MACvD,KAAK,SAAS,YAAY,QAAQ,QAAQ,MAAM;AAAA,IACtD;AAEA,cAAU,UAAU,oBAAoB,SAAS,GAAG,gBAAgB,YAAY;AAC9E,UAAI,mBAAmB;AACvB,UAAI,cAAc,WAAW,KAAK,CAAC,MAAM,KAAK;AAC5C,2BAAmB,KAAK,WAAW,qBAAqB,KAAK;AAAA,MAC/D,WAAW,eAAe,SAAS,MAAM,aACvC,eAAe,OAAO,KAAK,CAAC,MAAM,OAAO,eAAe,KAAK,CAAC,MAAM,KAAK;AAEzE,YAAI,WAAW,eAAe,OAAO,KAAK,OAAO,CAAC,EAAE,YAAY;AAChE,YAAI,KAAK,wBAAwB,QAAQ,GAAG;AAE1C,6BAAmB,KAAK,OAAO,UAAU,IAAI,OAAO,OAAO,WAAW,mBAAmB,IAAI,CAAC;AAAA,QAChG;AAAA,MACF;AAEA,UAAI,kBAAkB;AACpB,eAAO,KAAK,cAAc,MAAM,MAAM,gBAAgB;AAAA,MACxD;AAEA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,yBAAyB,SAAS,GAAG,gBAAgB;AACvE,UAAI,eAAe,SAAS,MAAM,aAAa,eAAe,OAAO,KAAK,CAAC,MAAM,OAAO,eAAe,KAAK,CAAC,MAAM,KAAK;AACtH,YAAI,WAAW,eAAe,OAAO,KAAK,OAAO,CAAC,EAAE,YAAY;AAChE,YAAI,aAAa,YAAY,aAAa,SAAS;AAGjD,cAAI,QAAQ,KAAK,uBAAuB,CAAC;AACzC,cAAI,OAAO;AACT,kBAAM,OAAO,MAAM;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,mBAAmB,KAAK,OAAO,UAAU,IAAI,OAAO,OAAO,WAAW,mBAAmB,IAAI,CAAC;AAClG,cAAI,kBAAkB;AACpB,mBAAO,KAAK,cAAc,MAAM,MAAM,gBAAgB;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,qBAAqB,SAAS,GAAG,YAAY;AAC/D,UAAI,mBAAmB;AACvB,UAAI,KAAK,SAAS,+BAA+B;AAC/C,YAAI,MAAM,KAAK,SAAS,8BAA8B,CAAC,GAAG;AACxD,6BAAmB,KAAK,WAAW,8BAA8B,KAAK;AAAA,QACxE;AAAA,MACF;AAEA,UAAI,CAAC,kBAAkB;AACrB,2BAAoB,cAAc,WAAW,SAAS,MAAM,oBAAqB,KAAK,WAAW,iCAAiC,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK;AAAA,MACvK;AACA,UAAI,kBAAkB;AACpB,eAAO,KAAK,cAAc,MAAM,MAAM,gBAAgB;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,YAAY;AAC3B,WAAO,QAAQ,QAAQ;AAAA;AAAA;;;ACpYvB,IAAAC,sBAAA;AAAA;AAAA;AA8BA,QAAI,UAAU,mBAA2B;AACzC,QAAI,SAAS,iBAA0B;AACvC,QAAI,YAAY,qBAA6B;AAC7C,QAAI,QAAQ,qBAA6B;AAEzC,QAAI,YAAY;AAChB,QAAI,gBAAgB;AAEpB,QAAI,UAAU,SAAS,SAAS,oBAAoB;AAElD,WAAK,eAAe;AACpB,WAAK,iBAAiB;AACtB,WAAK,wBAAwB,QAAQ;AACrC,WAAK,oBAAoB,QAAQ;AAEjC,WAAK,UAAU,IAAI,OAAO,SAAS,kBAAkB;AAAA,IAEvD;AAEA,YAAQ,UAAU,yBAAyB,SAAS,SAAS;AAC3D,aAAO,KAAK,QAAQ,aAAa,UAAU,OAAO;AAAA,IACpD;AAEA,YAAQ,UAAU,yBAAyB,SAAS,OAAO,cAAc;AACvE,WAAK,QAAQ,qBAAqB;AAClC,WAAK,QAAQ,qBAAqB;AAAA,IACpC;AAEA,YAAQ,UAAU,iBAAiB,WAAW;AAC5C,WAAK,QAAQ,WAAW,KAAK,cAAc,KAAK,cAAc;AAC9D,WAAK,QAAQ,eAAe;AAAA,IAC9B;AAGA,YAAQ,UAAU,gBAAgB,SAAS,OAAO;AAChD,WAAK,QAAQ,cAAc,KAAK;AAAA,IAClC;AAEA,YAAQ,UAAU,2BAA2B,SAAS,WAAW;AAC/D,UAAI,WAAW;AACf,UAAI,UAAU,SAAS,MAAM,QAAQ,UAAU,SAAS,SAAS,MAAM,MAAM;AAC3E,mBAAW,UAAU,WAAW,IAAI;AAAA,MACtC;AAEA,UAAI,KAAK,mBAAmB;AAC1B,mBAAW,UAAU,WAAW,KAAK,wBAAwB,IAAI,UAAU,WAAW,KAAK,wBAAwB;AAAA,MACrH;AACA,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,aAAK,cAAc,IAAI,CAAC;AAAA,MAC1B;AAEA,aAAO,aAAa;AAAA,IACtB;AAEA,YAAQ,UAAU,sBAAsB,SAAS,WAAW;AAC1D,UAAI,UAAU,qBAAqB,UAAU,UAAU;AACrD,YAAI,CAAC,KAAK,yBAAyB,SAAS,GAAG;AAC7C,eAAK,QAAQ,qBAAqB;AAAA,QACpC;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,yBAAyB,WAAW;AACpD,aAAO,KAAK,QAAQ;AAAA,IACtB;AAEA,YAAQ,UAAU,gBAAgB,SAAS,OAAO;AAChD,WAAK,QAAQ,aAAa,KAAK;AAAA,IACjC;AAEA,YAAQ,UAAU,cAAc,SAAS,OAAO;AAC9C,UAAI,MAAM,MAAM;AACd,aAAK,QAAQ,WAAW,KAAK,cAAc,KAAK,cAAc;AAC9D,aAAK,QAAQ,UAAU,MAAM,IAAI;AAAA,MACnC;AAAA,IACF;AAEA,YAAQ,UAAU,SAAS,WAAW;AACpC,WAAK;AAAA,IACP;AAEA,YAAQ,UAAU,WAAW,WAAW;AACtC,UAAI,KAAK,eAAe,GAAG;AACzB,aAAK;AACL,aAAK,QAAQ,WAAW,KAAK,cAAc,KAAK,cAAc;AAAA,MAChE;AAAA,IACF;AAEA,YAAQ,UAAU,kBAAkB,SAAS,OAAO;AAClD,cAAQ,KAAK,gBAAgB,SAAS;AACtC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,QAAQ,kBAAkB,KAAK;AAAA,IAC7C;AAEA,QAAI,qBAAqB,SAAS,aAAa;AAC7C,UAAI,SAAS;AACb,UAAI,YAAY,YAAY;AAG5B,aAAO,UAAU,SAAS,MAAM,OAAO,YAAY,WAAW,WAAW;AACvE,YAAI,UAAU,SAAS,MAAM,aAAa,UAAU,SAAS,QAAQ;AACnE,cAAI,UAAU,QAAQ,UAAU,KAAK,SAAS,MAAM,UAClD,UAAU,KAAK,QAAQ,UAAU,KAAK,KAAK,SAAS,MAAM,OAAO;AACjE,qBAAS,UAAU,KAAK,KAAK;AAAA,UAC/B;AACA;AAAA,QACF;AACA,oBAAY,UAAU;AAAA,MACxB;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,6BAA6B,SAAS,WAAW,WAAW;AAC9D,UAAI,gBAAgB;AACpB,UAAI,SAAS;AAEb,UAAI,CAAC,UAAU,QAAQ;AACrB,eAAO;AAAA,MACT;AAEA,UAAI,cAAc,UAAU;AAC1B,wBAAgB;AAAA,MAClB,WAAW,cAAc,SAAS;AAChC,wBAAgB;AAAA,MAClB;AAEA,sBAAgB,mBAAmB,SAAS,KAAK;AAIjD,UAAI,cAAc,OAAO,UAAU,IAAI,IAAI;AACzC,iBAAS;AAAA,MACX,WAAW,cAAc,OAAO,6GAA6G,IAAI,IAAI;AACnJ,iBAAS;AAAA,MACX,WAAW,cAAc,OAAO,sCAAsC,IAAI,IAAI;AAC5E,iBAAS;AAAA,MACX,WAAW,cAAc,OAAO,YAAY,IAAI,IAAI;AAElD,iBAAS;AAAA,MACX;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,SAAS,MAAM,KAAK;AAC3B,aAAO,IAAI,QAAQ,IAAI,MAAM;AAAA,IAC/B;AAEA,aAAS,SAAS,QAAQ,cAAc,cAAc;AACpD,WAAK,SAAS,UAAU;AACxB,WAAK,MAAM,eAAe,aAAa,WAAW;AAClD,WAAK,eAAe,gBAAgB;AACpC,WAAK,eAAe,gBAAgB;AAAA,IACtC;AAEA,aAAS,SAAS,SAAS;AACzB,WAAK,WAAW;AAChB,WAAK,iBAAiB;AAAA,IACxB;AAEA,aAAS,UAAU,mBAAmB,WAAW;AAC/C,aAAO,KAAK,iBAAiB,KAAK,eAAe,eAAe;AAAA,IAClE;AAEA,aAAS,UAAU,aAAa,SAAS,cAAc;AACrD,UAAI,YAAY,IAAI,SAAS,KAAK,gBAAgB,cAAc,KAAK,SAAS,YAAY;AAC1F,WAAK,iBAAiB;AAAA,IACxB;AAEA,aAAS,UAAU,iBAAiB,SAAS,OAAO;AAClD,UAAI,eAAe;AAEnB,UAAI,OAAO;AACT,uBAAe,MAAM;AACrB,aAAK,SAAS,eAAe,MAAM;AACnC,aAAK,iBAAiB,MAAM;AAAA,MAC9B;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,aAAa,SAAS,UAAU,WAAW;AAC5D,UAAI,QAAQ,KAAK;AAEjB,aAAO,OAAO;AACZ,YAAI,SAAS,QAAQ,MAAM,GAAG,MAAM,IAAI;AACtC;AAAA,QACF,WAAW,aAAa,UAAU,QAAQ,MAAM,GAAG,MAAM,IAAI;AAC3D,kBAAQ;AACR;AAAA,QACF;AACA,gBAAQ,MAAM;AAAA,MAChB;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,UAAU,SAAS,KAAK,WAAW;AACpD,UAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,GAAG,SAAS;AAC5C,aAAO,KAAK,eAAe,KAAK;AAAA,IAClC;AAEA,aAAS,UAAU,gBAAgB,SAAS,UAAU;AACpD,UAAI,QAAQ,KAAK,WAAW,QAAQ;AACpC,UAAI,OAAO;AACT,aAAK,SAAS,eAAe,MAAM;AAAA,MACrC;AAAA,IACF;AAEA,aAAS,WAAW,aAAa,SAAS,aAAa,cAAc;AAEnE,WAAK,eAAe,eAAe;AACnC,gBAAU,WAAW,CAAC;AACtB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAIlB,UAAI,aAAa,IAAI,QAAQ,SAAS,MAAM;AAE5C,WAAK,WAAW;AAEhB,WAAK,4BAA4B,KAAK,SAAS,gBAAgB,OAAO,GAAG,QAAQ,MAAM,MAAM;AAC7F,WAAK,6CAA8C,KAAK,SAAS,oBAAoB;AACrF,WAAK,oCAAqC,KAAK,SAAS,oBAAoB;AAC5E,WAAK,uCAAwC,KAAK,SAAS,oBAAoB;AAC/E,WAAK,+BAA+B,KAAK,SAAS,gBAAgB,OAAO,GAAG,WAAW,MAAM,MAAM;AACnG,WAAK,uCAAwC,KAAK,SAAS,oBAAoB;AAAA,IACjF;AAEA,eAAW,UAAU,WAAW,WAAW;AAGzC,UAAI,KAAK,SAAS,UAAU;AAC1B,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,cAAc,KAAK;AACvB,UAAI,MAAM,KAAK,SAAS;AACxB,UAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,cAAM;AACN,YAAI,eAAe,UAAU,KAAK,WAAW,GAAG;AAC9C,gBAAM,YAAY,MAAM,SAAS,EAAE,CAAC;AAAA,QACtC;AAAA,MACF;AAGA,oBAAc,YAAY,QAAQ,eAAe,IAAI;AAErD,UAAI,mBAAmB,YAAY,MAAM,SAAS,EAAE,CAAC;AAErD,UAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAEA,UAAI,iBAAiB,IAAI,mBAAmB,KAAK,QAAQ;AAEzD,UAAI,UAAU,IAAI,QAAQ,KAAK,UAAU,gBAAgB;AACzD,UAAI,SAAS,IAAI,UAAU,aAAa,KAAK,QAAQ,EAAE,SAAS;AAEhE,WAAK,aAAa,IAAI,SAAS,OAAO;AAEtC,UAAI,eAAe;AACnB,UAAI,YAAY,OAAO,KAAK;AAC5B,aAAO,UAAU,SAAS,MAAM,KAAK;AAEnC,YAAI,UAAU,SAAS,MAAM,YAAY,UAAU,SAAS,MAAM,SAAS;AACzE,yBAAe,KAAK,iBAAiB,SAAS,WAAW,gBAAgB,YAAY,MAAM;AAC3F,2BAAiB;AAAA,QACnB,WAAY,UAAU,SAAS,MAAM,aAAa,UAAU,SAAS,MAAM,UAAU,UAAU,SAAS,MAAM,SAC3G,UAAU,SAAS,MAAM,QAAQ,CAAC,eAAe,cAAe;AACjE,yBAAe,KAAK,mBAAmB,SAAS,WAAW,gBAAgB,UAAU;AAAA,QACvF,WAAW,UAAU,SAAS,MAAM,WAAW;AAC7C,yBAAe,KAAK,kBAAkB,SAAS,WAAW,cAAc;AAAA,QAC1E,WAAW,UAAU,SAAS,MAAM,MAAM;AACxC,yBAAe,KAAK,aAAa,SAAS,WAAW,cAAc;AAAA,QACrE,WAAW,UAAU,SAAS,MAAM,mBAAmB;AACrD,yBAAe,KAAK,0BAA0B,SAAS,SAAS;AAAA,QAClE,WAAW,UAAU,SAAS,MAAM,oBAAoB;AACtD,yBAAe,KAAK,2BAA2B,SAAS,SAAS;AAAA,QACnE,OAAO;AAEL,kBAAQ,cAAc,SAAS;AAAA,QACjC;AAEA,qBAAa;AAEb,oBAAY,OAAO,KAAK;AAAA,MAC1B;AACA,UAAI,aAAa,QAAQ,QAAQ,SAAS,GAAG;AAE7C,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,4BAA4B,SAAS,SAAS,WAAW;AAC5E,UAAI,eAAe;AAAA,QACjB,MAAM,UAAU;AAAA,QAChB,MAAM,UAAU;AAAA,MAClB;AACA,cAAQ,uBAAuB,UAAU,YAAY,UAAU,sBAAsB,IAAI,IAAI;AAC7F,UAAI,UAAU,UAAU;AACtB,gBAAQ,yBAAyB,SAAS;AAAA,MAC5C,OAAO;AACL,gBAAQ,uBAAuB,UAAU,YAAY,UAAU,sBAAsB,IAAI,IAAI;AAAA,MAC/F;AACA,cAAQ,YAAY,SAAS;AAC7B,cAAQ,OAAO;AACf,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,6BAA6B,SAAS,SAAS,WAAW;AAC7E,UAAI,eAAe;AAAA,QACjB,MAAM,UAAU;AAAA,QAChB,MAAM,UAAU;AAAA,MAClB;AAEA,cAAQ,SAAS;AACjB,UAAI,UAAU,UAAU;AACtB,gBAAQ,yBAAyB,SAAS;AAAA,MAC5C,OAAO;AACL,gBAAQ,uBAAuB,UAAU,YAAY,UAAU,sBAAsB,IAAI,IAAI;AAAA,MAC/F;AACA,cAAQ,YAAY,SAAS;AAC7B,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,oBAAoB,SAAS,SAAS,WAAW,gBAAgB;AACpF,UAAI,eAAe;AAAA,QACjB,MAAM,UAAU;AAAA,QAChB,MAAM,UAAU;AAAA,MAClB;AACA,cAAQ,iBAAiB;AACzB,qBAAe,eAAe;AAE9B,cAAQ,uBAAuB,UAAU,YAAY,UAAU,sBAAsB,IAAI,IAAI;AAC7F,UAAI,eAAe,gBAAgB;AACjC,gBAAQ,cAAc,SAAS;AAAA,MACjC,OAAO;AACL,YAAI,eAAe,mBAAmB,KAAK;AACzC,kBAAQ,uBAAuB,UAAU,KAAK,CAAC,MAAM,KAAK,IAAI;AAC9D,cAAI,KAAK,8CAA8C,eAAe,mBAAmB;AACvF,oBAAQ,cAAc,KAAK;AAAA,UAC7B;AAAA,QACF;AACA,gBAAQ,YAAY,SAAS;AAAA,MAE/B;AAEA,UAAI,eAAe,kBACjB,EAAE,eAAe,kBAAkB,eAAe,yBAAyB;AAC3E,gBAAQ,OAAO;AAGf,uBAAe,iBAAiB;AAAA,MAClC;AAEA,UAAI,CAAC,eAAe,qBAClB,EAAE,eAAe,kBAAkB,eAAe,yBAAyB;AAC3E,gBAAQ,eAAe;AAAA,MACzB;AAEA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,qBAAqB,SAAS,SAAS,WAAW,gBAAgB,YAAY;AACjG,UAAI,UAAU,eAAe;AAC7B,UAAI,eAAe;AAAA,QACjB,MAAM,UAAU;AAAA,QAChB,MAAM,UAAU;AAAA,MAClB;AAEA,cAAQ,uBAAuB,UAAU,YAAY,UAAU,sBAAsB,IAAI,IAAI;AAC7F,UAAI,eAAe,gBAAgB;AACjC,gBAAQ,cAAc,SAAS;AAAA,MACjC,WAAW,eAAe,mBAAmB,OAAO,UAAU,SAAS,MAAM,MAAM;AAEjF,YAAI,QAAQ,yBAAyB,SAAS,GAAG;AAC/C,oBAAU,WAAW;AACrB,kBAAQ,cAAc,SAAS;AAAA,QACjC,OAAO;AACL,kBAAQ,YAAY,SAAS;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,YAAI,UAAU,SAAS,MAAM,WAAW;AACtC,kBAAQ,uBAAuB,IAAI;AAAA,QACrC,WAAW,UAAU,SAAS,MAAM,QAAQ;AAC1C,kBAAQ,uBAAuB,KAAK;AAAA,QACtC,WAAW,UAAU,SAAS,MAAM,SAAS,UAAU,SAAS,SAAS,MAAM,QAAQ;AACrF,kBAAQ,uBAAuB,KAAK;AAAA,QACtC;AAEA,YAAI,UAAU,SAAS,MAAM,aAAa,eAAe,mBAAmB,KAAK;AAC/E,cAAI,KAAK,gCAAgC,KAAK,sCAAsC;AAClF,oBAAQ,oBAAoB,SAAS;AACrC,sBAAU,WAAW,UAAU,aAAa;AAAA,UAC9C;AAKA,cAAI,KAAK,6BACP,eAAe,cAAc,KAAK,SAAS,8BAC1C,WAAW,SAAS,MAAM;AAAA,UACzB,KAAK,6CAA6C;AACpD,oBAAQ,cAAc,KAAK;AAC3B,sBAAU;AAAA,UACZ;AAAA,QACF;AACA,gBAAQ,YAAY,SAAS;AAC7B,kBAAU,WAAW,QAAQ,uBAAuB;AACpD,uBAAe,oBAAoB;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,eAAe,SAAS,SAAS,WAAW,gBAAgB;AAC/E,UAAI,eAAe;AAAA,QACjB,MAAM,UAAU;AAAA,QAChB,MAAM;AAAA,MACR;AACA,UAAI,eAAe,wBAAwB;AACzC,aAAK,6BAA6B,SAAS,WAAW,cAAc;AAAA,MACtE,WAAW,eAAe,kBAAkB,eAAe,wBAAwB;AACjF,gBAAQ,cAAc,SAAS;AAAA,MACjC,OAAO;AACL,gBAAQ,oBAAoB,SAAS;AACrC,gBAAQ,YAAY,SAAS;AAAA,MAC/B;AACA,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,+BAA+B,SAAS,SAAS,WAAW,gBAAgB;AAC/F,UAAI,QAAQ;AACZ,UAAI,UAAU,SAAS,IAAI;AAEzB,YAAI,OAAO,UAAU,MACnB,aACA,sBAAsB,GACtB,MAAM,IACN,OAAO;AACT,YAAI,eAAe,2BAA2B,gBAAgB,OAAO,KAAK,iBAAiB,YAAY;AACrG,wBAAc,KAAK;AAAA,QACrB,WAAW,eAAe,2BAA2B,SAAS,OAAO,KAAK,kBAAkB,YAAY;AACtG,wBAAc,KAAK;AAAA,QACrB,WAAW,eAAe,2BAA2B,QAAQ;AAC3D,wBAAc,SAAS,aAAa,SAAS;AAC3C,gBAAI,aAAa,IAAI,WAAW,aAAa,SAAS,MAAM,cAAc,MAAM,aAAa;AAC7F,mBAAO,WAAW,SAAS;AAAA,UAC7B;AAAA,QACF;AAEA,YAAI,KAAK,SAAS,mBAAmB,QAAQ;AAC3C,gCAAsB;AAAA,QACxB,WAAW,KAAK,SAAS,mBAAmB,YAAY;AACtD,gCAAsB,CAAC,QAAQ;AAAA,QACjC;AAEA,YAAI,cAAc,QAAQ,gBAAgB,mBAAmB;AAI7D,eAAO,KAAK,QAAQ,aAAa,EAAE;AAGnC,YAAI,eAAe,2BAA2B,UAC5C,KAAK,CAAC,MAAM,OAAO,KAAK,MAAM,qBAAqB,GAAG;AACtD,cAAI,UAAU,8DAA8D,KAAK,IAAI;AAGrF,cAAI,CAAC,SAAS;AACZ,oBAAQ,cAAc,SAAS;AAC/B;AAAA,UACF;AAEA,gBAAM,cAAc,QAAQ,CAAC,IAAI;AACjC,iBAAO,QAAQ,CAAC;AAChB,cAAI,QAAQ,CAAC,GAAG;AACd,mBAAO,cAAc,QAAQ,CAAC;AAAA,UAChC;AAIA,iBAAO,KAAK,QAAQ,aAAa,EAAE;AAEnC,cAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAGjD,sBAAU,QAAQ,CAAC,EAAE,MAAM,SAAS;AACpC,gBAAI,SAAS;AACX,wBAAU,oBAAoB,QAAQ,CAAC;AAAA,YACzC;AAAA,UACF;AAAA,QACF;AAEA,YAAI,MAAM;AACR,cAAI,aAAa;AAGf,gBAAI,gBAAgB,WAAW;AAC7B,mBAAK,MAAM;AAAA,YACb;AACA,0BAAc,YAAY,KAAK,SAAS;AACxC,gBAAI,gBAAgB,IAAI,cAAc;AACtC,mBAAO,YAAY,cAAc,MAAM,aAAa;AAAA,UACtD,OAAO;AAEL,gBAAI,QAAQ,UAAU;AACtB,gBAAI,OAAO;AACT,qBAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,QAAQ,MAAM,GAAG,GAAG,IAAI;AAAA,YACjE;AAEA,mBAAO,cAAc,KAAK,QAAQ,OAAO,OAAO,WAAW;AAAA,UAC7D;AAAA,QACF;AAEA,YAAI,KAAK;AACP,cAAI,CAAC,MAAM;AACT,mBAAO,MAAM;AAAA,UACf,OAAO;AACL,mBAAO,MAAM,OAAO,OAAO;AAAA,UAC7B;AAAA,QACF;AAEA,gBAAQ,cAAc,KAAK;AAC3B,YAAI,MAAM;AACR,oBAAU,OAAO;AACjB,oBAAU,oBAAoB;AAC9B,oBAAU,WAAW;AACrB,kBAAQ,cAAc,SAAS;AAC/B,kBAAQ,cAAc,IAAI;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAEA,eAAW,UAAU,mBAAmB,SAAS,SAAS,WAAW,gBAAgB,YAAY,QAAQ;AACvG,UAAI,eAAe,KAAK,oBAAoB,SAAS;AAErD,WAAK,eAAe,kBAAkB,eAAe,2BACnD,CAAC,eAAe,oBAChB,UAAU,SAAS,MAAM,YAAY,CAAC,aAAa,cAAc;AAGjE,gBAAQ,cAAc,SAAS;AAC/B,qBAAa,kBAAkB,KAAK,WAAW,QAAQ,aAAa,QAAQ;AAAA,MAC9E,OAAO;AACL,gBAAQ,oBAAoB,SAAS;AACrC,aAAK,kBAAkB,SAAS,WAAW,cAAc,gBAAgB,UAAU;AACnF,YAAI,CAAC,aAAa,mBAAmB;AACnC,kBAAQ,eAAe;AAAA,QACzB;AACA,gBAAQ,YAAY,SAAS;AAAA,MAC/B;AAGA,UAAI,aAAa,gBAAgB,KAAK,2BAA2B;AAC/D,YAAI,aAAa;AACjB,YAAI;AACJ,WAAG;AACD,uBAAa,OAAO,KAAK,UAAU;AACnC,cAAI,WAAW,SAAS,MAAM,WAAW;AACvC,yBAAa,cAAc;AAAA,UAC7B;AACA,wBAAc;AAAA,QAChB,SAAS,WAAW,SAAS,MAAM,OAAO,WAAW,SAAS,MAAM;AAAA,MACtE;AAGA,UAAI,KAAK,qCAAqC,KAAK,wCAAwC,KAAK,sCAAsC;AACpI,qBAAa,iBAAiB,UAAU,KAAK,SAAS;AAAA,MACxD;AAEA,UAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,gBAAgB;AAC9D,gBAAQ,iBAAiB,aAAa;AAAA,MACxC;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,qBAAqB,SAAS,SAAS,QAAQ,WAAW;AAC5D,WAAK,SAAS,UAAU;AACxB,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;AACtB,WAAK,yBAAyB;AAC9B,WAAK,mBAAmB;AACxB,WAAK,eAAe;AACpB,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,WAAK,oBAAoB;AACzB,WAAK,yBAAyB;AAC9B,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAClB,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AACtB,WAAK,YAAY;AAEjB,UAAI,CAAC,WAAW;AACd,aAAK,eAAe;AAAA,MACtB,OAAO;AACL,YAAI;AAEJ,aAAK,iBAAiB,UAAU,KAAK,CAAC;AACtC,aAAK,OAAO,UAAU;AAEtB,YAAI,KAAK,mBAAmB,KAAK;AAC/B,4BAAkB,UAAU,KAAK,MAAM,aAAa;AACpD,eAAK,YAAY,kBAAkB,gBAAgB,CAAC,IAAI;AAAA,QAC1D,OAAO;AACL,4BAAkB,UAAU,KAAK,MAAM,8BAA8B;AACrE,eAAK,YAAY,kBAAkB,gBAAgB,CAAC,IAAI;AAGxD,eAAK,UAAU,KAAK,WAAW,MAAM,KAAK,UAAU,KAAK,WAAW,OAAO,MAAM,KAAK,UAAU,CAAC,MAAM,KAAK;AAC1G,gBAAI,KAAK,cAAc,OAAO,UAAU,SAAS,MAAM;AACrD,mBAAK,YAAY,UAAU,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,YACnD,OAAO;AACL,mBAAK,YAAY,UAAU,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAEA,aAAK,YAAY,KAAK,UAAU,YAAY;AAE5C,YAAI,UAAU,SAAS,MAAM,SAAS;AACpC,eAAK,eAAe;AAAA,QACtB;AAEA,aAAK,eAAe,KAAK,UAAU,OAAO,CAAC,MAAM;AACjD,aAAK,WAAW,CAAC,KAAK,eAAe,KAAK,UAAU,OAAO,CAAC,IAAI,KAAK;AACrE,aAAK,aAAa,CAAC,KAAK,gBACrB,UAAU,UAAU,UAAU,OAAO,SAAS;AAGjD,YAAI,mBAAmB;AACvB,YAAI,KAAK,mBAAmB,OAAO,KAAK,KAAK,UAAU,GAAG;AACxD,cAAI,KAAK,KAAK,OAAO,CAAC,MAAM,KAAK;AAC/B,+BAAmB;AAAA,UACrB;AAAA,QACF;AAIA,aAAK,aAAa,KAAK,cACpB,KAAK,mBAAmB,QAAQ,CAAC,QAAQ,qBAAqB,KAAK,KAAK,SAAS,KAAM,SAAS,KAAK,KAAK,KAAK,OAAO,gBAAgB,CAAC;AAAA,MAC5I;AAAA,IACF;AAEA,eAAW,UAAU,sBAAsB,SAAS,WAAW;AAC7D,UAAI,eAAe,IAAI,mBAAmB,KAAK,UAAU,KAAK,WAAW,iBAAiB,GAAG,SAAS;AAEtG,mBAAa,iBAAiB,KAAK,SAAS;AAE5C,mBAAa,aAAa,aAAa,cACrC,SAAS,aAAa,WAAW,KAAK,SAAS,aAAa;AAE9D,mBAAa,mBAAmB,aAAa,gBAC1C,aAAa,gBAAgB,aAAa;AAE7C,mBAAa,iBAAiB,CAAC,aAAa,gBAAgB,SAAS,aAAa,WAAW,KAAK,SAAS,WAAW;AACtH,mBAAa,yBAAyB,CAAC,aAAa,oBAAoB,SAAS,aAAa,WAAW,KAAK,SAAS,mBAAmB;AAC1I,mBAAa,oBAAoB,SAAS,aAAa,UAAU,KAAK,SAAS,MAAM,KAAM,KAAK,SAAS,0BAA0B,aAAa,SAAS,SAAS,GAAG,KAAM,aAAa,mBAAmB;AAE3M,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,oBAAoB,SAAS,SAAS,WAAW,cAAc,gBAAgB,YAAY;AAE9G,UAAI,CAAC,aAAa,kBAAkB;AAClC,YAAI,aAAa,YAAY;AAC3B,uBAAa,kBAAkB,KAAK,WAAW,QAAQ,aAAa,QAAQ;AAAA,QAC9E,OAAO;AAGL,cAAI,KAAK,yBAAyB,YAAY,GAAG;AAC/C,gBAAI,CAAC,aAAa,mBAAmB;AACnC,sBAAQ,cAAc,KAAK;AAAA,YAC7B;AAAA,UACF;AAEA,eAAK,WAAW,WAAW,YAAY;AAEvC,eAAK,aAAa,aAAa,YAAY,aAAa,aAAa,YACnE,EAAE,aAAa,kBAAkB,aAAa,yBAAyB;AACvE,yBAAa,yBAAyB,2BAA2B,aAAa,WAAW,SAAS;AAAA,UACpG;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,aAAa,WAAW,KAAK,SAAS,YAAY,GAAG;AAChE,gBAAQ,cAAc,KAAK;AAC3B,YAAI,CAAC,QAAQ,QAAQ,qBAAqB,GAAG;AAC3C,kBAAQ,cAAc,IAAI;AAAA,QAC5B;AAAA,MACF;AAEA,UAAI,aAAa,kBAAkB;AAIjC,YAAI,aAAa,mBAAmB,OAAO,aAAa,cAAc,QAAQ;AAC5E,eAAK,WAAW,cAAc,CAAC,MAAM,UAAU,MAAM,CAAC;AACtD,uBAAa,iBAAiB;AAE9B,cAAI,uBAAuB,QAAQ,uBAAuB,OAAO;AACjE,cAAI,CAAC,sBAAsB;AACzB,oBAAQ,cAAc,KAAK;AAAA,UAC7B;AAAA,QACF;AAGA,YAAI,aAAa,aAAa,SAAS,WAAW,SAAS,MAAM,aAC/D,eAAe,cAAc,aAAa,KAAK,QAAQ,IAAI,MAAM,IAAI;AAAA,QAEvE,OAAO;AACL,cAAI,EAAE,aAAa,qBAAqB,aAAa,iBAAiB;AACpE,oBAAQ,cAAc,KAAK;AAAA,UAC7B;AACA,eAAK,4BAA4B,SAAS,YAAY;AAAA,QACxD;AAAA,MACF,WAAW,aAAa,YAAY;AAClC,YAAI,gBAAgB;AAGpB,wBAAgB,aAAa,mBAAmB,aAAa,gBAAgB;AAC7E,wBAAgB,iBAAkB,CAAC,aAAa,qBAC9C,EAAE,eAAe,qBAAqB,eAAe,mBACrD,EAAE,WAAW,SAAS,MAAM,aAAa,aAAa,oBAAoB,mBAC1E,WAAW,SAAS;AAGtB,YAAI,aAAa,0BAA0B,aAAa,gBAAgB;AACtE,0BAAgB;AAAA,QAClB;AAEA,YAAI,eAAe;AACjB,kBAAQ,cAAc,KAAK;AAAA,QAC7B;AAAA,MACF,OAAO;AACL,qBAAa,iBAAiB,CAAC,aAAa;AAE5C,YAAI,aAAa,mBAAmB,KAAK;AACvC,cAAI,aAAa,aAAa,QAAQ;AACpC,yBAAa,iBAAiB,KAAK,SAAS;AAAA,UAC9C,WAAW,aAAa,aAAa,QAAQ;AAC3C,yBAAa,iBAAiB,KAAK,SAAS;AAAA,UAC9C,WAAW,aAAa,aAAa,QAAQ;AAC3C,yBAAa,iBAAiB,KAAK,SAAS;AAAA,UAC9C;AAAA,QACF;AAEA,YAAI,EAAE,aAAa,qBAAqB,aAAa,oBAClD,WAAW,SAAS,gBAAgB,aAAa,yBAAyB;AAC3E,kBAAQ,cAAc,KAAK;AAAA,QAC7B;AAEA,aAAK,4BAA4B,SAAS,YAAY;AAAA,MACxD;AAAA,IACF;AAEA,eAAW,UAAU,8BAA8B,SAAS,SAAS,cAAc;AACjF,UAAI,aAAa,UAAU,QAAQ,QAAQ,mBAAmB,KAC5D,GAAG,aAAa,qBAAqB,aAAa,mBAAmB,aAAa,OAAO,oBAAoB;AAC7G,qBAAa,OAAO,oBAAoB;AAAA,MAC1C;AAAA,IACF;AAGA,QAAI,YAAY,CAAC,WAAW,WAAW,SAAS,cAAc,WAAW,OAAO,MAAM,YAAY,cAAc,UAAU,UAAU,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,UAAU,MAAM,QAAQ,QAAQ,OAAO,MAAM,KAAK,OAAO,WAAW,SAAS,IAAI;AACjQ,QAAI,oBAAoB,CAAC,KAAK,SAAS,OAAO,OAAO,OAAO,YAAY,OAAO;AAE/E,eAAW,UAAU,2BAA2B,SAAS,cAAc;AACrE,UAAI,SAAS;AAKb,UAAI,aAAa,oBAAoB,CAAC,aAAa,gBAAgB,CAAC,aAAa,QAAQ;AACvF;AAAA,MAEF;AAEA,UAAI,aAAa,aAAa,QAAQ;AAEpC,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM;AAAA,MAKnD,WAAW,aAAa,aAAa,MAAM;AAEzC,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,MAEvE,WAAW,aAAa,aAAa,QAAQ,aAAa,aAAa,MAAM;AAG3E,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,IAAI,CAAC;AACvD,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,IAAI,CAAC;AAAA,MAGzD,WAAW,aAAa,OAAO,aAAa,OAAO,UAAU,QAAQ,aAAa,QAAQ,MAAM,IAAI;AAKlG,YAAI,WAAW,aAAa,OAAO;AACnC,YAAI,CAAC,YAAY,kBAAkB,QAAQ,SAAS,QAAQ,MAAM,IAAI;AACpE,mBAAS,UAAU,KAAK,WAAW,QAAQ,GAAG;AAAA,QAChD;AAAA,MACF,WAAW,aAAa,aAAa,QAAQ,aAAa,aAAa,MAAM;AAG3E,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,CAAC;AAChE,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,CAAC;AAAA,MAElE,WAAW,aAAa,aAAa,YAAY;AAG/C,iBAAS,UAAU,KAAK,WAAW,QAAQ,YAAY,CAAC,QAAQ,CAAC;AAAA,MAGnE,WAAW,aAAa,aAAa,UAAU;AAE7C,iBAAS,UAAU,KAAK,WAAW,QAAQ,UAAU,CAAC,UAAU,YAAY,UAAU,CAAC;AAAA,MAEzF,WAAW,aAAa,aAAa,YAAY;AAG/C,iBAAS,UAAU,KAAK,WAAW,QAAQ,WAAW,CAAC,OAAO,CAAC;AAAA,MAEjE,WAAW,aAAa,aAAa,SAAS;AAG5C,iBAAS,UAAU,KAAK,WAAW,QAAQ,WAAW,CAAC,OAAO,CAAC;AAC/D,iBAAS,UAAU,KAAK,WAAW,QAAQ,YAAY,CAAC,OAAO,CAAC;AAAA,MAKlE,WAAW,aAAa,aAAa,WAAW,aAAa,aAAa,SAAS;AAKjF,iBAAS,UAAU,KAAK,WAAW,QAAQ,WAAW,CAAC,OAAO,CAAC;AAC/D,iBAAS,UAAU,KAAK,WAAW,QAAQ,YAAY,CAAC,OAAO,CAAC;AAChE,iBAAS,UAAU,KAAK,WAAW,QAAQ,SAAS,CAAC,OAAO,CAAC;AAC7D,iBAAS,UAAU,KAAK,WAAW,QAAQ,SAAS,CAAC,OAAO,CAAC;AAAA,MAK/D,WAAW,aAAa,aAAa,MAAM;AAIzC,iBAAS,UAAU,KAAK,WAAW,QAAQ,WAAW,CAAC,OAAO,CAAC;AAC/D,iBAAS,UAAU,KAAK,WAAW,QAAQ,YAAY,CAAC,OAAO,CAAC;AAChE,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,SAAS,SAAS,SAAS,OAAO,CAAC;AAAA,MAEvF,WAAW,aAAa,aAAa,QAAQ,aAAa,aAAa,MAAM;AAG3E,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,SAAS,SAAS,SAAS,SAAS,IAAI,CAAC;AAC3F,iBAAS,UAAU,KAAK,WAAW,QAAQ,MAAM,CAAC,SAAS,SAAS,SAAS,SAAS,IAAI,CAAC;AAAA,MAC7F;AAQA,mBAAa,SAAS,KAAK,WAAW,iBAAiB;AAEvD,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,aAAa;AAAA;AAAA;;;ACv5B5B;AAAA;AAAA;AA8BA,QAAI,aAAa,sBAAwB;AAAzC,QACE,UAAU,mBAAqB;AAEjC,aAAS,WAAW,aAAa,SAAS,aAAa,cAAc;AACnE,UAAI,aAAa,IAAI,WAAW,aAAa,SAAS,aAAa,YAAY;AAC/E,aAAO,WAAW,SAAS;AAAA,IAC7B;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,iBAAiB,WAAW;AACzC,aAAO,IAAI,QAAQ;AAAA,IACrB;AAAA;AAAA;;;ACzCA;AAAA;AAAA;AA8BA,QAAI,cAAc;AAClB,QAAI,eAAe;AACnB,QAAI,gBAAgB;AAEpB,aAAS,WAAW,aAAa,SAAS,IAAI,KAAK;AACjD,WAAK,MAAM;AACX,YAAM,OAAO;AACb,aAAO,cAAc,aAAa,SAAS,IAAI,GAAG;AAAA,IACpD;AACA,eAAW,iBAAiB,cAAc;AAE1C,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,MAAM;AACrB,WAAO,QAAQ,OAAO;AAAA;AAAA;;;AC3CtB;AAAA;AAAA;AA+CA,aAAS,aAAa,aAAa,cAAc,eAAe;AAE9D,UAAI,WAAW,SAAS,KAAK,QAAQ;AACnC,eAAO,YAAY,YAAY,KAAK,MAAM;AAAA,MAC5C;AAGA,eAAS,KAAK,YAAY;AAC1B,eAAS,MAAM,aAAa;AAC5B,eAAS,OAAO,cAAc;AAG9B,eAAS,cAAc,YAAY;AACnC,eAAS,eAAe,aAAa;AACrC,eAAS,gBAAgB,cAAc;AAEvC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE9C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,SAAS,aAAa,cAAc,eAAe;AACpD,eAAO,aAAa,aAAa,cAAc,aAAa;AAAA,MAC9D,CAAC;AAAA,IACH,OAAO;AACL,OAAC,SAAS,KAAK;AACb,YAAI,aAAa;AACjB,mBAAW,cAAc,WAAW;AACpC,mBAAW,eAAe,WAAW;AACrC,mBAAW,gBAAgB,WAAW;AAEtC,YAAI,UAAU,aAAa,YAAY,YAAY,UAAU;AAAA,MAE/D,GAAG,MAAM;AAAA,IACX;AAAA;AAAA;;;ACrFA;AAAA;AAAA;AASA,QAAI;AAEJ,WAAO,UAAU,SAAS,aAAa,KAAK;AAC1C,aAAQ,OAAO,QAAQ,YAAa,MAAM,EAAE,KAAK,GAAG;AAAA,IACtD;AAEA,aAAS,QAAQ;AAEf,aAAO,UAAU,QAAQ,IAAI,OAAO,wDAAyJ;AAAA,IAC/L;AAAA;AAAA;;;AClBA;AAAA;AAAA;AASA,WAAO,UAAU,SAAS,aAAa,KAAK;AAC1C,aAAO,OAAO,QAAQ,eAAe,QAAQ,SACvC,OAAO,QAAQ,YAAY,OAAO,QAAQ;AAAA,IAClD;AAAA;AAAA;;;ACZA;AAAA;AAAA;AAEA,QAAI,WAAW;AAEf,WAAO,UAAU,SAAS,OAAO,GAAgB;AAC/C,UAAI,CAAC,SAAS,CAAC,GAAG;AAAE,YAAI,CAAC;AAAA,MAAG;AAE5B,UAAI,MAAM,UAAU;AACpB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,MAAM,UAAU,CAAC;AAErB,YAAI,SAAS,GAAG,GAAG;AACjB,iBAAO,GAAG,GAAG;AAAA,QACf;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,GAAG,GAAG;AACpB,eAAS,OAAO,GAAG;AACjB,YAAI,OAAO,GAAG,GAAG,GAAG;AAClB,YAAE,GAAG,IAAI,EAAE,GAAG;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAMA,aAAS,OAAO,KAAK,KAAK;AACxB,aAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AAAA,IACtD;AAAA;AAAA;;;AChCA;AAAA;AASA,WAAO,UAAU,SAAU,KAAK;AAC9B,aAAO,OAAO,SAAS,SAAS,GAAG,KAAK,aAAa,GAAG,KAAK,CAAC,CAAC,IAAI;AAAA,IACrE;AAEA,aAAS,SAAU,KAAK;AACtB,aAAO,CAAC,CAAC,IAAI,eAAe,OAAO,IAAI,YAAY,aAAa,cAAc,IAAI,YAAY,SAAS,GAAG;AAAA,IAC5G;AAGA,aAAS,aAAc,KAAK;AAC1B,aAAO,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,UAAU,cAAc,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IAC7G;AAAA;AAAA;;;ACpBA;AAAA;AAAA,QAAI,WAAW;AACf,QAAI,WAAW,OAAO,UAAU;AAShC,WAAO,UAAU,SAAS,OAAO,KAAK;AAEpC,UAAI,OAAO,QAAQ,aAAa;AAC9B,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,QAAQ,QAAQ,SAAS,eAAe,SAAS;AAC3D,eAAO;AAAA,MACT;AACA,UAAI,OAAO,QAAQ,YAAY,eAAe,QAAQ;AACpD,eAAO;AAAA,MACT;AACA,UAAI,OAAO,QAAQ,YAAY,eAAe,QAAQ;AACpD,eAAO;AAAA,MACT;AAGA,UAAI,OAAO,QAAQ,cAAc,eAAe,UAAU;AACxD,eAAO;AAAA,MACT;AAGA,UAAI,OAAO,MAAM,YAAY,eAAe,MAAM,QAAQ,GAAG,GAAG;AAC9D,eAAO;AAAA,MACT;AAGA,UAAI,eAAe,QAAQ;AACzB,eAAO;AAAA,MACT;AACA,UAAI,eAAe,MAAM;AACvB,eAAO;AAAA,MACT;AAGA,UAAI,OAAO,SAAS,KAAK,GAAG;AAE5B,UAAI,SAAS,mBAAmB;AAC9B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,iBAAiB;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,sBAAsB;AACjC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,kBAAkB;AAC7B,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,GAAG,GAAG;AACjB,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,gBAAgB;AAC3B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,oBAAoB;AAC/B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,gBAAgB;AAC3B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,oBAAoB;AAC/B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,mBAAmB;AAC9B,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,sBAAsB;AACjC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,8BAA8B;AACzC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,wBAAwB;AACnC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,wBAAwB;AACnC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,yBAAyB;AACpC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,yBAAyB;AACpC,eAAO;AAAA,MACT;AAGA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnHA;AAAA;AAAA;AASA,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,WAAO,UAAU,SAAS,KAAK,SAAS;AACtC,UAAI,OAAO,OAAO,CAAC,GAAG,OAAO;AAC7B,UAAI,MAAM,KAAK,OAAO;AACtB,UAAI,MAAM,KAAK;AACf,UAAI;AAEJ,UAAI,OAAO,QAAQ,YAAY,QAAQ,GAAG;AACxC,aAAK,IAAI,OAAO,2BAA2B,MAAM,IAAI;AAAA,MACvD;AACA,UAAI,OAAO,OAAO,aAAa;AAC7B,aAAK,KAAK,SAAS;AAAA,MACrB;AAIA,UAAI,KAAK,mBAAmB,MAAM;AAChC,cAAM,IAAI,MAAM,IAAI,EAAE,IAAI,SAAS,MAAM;AACvC,iBAAO,aAAa,IAAI,IAAI,KAAK,KAAK,IAAI;AAAA,QAC5C,CAAC,EAAE,KAAK,IAAI;AAAA,MACd;AAEA,YAAM,gBAAgB,KAAK,IAAI;AAC/B,aAAO,IAAI,QAAQ,IAAI,GAAG;AAAA,IAC5B;AAEA,aAAS,gBAAgB,KAAK,SAAS;AACrC,UAAI,MAAM,QAAQ;AAClB,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT;AAEA,cAAQ,OAAO,GAAG,GAAG;AAAA,QACnB,KAAK;AACH,gBAAM,IAAI,QAAQ,QAAQ,QAAQ,eAAe;AACjD;AAAA,QACF,KAAK;AACH,gBAAM,QAAQ,gBAAgB,GAAG;AACjC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,SAAS;AACP,gBAAM,IAAI,QAAQ,QAAQ,IAAI;AAC9B;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC3DA;AAAA;AAAA;AASA,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,SAAS;AACb,QAAI,WAAW;AAAA,MACb,aAAa,CAAC,QAAQ,OAAO,MAAM,UAAU,MAAM;AAAA,MACnD,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,KAAK;AAAA,IACP;AAEA,WAAO,UAAU,SAASC,QAAO,KAAK,SAAS;AAC7C,UAAI,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO;AACvC,YAAM,SAAS,KAAK,KAAK,IAAI;AAE7B,UAAI,KAAK,QAAQ,MAAM;AACrB,YAAI,KAAK,SAAU,MAAK,MAAM,KAAK;AACnC,eAAO,IAAI,KAAK,IAAI;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,IAAI,KAAK,SAAS;AAEzB,aAAO,SAAS,KAAK,OAAO,EAEzB,QAAQ,SAAS,EAAE,EAEnB,QAAQ,SAAS,IAAI,EAGrB,QAAQ,cAAc,MAAM,EAE5B,QAAQ,wBAAwB,IAAI;AAAA,IACzC;AAAA;AAAA;;;ACtBA,qBAAgC;AAEhC,oBAAmB;AAwBnB,IAAAC,iBAAoB;AAhDpB,IAAI,UAAU,CAAC,QAAQ,aAAa,cAAc;AAChD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,YAAY,CAAC,UAAU;AACzB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,QAAI,WAAW,CAAC,UAAU;AACxB,UAAI;AACF,aAAK,UAAU,MAAM,KAAK,CAAC;AAAA,MAC7B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,QAAI,OAAO,CAAC,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,IAAI,QAAQ,QAAQ,EAAE,KAAK,EAAE,KAAK,WAAW,QAAQ;AAC/F,UAAM,YAAY,UAAU,MAAM,QAAQ,WAAW,GAAG,KAAK,CAAC;AAAA,EAChE,CAAC;AACH;AA8BA,IAAI,aAAa,CAAC,mBAAmB,QAAQ,QAAQ,MAAM,aAAa;AACtE,QAAM,SAAS,eAAe,UAAU;AACxC,QAAM,SAAS,CAAC;AAChB,SAAO,MAAM;AACX,UAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,MAAM;AACR;AAAA,IACF;AACA,WAAO,KAAK,KAAK;AAAA,EACnB;AACA,SAAO,OAAO,IAAI,CAAC,UAAU,IAAI,YAAY,OAAO,EAAE,OAAO,KAAK,CAAC,EAAE,KAAK,EAAE;AAC9E,CAAC;AACD,IAAI,cAAc,CAAC,WAAW,YAAY,QAAQ,QAAQ,MAAM,aAAa;AAC3E,QAAM,kBAAkB,MAAM,OAAO,8BAAkB,GAAG;AAC1D,QAAM,iBAAiB,eAAe,0BAA0B,eAAe,kBAAkB,eAAe;AAChH,QAAM,UAAU;AAChB,QAAM,iBAAiB,MAAM,eAAe,SAAS;AACrD,QAAM,OAAO,OAAO,mBAAmB,WAAW,iBAAiB,MAAM,WAAW,cAAc;AAClG,MAAI,WAAW,OAAO,SAAS,QAAQ,WAAW;AAChD,WAAO,QAAS,MAAM;AAAA,MACpB,WAAW;AAAA,QACT,EAAE,UAAU,OAAO,QAAQ,OAAO;AAAA,QAClC,EAAE,UAAU,0BAA0B,QAAQ,OAAO;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,WAAW,GAAG,OAAO,GAAG,IAAI;AAClC,MAAI,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAC7C,eAAO,eAAAC,SAAQ,QAAQ;AAAA,EACzB;AACA,SAAO;AACT,CAAC;;;AChFD,IAAI,YAAY,OAAO;AACvB,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAIC,WAAU,CAAC,QAAQ,aAAa,cAAc;AAChD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,YAAY,CAAC,UAAU;AACzB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,QAAI,WAAW,CAAC,UAAU;AACxB,UAAI;AACF,aAAK,UAAU,MAAM,KAAK,CAAC;AAAA,MAC7B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,QAAI,OAAO,CAAC,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,IAAI,QAAQ,QAAQ,EAAE,KAAK,EAAE,KAAK,WAAW,QAAQ;AAC/F,UAAM,YAAY,UAAU,MAAM,QAAQ,WAAW,GAAG,KAAK,CAAC;AAAA,EAChE,CAAC;AACH;AAGA,IAAI,UAAU;AAGd,IAAI,UAAU,MAAM;AAAA,EAClB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO,IAAI,WAAW;AAC9C,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,aAAa,EAAE;AAAA,MACjB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAGA,IAAI,YAAY,MAAM;AAAA,EACpB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO,IAAI,YAAY;AAC/C,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI;AACN,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,EAAE;AAAA,MAClB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,EAAE;AAAA,MAClB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAIA,IAAI,QAAQ,MAAM;AAAA,EAChB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,KAAK,IAAI;AACP,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,aAAO,KAAK,OAAO,SAAS,OAAO;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,iBAAW,SAAS,SAAS;AAC3B,YAAI,MAAM,OAAO;AACf,gBAAM,OAAO,MAAM,YAAY,MAAM,KAAK;AAC1C,iBAAO,MAAM;AAAA,QACf;AAAA,MACF;AACA,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAGA,IAAI,WAAW,MAAM;AAAA,EACnB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,QAAQ,WAAW;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,KAAK,IAAI;AACP,WAAOA,SAAQ,MAAM,WAAW,WAAW;AAAA,MACzC;AAAA,IACF,GAAG;AACD,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,WAAW;AAAA,MAC3B;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI;AACN,WAAOA,SAAQ,MAAM,WAAW,WAAW;AAAA,MACzC;AAAA,MACA;AAAA,IACF,GAAG;AACD,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,WAAW,aAAa,EAAE;AAAA,MAC1C;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS;AACd,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,QAAQ,WAAW,aAAa,QAAQ,EAAE;AAAA,QACxD;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW;AAAA,MACzC;AAAA,MACA;AAAA,IACF,GAAG;AACD,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,cAAc,WAAW,aAAa,EAAE;AAAA,MAC1C;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAGA,IAAI,UAAU,MAAM;AAAA,EAClB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO,IAAI,UAAU;AAC7C,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI;AACN,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,YAAY,EAAE;AAAA,MAChB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,YAAY,EAAE;AAAA,MAChB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,YAAY,EAAE;AAAA,MAChB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAIA,IAAI,SAAS,MAAM;AAAA,EACjB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,KAAK,IAAI;AACP,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,aAAO,KAAK,OAAO,SAAS,OAAO;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,IAAI;AACT,WAAOA,SAAQ,MAAM,WAAW,WAAW,SAAS,UAAU,CAAC,GAAG;AAChE,UAAI,QAAQ,OAAO;AACjB,gBAAQ,OAAO,MAAM,YAAa,QAAQ,KAAK;AAC/C,eAAO,QAAQ;AAAA,MACjB;AACA,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI;AACN,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,OAAO,MAAM,KAAK,OAAO;AAAA,QAC7B,WAAW,EAAE;AAAA,MACf;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAGA,IAAI,wBAAwB,CAAC,aAAa;AACxC,MAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,WAAO;AAAA,EACT;AACA,QAAM,QAAQ;AACd,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,WAAO;AAAA,EACT;AACA,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,SAAO,OAAO,YAAY,YAAY,OAAO,SAAS;AACxD;AAGA,IAAI,iBAAiB;AACrB,IAAI,mBAAmB,eAAe,OAAO;AAC7C,IAAI,UAAU,OAAO,YAAY,eAAe,QAAQ,MAAM,QAAQ,IAAI,mBAAmB,iBAAiB;AAC9G,IAAI,YAAY,OAAO,YAAY,eAAe,QAAQ,MAAM,QAAQ,IAAI,qBAAqB,mBAAmB;AACpH,IAAI,SAAS,MAAM;AAAA,EACjB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,UAAU,IAAI,QAAQ,IAAI;AAC/B,SAAK,YAAY,IAAI,UAAU,IAAI;AACnC,SAAK,QAAQ,IAAI,MAAM,IAAI;AAC3B,SAAK,WAAW,IAAI,SAAS,IAAI;AACjC,SAAK,UAAU,IAAI,QAAQ,IAAI;AAC/B,SAAK,SAAS,IAAI,OAAO,IAAI;AAC7B,QAAI,CAAC,KAAK;AACR,UAAI,OAAO,YAAY,eAAe,QAAQ,KAAK;AACjD,aAAK,MAAM,QAAQ,IAAI;AAAA,MACzB;AACA,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU,IAAI,QAAQ;AAAA,MACzB,eAAe,UAAU,KAAK,GAAG;AAAA,MACjC,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,aAAa,IAAI;AACf,WAAOA,SAAQ,MAAM,WAAW,WAAW,MAAM,UAAU,CAAC,GAAG;AAC7D,YAAM,WAAW,MAAM,MAAM,GAAG,OAAO,GAAG,IAAI,IAAI,OAAO;AACzD,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,QAAQ,MAAM,SAAS,KAAK;AAClC,YAAI,sBAAsB,KAAK,GAAG;AAChC,iBAAO,EAAE,MAAM,MAAM,MAAM;AAAA,QAC7B;AACA,eAAO,EAAE,MAAM,MAAM,MAAM;AAAA,MAC7B;AACA,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,aAAO,EAAE,MAAM,OAAO,KAAK;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,KAAK,IAAI,IAAI;AACX,WAAOA,SAAQ,MAAM,WAAW,WAAW,MAAM,QAAQ,UAAU,CAAC,GAAG;AACrE,YAAM,iBAAiB,eAAe;AAAA,QACpC,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7B,GAAG,OAAO;AACV,aAAO,KAAK,aAAa,MAAM,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI;AACN,WAAOA,SAAQ,MAAM,WAAW,WAAW,MAAM,UAAU,CAAC,GAAG;AAC7D,YAAM,iBAAiB,eAAe;AAAA,QACpC,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,MAChB,GAAG,OAAO;AACV,aAAO,KAAK,aAAa,MAAM,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI,IAAI;AACV,WAAOA,SAAQ,MAAM,WAAW,WAAW,MAAM,QAAQ,UAAU,CAAC,GAAG;AACrE,YAAM,iBAAiB,eAAe;AAAA,QACpC,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7B,GAAG,OAAO;AACV,aAAO,KAAK,aAAa,MAAM,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,MAAM,IAAI,IAAI;AACZ,WAAOA,SAAQ,MAAM,WAAW,WAAW,MAAM,QAAQ,UAAU,CAAC,GAAG;AACrE,YAAM,iBAAiB,eAAe;AAAA,QACpC,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7B,GAAG,OAAO;AACV,aAAO,KAAK,aAAa,MAAM,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,OAAO,MAAM,OAAO;AAClB,WAAOA,SAAQ,MAAM,MAAM,aAAa;AACtC,YAAM,iBAAiB;AAAA,QACrB,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,KAAK;AAAA,MAC5B;AACA,aAAO,KAAK,aAAa,MAAM,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AACF;", "names": ["require_options", "require_tokenizer", "require_options", "require_beautifier", "require_options", "require_tokenizer", "require_beautifier", "pretty", "import_pretty", "pretty2", "__async"]}
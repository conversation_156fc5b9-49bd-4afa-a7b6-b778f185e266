{"version": 3, "sources": ["../../events/events.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAuBA,QAAI,IAAI,OAAO,YAAY,WAAW,UAAU;AAChD,QAAI,eAAe,KAAK,OAAO,EAAE,UAAU,aACvC,EAAE,QACF,SAASA,cAAa,QAAQ,UAAU,MAAM;AAC9C,aAAO,SAAS,UAAU,MAAM,KAAK,QAAQ,UAAU,IAAI;AAAA,IAC7D;AAEF,QAAI;AACJ,QAAI,KAAK,OAAO,EAAE,YAAY,YAAY;AACxC,uBAAiB,EAAE;AAAA,IACrB,WAAW,OAAO,uBAAuB;AACvC,uBAAiB,SAASC,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM,EACrC,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF,OAAO;AACL,uBAAiB,SAASA,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,mBAAmB,SAAS;AACnC,UAAI,WAAW,QAAQ,KAAM,SAAQ,KAAK,OAAO;AAAA,IACnD;AAEA,QAAI,cAAc,OAAO,SAAS,SAASC,aAAY,OAAO;AAC5D,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,eAAe;AACtB,mBAAa,KAAK,KAAK,IAAI;AAAA,IAC7B;AACA,WAAO,UAAU;AACjB,WAAO,QAAQ,OAAO;AAGtB,iBAAa,eAAe;AAE5B,iBAAa,UAAU,UAAU;AACjC,iBAAa,UAAU,eAAe;AACtC,iBAAa,UAAU,gBAAgB;AAIvC,QAAI,sBAAsB;AAE1B,aAAS,cAAc,UAAU;AAC/B,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,qEAAqE,OAAO,QAAQ;AAAA,MAC1G;AAAA,IACF;AAEA,WAAO,eAAe,cAAc,uBAAuB;AAAA,MACzD,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS,KAAK;AACjB,YAAI,OAAO,QAAQ,YAAY,MAAM,KAAK,YAAY,GAAG,GAAG;AAC1D,gBAAM,IAAI,WAAW,oGAAoG,MAAM,GAAG;AAAA,QACpI;AACA,8BAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAED,iBAAa,OAAO,WAAW;AAE7B,UAAI,KAAK,YAAY,UACjB,KAAK,YAAY,OAAO,eAAe,IAAI,EAAE,SAAS;AACxD,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,gBAAgB,KAAK,iBAAiB;AAAA,IAC7C;AAIA,iBAAa,UAAU,kBAAkB,SAAS,gBAAgB,GAAG;AACnE,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,GAAG;AACpD,cAAM,IAAI,WAAW,kFAAkF,IAAI,GAAG;AAAA,MAChH;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB;AACzB,eAAO,aAAa;AACtB,aAAO,KAAK;AAAA,IACd;AAEA,iBAAa,UAAU,kBAAkB,SAAS,kBAAkB;AAClE,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,iBAAa,UAAU,OAAO,SAAS,KAAK,MAAM;AAChD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,MAAK,KAAK,UAAU,CAAC,CAAC;AACjE,UAAI,UAAW,SAAS;AAExB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW;AACb,kBAAW,WAAW,OAAO,UAAU;AAAA,eAChC,CAAC;AACR,eAAO;AAGT,UAAI,SAAS;AACX,YAAI;AACJ,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,CAAC;AACb,YAAI,cAAc,OAAO;AAGvB,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO,GAAG,UAAU,MAAM,GAAG;AAC5E,YAAI,UAAU;AACd,cAAM;AAAA,MACR;AAEA,UAAI,UAAU,OAAO,IAAI;AAEzB,UAAI,YAAY;AACd,eAAO;AAET,UAAI,OAAO,YAAY,YAAY;AACjC,qBAAa,SAAS,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,MAAM,QAAQ;AAClB,YAAI,YAAY,WAAW,SAAS,GAAG;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,uBAAa,UAAU,CAAC,GAAG,MAAM,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ,MAAM,UAAU,SAAS;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,oBAAc,QAAQ;AAEtB,eAAS,OAAO;AAChB,UAAI,WAAW,QAAW;AACxB,iBAAS,OAAO,UAAU,uBAAO,OAAO,IAAI;AAC5C,eAAO,eAAe;AAAA,MACxB,OAAO;AAGL,YAAI,OAAO,gBAAgB,QAAW;AACpC,iBAAO;AAAA,YAAK;AAAA,YAAe;AAAA,YACf,SAAS,WAAW,SAAS,WAAW;AAAA,UAAQ;AAI5D,mBAAS,OAAO;AAAA,QAClB;AACA,mBAAW,OAAO,IAAI;AAAA,MACxB;AAEA,UAAI,aAAa,QAAW;AAE1B,mBAAW,OAAO,IAAI,IAAI;AAC1B,UAAE,OAAO;AAAA,MACX,OAAO;AACL,YAAI,OAAO,aAAa,YAAY;AAElC,qBAAW,OAAO,IAAI,IACpB,UAAU,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ;AAAA,QAExD,WAAW,SAAS;AAClB,mBAAS,QAAQ,QAAQ;AAAA,QAC3B,OAAO;AACL,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAGA,YAAI,iBAAiB,MAAM;AAC3B,YAAI,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAS,QAAQ;AACpD,mBAAS,SAAS;AAGlB,cAAI,IAAI,IAAI,MAAM,iDACE,SAAS,SAAS,MAAM,OAAO,IAAI,IAAI,mEAEvB;AACpC,YAAE,OAAO;AACT,YAAE,UAAU;AACZ,YAAE,OAAO;AACT,YAAE,QAAQ,SAAS;AACnB,6BAAmB,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,cAAc,SAAS,YAAY,MAAM,UAAU;AACxE,aAAO,aAAa,MAAM,MAAM,UAAU,KAAK;AAAA,IACjD;AAEA,iBAAa,UAAU,KAAK,aAAa,UAAU;AAEnD,iBAAa,UAAU,kBACnB,SAAS,gBAAgB,MAAM,UAAU;AACvC,aAAO,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,IAChD;AAEJ,aAAS,cAAc;AACrB,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,OAAO,eAAe,KAAK,MAAM,KAAK,MAAM;AACjD,aAAK,QAAQ;AACb,YAAI,UAAU,WAAW;AACvB,iBAAO,KAAK,SAAS,KAAK,KAAK,MAAM;AACvC,eAAO,KAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,MAAM,UAAU;AACzC,UAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,QAAW,QAAgB,MAAY,SAAmB;AAC9F,UAAI,UAAU,YAAY,KAAK,KAAK;AACpC,cAAQ,WAAW;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,OAAO,SAASC,MAAK,MAAM,UAAU;AAC1D,oBAAc,QAAQ;AACtB,WAAK,GAAG,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC7C,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,sBACnB,SAAS,oBAAoB,MAAM,UAAU;AAC3C,oBAAc,QAAQ;AACtB,WAAK,gBAAgB,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AAGJ,iBAAa,UAAU,iBACnB,SAAS,eAAe,MAAM,UAAU;AACtC,UAAI,MAAM,QAAQ,UAAU,GAAG;AAE/B,oBAAc,QAAQ;AAEtB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAET,aAAO,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO;AAET,UAAI,SAAS,YAAY,KAAK,aAAa,UAAU;AACnD,YAAI,EAAE,KAAK,iBAAiB;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,aAC9B;AACH,iBAAO,OAAO,IAAI;AAClB,cAAI,OAAO;AACT,iBAAK,KAAK,kBAAkB,MAAM,KAAK,YAAY,QAAQ;AAAA,QAC/D;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,mBAAW;AAEX,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,cAAI,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,aAAa,UAAU;AACzD,+BAAmB,KAAK,CAAC,EAAE;AAC3B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,aAAa;AACf,eAAK,MAAM;AAAA,aACR;AACH,oBAAU,MAAM,QAAQ;AAAA,QAC1B;AAEA,YAAI,KAAK,WAAW;AAClB,iBAAO,IAAI,IAAI,KAAK,CAAC;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,eAAK,KAAK,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAEJ,iBAAa,UAAU,MAAM,aAAa,UAAU;AAEpD,iBAAa,UAAU,qBACnB,SAAS,mBAAmB,MAAM;AAChC,UAAI,WAAW,QAAQ;AAEvB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAGT,UAAI,OAAO,mBAAmB,QAAW;AACvC,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,eAAK,eAAe;AAAA,QACtB,WAAW,OAAO,IAAI,MAAM,QAAW;AACrC,cAAI,EAAE,KAAK,iBAAiB;AAC1B,iBAAK,UAAU,uBAAO,OAAO,IAAI;AAAA;AAEjC,mBAAO,OAAO,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAM,KAAK,CAAC;AACZ,cAAI,QAAQ,iBAAkB;AAC9B,eAAK,mBAAmB,GAAG;AAAA,QAC7B;AACA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,IAAI;AAEvB,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,eAAe,MAAM,SAAS;AAAA,MACrC,WAAW,cAAc,QAAW;AAElC,aAAK,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAK,eAAe,MAAM,UAAU,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEJ,aAAS,WAAW,QAAQ,MAAM,QAAQ;AACxC,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW;AACb,eAAO,CAAC;AAEV,UAAI,aAAa,OAAO,IAAI;AAC5B,UAAI,eAAe;AACjB,eAAO,CAAC;AAEV,UAAI,OAAO,eAAe;AACxB,eAAO,SAAS,CAAC,WAAW,YAAY,UAAU,IAAI,CAAC,UAAU;AAEnE,aAAO,SACL,gBAAgB,UAAU,IAAI,WAAW,YAAY,WAAW,MAAM;AAAA,IAC1E;AAEA,iBAAa,UAAU,YAAY,SAAS,UAAU,MAAM;AAC1D,aAAO,WAAW,MAAM,MAAM,IAAI;AAAA,IACpC;AAEA,iBAAa,UAAU,eAAe,SAAS,aAAa,MAAM;AAChE,aAAO,WAAW,MAAM,MAAM,KAAK;AAAA,IACrC;AAEA,iBAAa,gBAAgB,SAAS,SAAS,MAAM;AACnD,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,eAAO,QAAQ,cAAc,IAAI;AAAA,MACnC,OAAO;AACL,eAAO,cAAc,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,iBAAa,UAAU,gBAAgB;AACvC,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,KAAK;AAElB,UAAI,WAAW,QAAW;AACxB,YAAI,aAAa,OAAO,IAAI;AAE5B,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,QAAW;AACnC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,aAAa,SAAS,aAAa;AACxD,aAAO,KAAK,eAAe,IAAI,eAAe,KAAK,OAAO,IAAI,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,OAAO,IAAI,MAAM,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,aAAK,CAAC,IAAI,IAAI,CAAC;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM,OAAO;AAC9B,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAC9B,aAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC9B,WAAK,IAAI;AAAA,IACX;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,CAAC,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,SAAS,MAAM;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,cAAc,KAAK;AAC1B,kBAAQ,eAAe,MAAM,QAAQ;AACrC,iBAAO,GAAG;AAAA,QACZ;AAEA,iBAAS,WAAW;AAClB,cAAI,OAAO,QAAQ,mBAAmB,YAAY;AAChD,oBAAQ,eAAe,SAAS,aAAa;AAAA,UAC/C;AACA,kBAAQ,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QAClC;AAAC;AAED,uCAA+B,SAAS,MAAM,UAAU,EAAE,MAAM,KAAK,CAAC;AACtE,YAAI,SAAS,SAAS;AACpB,wCAA8B,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,SAAS,SAAS,OAAO;AAC9D,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,uCAA+B,SAAS,SAAS,SAAS,KAAK;AAAA,MACjE;AAAA,IACF;AAEA,aAAS,+BAA+B,SAAS,MAAM,UAAU,OAAO;AACtE,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,MAAM,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,GAAG,MAAM,QAAQ;AAAA,QAC3B;AAAA,MACF,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAGzD,gBAAQ,iBAAiB,MAAM,SAAS,aAAa,KAAK;AAGxD,cAAI,MAAM,MAAM;AACd,oBAAQ,oBAAoB,MAAM,YAAY;AAAA,UAChD;AACA,mBAAS,GAAG;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,UAAU,wEAAwE,OAAO,OAAO;AAAA,MAC5G;AAAA,IACF;AAAA;AAAA;", "names": ["ReflectApply", "ReflectOwnKeys", "NumberIsNaN", "once"]}
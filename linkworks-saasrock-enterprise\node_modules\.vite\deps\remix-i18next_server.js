import {
  createInstance
} from "./chunk-SQZDYL4I.js";
import "./chunk-PLDDJCW6.js";

// node_modules/remix-i18next/build/lib/format-language-string.js
function formatLanguageString(language) {
  let parts = [language.code];
  if (language.script)
    parts.push(language.script);
  if (language.region)
    parts.push(language.region);
  return parts.join("-");
}

// node_modules/remix-i18next/build/lib/parser.js
var REGEX = /[ ]*((([a-zA-Z]+(-[a-zA-Z0-9]+){0,2})|\*)(;[ ]*q=[0-1](\.[0-9]+)?[ ]*)?)*/g;
function isString(value) {
  return typeof value === "string";
}
function parse(acceptLanguage) {
  let strings = (acceptLanguage || "").match(REGEX);
  if (!strings)
    throw new Error("Invalid Accept-Language header");
  let languages = [];
  for (let m of strings) {
    if (!m)
      continue;
    m = m.trim();
    let bits = m.split(";");
    let ietf = bits[0]?.split("-") ?? [];
    let hasScript = ietf.length === 3;
    languages.push({
      // biome-ignore lint/style/noNonNullAssertion: We know this is not null
      code: ietf[0],
      script: hasScript ? ietf[1] : null,
      region: hasScript ? ietf[2] : ietf[1],
      quality: bits[1] ? (
        // biome-ignore lint/style/noNonNullAssertion: We know this is not null
        Number.parseFloat(bits[1].split("=")[1]) ?? 1
      ) : 1
    });
  }
  return languages.sort((a, b) => b.quality - a.quality);
}
function pick(supportedLanguages, acceptLanguage, options = { loose: false }) {
  if (!supportedLanguages || !supportedLanguages.length || !acceptLanguage) {
    return null;
  }
  let parsedAcceptLanguage = isString(acceptLanguage) ? parse(acceptLanguage) : acceptLanguage;
  let supported = supportedLanguages.map((support) => {
    let bits = support.split("-");
    let hasScript = bits.length === 3;
    return {
      // biome-ignore lint/style/noNonNullAssertion: We know this is not null
      code: bits[0],
      script: hasScript ? bits[1] : null,
      region: (hasScript ? bits[2] : bits[1]) ?? void 0
    };
  });
  for (let lang of parsedAcceptLanguage) {
    if (!lang)
      continue;
    let langCode = lang.code.toLowerCase();
    let langRegion = lang.region ? lang.region.toLowerCase() : lang.region;
    let langScript = lang.script ? lang.script.toLowerCase() : lang.script;
    for (let supportedLanguage of supported) {
      let supportedCode = supportedLanguage.code?.toLowerCase() ?? "";
      if (langCode !== supportedCode)
        continue;
      let supportedScript = supportedLanguage.script ? supportedLanguage.script.toLowerCase() : supportedLanguage.script;
      let supportedRegion = supportedLanguage.region ? supportedLanguage.region.toLowerCase() : supportedLanguage.region;
      if (langCode === supportedCode && (options?.loose || !langScript || langScript === supportedScript) && (options?.loose || !langRegion || langRegion === supportedRegion)) {
        return formatLanguageString(supportedLanguage);
      }
    }
  }
  return null;
}

// node_modules/remix-i18next/build/lib/get-client-locales.js
function getClientLocales(requestOrHeaders) {
  let headers = getHeaders(requestOrHeaders);
  let acceptLanguage = headers.get("Accept-Language");
  if (!acceptLanguage)
    return void 0;
  let parsedLocales = parse(acceptLanguage).filter((lang) => lang.code !== "*").map(formatLanguageString);
  let validLocales = [];
  for (let locale2 of parsedLocales) {
    try {
      new Intl.Locale(locale2);
      validLocales.push(locale2);
    } catch {
    }
  }
  let locale = pick(Intl.DateTimeFormat.supportedLocalesOf(validLocales), acceptLanguage);
  return locale ?? void 0;
}
function getHeaders(requestOrHeaders) {
  if (requestOrHeaders instanceof Request)
    return requestOrHeaders.headers;
  return requestOrHeaders;
}

// node_modules/remix-i18next/build/lib/language-detector.js
var LanguageDetector = class {
  options;
  constructor(options) {
    this.options = options;
    this.isSessionOnly(options);
    this.isCookieOnly(options);
  }
  isSessionOnly(options) {
    if (options.order?.length === 1 && options.order[0] === "session" && !options.sessionStorage) {
      throw new Error("You need a sessionStorage if you want to only get the locale from the session");
    }
  }
  isCookieOnly(options) {
    if (options.order?.length === 1 && options.order[0] === "cookie" && !options.cookie) {
      throw new Error("You need a cookie if you want to only get the locale from the cookie");
    }
  }
  async detect(request) {
    let order = this.options.order ?? this.defaultOrder;
    for (let method of order) {
      let locale = null;
      if (method === "searchParams") {
        locale = this.fromSearchParams(request);
      }
      if (method === "cookie") {
        locale = await this.fromCookie(request);
      }
      if (method === "session") {
        locale = await this.fromSessionStorage(request);
      }
      if (method === "header") {
        locale = this.fromHeader(request);
      }
      if (method === "custom") {
        locale = await this.fromCustom(request);
      }
      if (locale)
        return locale;
    }
    return this.options.fallbackLanguage;
  }
  get defaultOrder() {
    let order = ["searchParams", "cookie", "session", "header"];
    if (this.options.findLocale)
      order.unshift("custom");
    return order;
  }
  fromSearchParams(request) {
    let url = new URL(request.url);
    if (!url.searchParams.has(this.options.searchParamKey ?? "lng")) {
      return null;
    }
    return this.fromSupported(url.searchParams.get(this.options.searchParamKey ?? "lng"));
  }
  async fromCookie(request) {
    if (!this.options.cookie)
      return null;
    let cookie = this.options.cookie;
    let lng = await cookie.parse(request.headers.get("Cookie"));
    if (typeof lng !== "string" || !lng)
      return null;
    return this.fromSupported(lng);
  }
  async fromSessionStorage(request) {
    if (!this.options.sessionStorage)
      return null;
    let session = await this.options.sessionStorage.getSession(request.headers.get("Cookie"));
    let lng = session.get(this.options.sessionKey ?? "lng");
    if (!lng)
      return null;
    return this.fromSupported(lng);
  }
  fromHeader(request) {
    let locales = getClientLocales(request);
    if (!locales)
      return null;
    if (Array.isArray(locales))
      return this.fromSupported(locales.join(","));
    return this.fromSupported(locales);
  }
  async fromCustom(request) {
    if (!this.options.findLocale) {
      throw new ReferenceError("You tried to find a locale using `findLocale` but it iss not defined. Change your order to not include `custom` or provide a findLocale functions.");
    }
    let locales = await this.options.findLocale(request);
    if (!locales)
      return null;
    if (Array.isArray(locales))
      return this.fromSupported(locales.join(","));
    return this.fromSupported(locales);
  }
  fromSupported(language) {
    return pick(this.options.supportedLanguages, language ?? this.options.fallbackLanguage, { loose: false }) || pick(this.options.supportedLanguages, language ?? this.options.fallbackLanguage, { loose: true });
  }
};

// node_modules/remix-i18next/build/server.js
var RemixI18Next = class {
  options;
  detector;
  constructor(options) {
    this.options = options;
    this.detector = new LanguageDetector(this.options.detection);
  }
  /**
   * Detect the current locale by following the order defined in the
   * `detection.order` option.
   * By default the order is
   * - searchParams
   * - cookie
   * - session
   * - header
   * And finally the fallback language.
   */
  async getLocale(request) {
    return this.detector.detect(request);
  }
  /**
   * Get the namespaces required by the routes which are going to be rendered
   * when doing SSR.
   *
   * @param context The EntryContext object received by `handleRequest` in entry.server
   *
   * @example
   * await instance.init({
   *   ns: i18n.getRouteNamespaces(context),
   *   // ...more options
   * });
   */
  getRouteNamespaces(context) {
    let namespaces = Object.values(context.routeModules).flatMap((route) => {
      if (typeof route?.handle !== "object")
        return [];
      if (!route.handle)
        return [];
      if (!("i18n" in route.handle))
        return [];
      if (typeof route.handle.i18n === "string")
        return [route.handle.i18n];
      if (Array.isArray(route.handle.i18n) && route.handle.i18n.every((value) => typeof value === "string")) {
        return route.handle.i18n;
      }
      return [];
    });
    return [...new Set(namespaces)];
  }
  async getFixedT(requestOrLocale, namespaces, options = {}) {
    let [instance, locale] = await Promise.all([
      this.createInstance({ ...this.options.i18next, ...options }),
      typeof requestOrLocale === "string" ? requestOrLocale : this.getLocale(requestOrLocale)
    ]);
    await instance.changeLanguage(locale);
    if (namespaces)
      await instance.loadNamespaces(namespaces);
    else if (instance.options.defaultNS) {
      await instance.loadNamespaces(instance.options.defaultNS);
    } else
      await instance.loadNamespaces("translation");
    return instance.getFixedT(locale, namespaces, options?.keyPrefix);
  }
  async createInstance(options = {}) {
    let instance = createInstance();
    let plugins = [
      ...this.options.backend ? [this.options.backend] : [],
      ...this.options.plugins || []
    ];
    for (const plugin of plugins)
      instance.use(plugin);
    await instance.init(options);
    return instance;
  }
};
export {
  LanguageDetector,
  RemixI18Next
};
//# sourceMappingURL=remix-i18next_server.js.map

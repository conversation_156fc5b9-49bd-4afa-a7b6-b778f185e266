import {
  changeLanguage,
  createInstance,
  dir,
  exists,
  getFixedT,
  hasLoadedNamespace,
  init,
  instance,
  loadLanguages,
  loadNamespaces,
  loadResources,
  reloadResources,
  setDefaultNamespace,
  t,
  use
} from "./chunk-SQZDYL4I.js";
import "./chunk-PLDDJCW6.js";
export {
  changeLanguage,
  createInstance,
  instance as default,
  dir,
  exists,
  getFixedT,
  hasLoadedNamespace,
  init,
  loadLanguages,
  loadNamespaces,
  loadResources,
  reloadResources,
  setDefaultNamespace,
  t,
  use
};
//# sourceMappingURL=i18next.js.map

import clsx from "clsx";
import { useTranslation } from "react-i18next";
import { Link, useNavigation } from "react-router";
import { Stat } from "~/application/dtos/stats/Stat";
import { StatChange } from "~/application/dtos/stats/StatChange";
import { Fragment } from "react";

interface Props {
  items: Stat[];
}

export function DashboardStats({ items }: Props) {
  const navigation = useNavigation();
  // const loading = navigation.state === "loading" && navigation.location.pathname === "/admin/dashboard";
  return (
    <div className="w-full">
      <div
        className={clsx(
          "grid w-full grid-cols-2 gap-4 sm:grid-cols-4",
          items.length === 1 && "sm:grid-cols-1",
          items.length === 2 && "sm:grid-cols-2",
          items.length === 3 && "sm:grid-cols-3",
          items.length === 4 && "sm:grid-cols-4",
          items.length >= 5 && "sm:grid-cols-4"
        )}
      >
        {items.map((item, idx) => (
          <Fragment key={idx}>
            {item.path ? (
              <Link to={item.path} className="w-full min-w-0">
                <DashboardStat
                  item={item}
                  className="hover:bg-secondary bg-card flex w-full min-w-0 cursor-pointer justify-between space-x-1 truncate rounded-lg border border-[#202229] p-5 shadow-sm overflow-hidden"
                />
              </Link>
            ) : (
              <DashboardStat className="border-[#202229] bg-card flex w-full min-w-0 justify-between space-x-1 truncate rounded-lg border p-5 shadow-sm overflow-hidden" item={item} />
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
}

function DashboardStat({ item, loading = false, className }: { item: Stat; loading?: boolean; className?: string }) {
  const { t } = useTranslation();
  return (
    <div key={item.name} className={className}>
      <div className="truncate">
        <div className="text-muted-foreground flex items-baseline space-x-2 text-sm">
          <div>{t(item.name)}</div>
          {item.hint && <div className="text-muted-foreground hidden text-xs xl:block">({t(item.hint)})</div>}
        </div>

        <div className="text-foreground flex items-baseline space-x-2 text-2xl font-medium">
          <div>{loading ? "..." : item.stat}</div>
          {/* {item.previousStat !== undefined && (
            <span className="text-muted-foreground ml-2 hidden text-sm font-medium xl:block">{!loading && <span>from {item.previousStat}</span>}</span>
          )} */}
        </div>
      </div>
      {/* {item.changeType === StatChange.Increase ? (
        <div className="mt-1 flex shrink-0 gap-1 truncate text-green-600">
          {loading ? (
            "..."
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              <div className="flex gap-2 text-xs">
                <span className="font-medium">
                  <div>{item.change}</div>
                </span>
              </div>
            </>
          )}
        </div>
      ) : item.changeType === StatChange.Decrease ? (
        <div className="mt-1 flex gap-1 text-red-600">
          {loading ? (
            "..."
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>

              <p className="flex gap-2 text-xs">
                <span className="font-medium">
                  <div>{item.change}</div>
                </span>
              </p>
            </>
          )}
        </div>
      ) : null} */}
    </div>
  );
}

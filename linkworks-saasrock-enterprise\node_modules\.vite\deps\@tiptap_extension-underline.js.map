{"version": 3, "sources": ["../../@tiptap/extension-underline/src/underline.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\n\nexport interface UnderlineOptions {\n  /**\n   * HTML attributes to add to the underline element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    underline: {\n      /**\n       * Set an underline mark\n       * @example editor.commands.setUnderline()\n       */\n      setUnderline: () => ReturnType,\n      /**\n       * Toggle an underline mark\n       * @example editor.commands.toggleUnderline()\n       */\n      toggleUnderline: () => ReturnType,\n      /**\n       * Unset an underline mark\n       * @example editor.commands.unsetUnderline()\n       */\n      unsetUnderline: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nexport const Underline = Mark.create<UnderlineOptions>({\n  name: 'underline',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'u',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('underline') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['u', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setUnderline: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleUnderline: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetUnderline: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-u': () => this.editor.commands.toggleUnderline(),\n      'Mod-U': () => this.editor.commands.toggleUnderline(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;AAqCa,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,OAAO;QACP,WAAW;QACX,UAAU,WAAW,MAAiB,SAAS,WAAW,IAAI,CAAA,IAAK;MACpE;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG9E,cAAW;AACT,WAAO;MACL,cAAc,MAAM,CAAC,EAAE,SAAQ,MAAM;AACnC,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,iBAAiB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACtC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,gBAAgB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACrC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAe;MACnD,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAe;;;AAGxD,CAAA;", "names": []}
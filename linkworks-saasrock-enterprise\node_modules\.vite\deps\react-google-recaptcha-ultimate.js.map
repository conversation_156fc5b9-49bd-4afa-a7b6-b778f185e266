{"version": 3, "sources": ["../../react-google-recaptcha-ultimate/dist/esm/_rollupPluginBabelHelpers-ee9924ff.js", "../../react-google-recaptcha-ultimate/src/context/GoogleReCaptchaContext.ts", "../../react-google-recaptcha-ultimate/src/context/useGoogleReCaptcha.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/removeGoogleReCaptchaContainer.ts", "../../react-google-recaptcha-ultimate/src/utils/hooks/useIsomorphicLayoutEffect.ts", "../../react-google-recaptcha-ultimate/src/components/GoogleReCaptchaCheckbox.tsx", "../../react-google-recaptcha-ultimate/src/utils/helpers/checkGoogleReCaptchaInjected.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/generateGoogleReCaptchaHiddenBadgeStyles.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/hideGoogleReCaptchaBadge.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/generateGoogleReCaptchaScriptSrc.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/injectGoogleReCaptchaScript.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/removeGoogleReCaptchaBadge.ts", "../../react-google-recaptcha-ultimate/src/utils/helpers/removeGoogleReCaptchaScript.ts", "../../react-google-recaptcha-ultimate/src/context/GoogleReCaptchaProvider.tsx", "../../react-google-recaptcha-ultimate/src/context/withGoogleReCaptcha.tsx", "../../react-google-recaptcha-ultimate/dist/esm/index.js"], "sourcesContent": ["/* @license react-google-recaptcha-ultimate v1.2.2 */\nfunction t(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function r(r){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?t(Object(n),!0).forEach((function(t){i(r,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach((function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(n,t))}))}return r}function e(){e=function(){return r};var t,r={},n=Object.prototype,o=n.hasOwnProperty,i=Object.defineProperty||function(t,r,e){t[r]=e.value},a=\"function\"==typeof Symbol?Symbol:{},c=a.iterator||\"@@iterator\",u=a.asyncIterator||\"@@asyncIterator\",l=a.toStringTag||\"@@toStringTag\";function f(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{f({},\"\")}catch(t){f=function(t,r,e){return t[r]=e}}function s(t,r,e,n){var o=r&&r.prototype instanceof b?r:b,a=Object.create(o.prototype),c=new A(n||[]);return i(a,\"_invoke\",{value:S(t,e,c)}),a}function h(t,r,e){try{return{type:\"normal\",arg:t.call(r,e)}}catch(t){return{type:\"throw\",arg:t}}}r.wrap=s;var p=\"suspendedStart\",y=\"suspendedYield\",v=\"executing\",g=\"completed\",d={};function b(){}function m(){}function w(){}var O={};f(O,c,(function(){return this}));var j=Object.getPrototypeOf,x=j&&j(j(G([])));x&&x!==n&&o.call(x,c)&&(O=x);var E=w.prototype=b.prototype=Object.create(O);function L(t){[\"next\",\"throw\",\"return\"].forEach((function(r){f(t,r,(function(t){return this._invoke(r,t)}))}))}function P(t,r){function e(n,i,a,c){var u=h(t[n],t,i);if(\"throw\"!==u.type){var l=u.arg,f=l.value;return f&&\"object\"==typeof f&&o.call(f,\"__await\")?r.resolve(f.__await).then((function(t){e(\"next\",t,a,c)}),(function(t){e(\"throw\",t,a,c)})):r.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return e(\"throw\",t,a,c)}))}c(u.arg)}var n;i(this,\"_invoke\",{value:function(t,o){function i(){return new r((function(r,n){e(t,o,r,n)}))}return n=n?n.then(i,i):i()}})}function S(r,e,n){var o=p;return function(i,a){if(o===v)throw new Error(\"Generator is already running\");if(o===g){if(\"throw\"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=_(c,n);if(u){if(u===d)continue;return u}}if(\"next\"===n.method)n.sent=n._sent=n.arg;else if(\"throw\"===n.method){if(o===p)throw o=g,n.arg;n.dispatchException(n.arg)}else\"return\"===n.method&&n.abrupt(\"return\",n.arg);o=v;var l=h(r,e,n);if(\"normal\"===l.type){if(o=n.done?g:y,l.arg===d)continue;return{value:l.arg,done:n.done}}\"throw\"===l.type&&(o=g,n.method=\"throw\",n.arg=l.arg)}}}function _(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,\"throw\"===n&&r.iterator.return&&(e.method=\"return\",e.arg=t,_(r,e),\"throw\"===e.method)||\"return\"!==n&&(e.method=\"throw\",e.arg=new TypeError(\"The iterator does not provide a '\"+n+\"' method\")),d;var i=h(o,r.iterator,e.arg);if(\"throw\"===i.type)return e.method=\"throw\",e.arg=i.arg,e.delegate=null,d;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,\"return\"!==e.method&&(e.method=\"next\",e.arg=t),e.delegate=null,d):a:(e.method=\"throw\",e.arg=new TypeError(\"iterator result is not an object\"),e.delegate=null,d)}function k(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function N(t){var r=t.completion||{};r.type=\"normal\",delete r.arg,t.completion=r}function A(t){this.tryEntries=[{tryLoc:\"root\"}],t.forEach(k,this),this.reset(!0)}function G(r){if(r||\"\"===r){var e=r[c];if(e)return e.call(r);if(\"function\"==typeof r.next)return r;if(!isNaN(r.length)){var n=-1,i=function e(){for(;++n<r.length;)if(o.call(r,n))return e.value=r[n],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(typeof r+\" is not iterable\")}return m.prototype=w,i(E,\"constructor\",{value:w,configurable:!0}),i(w,\"constructor\",{value:m,configurable:!0}),m.displayName=f(w,l,\"GeneratorFunction\"),r.isGeneratorFunction=function(t){var r=\"function\"==typeof t&&t.constructor;return!!r&&(r===m||\"GeneratorFunction\"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,l,\"GeneratorFunction\")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},L(P.prototype),f(P.prototype,u,(function(){return this})),r.AsyncIterator=P,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new P(s(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},L(E),f(E,l,\"Generator\"),f(E,c,(function(){return this})),f(E,\"toString\",(function(){return\"[object Generator]\"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=G,A.prototype={constructor:A,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method=\"next\",this.arg=t,this.tryEntries.forEach(N),!r)for(var e in this)\"t\"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if(\"throw\"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function n(n,o){return c.type=\"throw\",c.arg=r,e.next=n,o&&(e.method=\"next\",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if(\"root\"===a.tryLoc)return n(\"end\");if(a.tryLoc<=this.prev){var u=o.call(a,\"catchLoc\"),l=o.call(a,\"finallyLoc\");if(u&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw new Error(\"try statement without catch or finally\");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc<=this.prev&&o.call(n,\"finallyLoc\")&&this.prev<n.finallyLoc){var i=n;break}}i&&(\"break\"===t||\"continue\"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method=\"next\",this.next=i.finallyLoc,d):this.complete(a)},complete:function(t,r){if(\"throw\"===t.type)throw t.arg;return\"break\"===t.type||\"continue\"===t.type?this.next=t.arg:\"return\"===t.type?(this.rval=this.arg=t.arg,this.method=\"return\",this.next=\"end\"):\"normal\"===t.type&&r&&(this.next=r),d},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),N(e),d}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if(\"throw\"===n.type){var o=n.arg;N(e)}return o}}throw new Error(\"illegal catch attempt\")},delegateYield:function(r,e,n){return this.delegate={iterator:G(r),resultName:e,nextLoc:n},\"next\"===this.method&&(this.arg=t),d}},r}function n(t,r,e,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?r(u):Promise.resolve(u).then(n,o)}function o(t){return function(){var r=this,e=arguments;return new Promise((function(o,i){var a=t.apply(r,e);function c(t){n(a,o,i,c,u,\"next\",t)}function u(t){n(a,o,i,c,u,\"throw\",t)}c(void 0)}))}}function i(t,r,e){return(r=function(t){var r=function(t,r){if(\"object\"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||\"default\");if(\"object\"!=typeof n)return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===r?String:Number)(t)}(t,\"string\");return\"symbol\"==typeof r?r:String(r)}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function a(){return a=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var e=arguments[r];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},a.apply(this,arguments)}function c(t,r){if(null==t)return{};var e,n,o=function(t,r){if(null==t)return{};var e,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)e=i[n],r.indexOf(e)>=0||(o[e]=t[e]);return o}(t,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e=i[n],r.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(t,e)&&(o[e]=t[e])}return o}function u(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,r){var e=null==t?null:\"undefined\"!=typeof Symbol&&t[Symbol.iterator]||t[\"@@iterator\"];if(null!=e){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;u=!1}else for(;!(u=(n=i.call(e)).done)&&(c.push(n.value),c.length!==r);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,r)||function(t,r){if(!t)return;if(\"string\"==typeof t)return l(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);\"Object\"===e&&t.constructor&&(e=t.constructor.name);if(\"Map\"===e||\"Set\"===e)return Array.from(t);if(\"Arguments\"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return l(t,r)}(t,r)||function(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}()}function l(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}export{a as _,u as a,r as b,o as c,e as d,c as e};\n//# sourceMappingURL=_rollupPluginBabelHelpers-ee9924ff.js.map\n", "import { createContext } from 'react';\r\n\r\nimport type { Container, GoogleReCaptcha } from '../utils';\r\n\r\nexport interface GoogleReCaptchaContextProps {\r\n  googleReCaptcha: any;\r\n  siteKey: string;\r\n  isLoading: boolean;\r\n  language?: GoogleReCaptcha.Language;\r\n  executeV3?: (action: GoogleReCaptcha.Action['action']) => Promise<string>;\r\n  executeV2Invisible?: (optWidgetId?: GoogleReCaptcha.OptWidgetId) => Promise<void>;\r\n  reset?: (optWidgetId?: GoogleReCaptcha.OptWidgetId) => void;\r\n  getResponse?: (optWidgetId?: GoogleReCaptcha.OptWidgetId) => void;\r\n  render?: (\r\n    container: Container,\r\n    parameters?: GoogleReCaptcha.Parameters,\r\n    inherit?: boolean\r\n  ) => void;\r\n}\r\n\r\nconst contextError =\r\n  'GoogleReCaptcha Context has not yet been implemented, if you are using useGoogleReCaptcha hook, make sure the hook is called inside component wrapped by GoogleRecaptchaProvider';\r\n\r\nexport const GoogleReCaptchaContext = createContext<GoogleReCaptchaContextProps>({\r\n  googleReCaptcha: {},\r\n  siteKey: '',\r\n  language: '',\r\n  isLoading: true,\r\n  executeV3: () => {\r\n    throw Error(contextError);\r\n  },\r\n  executeV2Invisible: () => {\r\n    throw Error(contextError);\r\n  },\r\n  reset: () => {\r\n    throw Error(contextError);\r\n  },\r\n  getResponse: () => {\r\n    throw Error(contextError);\r\n  },\r\n  render: () => {\r\n    throw Error(contextError);\r\n  }\r\n});\r\n\r\nexport const {\r\n  Provider: GoogleReCaptchaContextProvider,\r\n  Consumer: GoogleReCaptchaContextConsumer\r\n} = GoogleReCaptchaContext;\r\n", "import React from 'react';\n\nimport { GoogleReCaptchaContext } from './GoogleReCaptchaContext';\n\nexport const useGoogleReCaptcha = () => React.useContext(GoogleReCaptchaContext);\n", "import type { ContainerId } from '../types';\n\n/**\n * Function to remove google recaptcha сontainer after container unmount.\n * This function is used if the user has not specified a container in an explicit.\n *\n * @category Function\n * @param containerId Container id for google recaptcha.\n */\n\nexport const removeGoogleReCaptchaContainer = (containerId: ContainerId) => {\n  const container = document.getElementById(containerId);\n\n  if (!container) {\n    return;\n  }\n\n  while (container.lastChild) {\n    container.lastChild.remove();\n  }\n};\n", "import { useEffect, useLayoutEffect } from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n", "import type { ComponentProps } from 'react';\r\nimport React from 'react';\r\n\r\nimport { useGoogleReCaptcha } from '../context/useGoogleReCaptcha';\r\nimport type { ContainerId, GoogleReCaptcha } from '../utils';\r\nimport { removeGoogleReCaptchaContainer, useIsomorphicLayoutEffect } from '../utils';\r\n\r\nexport interface GoogleReCaptchaCheckboxProps extends Omit<ComponentProps<'div'>, 'onChange'> {\r\n  id?: string;\r\n  action?: string;\r\n  className?: string;\r\n  container?: ContainerId | HTMLElement;\r\n  callback?: (token: string) => void;\r\n  errorCallback?: () => void;\r\n  expiredCallback?: () => void;\r\n  language?: GoogleReCaptcha.Language;\r\n  theme?: GoogleReCaptcha.Theme;\r\n  size?: GoogleReCaptcha.Size['v2-checkbox'];\r\n  onChange?: (token: string) => void;\r\n}\r\n\r\nconst CHECKBOX_CONTAINER_ID = 'google-recaptcha-checkbox-container';\r\n\r\nexport const GoogleReCaptchaCheckbox: React.FC<GoogleReCaptchaCheckboxProps> = ({\r\n  id = CHECKBOX_CONTAINER_ID,\r\n  onChange,\r\n  action,\r\n  language,\r\n  ...props\r\n}) => {\r\n  const { siteKey, render, language: hl } = useGoogleReCaptcha();\r\n  const googleReCaptchaCheckboxContainerRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    if (!render) return;\r\n    const checkbox = document.createElement('div');\r\n\r\n    const params = {\r\n      sitekey: siteKey,\r\n      callback: onChange,\r\n      ...((language ?? hl) && { hl: language ?? hl }),\r\n      ...props\r\n    } satisfies GoogleReCaptcha.Parameters;\r\n\r\n    if (action) {\r\n      render(checkbox, { ...params, action });\r\n    } else render(checkbox, params);\r\n\r\n    if (googleReCaptchaCheckboxContainerRef.current) {\r\n      googleReCaptchaCheckboxContainerRef.current.appendChild(checkbox);\r\n    }\r\n\r\n    return () => {\r\n      removeGoogleReCaptchaContainer(id);\r\n    };\r\n  }, [render, language, onChange, id, siteKey, props.size, action, props.theme]);\r\n\r\n  return <div id={id} ref={googleReCaptchaCheckboxContainerRef} {...props} />;\r\n};\r\n\r\nexport default GoogleReCaptchaCheckbox;\r\n", "/**\n * This function checks if the script already exists on the page\n *\n * @category Function\n * @param scriptId Script id of google recaptcha.\n * @returns Is script exist value.\n */\n\nexport const checkGoogleReCaptchaInjected = (scriptId: string) =>\n  !!document.querySelector(`#${scriptId}`) ||\n  !!document.querySelector('script[src*=\"/recaptcha/\"]:not([src*=\"gstatic\"])');\n", "/**\n * The function returns hidden styles for google recaptcha badge.\n *\n * @category Function\n * @returns Hidden styles\n */\n\nexport const generateGoogleReCaptchaHiddenBadgeStyles = () =>\n  '.grecaptcha-badge{display: none !important;}';\n", "import { generateGoogleReCaptchaHiddenBadgeStyles } from './generateGoogleReCaptchaHiddenBadgeStyles';\n\n/**\n * A function that hides the google recaptcha badge from the page.\n * This function is executed if the script was implicitly integrated.\n *\n * @category Function\n */\n\nexport const hideGoogleReCaptchaBadge = () => {\n  const style = document.createElement('style');\n  style.innerHTML = generateGoogleReCaptchaHiddenBadgeStyles();\n  document.body.appendChild(style);\n};\n", "import type { GoogleReCaptcha } from '../types';\n\ninterface GenerateGoogleReCaptchaSrcParams {\n  host?: GoogleReCaptcha.Host;\n  isEnterprise?: boolean;\n  render: string;\n  hl?: string;\n  badge?: Exclude<GoogleReCaptcha.Badge, 'hidden'>;\n}\n\n/**\n * Function to generate a script link src for google recaptcha.\n *\n * @category Function\n * @params object The link params.\n * @returns Script link src.\n */\n\nexport const generateGoogleReCaptchaScriptSrc = ({\n  host = 'google.com',\n  isEnterprise = false,\n  render,\n  hl,\n  badge\n}: GenerateGoogleReCaptchaSrcParams) => {\n  const queries = new URLSearchParams({\n    ...(hl && { hl }),\n    ...(badge && { badge }),\n    render\n  });\n\n  const file = isEnterprise ? 'enterprise.js' : 'api.js';\n\n  return `https://www.${host}/recaptcha/${file}?${queries}`;\n};\n", "import type { GoogleReCaptcha } from '../types';\n\nimport { generateGoogleReCaptchaScriptSrc } from './generateGoogleReCaptchaScriptSrc';\n\ninterface InjectGoogleReCaptchaScriptParams extends GoogleReCaptcha.Script {\n  isEnterprise?: boolean;\n  onload: () => void;\n  hl?: string;\n  host?: GoogleReCaptcha.Host;\n  badge?: Exclude<GoogleReCaptcha.Badge, 'hidden'>;\n  render: string;\n}\n\n/**\n * Function to inject a script link src for google recaptcha.\n *\n * @category Function\n * @params object The link and script params.\n */\nexport const injectGoogleReCaptchaScript = ({\n  onload,\n  appendTo = 'head',\n  isEnterprise = false,\n  host,\n  render,\n  badge,\n  hl,\n  ...params\n}: InjectGoogleReCaptchaScriptParams) => {\n  const googleReCaptchaSrc = generateGoogleReCaptchaScriptSrc({\n    host,\n    isEnterprise,\n    render,\n    hl,\n    badge\n  });\n\n  const script = document.createElement('script');\n\n  Object.entries(params).forEach(([key, value]) => {\n    script.setAttribute(key, value?.toString() ?? '');\n  });\n\n  script.src = googleReCaptchaSrc;\n  script.onload = onload;\n\n  document[appendTo].appendChild(script);\n};\n", "/**\n * Function to remove google recaptcha badge after container unmount\n *\n * @category Function\n */\n\nexport const removeGoogleReCaptchaBadge = () => {\n  const nodeBadge = document.querySelector('.grecaptcha-badge');\n  if (nodeBadge && nodeBadge.parentNode) {\n    document.body.removeChild(nodeBadge.parentNode);\n  }\n};\n", "export const GSTATIC_URL = 'https://www.gstatic.com/recaptcha/releases';\n\n/**\n * Function to remove google recaptcha script after container unmount.\n * This function is called if it was not created independently.\n *\n * @category Function\n * @param scriptId Script id of google recaptcha.\n */\n\nexport const removeGoogleReCaptchaScript = (scriptId: string) => {\n  // eslint-disable-next-line no-underscore-dangle\n  (window as any).___grecaptcha_cfg = undefined;\n\n  const googleReCaptchaScript = document.querySelector(`#${scriptId}`);\n  if (googleReCaptchaScript) {\n    googleReCaptchaScript.remove();\n  }\n\n  const gStaticScript = document.querySelector(`script[src^=\"${GSTATIC_URL}\"]`);\n\n  if (gStaticScript) {\n    gStaticScript.remove();\n  }\n};\n", "import React from 'react';\n\nimport type { ContainerId, GoogleReCaptcha } from '../utils';\nimport {\n  checkGoogleReCaptchaInjected,\n  hideGoogleReCaptchaBadge,\n  injectGoogleRe<PERSON>aptcha<PERSON>,\n  removeGoogleReCaptchaBadge,\n  removeGoogleReCaptchaContainer,\n  removeGoogleReCaptchaScript\n} from '../utils';\n\nimport { GoogleReCaptchaContextProvider } from './GoogleReCaptchaContext';\n\ninterface GoogleReCaptchaDefaultProviderProps {\n  siteKey: string;\n  language?: string;\n  isEnterprise?: boolean;\n  onLoad?: (googleReCaptcha: GoogleReCaptcha.Instance) => Promise<void> | void;\n  onError?: () => Promise<void>;\n  host?: GoogleReCaptcha.Host;\n  children?: React.ReactNode;\n  scriptProps?: GoogleReCaptcha.Script;\n}\n\ninterface Explicit {\n  container?: ContainerId | HTMLElement;\n  tabIndex?: number;\n  inherit?: boolean;\n  callback?: (token: string) => void;\n  errorCallback?: () => void;\n  expiredCallback?: () => void;\n}\n\nexport interface GoogleReCaptchaV2InvisibleProviderProps\n  extends GoogleReCaptchaDefaultProviderProps {\n  type: Extract<GoogleReCaptcha.Type, 'v2-invisible'>;\n  explicit?: Explicit & {\n    badge?: GoogleReCaptcha.Badge;\n  };\n}\nexport interface GoogleReCaptchaV2CheckBoxProviderProps\n  extends GoogleReCaptchaDefaultProviderProps {\n  type: Extract<GoogleReCaptcha.Type, 'v2-checkbox'>;\n  explicit?: Explicit & {\n    container: ContainerId | HTMLElement;\n    action?: GoogleReCaptcha.Action['action'];\n    theme?: GoogleReCaptcha.Theme;\n    size?: GoogleReCaptcha.Size['v2-checkbox'];\n  };\n}\nexport interface GoogleReCaptchaV3ProviderProps extends GoogleReCaptchaDefaultProviderProps {\n  type: Extract<GoogleReCaptcha.Type, 'v3'>;\n  explicit?: Explicit & {\n    badge?: GoogleReCaptcha.Badge;\n  };\n}\n\nexport type GoogleReCaptchaProviderProps =\n  | GoogleReCaptchaV3ProviderProps\n  | GoogleReCaptchaV2InvisibleProviderProps\n  | GoogleReCaptchaV2CheckBoxProviderProps;\n\nconst onLoadCallbackName = 'onGoogleReCaptchaLoad';\nconst containerId = 'google-recaptcha-container';\n\nexport const GoogleReCaptchaProvider: React.FC<GoogleReCaptchaProviderProps> = ({\n  type,\n  siteKey,\n  language,\n  scriptProps,\n  isEnterprise = false,\n  host,\n  children,\n  explicit,\n  onLoad,\n  onError\n}) => {\n  const [isLoading, setIsLoading] = React.useState(true);\n  const [googleReCaptchaInstance, setGoogleReCaptchaInstance] =\n    React.useState<GoogleReCaptcha.Instance>();\n\n  React.useEffect(() => {\n    if (!siteKey) {\n      throw new Error('Google ReCaptcha site key not provided');\n    }\n\n    const scriptId = scriptProps?.id ?? 'google-recaptcha-script';\n    const isGoogleReCaptchaInjected = checkGoogleReCaptchaInjected(scriptId);\n\n    const onload = () => {\n      const googleReCaptcha: GoogleReCaptcha.Instance = isEnterprise\n        ? (window as any).grecaptcha?.enterprise\n        : (window as any).grecaptcha;\n\n      if (!googleReCaptcha) {\n        if (onError) onError();\n        return;\n      }\n\n      if (!explicit) {\n        googleReCaptcha.ready(async () => {\n          setGoogleReCaptchaInstance(googleReCaptcha);\n          if (onLoad) await onLoad(googleReCaptcha);\n          setIsLoading(true);\n        });\n      }\n\n      if (explicit) {\n        const params = {\n          size: type === 'v3' || type === 'v2-invisible' ? 'invisible' : 'normal',\n          ...((type === 'v3' || type === 'v2-invisible') && ({ badge: 'bottomright' } as const)),\n          sitekey: siteKey,\n          ...explicit\n        } as const;\n\n        if (!isGoogleReCaptchaInjected) {\n          const isV3AndV2OptWidgetHidden =\n            (type === 'v3' || type === 'v2-invisible') && explicit?.badge === 'hidden';\n\n          if (isV3AndV2OptWidgetHidden) {\n            hideGoogleReCaptchaBadge();\n          }\n        }\n\n        googleReCaptcha.ready(async () => {\n          if (explicit.container) {\n            googleReCaptcha.render(explicit.container, params, !!explicit.inherit);\n          }\n\n          setGoogleReCaptchaInstance(googleReCaptcha);\n\n          if (onLoad) await onLoad(googleReCaptcha);\n          setIsLoading(true);\n        });\n      }\n    };\n    (window as unknown as { [key: string]: () => void })[onLoadCallbackName] = onload;\n\n    if (isGoogleReCaptchaInjected) {\n      onload();\n    } else {\n      injectGoogleReCaptchaScript({\n        isEnterprise,\n        host,\n        ...((type === 'v3' || type === 'v2-invisible') &&\n          explicit?.badge && {\n            badge: explicit?.badge === 'hidden' ? 'bottomright' : explicit?.badge\n          }),\n        ...(language && { hl: language }),\n        render:\n          ((type === 'v3' || type === 'v2-invisible') && explicit?.container) ||\n          type === 'v2-checkbox'\n            ? 'explicit'\n            : siteKey,\n        ...scriptProps,\n        onload,\n        id: scriptId\n      });\n    }\n\n    return () => {\n      googleReCaptchaInstance?.reset();\n      if (!isGoogleReCaptchaInjected) removeGoogleReCaptchaScript(scriptId);\n      if ((type === 'v3' || type === 'v2-invisible') && !explicit?.container && explicit?.badge) {\n        removeGoogleReCaptchaContainer(containerId);\n      } else {\n        removeGoogleReCaptchaBadge();\n      }\n    };\n  }, [isEnterprise, language, host]);\n\n  const executeV3 = React.useCallback(\n    (action: GoogleReCaptcha.Action['action']) => {\n      if (!googleReCaptchaInstance?.execute) {\n        throw new Error('Google ReCaptcha has not been loaded');\n      }\n\n      return googleReCaptchaInstance.execute(siteKey, { action });\n    },\n    [googleReCaptchaInstance]\n  );\n\n  const executeV2Invisible = React.useCallback(\n    (optWidgetId?: GoogleReCaptcha.OptWidgetId) => {\n      if (!googleReCaptchaInstance?.execute) {\n        throw new Error('Google ReCaptcha has not been loaded');\n      }\n\n      return googleReCaptchaInstance.execute(optWidgetId);\n    },\n    [googleReCaptchaInstance]\n  );\n\n  const value = React.useMemo(\n    () => ({\n      googleReCaptcha: googleReCaptchaInstance,\n      siteKey,\n      isLoading,\n      executeV2Invisible,\n      executeV3,\n      reset: googleReCaptchaInstance?.reset,\n      getResponse: googleReCaptchaInstance?.getResponse,\n      render: googleReCaptchaInstance?.render,\n      ...(language && { language })\n    }),\n    [siteKey, googleReCaptchaInstance, isLoading, language]\n  );\n\n  return <GoogleReCaptchaContextProvider value={value}>{children}</GoogleReCaptchaContextProvider>;\n};\n", "import React from 'react';\r\n\r\nimport type { GoogleReCaptchaContextProps } from './GoogleReCaptchaContext';\r\nimport { GoogleReCaptchaContextConsumer } from './GoogleReCaptchaContext';\r\n\r\nexport interface WithGoogleReCaptchaParams {\r\n  googleReCaptcha: GoogleReCaptchaContextProps;\r\n}\r\n\r\nexport const withGoogleReCaptcha = <OwnProps,>(\r\n  Component: React.ComponentType<OwnProps & WithGoogleReCaptchaParams>\r\n) => {\r\n  const WithGoogleReCaptchaComponent = (props: OwnProps & Partial<WithGoogleReCaptchaParams>) => (\r\n    <GoogleReCaptchaContextConsumer>\r\n      {(value) => <Component {...props} googleReCaptcha={value} />}\r\n    </GoogleReCaptchaContextConsumer>\r\n  );\r\n\r\n  WithGoogleReCaptchaComponent.displayName = `withGoogleReCaptcha(${\r\n    Component.displayName || Component.name || 'Component'\r\n  })`;\r\n\r\n  return WithGoogleReCaptchaComponent;\r\n};\r\n", "/* @license react-google-recaptcha-ultimate v1.2.2 */\nexport{GoogleReCaptchaCheckbox}from\"./components/GoogleReCaptchaCheckbox.js\";export{GoogleReCaptchaProvider}from\"./context/GoogleReCaptchaProvider.js\";export{useGoogleReCaptcha}from\"./context/useGoogleReCaptcha.js\";export{withGoogleReCaptcha}from\"./context/withGoogleReCaptcha.js\";export{generateGoogleReCaptchaHiddenBadgeStyles}from\"./utils/helpers/generateGoogleReCaptchaHiddenBadgeStyles.js\";export{generateGoogleReCaptchaScriptSrc}from\"./utils/helpers/generateGoogleReCaptchaScriptSrc.js\";import\"./_rollupPluginBabelHelpers-ee9924ff.js\";import\"react\";import\"./utils/helpers/removeGoogleReCaptchaContainer.js\";import\"./utils/hooks/useIsomorphicLayoutEffect.js\";import\"./utils/helpers/checkGoogleReCaptchaInjected.js\";import\"./utils/helpers/hideGoogleReCaptchaBadge.js\";import\"./utils/helpers/injectGoogleReCaptchaScript.js\";import\"./utils/helpers/removeGoogleReCaptchaBadge.js\";import\"./utils/helpers/removeGoogleReCaptchaScript.js\";import\"./context/GoogleReCaptchaContext.js\";\n//# sourceMappingURL=index.js.map\n"], "mappings": ";;;;;;;;AACA,SAAS,EAAEA,IAAEC,IAAE;AAAC,MAAIC,KAAE,OAAO,KAAKF,EAAC;AAAE,MAAG,OAAO,uBAAsB;AAAC,QAAIG,KAAE,OAAO,sBAAsBH,EAAC;AAAE,IAAAC,OAAIE,KAAEA,GAAE,OAAQ,SAASF,IAAE;AAAC,aAAO,OAAO,yBAAyBD,IAAEC,EAAC,EAAE;AAAA,IAAU,CAAE,IAAGC,GAAE,KAAK,MAAMA,IAAEC,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,QAAIC,KAAE,QAAM,UAAUD,EAAC,IAAE,UAAUA,EAAC,IAAE,CAAC;AAAE,IAAAA,KAAE,IAAE,EAAE,OAAOC,EAAC,GAAE,IAAE,EAAE,QAAS,SAASH,IAAE;AAAC,QAAEC,IAAED,IAAEG,GAAEH,EAAC,CAAC;AAAA,IAAC,CAAE,IAAE,OAAO,4BAA0B,OAAO,iBAAiBC,IAAE,OAAO,0BAA0BE,EAAC,CAAC,IAAE,EAAE,OAAOA,EAAC,CAAC,EAAE,QAAS,SAASH,IAAE;AAAC,aAAO,eAAeC,IAAED,IAAE,OAAO,yBAAyBG,IAAEH,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,IAAG;AAAC,MAAE,WAAU;AAAC,WAAOA;AAAA,EAAC;AAAE,MAAID,IAAEC,KAAE,CAAC,GAAEE,KAAE,OAAO,WAAUC,KAAED,GAAE,gBAAeE,KAAE,OAAO,kBAAgB,SAASL,IAAEC,IAAEC,IAAE;AAAC,IAAAF,GAAEC,EAAC,IAAEC,GAAE;AAAA,EAAK,GAAEI,KAAE,cAAY,OAAO,SAAO,SAAO,CAAC,GAAEC,KAAED,GAAE,YAAU,cAAaE,KAAEF,GAAE,iBAAe,mBAAkBG,KAAEH,GAAE,eAAa;AAAgB,WAAS,EAAEN,IAAEC,IAAEC,IAAE;AAAC,WAAO,OAAO,eAAeF,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,GAAEF,GAAEC,EAAC;AAAA,EAAC;AAAC,MAAG;AAAC,MAAE,CAAC,GAAE,EAAE;AAAA,EAAC,SAAOD,IAAE;AAAC,QAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,aAAOF,GAAEC,EAAC,IAAEC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAEH,MAAGA,GAAE,qBAAqB,IAAEA,KAAE,GAAEK,KAAE,OAAO,OAAOF,GAAE,SAAS,GAAEG,KAAE,IAAI,EAAEJ,MAAG,CAAC,CAAC;AAAE,WAAOE,GAAEC,IAAE,WAAU,EAAC,OAAM,EAAEN,IAAEE,IAAEK,EAAC,EAAC,CAAC,GAAED;AAAA,EAAC;AAAC,WAAS,EAAEN,IAAEC,IAAEC,IAAE;AAAC,QAAG;AAAC,aAAM,EAAC,MAAK,UAAS,KAAIF,GAAE,KAAKC,IAAEC,EAAC,EAAC;AAAA,IAAC,SAAOF,IAAE;AAAC,aAAM,EAAC,MAAK,SAAQ,KAAIA,GAAC;AAAA,IAAC;AAAA,EAAC;AAAC,EAAAC,GAAE,OAAK;AAAE,MAAI,IAAE,kBAAiB,IAAE,kBAAiB,IAAE,aAAY,IAAE,aAAYS,KAAE,CAAC;AAAE,WAAS,IAAG;AAAA,EAAC;AAAC,WAAS,IAAG;AAAA,EAAC;AAAC,WAAS,IAAG;AAAA,EAAC;AAAC,MAAI,IAAE,CAAC;AAAE,IAAE,GAAEH,IAAG,WAAU;AAAC,WAAO;AAAA,EAAI,CAAE;AAAE,MAAI,IAAE,OAAO,gBAAe,IAAE,KAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAAE,OAAG,MAAIJ,MAAGC,GAAE,KAAK,GAAEG,EAAC,MAAI,IAAE;AAAG,MAAI,IAAE,EAAE,YAAU,EAAE,YAAU,OAAO,OAAO,CAAC;AAAE,WAAS,EAAEP,IAAE;AAAC,KAAC,QAAO,SAAQ,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,QAAED,IAAEC,IAAG,SAASD,IAAE;AAAC,eAAO,KAAK,QAAQC,IAAED,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAEC,IAAE;AAAC,aAASC,GAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,UAAIC,KAAE,EAAER,GAAEG,EAAC,GAAEH,IAAEK,EAAC;AAAE,UAAG,YAAUG,GAAE,MAAK;AAAC,YAAIC,KAAED,GAAE,KAAIG,KAAEF,GAAE;AAAM,eAAOE,MAAG,YAAU,OAAOA,MAAGP,GAAE,KAAKO,IAAE,SAAS,IAAEV,GAAE,QAAQU,GAAE,OAAO,EAAE,KAAM,SAASX,IAAE;AAAC,UAAAE,GAAE,QAAOF,IAAEM,IAAEC,EAAC;AAAA,QAAC,GAAI,SAASP,IAAE;AAAC,UAAAE,GAAE,SAAQF,IAAEM,IAAEC,EAAC;AAAA,QAAC,CAAE,IAAEN,GAAE,QAAQU,EAAC,EAAE,KAAM,SAASX,IAAE;AAAC,UAAAS,GAAE,QAAMT,IAAEM,GAAEG,EAAC;AAAA,QAAC,GAAI,SAAST,IAAE;AAAC,iBAAOE,GAAE,SAAQF,IAAEM,IAAEC,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,MAAAA,GAAEC,GAAE,GAAG;AAAA,IAAC;AAAC,QAAIL;AAAE,IAAAE,GAAE,MAAK,WAAU,EAAC,OAAM,SAASL,IAAEI,IAAE;AAAC,eAASC,KAAG;AAAC,eAAO,IAAIJ,GAAG,SAASA,IAAEE,IAAE;AAAC,UAAAD,GAAEF,IAAEI,IAAEH,IAAEE,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,aAAOA,KAAEA,KAAEA,GAAE,KAAKE,IAAEA,EAAC,IAAEA,GAAE;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,WAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE;AAAE,WAAO,SAASC,IAAEC,IAAE;AAAC,UAAGF,OAAI,EAAE,OAAM,IAAI,MAAM,8BAA8B;AAAE,UAAGA,OAAI,GAAE;AAAC,YAAG,YAAUC,GAAE,OAAMC;AAAE,eAAM,EAAC,OAAMN,IAAE,MAAK,KAAE;AAAA,MAAC;AAAC,WAAIG,GAAE,SAAOE,IAAEF,GAAE,MAAIG,QAAI;AAAC,YAAIC,KAAEJ,GAAE;AAAS,YAAGI,IAAE;AAAC,cAAIC,KAAE,EAAED,IAAEJ,EAAC;AAAE,cAAGK,IAAE;AAAC,gBAAGA,OAAIE,GAAE;AAAS,mBAAOF;AAAA,UAAC;AAAA,QAAC;AAAC,YAAG,WAASL,GAAE,OAAO,CAAAA,GAAE,OAAKA,GAAE,QAAMA,GAAE;AAAA,iBAAY,YAAUA,GAAE,QAAO;AAAC,cAAGC,OAAI,EAAE,OAAMA,KAAE,GAAED,GAAE;AAAI,UAAAA,GAAE,kBAAkBA,GAAE,GAAG;AAAA,QAAC,MAAK,cAAWA,GAAE,UAAQA,GAAE,OAAO,UAASA,GAAE,GAAG;AAAE,QAAAC,KAAE;AAAE,YAAIK,KAAE,EAAER,IAAEC,IAAEC,EAAC;AAAE,YAAG,aAAWM,GAAE,MAAK;AAAC,cAAGL,KAAED,GAAE,OAAK,IAAE,GAAEM,GAAE,QAAMC,GAAE;AAAS,iBAAM,EAAC,OAAMD,GAAE,KAAI,MAAKN,GAAE,KAAI;AAAA,QAAC;AAAC,oBAAUM,GAAE,SAAOL,KAAE,GAAED,GAAE,SAAO,SAAQA,GAAE,MAAIM,GAAE;AAAA,MAAI;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,EAAER,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE,QAAOE,KAAEH,GAAE,SAASE,EAAC;AAAE,QAAGC,OAAIJ,GAAE,QAAOE,GAAE,WAAS,MAAK,YAAUC,MAAGF,GAAE,SAAS,WAASC,GAAE,SAAO,UAASA,GAAE,MAAIF,IAAE,EAAEC,IAAEC,EAAC,GAAE,YAAUA,GAAE,WAAS,aAAWC,OAAID,GAAE,SAAO,SAAQA,GAAE,MAAI,IAAI,UAAU,sCAAoCC,KAAE,UAAU,IAAGO;AAAE,QAAIL,KAAE,EAAED,IAAEH,GAAE,UAASC,GAAE,GAAG;AAAE,QAAG,YAAUG,GAAE,KAAK,QAAOH,GAAE,SAAO,SAAQA,GAAE,MAAIG,GAAE,KAAIH,GAAE,WAAS,MAAKQ;AAAE,QAAIJ,KAAED,GAAE;AAAI,WAAOC,KAAEA,GAAE,QAAMJ,GAAED,GAAE,UAAU,IAAEK,GAAE,OAAMJ,GAAE,OAAKD,GAAE,SAAQ,aAAWC,GAAE,WAASA,GAAE,SAAO,QAAOA,GAAE,MAAIF,KAAGE,GAAE,WAAS,MAAKQ,MAAGJ,MAAGJ,GAAE,SAAO,SAAQA,GAAE,MAAI,IAAI,UAAU,kCAAkC,GAAEA,GAAE,WAAS,MAAKQ;AAAA,EAAE;AAAC,WAAS,EAAEV,IAAE;AAAC,QAAIC,KAAE,EAAC,QAAOD,GAAE,CAAC,EAAC;AAAE,SAAKA,OAAIC,GAAE,WAASD,GAAE,CAAC,IAAG,KAAKA,OAAIC,GAAE,aAAWD,GAAE,CAAC,GAAEC,GAAE,WAASD,GAAE,CAAC,IAAG,KAAK,WAAW,KAAKC,EAAC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,QAAIC,KAAED,GAAE,cAAY,CAAC;AAAE,IAAAC,GAAE,OAAK,UAAS,OAAOA,GAAE,KAAID,GAAE,aAAWC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,SAAK,aAAW,CAAC,EAAC,QAAO,OAAM,CAAC,GAAEA,GAAE,QAAQ,GAAE,IAAI,GAAE,KAAK,MAAM,IAAE;AAAA,EAAC;AAAC,WAAS,EAAEC,IAAE;AAAC,QAAGA,MAAG,OAAKA,IAAE;AAAC,UAAIC,KAAED,GAAEM,EAAC;AAAE,UAAGL,GAAE,QAAOA,GAAE,KAAKD,EAAC;AAAE,UAAG,cAAY,OAAOA,GAAE,KAAK,QAAOA;AAAE,UAAG,CAAC,MAAMA,GAAE,MAAM,GAAE;AAAC,YAAIE,KAAE,IAAGE,KAAE,SAASH,KAAG;AAAC,iBAAK,EAAEC,KAAEF,GAAE,SAAQ,KAAGG,GAAE,KAAKH,IAAEE,EAAC,EAAE,QAAOD,GAAE,QAAMD,GAAEE,EAAC,GAAED,GAAE,OAAK,OAAGA;AAAE,iBAAOA,GAAE,QAAMF,IAAEE,GAAE,OAAK,MAAGA;AAAA,QAAC;AAAE,eAAOG,GAAE,OAAKA;AAAA,MAAC;AAAA,IAAC;AAAC,UAAM,IAAI,UAAU,OAAOJ,KAAE,kBAAkB;AAAA,EAAC;AAAC,SAAO,EAAE,YAAU,GAAEI,GAAE,GAAE,eAAc,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC,GAAEA,GAAE,GAAE,eAAc,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC,GAAE,EAAE,cAAY,EAAE,GAAEI,IAAE,mBAAmB,GAAER,GAAE,sBAAoB,SAASD,IAAE;AAAC,QAAIC,KAAE,cAAY,OAAOD,MAAGA,GAAE;AAAY,WAAM,CAAC,CAACC,OAAIA,OAAI,KAAG,yBAAuBA,GAAE,eAAaA,GAAE;AAAA,EAAM,GAAEA,GAAE,OAAK,SAASD,IAAE;AAAC,WAAO,OAAO,iBAAe,OAAO,eAAeA,IAAE,CAAC,KAAGA,GAAE,YAAU,GAAE,EAAEA,IAAES,IAAE,mBAAmB,IAAGT,GAAE,YAAU,OAAO,OAAO,CAAC,GAAEA;AAAA,EAAC,GAAEC,GAAE,QAAM,SAASD,IAAE;AAAC,WAAM,EAAC,SAAQA,GAAC;AAAA,EAAC,GAAE,EAAE,EAAE,SAAS,GAAE,EAAE,EAAE,WAAUQ,IAAG,WAAU;AAAC,WAAO;AAAA,EAAI,CAAE,GAAEP,GAAE,gBAAc,GAAEA,GAAE,QAAM,SAASD,IAAEE,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAASA,OAAIA,KAAE;AAAS,QAAIC,KAAE,IAAI,EAAE,EAAEN,IAAEE,IAAEC,IAAEC,EAAC,GAAEC,EAAC;AAAE,WAAOJ,GAAE,oBAAoBC,EAAC,IAAEI,KAAEA,GAAE,KAAK,EAAE,KAAM,SAASN,IAAE;AAAC,aAAOA,GAAE,OAAKA,GAAE,QAAMM,GAAE,KAAK;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,CAAC,GAAE,EAAE,GAAEG,IAAE,WAAW,GAAE,EAAE,GAAEF,IAAG,WAAU;AAAC,WAAO;AAAA,EAAI,CAAE,GAAE,EAAE,GAAE,YAAY,WAAU;AAAC,WAAM;AAAA,EAAoB,CAAE,GAAEN,GAAE,OAAK,SAASD,IAAE;AAAC,QAAIC,KAAE,OAAOD,EAAC,GAAEE,KAAE,CAAC;AAAE,aAAQC,MAAKF,GAAE,CAAAC,GAAE,KAAKC,EAAC;AAAE,WAAOD,GAAE,QAAQ,GAAE,SAASF,KAAG;AAAC,aAAKE,GAAE,UAAQ;AAAC,YAAIC,KAAED,GAAE,IAAI;AAAE,YAAGC,MAAKF,GAAE,QAAOD,GAAE,QAAMG,IAAEH,GAAE,OAAK,OAAGA;AAAA,MAAC;AAAC,aAAOA,GAAE,OAAK,MAAGA;AAAA,IAAC;AAAA,EAAC,GAAEC,GAAE,SAAO,GAAE,EAAE,YAAU,EAAC,aAAY,GAAE,OAAM,SAASA,IAAE;AAAC,QAAG,KAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,KAAK,QAAMD,IAAE,KAAK,OAAK,OAAG,KAAK,WAAS,MAAK,KAAK,SAAO,QAAO,KAAK,MAAIA,IAAE,KAAK,WAAW,QAAQ,CAAC,GAAE,CAACC,GAAE,UAAQC,MAAK,KAAK,SAAMA,GAAE,OAAO,CAAC,KAAGE,GAAE,KAAK,MAAKF,EAAC,KAAG,CAAC,MAAM,CAACA,GAAE,MAAM,CAAC,CAAC,MAAI,KAAKA,EAAC,IAAEF;AAAA,EAAE,GAAE,MAAK,WAAU;AAAC,SAAK,OAAK;AAAG,QAAIA,KAAE,KAAK,WAAW,CAAC,EAAE;AAAW,QAAG,YAAUA,GAAE,KAAK,OAAMA,GAAE;AAAI,WAAO,KAAK;AAAA,EAAI,GAAE,mBAAkB,SAASC,IAAE;AAAC,QAAG,KAAK,KAAK,OAAMA;AAAE,QAAIC,KAAE;AAAK,aAASC,GAAEA,IAAEC,IAAE;AAAC,aAAOG,GAAE,OAAK,SAAQA,GAAE,MAAIN,IAAEC,GAAE,OAAKC,IAAEC,OAAIF,GAAE,SAAO,QAAOA,GAAE,MAAIF,KAAG,CAAC,CAACI;AAAA,IAAC;AAAC,aAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,UAAIC,KAAE,KAAK,WAAWD,EAAC,GAAEE,KAAED,GAAE;AAAW,UAAG,WAASA,GAAE,OAAO,QAAOH,GAAE,KAAK;AAAE,UAAGG,GAAE,UAAQ,KAAK,MAAK;AAAC,YAAIE,KAAEJ,GAAE,KAAKE,IAAE,UAAU,GAAEG,KAAEL,GAAE,KAAKE,IAAE,YAAY;AAAE,YAAGE,MAAGC,IAAE;AAAC,cAAG,KAAK,OAAKH,GAAE,SAAS,QAAOH,GAAEG,GAAE,UAAS,IAAE;AAAE,cAAG,KAAK,OAAKA,GAAE,WAAW,QAAOH,GAAEG,GAAE,UAAU;AAAA,QAAC,WAASE,IAAE;AAAC,cAAG,KAAK,OAAKF,GAAE,SAAS,QAAOH,GAAEG,GAAE,UAAS,IAAE;AAAA,QAAC,OAAK;AAAC,cAAG,CAACG,GAAE,OAAM,IAAI,MAAM,wCAAwC;AAAE,cAAG,KAAK,OAAKH,GAAE,WAAW,QAAOH,GAAEG,GAAE,UAAU;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC,GAAE,QAAO,SAASN,IAAEC,IAAE;AAAC,aAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,UAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,UAAGC,GAAE,UAAQ,KAAK,QAAMC,GAAE,KAAKD,IAAE,YAAY,KAAG,KAAK,OAAKA,GAAE,YAAW;AAAC,YAAIE,KAAEF;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,IAAAE,OAAI,YAAUL,MAAG,eAAaA,OAAIK,GAAE,UAAQJ,MAAGA,MAAGI,GAAE,eAAaA,KAAE;AAAM,QAAIC,KAAED,KAAEA,GAAE,aAAW,CAAC;AAAE,WAAOC,GAAE,OAAKN,IAAEM,GAAE,MAAIL,IAAEI,MAAG,KAAK,SAAO,QAAO,KAAK,OAAKA,GAAE,YAAWK,MAAG,KAAK,SAASJ,EAAC;AAAA,EAAC,GAAE,UAAS,SAASN,IAAEC,IAAE;AAAC,QAAG,YAAUD,GAAE,KAAK,OAAMA,GAAE;AAAI,WAAM,YAAUA,GAAE,QAAM,eAAaA,GAAE,OAAK,KAAK,OAAKA,GAAE,MAAI,aAAWA,GAAE,QAAM,KAAK,OAAK,KAAK,MAAIA,GAAE,KAAI,KAAK,SAAO,UAAS,KAAK,OAAK,SAAO,aAAWA,GAAE,QAAMC,OAAI,KAAK,OAAKA,KAAGS;AAAA,EAAC,GAAE,QAAO,SAASV,IAAE;AAAC,aAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,UAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,UAAGC,GAAE,eAAaF,GAAE,QAAO,KAAK,SAASE,GAAE,YAAWA,GAAE,QAAQ,GAAE,EAAEA,EAAC,GAAEQ;AAAA,IAAC;AAAA,EAAC,GAAE,OAAM,SAASV,IAAE;AAAC,aAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,UAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,UAAGC,GAAE,WAASF,IAAE;AAAC,YAAIG,KAAED,GAAE;AAAW,YAAG,YAAUC,GAAE,MAAK;AAAC,cAAIC,KAAED,GAAE;AAAI,YAAED,EAAC;AAAA,QAAC;AAAC,eAAOE;AAAA,MAAC;AAAA,IAAC;AAAC,UAAM,IAAI,MAAM,uBAAuB;AAAA,EAAC,GAAE,eAAc,SAASH,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,WAAS,EAAC,UAAS,EAAEF,EAAC,GAAE,YAAWC,IAAE,SAAQC,GAAC,GAAE,WAAS,KAAK,WAAS,KAAK,MAAIH,KAAGU;AAAA,EAAC,EAAC,GAAET;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG;AAAC,QAAIC,KAAEP,GAAEK,EAAC,EAAEC,EAAC,GAAEE,KAAED,GAAE;AAAA,EAAK,SAAOP,IAAE;AAAC,WAAO,KAAKE,GAAEF,EAAC;AAAA,EAAC;AAAC,EAAAO,GAAE,OAAKN,GAAEO,EAAC,IAAE,QAAQ,QAAQA,EAAC,EAAE,KAAKL,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAE;AAAC,SAAO,WAAU;AAAC,QAAIC,KAAE,MAAKC,KAAE;AAAU,WAAO,IAAI,QAAS,SAASE,IAAEC,IAAE;AAAC,UAAIC,KAAEN,GAAE,MAAMC,IAAEC,EAAC;AAAE,eAASK,GAAEP,IAAE;AAAC,UAAEM,IAAEF,IAAEC,IAAEE,IAAEC,IAAE,QAAOR,EAAC;AAAA,MAAC;AAAC,eAASQ,GAAER,IAAE;AAAC,UAAEM,IAAEF,IAAEC,IAAEE,IAAEC,IAAE,SAAQR,EAAC;AAAA,MAAC;AAAC,MAAAO,GAAE,MAAM;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEP,IAAEC,IAAEC,IAAE;AAAC,UAAOD,KAAE,SAASD,IAAE;AAAC,QAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,UAAG,YAAU,OAAOD,MAAG,SAAOA,GAAE,QAAOA;AAAE,UAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,UAAG,WAASE,IAAE;AAAC,YAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,YAAG,YAAU,OAAOE,GAAE,QAAOA;AAAE,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAC;AAAC,cAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,IAAC,EAAEA,IAAE,QAAQ;AAAE,WAAM,YAAU,OAAOC,KAAEA,KAAE,OAAOA,EAAC;AAAA,EAAC,EAAEA,EAAC,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC,IAAEF;AAAC;AAAC,SAAS,IAAG;AAAC,SAAO,IAAE,OAAO,SAAO,OAAO,OAAO,KAAK,IAAE,SAASA,IAAE;AAAC,aAAQC,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,UAAIC,KAAE,UAAUD,EAAC;AAAE,eAAQE,MAAKD,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEC,EAAC,MAAIH,GAAEG,EAAC,IAAED,GAAEC,EAAC;AAAA,IAAE;AAAC,WAAOH;AAAA,EAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,MAAG,QAAMD,GAAE,QAAM,CAAC;AAAE,MAAIE,IAAEC,IAAEC,KAAE,SAASJ,IAAEC,IAAE;AAAC,QAAG,QAAMD,GAAE,QAAM,CAAC;AAAE,QAAIE,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAE,OAAO,KAAKL,EAAC;AAAE,SAAIG,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,CAAAD,KAAEG,GAAEF,EAAC,GAAEF,GAAE,QAAQC,EAAC,KAAG,MAAIE,GAAEF,EAAC,IAAEF,GAAEE,EAAC;AAAG,WAAOE;AAAA,EAAC,EAAEJ,IAAEC,EAAC;AAAE,MAAG,OAAO,uBAAsB;AAAC,QAAII,KAAE,OAAO,sBAAsBL,EAAC;AAAE,SAAIG,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,CAAAD,KAAEG,GAAEF,EAAC,GAAEF,GAAE,QAAQC,EAAC,KAAG,KAAG,OAAO,UAAU,qBAAqB,KAAKF,IAAEE,EAAC,MAAIE,GAAEF,EAAC,IAAEF,GAAEE,EAAC;AAAA,EAAE;AAAC,SAAOE;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,SAAO,SAASD,IAAE;AAAC,QAAG,MAAM,QAAQA,EAAC,EAAE,QAAOA;AAAA,EAAC,EAAEA,EAAC,KAAG,SAASA,IAAEC,IAAE;AAAC,QAAIC,KAAE,QAAMF,KAAE,OAAK,eAAa,OAAO,UAAQA,GAAE,OAAO,QAAQ,KAAGA,GAAE,YAAY;AAAE,QAAG,QAAME,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAE,MAAGC,KAAE;AAAG,UAAG;AAAC,YAAGJ,MAAGH,KAAEA,GAAE,KAAKF,EAAC,GAAG,MAAK,MAAIC,IAAE;AAAC,cAAG,OAAOC,EAAC,MAAIA,GAAE;AAAO,UAAAM,KAAE;AAAA,QAAE,MAAM,QAAK,EAAEA,MAAGL,KAAEE,GAAE,KAAKH,EAAC,GAAG,UAAQK,GAAE,KAAKJ,GAAE,KAAK,GAAEI,GAAE,WAASN,KAAGO,KAAE,KAAG;AAAA,MAAC,SAAOR,IAAE;AAAC,QAAAS,KAAE,MAAGL,KAAEJ;AAAA,MAAC,UAAC;AAAQ,YAAG;AAAC,cAAG,CAACQ,MAAG,QAAMN,GAAE,WAASI,KAAEJ,GAAE,OAAO,GAAE,OAAOI,EAAC,MAAIA,IAAG;AAAA,QAAM,UAAC;AAAQ,cAAGG,GAAE,OAAML;AAAA,QAAC;AAAA,MAAC;AAAC,aAAOG;AAAA,IAAC;AAAA,EAAC,EAAEP,IAAEC,EAAC,KAAG,SAASD,IAAEC,IAAE;AAAC,QAAG,CAACD,GAAE;AAAO,QAAG,YAAU,OAAOA,GAAE,QAAO,EAAEA,IAAEC,EAAC;AAAE,QAAIC,KAAE,OAAO,UAAU,SAAS,KAAKF,EAAC,EAAE,MAAM,GAAE,EAAE;AAAE,iBAAWE,MAAGF,GAAE,gBAAcE,KAAEF,GAAE,YAAY;AAAM,QAAG,UAAQE,MAAG,UAAQA,GAAE,QAAO,MAAM,KAAKF,EAAC;AAAE,QAAG,gBAAcE,MAAG,2CAA2C,KAAKA,EAAC,EAAE,QAAO,EAAEF,IAAEC,EAAC;AAAA,EAAC,EAAED,IAAEC,EAAC,KAAG,WAAU;AAAC,UAAM,IAAI,UAAU,2IAA2I;AAAA,EAAC,EAAE;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,GAAC,QAAMA,MAAGA,KAAED,GAAE,YAAUC,KAAED,GAAE;AAAQ,WAAQE,KAAE,GAAEC,KAAE,IAAI,MAAMF,EAAC,GAAEC,KAAED,IAAEC,KAAI,CAAAC,GAAED,EAAC,IAAEF,GAAEE,EAAC;AAAE,SAAOC;AAAC;;;;;;;;;;ACmB3yS,IAAMS,KACJ;AADF,IAGaC,SAAyBC,aAAAA,eAA2C,EAC/EC,iBAAiB,CAAE,GACnBC,SAAS,IACTC,UAAU,IACVC,WAAAA,MACAC,WAAW,WAAA;AACT,QAAMC,MAAMR,EAAAA;AACb,GACDS,oBAAoB,WAAA;AAClB,QAAMD,MAAMR,EAAAA;AACb,GACDU,OAAO,WAAA;AACL,QAAMF,MAAMR,EAAAA;AACb,GACDW,aAAa,WAAA;AACX,QAAMH,MAAMR,EAAAA;AACb,GACDY,QAAQ,WAAA;AACN,QAAMJ,MAAMR,EAAAA;AACd,EAAA,CAAA;AAtBF,IA0BYa,KAERZ,GAFFa;AA1BF,IA2BYC,KACRd,GADFe;;;AC3CWC,IAAAA,KAAqB,WAAA;AAAH,SAASC,cAAAA,QAAMC,WAAWC,EAAAA;AAAuB;;;ICMnEC,KAAiC,SAACC,IAAAA;AAC7C,MAAMC,KAAYC,SAASC,eAAeH,EAAAA;AAE1C,MAAKC,GAIL,QAAOA,GAAUG,YACfH,CAAAA,GAAUG,UAAUC,OAAAA;AAExB;;;;AClBO,IAAMC,KACO,eAAA,OAAXC,SAAyBC,cAAAA,kBAAkBC,cAAAA;;;;ICoBvCC,KAAkE,SAAHC,IAAAA;AAMtE,MAAAC,KAAAD,GALJE,IAAAA,IAAAA,WAAED,KAH0B,wCAGFA,IAC1BE,IAAQH,GAARG,UACAC,IAAMJ,GAANI,QACAC,IAAQL,GAARK,UACGC,IAAKC,EAAAP,IAAAQ,EAAAA,GAERC,IAA0CC,GAAAA,GAAlCC,KAAOF,EAAPE,SAASC,IAAMH,EAANG,QAAkBC,IAAEJ,EAAZJ,UACnBS,IAAsCC,cAAAA,QAAMC,OAAuB,IAAA;AA0BzE,SAxBAC,GAA0B,WAAA;AACxB,QAAKL,GAAL;AACA,UAAMM,KAAWC,SAASC,cAAc,KAAA,GAElCC,KAAMC,EAAAA,EAAA,EACVC,SAASZ,IACTa,UAAUrB,EAAAA,IACLE,QAAAA,IAAAA,IAAYQ,MAAO,EAAEA,IAAIR,QAAAA,IAAAA,IAAYQ,EAAAA,CAAAA,GACvCP,CAAAA;AAWL,aAPEM,EAAOM,IADLd,IACakB,EAAAA,EAAAA,CAAAA,GAAOD,EAAAA,GAAM,CAAA,GAAA,EAAEjB,QAAAA,EAAAA,CAAAA,IACRiB,EAAAA,GAEpBP,EAAoCW,WACtCX,EAAoCW,QAAQC,YAAYR,EAAAA,GAGnD,WAAA;AACLS,QAAAA,GAA+BzB,CAAAA;MAAAA;IAnBpB;EAqBd,GAAE,CAACU,GAAQP,GAAUF,GAAUD,GAAIS,IAASL,EAAMsB,MAAMxB,GAAQE,EAAMuB,KAAAA,CAAAA,GAEhEd,cAAAA,QAAAK,cAAA,OAAAU,EAAA,EAAK5B,IAAIA,GAAI6B,KAAKjB,EAAAA,GAAyCR,CAAAA,CAAAA;AACpE;;;;;;IClDa0B,KAA+B,SAACC,IAAAA;AAAgB,SAAA,CAAA,CACzDC,SAASC,cAAa,IAAAC,OAAKH,EAAAA,CAAAA,KAAAA,CAAAA,CAC3BC,SAASC,cAAc,kDAAA;AAAmD;;;ACHjEE,IAAAA,KAA2C,WAAA;AAAH,SACnD;AAA8C;;;ICCnCC,KAA2B,WAAA;AACtC,MAAMC,KAAQC,SAASC,cAAc,OAAA;AACrCF,EAAAA,GAAMG,YAAYC,GAAAA,GAClBH,SAASI,KAAKC,YAAYN,EAAAA;AAC5B;;;ICKaO,KAAmC,SAAHC,IAAAA;AAML,MAAAC,KAAAD,GALtCE,MAAAA,KAAAA,WAAID,KAAG,eAAYA,IAAAE,KAAAH,GACnBI,cAAAA,KAAAA,WAAYD,MAAQA,IACpBE,KAAML,GAANK,QACAC,IAAEN,GAAFM,IACAC,KAAKP,GAALO,OAEMC,IAAU,IAAIC,gBAAeC,EAAAA,EAAAA,EAC7BJ,CAAAA,GAAAA,KAAM,EAAEA,IAAAA,EAAAA,CAAAA,GACRC,MAAS,EAAEA,OAAAA,GAAAA,CAAAA,GAAO,CAAA,GAAA,EACtBF,QAAAA,GAAAA,CAAAA,CAAAA,GAGIM,KAAOP,KAAe,kBAAkB;AAE9C,SAAAQ,eAAAA,OAAsBV,IAAIU,aAAAA,EAAAA,OAAcD,IAAI,GAAA,EAAAC,OAAIJ,CAAAA;AAClD;;;;ICfaK,KAA8B,SAAHC,IAAAA;AASC,MARvCC,KAAMD,GAANC,QAAMC,KAAAF,GACNG,UAAAA,KAAAA,WAAQD,KAAG,SAAMA,IAAAE,KAAAJ,GACjBK,cAAAA,IAAAA,WAAYD,MAAQA,IACpBE,IAAIN,GAAJM,MACAC,KAAMP,GAANO,QACAC,IAAKR,GAALQ,OACAC,KAAET,GAAFS,IACGC,IAAMC,EAAAX,IAAAY,EAAAA,GAEHC,IAAqBC,GAAiC,EAC1DR,MAAAA,GACAD,cAAAA,GACAE,QAAAA,IACAE,IAAAA,IACAD,OAAAA,EAAAA,CAAAA,GAGIO,IAASC,SAASC,cAAc,QAAA;AAEtCC,SAAOC,QAAQT,CAAAA,EAAQU,QAAQ,SAAAC,IAAAA;AAAkB,QAAAC,IAAAC,KAAAC,EAAAH,IAAA,CAAA,GAAhBI,KAAGF,GAAA,CAAA,GAAEG,KAAKH,GAAA,CAAA;AACzCR,MAAOY,aAAaF,IAAsB,UAAnBH,KAAEI,QAAAA,KAAAA,SAAAA,GAAOE,SAAAA,MAAAA,WAAUN,KAAAA,KAAI,EAAA;EAChD,CAAA,GAEAP,EAAOc,MAAMhB,GACbE,EAAOd,SAASA,IAEhBe,SAASb,EAAAA,EAAU2B,YAAYf,CAAAA;AACjC;;;ICzCagB,KAA6B,WAAA;AACxC,MAAMC,KAAYC,SAASC,cAAc,mBAAA;AACrCF,EAAAA,MAAaA,GAAUG,cACzBF,SAASG,KAAKC,YAAYL,GAAUG,UAAAA;AAExC;;;ACXO,IAAMG,KAAc;AAApB,IAUMC,KAA8B,SAACC,IAAAA;AAEzCC,SAAeC,oBAAAA;AAEhB,MAAMC,KAAwBC,SAASC,cAAAA,IAAaC,OAAKN,EAAAA,CAAAA;AACrDG,EAAAA,MACFA,GAAsBI,OAAAA;AAGxB,MAAMC,KAAgBJ,SAASC,cAAAA,gBAAaC,OAAiBR,IAAW,IAAA,CAAA;AAEpEU,EAAAA,MACFA,GAAcD,OAAAA;AAElB;;;ACuCA,IAGaE,IAAkE,SAAHC,IAAAA;AAWtE,MAVJC,IAAID,GAAJC,MACAC,IAAOF,GAAPE,SACAC,IAAQH,GAARG,UACAC,IAAWJ,GAAXI,aAAWC,IAAAL,GACXM,cAAAA,IAAAA,WAAYD,KAAQA,GACpBE,IAAIP,GAAJO,MACAC,IAAQR,GAARQ,UACAC,IAAQT,GAARS,UACAC,IAAMV,GAANU,QACAC,IAAOX,GAAPW,SAEAC,IAAkCC,cAAAA,QAAMC,SAAAA,IAAS,GAAKC,IAAAC,EAAAJ,GAAA,CAAA,GAA/CK,IAASF,EAAA,CAAA,GAAEG,IAAYH,EAAA,CAAA,GAC9BI,IACEN,cAAAA,QAAMC,SAAAA,GAAoCM,IAAAJ,EAAAG,GAAA,CAAA,GADrCE,IAAuBD,EAAA,CAAA,GAAEE,IAA0BF,EAAA,CAAA;AAG1DP,gBAAAA,QAAMU,UAAU,WAAA;AAAM,QAAAC;AACpB,QAAA,CAAKtB,EACH,OAAM,IAAIuB,MAAM,wCAAA;AAGlB,QAAMC,KAA0BF,UAAlBA,KAAGpB,QAAAA,IAAAA,SAAAA,EAAauB,OAAAA,WAAEH,KAAAA,KAAI,2BAC9BI,IAA4BC,GAA6BH,EAAAA,GAEzDI,KAAS,WAAA;AAAM,UAAAC,IACbC,KAA4C1B,IACpByB,UADgCA,KACzDE,OAAeC,eAAAA,WAAUH,KAAAA,SAA1BA,GAA4BI,aAC3BF,OAAeC;AAEpB,UAAKF,IAAAA;AAaL,YARKvB,KACHuB,GAAgBI,MAAKC,EAAAC,EAAAA,EAAAC,KAAC,SAAAC,KAAAA;AAAA,iBAAAF,EAAAA,EAAAG,KAAA,SAAAC,KAAAA;AAAA,sBAAA,SAAAA,IAAAC,OAAAD,IAAAE,MAAAA;cAAA,KAAA;AACwB,oBAA5CtB,EAA2BU,EAAAA,GAAAA,CACvBtB,GAAM;AAAAgC,kBAAAA,IAAAE,OAAA;AAAA;gBAAA;AAAA,uBAAAF,IAAAE,OAAA,GAAQlC,EAAOsB,EAAAA;cAAgB,KAAA;AACzCd,kBAAAA,IAAa;cAAM,KAAA;cAAA,KAAA;AAAA,uBAAAwB,IAAAG,KAAAA;YAAAA;UAAA,GAAAL,EAAAA;QACpB,CAAA,CAAA,CAAA,GAGC/B,GAAU;AACZ,cAAMqC,KAAMC,EAAAA,EAAA,EACVC,MAAe,SAAT/C,KAA0B,mBAATA,IAA0B,cAAc,SAAA,IACjD,SAATA,KAA0B,mBAATA,MAA6B,EAAEgD,OAAO,cAAA,CAAA,GAAyB,CAAA,GAAA,EACrFC,SAAShD,EAAAA,GACNO,CAAAA;AAGL,cAAA,CAAKmB,EAAAA,EAES,SAAT3B,KAA0B,mBAATA,MAAgD,cAApBQ,QAAAA,IAAAA,SAAAA,EAAUwC,UAGxDE,GAAAA;AAIJnB,UAAAA,GAAgBI,MAAKC,EAAAC,EAAAA,EAAAC,KAAC,SAAAa,KAAAA;AAAA,mBAAAd,EAAAA,EAAAG,KAAA,SAAAY,KAAAA;AAAA,wBAAA,SAAAA,IAAAV,OAAAU,IAAAT,MAAAA;gBAAA,KAAA;AAKwB,sBAJxCnC,EAAS6C,aACXtB,GAAgBuB,OAAO9C,EAAS6C,WAAWR,IAAAA,CAAAA,CAAUrC,EAAS+C,OAAAA,GAGhElC,EAA2BU,EAAAA,GAAAA,CAEvBtB,GAAM;AAAA2C,oBAAAA,IAAAT,OAAA;AAAA;kBAAA;AAAA,yBAAAS,IAAAT,OAAA,GAAQlC,EAAOsB,EAAAA;gBAAgB,KAAA;AACzCd,oBAAAA,IAAa;gBAAM,KAAA;gBAAA,KAAA;AAAA,yBAAAmC,IAAAR,KAAAA;cAAAA;YAAA,GAAAO,EAAAA;UACpB,CAAA,CAAA,CAAA;QACH;MAAA,MAvCMzC,MAASA,EAAAA;IAAAA;AAiEjB,WAxBCsB,OAAsE,wBAAIH,IAEvEF,IACFE,GAAAA,IAEA2B,GAA2BV,EAAAA,EAAAA,EAAAA,EAAA,EACzBzC,cAAAA,GACAC,MAAAA,EAAAA,IACc,SAATN,KAA0B,mBAATA,OACpBQ,QAAAA,IAAAA,SAAAA,EAAUwC,UAAS,EACjBA,OAA2B,cAApBxC,QAAAA,IAAAA,SAAAA,EAAUwC,SAAqB,gBAAgBxC,QAAAA,IAAAA,SAAAA,EAAUwC,MAAAA,CAAAA,GAEhE9C,KAAY,EAAEuD,IAAIvD,EAAAA,CAAAA,GAAU,CAAA,GAAA,EAChCoD,SACa,SAATtD,KAA0B,mBAATA,MAAlB,QAA8CQ,KAAAA,EAAU6C,aAChD,kBAATrD,IACI,aACAC,EAAAA,GACHE,CAAAA,GAAW,CAAA,GAAA,EACd0B,QAAAA,IACAH,IAAID,GAAAA,CAAAA,CAAAA,GAID,WAAA;AACLL,cAAAA,KAAAA,EAAyBsC,MAAAA,GACpB/B,KAA2BgC,GAA4BlC,EAAAA,GAC9C,SAATzB,KAA0B,mBAATA,KAA6BQ,QAAAA,KAAAA,EAAU6C,aAAzD,QAAsE7C,KAAAA,CAAAA,EAAUwC,QAGlFY,GAAAA,IAFAC,GArGY,4BAAA;IAAA;EA0GjB,GAAE,CAACxD,GAAcH,GAAUI,CAAAA,CAAAA;AAE5B,MAAMwD,IAAYlD,cAAAA,QAAMmD,YACtB,SAACC,IAAAA;AACC,QAAK5C,QAAAA,KAAAA,CAAAA,EAAyB6C,QAC5B,OAAM,IAAIzC,MAAM,sCAAA;AAGlB,WAAOJ,EAAwB6C,QAAQhE,GAAS,EAAE+D,QAAAA,GAAAA,CAAAA;EACpD,GACA,CAAC5C,CAAAA,CAAAA,GAGG8C,IAAqBtD,cAAAA,QAAMmD,YAC/B,SAACI,IAAAA;AACC,QAAK/C,QAAAA,KAAAA,CAAAA,EAAyB6C,QAC5B,OAAM,IAAIzC,MAAM,sCAAA;AAGlB,WAAOJ,EAAwB6C,QAAQE,EAAAA;EACzC,GACA,CAAC/C,CAAAA,CAAAA,GAGGgD,IAAQxD,cAAAA,QAAMyD,QAClB,WAAA;AAAA,WAAAvB,EAAA,EACEf,iBAAiBX,GACjBnB,SAAAA,GACAe,WAAAA,GACAkD,oBAAAA,GACAJ,WAAAA,GACAJ,OAAOtC,QAAAA,IAAAA,SAAAA,EAAyBsC,OAChCY,aAAalD,QAAAA,IAAAA,SAAAA,EAAyBkD,aACtChB,QAAQlC,QAAAA,IAAAA,SAAAA,EAAyBkC,OAAAA,GAC7BpD,KAAY,EAAEA,UAAAA,EAAAA,CAAAA;EAClB,GACF,CAACD,GAASmB,GAAyBJ,GAAWd,CAAAA,CAAAA;AAGhD,SAAOU,cAAAA,QAAA2D,cAACC,IAA8B,EAACJ,OAAOA,EAAAA,GAAQ7D,CAAAA;AACxD;;;;ICzMakE,KAAsB,SACjCC,IAAAA;AAEA,MAAMC,KAA+B,SAACC,IAAAA;AAAoD,WACxFC,cAAAA,QAAAC,cAACC,IAA8B,MAC5B,SAACC,IAAAA;AAAK,aAAKH,cAAAA,QAAAC,cAACJ,IAASO,EAAAA,CAAAA,GAAKL,IAAK,EAAEM,iBAAiBF,GAAAA,CAAAA,CAAAA;IAAS,CAAA;EAAA;AAQhE,SAJAL,GAA6BQ,cAAWC,uBAAAA,OACtCV,GAAUS,eAAeT,GAAUW,QAAQ,aAC1C,GAAA,GAEIV;AACT;;;ACtB6hB,IAAAW,gBAAM;", "names": ["t", "r", "e", "n", "o", "i", "a", "c", "u", "l", "d", "f", "contextError", "GoogleReCaptchaContext", "createContext", "googleReCaptcha", "siteKey", "language", "isLoading", "executeV3", "Error", "executeV2Invisible", "reset", "getResponse", "render", "GoogleReCaptchaContextProvider", "Provider", "GoogleReCaptchaContextConsumer", "Consumer", "useGoogleReCaptcha", "React", "useContext", "GoogleReCaptchaContext", "removeGoogleReCaptchaContainer", "containerId", "container", "document", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "remove", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "useEffect", "GoogleReCaptchaCheckbox", "_ref", "_ref$id", "id", "onChange", "action", "language", "props", "_objectWithoutProperties", "_excluded", "_useGoogleReCaptcha", "useGoogleReCaptcha", "siteKey", "render", "hl", "googleReCaptchaCheckboxContainerRef", "React", "useRef", "useIsomorphicLayoutEffect", "checkbox", "document", "createElement", "params", "_objectSpread", "sitekey", "callback", "current", "append<PERSON><PERSON><PERSON>", "removeGoogleReCaptchaContainer", "size", "theme", "_extends", "ref", "checkGoogleReCaptchaInjected", "scriptId", "document", "querySelector", "concat", "generateGoogleReCaptchaHiddenBadgeStyles", "hideGoogleReCaptchaBadge", "style", "document", "createElement", "innerHTML", "generateGoogleReCaptchaHiddenBadgeStyles", "body", "append<PERSON><PERSON><PERSON>", "generateGoogleReCaptchaScriptSrc", "_ref", "_ref$host", "host", "_ref$isEnterprise", "isEnterprise", "render", "hl", "badge", "queries", "URLSearchParams", "_objectSpread", "file", "concat", "injectGoogleReCaptchaScript", "_ref", "onload", "_ref$appendTo", "appendTo", "_ref$isEnterprise", "isEnterprise", "host", "render", "badge", "hl", "params", "_objectWithoutProperties", "_excluded", "googleReCaptchaSrc", "generateGoogleReCaptchaScriptSrc", "script", "document", "createElement", "Object", "entries", "for<PERSON>ach", "_ref2", "_value$toString", "_ref3", "_slicedToArray", "key", "value", "setAttribute", "toString", "src", "append<PERSON><PERSON><PERSON>", "removeGoogleReCaptchaBadge", "nodeBadge", "document", "querySelector", "parentNode", "body", "<PERSON><PERSON><PERSON><PERSON>", "GSTATIC_URL", "removeGoogleReCaptchaScript", "scriptId", "window", "___grecaptcha_cfg", "googleReCaptchaScript", "document", "querySelector", "concat", "remove", "gStaticScript", "GoogleReCaptchaProvider", "_ref", "type", "siteKey", "language", "scriptProps", "_ref$isEnterprise", "isEnterprise", "host", "children", "explicit", "onLoad", "onError", "_React$useState", "React", "useState", "_React$useState2", "_slicedToArray", "isLoading", "setIsLoading", "_React$useState3", "_React$useState4", "googleReCaptchaInstance", "setGoogleReCaptchaInstance", "useEffect", "_scriptProps$id", "Error", "scriptId", "id", "isGoogleReCaptchaInjected", "checkGoogleReCaptchaInjected", "onload", "_grecap<PERSON>a", "googleReCaptcha", "window", "gre<PERSON><PERSON>a", "enterprise", "ready", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_context", "prev", "next", "stop", "params", "_objectSpread", "size", "badge", "sitekey", "hideGoogleReCaptchaBadge", "_callee2", "_context2", "container", "render", "inherit", "injectGoogleReCaptchaScript", "hl", "reset", "removeGoogleReCaptchaScript", "removeGoogleReCaptchaBadge", "removeGoogleReCaptchaContainer", "executeV3", "useCallback", "action", "execute", "executeV2Invisible", "optWidgetId", "value", "useMemo", "getResponse", "createElement", "GoogleReCaptchaContextProvider", "withGoogleReCaptcha", "Component", "WithGoogleReCaptchaComponent", "props", "React", "createElement", "GoogleReCaptchaContextConsumer", "value", "_extends", "googleReCaptcha", "displayName", "concat", "name", "import_react"]}
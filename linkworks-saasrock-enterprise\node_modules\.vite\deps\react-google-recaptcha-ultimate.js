import {
  require_react
} from "./chunk-R26XTA6N.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/react-google-recaptcha-ultimate/dist/esm/_rollupPluginBabelHelpers-ee9924ff.js
function t(t7, r7) {
  var e7 = Object.keys(t7);
  if (Object.getOwnPropertySymbols) {
    var n4 = Object.getOwnPropertySymbols(t7);
    r7 && (n4 = n4.filter(function(r8) {
      return Object.getOwnPropertyDescriptor(t7, r8).enumerable;
    })), e7.push.apply(e7, n4);
  }
  return e7;
}
function r(r7) {
  for (var e7 = 1; e7 < arguments.length; e7++) {
    var n4 = null != arguments[e7] ? arguments[e7] : {};
    e7 % 2 ? t(Object(n4), true).forEach(function(t7) {
      i(r7, t7, n4[t7]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(r7, Object.getOwnPropertyDescriptors(n4)) : t(Object(n4)).forEach(function(t7) {
      Object.defineProperty(r7, t7, Object.getOwnPropertyDescriptor(n4, t7));
    });
  }
  return r7;
}
function e() {
  e = function() {
    return r7;
  };
  var t7, r7 = {}, n4 = Object.prototype, o4 = n4.hasOwnProperty, i3 = Object.defineProperty || function(t8, r8, e7) {
    t8[r8] = e7.value;
  }, a4 = "function" == typeof Symbol ? Symbol : {}, c5 = a4.iterator || "@@iterator", u2 = a4.asyncIterator || "@@asyncIterator", l3 = a4.toStringTag || "@@toStringTag";
  function f(t8, r8, e7) {
    return Object.defineProperty(t8, r8, { value: e7, enumerable: true, configurable: true, writable: true }), t8[r8];
  }
  try {
    f({}, "");
  } catch (t8) {
    f = function(t9, r8, e7) {
      return t9[r8] = e7;
    };
  }
  function s(t8, r8, e7, n5) {
    var o5 = r8 && r8.prototype instanceof b ? r8 : b, a5 = Object.create(o5.prototype), c6 = new A(n5 || []);
    return i3(a5, "_invoke", { value: S(t8, e7, c6) }), a5;
  }
  function h(t8, r8, e7) {
    try {
      return { type: "normal", arg: t8.call(r8, e7) };
    } catch (t9) {
      return { type: "throw", arg: t9 };
    }
  }
  r7.wrap = s;
  var p = "suspendedStart", y = "suspendedYield", v = "executing", g = "completed", d2 = {};
  function b() {
  }
  function m() {
  }
  function w() {
  }
  var O = {};
  f(O, c5, function() {
    return this;
  });
  var j = Object.getPrototypeOf, x = j && j(j(G([])));
  x && x !== n4 && o4.call(x, c5) && (O = x);
  var E = w.prototype = b.prototype = Object.create(O);
  function L(t8) {
    ["next", "throw", "return"].forEach(function(r8) {
      f(t8, r8, function(t9) {
        return this._invoke(r8, t9);
      });
    });
  }
  function P(t8, r8) {
    function e7(n6, i4, a5, c6) {
      var u3 = h(t8[n6], t8, i4);
      if ("throw" !== u3.type) {
        var l4 = u3.arg, f2 = l4.value;
        return f2 && "object" == typeof f2 && o4.call(f2, "__await") ? r8.resolve(f2.__await).then(function(t9) {
          e7("next", t9, a5, c6);
        }, function(t9) {
          e7("throw", t9, a5, c6);
        }) : r8.resolve(f2).then(function(t9) {
          l4.value = t9, a5(l4);
        }, function(t9) {
          return e7("throw", t9, a5, c6);
        });
      }
      c6(u3.arg);
    }
    var n5;
    i3(this, "_invoke", { value: function(t9, o5) {
      function i4() {
        return new r8(function(r9, n6) {
          e7(t9, o5, r9, n6);
        });
      }
      return n5 = n5 ? n5.then(i4, i4) : i4();
    } });
  }
  function S(r8, e7, n5) {
    var o5 = p;
    return function(i4, a5) {
      if (o5 === v) throw new Error("Generator is already running");
      if (o5 === g) {
        if ("throw" === i4) throw a5;
        return { value: t7, done: true };
      }
      for (n5.method = i4, n5.arg = a5; ; ) {
        var c6 = n5.delegate;
        if (c6) {
          var u3 = _(c6, n5);
          if (u3) {
            if (u3 === d2) continue;
            return u3;
          }
        }
        if ("next" === n5.method) n5.sent = n5._sent = n5.arg;
        else if ("throw" === n5.method) {
          if (o5 === p) throw o5 = g, n5.arg;
          n5.dispatchException(n5.arg);
        } else "return" === n5.method && n5.abrupt("return", n5.arg);
        o5 = v;
        var l4 = h(r8, e7, n5);
        if ("normal" === l4.type) {
          if (o5 = n5.done ? g : y, l4.arg === d2) continue;
          return { value: l4.arg, done: n5.done };
        }
        "throw" === l4.type && (o5 = g, n5.method = "throw", n5.arg = l4.arg);
      }
    };
  }
  function _(r8, e7) {
    var n5 = e7.method, o5 = r8.iterator[n5];
    if (o5 === t7) return e7.delegate = null, "throw" === n5 && r8.iterator.return && (e7.method = "return", e7.arg = t7, _(r8, e7), "throw" === e7.method) || "return" !== n5 && (e7.method = "throw", e7.arg = new TypeError("The iterator does not provide a '" + n5 + "' method")), d2;
    var i4 = h(o5, r8.iterator, e7.arg);
    if ("throw" === i4.type) return e7.method = "throw", e7.arg = i4.arg, e7.delegate = null, d2;
    var a5 = i4.arg;
    return a5 ? a5.done ? (e7[r8.resultName] = a5.value, e7.next = r8.nextLoc, "return" !== e7.method && (e7.method = "next", e7.arg = t7), e7.delegate = null, d2) : a5 : (e7.method = "throw", e7.arg = new TypeError("iterator result is not an object"), e7.delegate = null, d2);
  }
  function k(t8) {
    var r8 = { tryLoc: t8[0] };
    1 in t8 && (r8.catchLoc = t8[1]), 2 in t8 && (r8.finallyLoc = t8[2], r8.afterLoc = t8[3]), this.tryEntries.push(r8);
  }
  function N(t8) {
    var r8 = t8.completion || {};
    r8.type = "normal", delete r8.arg, t8.completion = r8;
  }
  function A(t8) {
    this.tryEntries = [{ tryLoc: "root" }], t8.forEach(k, this), this.reset(true);
  }
  function G(r8) {
    if (r8 || "" === r8) {
      var e7 = r8[c5];
      if (e7) return e7.call(r8);
      if ("function" == typeof r8.next) return r8;
      if (!isNaN(r8.length)) {
        var n5 = -1, i4 = function e8() {
          for (; ++n5 < r8.length; ) if (o4.call(r8, n5)) return e8.value = r8[n5], e8.done = false, e8;
          return e8.value = t7, e8.done = true, e8;
        };
        return i4.next = i4;
      }
    }
    throw new TypeError(typeof r8 + " is not iterable");
  }
  return m.prototype = w, i3(E, "constructor", { value: w, configurable: true }), i3(w, "constructor", { value: m, configurable: true }), m.displayName = f(w, l3, "GeneratorFunction"), r7.isGeneratorFunction = function(t8) {
    var r8 = "function" == typeof t8 && t8.constructor;
    return !!r8 && (r8 === m || "GeneratorFunction" === (r8.displayName || r8.name));
  }, r7.mark = function(t8) {
    return Object.setPrototypeOf ? Object.setPrototypeOf(t8, w) : (t8.__proto__ = w, f(t8, l3, "GeneratorFunction")), t8.prototype = Object.create(E), t8;
  }, r7.awrap = function(t8) {
    return { __await: t8 };
  }, L(P.prototype), f(P.prototype, u2, function() {
    return this;
  }), r7.AsyncIterator = P, r7.async = function(t8, e7, n5, o5, i4) {
    void 0 === i4 && (i4 = Promise);
    var a5 = new P(s(t8, e7, n5, o5), i4);
    return r7.isGeneratorFunction(e7) ? a5 : a5.next().then(function(t9) {
      return t9.done ? t9.value : a5.next();
    });
  }, L(E), f(E, l3, "Generator"), f(E, c5, function() {
    return this;
  }), f(E, "toString", function() {
    return "[object Generator]";
  }), r7.keys = function(t8) {
    var r8 = Object(t8), e7 = [];
    for (var n5 in r8) e7.push(n5);
    return e7.reverse(), function t9() {
      for (; e7.length; ) {
        var n6 = e7.pop();
        if (n6 in r8) return t9.value = n6, t9.done = false, t9;
      }
      return t9.done = true, t9;
    };
  }, r7.values = G, A.prototype = { constructor: A, reset: function(r8) {
    if (this.prev = 0, this.next = 0, this.sent = this._sent = t7, this.done = false, this.delegate = null, this.method = "next", this.arg = t7, this.tryEntries.forEach(N), !r8) for (var e7 in this) "t" === e7.charAt(0) && o4.call(this, e7) && !isNaN(+e7.slice(1)) && (this[e7] = t7);
  }, stop: function() {
    this.done = true;
    var t8 = this.tryEntries[0].completion;
    if ("throw" === t8.type) throw t8.arg;
    return this.rval;
  }, dispatchException: function(r8) {
    if (this.done) throw r8;
    var e7 = this;
    function n5(n6, o5) {
      return c6.type = "throw", c6.arg = r8, e7.next = n6, o5 && (e7.method = "next", e7.arg = t7), !!o5;
    }
    for (var i4 = this.tryEntries.length - 1; i4 >= 0; --i4) {
      var a5 = this.tryEntries[i4], c6 = a5.completion;
      if ("root" === a5.tryLoc) return n5("end");
      if (a5.tryLoc <= this.prev) {
        var u3 = o4.call(a5, "catchLoc"), l4 = o4.call(a5, "finallyLoc");
        if (u3 && l4) {
          if (this.prev < a5.catchLoc) return n5(a5.catchLoc, true);
          if (this.prev < a5.finallyLoc) return n5(a5.finallyLoc);
        } else if (u3) {
          if (this.prev < a5.catchLoc) return n5(a5.catchLoc, true);
        } else {
          if (!l4) throw new Error("try statement without catch or finally");
          if (this.prev < a5.finallyLoc) return n5(a5.finallyLoc);
        }
      }
    }
  }, abrupt: function(t8, r8) {
    for (var e7 = this.tryEntries.length - 1; e7 >= 0; --e7) {
      var n5 = this.tryEntries[e7];
      if (n5.tryLoc <= this.prev && o4.call(n5, "finallyLoc") && this.prev < n5.finallyLoc) {
        var i4 = n5;
        break;
      }
    }
    i4 && ("break" === t8 || "continue" === t8) && i4.tryLoc <= r8 && r8 <= i4.finallyLoc && (i4 = null);
    var a5 = i4 ? i4.completion : {};
    return a5.type = t8, a5.arg = r8, i4 ? (this.method = "next", this.next = i4.finallyLoc, d2) : this.complete(a5);
  }, complete: function(t8, r8) {
    if ("throw" === t8.type) throw t8.arg;
    return "break" === t8.type || "continue" === t8.type ? this.next = t8.arg : "return" === t8.type ? (this.rval = this.arg = t8.arg, this.method = "return", this.next = "end") : "normal" === t8.type && r8 && (this.next = r8), d2;
  }, finish: function(t8) {
    for (var r8 = this.tryEntries.length - 1; r8 >= 0; --r8) {
      var e7 = this.tryEntries[r8];
      if (e7.finallyLoc === t8) return this.complete(e7.completion, e7.afterLoc), N(e7), d2;
    }
  }, catch: function(t8) {
    for (var r8 = this.tryEntries.length - 1; r8 >= 0; --r8) {
      var e7 = this.tryEntries[r8];
      if (e7.tryLoc === t8) {
        var n5 = e7.completion;
        if ("throw" === n5.type) {
          var o5 = n5.arg;
          N(e7);
        }
        return o5;
      }
    }
    throw new Error("illegal catch attempt");
  }, delegateYield: function(r8, e7, n5) {
    return this.delegate = { iterator: G(r8), resultName: e7, nextLoc: n5 }, "next" === this.method && (this.arg = t7), d2;
  } }, r7;
}
function n(t7, r7, e7, n4, o4, i3, a4) {
  try {
    var c5 = t7[i3](a4), u2 = c5.value;
  } catch (t8) {
    return void e7(t8);
  }
  c5.done ? r7(u2) : Promise.resolve(u2).then(n4, o4);
}
function o(t7) {
  return function() {
    var r7 = this, e7 = arguments;
    return new Promise(function(o4, i3) {
      var a4 = t7.apply(r7, e7);
      function c5(t8) {
        n(a4, o4, i3, c5, u2, "next", t8);
      }
      function u2(t8) {
        n(a4, o4, i3, c5, u2, "throw", t8);
      }
      c5(void 0);
    });
  };
}
function i(t7, r7, e7) {
  return (r7 = function(t8) {
    var r8 = function(t9, r9) {
      if ("object" != typeof t9 || null === t9) return t9;
      var e8 = t9[Symbol.toPrimitive];
      if (void 0 !== e8) {
        var n4 = e8.call(t9, r9 || "default");
        if ("object" != typeof n4) return n4;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return ("string" === r9 ? String : Number)(t9);
    }(t8, "string");
    return "symbol" == typeof r8 ? r8 : String(r8);
  }(r7)) in t7 ? Object.defineProperty(t7, r7, { value: e7, enumerable: true, configurable: true, writable: true }) : t7[r7] = e7, t7;
}
function a() {
  return a = Object.assign ? Object.assign.bind() : function(t7) {
    for (var r7 = 1; r7 < arguments.length; r7++) {
      var e7 = arguments[r7];
      for (var n4 in e7) Object.prototype.hasOwnProperty.call(e7, n4) && (t7[n4] = e7[n4]);
    }
    return t7;
  }, a.apply(this, arguments);
}
function c(t7, r7) {
  if (null == t7) return {};
  var e7, n4, o4 = function(t8, r8) {
    if (null == t8) return {};
    var e8, n5, o5 = {}, i4 = Object.keys(t8);
    for (n5 = 0; n5 < i4.length; n5++) e8 = i4[n5], r8.indexOf(e8) >= 0 || (o5[e8] = t8[e8]);
    return o5;
  }(t7, r7);
  if (Object.getOwnPropertySymbols) {
    var i3 = Object.getOwnPropertySymbols(t7);
    for (n4 = 0; n4 < i3.length; n4++) e7 = i3[n4], r7.indexOf(e7) >= 0 || Object.prototype.propertyIsEnumerable.call(t7, e7) && (o4[e7] = t7[e7]);
  }
  return o4;
}
function u(t7, r7) {
  return function(t8) {
    if (Array.isArray(t8)) return t8;
  }(t7) || function(t8, r8) {
    var e7 = null == t8 ? null : "undefined" != typeof Symbol && t8[Symbol.iterator] || t8["@@iterator"];
    if (null != e7) {
      var n4, o4, i3, a4, c5 = [], u2 = true, l3 = false;
      try {
        if (i3 = (e7 = e7.call(t8)).next, 0 === r8) {
          if (Object(e7) !== e7) return;
          u2 = false;
        } else for (; !(u2 = (n4 = i3.call(e7)).done) && (c5.push(n4.value), c5.length !== r8); u2 = true) ;
      } catch (t9) {
        l3 = true, o4 = t9;
      } finally {
        try {
          if (!u2 && null != e7.return && (a4 = e7.return(), Object(a4) !== a4)) return;
        } finally {
          if (l3) throw o4;
        }
      }
      return c5;
    }
  }(t7, r7) || function(t8, r8) {
    if (!t8) return;
    if ("string" == typeof t8) return l(t8, r8);
    var e7 = Object.prototype.toString.call(t8).slice(8, -1);
    "Object" === e7 && t8.constructor && (e7 = t8.constructor.name);
    if ("Map" === e7 || "Set" === e7) return Array.from(t8);
    if ("Arguments" === e7 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e7)) return l(t8, r8);
  }(t7, r7) || function() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function l(t7, r7) {
  (null == r7 || r7 > t7.length) && (r7 = t7.length);
  for (var e7 = 0, n4 = new Array(r7); e7 < r7; e7++) n4[e7] = t7[e7];
  return n4;
}

// node_modules/react-google-recaptcha-ultimate/dist/esm/components/GoogleReCaptchaCheckbox.js
var import_react4 = __toESM(require_react());

// node_modules/react-google-recaptcha-ultimate/dist/esm/context/useGoogleReCaptcha.js
var import_react2 = __toESM(require_react());

// node_modules/react-google-recaptcha-ultimate/dist/esm/context/GoogleReCaptchaContext.js
var import_react = __toESM(require_react());
var o2 = "GoogleReCaptcha Context has not yet been implemented, if you are using useGoogleReCaptcha hook, make sure the hook is called inside component wrapped by GoogleRecaptchaProvider";
var r2 = (0, import_react.createContext)({ googleReCaptcha: {}, siteKey: "", language: "", isLoading: true, executeV3: function() {
  throw Error(o2);
}, executeV2Invisible: function() {
  throw Error(o2);
}, reset: function() {
  throw Error(o2);
}, getResponse: function() {
  throw Error(o2);
}, render: function() {
  throw Error(o2);
} });
var t2 = r2.Provider;
var n2 = r2.Consumer;

// node_modules/react-google-recaptcha-ultimate/dist/esm/context/useGoogleReCaptcha.js
var r3 = function() {
  return import_react2.default.useContext(r2);
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/removeGoogleReCaptchaContainer.js
var e3 = function(e7) {
  var t7 = document.getElementById(e7);
  if (t7) for (; t7.lastChild; ) t7.lastChild.remove();
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/hooks/useIsomorphicLayoutEffect.js
var import_react3 = __toESM(require_react());
var r4 = "undefined" != typeof window ? import_react3.useLayoutEffect : import_react3.useEffect;

// node_modules/react-google-recaptcha-ultimate/dist/esm/components/GoogleReCaptchaCheckbox.js
var l2 = ["id", "onChange", "action", "language"];
var c2 = function(c5) {
  var u2 = c5.id, s = void 0 === u2 ? "google-recaptcha-checkbox-container" : u2, m = c5.onChange, p = c5.action, f = c5.language, g = c(c5, l2), h = r3(), d2 = h.siteKey, C = h.render, v = h.language, j = import_react4.default.useRef(null);
  return r4(function() {
    if (C) {
      var e7 = document.createElement("div"), t7 = r(r({ sitekey: d2, callback: m }, (null != f ? f : v) && { hl: null != f ? f : v }), g);
      return C(e7, p ? r(r({}, t7), {}, { action: p }) : t7), j.current && j.current.appendChild(e7), function() {
        e3(s);
      };
    }
  }, [C, f, m, s, d2, g.size, p, g.theme]), import_react4.default.createElement("div", a({ id: s, ref: j }, g));
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/context/GoogleReCaptchaProvider.js
var import_react5 = __toESM(require_react());

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/checkGoogleReCaptchaInjected.js
var c3 = function(c5) {
  return !!document.querySelector("#".concat(c5)) || !!document.querySelector('script[src*="/recaptcha/"]:not([src*="gstatic"])');
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/generateGoogleReCaptchaHiddenBadgeStyles.js
var a3 = function() {
  return ".grecaptcha-badge{display: none !important;}";
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/hideGoogleReCaptchaBadge.js
var t4 = function() {
  var t7 = document.createElement("style");
  t7.innerHTML = a3(), document.body.appendChild(t7);
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/generateGoogleReCaptchaScriptSrc.js
var r5 = function(r7) {
  var a4 = r7.host, o4 = void 0 === a4 ? "google.com" : a4, t7 = r7.isEnterprise, n4 = void 0 !== t7 && t7, c5 = r7.render, s = r7.hl, i3 = r7.badge, p = new URLSearchParams(r(r(r({}, s && { hl: s }), i3 && { badge: i3 }), {}, { render: c5 })), l3 = n4 ? "enterprise.js" : "api.js";
  return "https://www.".concat(o4, "/recaptcha/").concat(l3, "?").concat(p);
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/injectGoogleReCaptchaScript.js
var t5 = ["onload", "appendTo", "isEnterprise", "host", "render", "badge", "hl"];
var n3 = function(n4) {
  var a4 = n4.onload, i3 = n4.appendTo, d2 = void 0 === i3 ? "head" : i3, l3 = n4.isEnterprise, s = void 0 !== l3 && l3, p = n4.host, c5 = n4.render, h = n4.badge, u2 = n4.hl, g = c(n4, t5), m = r5({ host: p, isEnterprise: s, render: c5, hl: u2, badge: h }), v = document.createElement("script");
  Object.entries(g).forEach(function(e7) {
    var o4, t7 = u(e7, 2), n5 = t7[0], a5 = t7[1];
    v.setAttribute(n5, null !== (o4 = null == a5 ? void 0 : a5.toString()) && void 0 !== o4 ? o4 : "");
  }), v.src = m, v.onload = a4, document[d2].appendChild(v);
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/removeGoogleReCaptchaBadge.js
var e5 = function() {
  var e7 = document.querySelector(".grecaptcha-badge");
  e7 && e7.parentNode && document.body.removeChild(e7.parentNode);
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/utils/helpers/removeGoogleReCaptchaScript.js
var c4 = "https://www.gstatic.com/recaptcha/releases";
var e6 = function(e7) {
  window.___grecaptcha_cfg = void 0;
  var r7 = document.querySelector("#".concat(e7));
  r7 && r7.remove();
  var t7 = document.querySelector('script[src^="'.concat(c4, '"]'));
  t7 && t7.remove();
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/context/GoogleReCaptchaProvider.js
var d = function(d2) {
  var v = d2.type, h = d2.siteKey, g = d2.language, f = d2.scriptProps, m = d2.isEnterprise, b = void 0 !== m && m, C = d2.host, x = d2.children, w = d2.explicit, R = d2.onLoad, G = d2.onError, j = import_react5.default.useState(true), k = u(j, 2), y = k[0], E = k[1], S = import_react5.default.useState(), B = u(S, 2), L = B[0], H = B[1];
  import_react5.default.useEffect(function() {
    var e7;
    if (!h) throw new Error("Google ReCaptcha site key not provided");
    var i3 = null !== (e7 = null == f ? void 0 : f.id) && void 0 !== e7 ? e7 : "google-recaptcha-script", p = c3(i3), d3 = function() {
      var e8, i4 = b ? null === (e8 = window.grecaptcha) || void 0 === e8 ? void 0 : e8.enterprise : window.grecaptcha;
      if (i4) {
        if (w || i4.ready(o(e().mark(function e9() {
          return e().wrap(function(e10) {
            for (; ; ) switch (e10.prev = e10.next) {
              case 0:
                if (H(i4), !R) {
                  e10.next = 4;
                  break;
                }
                return e10.next = 4, R(i4);
              case 4:
                E(true);
              case 5:
              case "end":
                return e10.stop();
            }
          }, e9);
        }))), w) {
          var n4 = r(r({ size: "v3" === v || "v2-invisible" === v ? "invisible" : "normal" }, ("v3" === v || "v2-invisible" === v) && { badge: "bottomright" }), {}, { sitekey: h }, w);
          if (!p) ("v3" === v || "v2-invisible" === v) && "hidden" === (null == w ? void 0 : w.badge) && t4();
          i4.ready(o(e().mark(function e9() {
            return e().wrap(function(e10) {
              for (; ; ) switch (e10.prev = e10.next) {
                case 0:
                  if (w.container && i4.render(w.container, n4, !!w.inherit), H(i4), !R) {
                    e10.next = 5;
                    break;
                  }
                  return e10.next = 5, R(i4);
                case 5:
                  E(true);
                case 6:
                case "end":
                  return e10.stop();
              }
            }, e9);
          })));
        }
      } else G && G();
    };
    return window.onGoogleReCaptchaLoad = d3, p ? d3() : n3(r(r(r(r({ isEnterprise: b, host: C }, ("v3" === v || "v2-invisible" === v) && (null == w ? void 0 : w.badge) && { badge: "hidden" === (null == w ? void 0 : w.badge) ? "bottomright" : null == w ? void 0 : w.badge }), g && { hl: g }), {}, { render: ("v3" === v || "v2-invisible" === v) && null != w && w.container || "v2-checkbox" === v ? "explicit" : h }, f), {}, { onload: d3, id: i3 })), function() {
      null == L || L.reset(), p || e6(i3), "v3" !== v && "v2-invisible" !== v || null != w && w.container || null == w || !w.badge ? e5() : e3("google-recaptcha-container");
    };
  }, [b, g, C]);
  var I = import_react5.default.useCallback(function(e7) {
    if (null == L || !L.execute) throw new Error("Google ReCaptcha has not been loaded");
    return L.execute(h, { action: e7 });
  }, [L]), K = import_react5.default.useCallback(function(e7) {
    if (null == L || !L.execute) throw new Error("Google ReCaptcha has not been loaded");
    return L.execute(e7);
  }, [L]), P = import_react5.default.useMemo(function() {
    return r({ googleReCaptcha: L, siteKey: h, isLoading: y, executeV2Invisible: K, executeV3: I, reset: null == L ? void 0 : L.reset, getResponse: null == L ? void 0 : L.getResponse, render: null == L ? void 0 : L.render }, g && { language: g });
  }, [h, L, y, g]);
  return import_react5.default.createElement(t2, { value: P }, x);
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/context/withGoogleReCaptcha.js
var import_react6 = __toESM(require_react());
var r6 = function(r7) {
  var a4 = function(a5) {
    return import_react6.default.createElement(n2, null, function(o4) {
      return import_react6.default.createElement(r7, a({}, a5, { googleReCaptcha: o4 }));
    });
  };
  return a4.displayName = "withGoogleReCaptcha(".concat(r7.displayName || r7.name || "Component", ")"), a4;
};

// node_modules/react-google-recaptcha-ultimate/dist/esm/index.js
var import_react7 = __toESM(require_react());
export {
  c2 as GoogleReCaptchaCheckbox,
  d as GoogleReCaptchaProvider,
  a3 as generateGoogleReCaptchaHiddenBadgeStyles,
  r5 as generateGoogleReCaptchaScriptSrc,
  r3 as useGoogleReCaptcha,
  r6 as withGoogleReCaptcha
};
/*! Bundled license information:

react-google-recaptcha-ultimate/dist/esm/_rollupPluginBabelHelpers-ee9924ff.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/context/GoogleReCaptchaContext.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/context/useGoogleReCaptcha.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/removeGoogleReCaptchaContainer.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/hooks/useIsomorphicLayoutEffect.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/components/GoogleReCaptchaCheckbox.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/checkGoogleReCaptchaInjected.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/generateGoogleReCaptchaHiddenBadgeStyles.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/hideGoogleReCaptchaBadge.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/generateGoogleReCaptchaScriptSrc.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/injectGoogleReCaptchaScript.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/removeGoogleReCaptchaBadge.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/utils/helpers/removeGoogleReCaptchaScript.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/context/GoogleReCaptchaProvider.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/context/withGoogleReCaptcha.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)

react-google-recaptcha-ultimate/dist/esm/index.js:
  (* @license react-google-recaptcha-ultimate v1.2.2 *)
*/
//# sourceMappingURL=react-google-recaptcha-ultimate.js.map

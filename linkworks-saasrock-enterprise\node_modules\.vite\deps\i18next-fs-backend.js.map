{"version": 3, "sources": ["../../i18next-fs-backend/esm/utils.js", "../../i18next-fs-backend/esm/formats/json5.js", "../../i18next-fs-backend/esm/formats/jsonc.js", "../../i18next-fs-backend/esm/formats/yaml.js", "../../i18next-fs-backend/esm/extname.js", "../../i18next-fs-backend/esm/readFile.js", "../../i18next-fs-backend/esm/writeFile.js", "../../i18next-fs-backend/esm/index.js"], "sourcesContent": ["var arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function debounce(func, wait, immediate) {\n  var timeout;\n  return function () {\n    var context = this;\n    var args = arguments;\n    var later = function later() {\n      timeout = null;\n      if (!immediate) func.apply(context, args);\n    };\n    var callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) func.apply(context, args);\n  };\n}\nfunction getLastOfPath(object, path, Empty) {\n  function cleanKey(key) {\n    return key && key.indexOf('###') > -1 ? key.replace(/###/g, '.') : key;\n  }\n  var stack = typeof path !== 'string' ? [].concat(path) : path.split('.');\n  while (stack.length > 1) {\n    if (!object) return {};\n    var key = cleanKey(stack.shift());\n    if (!object[key] && Empty) object[key] = new Empty();\n    object = object[key];\n  }\n  if (!object) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack.shift())\n  };\n}\nexport function setPath(object, path, newValue) {\n  var _getLastOfPath = getLastOfPath(object, path, Object),\n    obj = _getLastOfPath.obj,\n    k = _getLastOfPath.k;\n  if (Array.isArray(obj) && isNaN(k)) throw new Error(\"Cannot create property \\\"\".concat(k, \"\\\" here since object is an array\"));\n  obj[k] = newValue;\n}\nexport function pushPath(object, path, newValue, concat) {\n  var _getLastOfPath2 = getLastOfPath(object, path, Object),\n    obj = _getLastOfPath2.obj,\n    k = _getLastOfPath2.k;\n  obj[k] = obj[k] || [];\n  if (concat) obj[k] = obj[k].concat(newValue);\n  if (!concat) obj[k].push(newValue);\n}\nexport function getPath(object, path) {\n  var _getLastOfPath3 = getLastOfPath(object, path),\n    obj = _getLastOfPath3.obj,\n    k = _getLastOfPath3.k;\n  if (!obj) return undefined;\n  return obj[k];\n}", "function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar Space_Separator = /[\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar ID_Start = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF19]|\\uD806[\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]/;\nvar ID_Continue = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D00-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF9\\u1D00-\\u1DF9\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC6F\\uDC7F-\\uDCBA\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDCA-\\uDDCC\\uDDD0-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3C-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB7\\uDEC0-\\uDEC9\\uDF00-\\uDF19\\uDF1D-\\uDF2B\\uDF30-\\uDF39]|\\uD806[\\uDCA0-\\uDCE9\\uDCFF\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE83\\uDE86-\\uDE99\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50-\\uDF7E\\uDF8F-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4A\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uDB40[\\uDD00-\\uDDEF]/;\nvar unicode = {\n  Space_Separator: Space_Separator,\n  ID_Start: ID_Start,\n  ID_Continue: ID_Continue\n};\nvar util = {\n  isSpaceSeparator: function isSpaceSeparator(c) {\n    return typeof c === 'string' && unicode.Space_Separator.test(c);\n  },\n  isIdStartChar: function isIdStartChar(c) {\n    return typeof c === 'string' && (c >= 'a' && c <= 'z' || c >= 'A' && c <= 'Z' || c === '$' || c === '_' || unicode.ID_Start.test(c));\n  },\n  isIdContinueChar: function isIdContinueChar(c) {\n    return typeof c === 'string' && (c >= 'a' && c <= 'z' || c >= 'A' && c <= 'Z' || c >= '0' && c <= '9' || c === '$' || c === '_' || c === \"\\u200C\" || c === \"\\u200D\" || unicode.ID_Continue.test(c));\n  },\n  isDigit: function isDigit(c) {\n    return typeof c === 'string' && /[0-9]/.test(c);\n  },\n  isHexDigit: function isHexDigit(c) {\n    return typeof c === 'string' && /[0-9A-Fa-f]/.test(c);\n  }\n};\nvar source;\nvar parseState;\nvar stack;\nvar pos;\nvar line;\nvar column;\nvar token;\nvar key;\nvar root;\nvar parse = function parse(text, reviver) {\n  source = String(text);\n  parseState = 'start';\n  stack = [];\n  pos = 0;\n  line = 1;\n  column = 0;\n  token = undefined;\n  key = undefined;\n  root = undefined;\n  do {\n    token = lex();\n    parseStates[parseState]();\n  } while (token.type !== 'eof');\n  if (typeof reviver === 'function') {\n    return internalize({\n      '': root\n    }, '', reviver);\n  }\n  return root;\n};\nfunction internalize(holder, name, reviver) {\n  var value = holder[name];\n  if (value != null && _typeof(value) === 'object') {\n    if (Array.isArray(value)) {\n      for (var i = 0; i < value.length; i++) {\n        var _key = String(i);\n        var replacement = internalize(value, _key, reviver);\n        if (replacement === undefined) {\n          delete value[_key];\n        } else {\n          Object.defineProperty(value, _key, {\n            value: replacement,\n            writable: true,\n            enumerable: true,\n            configurable: true\n          });\n        }\n      }\n    } else {\n      for (var _key2 in value) {\n        var _replacement = internalize(value, _key2, reviver);\n        if (_replacement === undefined) {\n          delete value[_key2];\n        } else {\n          Object.defineProperty(value, _key2, {\n            value: _replacement,\n            writable: true,\n            enumerable: true,\n            configurable: true\n          });\n        }\n      }\n    }\n  }\n  return reviver.call(holder, name, value);\n}\nvar lexState;\nvar buffer;\nvar doubleQuote;\nvar _sign;\nvar c;\nfunction lex() {\n  lexState = 'default';\n  buffer = '';\n  doubleQuote = false;\n  _sign = 1;\n  for (;;) {\n    c = peek();\n    var _token = lexStates[lexState]();\n    if (_token) {\n      return _token;\n    }\n  }\n}\nfunction peek() {\n  if (source[pos]) {\n    return String.fromCodePoint(source.codePointAt(pos));\n  }\n}\nfunction read() {\n  var c = peek();\n  if (c === '\\n') {\n    line++;\n    column = 0;\n  } else if (c) {\n    column += c.length;\n  } else {\n    column++;\n  }\n  if (c) {\n    pos += c.length;\n  }\n  return c;\n}\nvar lexStates = {\n  default: function _default() {\n    switch (c) {\n      case '\\t':\n      case '\\v':\n      case '\\f':\n      case ' ':\n      case \"\\xA0\":\n      case \"\\uFEFF\":\n      case '\\n':\n      case '\\r':\n      case \"\\u2028\":\n      case \"\\u2029\":\n        read();\n        return;\n      case '/':\n        read();\n        lexState = 'comment';\n        return;\n      case undefined:\n        read();\n        return newToken('eof');\n    }\n    if (util.isSpaceSeparator(c)) {\n      read();\n      return;\n    }\n    return lexStates[parseState]();\n  },\n  comment: function comment() {\n    switch (c) {\n      case '*':\n        read();\n        lexState = 'multiLineComment';\n        return;\n      case '/':\n        read();\n        lexState = 'singleLineComment';\n        return;\n    }\n    throw invalidChar(read());\n  },\n  multiLineComment: function multiLineComment() {\n    switch (c) {\n      case '*':\n        read();\n        lexState = 'multiLineCommentAsterisk';\n        return;\n      case undefined:\n        throw invalidChar(read());\n    }\n    read();\n  },\n  multiLineCommentAsterisk: function multiLineCommentAsterisk() {\n    switch (c) {\n      case '*':\n        read();\n        return;\n      case '/':\n        read();\n        lexState = 'default';\n        return;\n      case undefined:\n        throw invalidChar(read());\n    }\n    read();\n    lexState = 'multiLineComment';\n  },\n  singleLineComment: function singleLineComment() {\n    switch (c) {\n      case '\\n':\n      case '\\r':\n      case \"\\u2028\":\n      case \"\\u2029\":\n        read();\n        lexState = 'default';\n        return;\n      case undefined:\n        read();\n        return newToken('eof');\n    }\n    read();\n  },\n  value: function value() {\n    switch (c) {\n      case '{':\n      case '[':\n        return newToken('punctuator', read());\n      case 'n':\n        read();\n        literal('ull');\n        return newToken('null', null);\n      case 't':\n        read();\n        literal('rue');\n        return newToken('boolean', true);\n      case 'f':\n        read();\n        literal('alse');\n        return newToken('boolean', false);\n      case '-':\n      case '+':\n        if (read() === '-') {\n          _sign = -1;\n        }\n        lexState = 'sign';\n        return;\n      case '.':\n        buffer = read();\n        lexState = 'decimalPointLeading';\n        return;\n      case '0':\n        buffer = read();\n        lexState = 'zero';\n        return;\n      case '1':\n      case '2':\n      case '3':\n      case '4':\n      case '5':\n      case '6':\n      case '7':\n      case '8':\n      case '9':\n        buffer = read();\n        lexState = 'decimalInteger';\n        return;\n      case 'I':\n        read();\n        literal('nfinity');\n        return newToken('numeric', Infinity);\n      case 'N':\n        read();\n        literal('aN');\n        return newToken('numeric', NaN);\n      case '\"':\n      case \"'\":\n        doubleQuote = read() === '\"';\n        buffer = '';\n        lexState = 'string';\n        return;\n    }\n    throw invalidChar(read());\n  },\n  identifierNameStartEscape: function identifierNameStartEscape() {\n    if (c !== 'u') {\n      throw invalidChar(read());\n    }\n    read();\n    var u = unicodeEscape();\n    switch (u) {\n      case '$':\n      case '_':\n        break;\n      default:\n        if (!util.isIdStartChar(u)) {\n          throw invalidIdentifier();\n        }\n        break;\n    }\n    buffer += u;\n    lexState = 'identifierName';\n  },\n  identifierName: function identifierName() {\n    switch (c) {\n      case '$':\n      case '_':\n      case \"\\u200C\":\n      case \"\\u200D\":\n        buffer += read();\n        return;\n      case '\\\\':\n        read();\n        lexState = 'identifierNameEscape';\n        return;\n    }\n    if (util.isIdContinueChar(c)) {\n      buffer += read();\n      return;\n    }\n    return newToken('identifier', buffer);\n  },\n  identifierNameEscape: function identifierNameEscape() {\n    if (c !== 'u') {\n      throw invalidChar(read());\n    }\n    read();\n    var u = unicodeEscape();\n    switch (u) {\n      case '$':\n      case '_':\n      case \"\\u200C\":\n      case \"\\u200D\":\n        break;\n      default:\n        if (!util.isIdContinueChar(u)) {\n          throw invalidIdentifier();\n        }\n        break;\n    }\n    buffer += u;\n    lexState = 'identifierName';\n  },\n  sign: function sign() {\n    switch (c) {\n      case '.':\n        buffer = read();\n        lexState = 'decimalPointLeading';\n        return;\n      case '0':\n        buffer = read();\n        lexState = 'zero';\n        return;\n      case '1':\n      case '2':\n      case '3':\n      case '4':\n      case '5':\n      case '6':\n      case '7':\n      case '8':\n      case '9':\n        buffer = read();\n        lexState = 'decimalInteger';\n        return;\n      case 'I':\n        read();\n        literal('nfinity');\n        return newToken('numeric', _sign * Infinity);\n      case 'N':\n        read();\n        literal('aN');\n        return newToken('numeric', NaN);\n    }\n    throw invalidChar(read());\n  },\n  zero: function zero() {\n    switch (c) {\n      case '.':\n        buffer += read();\n        lexState = 'decimalPoint';\n        return;\n      case 'e':\n      case 'E':\n        buffer += read();\n        lexState = 'decimalExponent';\n        return;\n      case 'x':\n      case 'X':\n        buffer += read();\n        lexState = 'hexadecimal';\n        return;\n    }\n    return newToken('numeric', _sign * 0);\n  },\n  decimalInteger: function decimalInteger() {\n    switch (c) {\n      case '.':\n        buffer += read();\n        lexState = 'decimalPoint';\n        return;\n      case 'e':\n      case 'E':\n        buffer += read();\n        lexState = 'decimalExponent';\n        return;\n    }\n    if (util.isDigit(c)) {\n      buffer += read();\n      return;\n    }\n    return newToken('numeric', _sign * Number(buffer));\n  },\n  decimalPointLeading: function decimalPointLeading() {\n    if (util.isDigit(c)) {\n      buffer += read();\n      lexState = 'decimalFraction';\n      return;\n    }\n    throw invalidChar(read());\n  },\n  decimalPoint: function decimalPoint() {\n    switch (c) {\n      case 'e':\n      case 'E':\n        buffer += read();\n        lexState = 'decimalExponent';\n        return;\n    }\n    if (util.isDigit(c)) {\n      buffer += read();\n      lexState = 'decimalFraction';\n      return;\n    }\n    return newToken('numeric', _sign * Number(buffer));\n  },\n  decimalFraction: function decimalFraction() {\n    switch (c) {\n      case 'e':\n      case 'E':\n        buffer += read();\n        lexState = 'decimalExponent';\n        return;\n    }\n    if (util.isDigit(c)) {\n      buffer += read();\n      return;\n    }\n    return newToken('numeric', _sign * Number(buffer));\n  },\n  decimalExponent: function decimalExponent() {\n    switch (c) {\n      case '+':\n      case '-':\n        buffer += read();\n        lexState = 'decimalExponentSign';\n        return;\n    }\n    if (util.isDigit(c)) {\n      buffer += read();\n      lexState = 'decimalExponentInteger';\n      return;\n    }\n    throw invalidChar(read());\n  },\n  decimalExponentSign: function decimalExponentSign() {\n    if (util.isDigit(c)) {\n      buffer += read();\n      lexState = 'decimalExponentInteger';\n      return;\n    }\n    throw invalidChar(read());\n  },\n  decimalExponentInteger: function decimalExponentInteger() {\n    if (util.isDigit(c)) {\n      buffer += read();\n      return;\n    }\n    return newToken('numeric', _sign * Number(buffer));\n  },\n  hexadecimal: function hexadecimal() {\n    if (util.isHexDigit(c)) {\n      buffer += read();\n      lexState = 'hexadecimalInteger';\n      return;\n    }\n    throw invalidChar(read());\n  },\n  hexadecimalInteger: function hexadecimalInteger() {\n    if (util.isHexDigit(c)) {\n      buffer += read();\n      return;\n    }\n    return newToken('numeric', _sign * Number(buffer));\n  },\n  string: function string() {\n    switch (c) {\n      case '\\\\':\n        read();\n        buffer += escape();\n        return;\n      case '\"':\n        if (doubleQuote) {\n          read();\n          return newToken('string', buffer);\n        }\n        buffer += read();\n        return;\n      case \"'\":\n        if (!doubleQuote) {\n          read();\n          return newToken('string', buffer);\n        }\n        buffer += read();\n        return;\n      case '\\n':\n      case '\\r':\n        throw invalidChar(read());\n      case \"\\u2028\":\n      case \"\\u2029\":\n        separatorChar(c);\n        break;\n      case undefined:\n        throw invalidChar(read());\n    }\n    buffer += read();\n  },\n  start: function start() {\n    switch (c) {\n      case '{':\n      case '[':\n        return newToken('punctuator', read());\n    }\n    lexState = 'value';\n  },\n  beforePropertyName: function beforePropertyName() {\n    switch (c) {\n      case '$':\n      case '_':\n        buffer = read();\n        lexState = 'identifierName';\n        return;\n      case '\\\\':\n        read();\n        lexState = 'identifierNameStartEscape';\n        return;\n      case '}':\n        return newToken('punctuator', read());\n      case '\"':\n      case \"'\":\n        doubleQuote = read() === '\"';\n        lexState = 'string';\n        return;\n    }\n    if (util.isIdStartChar(c)) {\n      buffer += read();\n      lexState = 'identifierName';\n      return;\n    }\n    throw invalidChar(read());\n  },\n  afterPropertyName: function afterPropertyName() {\n    if (c === ':') {\n      return newToken('punctuator', read());\n    }\n    throw invalidChar(read());\n  },\n  beforePropertyValue: function beforePropertyValue() {\n    lexState = 'value';\n  },\n  afterPropertyValue: function afterPropertyValue() {\n    switch (c) {\n      case ',':\n      case '}':\n        return newToken('punctuator', read());\n    }\n    throw invalidChar(read());\n  },\n  beforeArrayValue: function beforeArrayValue() {\n    if (c === ']') {\n      return newToken('punctuator', read());\n    }\n    lexState = 'value';\n  },\n  afterArrayValue: function afterArrayValue() {\n    switch (c) {\n      case ',':\n      case ']':\n        return newToken('punctuator', read());\n    }\n    throw invalidChar(read());\n  },\n  end: function end() {\n    throw invalidChar(read());\n  }\n};\nfunction newToken(type, value) {\n  return {\n    type: type,\n    value: value,\n    line: line,\n    column: column\n  };\n}\nfunction literal(s) {\n  var _iterator = _createForOfIteratorHelper(s),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var _c = _step.value;\n      var p = peek();\n      if (p !== _c) {\n        throw invalidChar(read());\n      }\n      read();\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n}\nfunction escape() {\n  var c = peek();\n  switch (c) {\n    case 'b':\n      read();\n      return '\\b';\n    case 'f':\n      read();\n      return '\\f';\n    case 'n':\n      read();\n      return '\\n';\n    case 'r':\n      read();\n      return '\\r';\n    case 't':\n      read();\n      return '\\t';\n    case 'v':\n      read();\n      return '\\v';\n    case '0':\n      read();\n      if (util.isDigit(peek())) {\n        throw invalidChar(read());\n      }\n      return '\\0';\n    case 'x':\n      read();\n      return hexEscape();\n    case 'u':\n      read();\n      return unicodeEscape();\n    case '\\n':\n    case \"\\u2028\":\n    case \"\\u2029\":\n      read();\n      return '';\n    case '\\r':\n      read();\n      if (peek() === '\\n') {\n        read();\n      }\n      return '';\n    case '1':\n    case '2':\n    case '3':\n    case '4':\n    case '5':\n    case '6':\n    case '7':\n    case '8':\n    case '9':\n      throw invalidChar(read());\n    case undefined:\n      throw invalidChar(read());\n  }\n  return read();\n}\nfunction hexEscape() {\n  var buffer = '';\n  var c = peek();\n  if (!util.isHexDigit(c)) {\n    throw invalidChar(read());\n  }\n  buffer += read();\n  c = peek();\n  if (!util.isHexDigit(c)) {\n    throw invalidChar(read());\n  }\n  buffer += read();\n  return String.fromCodePoint(parseInt(buffer, 16));\n}\nfunction unicodeEscape() {\n  var buffer = '';\n  var count = 4;\n  while (count-- > 0) {\n    var _c2 = peek();\n    if (!util.isHexDigit(_c2)) {\n      throw invalidChar(read());\n    }\n    buffer += read();\n  }\n  return String.fromCodePoint(parseInt(buffer, 16));\n}\nvar parseStates = {\n  start: function start() {\n    if (token.type === 'eof') {\n      throw invalidEOF();\n    }\n    push();\n  },\n  beforePropertyName: function beforePropertyName() {\n    switch (token.type) {\n      case 'identifier':\n      case 'string':\n        key = token.value;\n        parseState = 'afterPropertyName';\n        return;\n      case 'punctuator':\n        pop();\n        return;\n      case 'eof':\n        throw invalidEOF();\n    }\n  },\n  afterPropertyName: function afterPropertyName() {\n    if (token.type === 'eof') {\n      throw invalidEOF();\n    }\n    parseState = 'beforePropertyValue';\n  },\n  beforePropertyValue: function beforePropertyValue() {\n    if (token.type === 'eof') {\n      throw invalidEOF();\n    }\n    push();\n  },\n  beforeArrayValue: function beforeArrayValue() {\n    if (token.type === 'eof') {\n      throw invalidEOF();\n    }\n    if (token.type === 'punctuator' && token.value === ']') {\n      pop();\n      return;\n    }\n    push();\n  },\n  afterPropertyValue: function afterPropertyValue() {\n    if (token.type === 'eof') {\n      throw invalidEOF();\n    }\n    switch (token.value) {\n      case ',':\n        parseState = 'beforePropertyName';\n        return;\n      case '}':\n        pop();\n    }\n  },\n  afterArrayValue: function afterArrayValue() {\n    if (token.type === 'eof') {\n      throw invalidEOF();\n    }\n    switch (token.value) {\n      case ',':\n        parseState = 'beforeArrayValue';\n        return;\n      case ']':\n        pop();\n    }\n  },\n  end: function end() {}\n};\nfunction push() {\n  var value;\n  switch (token.type) {\n    case 'punctuator':\n      switch (token.value) {\n        case '{':\n          value = {};\n          break;\n        case '[':\n          value = [];\n          break;\n      }\n      break;\n    case 'null':\n    case 'boolean':\n    case 'numeric':\n    case 'string':\n      value = token.value;\n      break;\n  }\n  if (root === undefined) {\n    root = value;\n  } else {\n    var parent = stack[stack.length - 1];\n    if (Array.isArray(parent)) {\n      parent.push(value);\n    } else {\n      Object.defineProperty(parent, key, {\n        value: value,\n        writable: true,\n        enumerable: true,\n        configurable: true\n      });\n    }\n  }\n  if (value !== null && _typeof(value) === 'object') {\n    stack.push(value);\n    if (Array.isArray(value)) {\n      parseState = 'beforeArrayValue';\n    } else {\n      parseState = 'beforePropertyName';\n    }\n  } else {\n    var current = stack[stack.length - 1];\n    if (current == null) {\n      parseState = 'end';\n    } else if (Array.isArray(current)) {\n      parseState = 'afterArrayValue';\n    } else {\n      parseState = 'afterPropertyValue';\n    }\n  }\n}\nfunction pop() {\n  stack.pop();\n  var current = stack[stack.length - 1];\n  if (current == null) {\n    parseState = 'end';\n  } else if (Array.isArray(current)) {\n    parseState = 'afterArrayValue';\n  } else {\n    parseState = 'afterPropertyValue';\n  }\n}\nfunction invalidChar(c) {\n  if (c === undefined) {\n    return syntaxError(\"JSON5: invalid end of input at \".concat(line, \":\").concat(column));\n  }\n  return syntaxError(\"JSON5: invalid character '\".concat(formatChar(c), \"' at \").concat(line, \":\").concat(column));\n}\nfunction invalidEOF() {\n  return syntaxError(\"JSON5: invalid end of input at \".concat(line, \":\").concat(column));\n}\nfunction invalidIdentifier() {\n  column -= 5;\n  return syntaxError(\"JSON5: invalid identifier character at \".concat(line, \":\").concat(column));\n}\nfunction separatorChar(c) {\n  console.warn(\"JSON5: '\".concat(formatChar(c), \"' in strings is not valid ECMAScript; consider escaping\"));\n}\nfunction formatChar(c) {\n  var replacements = {\n    \"'\": \"\\\\'\",\n    '\"': '\\\\\"',\n    '\\\\': '\\\\\\\\',\n    '\\b': '\\\\b',\n    '\\f': '\\\\f',\n    '\\n': '\\\\n',\n    '\\r': '\\\\r',\n    '\\t': '\\\\t',\n    '\\v': '\\\\v',\n    '\\0': '\\\\0',\n    \"\\u2028\": \"\\\\u2028\",\n    \"\\u2029\": \"\\\\u2029\"\n  };\n  if (replacements[c]) {\n    return replacements[c];\n  }\n  if (c < ' ') {\n    var hexString = c.charCodeAt(0).toString(16);\n    return '\\\\x' + ('00' + hexString).substring(hexString.length);\n  }\n  return c;\n}\nfunction syntaxError(message) {\n  var err = new SyntaxError(message);\n  err.lineNumber = line;\n  err.columnNumber = column;\n  return err;\n}\nvar stringify = function stringify(value, replacer, space) {\n  var stack = [];\n  var indent = '';\n  var propertyList;\n  var replacerFunc;\n  var gap = '';\n  var quote;\n  if (replacer != null && _typeof(replacer) === 'object' && !Array.isArray(replacer)) {\n    space = replacer.space;\n    quote = replacer.quote;\n    replacer = replacer.replacer;\n  }\n  if (typeof replacer === 'function') {\n    replacerFunc = replacer;\n  } else if (Array.isArray(replacer)) {\n    propertyList = [];\n    var _iterator2 = _createForOfIteratorHelper(replacer),\n      _step2;\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var v = _step2.value;\n        var item = void 0;\n        if (typeof v === 'string') {\n          item = v;\n        } else if (typeof v === 'number' || v instanceof String || v instanceof Number) {\n          item = String(v);\n        }\n        if (item !== undefined && propertyList.indexOf(item) < 0) {\n          propertyList.push(item);\n        }\n      }\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n  }\n  if (space instanceof Number) {\n    space = Number(space);\n  } else if (space instanceof String) {\n    space = String(space);\n  }\n  if (typeof space === 'number') {\n    if (space > 0) {\n      space = Math.min(10, Math.floor(space));\n      gap = '          '.substr(0, space);\n    }\n  } else if (typeof space === 'string') {\n    gap = space.substr(0, 10);\n  }\n  return serializeProperty('', {\n    '': value\n  });\n  function serializeProperty(key, holder) {\n    var value = holder[key];\n    if (value != null) {\n      if (typeof value.toJSON5 === 'function') {\n        value = value.toJSON5(key);\n      } else if (typeof value.toJSON === 'function') {\n        value = value.toJSON(key);\n      }\n    }\n    if (replacerFunc) {\n      value = replacerFunc.call(holder, key, value);\n    }\n    if (value instanceof Number) {\n      value = Number(value);\n    } else if (value instanceof String) {\n      value = String(value);\n    } else if (value instanceof Boolean) {\n      value = value.valueOf();\n    }\n    switch (value) {\n      case null:\n        return 'null';\n      case true:\n        return 'true';\n      case false:\n        return 'false';\n    }\n    if (typeof value === 'string') {\n      return quoteString(value, false);\n    }\n    if (typeof value === 'number') {\n      return String(value);\n    }\n    if (_typeof(value) === 'object') {\n      return Array.isArray(value) ? serializeArray(value) : serializeObject(value);\n    }\n    return undefined;\n  }\n  function quoteString(value) {\n    var quotes = {\n      \"'\": 0.1,\n      '\"': 0.2\n    };\n    var replacements = {\n      \"'\": \"\\\\'\",\n      '\"': '\\\\\"',\n      '\\\\': '\\\\\\\\',\n      '\\b': '\\\\b',\n      '\\f': '\\\\f',\n      '\\n': '\\\\n',\n      '\\r': '\\\\r',\n      '\\t': '\\\\t',\n      '\\v': '\\\\v',\n      '\\0': '\\\\0',\n      \"\\u2028\": \"\\\\u2028\",\n      \"\\u2029\": \"\\\\u2029\"\n    };\n    var product = '';\n    for (var i = 0; i < value.length; i++) {\n      var _c3 = value[i];\n      switch (_c3) {\n        case \"'\":\n        case '\"':\n          quotes[_c3]++;\n          product += _c3;\n          continue;\n        case '\\0':\n          if (util.isDigit(value[i + 1])) {\n            product += '\\\\x00';\n            continue;\n          }\n      }\n      if (replacements[_c3]) {\n        product += replacements[_c3];\n        continue;\n      }\n      if (_c3 < ' ') {\n        var hexString = _c3.charCodeAt(0).toString(16);\n        product += '\\\\x' + ('00' + hexString).substring(hexString.length);\n        continue;\n      }\n      product += _c3;\n    }\n    var quoteChar = quote || Object.keys(quotes).reduce(function (a, b) {\n      return quotes[a] < quotes[b] ? a : b;\n    });\n    product = product.replace(new RegExp(quoteChar, 'g'), replacements[quoteChar]);\n    return quoteChar + product + quoteChar;\n  }\n  function serializeObject(value) {\n    if (stack.indexOf(value) >= 0) {\n      throw TypeError('Converting circular structure to JSON5');\n    }\n    stack.push(value);\n    var stepback = indent;\n    indent = indent + gap;\n    var keys = propertyList || Object.keys(value);\n    var partial = [];\n    var _iterator3 = _createForOfIteratorHelper(keys),\n      _step3;\n    try {\n      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n        var _key3 = _step3.value;\n        var propertyString = serializeProperty(_key3, value);\n        if (propertyString !== undefined) {\n          var member = serializeKey(_key3) + ':';\n          if (gap !== '') {\n            member += ' ';\n          }\n          member += propertyString;\n          partial.push(member);\n        }\n      }\n    } catch (err) {\n      _iterator3.e(err);\n    } finally {\n      _iterator3.f();\n    }\n    var final;\n    if (partial.length === 0) {\n      final = '{}';\n    } else {\n      var properties;\n      if (gap === '') {\n        properties = partial.join(',');\n        final = '{' + properties + '}';\n      } else {\n        var separator = ',\\n' + indent;\n        properties = partial.join(separator);\n        final = '{\\n' + indent + properties + ',\\n' + stepback + '}';\n      }\n    }\n    stack.pop();\n    indent = stepback;\n    return final;\n  }\n  function serializeKey(key) {\n    if (key.length === 0) {\n      return quoteString(key, true);\n    }\n    var firstChar = String.fromCodePoint(key.codePointAt(0));\n    if (!util.isIdStartChar(firstChar)) {\n      return quoteString(key, true);\n    }\n    for (var i = firstChar.length; i < key.length; i++) {\n      if (!util.isIdContinueChar(String.fromCodePoint(key.codePointAt(i)))) {\n        return quoteString(key, true);\n      }\n    }\n    return key;\n  }\n  function serializeArray(value) {\n    if (stack.indexOf(value) >= 0) {\n      throw TypeError('Converting circular structure to JSON5');\n    }\n    stack.push(value);\n    var stepback = indent;\n    indent = indent + gap;\n    var partial = [];\n    for (var i = 0; i < value.length; i++) {\n      var propertyString = serializeProperty(String(i), value);\n      partial.push(propertyString !== undefined ? propertyString : 'null');\n    }\n    var final;\n    if (partial.length === 0) {\n      final = '[]';\n    } else {\n      if (gap === '') {\n        var properties = partial.join(',');\n        final = '[' + properties + ']';\n      } else {\n        var separator = ',\\n' + indent;\n        var _properties = partial.join(separator);\n        final = '[\\n' + indent + _properties + ',\\n' + stepback + ']';\n      }\n    }\n    stack.pop();\n    indent = stepback;\n    return final;\n  }\n};\nvar JSON5 = {\n  parse: parse,\n  stringify: stringify\n};\nvar lib = JSON5;\nexport default lib;", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction createScanner(text) {\n  var ignoreTrivia = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var len = text.length;\n  var pos = 0,\n    value = \"\",\n    tokenOffset = 0,\n    token = 16,\n    lineNumber = 0,\n    lineStartOffset = 0,\n    tokenLineStartOffset = 0,\n    prevTokenLineStartOffset = 0,\n    scanError = 0;\n  function scanHexDigits(count, exact) {\n    var digits = 0;\n    var value2 = 0;\n    while (digits < count || !exact) {\n      var ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    var start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    var end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    var result = \"\",\n      start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      var ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        var ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            var ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    var code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        var start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          var safeLength = len - 1;\n          var commentClosed = false;\n          while (pos < safeLength) {\n            var ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    var result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition: setPosition,\n    getPosition: function getPosition() {\n      return pos;\n    },\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: function getToken() {\n      return token;\n    },\n    getTokenValue: function getTokenValue() {\n      return value;\n    },\n    getTokenOffset: function getTokenOffset() {\n      return tokenOffset;\n    },\n    getTokenLength: function getTokenLength() {\n      return pos - tokenOffset;\n    },\n    getTokenStartLine: function getTokenStartLine() {\n      return lineStartOffset;\n    },\n    getTokenStartCharacter: function getTokenStartCharacter() {\n      return tokenOffset - prevTokenLineStartOffset;\n    },\n    getTokenError: function getTokenError() {\n      return scanError;\n    }\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\nvar CharacterCodes;\n(function (CharacterCodes2) {\n  CharacterCodes2[CharacterCodes2[\"lineFeed\"] = 10] = \"lineFeed\";\n  CharacterCodes2[CharacterCodes2[\"carriageReturn\"] = 13] = \"carriageReturn\";\n  CharacterCodes2[CharacterCodes2[\"space\"] = 32] = \"space\";\n  CharacterCodes2[CharacterCodes2[\"_0\"] = 48] = \"_0\";\n  CharacterCodes2[CharacterCodes2[\"_1\"] = 49] = \"_1\";\n  CharacterCodes2[CharacterCodes2[\"_2\"] = 50] = \"_2\";\n  CharacterCodes2[CharacterCodes2[\"_3\"] = 51] = \"_3\";\n  CharacterCodes2[CharacterCodes2[\"_4\"] = 52] = \"_4\";\n  CharacterCodes2[CharacterCodes2[\"_5\"] = 53] = \"_5\";\n  CharacterCodes2[CharacterCodes2[\"_6\"] = 54] = \"_6\";\n  CharacterCodes2[CharacterCodes2[\"_7\"] = 55] = \"_7\";\n  CharacterCodes2[CharacterCodes2[\"_8\"] = 56] = \"_8\";\n  CharacterCodes2[CharacterCodes2[\"_9\"] = 57] = \"_9\";\n  CharacterCodes2[CharacterCodes2[\"a\"] = 97] = \"a\";\n  CharacterCodes2[CharacterCodes2[\"b\"] = 98] = \"b\";\n  CharacterCodes2[CharacterCodes2[\"c\"] = 99] = \"c\";\n  CharacterCodes2[CharacterCodes2[\"d\"] = 100] = \"d\";\n  CharacterCodes2[CharacterCodes2[\"e\"] = 101] = \"e\";\n  CharacterCodes2[CharacterCodes2[\"f\"] = 102] = \"f\";\n  CharacterCodes2[CharacterCodes2[\"g\"] = 103] = \"g\";\n  CharacterCodes2[CharacterCodes2[\"h\"] = 104] = \"h\";\n  CharacterCodes2[CharacterCodes2[\"i\"] = 105] = \"i\";\n  CharacterCodes2[CharacterCodes2[\"j\"] = 106] = \"j\";\n  CharacterCodes2[CharacterCodes2[\"k\"] = 107] = \"k\";\n  CharacterCodes2[CharacterCodes2[\"l\"] = 108] = \"l\";\n  CharacterCodes2[CharacterCodes2[\"m\"] = 109] = \"m\";\n  CharacterCodes2[CharacterCodes2[\"n\"] = 110] = \"n\";\n  CharacterCodes2[CharacterCodes2[\"o\"] = 111] = \"o\";\n  CharacterCodes2[CharacterCodes2[\"p\"] = 112] = \"p\";\n  CharacterCodes2[CharacterCodes2[\"q\"] = 113] = \"q\";\n  CharacterCodes2[CharacterCodes2[\"r\"] = 114] = \"r\";\n  CharacterCodes2[CharacterCodes2[\"s\"] = 115] = \"s\";\n  CharacterCodes2[CharacterCodes2[\"t\"] = 116] = \"t\";\n  CharacterCodes2[CharacterCodes2[\"u\"] = 117] = \"u\";\n  CharacterCodes2[CharacterCodes2[\"v\"] = 118] = \"v\";\n  CharacterCodes2[CharacterCodes2[\"w\"] = 119] = \"w\";\n  CharacterCodes2[CharacterCodes2[\"x\"] = 120] = \"x\";\n  CharacterCodes2[CharacterCodes2[\"y\"] = 121] = \"y\";\n  CharacterCodes2[CharacterCodes2[\"z\"] = 122] = \"z\";\n  CharacterCodes2[CharacterCodes2[\"A\"] = 65] = \"A\";\n  CharacterCodes2[CharacterCodes2[\"B\"] = 66] = \"B\";\n  CharacterCodes2[CharacterCodes2[\"C\"] = 67] = \"C\";\n  CharacterCodes2[CharacterCodes2[\"D\"] = 68] = \"D\";\n  CharacterCodes2[CharacterCodes2[\"E\"] = 69] = \"E\";\n  CharacterCodes2[CharacterCodes2[\"F\"] = 70] = \"F\";\n  CharacterCodes2[CharacterCodes2[\"G\"] = 71] = \"G\";\n  CharacterCodes2[CharacterCodes2[\"H\"] = 72] = \"H\";\n  CharacterCodes2[CharacterCodes2[\"I\"] = 73] = \"I\";\n  CharacterCodes2[CharacterCodes2[\"J\"] = 74] = \"J\";\n  CharacterCodes2[CharacterCodes2[\"K\"] = 75] = \"K\";\n  CharacterCodes2[CharacterCodes2[\"L\"] = 76] = \"L\";\n  CharacterCodes2[CharacterCodes2[\"M\"] = 77] = \"M\";\n  CharacterCodes2[CharacterCodes2[\"N\"] = 78] = \"N\";\n  CharacterCodes2[CharacterCodes2[\"O\"] = 79] = \"O\";\n  CharacterCodes2[CharacterCodes2[\"P\"] = 80] = \"P\";\n  CharacterCodes2[CharacterCodes2[\"Q\"] = 81] = \"Q\";\n  CharacterCodes2[CharacterCodes2[\"R\"] = 82] = \"R\";\n  CharacterCodes2[CharacterCodes2[\"S\"] = 83] = \"S\";\n  CharacterCodes2[CharacterCodes2[\"T\"] = 84] = \"T\";\n  CharacterCodes2[CharacterCodes2[\"U\"] = 85] = \"U\";\n  CharacterCodes2[CharacterCodes2[\"V\"] = 86] = \"V\";\n  CharacterCodes2[CharacterCodes2[\"W\"] = 87] = \"W\";\n  CharacterCodes2[CharacterCodes2[\"X\"] = 88] = \"X\";\n  CharacterCodes2[CharacterCodes2[\"Y\"] = 89] = \"Y\";\n  CharacterCodes2[CharacterCodes2[\"Z\"] = 90] = \"Z\";\n  CharacterCodes2[CharacterCodes2[\"asterisk\"] = 42] = \"asterisk\";\n  CharacterCodes2[CharacterCodes2[\"backslash\"] = 92] = \"backslash\";\n  CharacterCodes2[CharacterCodes2[\"closeBrace\"] = 125] = \"closeBrace\";\n  CharacterCodes2[CharacterCodes2[\"closeBracket\"] = 93] = \"closeBracket\";\n  CharacterCodes2[CharacterCodes2[\"colon\"] = 58] = \"colon\";\n  CharacterCodes2[CharacterCodes2[\"comma\"] = 44] = \"comma\";\n  CharacterCodes2[CharacterCodes2[\"dot\"] = 46] = \"dot\";\n  CharacterCodes2[CharacterCodes2[\"doubleQuote\"] = 34] = \"doubleQuote\";\n  CharacterCodes2[CharacterCodes2[\"minus\"] = 45] = \"minus\";\n  CharacterCodes2[CharacterCodes2[\"openBrace\"] = 123] = \"openBrace\";\n  CharacterCodes2[CharacterCodes2[\"openBracket\"] = 91] = \"openBracket\";\n  CharacterCodes2[CharacterCodes2[\"plus\"] = 43] = \"plus\";\n  CharacterCodes2[CharacterCodes2[\"slash\"] = 47] = \"slash\";\n  CharacterCodes2[CharacterCodes2[\"formFeed\"] = 12] = \"formFeed\";\n  CharacterCodes2[CharacterCodes2[\"tab\"] = 9] = \"tab\";\n})(CharacterCodes || (CharacterCodes = {}));\nvar cachedSpaces = new Array(20).fill(0).map(function (_, index) {\n  return \" \".repeat(index);\n});\nvar maxCachedValues = 200;\nvar cachedBreakLinesWithSpaces = {\n  \" \": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map(function (_, index) {\n      return \"\\n\" + \" \".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map(function (_, index) {\n      return \"\\r\" + \" \".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map(function (_, index) {\n      return \"\\r\\n\" + \" \".repeat(index);\n    })\n  },\n  \"\t\": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map(function (_, index) {\n      return \"\\n\" + \"\t\".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map(function (_, index) {\n      return \"\\r\" + \"\t\".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map(function (_, index) {\n      return \"\\r\\n\" + \"\t\".repeat(index);\n    })\n  }\n};\nvar supportedEols = [\"\\n\", \"\\r\", \"\\r\\n\"];\nfunction format(documentText, range, options) {\n  var initialIndentLevel;\n  var formatText;\n  var formatTextStart;\n  var rangeStart;\n  var rangeEnd;\n  if (range) {\n    rangeStart = range.offset;\n    rangeEnd = rangeStart + range.length;\n    formatTextStart = rangeStart;\n    while (formatTextStart > 0 && !isEOL(documentText, formatTextStart - 1)) {\n      formatTextStart--;\n    }\n    var endOffset = rangeEnd;\n    while (endOffset < documentText.length && !isEOL(documentText, endOffset)) {\n      endOffset++;\n    }\n    formatText = documentText.substring(formatTextStart, endOffset);\n    initialIndentLevel = computeIndentLevel(formatText, options);\n  } else {\n    formatText = documentText;\n    initialIndentLevel = 0;\n    formatTextStart = 0;\n    rangeStart = 0;\n    rangeEnd = documentText.length;\n  }\n  var eol = getEOL(options, documentText);\n  var eolFastPathSupported = supportedEols.includes(eol);\n  var numberLineBreaks = 0;\n  var indentLevel = 0;\n  var indentValue;\n  if (options.insertSpaces) {\n    var _cachedSpaces;\n    indentValue = (_cachedSpaces = cachedSpaces[options.tabSize || 4]) !== null && _cachedSpaces !== void 0 ? _cachedSpaces : repeat(cachedSpaces[1], options.tabSize || 4);\n  } else {\n    indentValue = \"\t\";\n  }\n  var indentType = indentValue === \"\t\" ? \"\t\" : \" \";\n  var scanner = createScanner(formatText, false);\n  var hasError = false;\n  function newLinesAndIndent() {\n    if (numberLineBreaks > 1) {\n      return repeat(eol, numberLineBreaks) + repeat(indentValue, initialIndentLevel + indentLevel);\n    }\n    var amountOfSpaces = indentValue.length * (initialIndentLevel + indentLevel);\n    if (!eolFastPathSupported || amountOfSpaces > cachedBreakLinesWithSpaces[indentType][eol].length) {\n      return eol + repeat(indentValue, initialIndentLevel + indentLevel);\n    }\n    if (amountOfSpaces <= 0) {\n      return eol;\n    }\n    return cachedBreakLinesWithSpaces[indentType][eol][amountOfSpaces];\n  }\n  function scanNext() {\n    var token = scanner.scan();\n    numberLineBreaks = 0;\n    while (token === 15 || token === 14) {\n      if (token === 14 && options.keepLines) {\n        numberLineBreaks += 1;\n      } else if (token === 14) {\n        numberLineBreaks = 1;\n      }\n      token = scanner.scan();\n    }\n    hasError = token === 16 || scanner.getTokenError() !== 0;\n    return token;\n  }\n  var editOperations = [];\n  function addEdit(text, startOffset, endOffset) {\n    if (!hasError && (!range || startOffset < rangeEnd && endOffset > rangeStart) && documentText.substring(startOffset, endOffset) !== text) {\n      editOperations.push({\n        offset: startOffset,\n        length: endOffset - startOffset,\n        content: text\n      });\n    }\n  }\n  var firstToken = scanNext();\n  if (options.keepLines && numberLineBreaks > 0) {\n    addEdit(repeat(eol, numberLineBreaks), 0, 0);\n  }\n  if (firstToken !== 17) {\n    var firstTokenStart = scanner.getTokenOffset() + formatTextStart;\n    var initialIndent = indentValue.length * initialIndentLevel < 20 && options.insertSpaces ? cachedSpaces[indentValue.length * initialIndentLevel] : repeat(indentValue, initialIndentLevel);\n    addEdit(initialIndent, formatTextStart, firstTokenStart);\n  }\n  while (firstToken !== 17) {\n    var firstTokenEnd = scanner.getTokenOffset() + scanner.getTokenLength() + formatTextStart;\n    var secondToken = scanNext();\n    var replaceContent = \"\";\n    var needsLineBreak = false;\n    while (numberLineBreaks === 0 && (secondToken === 12 || secondToken === 13)) {\n      var commentTokenStart = scanner.getTokenOffset() + formatTextStart;\n      addEdit(cachedSpaces[1], firstTokenEnd, commentTokenStart);\n      firstTokenEnd = scanner.getTokenOffset() + scanner.getTokenLength() + formatTextStart;\n      needsLineBreak = secondToken === 12;\n      replaceContent = needsLineBreak ? newLinesAndIndent() : \"\";\n      secondToken = scanNext();\n    }\n    if (secondToken === 2) {\n      if (firstToken !== 1) {\n        indentLevel--;\n      }\n      ;\n      if (options.keepLines && numberLineBreaks > 0 || !options.keepLines && firstToken !== 1) {\n        replaceContent = newLinesAndIndent();\n      } else if (options.keepLines) {\n        replaceContent = cachedSpaces[1];\n      }\n    } else if (secondToken === 4) {\n      if (firstToken !== 3) {\n        indentLevel--;\n      }\n      ;\n      if (options.keepLines && numberLineBreaks > 0 || !options.keepLines && firstToken !== 3) {\n        replaceContent = newLinesAndIndent();\n      } else if (options.keepLines) {\n        replaceContent = cachedSpaces[1];\n      }\n    } else {\n      switch (firstToken) {\n        case 3:\n        case 1:\n          indentLevel++;\n          if (options.keepLines && numberLineBreaks > 0 || !options.keepLines) {\n            replaceContent = newLinesAndIndent();\n          } else {\n            replaceContent = cachedSpaces[1];\n          }\n          break;\n        case 5:\n          if (options.keepLines && numberLineBreaks > 0 || !options.keepLines) {\n            replaceContent = newLinesAndIndent();\n          } else {\n            replaceContent = cachedSpaces[1];\n          }\n          break;\n        case 12:\n          replaceContent = newLinesAndIndent();\n          break;\n        case 13:\n          if (numberLineBreaks > 0) {\n            replaceContent = newLinesAndIndent();\n          } else if (!needsLineBreak) {\n            replaceContent = cachedSpaces[1];\n          }\n          break;\n        case 6:\n          if (options.keepLines && numberLineBreaks > 0) {\n            replaceContent = newLinesAndIndent();\n          } else if (!needsLineBreak) {\n            replaceContent = cachedSpaces[1];\n          }\n          break;\n        case 10:\n          if (options.keepLines && numberLineBreaks > 0) {\n            replaceContent = newLinesAndIndent();\n          } else if (secondToken === 6 && !needsLineBreak) {\n            replaceContent = \"\";\n          }\n          break;\n        case 7:\n        case 8:\n        case 9:\n        case 11:\n        case 2:\n        case 4:\n          if (options.keepLines && numberLineBreaks > 0) {\n            replaceContent = newLinesAndIndent();\n          } else {\n            if ((secondToken === 12 || secondToken === 13) && !needsLineBreak) {\n              replaceContent = cachedSpaces[1];\n            } else if (secondToken !== 5 && secondToken !== 17) {\n              hasError = true;\n            }\n          }\n          break;\n        case 16:\n          hasError = true;\n          break;\n      }\n      if (numberLineBreaks > 0 && (secondToken === 12 || secondToken === 13)) {\n        replaceContent = newLinesAndIndent();\n      }\n    }\n    if (secondToken === 17) {\n      if (options.keepLines && numberLineBreaks > 0) {\n        replaceContent = newLinesAndIndent();\n      } else {\n        replaceContent = options.insertFinalNewline ? eol : \"\";\n      }\n    }\n    var secondTokenStart = scanner.getTokenOffset() + formatTextStart;\n    addEdit(replaceContent, firstTokenEnd, secondTokenStart);\n    firstToken = secondToken;\n  }\n  return editOperations;\n}\nfunction repeat(s, count) {\n  var result = \"\";\n  for (var i = 0; i < count; i++) {\n    result += s;\n  }\n  return result;\n}\nfunction computeIndentLevel(content, options) {\n  var i = 0;\n  var nChars = 0;\n  var tabSize = options.tabSize || 4;\n  while (i < content.length) {\n    var ch = content.charAt(i);\n    if (ch === cachedSpaces[1]) {\n      nChars++;\n    } else if (ch === \"\t\") {\n      nChars += tabSize;\n    } else {\n      break;\n    }\n    i++;\n  }\n  return Math.floor(nChars / tabSize);\n}\nfunction getEOL(options, text) {\n  for (var i = 0; i < text.length; i++) {\n    var ch = text.charAt(i);\n    if (ch === \"\\r\") {\n      if (i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n        return \"\\r\\n\";\n      }\n      return \"\\r\";\n    } else if (ch === \"\\n\") {\n      return \"\\n\";\n    }\n  }\n  return options && options.eol || \"\\n\";\n}\nfunction isEOL(text, offset) {\n  return \"\\r\\n\".indexOf(text.charAt(offset)) !== -1;\n}\nvar ParseOptions;\n(function (ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\nfunction getLocation(text, position) {\n  var segments = [];\n  var earlyReturnException = new Object();\n  var previousNode = void 0;\n  var previousNodeInst = {\n    value: {},\n    offset: 0,\n    length: 0,\n    type: \"object\",\n    parent: void 0\n  };\n  var isAtPropertyKey = false;\n  function setPreviousNode(value, offset, length, type) {\n    previousNodeInst.value = value;\n    previousNodeInst.offset = offset;\n    previousNodeInst.length = length;\n    previousNodeInst.type = type;\n    previousNodeInst.colonOffset = void 0;\n    previousNode = previousNodeInst;\n  }\n  try {\n    visit(text, {\n      onObjectBegin: function onObjectBegin(offset, length) {\n        if (position <= offset) {\n          throw earlyReturnException;\n        }\n        previousNode = void 0;\n        isAtPropertyKey = position > offset;\n        segments.push(\"\");\n      },\n      onObjectProperty: function onObjectProperty(name, offset, length) {\n        if (position < offset) {\n          throw earlyReturnException;\n        }\n        setPreviousNode(name, offset, length, \"property\");\n        segments[segments.length - 1] = name;\n        if (position <= offset + length) {\n          throw earlyReturnException;\n        }\n      },\n      onObjectEnd: function onObjectEnd(offset, length) {\n        if (position <= offset) {\n          throw earlyReturnException;\n        }\n        previousNode = void 0;\n        segments.pop();\n      },\n      onArrayBegin: function onArrayBegin(offset, length) {\n        if (position <= offset) {\n          throw earlyReturnException;\n        }\n        previousNode = void 0;\n        segments.push(0);\n      },\n      onArrayEnd: function onArrayEnd(offset, length) {\n        if (position <= offset) {\n          throw earlyReturnException;\n        }\n        previousNode = void 0;\n        segments.pop();\n      },\n      onLiteralValue: function onLiteralValue(value, offset, length) {\n        if (position < offset) {\n          throw earlyReturnException;\n        }\n        setPreviousNode(value, offset, length, getNodeType(value));\n        if (position <= offset + length) {\n          throw earlyReturnException;\n        }\n      },\n      onSeparator: function onSeparator(sep, offset, length) {\n        if (position <= offset) {\n          throw earlyReturnException;\n        }\n        if (sep === \":\" && previousNode && previousNode.type === \"property\") {\n          previousNode.colonOffset = offset;\n          isAtPropertyKey = false;\n          previousNode = void 0;\n        } else if (sep === \",\") {\n          var last = segments[segments.length - 1];\n          if (typeof last === \"number\") {\n            segments[segments.length - 1] = last + 1;\n          } else {\n            isAtPropertyKey = true;\n            segments[segments.length - 1] = \"\";\n          }\n          previousNode = void 0;\n        }\n      }\n    });\n  } catch (e) {\n    if (e !== earlyReturnException) {\n      throw e;\n    }\n  }\n  return {\n    path: segments,\n    previousNode: previousNode,\n    isAtPropertyKey: isAtPropertyKey,\n    matches: function matches(pattern) {\n      var k = 0;\n      for (var i = 0; k < pattern.length && i < segments.length; i++) {\n        if (pattern[k] === segments[i] || pattern[k] === \"*\") {\n          k++;\n        } else if (pattern[k] !== \"**\") {\n          return false;\n        }\n      }\n      return k === pattern.length;\n    }\n  };\n}\nfunction parse(text) {\n  var errors = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ParseOptions.DEFAULT;\n  var currentProperty = null;\n  var currentParent = [];\n  var previousParents = [];\n  function onValue(value) {\n    if (Array.isArray(currentParent)) {\n      currentParent.push(value);\n    } else if (currentProperty !== null) {\n      currentParent[currentProperty] = value;\n    }\n  }\n  var visitor = {\n    onObjectBegin: function onObjectBegin() {\n      var object = {};\n      onValue(object);\n      previousParents.push(currentParent);\n      currentParent = object;\n      currentProperty = null;\n    },\n    onObjectProperty: function onObjectProperty(name) {\n      currentProperty = name;\n    },\n    onObjectEnd: function onObjectEnd() {\n      currentParent = previousParents.pop();\n    },\n    onArrayBegin: function onArrayBegin() {\n      var array = [];\n      onValue(array);\n      previousParents.push(currentParent);\n      currentParent = array;\n      currentProperty = null;\n    },\n    onArrayEnd: function onArrayEnd() {\n      currentParent = previousParents.pop();\n    },\n    onLiteralValue: onValue,\n    onError: function onError(error, offset, length) {\n      errors.push({\n        error: error,\n        offset: offset,\n        length: length\n      });\n    }\n  };\n  visit(text, visitor, options);\n  return currentParent[0];\n}\nfunction parseTree(text) {\n  var errors = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ParseOptions.DEFAULT;\n  var currentParent = {\n    type: \"array\",\n    offset: -1,\n    length: -1,\n    children: [],\n    parent: void 0\n  };\n  function ensurePropertyComplete(endOffset) {\n    if (currentParent.type === \"property\") {\n      currentParent.length = endOffset - currentParent.offset;\n      currentParent = currentParent.parent;\n    }\n  }\n  function onValue(valueNode) {\n    currentParent.children.push(valueNode);\n    return valueNode;\n  }\n  var visitor = {\n    onObjectBegin: function onObjectBegin(offset) {\n      currentParent = onValue({\n        type: \"object\",\n        offset: offset,\n        length: -1,\n        parent: currentParent,\n        children: []\n      });\n    },\n    onObjectProperty: function onObjectProperty(name, offset, length) {\n      currentParent = onValue({\n        type: \"property\",\n        offset: offset,\n        length: -1,\n        parent: currentParent,\n        children: []\n      });\n      currentParent.children.push({\n        type: \"string\",\n        value: name,\n        offset: offset,\n        length: length,\n        parent: currentParent\n      });\n    },\n    onObjectEnd: function onObjectEnd(offset, length) {\n      ensurePropertyComplete(offset + length);\n      currentParent.length = offset + length - currentParent.offset;\n      currentParent = currentParent.parent;\n      ensurePropertyComplete(offset + length);\n    },\n    onArrayBegin: function onArrayBegin(offset, length) {\n      currentParent = onValue({\n        type: \"array\",\n        offset: offset,\n        length: -1,\n        parent: currentParent,\n        children: []\n      });\n    },\n    onArrayEnd: function onArrayEnd(offset, length) {\n      currentParent.length = offset + length - currentParent.offset;\n      currentParent = currentParent.parent;\n      ensurePropertyComplete(offset + length);\n    },\n    onLiteralValue: function onLiteralValue(value, offset, length) {\n      onValue({\n        type: getNodeType(value),\n        offset: offset,\n        length: length,\n        parent: currentParent,\n        value: value\n      });\n      ensurePropertyComplete(offset + length);\n    },\n    onSeparator: function onSeparator(sep, offset, length) {\n      if (currentParent.type === \"property\") {\n        if (sep === \":\") {\n          currentParent.colonOffset = offset;\n        } else if (sep === \",\") {\n          ensurePropertyComplete(offset);\n        }\n      }\n    },\n    onError: function onError(error, offset, length) {\n      errors.push({\n        error: error,\n        offset: offset,\n        length: length\n      });\n    }\n  };\n  visit(text, visitor, options);\n  var result = currentParent.children[0];\n  if (result) {\n    delete result.parent;\n  }\n  return result;\n}\nfunction findNodeAtLocation(root, path) {\n  if (!root) {\n    return void 0;\n  }\n  var node = root;\n  var _iterator = _createForOfIteratorHelper(path),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var segment = _step.value;\n      if (typeof segment === \"string\") {\n        if (node.type !== \"object\" || !Array.isArray(node.children)) {\n          return void 0;\n        }\n        var found = false;\n        var _iterator2 = _createForOfIteratorHelper(node.children),\n          _step2;\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var propertyNode = _step2.value;\n            if (Array.isArray(propertyNode.children) && propertyNode.children[0].value === segment && propertyNode.children.length === 2) {\n              node = propertyNode.children[1];\n              found = true;\n              break;\n            }\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n        if (!found) {\n          return void 0;\n        }\n      } else {\n        var index = segment;\n        if (node.type !== \"array\" || index < 0 || !Array.isArray(node.children) || index >= node.children.length) {\n          return void 0;\n        }\n        node = node.children[index];\n      }\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  return node;\n}\nfunction getNodePath(node) {\n  if (!node.parent || !node.parent.children) {\n    return [];\n  }\n  var path = getNodePath(node.parent);\n  if (node.parent.type === \"property\") {\n    var key = node.parent.children[0].value;\n    path.push(key);\n  } else if (node.parent.type === \"array\") {\n    var index = node.parent.children.indexOf(node);\n    if (index !== -1) {\n      path.push(index);\n    }\n  }\n  return path;\n}\nfunction getNodeValue(node) {\n  switch (node.type) {\n    case \"array\":\n      return node.children.map(getNodeValue);\n    case \"object\":\n      var obj = Object.create(null);\n      var _iterator3 = _createForOfIteratorHelper(node.children),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var prop = _step3.value;\n          var valueNode = prop.children[1];\n          if (valueNode) {\n            obj[prop.children[0].value] = getNodeValue(valueNode);\n          }\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      return obj;\n    case \"null\":\n    case \"string\":\n    case \"number\":\n    case \"boolean\":\n      return node.value;\n    default:\n      return void 0;\n  }\n}\nfunction contains(node, offset) {\n  var includeRightBound = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  return offset >= node.offset && offset < node.offset + node.length || includeRightBound && offset === node.offset + node.length;\n}\nfunction findNodeAtOffset(node, offset) {\n  var includeRightBound = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (contains(node, offset, includeRightBound)) {\n    var children = node.children;\n    if (Array.isArray(children)) {\n      for (var i = 0; i < children.length && children[i].offset <= offset; i++) {\n        var item = findNodeAtOffset(children[i], offset, includeRightBound);\n        if (item) {\n          return item;\n        }\n      }\n    }\n    return node;\n  }\n  return void 0;\n}\nfunction visit(text, visitor) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ParseOptions.DEFAULT;\n  var _scanner = createScanner(text, false);\n  var _jsonPath = [];\n  var suppressedCallbacks = 0;\n  function toNoArgVisit(visitFunction) {\n    return visitFunction ? function () {\n      return suppressedCallbacks === 0 && visitFunction(_scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter());\n    } : function () {\n      return true;\n    };\n  }\n  function toOneArgVisit(visitFunction) {\n    return visitFunction ? function (arg) {\n      return suppressedCallbacks === 0 && visitFunction(arg, _scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter());\n    } : function () {\n      return true;\n    };\n  }\n  function toOneArgVisitWithPath(visitFunction) {\n    return visitFunction ? function (arg) {\n      return suppressedCallbacks === 0 && visitFunction(arg, _scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter(), function () {\n        return _jsonPath.slice();\n      });\n    } : function () {\n      return true;\n    };\n  }\n  function toBeginVisit(visitFunction) {\n    return visitFunction ? function () {\n      if (suppressedCallbacks > 0) {\n        suppressedCallbacks++;\n      } else {\n        var cbReturn = visitFunction(_scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter(), function () {\n          return _jsonPath.slice();\n        });\n        if (cbReturn === false) {\n          suppressedCallbacks = 1;\n        }\n      }\n    } : function () {\n      return true;\n    };\n  }\n  function toEndVisit(visitFunction) {\n    return visitFunction ? function () {\n      if (suppressedCallbacks > 0) {\n        suppressedCallbacks--;\n      }\n      if (suppressedCallbacks === 0) {\n        visitFunction(_scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter());\n      }\n    } : function () {\n      return true;\n    };\n  }\n  var onObjectBegin = toBeginVisit(visitor.onObjectBegin),\n    onObjectProperty = toOneArgVisitWithPath(visitor.onObjectProperty),\n    onObjectEnd = toEndVisit(visitor.onObjectEnd),\n    onArrayBegin = toBeginVisit(visitor.onArrayBegin),\n    onArrayEnd = toEndVisit(visitor.onArrayEnd),\n    onLiteralValue = toOneArgVisitWithPath(visitor.onLiteralValue),\n    onSeparator = toOneArgVisit(visitor.onSeparator),\n    onComment = toNoArgVisit(visitor.onComment),\n    onError = toOneArgVisit(visitor.onError);\n  var disallowComments = options && options.disallowComments;\n  var allowTrailingComma = options && options.allowTrailingComma;\n  function scanNext() {\n    while (true) {\n      var token = _scanner.scan();\n      switch (_scanner.getTokenError()) {\n        case 4:\n          handleError(14);\n          break;\n        case 5:\n          handleError(15);\n          break;\n        case 3:\n          handleError(13);\n          break;\n        case 1:\n          if (!disallowComments) {\n            handleError(11);\n          }\n          break;\n        case 2:\n          handleError(12);\n          break;\n        case 6:\n          handleError(16);\n          break;\n      }\n      switch (token) {\n        case 12:\n        case 13:\n          if (disallowComments) {\n            handleError(10);\n          } else {\n            onComment();\n          }\n          break;\n        case 16:\n          handleError(1);\n          break;\n        case 15:\n        case 14:\n          break;\n        default:\n          return token;\n      }\n    }\n  }\n  function handleError(error) {\n    var skipUntilAfter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    var skipUntil = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    onError(error);\n    if (skipUntilAfter.length + skipUntil.length > 0) {\n      var token = _scanner.getToken();\n      while (token !== 17) {\n        if (skipUntilAfter.indexOf(token) !== -1) {\n          scanNext();\n          break;\n        } else if (skipUntil.indexOf(token) !== -1) {\n          break;\n        }\n        token = scanNext();\n      }\n    }\n  }\n  function parseString(isValue) {\n    var value = _scanner.getTokenValue();\n    if (isValue) {\n      onLiteralValue(value);\n    } else {\n      onObjectProperty(value);\n      _jsonPath.push(value);\n    }\n    scanNext();\n    return true;\n  }\n  function parseLiteral() {\n    switch (_scanner.getToken()) {\n      case 11:\n        var tokenValue = _scanner.getTokenValue();\n        var value = Number(tokenValue);\n        if (isNaN(value)) {\n          handleError(2);\n          value = 0;\n        }\n        onLiteralValue(value);\n        break;\n      case 7:\n        onLiteralValue(null);\n        break;\n      case 8:\n        onLiteralValue(true);\n        break;\n      case 9:\n        onLiteralValue(false);\n        break;\n      default:\n        return false;\n    }\n    scanNext();\n    return true;\n  }\n  function parseProperty() {\n    if (_scanner.getToken() !== 10) {\n      handleError(3, [], [2, 5]);\n      return false;\n    }\n    parseString(false);\n    if (_scanner.getToken() === 6) {\n      onSeparator(\":\");\n      scanNext();\n      if (!parseValue()) {\n        handleError(4, [], [2, 5]);\n      }\n    } else {\n      handleError(5, [], [2, 5]);\n    }\n    _jsonPath.pop();\n    return true;\n  }\n  function parseObject() {\n    onObjectBegin();\n    scanNext();\n    var needsComma = false;\n    while (_scanner.getToken() !== 2 && _scanner.getToken() !== 17) {\n      if (_scanner.getToken() === 5) {\n        if (!needsComma) {\n          handleError(4, [], []);\n        }\n        onSeparator(\",\");\n        scanNext();\n        if (_scanner.getToken() === 2 && allowTrailingComma) {\n          break;\n        }\n      } else if (needsComma) {\n        handleError(6, [], []);\n      }\n      if (!parseProperty()) {\n        handleError(4, [], [2, 5]);\n      }\n      needsComma = true;\n    }\n    onObjectEnd();\n    if (_scanner.getToken() !== 2) {\n      handleError(7, [2], []);\n    } else {\n      scanNext();\n    }\n    return true;\n  }\n  function parseArray() {\n    onArrayBegin();\n    scanNext();\n    var isFirstElement = true;\n    var needsComma = false;\n    while (_scanner.getToken() !== 4 && _scanner.getToken() !== 17) {\n      if (_scanner.getToken() === 5) {\n        if (!needsComma) {\n          handleError(4, [], []);\n        }\n        onSeparator(\",\");\n        scanNext();\n        if (_scanner.getToken() === 4 && allowTrailingComma) {\n          break;\n        }\n      } else if (needsComma) {\n        handleError(6, [], []);\n      }\n      if (isFirstElement) {\n        _jsonPath.push(0);\n        isFirstElement = false;\n      } else {\n        _jsonPath[_jsonPath.length - 1]++;\n      }\n      if (!parseValue()) {\n        handleError(4, [], [4, 5]);\n      }\n      needsComma = true;\n    }\n    onArrayEnd();\n    if (!isFirstElement) {\n      _jsonPath.pop();\n    }\n    if (_scanner.getToken() !== 4) {\n      handleError(8, [4], []);\n    } else {\n      scanNext();\n    }\n    return true;\n  }\n  function parseValue() {\n    switch (_scanner.getToken()) {\n      case 3:\n        return parseArray();\n      case 1:\n        return parseObject();\n      case 10:\n        return parseString(true);\n      default:\n        return parseLiteral();\n    }\n  }\n  scanNext();\n  if (_scanner.getToken() === 17) {\n    if (options.allowEmptyContent) {\n      return true;\n    }\n    handleError(4, [], []);\n    return false;\n  }\n  if (!parseValue()) {\n    handleError(4, [], []);\n    return false;\n  }\n  if (_scanner.getToken() !== 17) {\n    handleError(9, [], []);\n  }\n  return true;\n}\nfunction stripComments(text, replaceCh) {\n  var _scanner = createScanner(text),\n    parts = [],\n    kind,\n    offset = 0,\n    pos;\n  do {\n    pos = _scanner.getPosition();\n    kind = _scanner.scan();\n    switch (kind) {\n      case 12:\n      case 13:\n      case 17:\n        if (offset !== pos) {\n          parts.push(text.substring(offset, pos));\n        }\n        if (replaceCh !== void 0) {\n          parts.push(_scanner.getTokenValue().replace(/[^\\r\\n]/g, replaceCh));\n        }\n        offset = _scanner.getPosition();\n        break;\n    }\n  } while (kind !== 17);\n  return parts.join(\"\");\n}\nfunction getNodeType(value) {\n  switch (_typeof(value)) {\n    case \"boolean\":\n      return \"boolean\";\n    case \"number\":\n      return \"number\";\n    case \"string\":\n      return \"string\";\n    case \"object\":\n      {\n        if (!value) {\n          return \"null\";\n        } else if (Array.isArray(value)) {\n          return \"array\";\n        }\n        return \"object\";\n      }\n    default:\n      return \"null\";\n  }\n}\nfunction setProperty(text, originalPath, value, options) {\n  var path = originalPath.slice();\n  var errors = [];\n  var root = parseTree(text, errors);\n  var parent = void 0;\n  var lastSegment = void 0;\n  while (path.length > 0) {\n    lastSegment = path.pop();\n    parent = findNodeAtLocation(root, path);\n    if (parent === void 0 && value !== void 0) {\n      if (typeof lastSegment === \"string\") {\n        value = _defineProperty({}, lastSegment, value);\n      } else {\n        value = [value];\n      }\n    } else {\n      break;\n    }\n  }\n  if (!parent) {\n    if (value === void 0) {\n      throw new Error(\"Can not delete in empty document\");\n    }\n    return withFormatting(text, {\n      offset: root ? root.offset : 0,\n      length: root ? root.length : 0,\n      content: JSON.stringify(value)\n    }, options);\n  } else if (parent.type === \"object\" && typeof lastSegment === \"string\" && Array.isArray(parent.children)) {\n    var existing = findNodeAtLocation(parent, [lastSegment]);\n    if (existing !== void 0) {\n      if (value === void 0) {\n        if (!existing.parent) {\n          throw new Error(\"Malformed AST\");\n        }\n        var propertyIndex = parent.children.indexOf(existing.parent);\n        var removeBegin;\n        var removeEnd = existing.parent.offset + existing.parent.length;\n        if (propertyIndex > 0) {\n          var previous = parent.children[propertyIndex - 1];\n          removeBegin = previous.offset + previous.length;\n        } else {\n          removeBegin = parent.offset + 1;\n          if (parent.children.length > 1) {\n            var next = parent.children[1];\n            removeEnd = next.offset;\n          }\n        }\n        return withFormatting(text, {\n          offset: removeBegin,\n          length: removeEnd - removeBegin,\n          content: \"\"\n        }, options);\n      } else {\n        return withFormatting(text, {\n          offset: existing.offset,\n          length: existing.length,\n          content: JSON.stringify(value)\n        }, options);\n      }\n    } else {\n      if (value === void 0) {\n        return [];\n      }\n      var newProperty = \"\".concat(JSON.stringify(lastSegment), \": \").concat(JSON.stringify(value));\n      var index = options.getInsertionIndex ? options.getInsertionIndex(parent.children.map(function (p) {\n        return p.children[0].value;\n      })) : parent.children.length;\n      var edit;\n      if (index > 0) {\n        var _previous = parent.children[index - 1];\n        edit = {\n          offset: _previous.offset + _previous.length,\n          length: 0,\n          content: \",\" + newProperty\n        };\n      } else if (parent.children.length === 0) {\n        edit = {\n          offset: parent.offset + 1,\n          length: 0,\n          content: newProperty\n        };\n      } else {\n        edit = {\n          offset: parent.offset + 1,\n          length: 0,\n          content: newProperty + \",\"\n        };\n      }\n      return withFormatting(text, edit, options);\n    }\n  } else if (parent.type === \"array\" && typeof lastSegment === \"number\" && Array.isArray(parent.children)) {\n    var insertIndex = lastSegment;\n    if (insertIndex === -1) {\n      var _newProperty = \"\".concat(JSON.stringify(value));\n      var _edit;\n      if (parent.children.length === 0) {\n        _edit = {\n          offset: parent.offset + 1,\n          length: 0,\n          content: _newProperty\n        };\n      } else {\n        var _previous2 = parent.children[parent.children.length - 1];\n        _edit = {\n          offset: _previous2.offset + _previous2.length,\n          length: 0,\n          content: \",\" + _newProperty\n        };\n      }\n      return withFormatting(text, _edit, options);\n    } else if (value === void 0 && parent.children.length >= 0) {\n      var removalIndex = lastSegment;\n      var toRemove = parent.children[removalIndex];\n      var _edit2;\n      if (parent.children.length === 1) {\n        _edit2 = {\n          offset: parent.offset + 1,\n          length: parent.length - 2,\n          content: \"\"\n        };\n      } else if (parent.children.length - 1 === removalIndex) {\n        var _previous3 = parent.children[removalIndex - 1];\n        var offset = _previous3.offset + _previous3.length;\n        var parentEndOffset = parent.offset + parent.length;\n        _edit2 = {\n          offset: offset,\n          length: parentEndOffset - 2 - offset,\n          content: \"\"\n        };\n      } else {\n        _edit2 = {\n          offset: toRemove.offset,\n          length: parent.children[removalIndex + 1].offset - toRemove.offset,\n          content: \"\"\n        };\n      }\n      return withFormatting(text, _edit2, options);\n    } else if (value !== void 0) {\n      var _edit3;\n      var _newProperty2 = \"\".concat(JSON.stringify(value));\n      if (!options.isArrayInsertion && parent.children.length > lastSegment) {\n        var toModify = parent.children[lastSegment];\n        _edit3 = {\n          offset: toModify.offset,\n          length: toModify.length,\n          content: _newProperty2\n        };\n      } else if (parent.children.length === 0 || lastSegment === 0) {\n        _edit3 = {\n          offset: parent.offset + 1,\n          length: 0,\n          content: parent.children.length === 0 ? _newProperty2 : _newProperty2 + \",\"\n        };\n      } else {\n        var _index = lastSegment > parent.children.length ? parent.children.length : lastSegment;\n        var _previous4 = parent.children[_index - 1];\n        _edit3 = {\n          offset: _previous4.offset + _previous4.length,\n          length: 0,\n          content: \",\" + _newProperty2\n        };\n      }\n      return withFormatting(text, _edit3, options);\n    } else {\n      throw new Error(\"Can not \".concat(value === void 0 ? \"remove\" : options.isArrayInsertion ? \"insert\" : \"modify\", \" Array index \").concat(insertIndex, \" as length is not sufficient\"));\n    }\n  } else {\n    throw new Error(\"Can not add \".concat(typeof lastSegment !== \"number\" ? \"index\" : \"property\", \" to parent of type \").concat(parent.type));\n  }\n}\nfunction withFormatting(text, edit, options) {\n  if (!options.formattingOptions) {\n    return [edit];\n  }\n  var newText = applyEdit(text, edit);\n  var begin = edit.offset;\n  var end = edit.offset + edit.content.length;\n  if (edit.length === 0 || edit.content.length === 0) {\n    while (begin > 0 && !isEOL(newText, begin - 1)) {\n      begin--;\n    }\n    while (end < newText.length && !isEOL(newText, end)) {\n      end++;\n    }\n  }\n  var edits = format(newText, {\n    offset: begin,\n    length: end - begin\n  }, _objectSpread(_objectSpread({}, options.formattingOptions), {}, {\n    keepLines: false\n  }));\n  for (var i = edits.length - 1; i >= 0; i--) {\n    var edit2 = edits[i];\n    newText = applyEdit(newText, edit2);\n    begin = Math.min(begin, edit2.offset);\n    end = Math.max(end, edit2.offset + edit2.length);\n    end += edit2.content.length - edit2.length;\n  }\n  var editLength = text.length - (newText.length - end) - begin;\n  return [{\n    offset: begin,\n    length: editLength,\n    content: newText.substring(begin, end)\n  }];\n}\nfunction applyEdit(text, edit) {\n  return text.substring(0, edit.offset) + edit.content + text.substring(edit.offset + edit.length);\n}\nvar createScanner2 = createScanner;\nvar ScanError;\n(function (ScanError2) {\n  ScanError2[ScanError2[\"None\"] = 0] = \"None\";\n  ScanError2[ScanError2[\"UnexpectedEndOfComment\"] = 1] = \"UnexpectedEndOfComment\";\n  ScanError2[ScanError2[\"UnexpectedEndOfString\"] = 2] = \"UnexpectedEndOfString\";\n  ScanError2[ScanError2[\"UnexpectedEndOfNumber\"] = 3] = \"UnexpectedEndOfNumber\";\n  ScanError2[ScanError2[\"InvalidUnicode\"] = 4] = \"InvalidUnicode\";\n  ScanError2[ScanError2[\"InvalidEscapeCharacter\"] = 5] = \"InvalidEscapeCharacter\";\n  ScanError2[ScanError2[\"InvalidCharacter\"] = 6] = \"InvalidCharacter\";\n})(ScanError || (ScanError = {}));\nvar SyntaxKind;\n(function (SyntaxKind2) {\n  SyntaxKind2[SyntaxKind2[\"OpenBraceToken\"] = 1] = \"OpenBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBraceToken\"] = 2] = \"CloseBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"OpenBracketToken\"] = 3] = \"OpenBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBracketToken\"] = 4] = \"CloseBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CommaToken\"] = 5] = \"CommaToken\";\n  SyntaxKind2[SyntaxKind2[\"ColonToken\"] = 6] = \"ColonToken\";\n  SyntaxKind2[SyntaxKind2[\"NullKeyword\"] = 7] = \"NullKeyword\";\n  SyntaxKind2[SyntaxKind2[\"TrueKeyword\"] = 8] = \"TrueKeyword\";\n  SyntaxKind2[SyntaxKind2[\"FalseKeyword\"] = 9] = \"FalseKeyword\";\n  SyntaxKind2[SyntaxKind2[\"StringLiteral\"] = 10] = \"StringLiteral\";\n  SyntaxKind2[SyntaxKind2[\"NumericLiteral\"] = 11] = \"NumericLiteral\";\n  SyntaxKind2[SyntaxKind2[\"LineCommentTrivia\"] = 12] = \"LineCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"BlockCommentTrivia\"] = 13] = \"BlockCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"LineBreakTrivia\"] = 14] = \"LineBreakTrivia\";\n  SyntaxKind2[SyntaxKind2[\"Trivia\"] = 15] = \"Trivia\";\n  SyntaxKind2[SyntaxKind2[\"Unknown\"] = 16] = \"Unknown\";\n  SyntaxKind2[SyntaxKind2[\"EOF\"] = 17] = \"EOF\";\n})(SyntaxKind || (SyntaxKind = {}));\nvar getLocation2 = getLocation;\nvar parse2 = parse;\nvar parseTree2 = parseTree;\nvar findNodeAtLocation2 = findNodeAtLocation;\nvar findNodeAtOffset2 = findNodeAtOffset;\nvar getNodePath2 = getNodePath;\nvar getNodeValue2 = getNodeValue;\nvar visit2 = visit;\nvar stripComments2 = stripComments;\nvar ParseErrorCode;\n(function (ParseErrorCode2) {\n  ParseErrorCode2[ParseErrorCode2[\"InvalidSymbol\"] = 1] = \"InvalidSymbol\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidNumberFormat\"] = 2] = \"InvalidNumberFormat\";\n  ParseErrorCode2[ParseErrorCode2[\"PropertyNameExpected\"] = 3] = \"PropertyNameExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ValueExpected\"] = 4] = \"ValueExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ColonExpected\"] = 5] = \"ColonExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CommaExpected\"] = 6] = \"CommaExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBraceExpected\"] = 7] = \"CloseBraceExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBracketExpected\"] = 8] = \"CloseBracketExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"EndOfFileExpected\"] = 9] = \"EndOfFileExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCommentToken\"] = 10] = \"InvalidCommentToken\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfComment\"] = 11] = \"UnexpectedEndOfComment\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfString\"] = 12] = \"UnexpectedEndOfString\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfNumber\"] = 13] = \"UnexpectedEndOfNumber\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidUnicode\"] = 14] = \"InvalidUnicode\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidEscapeCharacter\"] = 15] = \"InvalidEscapeCharacter\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCharacter\"] = 16] = \"InvalidCharacter\";\n})(ParseErrorCode || (ParseErrorCode = {}));\nfunction printParseErrorCode(code) {\n  switch (code) {\n    case 1:\n      return \"InvalidSymbol\";\n    case 2:\n      return \"InvalidNumberFormat\";\n    case 3:\n      return \"PropertyNameExpected\";\n    case 4:\n      return \"ValueExpected\";\n    case 5:\n      return \"ColonExpected\";\n    case 6:\n      return \"CommaExpected\";\n    case 7:\n      return \"CloseBraceExpected\";\n    case 8:\n      return \"CloseBracketExpected\";\n    case 9:\n      return \"EndOfFileExpected\";\n    case 10:\n      return \"InvalidCommentToken\";\n    case 11:\n      return \"UnexpectedEndOfComment\";\n    case 12:\n      return \"UnexpectedEndOfString\";\n    case 13:\n      return \"UnexpectedEndOfNumber\";\n    case 14:\n      return \"InvalidUnicode\";\n    case 15:\n      return \"InvalidEscapeCharacter\";\n    case 16:\n      return \"InvalidCharacter\";\n  }\n  return \"<unknown ParseErrorCode>\";\n}\nfunction format2(documentText, range, options) {\n  return format(documentText, range, options);\n}\nfunction modify(text, path, value, options) {\n  return setProperty(text, path, value, options);\n}\nfunction applyEdits(text, edits) {\n  var sortedEdits = edits.slice(0).sort(function (a, b) {\n    var diff = a.offset - b.offset;\n    if (diff === 0) {\n      return a.length - b.length;\n    }\n    return diff;\n  });\n  var lastModifiedOffset = text.length;\n  for (var i = sortedEdits.length - 1; i >= 0; i--) {\n    var e = sortedEdits[i];\n    if (e.offset + e.length <= lastModifiedOffset) {\n      text = applyEdit(text, e);\n    } else {\n      throw new Error(\"Overlapping edit\");\n    }\n    lastModifiedOffset = e.offset;\n  }\n  return text;\n}\nexport { ParseErrorCode, ScanError, SyntaxKind, applyEdits, createScanner2 as createScanner, findNodeAtLocation2 as findNodeAtLocation, findNodeAtOffset2 as findNodeAtOffset, format2 as format, getLocation2 as getLocation, getNodePath2 as getNodePath, getNodeValue2 as getNodeValue, modify, parse2 as parse, parseTree2 as parseTree, printParseErrorCode, stripComments2 as stripComments, visit2 as visit };", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */\nfunction isNothing(subject) {\n  return typeof subject === 'undefined' || subject === null;\n}\nfunction isObject(subject) {\n  return _typeof(subject) === 'object' && subject !== null;\n}\nfunction toArray(sequence) {\n  if (Array.isArray(sequence)) return sequence;else if (isNothing(sequence)) return [];\n  return [sequence];\n}\nfunction extend(target, source) {\n  var index, length, key, sourceKeys;\n  if (source) {\n    sourceKeys = Object.keys(source);\n    for (index = 0, length = sourceKeys.length; index < length; index += 1) {\n      key = sourceKeys[index];\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction repeat(string, count) {\n  var result = '',\n    cycle;\n  for (cycle = 0; cycle < count; cycle += 1) {\n    result += string;\n  }\n  return result;\n}\nfunction isNegativeZero(number) {\n  return number === 0 && Number.NEGATIVE_INFINITY === 1 / number;\n}\nvar isNothing_1 = isNothing;\nvar isObject_1 = isObject;\nvar toArray_1 = toArray;\nvar repeat_1 = repeat;\nvar isNegativeZero_1 = isNegativeZero;\nvar extend_1 = extend;\nvar common = {\n  isNothing: isNothing_1,\n  isObject: isObject_1,\n  toArray: toArray_1,\n  repeat: repeat_1,\n  isNegativeZero: isNegativeZero_1,\n  extend: extend_1\n};\nfunction formatError(exception, compact) {\n  var where = '',\n    message = exception.reason || '(unknown reason)';\n  if (!exception.mark) return message;\n  if (exception.mark.name) {\n    where += 'in \"' + exception.mark.name + '\" ';\n  }\n  where += '(' + (exception.mark.line + 1) + ':' + (exception.mark.column + 1) + ')';\n  if (!compact && exception.mark.snippet) {\n    where += '\\n\\n' + exception.mark.snippet;\n  }\n  return message + ' ' + where;\n}\nfunction YAMLException$1(reason, mark) {\n  Error.call(this);\n  this.name = 'YAMLException';\n  this.reason = reason;\n  this.mark = mark;\n  this.message = formatError(this, false);\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = new Error().stack || '';\n  }\n}\nYAMLException$1.prototype = Object.create(Error.prototype);\nYAMLException$1.prototype.constructor = YAMLException$1;\nYAMLException$1.prototype.toString = function toString(compact) {\n  return this.name + ': ' + formatError(this, compact);\n};\nvar exception = YAMLException$1;\nfunction getLine(buffer, lineStart, lineEnd, position, maxLineLength) {\n  var head = '';\n  var tail = '';\n  var maxHalfLength = Math.floor(maxLineLength / 2) - 1;\n  if (position - lineStart > maxHalfLength) {\n    head = ' ... ';\n    lineStart = position - maxHalfLength + head.length;\n  }\n  if (lineEnd - position > maxHalfLength) {\n    tail = ' ...';\n    lineEnd = position + maxHalfLength - tail.length;\n  }\n  return {\n    str: head + buffer.slice(lineStart, lineEnd).replace(/\\t/g, '→') + tail,\n    pos: position - lineStart + head.length\n  };\n}\nfunction padStart(string, max) {\n  return common.repeat(' ', max - string.length) + string;\n}\nfunction makeSnippet(mark, options) {\n  options = Object.create(options || null);\n  if (!mark.buffer) return null;\n  if (!options.maxLength) options.maxLength = 79;\n  if (typeof options.indent !== 'number') options.indent = 1;\n  if (typeof options.linesBefore !== 'number') options.linesBefore = 3;\n  if (typeof options.linesAfter !== 'number') options.linesAfter = 2;\n  var re = /\\r?\\n|\\r|\\0/g;\n  var lineStarts = [0];\n  var lineEnds = [];\n  var match;\n  var foundLineNo = -1;\n  while (match = re.exec(mark.buffer)) {\n    lineEnds.push(match.index);\n    lineStarts.push(match.index + match[0].length);\n    if (mark.position <= match.index && foundLineNo < 0) {\n      foundLineNo = lineStarts.length - 2;\n    }\n  }\n  if (foundLineNo < 0) foundLineNo = lineStarts.length - 1;\n  var result = '',\n    i,\n    line;\n  var lineNoLength = Math.min(mark.line + options.linesAfter, lineEnds.length).toString().length;\n  var maxLineLength = options.maxLength - (options.indent + lineNoLength + 3);\n  for (i = 1; i <= options.linesBefore; i++) {\n    if (foundLineNo - i < 0) break;\n    line = getLine(mark.buffer, lineStarts[foundLineNo - i], lineEnds[foundLineNo - i], mark.position - (lineStarts[foundLineNo] - lineStarts[foundLineNo - i]), maxLineLength);\n    result = common.repeat(' ', options.indent) + padStart((mark.line - i + 1).toString(), lineNoLength) + ' | ' + line.str + '\\n' + result;\n  }\n  line = getLine(mark.buffer, lineStarts[foundLineNo], lineEnds[foundLineNo], mark.position, maxLineLength);\n  result += common.repeat(' ', options.indent) + padStart((mark.line + 1).toString(), lineNoLength) + ' | ' + line.str + '\\n';\n  result += common.repeat('-', options.indent + lineNoLength + 3 + line.pos) + '^' + '\\n';\n  for (i = 1; i <= options.linesAfter; i++) {\n    if (foundLineNo + i >= lineEnds.length) break;\n    line = getLine(mark.buffer, lineStarts[foundLineNo + i], lineEnds[foundLineNo + i], mark.position - (lineStarts[foundLineNo] - lineStarts[foundLineNo + i]), maxLineLength);\n    result += common.repeat(' ', options.indent) + padStart((mark.line + i + 1).toString(), lineNoLength) + ' | ' + line.str + '\\n';\n  }\n  return result.replace(/\\n$/, '');\n}\nvar snippet = makeSnippet;\nvar TYPE_CONSTRUCTOR_OPTIONS = ['kind', 'multi', 'resolve', 'construct', 'instanceOf', 'predicate', 'represent', 'representName', 'defaultStyle', 'styleAliases'];\nvar YAML_NODE_KINDS = ['scalar', 'sequence', 'mapping'];\nfunction compileStyleAliases(map) {\n  var result = {};\n  if (map !== null) {\n    Object.keys(map).forEach(function (style) {\n      map[style].forEach(function (alias) {\n        result[String(alias)] = style;\n      });\n    });\n  }\n  return result;\n}\nfunction Type$1(tag, options) {\n  options = options || {};\n  Object.keys(options).forEach(function (name) {\n    if (TYPE_CONSTRUCTOR_OPTIONS.indexOf(name) === -1) {\n      throw new exception('Unknown option \"' + name + '\" is met in definition of \"' + tag + '\" YAML type.');\n    }\n  });\n  this.options = options;\n  this.tag = tag;\n  this.kind = options['kind'] || null;\n  this.resolve = options['resolve'] || function () {\n    return true;\n  };\n  this.construct = options['construct'] || function (data) {\n    return data;\n  };\n  this.instanceOf = options['instanceOf'] || null;\n  this.predicate = options['predicate'] || null;\n  this.represent = options['represent'] || null;\n  this.representName = options['representName'] || null;\n  this.defaultStyle = options['defaultStyle'] || null;\n  this.multi = options['multi'] || false;\n  this.styleAliases = compileStyleAliases(options['styleAliases'] || null);\n  if (YAML_NODE_KINDS.indexOf(this.kind) === -1) {\n    throw new exception('Unknown kind \"' + this.kind + '\" is specified for \"' + tag + '\" YAML type.');\n  }\n}\nvar type = Type$1;\nfunction compileList(schema, name) {\n  var result = [];\n  schema[name].forEach(function (currentType) {\n    var newIndex = result.length;\n    result.forEach(function (previousType, previousIndex) {\n      if (previousType.tag === currentType.tag && previousType.kind === currentType.kind && previousType.multi === currentType.multi) {\n        newIndex = previousIndex;\n      }\n    });\n    result[newIndex] = currentType;\n  });\n  return result;\n}\nfunction compileMap() {\n  var result = {\n      scalar: {},\n      sequence: {},\n      mapping: {},\n      fallback: {},\n      multi: {\n        scalar: [],\n        sequence: [],\n        mapping: [],\n        fallback: []\n      }\n    },\n    index,\n    length;\n  function collectType(type) {\n    if (type.multi) {\n      result.multi[type.kind].push(type);\n      result.multi['fallback'].push(type);\n    } else {\n      result[type.kind][type.tag] = result['fallback'][type.tag] = type;\n    }\n  }\n  for (index = 0, length = arguments.length; index < length; index += 1) {\n    arguments[index].forEach(collectType);\n  }\n  return result;\n}\nfunction Schema$1(definition) {\n  return this.extend(definition);\n}\nSchema$1.prototype.extend = function extend(definition) {\n  var implicit = [];\n  var explicit = [];\n  if (definition instanceof type) {\n    explicit.push(definition);\n  } else if (Array.isArray(definition)) {\n    explicit = explicit.concat(definition);\n  } else if (definition && (Array.isArray(definition.implicit) || Array.isArray(definition.explicit))) {\n    if (definition.implicit) implicit = implicit.concat(definition.implicit);\n    if (definition.explicit) explicit = explicit.concat(definition.explicit);\n  } else {\n    throw new exception('Schema.extend argument should be a Type, [ Type ], ' + 'or a schema definition ({ implicit: [...], explicit: [...] })');\n  }\n  implicit.forEach(function (type$1) {\n    if (!(type$1 instanceof type)) {\n      throw new exception('Specified list of YAML types (or a single Type object) contains a non-Type object.');\n    }\n    if (type$1.loadKind && type$1.loadKind !== 'scalar') {\n      throw new exception('There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.');\n    }\n    if (type$1.multi) {\n      throw new exception('There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.');\n    }\n  });\n  explicit.forEach(function (type$1) {\n    if (!(type$1 instanceof type)) {\n      throw new exception('Specified list of YAML types (or a single Type object) contains a non-Type object.');\n    }\n  });\n  var result = Object.create(Schema$1.prototype);\n  result.implicit = (this.implicit || []).concat(implicit);\n  result.explicit = (this.explicit || []).concat(explicit);\n  result.compiledImplicit = compileList(result, 'implicit');\n  result.compiledExplicit = compileList(result, 'explicit');\n  result.compiledTypeMap = compileMap(result.compiledImplicit, result.compiledExplicit);\n  return result;\n};\nvar schema = Schema$1;\nvar str = new type('tag:yaml.org,2002:str', {\n  kind: 'scalar',\n  construct: function construct(data) {\n    return data !== null ? data : '';\n  }\n});\nvar seq = new type('tag:yaml.org,2002:seq', {\n  kind: 'sequence',\n  construct: function construct(data) {\n    return data !== null ? data : [];\n  }\n});\nvar map = new type('tag:yaml.org,2002:map', {\n  kind: 'mapping',\n  construct: function construct(data) {\n    return data !== null ? data : {};\n  }\n});\nvar failsafe = new schema({\n  explicit: [str, seq, map]\n});\nfunction resolveYamlNull(data) {\n  if (data === null) return true;\n  var max = data.length;\n  return max === 1 && data === '~' || max === 4 && (data === 'null' || data === 'Null' || data === 'NULL');\n}\nfunction constructYamlNull() {\n  return null;\n}\nfunction isNull(object) {\n  return object === null;\n}\nvar _null = new type('tag:yaml.org,2002:null', {\n  kind: 'scalar',\n  resolve: resolveYamlNull,\n  construct: constructYamlNull,\n  predicate: isNull,\n  represent: {\n    canonical: function canonical() {\n      return '~';\n    },\n    lowercase: function lowercase() {\n      return 'null';\n    },\n    uppercase: function uppercase() {\n      return 'NULL';\n    },\n    camelcase: function camelcase() {\n      return 'Null';\n    },\n    empty: function empty() {\n      return '';\n    }\n  },\n  defaultStyle: 'lowercase'\n});\nfunction resolveYamlBoolean(data) {\n  if (data === null) return false;\n  var max = data.length;\n  return max === 4 && (data === 'true' || data === 'True' || data === 'TRUE') || max === 5 && (data === 'false' || data === 'False' || data === 'FALSE');\n}\nfunction constructYamlBoolean(data) {\n  return data === 'true' || data === 'True' || data === 'TRUE';\n}\nfunction isBoolean(object) {\n  return Object.prototype.toString.call(object) === '[object Boolean]';\n}\nvar bool = new type('tag:yaml.org,2002:bool', {\n  kind: 'scalar',\n  resolve: resolveYamlBoolean,\n  construct: constructYamlBoolean,\n  predicate: isBoolean,\n  represent: {\n    lowercase: function lowercase(object) {\n      return object ? 'true' : 'false';\n    },\n    uppercase: function uppercase(object) {\n      return object ? 'TRUE' : 'FALSE';\n    },\n    camelcase: function camelcase(object) {\n      return object ? 'True' : 'False';\n    }\n  },\n  defaultStyle: 'lowercase'\n});\nfunction isHexCode(c) {\n  return 0x30 <= c && c <= 0x39 || 0x41 <= c && c <= 0x46 || 0x61 <= c && c <= 0x66;\n}\nfunction isOctCode(c) {\n  return 0x30 <= c && c <= 0x37;\n}\nfunction isDecCode(c) {\n  return 0x30 <= c && c <= 0x39;\n}\nfunction resolveYamlInteger(data) {\n  if (data === null) return false;\n  var max = data.length,\n    index = 0,\n    hasDigits = false,\n    ch;\n  if (!max) return false;\n  ch = data[index];\n  if (ch === '-' || ch === '+') {\n    ch = data[++index];\n  }\n  if (ch === '0') {\n    if (index + 1 === max) return true;\n    ch = data[++index];\n    if (ch === 'b') {\n      index++;\n      for (; index < max; index++) {\n        ch = data[index];\n        if (ch === '_') continue;\n        if (ch !== '0' && ch !== '1') return false;\n        hasDigits = true;\n      }\n      return hasDigits && ch !== '_';\n    }\n    if (ch === 'x') {\n      index++;\n      for (; index < max; index++) {\n        ch = data[index];\n        if (ch === '_') continue;\n        if (!isHexCode(data.charCodeAt(index))) return false;\n        hasDigits = true;\n      }\n      return hasDigits && ch !== '_';\n    }\n    if (ch === 'o') {\n      index++;\n      for (; index < max; index++) {\n        ch = data[index];\n        if (ch === '_') continue;\n        if (!isOctCode(data.charCodeAt(index))) return false;\n        hasDigits = true;\n      }\n      return hasDigits && ch !== '_';\n    }\n  }\n  if (ch === '_') return false;\n  for (; index < max; index++) {\n    ch = data[index];\n    if (ch === '_') continue;\n    if (!isDecCode(data.charCodeAt(index))) {\n      return false;\n    }\n    hasDigits = true;\n  }\n  if (!hasDigits || ch === '_') return false;\n  return true;\n}\nfunction constructYamlInteger(data) {\n  var value = data,\n    sign = 1,\n    ch;\n  if (value.indexOf('_') !== -1) {\n    value = value.replace(/_/g, '');\n  }\n  ch = value[0];\n  if (ch === '-' || ch === '+') {\n    if (ch === '-') sign = -1;\n    value = value.slice(1);\n    ch = value[0];\n  }\n  if (value === '0') return 0;\n  if (ch === '0') {\n    if (value[1] === 'b') return sign * parseInt(value.slice(2), 2);\n    if (value[1] === 'x') return sign * parseInt(value.slice(2), 16);\n    if (value[1] === 'o') return sign * parseInt(value.slice(2), 8);\n  }\n  return sign * parseInt(value, 10);\n}\nfunction isInteger(object) {\n  return Object.prototype.toString.call(object) === '[object Number]' && object % 1 === 0 && !common.isNegativeZero(object);\n}\nvar int = new type('tag:yaml.org,2002:int', {\n  kind: 'scalar',\n  resolve: resolveYamlInteger,\n  construct: constructYamlInteger,\n  predicate: isInteger,\n  represent: {\n    binary: function binary(obj) {\n      return obj >= 0 ? '0b' + obj.toString(2) : '-0b' + obj.toString(2).slice(1);\n    },\n    octal: function octal(obj) {\n      return obj >= 0 ? '0o' + obj.toString(8) : '-0o' + obj.toString(8).slice(1);\n    },\n    decimal: function decimal(obj) {\n      return obj.toString(10);\n    },\n    hexadecimal: function hexadecimal(obj) {\n      return obj >= 0 ? '0x' + obj.toString(16).toUpperCase() : '-0x' + obj.toString(16).toUpperCase().slice(1);\n    }\n  },\n  defaultStyle: 'decimal',\n  styleAliases: {\n    binary: [2, 'bin'],\n    octal: [8, 'oct'],\n    decimal: [10, 'dec'],\n    hexadecimal: [16, 'hex']\n  }\n});\nvar YAML_FLOAT_PATTERN = new RegExp('^(?:[-+]?(?:[0-9][0-9_]*)(?:\\\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?' + '|\\\\.[0-9_]+(?:[eE][-+]?[0-9]+)?' + '|[-+]?\\\\.(?:inf|Inf|INF)' + '|\\\\.(?:nan|NaN|NAN))$');\nfunction resolveYamlFloat(data) {\n  if (data === null) return false;\n  if (!YAML_FLOAT_PATTERN.test(data) || data[data.length - 1] === '_') {\n    return false;\n  }\n  return true;\n}\nfunction constructYamlFloat(data) {\n  var value, sign;\n  value = data.replace(/_/g, '').toLowerCase();\n  sign = value[0] === '-' ? -1 : 1;\n  if ('+-'.indexOf(value[0]) >= 0) {\n    value = value.slice(1);\n  }\n  if (value === '.inf') {\n    return sign === 1 ? Number.POSITIVE_INFINITY : Number.NEGATIVE_INFINITY;\n  } else if (value === '.nan') {\n    return NaN;\n  }\n  return sign * parseFloat(value, 10);\n}\nvar SCIENTIFIC_WITHOUT_DOT = /^[-+]?[0-9]+e/;\nfunction representYamlFloat(object, style) {\n  var res;\n  if (isNaN(object)) {\n    switch (style) {\n      case 'lowercase':\n        return '.nan';\n      case 'uppercase':\n        return '.NAN';\n      case 'camelcase':\n        return '.NaN';\n    }\n  } else if (Number.POSITIVE_INFINITY === object) {\n    switch (style) {\n      case 'lowercase':\n        return '.inf';\n      case 'uppercase':\n        return '.INF';\n      case 'camelcase':\n        return '.Inf';\n    }\n  } else if (Number.NEGATIVE_INFINITY === object) {\n    switch (style) {\n      case 'lowercase':\n        return '-.inf';\n      case 'uppercase':\n        return '-.INF';\n      case 'camelcase':\n        return '-.Inf';\n    }\n  } else if (common.isNegativeZero(object)) {\n    return '-0.0';\n  }\n  res = object.toString(10);\n  return SCIENTIFIC_WITHOUT_DOT.test(res) ? res.replace('e', '.e') : res;\n}\nfunction isFloat(object) {\n  return Object.prototype.toString.call(object) === '[object Number]' && (object % 1 !== 0 || common.isNegativeZero(object));\n}\nvar float = new type('tag:yaml.org,2002:float', {\n  kind: 'scalar',\n  resolve: resolveYamlFloat,\n  construct: constructYamlFloat,\n  predicate: isFloat,\n  represent: representYamlFloat,\n  defaultStyle: 'lowercase'\n});\nvar json = failsafe.extend({\n  implicit: [_null, bool, int, float]\n});\nvar core = json;\nvar YAML_DATE_REGEXP = new RegExp('^([0-9][0-9][0-9][0-9])' + '-([0-9][0-9])' + '-([0-9][0-9])$');\nvar YAML_TIMESTAMP_REGEXP = new RegExp('^([0-9][0-9][0-9][0-9])' + '-([0-9][0-9]?)' + '-([0-9][0-9]?)' + '(?:[Tt]|[ \\\\t]+)' + '([0-9][0-9]?)' + ':([0-9][0-9])' + ':([0-9][0-9])' + '(?:\\\\.([0-9]*))?' + '(?:[ \\\\t]*(Z|([-+])([0-9][0-9]?)' + '(?::([0-9][0-9]))?))?$');\nfunction resolveYamlTimestamp(data) {\n  if (data === null) return false;\n  if (YAML_DATE_REGEXP.exec(data) !== null) return true;\n  if (YAML_TIMESTAMP_REGEXP.exec(data) !== null) return true;\n  return false;\n}\nfunction constructYamlTimestamp(data) {\n  var match,\n    year,\n    month,\n    day,\n    hour,\n    minute,\n    second,\n    fraction = 0,\n    delta = null,\n    tz_hour,\n    tz_minute,\n    date;\n  match = YAML_DATE_REGEXP.exec(data);\n  if (match === null) match = YAML_TIMESTAMP_REGEXP.exec(data);\n  if (match === null) throw new Error('Date resolve error');\n  year = +match[1];\n  month = +match[2] - 1;\n  day = +match[3];\n  if (!match[4]) {\n    return new Date(Date.UTC(year, month, day));\n  }\n  hour = +match[4];\n  minute = +match[5];\n  second = +match[6];\n  if (match[7]) {\n    fraction = match[7].slice(0, 3);\n    while (fraction.length < 3) {\n      fraction += '0';\n    }\n    fraction = +fraction;\n  }\n  if (match[9]) {\n    tz_hour = +match[10];\n    tz_minute = +(match[11] || 0);\n    delta = (tz_hour * 60 + tz_minute) * 60000;\n    if (match[9] === '-') delta = -delta;\n  }\n  date = new Date(Date.UTC(year, month, day, hour, minute, second, fraction));\n  if (delta) date.setTime(date.getTime() - delta);\n  return date;\n}\nfunction representYamlTimestamp(object) {\n  return object.toISOString();\n}\nvar timestamp = new type('tag:yaml.org,2002:timestamp', {\n  kind: 'scalar',\n  resolve: resolveYamlTimestamp,\n  construct: constructYamlTimestamp,\n  instanceOf: Date,\n  represent: representYamlTimestamp\n});\nfunction resolveYamlMerge(data) {\n  return data === '<<' || data === null;\n}\nvar merge = new type('tag:yaml.org,2002:merge', {\n  kind: 'scalar',\n  resolve: resolveYamlMerge\n});\nvar BASE64_MAP = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\\n\\r';\nfunction resolveYamlBinary(data) {\n  if (data === null) return false;\n  var code,\n    idx,\n    bitlen = 0,\n    max = data.length,\n    map = BASE64_MAP;\n  for (idx = 0; idx < max; idx++) {\n    code = map.indexOf(data.charAt(idx));\n    if (code > 64) continue;\n    if (code < 0) return false;\n    bitlen += 6;\n  }\n  return bitlen % 8 === 0;\n}\nfunction constructYamlBinary(data) {\n  var idx,\n    tailbits,\n    input = data.replace(/[\\r\\n=]/g, ''),\n    max = input.length,\n    map = BASE64_MAP,\n    bits = 0,\n    result = [];\n  for (idx = 0; idx < max; idx++) {\n    if (idx % 4 === 0 && idx) {\n      result.push(bits >> 16 & 0xFF);\n      result.push(bits >> 8 & 0xFF);\n      result.push(bits & 0xFF);\n    }\n    bits = bits << 6 | map.indexOf(input.charAt(idx));\n  }\n  tailbits = max % 4 * 6;\n  if (tailbits === 0) {\n    result.push(bits >> 16 & 0xFF);\n    result.push(bits >> 8 & 0xFF);\n    result.push(bits & 0xFF);\n  } else if (tailbits === 18) {\n    result.push(bits >> 10 & 0xFF);\n    result.push(bits >> 2 & 0xFF);\n  } else if (tailbits === 12) {\n    result.push(bits >> 4 & 0xFF);\n  }\n  return new Uint8Array(result);\n}\nfunction representYamlBinary(object) {\n  var result = '',\n    bits = 0,\n    idx,\n    tail,\n    max = object.length,\n    map = BASE64_MAP;\n  for (idx = 0; idx < max; idx++) {\n    if (idx % 3 === 0 && idx) {\n      result += map[bits >> 18 & 0x3F];\n      result += map[bits >> 12 & 0x3F];\n      result += map[bits >> 6 & 0x3F];\n      result += map[bits & 0x3F];\n    }\n    bits = (bits << 8) + object[idx];\n  }\n  tail = max % 3;\n  if (tail === 0) {\n    result += map[bits >> 18 & 0x3F];\n    result += map[bits >> 12 & 0x3F];\n    result += map[bits >> 6 & 0x3F];\n    result += map[bits & 0x3F];\n  } else if (tail === 2) {\n    result += map[bits >> 10 & 0x3F];\n    result += map[bits >> 4 & 0x3F];\n    result += map[bits << 2 & 0x3F];\n    result += map[64];\n  } else if (tail === 1) {\n    result += map[bits >> 2 & 0x3F];\n    result += map[bits << 4 & 0x3F];\n    result += map[64];\n    result += map[64];\n  }\n  return result;\n}\nfunction isBinary(obj) {\n  return Object.prototype.toString.call(obj) === '[object Uint8Array]';\n}\nvar binary = new type('tag:yaml.org,2002:binary', {\n  kind: 'scalar',\n  resolve: resolveYamlBinary,\n  construct: constructYamlBinary,\n  predicate: isBinary,\n  represent: representYamlBinary\n});\nvar _hasOwnProperty$3 = Object.prototype.hasOwnProperty;\nvar _toString$2 = Object.prototype.toString;\nfunction resolveYamlOmap(data) {\n  if (data === null) return true;\n  var objectKeys = [],\n    index,\n    length,\n    pair,\n    pairKey,\n    pairHasKey,\n    object = data;\n  for (index = 0, length = object.length; index < length; index += 1) {\n    pair = object[index];\n    pairHasKey = false;\n    if (_toString$2.call(pair) !== '[object Object]') return false;\n    for (pairKey in pair) {\n      if (_hasOwnProperty$3.call(pair, pairKey)) {\n        if (!pairHasKey) pairHasKey = true;else return false;\n      }\n    }\n    if (!pairHasKey) return false;\n    if (objectKeys.indexOf(pairKey) === -1) objectKeys.push(pairKey);else return false;\n  }\n  return true;\n}\nfunction constructYamlOmap(data) {\n  return data !== null ? data : [];\n}\nvar omap = new type('tag:yaml.org,2002:omap', {\n  kind: 'sequence',\n  resolve: resolveYamlOmap,\n  construct: constructYamlOmap\n});\nvar _toString$1 = Object.prototype.toString;\nfunction resolveYamlPairs(data) {\n  if (data === null) return true;\n  var index,\n    length,\n    pair,\n    keys,\n    result,\n    object = data;\n  result = new Array(object.length);\n  for (index = 0, length = object.length; index < length; index += 1) {\n    pair = object[index];\n    if (_toString$1.call(pair) !== '[object Object]') return false;\n    keys = Object.keys(pair);\n    if (keys.length !== 1) return false;\n    result[index] = [keys[0], pair[keys[0]]];\n  }\n  return true;\n}\nfunction constructYamlPairs(data) {\n  if (data === null) return [];\n  var index,\n    length,\n    pair,\n    keys,\n    result,\n    object = data;\n  result = new Array(object.length);\n  for (index = 0, length = object.length; index < length; index += 1) {\n    pair = object[index];\n    keys = Object.keys(pair);\n    result[index] = [keys[0], pair[keys[0]]];\n  }\n  return result;\n}\nvar pairs = new type('tag:yaml.org,2002:pairs', {\n  kind: 'sequence',\n  resolve: resolveYamlPairs,\n  construct: constructYamlPairs\n});\nvar _hasOwnProperty$2 = Object.prototype.hasOwnProperty;\nfunction resolveYamlSet(data) {\n  if (data === null) return true;\n  var key,\n    object = data;\n  for (key in object) {\n    if (_hasOwnProperty$2.call(object, key)) {\n      if (object[key] !== null) return false;\n    }\n  }\n  return true;\n}\nfunction constructYamlSet(data) {\n  return data !== null ? data : {};\n}\nvar set = new type('tag:yaml.org,2002:set', {\n  kind: 'mapping',\n  resolve: resolveYamlSet,\n  construct: constructYamlSet\n});\nvar _default = core.extend({\n  implicit: [timestamp, merge],\n  explicit: [binary, omap, pairs, set]\n});\nvar _hasOwnProperty$1 = Object.prototype.hasOwnProperty;\nvar CONTEXT_FLOW_IN = 1;\nvar CONTEXT_FLOW_OUT = 2;\nvar CONTEXT_BLOCK_IN = 3;\nvar CONTEXT_BLOCK_OUT = 4;\nvar CHOMPING_CLIP = 1;\nvar CHOMPING_STRIP = 2;\nvar CHOMPING_KEEP = 3;\nvar PATTERN_NON_PRINTABLE = /[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x84\\x86-\\x9F\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\nvar PATTERN_NON_ASCII_LINE_BREAKS = /[\\x85\\u2028\\u2029]/;\nvar PATTERN_FLOW_INDICATORS = /[,\\[\\]\\{\\}]/;\nvar PATTERN_TAG_HANDLE = /^(?:!|!!|![a-z\\-]+!)$/i;\nvar PATTERN_TAG_URI = /^(?:!|[^,\\[\\]\\{\\}])(?:%[0-9a-f]{2}|[0-9a-z\\-#;\\/\\?:@&=\\+\\$,_\\.!~\\*'\\(\\)\\[\\]])*$/i;\nfunction _class(obj) {\n  return Object.prototype.toString.call(obj);\n}\nfunction is_EOL(c) {\n  return c === 0x0A || c === 0x0D;\n}\nfunction is_WHITE_SPACE(c) {\n  return c === 0x09 || c === 0x20;\n}\nfunction is_WS_OR_EOL(c) {\n  return c === 0x09 || c === 0x20 || c === 0x0A || c === 0x0D;\n}\nfunction is_FLOW_INDICATOR(c) {\n  return c === 0x2C || c === 0x5B || c === 0x5D || c === 0x7B || c === 0x7D;\n}\nfunction fromHexCode(c) {\n  var lc;\n  if (0x30 <= c && c <= 0x39) {\n    return c - 0x30;\n  }\n  lc = c | 0x20;\n  if (0x61 <= lc && lc <= 0x66) {\n    return lc - 0x61 + 10;\n  }\n  return -1;\n}\nfunction escapedHexLen(c) {\n  if (c === 0x78) {\n    return 2;\n  }\n  if (c === 0x75) {\n    return 4;\n  }\n  if (c === 0x55) {\n    return 8;\n  }\n  return 0;\n}\nfunction fromDecimalCode(c) {\n  if (0x30 <= c && c <= 0x39) {\n    return c - 0x30;\n  }\n  return -1;\n}\nfunction simpleEscapeSequence(c) {\n  return c === 0x30 ? '\\x00' : c === 0x61 ? '\\x07' : c === 0x62 ? '\\x08' : c === 0x74 ? '\\x09' : c === 0x09 ? '\\x09' : c === 0x6E ? '\\x0A' : c === 0x76 ? '\\x0B' : c === 0x66 ? '\\x0C' : c === 0x72 ? '\\x0D' : c === 0x65 ? '\\x1B' : c === 0x20 ? ' ' : c === 0x22 ? '\\x22' : c === 0x2F ? '/' : c === 0x5C ? '\\x5C' : c === 0x4E ? '\\x85' : c === 0x5F ? '\\xA0' : c === 0x4C ? \"\\u2028\" : c === 0x50 ? \"\\u2029\" : '';\n}\nfunction charFromCodepoint(c) {\n  if (c <= 0xFFFF) {\n    return String.fromCharCode(c);\n  }\n  return String.fromCharCode((c - 0x010000 >> 10) + 0xD800, (c - 0x010000 & 0x03FF) + 0xDC00);\n}\nvar simpleEscapeCheck = new Array(256);\nvar simpleEscapeMap = new Array(256);\nfor (var i = 0; i < 256; i++) {\n  simpleEscapeCheck[i] = simpleEscapeSequence(i) ? 1 : 0;\n  simpleEscapeMap[i] = simpleEscapeSequence(i);\n}\nfunction State$1(input, options) {\n  this.input = input;\n  this.filename = options['filename'] || null;\n  this.schema = options['schema'] || _default;\n  this.onWarning = options['onWarning'] || null;\n  this.legacy = options['legacy'] || false;\n  this.json = options['json'] || false;\n  this.listener = options['listener'] || null;\n  this.implicitTypes = this.schema.compiledImplicit;\n  this.typeMap = this.schema.compiledTypeMap;\n  this.length = input.length;\n  this.position = 0;\n  this.line = 0;\n  this.lineStart = 0;\n  this.lineIndent = 0;\n  this.firstTabInLine = -1;\n  this.documents = [];\n}\nfunction generateError(state, message) {\n  var mark = {\n    name: state.filename,\n    buffer: state.input.slice(0, -1),\n    position: state.position,\n    line: state.line,\n    column: state.position - state.lineStart\n  };\n  mark.snippet = snippet(mark);\n  return new exception(message, mark);\n}\nfunction throwError(state, message) {\n  throw generateError(state, message);\n}\nfunction throwWarning(state, message) {\n  if (state.onWarning) {\n    state.onWarning.call(null, generateError(state, message));\n  }\n}\nvar directiveHandlers = {\n  YAML: function handleYamlDirective(state, name, args) {\n    var match, major, minor;\n    if (state.version !== null) {\n      throwError(state, 'duplication of %YAML directive');\n    }\n    if (args.length !== 1) {\n      throwError(state, 'YAML directive accepts exactly one argument');\n    }\n    match = /^([0-9]+)\\.([0-9]+)$/.exec(args[0]);\n    if (match === null) {\n      throwError(state, 'ill-formed argument of the YAML directive');\n    }\n    major = parseInt(match[1], 10);\n    minor = parseInt(match[2], 10);\n    if (major !== 1) {\n      throwError(state, 'unacceptable YAML version of the document');\n    }\n    state.version = args[0];\n    state.checkLineBreaks = minor < 2;\n    if (minor !== 1 && minor !== 2) {\n      throwWarning(state, 'unsupported YAML version of the document');\n    }\n  },\n  TAG: function handleTagDirective(state, name, args) {\n    var handle, prefix;\n    if (args.length !== 2) {\n      throwError(state, 'TAG directive accepts exactly two arguments');\n    }\n    handle = args[0];\n    prefix = args[1];\n    if (!PATTERN_TAG_HANDLE.test(handle)) {\n      throwError(state, 'ill-formed tag handle (first argument) of the TAG directive');\n    }\n    if (_hasOwnProperty$1.call(state.tagMap, handle)) {\n      throwError(state, 'there is a previously declared suffix for \"' + handle + '\" tag handle');\n    }\n    if (!PATTERN_TAG_URI.test(prefix)) {\n      throwError(state, 'ill-formed tag prefix (second argument) of the TAG directive');\n    }\n    try {\n      prefix = decodeURIComponent(prefix);\n    } catch (err) {\n      throwError(state, 'tag prefix is malformed: ' + prefix);\n    }\n    state.tagMap[handle] = prefix;\n  }\n};\nfunction captureSegment(state, start, end, checkJson) {\n  var _position, _length, _character, _result;\n  if (start < end) {\n    _result = state.input.slice(start, end);\n    if (checkJson) {\n      for (_position = 0, _length = _result.length; _position < _length; _position += 1) {\n        _character = _result.charCodeAt(_position);\n        if (!(_character === 0x09 || 0x20 <= _character && _character <= 0x10FFFF)) {\n          throwError(state, 'expected valid JSON character');\n        }\n      }\n    } else if (PATTERN_NON_PRINTABLE.test(_result)) {\n      throwError(state, 'the stream contains non-printable characters');\n    }\n    state.result += _result;\n  }\n}\nfunction mergeMappings(state, destination, source, overridableKeys) {\n  var sourceKeys, key, index, quantity;\n  if (!common.isObject(source)) {\n    throwError(state, 'cannot merge mappings; the provided source object is unacceptable');\n  }\n  sourceKeys = Object.keys(source);\n  for (index = 0, quantity = sourceKeys.length; index < quantity; index += 1) {\n    key = sourceKeys[index];\n    if (!_hasOwnProperty$1.call(destination, key)) {\n      destination[key] = source[key];\n      overridableKeys[key] = true;\n    }\n  }\n}\nfunction storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, startLine, startLineStart, startPos) {\n  var index, quantity;\n  if (Array.isArray(keyNode)) {\n    keyNode = Array.prototype.slice.call(keyNode);\n    for (index = 0, quantity = keyNode.length; index < quantity; index += 1) {\n      if (Array.isArray(keyNode[index])) {\n        throwError(state, 'nested arrays are not supported inside keys');\n      }\n      if (_typeof(keyNode) === 'object' && _class(keyNode[index]) === '[object Object]') {\n        keyNode[index] = '[object Object]';\n      }\n    }\n  }\n  if (_typeof(keyNode) === 'object' && _class(keyNode) === '[object Object]') {\n    keyNode = '[object Object]';\n  }\n  keyNode = String(keyNode);\n  if (_result === null) {\n    _result = {};\n  }\n  if (keyTag === 'tag:yaml.org,2002:merge') {\n    if (Array.isArray(valueNode)) {\n      for (index = 0, quantity = valueNode.length; index < quantity; index += 1) {\n        mergeMappings(state, _result, valueNode[index], overridableKeys);\n      }\n    } else {\n      mergeMappings(state, _result, valueNode, overridableKeys);\n    }\n  } else {\n    if (!state.json && !_hasOwnProperty$1.call(overridableKeys, keyNode) && _hasOwnProperty$1.call(_result, keyNode)) {\n      state.line = startLine || state.line;\n      state.lineStart = startLineStart || state.lineStart;\n      state.position = startPos || state.position;\n      throwError(state, 'duplicated mapping key');\n    }\n    if (keyNode === '__proto__') {\n      Object.defineProperty(_result, keyNode, {\n        configurable: true,\n        enumerable: true,\n        writable: true,\n        value: valueNode\n      });\n    } else {\n      _result[keyNode] = valueNode;\n    }\n    delete overridableKeys[keyNode];\n  }\n  return _result;\n}\nfunction readLineBreak(state) {\n  var ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch === 0x0A) {\n    state.position++;\n  } else if (ch === 0x0D) {\n    state.position++;\n    if (state.input.charCodeAt(state.position) === 0x0A) {\n      state.position++;\n    }\n  } else {\n    throwError(state, 'a line break is expected');\n  }\n  state.line += 1;\n  state.lineStart = state.position;\n  state.firstTabInLine = -1;\n}\nfunction skipSeparationSpace(state, allowComments, checkIndent) {\n  var lineBreaks = 0,\n    ch = state.input.charCodeAt(state.position);\n  while (ch !== 0) {\n    while (is_WHITE_SPACE(ch)) {\n      if (ch === 0x09 && state.firstTabInLine === -1) {\n        state.firstTabInLine = state.position;\n      }\n      ch = state.input.charCodeAt(++state.position);\n    }\n    if (allowComments && ch === 0x23) {\n      do {\n        ch = state.input.charCodeAt(++state.position);\n      } while (ch !== 0x0A && ch !== 0x0D && ch !== 0);\n    }\n    if (is_EOL(ch)) {\n      readLineBreak(state);\n      ch = state.input.charCodeAt(state.position);\n      lineBreaks++;\n      state.lineIndent = 0;\n      while (ch === 0x20) {\n        state.lineIndent++;\n        ch = state.input.charCodeAt(++state.position);\n      }\n    } else {\n      break;\n    }\n  }\n  if (checkIndent !== -1 && lineBreaks !== 0 && state.lineIndent < checkIndent) {\n    throwWarning(state, 'deficient indentation');\n  }\n  return lineBreaks;\n}\nfunction testDocumentSeparator(state) {\n  var _position = state.position,\n    ch;\n  ch = state.input.charCodeAt(_position);\n  if ((ch === 0x2D || ch === 0x2E) && ch === state.input.charCodeAt(_position + 1) && ch === state.input.charCodeAt(_position + 2)) {\n    _position += 3;\n    ch = state.input.charCodeAt(_position);\n    if (ch === 0 || is_WS_OR_EOL(ch)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction writeFoldedLines(state, count) {\n  if (count === 1) {\n    state.result += ' ';\n  } else if (count > 1) {\n    state.result += common.repeat('\\n', count - 1);\n  }\n}\nfunction readPlainScalar(state, nodeIndent, withinFlowCollection) {\n  var preceding,\n    following,\n    captureStart,\n    captureEnd,\n    hasPendingContent,\n    _line,\n    _lineStart,\n    _lineIndent,\n    _kind = state.kind,\n    _result = state.result,\n    ch;\n  ch = state.input.charCodeAt(state.position);\n  if (is_WS_OR_EOL(ch) || is_FLOW_INDICATOR(ch) || ch === 0x23 || ch === 0x26 || ch === 0x2A || ch === 0x21 || ch === 0x7C || ch === 0x3E || ch === 0x27 || ch === 0x22 || ch === 0x25 || ch === 0x40 || ch === 0x60) {\n    return false;\n  }\n  if (ch === 0x3F || ch === 0x2D) {\n    following = state.input.charCodeAt(state.position + 1);\n    if (is_WS_OR_EOL(following) || withinFlowCollection && is_FLOW_INDICATOR(following)) {\n      return false;\n    }\n  }\n  state.kind = 'scalar';\n  state.result = '';\n  captureStart = captureEnd = state.position;\n  hasPendingContent = false;\n  while (ch !== 0) {\n    if (ch === 0x3A) {\n      following = state.input.charCodeAt(state.position + 1);\n      if (is_WS_OR_EOL(following) || withinFlowCollection && is_FLOW_INDICATOR(following)) {\n        break;\n      }\n    } else if (ch === 0x23) {\n      preceding = state.input.charCodeAt(state.position - 1);\n      if (is_WS_OR_EOL(preceding)) {\n        break;\n      }\n    } else if (state.position === state.lineStart && testDocumentSeparator(state) || withinFlowCollection && is_FLOW_INDICATOR(ch)) {\n      break;\n    } else if (is_EOL(ch)) {\n      _line = state.line;\n      _lineStart = state.lineStart;\n      _lineIndent = state.lineIndent;\n      skipSeparationSpace(state, false, -1);\n      if (state.lineIndent >= nodeIndent) {\n        hasPendingContent = true;\n        ch = state.input.charCodeAt(state.position);\n        continue;\n      } else {\n        state.position = captureEnd;\n        state.line = _line;\n        state.lineStart = _lineStart;\n        state.lineIndent = _lineIndent;\n        break;\n      }\n    }\n    if (hasPendingContent) {\n      captureSegment(state, captureStart, captureEnd, false);\n      writeFoldedLines(state, state.line - _line);\n      captureStart = captureEnd = state.position;\n      hasPendingContent = false;\n    }\n    if (!is_WHITE_SPACE(ch)) {\n      captureEnd = state.position + 1;\n    }\n    ch = state.input.charCodeAt(++state.position);\n  }\n  captureSegment(state, captureStart, captureEnd, false);\n  if (state.result) {\n    return true;\n  }\n  state.kind = _kind;\n  state.result = _result;\n  return false;\n}\nfunction readSingleQuotedScalar(state, nodeIndent) {\n  var ch, captureStart, captureEnd;\n  ch = state.input.charCodeAt(state.position);\n  if (ch !== 0x27) {\n    return false;\n  }\n  state.kind = 'scalar';\n  state.result = '';\n  state.position++;\n  captureStart = captureEnd = state.position;\n  while ((ch = state.input.charCodeAt(state.position)) !== 0) {\n    if (ch === 0x27) {\n      captureSegment(state, captureStart, state.position, true);\n      ch = state.input.charCodeAt(++state.position);\n      if (ch === 0x27) {\n        captureStart = state.position;\n        state.position++;\n        captureEnd = state.position;\n      } else {\n        return true;\n      }\n    } else if (is_EOL(ch)) {\n      captureSegment(state, captureStart, captureEnd, true);\n      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));\n      captureStart = captureEnd = state.position;\n    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {\n      throwError(state, 'unexpected end of the document within a single quoted scalar');\n    } else {\n      state.position++;\n      captureEnd = state.position;\n    }\n  }\n  throwError(state, 'unexpected end of the stream within a single quoted scalar');\n}\nfunction readDoubleQuotedScalar(state, nodeIndent) {\n  var captureStart, captureEnd, hexLength, hexResult, tmp, ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch !== 0x22) {\n    return false;\n  }\n  state.kind = 'scalar';\n  state.result = '';\n  state.position++;\n  captureStart = captureEnd = state.position;\n  while ((ch = state.input.charCodeAt(state.position)) !== 0) {\n    if (ch === 0x22) {\n      captureSegment(state, captureStart, state.position, true);\n      state.position++;\n      return true;\n    } else if (ch === 0x5C) {\n      captureSegment(state, captureStart, state.position, true);\n      ch = state.input.charCodeAt(++state.position);\n      if (is_EOL(ch)) {\n        skipSeparationSpace(state, false, nodeIndent);\n      } else if (ch < 256 && simpleEscapeCheck[ch]) {\n        state.result += simpleEscapeMap[ch];\n        state.position++;\n      } else if ((tmp = escapedHexLen(ch)) > 0) {\n        hexLength = tmp;\n        hexResult = 0;\n        for (; hexLength > 0; hexLength--) {\n          ch = state.input.charCodeAt(++state.position);\n          if ((tmp = fromHexCode(ch)) >= 0) {\n            hexResult = (hexResult << 4) + tmp;\n          } else {\n            throwError(state, 'expected hexadecimal character');\n          }\n        }\n        state.result += charFromCodepoint(hexResult);\n        state.position++;\n      } else {\n        throwError(state, 'unknown escape sequence');\n      }\n      captureStart = captureEnd = state.position;\n    } else if (is_EOL(ch)) {\n      captureSegment(state, captureStart, captureEnd, true);\n      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));\n      captureStart = captureEnd = state.position;\n    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {\n      throwError(state, 'unexpected end of the document within a double quoted scalar');\n    } else {\n      state.position++;\n      captureEnd = state.position;\n    }\n  }\n  throwError(state, 'unexpected end of the stream within a double quoted scalar');\n}\nfunction readFlowCollection(state, nodeIndent) {\n  var readNext = true,\n    _line,\n    _lineStart,\n    _pos,\n    _tag = state.tag,\n    _result,\n    _anchor = state.anchor,\n    following,\n    terminator,\n    isPair,\n    isExplicitPair,\n    isMapping,\n    overridableKeys = Object.create(null),\n    keyNode,\n    keyTag,\n    valueNode,\n    ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch === 0x5B) {\n    terminator = 0x5D;\n    isMapping = false;\n    _result = [];\n  } else if (ch === 0x7B) {\n    terminator = 0x7D;\n    isMapping = true;\n    _result = {};\n  } else {\n    return false;\n  }\n  if (state.anchor !== null) {\n    state.anchorMap[state.anchor] = _result;\n  }\n  ch = state.input.charCodeAt(++state.position);\n  while (ch !== 0) {\n    skipSeparationSpace(state, true, nodeIndent);\n    ch = state.input.charCodeAt(state.position);\n    if (ch === terminator) {\n      state.position++;\n      state.tag = _tag;\n      state.anchor = _anchor;\n      state.kind = isMapping ? 'mapping' : 'sequence';\n      state.result = _result;\n      return true;\n    } else if (!readNext) {\n      throwError(state, 'missed comma between flow collection entries');\n    } else if (ch === 0x2C) {\n      throwError(state, \"expected the node content, but found ','\");\n    }\n    keyTag = keyNode = valueNode = null;\n    isPair = isExplicitPair = false;\n    if (ch === 0x3F) {\n      following = state.input.charCodeAt(state.position + 1);\n      if (is_WS_OR_EOL(following)) {\n        isPair = isExplicitPair = true;\n        state.position++;\n        skipSeparationSpace(state, true, nodeIndent);\n      }\n    }\n    _line = state.line;\n    _lineStart = state.lineStart;\n    _pos = state.position;\n    composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);\n    keyTag = state.tag;\n    keyNode = state.result;\n    skipSeparationSpace(state, true, nodeIndent);\n    ch = state.input.charCodeAt(state.position);\n    if ((isExplicitPair || state.line === _line) && ch === 0x3A) {\n      isPair = true;\n      ch = state.input.charCodeAt(++state.position);\n      skipSeparationSpace(state, true, nodeIndent);\n      composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);\n      valueNode = state.result;\n    }\n    if (isMapping) {\n      storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, _line, _lineStart, _pos);\n    } else if (isPair) {\n      _result.push(storeMappingPair(state, null, overridableKeys, keyTag, keyNode, valueNode, _line, _lineStart, _pos));\n    } else {\n      _result.push(keyNode);\n    }\n    skipSeparationSpace(state, true, nodeIndent);\n    ch = state.input.charCodeAt(state.position);\n    if (ch === 0x2C) {\n      readNext = true;\n      ch = state.input.charCodeAt(++state.position);\n    } else {\n      readNext = false;\n    }\n  }\n  throwError(state, 'unexpected end of the stream within a flow collection');\n}\nfunction readBlockScalar(state, nodeIndent) {\n  var captureStart,\n    folding,\n    chomping = CHOMPING_CLIP,\n    didReadContent = false,\n    detectedIndent = false,\n    textIndent = nodeIndent,\n    emptyLines = 0,\n    atMoreIndented = false,\n    tmp,\n    ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch === 0x7C) {\n    folding = false;\n  } else if (ch === 0x3E) {\n    folding = true;\n  } else {\n    return false;\n  }\n  state.kind = 'scalar';\n  state.result = '';\n  while (ch !== 0) {\n    ch = state.input.charCodeAt(++state.position);\n    if (ch === 0x2B || ch === 0x2D) {\n      if (CHOMPING_CLIP === chomping) {\n        chomping = ch === 0x2B ? CHOMPING_KEEP : CHOMPING_STRIP;\n      } else {\n        throwError(state, 'repeat of a chomping mode identifier');\n      }\n    } else if ((tmp = fromDecimalCode(ch)) >= 0) {\n      if (tmp === 0) {\n        throwError(state, 'bad explicit indentation width of a block scalar; it cannot be less than one');\n      } else if (!detectedIndent) {\n        textIndent = nodeIndent + tmp - 1;\n        detectedIndent = true;\n      } else {\n        throwError(state, 'repeat of an indentation width identifier');\n      }\n    } else {\n      break;\n    }\n  }\n  if (is_WHITE_SPACE(ch)) {\n    do {\n      ch = state.input.charCodeAt(++state.position);\n    } while (is_WHITE_SPACE(ch));\n    if (ch === 0x23) {\n      do {\n        ch = state.input.charCodeAt(++state.position);\n      } while (!is_EOL(ch) && ch !== 0);\n    }\n  }\n  while (ch !== 0) {\n    readLineBreak(state);\n    state.lineIndent = 0;\n    ch = state.input.charCodeAt(state.position);\n    while ((!detectedIndent || state.lineIndent < textIndent) && ch === 0x20) {\n      state.lineIndent++;\n      ch = state.input.charCodeAt(++state.position);\n    }\n    if (!detectedIndent && state.lineIndent > textIndent) {\n      textIndent = state.lineIndent;\n    }\n    if (is_EOL(ch)) {\n      emptyLines++;\n      continue;\n    }\n    if (state.lineIndent < textIndent) {\n      if (chomping === CHOMPING_KEEP) {\n        state.result += common.repeat('\\n', didReadContent ? 1 + emptyLines : emptyLines);\n      } else if (chomping === CHOMPING_CLIP) {\n        if (didReadContent) {\n          state.result += '\\n';\n        }\n      }\n      break;\n    }\n    if (folding) {\n      if (is_WHITE_SPACE(ch)) {\n        atMoreIndented = true;\n        state.result += common.repeat('\\n', didReadContent ? 1 + emptyLines : emptyLines);\n      } else if (atMoreIndented) {\n        atMoreIndented = false;\n        state.result += common.repeat('\\n', emptyLines + 1);\n      } else if (emptyLines === 0) {\n        if (didReadContent) {\n          state.result += ' ';\n        }\n      } else {\n        state.result += common.repeat('\\n', emptyLines);\n      }\n    } else {\n      state.result += common.repeat('\\n', didReadContent ? 1 + emptyLines : emptyLines);\n    }\n    didReadContent = true;\n    detectedIndent = true;\n    emptyLines = 0;\n    captureStart = state.position;\n    while (!is_EOL(ch) && ch !== 0) {\n      ch = state.input.charCodeAt(++state.position);\n    }\n    captureSegment(state, captureStart, state.position, false);\n  }\n  return true;\n}\nfunction readBlockSequence(state, nodeIndent) {\n  var _line,\n    _tag = state.tag,\n    _anchor = state.anchor,\n    _result = [],\n    following,\n    detected = false,\n    ch;\n  if (state.firstTabInLine !== -1) return false;\n  if (state.anchor !== null) {\n    state.anchorMap[state.anchor] = _result;\n  }\n  ch = state.input.charCodeAt(state.position);\n  while (ch !== 0) {\n    if (state.firstTabInLine !== -1) {\n      state.position = state.firstTabInLine;\n      throwError(state, 'tab characters must not be used in indentation');\n    }\n    if (ch !== 0x2D) {\n      break;\n    }\n    following = state.input.charCodeAt(state.position + 1);\n    if (!is_WS_OR_EOL(following)) {\n      break;\n    }\n    detected = true;\n    state.position++;\n    if (skipSeparationSpace(state, true, -1)) {\n      if (state.lineIndent <= nodeIndent) {\n        _result.push(null);\n        ch = state.input.charCodeAt(state.position);\n        continue;\n      }\n    }\n    _line = state.line;\n    composeNode(state, nodeIndent, CONTEXT_BLOCK_IN, false, true);\n    _result.push(state.result);\n    skipSeparationSpace(state, true, -1);\n    ch = state.input.charCodeAt(state.position);\n    if ((state.line === _line || state.lineIndent > nodeIndent) && ch !== 0) {\n      throwError(state, 'bad indentation of a sequence entry');\n    } else if (state.lineIndent < nodeIndent) {\n      break;\n    }\n  }\n  if (detected) {\n    state.tag = _tag;\n    state.anchor = _anchor;\n    state.kind = 'sequence';\n    state.result = _result;\n    return true;\n  }\n  return false;\n}\nfunction readBlockMapping(state, nodeIndent, flowIndent) {\n  var following,\n    allowCompact,\n    _line,\n    _keyLine,\n    _keyLineStart,\n    _keyPos,\n    _tag = state.tag,\n    _anchor = state.anchor,\n    _result = {},\n    overridableKeys = Object.create(null),\n    keyTag = null,\n    keyNode = null,\n    valueNode = null,\n    atExplicitKey = false,\n    detected = false,\n    ch;\n  if (state.firstTabInLine !== -1) return false;\n  if (state.anchor !== null) {\n    state.anchorMap[state.anchor] = _result;\n  }\n  ch = state.input.charCodeAt(state.position);\n  while (ch !== 0) {\n    if (!atExplicitKey && state.firstTabInLine !== -1) {\n      state.position = state.firstTabInLine;\n      throwError(state, 'tab characters must not be used in indentation');\n    }\n    following = state.input.charCodeAt(state.position + 1);\n    _line = state.line;\n    if ((ch === 0x3F || ch === 0x3A) && is_WS_OR_EOL(following)) {\n      if (ch === 0x3F) {\n        if (atExplicitKey) {\n          storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);\n          keyTag = keyNode = valueNode = null;\n        }\n        detected = true;\n        atExplicitKey = true;\n        allowCompact = true;\n      } else if (atExplicitKey) {\n        atExplicitKey = false;\n        allowCompact = true;\n      } else {\n        throwError(state, 'incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line');\n      }\n      state.position += 1;\n      ch = following;\n    } else {\n      _keyLine = state.line;\n      _keyLineStart = state.lineStart;\n      _keyPos = state.position;\n      if (!composeNode(state, flowIndent, CONTEXT_FLOW_OUT, false, true)) {\n        break;\n      }\n      if (state.line === _line) {\n        ch = state.input.charCodeAt(state.position);\n        while (is_WHITE_SPACE(ch)) {\n          ch = state.input.charCodeAt(++state.position);\n        }\n        if (ch === 0x3A) {\n          ch = state.input.charCodeAt(++state.position);\n          if (!is_WS_OR_EOL(ch)) {\n            throwError(state, 'a whitespace character is expected after the key-value separator within a block mapping');\n          }\n          if (atExplicitKey) {\n            storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);\n            keyTag = keyNode = valueNode = null;\n          }\n          detected = true;\n          atExplicitKey = false;\n          allowCompact = false;\n          keyTag = state.tag;\n          keyNode = state.result;\n        } else if (detected) {\n          throwError(state, 'can not read an implicit mapping pair; a colon is missed');\n        } else {\n          state.tag = _tag;\n          state.anchor = _anchor;\n          return true;\n        }\n      } else if (detected) {\n        throwError(state, 'can not read a block mapping entry; a multiline key may not be an implicit key');\n      } else {\n        state.tag = _tag;\n        state.anchor = _anchor;\n        return true;\n      }\n    }\n    if (state.line === _line || state.lineIndent > nodeIndent) {\n      if (atExplicitKey) {\n        _keyLine = state.line;\n        _keyLineStart = state.lineStart;\n        _keyPos = state.position;\n      }\n      if (composeNode(state, nodeIndent, CONTEXT_BLOCK_OUT, true, allowCompact)) {\n        if (atExplicitKey) {\n          keyNode = state.result;\n        } else {\n          valueNode = state.result;\n        }\n      }\n      if (!atExplicitKey) {\n        storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, _keyLine, _keyLineStart, _keyPos);\n        keyTag = keyNode = valueNode = null;\n      }\n      skipSeparationSpace(state, true, -1);\n      ch = state.input.charCodeAt(state.position);\n    }\n    if ((state.line === _line || state.lineIndent > nodeIndent) && ch !== 0) {\n      throwError(state, 'bad indentation of a mapping entry');\n    } else if (state.lineIndent < nodeIndent) {\n      break;\n    }\n  }\n  if (atExplicitKey) {\n    storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);\n  }\n  if (detected) {\n    state.tag = _tag;\n    state.anchor = _anchor;\n    state.kind = 'mapping';\n    state.result = _result;\n  }\n  return detected;\n}\nfunction readTagProperty(state) {\n  var _position,\n    isVerbatim = false,\n    isNamed = false,\n    tagHandle,\n    tagName,\n    ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch !== 0x21) return false;\n  if (state.tag !== null) {\n    throwError(state, 'duplication of a tag property');\n  }\n  ch = state.input.charCodeAt(++state.position);\n  if (ch === 0x3C) {\n    isVerbatim = true;\n    ch = state.input.charCodeAt(++state.position);\n  } else if (ch === 0x21) {\n    isNamed = true;\n    tagHandle = '!!';\n    ch = state.input.charCodeAt(++state.position);\n  } else {\n    tagHandle = '!';\n  }\n  _position = state.position;\n  if (isVerbatim) {\n    do {\n      ch = state.input.charCodeAt(++state.position);\n    } while (ch !== 0 && ch !== 0x3E);\n    if (state.position < state.length) {\n      tagName = state.input.slice(_position, state.position);\n      ch = state.input.charCodeAt(++state.position);\n    } else {\n      throwError(state, 'unexpected end of the stream within a verbatim tag');\n    }\n  } else {\n    while (ch !== 0 && !is_WS_OR_EOL(ch)) {\n      if (ch === 0x21) {\n        if (!isNamed) {\n          tagHandle = state.input.slice(_position - 1, state.position + 1);\n          if (!PATTERN_TAG_HANDLE.test(tagHandle)) {\n            throwError(state, 'named tag handle cannot contain such characters');\n          }\n          isNamed = true;\n          _position = state.position + 1;\n        } else {\n          throwError(state, 'tag suffix cannot contain exclamation marks');\n        }\n      }\n      ch = state.input.charCodeAt(++state.position);\n    }\n    tagName = state.input.slice(_position, state.position);\n    if (PATTERN_FLOW_INDICATORS.test(tagName)) {\n      throwError(state, 'tag suffix cannot contain flow indicator characters');\n    }\n  }\n  if (tagName && !PATTERN_TAG_URI.test(tagName)) {\n    throwError(state, 'tag name cannot contain such characters: ' + tagName);\n  }\n  try {\n    tagName = decodeURIComponent(tagName);\n  } catch (err) {\n    throwError(state, 'tag name is malformed: ' + tagName);\n  }\n  if (isVerbatim) {\n    state.tag = tagName;\n  } else if (_hasOwnProperty$1.call(state.tagMap, tagHandle)) {\n    state.tag = state.tagMap[tagHandle] + tagName;\n  } else if (tagHandle === '!') {\n    state.tag = '!' + tagName;\n  } else if (tagHandle === '!!') {\n    state.tag = 'tag:yaml.org,2002:' + tagName;\n  } else {\n    throwError(state, 'undeclared tag handle \"' + tagHandle + '\"');\n  }\n  return true;\n}\nfunction readAnchorProperty(state) {\n  var _position, ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch !== 0x26) return false;\n  if (state.anchor !== null) {\n    throwError(state, 'duplication of an anchor property');\n  }\n  ch = state.input.charCodeAt(++state.position);\n  _position = state.position;\n  while (ch !== 0 && !is_WS_OR_EOL(ch) && !is_FLOW_INDICATOR(ch)) {\n    ch = state.input.charCodeAt(++state.position);\n  }\n  if (state.position === _position) {\n    throwError(state, 'name of an anchor node must contain at least one character');\n  }\n  state.anchor = state.input.slice(_position, state.position);\n  return true;\n}\nfunction readAlias(state) {\n  var _position, alias, ch;\n  ch = state.input.charCodeAt(state.position);\n  if (ch !== 0x2A) return false;\n  ch = state.input.charCodeAt(++state.position);\n  _position = state.position;\n  while (ch !== 0 && !is_WS_OR_EOL(ch) && !is_FLOW_INDICATOR(ch)) {\n    ch = state.input.charCodeAt(++state.position);\n  }\n  if (state.position === _position) {\n    throwError(state, 'name of an alias node must contain at least one character');\n  }\n  alias = state.input.slice(_position, state.position);\n  if (!_hasOwnProperty$1.call(state.anchorMap, alias)) {\n    throwError(state, 'unidentified alias \"' + alias + '\"');\n  }\n  state.result = state.anchorMap[alias];\n  skipSeparationSpace(state, true, -1);\n  return true;\n}\nfunction composeNode(state, parentIndent, nodeContext, allowToSeek, allowCompact) {\n  var allowBlockStyles,\n    allowBlockScalars,\n    allowBlockCollections,\n    indentStatus = 1,\n    atNewLine = false,\n    hasContent = false,\n    typeIndex,\n    typeQuantity,\n    typeList,\n    type,\n    flowIndent,\n    blockIndent;\n  if (state.listener !== null) {\n    state.listener('open', state);\n  }\n  state.tag = null;\n  state.anchor = null;\n  state.kind = null;\n  state.result = null;\n  allowBlockStyles = allowBlockScalars = allowBlockCollections = CONTEXT_BLOCK_OUT === nodeContext || CONTEXT_BLOCK_IN === nodeContext;\n  if (allowToSeek) {\n    if (skipSeparationSpace(state, true, -1)) {\n      atNewLine = true;\n      if (state.lineIndent > parentIndent) {\n        indentStatus = 1;\n      } else if (state.lineIndent === parentIndent) {\n        indentStatus = 0;\n      } else if (state.lineIndent < parentIndent) {\n        indentStatus = -1;\n      }\n    }\n  }\n  if (indentStatus === 1) {\n    while (readTagProperty(state) || readAnchorProperty(state)) {\n      if (skipSeparationSpace(state, true, -1)) {\n        atNewLine = true;\n        allowBlockCollections = allowBlockStyles;\n        if (state.lineIndent > parentIndent) {\n          indentStatus = 1;\n        } else if (state.lineIndent === parentIndent) {\n          indentStatus = 0;\n        } else if (state.lineIndent < parentIndent) {\n          indentStatus = -1;\n        }\n      } else {\n        allowBlockCollections = false;\n      }\n    }\n  }\n  if (allowBlockCollections) {\n    allowBlockCollections = atNewLine || allowCompact;\n  }\n  if (indentStatus === 1 || CONTEXT_BLOCK_OUT === nodeContext) {\n    if (CONTEXT_FLOW_IN === nodeContext || CONTEXT_FLOW_OUT === nodeContext) {\n      flowIndent = parentIndent;\n    } else {\n      flowIndent = parentIndent + 1;\n    }\n    blockIndent = state.position - state.lineStart;\n    if (indentStatus === 1) {\n      if (allowBlockCollections && (readBlockSequence(state, blockIndent) || readBlockMapping(state, blockIndent, flowIndent)) || readFlowCollection(state, flowIndent)) {\n        hasContent = true;\n      } else {\n        if (allowBlockScalars && readBlockScalar(state, flowIndent) || readSingleQuotedScalar(state, flowIndent) || readDoubleQuotedScalar(state, flowIndent)) {\n          hasContent = true;\n        } else if (readAlias(state)) {\n          hasContent = true;\n          if (state.tag !== null || state.anchor !== null) {\n            throwError(state, 'alias node should not have any properties');\n          }\n        } else if (readPlainScalar(state, flowIndent, CONTEXT_FLOW_IN === nodeContext)) {\n          hasContent = true;\n          if (state.tag === null) {\n            state.tag = '?';\n          }\n        }\n        if (state.anchor !== null) {\n          state.anchorMap[state.anchor] = state.result;\n        }\n      }\n    } else if (indentStatus === 0) {\n      hasContent = allowBlockCollections && readBlockSequence(state, blockIndent);\n    }\n  }\n  if (state.tag === null) {\n    if (state.anchor !== null) {\n      state.anchorMap[state.anchor] = state.result;\n    }\n  } else if (state.tag === '?') {\n    if (state.result !== null && state.kind !== 'scalar') {\n      throwError(state, 'unacceptable node kind for !<?> tag; it should be \"scalar\", not \"' + state.kind + '\"');\n    }\n    for (typeIndex = 0, typeQuantity = state.implicitTypes.length; typeIndex < typeQuantity; typeIndex += 1) {\n      type = state.implicitTypes[typeIndex];\n      if (type.resolve(state.result)) {\n        state.result = type.construct(state.result);\n        state.tag = type.tag;\n        if (state.anchor !== null) {\n          state.anchorMap[state.anchor] = state.result;\n        }\n        break;\n      }\n    }\n  } else if (state.tag !== '!') {\n    if (_hasOwnProperty$1.call(state.typeMap[state.kind || 'fallback'], state.tag)) {\n      type = state.typeMap[state.kind || 'fallback'][state.tag];\n    } else {\n      type = null;\n      typeList = state.typeMap.multi[state.kind || 'fallback'];\n      for (typeIndex = 0, typeQuantity = typeList.length; typeIndex < typeQuantity; typeIndex += 1) {\n        if (state.tag.slice(0, typeList[typeIndex].tag.length) === typeList[typeIndex].tag) {\n          type = typeList[typeIndex];\n          break;\n        }\n      }\n    }\n    if (!type) {\n      throwError(state, 'unknown tag !<' + state.tag + '>');\n    }\n    if (state.result !== null && type.kind !== state.kind) {\n      throwError(state, 'unacceptable node kind for !<' + state.tag + '> tag; it should be \"' + type.kind + '\", not \"' + state.kind + '\"');\n    }\n    if (!type.resolve(state.result, state.tag)) {\n      throwError(state, 'cannot resolve a node with !<' + state.tag + '> explicit tag');\n    } else {\n      state.result = type.construct(state.result, state.tag);\n      if (state.anchor !== null) {\n        state.anchorMap[state.anchor] = state.result;\n      }\n    }\n  }\n  if (state.listener !== null) {\n    state.listener('close', state);\n  }\n  return state.tag !== null || state.anchor !== null || hasContent;\n}\nfunction readDocument(state) {\n  var documentStart = state.position,\n    _position,\n    directiveName,\n    directiveArgs,\n    hasDirectives = false,\n    ch;\n  state.version = null;\n  state.checkLineBreaks = state.legacy;\n  state.tagMap = Object.create(null);\n  state.anchorMap = Object.create(null);\n  while ((ch = state.input.charCodeAt(state.position)) !== 0) {\n    skipSeparationSpace(state, true, -1);\n    ch = state.input.charCodeAt(state.position);\n    if (state.lineIndent > 0 || ch !== 0x25) {\n      break;\n    }\n    hasDirectives = true;\n    ch = state.input.charCodeAt(++state.position);\n    _position = state.position;\n    while (ch !== 0 && !is_WS_OR_EOL(ch)) {\n      ch = state.input.charCodeAt(++state.position);\n    }\n    directiveName = state.input.slice(_position, state.position);\n    directiveArgs = [];\n    if (directiveName.length < 1) {\n      throwError(state, 'directive name must not be less than one character in length');\n    }\n    while (ch !== 0) {\n      while (is_WHITE_SPACE(ch)) {\n        ch = state.input.charCodeAt(++state.position);\n      }\n      if (ch === 0x23) {\n        do {\n          ch = state.input.charCodeAt(++state.position);\n        } while (ch !== 0 && !is_EOL(ch));\n        break;\n      }\n      if (is_EOL(ch)) break;\n      _position = state.position;\n      while (ch !== 0 && !is_WS_OR_EOL(ch)) {\n        ch = state.input.charCodeAt(++state.position);\n      }\n      directiveArgs.push(state.input.slice(_position, state.position));\n    }\n    if (ch !== 0) readLineBreak(state);\n    if (_hasOwnProperty$1.call(directiveHandlers, directiveName)) {\n      directiveHandlers[directiveName](state, directiveName, directiveArgs);\n    } else {\n      throwWarning(state, 'unknown document directive \"' + directiveName + '\"');\n    }\n  }\n  skipSeparationSpace(state, true, -1);\n  if (state.lineIndent === 0 && state.input.charCodeAt(state.position) === 0x2D && state.input.charCodeAt(state.position + 1) === 0x2D && state.input.charCodeAt(state.position + 2) === 0x2D) {\n    state.position += 3;\n    skipSeparationSpace(state, true, -1);\n  } else if (hasDirectives) {\n    throwError(state, 'directives end mark is expected');\n  }\n  composeNode(state, state.lineIndent - 1, CONTEXT_BLOCK_OUT, false, true);\n  skipSeparationSpace(state, true, -1);\n  if (state.checkLineBreaks && PATTERN_NON_ASCII_LINE_BREAKS.test(state.input.slice(documentStart, state.position))) {\n    throwWarning(state, 'non-ASCII line breaks are interpreted as content');\n  }\n  state.documents.push(state.result);\n  if (state.position === state.lineStart && testDocumentSeparator(state)) {\n    if (state.input.charCodeAt(state.position) === 0x2E) {\n      state.position += 3;\n      skipSeparationSpace(state, true, -1);\n    }\n    return;\n  }\n  if (state.position < state.length - 1) {\n    throwError(state, 'end of the stream or a document separator is expected');\n  } else {\n    return;\n  }\n}\nfunction loadDocuments(input, options) {\n  input = String(input);\n  options = options || {};\n  if (input.length !== 0) {\n    if (input.charCodeAt(input.length - 1) !== 0x0A && input.charCodeAt(input.length - 1) !== 0x0D) {\n      input += '\\n';\n    }\n    if (input.charCodeAt(0) === 0xFEFF) {\n      input = input.slice(1);\n    }\n  }\n  var state = new State$1(input, options);\n  var nullpos = input.indexOf('\\0');\n  if (nullpos !== -1) {\n    state.position = nullpos;\n    throwError(state, 'null byte is not allowed in input');\n  }\n  state.input += '\\0';\n  while (state.input.charCodeAt(state.position) === 0x20) {\n    state.lineIndent += 1;\n    state.position += 1;\n  }\n  while (state.position < state.length - 1) {\n    readDocument(state);\n  }\n  return state.documents;\n}\nfunction loadAll$1(input, iterator, options) {\n  if (iterator !== null && _typeof(iterator) === 'object' && typeof options === 'undefined') {\n    options = iterator;\n    iterator = null;\n  }\n  var documents = loadDocuments(input, options);\n  if (typeof iterator !== 'function') {\n    return documents;\n  }\n  for (var index = 0, length = documents.length; index < length; index += 1) {\n    iterator(documents[index]);\n  }\n}\nfunction load$1(input, options) {\n  var documents = loadDocuments(input, options);\n  if (documents.length === 0) {\n    return undefined;\n  } else if (documents.length === 1) {\n    return documents[0];\n  }\n  throw new exception('expected a single document in the stream, but found more');\n}\nvar loadAll_1 = loadAll$1;\nvar load_1 = load$1;\nvar loader = {\n  loadAll: loadAll_1,\n  load: load_1\n};\nvar _toString = Object.prototype.toString;\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\nvar CHAR_BOM = 0xFEFF;\nvar CHAR_TAB = 0x09;\nvar CHAR_LINE_FEED = 0x0A;\nvar CHAR_CARRIAGE_RETURN = 0x0D;\nvar CHAR_SPACE = 0x20;\nvar CHAR_EXCLAMATION = 0x21;\nvar CHAR_DOUBLE_QUOTE = 0x22;\nvar CHAR_SHARP = 0x23;\nvar CHAR_PERCENT = 0x25;\nvar CHAR_AMPERSAND = 0x26;\nvar CHAR_SINGLE_QUOTE = 0x27;\nvar CHAR_ASTERISK = 0x2A;\nvar CHAR_COMMA = 0x2C;\nvar CHAR_MINUS = 0x2D;\nvar CHAR_COLON = 0x3A;\nvar CHAR_EQUALS = 0x3D;\nvar CHAR_GREATER_THAN = 0x3E;\nvar CHAR_QUESTION = 0x3F;\nvar CHAR_COMMERCIAL_AT = 0x40;\nvar CHAR_LEFT_SQUARE_BRACKET = 0x5B;\nvar CHAR_RIGHT_SQUARE_BRACKET = 0x5D;\nvar CHAR_GRAVE_ACCENT = 0x60;\nvar CHAR_LEFT_CURLY_BRACKET = 0x7B;\nvar CHAR_VERTICAL_LINE = 0x7C;\nvar CHAR_RIGHT_CURLY_BRACKET = 0x7D;\nvar ESCAPE_SEQUENCES = {};\nESCAPE_SEQUENCES[0x00] = '\\\\0';\nESCAPE_SEQUENCES[0x07] = '\\\\a';\nESCAPE_SEQUENCES[0x08] = '\\\\b';\nESCAPE_SEQUENCES[0x09] = '\\\\t';\nESCAPE_SEQUENCES[0x0A] = '\\\\n';\nESCAPE_SEQUENCES[0x0B] = '\\\\v';\nESCAPE_SEQUENCES[0x0C] = '\\\\f';\nESCAPE_SEQUENCES[0x0D] = '\\\\r';\nESCAPE_SEQUENCES[0x1B] = '\\\\e';\nESCAPE_SEQUENCES[0x22] = '\\\\\"';\nESCAPE_SEQUENCES[0x5C] = '\\\\\\\\';\nESCAPE_SEQUENCES[0x85] = '\\\\N';\nESCAPE_SEQUENCES[0xA0] = '\\\\_';\nESCAPE_SEQUENCES[0x2028] = '\\\\L';\nESCAPE_SEQUENCES[0x2029] = '\\\\P';\nvar DEPRECATED_BOOLEANS_SYNTAX = ['y', 'Y', 'yes', 'Yes', 'YES', 'on', 'On', 'ON', 'n', 'N', 'no', 'No', 'NO', 'off', 'Off', 'OFF'];\nvar DEPRECATED_BASE60_SYNTAX = /^[-+]?[0-9_]+(?::[0-9_]+)+(?:\\.[0-9_]*)?$/;\nfunction compileStyleMap(schema, map) {\n  var result, keys, index, length, tag, style, type;\n  if (map === null) return {};\n  result = {};\n  keys = Object.keys(map);\n  for (index = 0, length = keys.length; index < length; index += 1) {\n    tag = keys[index];\n    style = String(map[tag]);\n    if (tag.slice(0, 2) === '!!') {\n      tag = 'tag:yaml.org,2002:' + tag.slice(2);\n    }\n    type = schema.compiledTypeMap['fallback'][tag];\n    if (type && _hasOwnProperty.call(type.styleAliases, style)) {\n      style = type.styleAliases[style];\n    }\n    result[tag] = style;\n  }\n  return result;\n}\nfunction encodeHex(character) {\n  var string, handle, length;\n  string = character.toString(16).toUpperCase();\n  if (character <= 0xFF) {\n    handle = 'x';\n    length = 2;\n  } else if (character <= 0xFFFF) {\n    handle = 'u';\n    length = 4;\n  } else if (character <= 0xFFFFFFFF) {\n    handle = 'U';\n    length = 8;\n  } else {\n    throw new exception('code point within a string may not be greater than 0xFFFFFFFF');\n  }\n  return '\\\\' + handle + common.repeat('0', length - string.length) + string;\n}\nvar QUOTING_TYPE_SINGLE = 1,\n  QUOTING_TYPE_DOUBLE = 2;\nfunction State(options) {\n  this.schema = options['schema'] || _default;\n  this.indent = Math.max(1, options['indent'] || 2);\n  this.noArrayIndent = options['noArrayIndent'] || false;\n  this.skipInvalid = options['skipInvalid'] || false;\n  this.flowLevel = common.isNothing(options['flowLevel']) ? -1 : options['flowLevel'];\n  this.styleMap = compileStyleMap(this.schema, options['styles'] || null);\n  this.sortKeys = options['sortKeys'] || false;\n  this.lineWidth = options['lineWidth'] || 80;\n  this.noRefs = options['noRefs'] || false;\n  this.noCompatMode = options['noCompatMode'] || false;\n  this.condenseFlow = options['condenseFlow'] || false;\n  this.quotingType = options['quotingType'] === '\"' ? QUOTING_TYPE_DOUBLE : QUOTING_TYPE_SINGLE;\n  this.forceQuotes = options['forceQuotes'] || false;\n  this.replacer = typeof options['replacer'] === 'function' ? options['replacer'] : null;\n  this.implicitTypes = this.schema.compiledImplicit;\n  this.explicitTypes = this.schema.compiledExplicit;\n  this.tag = null;\n  this.result = '';\n  this.duplicates = [];\n  this.usedDuplicates = null;\n}\nfunction indentString(string, spaces) {\n  var ind = common.repeat(' ', spaces),\n    position = 0,\n    next = -1,\n    result = '',\n    line,\n    length = string.length;\n  while (position < length) {\n    next = string.indexOf('\\n', position);\n    if (next === -1) {\n      line = string.slice(position);\n      position = length;\n    } else {\n      line = string.slice(position, next + 1);\n      position = next + 1;\n    }\n    if (line.length && line !== '\\n') result += ind;\n    result += line;\n  }\n  return result;\n}\nfunction generateNextLine(state, level) {\n  return '\\n' + common.repeat(' ', state.indent * level);\n}\nfunction testImplicitResolving(state, str) {\n  var index, length, type;\n  for (index = 0, length = state.implicitTypes.length; index < length; index += 1) {\n    type = state.implicitTypes[index];\n    if (type.resolve(str)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction isWhitespace(c) {\n  return c === CHAR_SPACE || c === CHAR_TAB;\n}\nfunction isPrintable(c) {\n  return 0x00020 <= c && c <= 0x00007E || 0x000A1 <= c && c <= 0x00D7FF && c !== 0x2028 && c !== 0x2029 || 0x0E000 <= c && c <= 0x00FFFD && c !== CHAR_BOM || 0x10000 <= c && c <= 0x10FFFF;\n}\nfunction isNsCharOrWhitespace(c) {\n  return isPrintable(c) && c !== CHAR_BOM && c !== CHAR_CARRIAGE_RETURN && c !== CHAR_LINE_FEED;\n}\nfunction isPlainSafe(c, prev, inblock) {\n  var cIsNsCharOrWhitespace = isNsCharOrWhitespace(c);\n  var cIsNsChar = cIsNsCharOrWhitespace && !isWhitespace(c);\n  return (inblock ? cIsNsCharOrWhitespace : cIsNsCharOrWhitespace && c !== CHAR_COMMA && c !== CHAR_LEFT_SQUARE_BRACKET && c !== CHAR_RIGHT_SQUARE_BRACKET && c !== CHAR_LEFT_CURLY_BRACKET && c !== CHAR_RIGHT_CURLY_BRACKET) && c !== CHAR_SHARP && !(prev === CHAR_COLON && !cIsNsChar) || isNsCharOrWhitespace(prev) && !isWhitespace(prev) && c === CHAR_SHARP || prev === CHAR_COLON && cIsNsChar;\n}\nfunction isPlainSafeFirst(c) {\n  return isPrintable(c) && c !== CHAR_BOM && !isWhitespace(c) && c !== CHAR_MINUS && c !== CHAR_QUESTION && c !== CHAR_COLON && c !== CHAR_COMMA && c !== CHAR_LEFT_SQUARE_BRACKET && c !== CHAR_RIGHT_SQUARE_BRACKET && c !== CHAR_LEFT_CURLY_BRACKET && c !== CHAR_RIGHT_CURLY_BRACKET && c !== CHAR_SHARP && c !== CHAR_AMPERSAND && c !== CHAR_ASTERISK && c !== CHAR_EXCLAMATION && c !== CHAR_VERTICAL_LINE && c !== CHAR_EQUALS && c !== CHAR_GREATER_THAN && c !== CHAR_SINGLE_QUOTE && c !== CHAR_DOUBLE_QUOTE && c !== CHAR_PERCENT && c !== CHAR_COMMERCIAL_AT && c !== CHAR_GRAVE_ACCENT;\n}\nfunction isPlainSafeLast(c) {\n  return !isWhitespace(c) && c !== CHAR_COLON;\n}\nfunction codePointAt(string, pos) {\n  var first = string.charCodeAt(pos),\n    second;\n  if (first >= 0xD800 && first <= 0xDBFF && pos + 1 < string.length) {\n    second = string.charCodeAt(pos + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\nfunction needIndentIndicator(string) {\n  var leadingSpaceRe = /^\\n* /;\n  return leadingSpaceRe.test(string);\n}\nvar STYLE_PLAIN = 1,\n  STYLE_SINGLE = 2,\n  STYLE_LITERAL = 3,\n  STYLE_FOLDED = 4,\n  STYLE_DOUBLE = 5;\nfunction chooseScalarStyle(string, singleLineOnly, indentPerLevel, lineWidth, testAmbiguousType, quotingType, forceQuotes, inblock) {\n  var i;\n  var char = 0;\n  var prevChar = null;\n  var hasLineBreak = false;\n  var hasFoldableLine = false;\n  var shouldTrackWidth = lineWidth !== -1;\n  var previousLineBreak = -1;\n  var plain = isPlainSafeFirst(codePointAt(string, 0)) && isPlainSafeLast(codePointAt(string, string.length - 1));\n  if (singleLineOnly || forceQuotes) {\n    for (i = 0; i < string.length; char >= 0x10000 ? i += 2 : i++) {\n      char = codePointAt(string, i);\n      if (!isPrintable(char)) {\n        return STYLE_DOUBLE;\n      }\n      plain = plain && isPlainSafe(char, prevChar, inblock);\n      prevChar = char;\n    }\n  } else {\n    for (i = 0; i < string.length; char >= 0x10000 ? i += 2 : i++) {\n      char = codePointAt(string, i);\n      if (char === CHAR_LINE_FEED) {\n        hasLineBreak = true;\n        if (shouldTrackWidth) {\n          hasFoldableLine = hasFoldableLine || i - previousLineBreak - 1 > lineWidth && string[previousLineBreak + 1] !== ' ';\n          previousLineBreak = i;\n        }\n      } else if (!isPrintable(char)) {\n        return STYLE_DOUBLE;\n      }\n      plain = plain && isPlainSafe(char, prevChar, inblock);\n      prevChar = char;\n    }\n    hasFoldableLine = hasFoldableLine || shouldTrackWidth && i - previousLineBreak - 1 > lineWidth && string[previousLineBreak + 1] !== ' ';\n  }\n  if (!hasLineBreak && !hasFoldableLine) {\n    if (plain && !forceQuotes && !testAmbiguousType(string)) {\n      return STYLE_PLAIN;\n    }\n    return quotingType === QUOTING_TYPE_DOUBLE ? STYLE_DOUBLE : STYLE_SINGLE;\n  }\n  if (indentPerLevel > 9 && needIndentIndicator(string)) {\n    return STYLE_DOUBLE;\n  }\n  if (!forceQuotes) {\n    return hasFoldableLine ? STYLE_FOLDED : STYLE_LITERAL;\n  }\n  return quotingType === QUOTING_TYPE_DOUBLE ? STYLE_DOUBLE : STYLE_SINGLE;\n}\nfunction writeScalar(state, string, level, iskey, inblock) {\n  state.dump = function () {\n    if (string.length === 0) {\n      return state.quotingType === QUOTING_TYPE_DOUBLE ? '\"\"' : \"''\";\n    }\n    if (!state.noCompatMode) {\n      if (DEPRECATED_BOOLEANS_SYNTAX.indexOf(string) !== -1 || DEPRECATED_BASE60_SYNTAX.test(string)) {\n        return state.quotingType === QUOTING_TYPE_DOUBLE ? '\"' + string + '\"' : \"'\" + string + \"'\";\n      }\n    }\n    var indent = state.indent * Math.max(1, level);\n    var lineWidth = state.lineWidth === -1 ? -1 : Math.max(Math.min(state.lineWidth, 40), state.lineWidth - indent);\n    var singleLineOnly = iskey || state.flowLevel > -1 && level >= state.flowLevel;\n    function testAmbiguity(string) {\n      return testImplicitResolving(state, string);\n    }\n    switch (chooseScalarStyle(string, singleLineOnly, state.indent, lineWidth, testAmbiguity, state.quotingType, state.forceQuotes && !iskey, inblock)) {\n      case STYLE_PLAIN:\n        return string;\n      case STYLE_SINGLE:\n        return \"'\" + string.replace(/'/g, \"''\") + \"'\";\n      case STYLE_LITERAL:\n        return '|' + blockHeader(string, state.indent) + dropEndingNewline(indentString(string, indent));\n      case STYLE_FOLDED:\n        return '>' + blockHeader(string, state.indent) + dropEndingNewline(indentString(foldString(string, lineWidth), indent));\n      case STYLE_DOUBLE:\n        return '\"' + escapeString(string) + '\"';\n      default:\n        throw new exception('impossible error: invalid scalar style');\n    }\n  }();\n}\nfunction blockHeader(string, indentPerLevel) {\n  var indentIndicator = needIndentIndicator(string) ? String(indentPerLevel) : '';\n  var clip = string[string.length - 1] === '\\n';\n  var keep = clip && (string[string.length - 2] === '\\n' || string === '\\n');\n  var chomp = keep ? '+' : clip ? '' : '-';\n  return indentIndicator + chomp + '\\n';\n}\nfunction dropEndingNewline(string) {\n  return string[string.length - 1] === '\\n' ? string.slice(0, -1) : string;\n}\nfunction foldString(string, width) {\n  var lineRe = /(\\n+)([^\\n]*)/g;\n  var result = function () {\n    var nextLF = string.indexOf('\\n');\n    nextLF = nextLF !== -1 ? nextLF : string.length;\n    lineRe.lastIndex = nextLF;\n    return foldLine(string.slice(0, nextLF), width);\n  }();\n  var prevMoreIndented = string[0] === '\\n' || string[0] === ' ';\n  var moreIndented;\n  var match;\n  while (match = lineRe.exec(string)) {\n    var prefix = match[1],\n      line = match[2];\n    moreIndented = line[0] === ' ';\n    result += prefix + (!prevMoreIndented && !moreIndented && line !== '' ? '\\n' : '') + foldLine(line, width);\n    prevMoreIndented = moreIndented;\n  }\n  return result;\n}\nfunction foldLine(line, width) {\n  if (line === '' || line[0] === ' ') return line;\n  var breakRe = / [^ ]/g;\n  var match;\n  var start = 0,\n    end,\n    curr = 0,\n    next = 0;\n  var result = '';\n  while (match = breakRe.exec(line)) {\n    next = match.index;\n    if (next - start > width) {\n      end = curr > start ? curr : next;\n      result += '\\n' + line.slice(start, end);\n      start = end + 1;\n    }\n    curr = next;\n  }\n  result += '\\n';\n  if (line.length - start > width && curr > start) {\n    result += line.slice(start, curr) + '\\n' + line.slice(curr + 1);\n  } else {\n    result += line.slice(start);\n  }\n  return result.slice(1);\n}\nfunction escapeString(string) {\n  var result = '';\n  var char = 0;\n  var escapeSeq;\n  for (var i = 0; i < string.length; char >= 0x10000 ? i += 2 : i++) {\n    char = codePointAt(string, i);\n    escapeSeq = ESCAPE_SEQUENCES[char];\n    if (!escapeSeq && isPrintable(char)) {\n      result += string[i];\n      if (char >= 0x10000) result += string[i + 1];\n    } else {\n      result += escapeSeq || encodeHex(char);\n    }\n  }\n  return result;\n}\nfunction writeFlowSequence(state, level, object) {\n  var _result = '',\n    _tag = state.tag,\n    index,\n    length,\n    value;\n  for (index = 0, length = object.length; index < length; index += 1) {\n    value = object[index];\n    if (state.replacer) {\n      value = state.replacer.call(object, String(index), value);\n    }\n    if (writeNode(state, level, value, false, false) || typeof value === 'undefined' && writeNode(state, level, null, false, false)) {\n      if (_result !== '') _result += ',' + (!state.condenseFlow ? ' ' : '');\n      _result += state.dump;\n    }\n  }\n  state.tag = _tag;\n  state.dump = '[' + _result + ']';\n}\nfunction writeBlockSequence(state, level, object, compact) {\n  var _result = '',\n    _tag = state.tag,\n    index,\n    length,\n    value;\n  for (index = 0, length = object.length; index < length; index += 1) {\n    value = object[index];\n    if (state.replacer) {\n      value = state.replacer.call(object, String(index), value);\n    }\n    if (writeNode(state, level + 1, value, true, true, false, true) || typeof value === 'undefined' && writeNode(state, level + 1, null, true, true, false, true)) {\n      if (!compact || _result !== '') {\n        _result += generateNextLine(state, level);\n      }\n      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {\n        _result += '-';\n      } else {\n        _result += '- ';\n      }\n      _result += state.dump;\n    }\n  }\n  state.tag = _tag;\n  state.dump = _result || '[]';\n}\nfunction writeFlowMapping(state, level, object) {\n  var _result = '',\n    _tag = state.tag,\n    objectKeyList = Object.keys(object),\n    index,\n    length,\n    objectKey,\n    objectValue,\n    pairBuffer;\n  for (index = 0, length = objectKeyList.length; index < length; index += 1) {\n    pairBuffer = '';\n    if (_result !== '') pairBuffer += ', ';\n    if (state.condenseFlow) pairBuffer += '\"';\n    objectKey = objectKeyList[index];\n    objectValue = object[objectKey];\n    if (state.replacer) {\n      objectValue = state.replacer.call(object, objectKey, objectValue);\n    }\n    if (!writeNode(state, level, objectKey, false, false)) {\n      continue;\n    }\n    if (state.dump.length > 1024) pairBuffer += '? ';\n    pairBuffer += state.dump + (state.condenseFlow ? '\"' : '') + ':' + (state.condenseFlow ? '' : ' ');\n    if (!writeNode(state, level, objectValue, false, false)) {\n      continue;\n    }\n    pairBuffer += state.dump;\n    _result += pairBuffer;\n  }\n  state.tag = _tag;\n  state.dump = '{' + _result + '}';\n}\nfunction writeBlockMapping(state, level, object, compact) {\n  var _result = '',\n    _tag = state.tag,\n    objectKeyList = Object.keys(object),\n    index,\n    length,\n    objectKey,\n    objectValue,\n    explicitPair,\n    pairBuffer;\n  if (state.sortKeys === true) {\n    objectKeyList.sort();\n  } else if (typeof state.sortKeys === 'function') {\n    objectKeyList.sort(state.sortKeys);\n  } else if (state.sortKeys) {\n    throw new exception('sortKeys must be a boolean or a function');\n  }\n  for (index = 0, length = objectKeyList.length; index < length; index += 1) {\n    pairBuffer = '';\n    if (!compact || _result !== '') {\n      pairBuffer += generateNextLine(state, level);\n    }\n    objectKey = objectKeyList[index];\n    objectValue = object[objectKey];\n    if (state.replacer) {\n      objectValue = state.replacer.call(object, objectKey, objectValue);\n    }\n    if (!writeNode(state, level + 1, objectKey, true, true, true)) {\n      continue;\n    }\n    explicitPair = state.tag !== null && state.tag !== '?' || state.dump && state.dump.length > 1024;\n    if (explicitPair) {\n      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {\n        pairBuffer += '?';\n      } else {\n        pairBuffer += '? ';\n      }\n    }\n    pairBuffer += state.dump;\n    if (explicitPair) {\n      pairBuffer += generateNextLine(state, level);\n    }\n    if (!writeNode(state, level + 1, objectValue, true, explicitPair)) {\n      continue;\n    }\n    if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {\n      pairBuffer += ':';\n    } else {\n      pairBuffer += ': ';\n    }\n    pairBuffer += state.dump;\n    _result += pairBuffer;\n  }\n  state.tag = _tag;\n  state.dump = _result || '{}';\n}\nfunction detectType(state, object, explicit) {\n  var _result, typeList, index, length, type, style;\n  typeList = explicit ? state.explicitTypes : state.implicitTypes;\n  for (index = 0, length = typeList.length; index < length; index += 1) {\n    type = typeList[index];\n    if ((type.instanceOf || type.predicate) && (!type.instanceOf || _typeof(object) === 'object' && object instanceof type.instanceOf) && (!type.predicate || type.predicate(object))) {\n      if (explicit) {\n        if (type.multi && type.representName) {\n          state.tag = type.representName(object);\n        } else {\n          state.tag = type.tag;\n        }\n      } else {\n        state.tag = '?';\n      }\n      if (type.represent) {\n        style = state.styleMap[type.tag] || type.defaultStyle;\n        if (_toString.call(type.represent) === '[object Function]') {\n          _result = type.represent(object, style);\n        } else if (_hasOwnProperty.call(type.represent, style)) {\n          _result = type.represent[style](object, style);\n        } else {\n          throw new exception('!<' + type.tag + '> tag resolver accepts not \"' + style + '\" style');\n        }\n        state.dump = _result;\n      }\n      return true;\n    }\n  }\n  return false;\n}\nfunction writeNode(state, level, object, block, compact, iskey, isblockseq) {\n  state.tag = null;\n  state.dump = object;\n  if (!detectType(state, object, false)) {\n    detectType(state, object, true);\n  }\n  var type = _toString.call(state.dump);\n  var inblock = block;\n  var tagStr;\n  if (block) {\n    block = state.flowLevel < 0 || state.flowLevel > level;\n  }\n  var objectOrArray = type === '[object Object]' || type === '[object Array]',\n    duplicateIndex,\n    duplicate;\n  if (objectOrArray) {\n    duplicateIndex = state.duplicates.indexOf(object);\n    duplicate = duplicateIndex !== -1;\n  }\n  if (state.tag !== null && state.tag !== '?' || duplicate || state.indent !== 2 && level > 0) {\n    compact = false;\n  }\n  if (duplicate && state.usedDuplicates[duplicateIndex]) {\n    state.dump = '*ref_' + duplicateIndex;\n  } else {\n    if (objectOrArray && duplicate && !state.usedDuplicates[duplicateIndex]) {\n      state.usedDuplicates[duplicateIndex] = true;\n    }\n    if (type === '[object Object]') {\n      if (block && Object.keys(state.dump).length !== 0) {\n        writeBlockMapping(state, level, state.dump, compact);\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + state.dump;\n        }\n      } else {\n        writeFlowMapping(state, level, state.dump);\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + ' ' + state.dump;\n        }\n      }\n    } else if (type === '[object Array]') {\n      if (block && state.dump.length !== 0) {\n        if (state.noArrayIndent && !isblockseq && level > 0) {\n          writeBlockSequence(state, level - 1, state.dump, compact);\n        } else {\n          writeBlockSequence(state, level, state.dump, compact);\n        }\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + state.dump;\n        }\n      } else {\n        writeFlowSequence(state, level, state.dump);\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + ' ' + state.dump;\n        }\n      }\n    } else if (type === '[object String]') {\n      if (state.tag !== '?') {\n        writeScalar(state, state.dump, level, iskey, inblock);\n      }\n    } else if (type === '[object Undefined]') {\n      return false;\n    } else {\n      if (state.skipInvalid) return false;\n      throw new exception('unacceptable kind of an object to dump ' + type);\n    }\n    if (state.tag !== null && state.tag !== '?') {\n      tagStr = encodeURI(state.tag[0] === '!' ? state.tag.slice(1) : state.tag).replace(/!/g, '%21');\n      if (state.tag[0] === '!') {\n        tagStr = '!' + tagStr;\n      } else if (tagStr.slice(0, 18) === 'tag:yaml.org,2002:') {\n        tagStr = '!!' + tagStr.slice(18);\n      } else {\n        tagStr = '!<' + tagStr + '>';\n      }\n      state.dump = tagStr + ' ' + state.dump;\n    }\n  }\n  return true;\n}\nfunction getDuplicateReferences(object, state) {\n  var objects = [],\n    duplicatesIndexes = [],\n    index,\n    length;\n  inspectNode(object, objects, duplicatesIndexes);\n  for (index = 0, length = duplicatesIndexes.length; index < length; index += 1) {\n    state.duplicates.push(objects[duplicatesIndexes[index]]);\n  }\n  state.usedDuplicates = new Array(length);\n}\nfunction inspectNode(object, objects, duplicatesIndexes) {\n  var objectKeyList, index, length;\n  if (object !== null && _typeof(object) === 'object') {\n    index = objects.indexOf(object);\n    if (index !== -1) {\n      if (duplicatesIndexes.indexOf(index) === -1) {\n        duplicatesIndexes.push(index);\n      }\n    } else {\n      objects.push(object);\n      if (Array.isArray(object)) {\n        for (index = 0, length = object.length; index < length; index += 1) {\n          inspectNode(object[index], objects, duplicatesIndexes);\n        }\n      } else {\n        objectKeyList = Object.keys(object);\n        for (index = 0, length = objectKeyList.length; index < length; index += 1) {\n          inspectNode(object[objectKeyList[index]], objects, duplicatesIndexes);\n        }\n      }\n    }\n  }\n}\nfunction dump$1(input, options) {\n  options = options || {};\n  var state = new State(options);\n  if (!state.noRefs) getDuplicateReferences(input, state);\n  var value = input;\n  if (state.replacer) {\n    value = state.replacer.call({\n      '': value\n    }, '', value);\n  }\n  if (writeNode(state, 0, value, true, true)) return state.dump + '\\n';\n  return '';\n}\nvar dump_1 = dump$1;\nvar dumper = {\n  dump: dump_1\n};\nfunction renamed(from, to) {\n  return function () {\n    throw new Error('Function yaml.' + from + ' is removed in js-yaml 4. ' + 'Use yaml.' + to + ' instead, which is now safe by default.');\n  };\n}\nvar Type = type;\nvar Schema = schema;\nvar FAILSAFE_SCHEMA = failsafe;\nvar JSON_SCHEMA = json;\nvar CORE_SCHEMA = core;\nvar DEFAULT_SCHEMA = _default;\nvar load = loader.load;\nvar loadAll = loader.loadAll;\nvar dump = dumper.dump;\nvar YAMLException = exception;\nvar types = {\n  binary: binary,\n  float: float,\n  map: map,\n  null: _null,\n  pairs: pairs,\n  set: set,\n  timestamp: timestamp,\n  bool: bool,\n  int: int,\n  merge: merge,\n  omap: omap,\n  seq: seq,\n  str: str\n};\nvar safeLoad = renamed('safeLoad', 'load');\nvar safeLoadAll = renamed('safeLoadAll', 'loadAll');\nvar safeDump = renamed('safeDump', 'dump');\nvar jsYaml = {\n  Type: Type,\n  Schema: Schema,\n  FAILSAFE_SCHEMA: FAILSAFE_SCHEMA,\n  JSON_SCHEMA: JSON_SCHEMA,\n  CORE_SCHEMA: CORE_SCHEMA,\n  DEFAULT_SCHEMA: DEFAULT_SCHEMA,\n  load: load,\n  loadAll: loadAll,\n  dump: dump,\n  YAMLException: YAMLException,\n  types: types,\n  safeLoad: safeLoad,\n  safeLoadAll: safeLoadAll,\n  safeDump: safeDump\n};\nexport default jsYaml;\nexport { CORE_SCHEMA, DEFAULT_SCHEMA, FAILSAFE_SCHEMA, JSON_SCHEMA, Schema, Type, YAMLException, dump, load, loadAll, safeDump, safeLoad, safeLoadAll, types };", "export default (function (filename) {\n  if (filename.indexOf('.') < 0) return undefined;\n  return \".\".concat(filename.split('.').pop());\n});", "import JSON5 from './formats/json5.js';\nimport { parse as parseJ<PERSON><PERSON> } from './formats/jsonc.js';\nimport jsYaml from './formats/yaml.js';\nimport extname from './extname.js';\nvar isDeno = typeof Deno !== 'undefined';\nvar isBun = typeof Bun !== 'undefined';\nvar YAML = typeof jsYaml !== 'undefined' && jsYaml.load ? jsYaml : undefined;\nvar fs = !isDeno ? (await import('node:fs')).default : undefined;\nvar evalAlias = eval;\nvar readFileInNodeSync = function readFileInNodeSync(filename) {\n  var data = fs.readFileSync(filename, 'utf8');\n  var stat;\n  try {\n    stat = fs.statSync(filename);\n  } catch (e) {}\n  return {\n    data: data,\n    stat: stat\n  };\n};\nvar readFileInNode = function readFileInNode(filename) {\n  return new Promise(function (resolve, reject) {\n    fs.readFile(filename, 'utf8', function (err, data) {\n      if (err) return reject(err);\n      fs.stat(filename, function (err, stat) {\n        if (err) return resolve({\n          data: data\n        });\n        return resolve({\n          data: data,\n          stat: stat\n        });\n      });\n    });\n  });\n};\nvar readFileInDenoSync = function readFileInDenoSync(filename) {\n  var decoder = new TextDecoder('utf-8');\n  var d = Deno.readFileSync(filename);\n  var data = decoder.decode(d);\n  var stat;\n  try {\n    stat = Deno.statSync(filename);\n  } catch (e) {}\n  return {\n    data: data,\n    stat: stat\n  };\n};\nvar readFileInDeno = function readFileInDeno(filename) {\n  return new Promise(function (resolve, reject) {\n    var decoder = new TextDecoder('utf-8');\n    Deno.readFile(filename).then(function (d) {\n      var data = decoder.decode(d);\n      Deno.stat(filename).then(function (stat) {\n        return resolve({\n          data: data,\n          stat: stat\n        });\n      }).catch(function () {\n        return resolve({\n          data: data\n        });\n      });\n    }).catch(reject);\n  });\n};\nvar readFileInBunSync = readFileInNodeSync;\nvar readFileInBun = readFileInNode;\nvar replaceLast = function replaceLast(str, find, replace) {\n  var index = str.lastIndexOf(find);\n  if (index > -1) {\n    return str.substring(0, index) + replace + str.substring(index + find.length);\n  }\n  return str.toString();\n};\nvar parseData = function parseData(extension, data, options) {\n  data = data.replace(/^\\uFEFF/, '');\n  var result = {};\n  switch (extension) {\n    case '.js':\n    case '.ts':\n      if (typeof module === 'undefined') {\n        if (data.indexOf('exports') > -1) {\n          data = \"(\".concat(replaceLast(data.substring(data.indexOf('=') + 1), '};', ''), \")\");\n        } else if (data.indexOf('export default ') > -1) {\n          data = \"(\".concat(replaceLast(data.substring(data.indexOf('export default ') + 15), '};', ''), \")\");\n        }\n      }\n      result = evalAlias(data);\n      break;\n    case '.json5':\n      result = JSON5.parse(data);\n      break;\n    case '.jsonc':\n      result = parseJSONC(data);\n      break;\n    case '.yml':\n    case '.yaml':\n      result = YAML.load(data);\n      break;\n    default:\n      result = options.parse(data);\n  }\n  return result;\n};\nexport function readFileSync(filename, options) {\n  var ext = extname(filename);\n  var data, stat;\n  if (isBun) {\n    var ret = readFileInBunSync(filename);\n    data = ret.data;\n    stat = ret.stat;\n  } else if (isDeno) {\n    var _ret = readFileInDenoSync(filename);\n    data = _ret.data;\n    stat = _ret.stat;\n  } else {\n    var _ret2 = readFileInNodeSync(filename);\n    data = _ret2.data;\n    stat = _ret2.stat;\n  }\n  return {\n    data: parseData(ext, data, options),\n    stat: stat\n  };\n}\nexport function readFile(filename) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    parse: JSON.parse\n  };\n  var ext = extname(filename);\n  var fn = isBun ? readFileInBun : isDeno ? readFileInDeno : readFileInNode;\n  return new Promise(function (resolve, reject) {\n    fn(filename).then(function (_ref) {\n      var data = _ref.data,\n        stat = _ref.stat;\n      try {\n        var ret = parseData(ext, data, options);\n        resolve({\n          data: ret,\n          stat: stat\n        });\n      } catch (err) {\n        err.message = 'error parsing ' + filename + ': ' + err.message;\n        reject(err);\n      }\n    }).catch(reject);\n  });\n}", "import JSON5 from './formats/json5.js';\nimport jsYaml from './formats/yaml.js';\nimport extname from './extname.js';\nvar isDeno = typeof Deno !== 'undefined';\nvar isBun = typeof Bun !== 'undefined';\nvar YAML = typeof jsYaml !== 'undefined' && jsYaml.load ? jsYaml : undefined;\nvar fs = !isDeno ? (await import('node:fs')).default : undefined;\nfunction dirname(path) {\n  if (path.length === 0) return '.';\n  var code = path.charCodeAt(0);\n  var hasRoot = code === 47;\n  var end = -1;\n  var matchedSlash = true;\n  for (var i = path.length - 1; i >= 1; --i) {\n    code = path.charCodeAt(i);\n    if (code === 47) {\n      if (!matchedSlash) {\n        end = i;\n        break;\n      }\n    } else {\n      matchedSlash = false;\n    }\n  }\n  if (end === -1) return hasRoot ? '/' : '.';\n  if (hasRoot && end === 1) return '//';\n  return path.slice(0, end);\n}\nvar writeFileInNodeSync = function writeFileInNodeSync(filename, payload) {\n  try {\n    fs.mkdirSync(dirname(filename), {\n      recursive: true\n    });\n  } catch (err) {}\n  return fs.writeFileSync(filename, payload, 'utf8');\n};\nvar writeFileInNode = function writeFileInNode(filename, payload) {\n  return new Promise(function (resolve, reject) {\n    fs.mkdir(dirname(filename), {\n      recursive: true\n    }, function () {\n      fs.writeFile(filename, payload, 'utf8', function (err, data) {\n        return err ? reject(err) : resolve(data);\n      });\n    });\n  });\n};\nvar removeFileInNodeSync = function removeFileInNodeSync(filename) {\n  return fs.unlinkSync(filename);\n};\nvar removeFileInNode = function removeFileInNode(filename) {\n  return new Promise(function (resolve, reject) {\n    return fs.unlink(filename, function (err) {\n      return err ? reject(err) : resolve();\n    });\n  });\n};\nvar writeFileInDenoSync = function writeFileInDenoSync(filename, payload) {\n  var encoder = new TextEncoder();\n  var data = encoder.encode(payload);\n  try {\n    Deno.mkdirSync(dirname(filename), {\n      recursive: true\n    });\n  } catch (err) {}\n  Deno.writeFileSync(filename, data);\n};\nvar writeFileInDeno = function writeFileInDeno(filename, payload) {\n  var encoder = new TextEncoder();\n  var data = encoder.encode(payload);\n  return new Promise(function (resolve, reject) {\n    Deno.mkdir(dirname(filename), {\n      recursive: true\n    }).then(function () {\n      Deno.writeFile(filename, data).then(resolve, reject);\n    }).catch(function () {\n      Deno.writeFile(filename, data).then(resolve, reject);\n    });\n  });\n};\nvar removeFileInDenoSync = function removeFileInDenoSync(filename) {\n  Deno.removeSync(filename);\n};\nvar removeFileInDeno = function removeFileInDeno(filename) {\n  return Deno.remove(filename);\n};\nvar writeFileInBunSync = writeFileInNodeSync;\nvar writeFileInBun = writeFileInNode;\nvar removeFileInBunSync = removeFileInNodeSync;\nvar removeFileInBun = removeFileInNode;\nvar stringifyData = function stringifyData(extension, data, options) {\n  var result = '';\n  switch (extension) {\n    case '.js':\n    case '.ts':\n      if (typeof module === 'undefined') {\n        result = \"export default \".concat(options.stringify(data, null, options.ident));\n      } else {\n        result = \"module.exports = \".concat(options.stringify(data, null, options.ident));\n      }\n      break;\n    case '.json5':\n      result = JSON5.stringify(data, null, options.ident);\n      break;\n    case '.yml':\n    case '.yaml':\n      result = YAML.dump(data, {\n        ident: options.indent\n      });\n      break;\n    default:\n      result = options.stringify(data, null, options.ident);\n  }\n  return result;\n};\nexport function writeFileSync(filename, payload, options) {\n  var ext = extname(filename);\n  var data;\n  try {\n    data = stringifyData(ext, payload, options);\n  } catch (err) {\n    err.message = 'error stringifying ' + filename + ': ' + err.message;\n    throw err;\n  }\n  if (isBun) {\n    return writeFileInBunSync(filename, data);\n  } else if (isDeno) {\n    return writeFileInDenoSync(filename, data);\n  } else {\n    return writeFileInNodeSync(filename, data);\n  }\n}\nexport function writeFile(filename, payload) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    stringify: JSON.stringify,\n    ident: 2\n  };\n  var ext = extname(filename);\n  var data;\n  try {\n    data = stringifyData(ext, payload, options);\n  } catch (err) {\n    err.message = 'error stringifying ' + filename + ': ' + err.message;\n    throw err;\n  }\n  var fn = isBun ? writeFileInBun : isDeno ? writeFileInDeno : writeFileInNode;\n  return fn(filename, data);\n}\nexport function removeFileSync(filename) {\n  if (isBun) {\n    return removeFileInBunSync(filename);\n  } else if (isDeno) {\n    return removeFileInDenoSync(filename);\n  } else {\n    return removeFileInNodeSync(filename);\n  }\n}\nexport function removeFile(filename) {\n  var fn = isBun ? removeFileInBun : isDeno ? removeFileInDeno : removeFileInNode;\n  return fn(filename);\n}", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defaults, debounce, getPath, setPath, pushPath } from './utils.js';\nimport { readFile, readFileSync } from './readFile.js';\nimport { writeFile as _writeFile, removeFile as _removeFile } from './writeFile.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/{{lng}}/{{ns}}.missing.json',\n    ident: 2,\n    parse: JSON.parse,\n    stringify: JSON.stringify\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = defaults(options, this.options || {}, getDefaults());\n      this.allOptions = allOptions;\n      this.queuedWrites = {};\n      this.debouncedWrite = debounce(this.write, 250);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      var _this = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(language, namespace);\n      }\n      var filename = this.services.interpolator.interpolate(loadPath, {\n        lng: language,\n        ns: namespace\n      });\n      if (this.allOptions.initAsync === false || this.allOptions.initImmediate === false) {\n        try {\n          var _readFileSync = readFileSync(filename, this.options),\n            data = _readFileSync.data,\n            stat = _readFileSync.stat;\n          var timestamp = stat && stat.mtime && stat.mtime.getTime();\n          if (this.options.expirationTime && timestamp && timestamp + this.options.expirationTime < Date.now()) {\n            this.removeFile(language, namespace);\n            return callback(new Error('File expired!'), false);\n          }\n          callback(null, data, timestamp);\n        } catch (err) {\n          callback(err, false);\n        }\n        return;\n      }\n      readFile(filename, this.options).then(function (_ref) {\n        var data = _ref.data,\n          stat = _ref.stat;\n        var timestamp = stat && stat.mtime && stat.mtime.getTime();\n        if (_this.options.expirationTime && timestamp && timestamp + _this.options.expirationTime < Date.now()) {\n          _this.removeFile(language, namespace);\n          return callback(new Error('File expired!'), false);\n        }\n        callback(null, data, timestamp);\n      }).catch(function (err) {\n        return callback(err, false);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this2 = this;\n      if (typeof callback !== 'function') callback = function callback() {};\n      if (typeof languages === 'string') languages = [languages];\n      var todo = languages.length;\n      var done = function done() {\n        if (! --todo) callback();\n      };\n      languages.forEach(function (lng) {\n        _this2.queue.call(_this2, lng, namespace, key, fallbackValue, done);\n      });\n    }\n  }, {\n    key: \"save\",\n    value: function save(language, namespace, data, callback) {\n      var _this3 = this;\n      if (!callback) callback = function callback() {};\n      var keys = Object.keys(data);\n      var todo = keys.length;\n      var done = function done() {\n        if (! --todo) callback();\n      };\n      keys.forEach(function (key) {\n        _this3.queue.call(_this3, language, namespace, key, data[key], done);\n      });\n    }\n  }, {\n    key: \"removeFile\",\n    value: function removeFile(language, namespace) {\n      var addPath = this.options.addPath;\n      if (typeof this.options.addPath === 'function') {\n        addPath = this.options.addPath(language, namespace);\n      }\n      var filename = this.services.interpolator.interpolate(addPath, {\n        lng: language,\n        ns: namespace\n      });\n      _removeFile(filename, this.options).then(function () {}).catch(function () {});\n    }\n  }, {\n    key: \"write\",\n    value: function write() {\n      for (var lng in this.queuedWrites) {\n        var namespaces = this.queuedWrites[lng];\n        if (lng !== 'locks') {\n          for (var ns in namespaces) {\n            this.writeFile(lng, ns);\n          }\n        }\n      }\n    }\n  }, {\n    key: \"writeFile\",\n    value: function writeFile(lng, namespace) {\n      var _this4 = this;\n      var lock = getPath(this.queuedWrites, ['locks', lng, namespace]);\n      if (lock) return;\n      var addPath = this.options.addPath;\n      if (typeof this.options.addPath === 'function') {\n        addPath = this.options.addPath(lng, namespace);\n      }\n      var filename = this.services.interpolator.interpolate(addPath, {\n        lng: lng,\n        ns: namespace\n      });\n      var missings = getPath(this.queuedWrites, [lng, namespace]);\n      setPath(this.queuedWrites, [lng, namespace], []);\n      if (missings.length) {\n        setPath(this.queuedWrites, ['locks', lng, namespace], true);\n        var proceed = function proceed(_ref2) {\n          var data = _ref2.data;\n          missings.forEach(function (missing) {\n            var path = _this4.allOptions.keySeparator === false ? [missing.key] : missing.key.split(_this4.allOptions.keySeparator || '.');\n            try {\n              setPath(data, path, missing.fallbackValue);\n            } catch (e) {\n              if (path.length < 2 || !e.message || e.message.indexOf('Cannot create property') < 0) throw e;\n              setPath(data, [missing.key], missing.fallbackValue);\n            }\n          });\n          var proceedWrite = function proceedWrite() {\n            setPath(_this4.queuedWrites, ['locks', lng, namespace], false);\n            missings.forEach(function (missing) {\n              if (missing.callback) missing.callback();\n            });\n            _this4.debouncedWrite();\n          };\n          _writeFile(filename, data, _this4.options).then(proceedWrite).catch(proceedWrite);\n        };\n        readFile(filename, this.options).then(proceed).catch(function () {\n          return proceed({\n            data: {}\n          });\n        });\n      }\n    }\n  }, {\n    key: \"queue\",\n    value: function queue(lng, namespace, key, fallbackValue, callback) {\n      pushPath(this.queuedWrites, [lng, namespace], {\n        key: key,\n        fallbackValue: fallbackValue || '',\n        callback: callback\n      });\n      this.debouncedWrite();\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;"], "mappings": ";;;AAAA,IAAI,MAAM,CAAC;AACX,IAAI,OAAO,IAAI;AACf,IAAI,QAAQ,IAAI;AACT,SAAS,SAAS,KAAK;AAC5B,OAAK,KAAK,MAAM,KAAK,WAAW,CAAC,GAAG,SAAUA,SAAQ;AACpD,QAAIA,SAAQ;AACV,eAAS,QAAQA,SAAQ;AACvB,YAAI,IAAI,IAAI,MAAM,OAAW,KAAI,IAAI,IAAIA,QAAO,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACO,SAAS,SAAS,MAAM,MAAM,WAAW;AAC9C,MAAI;AACJ,SAAO,WAAY;AACjB,QAAI,UAAU;AACd,QAAI,OAAO;AACX,QAAI,QAAQ,SAASC,SAAQ;AAC3B,gBAAU;AACV,UAAI,CAAC,UAAW,MAAK,MAAM,SAAS,IAAI;AAAA,IAC1C;AACA,QAAI,UAAU,aAAa,CAAC;AAC5B,iBAAa,OAAO;AACpB,cAAU,WAAW,OAAO,IAAI;AAChC,QAAI,QAAS,MAAK,MAAM,SAAS,IAAI;AAAA,EACvC;AACF;AACA,SAAS,cAAc,QAAQ,MAAM,OAAO;AAC1C,WAAS,SAASC,MAAK;AACrB,WAAOA,QAAOA,KAAI,QAAQ,KAAK,IAAI,KAAKA,KAAI,QAAQ,QAAQ,GAAG,IAAIA;AAAA,EACrE;AACA,MAAIC,SAAQ,OAAO,SAAS,WAAW,CAAC,EAAE,OAAO,IAAI,IAAI,KAAK,MAAM,GAAG;AACvE,SAAOA,OAAM,SAAS,GAAG;AACvB,QAAI,CAAC,OAAQ,QAAO,CAAC;AACrB,QAAID,OAAM,SAASC,OAAM,MAAM,CAAC;AAChC,QAAI,CAAC,OAAOD,IAAG,KAAK,MAAO,QAAOA,IAAG,IAAI,IAAI,MAAM;AACnD,aAAS,OAAOA,IAAG;AAAA,EACrB;AACA,MAAI,CAAC,OAAQ,QAAO,CAAC;AACrB,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAG,SAASC,OAAM,MAAM,CAAC;AAAA,EAC3B;AACF;AACO,SAAS,QAAQ,QAAQ,MAAM,UAAU;AAC9C,MAAI,iBAAiB,cAAc,QAAQ,MAAM,MAAM,GACrD,MAAM,eAAe,KACrB,IAAI,eAAe;AACrB,MAAI,MAAM,QAAQ,GAAG,KAAK,MAAM,CAAC,EAAG,OAAM,IAAI,MAAM,2BAA4B,OAAO,GAAG,iCAAkC,CAAC;AAC7H,MAAI,CAAC,IAAI;AACX;AACO,SAAS,SAAS,QAAQ,MAAM,UAAU,QAAQ;AACvD,MAAI,kBAAkB,cAAc,QAAQ,MAAM,MAAM,GACtD,MAAM,gBAAgB,KACtB,IAAI,gBAAgB;AACtB,MAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;AACpB,MAAI,OAAQ,KAAI,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,QAAQ;AAC3C,MAAI,CAAC,OAAQ,KAAI,CAAC,EAAE,KAAK,QAAQ;AACnC;AACO,SAAS,QAAQ,QAAQ,MAAM;AACpC,MAAI,kBAAkB,cAAc,QAAQ,IAAI,GAC9C,MAAM,gBAAgB,KACtB,IAAI,gBAAgB;AACtB,MAAI,CAAC,IAAK,QAAO;AACjB,SAAO,IAAI,CAAC;AACd;;;AClEA,SAAS,2BAA2B,GAAG,GAAG;AAAE,MAAI,IAAI,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,MAAI,CAAC,GAAG;AAAE,QAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,KAAK,KAAK,YAAY,OAAO,EAAE,QAAQ;AAAE,YAAM,IAAI;AAAI,UAAI,KAAK,GAAG,IAAI,SAASC,KAAI;AAAA,MAAC;AAAG,aAAO,EAAE,GAAG,GAAG,GAAG,SAAS,IAAI;AAAE,eAAO,MAAM,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,IAAI,EAAE;AAAA,MAAG,GAAG,GAAG,SAASC,GAAEC,IAAG;AAAE,cAAMA;AAAA,MAAG,GAAG,GAAG,EAAE;AAAA,IAAG;AAAE,UAAM,IAAI,UAAU,uIAAuI;AAAA,EAAG;AAAE,MAAI,GAAG,IAAI,MAAI,IAAI;AAAI,SAAO,EAAE,GAAG,SAAS,IAAI;AAAE,QAAI,EAAE,KAAK,CAAC;AAAA,EAAG,GAAG,GAAG,SAAS,IAAI;AAAE,QAAIA,KAAI,EAAE,KAAK;AAAG,WAAO,IAAIA,GAAE,MAAMA;AAAA,EAAG,GAAG,GAAG,SAASD,GAAEC,IAAG;AAAE,QAAI,MAAI,IAAIA;AAAA,EAAG,GAAG,GAAG,SAAS,IAAI;AAAE,QAAI;AAAE,WAAK,QAAQ,EAAE,UAAU,EAAE,OAAO;AAAA,IAAG,UAAE;AAAU,UAAI,EAAG,OAAM;AAAA,IAAG;AAAA,EAAE,EAAE;AAAG;AACr1B,SAAS,4BAA4B,GAAG,GAAG;AAAE,MAAI,GAAG;AAAE,QAAI,YAAY,OAAO,EAAG,QAAO,kBAAkB,GAAG,CAAC;AAAG,QAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,WAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,EAAQ;AAAE;AACzX,SAAS,kBAAkB,GAAG,GAAG;AAAE,GAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AAAS,WAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AAAG,SAAO;AAAG;AACnJ,SAAS,QAAQ,GAAG;AAAE;AAA2B,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAG,QAAQ,CAAC;AAAG;AAC7T,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,OAAO;AAAA,EACT,kBAAkB,SAAS,iBAAiBC,IAAG;AAC7C,WAAO,OAAOA,OAAM,YAAY,QAAQ,gBAAgB,KAAKA,EAAC;AAAA,EAChE;AAAA,EACA,eAAe,SAAS,cAAcA,IAAG;AACvC,WAAO,OAAOA,OAAM,aAAaA,MAAK,OAAOA,MAAK,OAAOA,MAAK,OAAOA,MAAK,OAAOA,OAAM,OAAOA,OAAM,OAAO,QAAQ,SAAS,KAAKA,EAAC;AAAA,EACpI;AAAA,EACA,kBAAkB,SAAS,iBAAiBA,IAAG;AAC7C,WAAO,OAAOA,OAAM,aAAaA,MAAK,OAAOA,MAAK,OAAOA,MAAK,OAAOA,MAAK,OAAOA,MAAK,OAAOA,MAAK,OAAOA,OAAM,OAAOA,OAAM,OAAOA,OAAM,OAAYA,OAAM,OAAY,QAAQ,YAAY,KAAKA,EAAC;AAAA,EACnM;AAAA,EACA,SAAS,SAAS,QAAQA,IAAG;AAC3B,WAAO,OAAOA,OAAM,YAAY,QAAQ,KAAKA,EAAC;AAAA,EAChD;AAAA,EACA,YAAY,SAAS,WAAWA,IAAG;AACjC,WAAO,OAAOA,OAAM,YAAY,cAAc,KAAKA,EAAC;AAAA,EACtD;AACF;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,QAAQ,SAASC,OAAM,MAAM,SAAS;AACxC,WAAS,OAAO,IAAI;AACpB,eAAa;AACb,UAAQ,CAAC;AACT,QAAM;AACN,SAAO;AACP,WAAS;AACT,UAAQ;AACR,QAAM;AACN,SAAO;AACP,KAAG;AACD,YAAQ,IAAI;AACZ,gBAAY,UAAU,EAAE;AAAA,EAC1B,SAAS,MAAM,SAAS;AACxB,MAAI,OAAO,YAAY,YAAY;AACjC,WAAO,YAAY;AAAA,MACjB,IAAI;AAAA,IACN,GAAG,IAAI,OAAO;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,YAAY,QAAQ,MAAM,SAAS;AAC1C,MAAIC,SAAQ,OAAO,IAAI;AACvB,MAAIA,UAAS,QAAQ,QAAQA,MAAK,MAAM,UAAU;AAChD,QAAI,MAAM,QAAQA,MAAK,GAAG;AACxB,eAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,YAAI,OAAO,OAAO,CAAC;AACnB,YAAI,cAAc,YAAYA,QAAO,MAAM,OAAO;AAClD,YAAI,gBAAgB,QAAW;AAC7B,iBAAOA,OAAM,IAAI;AAAA,QACnB,OAAO;AACL,iBAAO,eAAeA,QAAO,MAAM;AAAA,YACjC,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,OAAO;AACL,eAAS,SAASA,QAAO;AACvB,YAAI,eAAe,YAAYA,QAAO,OAAO,OAAO;AACpD,YAAI,iBAAiB,QAAW;AAC9B,iBAAOA,OAAM,KAAK;AAAA,QACpB,OAAO;AACL,iBAAO,eAAeA,QAAO,OAAO;AAAA,YAClC,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,QAAQ,KAAK,QAAQ,MAAMA,MAAK;AACzC;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,MAAM;AACb,aAAW;AACX,WAAS;AACT,gBAAc;AACd,UAAQ;AACR,aAAS;AACP,QAAI,KAAK;AACT,QAAI,SAAS,UAAU,QAAQ,EAAE;AACjC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,OAAO;AACd,MAAI,OAAO,GAAG,GAAG;AACf,WAAO,OAAO,cAAc,OAAO,YAAY,GAAG,CAAC;AAAA,EACrD;AACF;AACA,SAAS,OAAO;AACd,MAAIF,KAAI,KAAK;AACb,MAAIA,OAAM,MAAM;AACd;AACA,aAAS;AAAA,EACX,WAAWA,IAAG;AACZ,cAAUA,GAAE;AAAA,EACd,OAAO;AACL;AAAA,EACF;AACA,MAAIA,IAAG;AACL,WAAOA,GAAE;AAAA,EACX;AACA,SAAOA;AACT;AACA,IAAI,YAAY;AAAA,EACd,SAAS,SAAS,WAAW;AAC3B,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK;AACL;AAAA,MACF,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,MACF,KAAK;AACH,aAAK;AACL,eAAO,SAAS,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,iBAAiB,CAAC,GAAG;AAC5B,WAAK;AACL;AAAA,IACF;AACA,WAAO,UAAU,UAAU,EAAE;AAAA,EAC/B;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,MACF,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,IACJ;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,kBAAkB,SAAS,mBAAmB;AAC5C,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,MACF,KAAK;AACH,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AACA,SAAK;AAAA,EACP;AAAA,EACA,0BAA0B,SAAS,2BAA2B;AAC5D,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,aAAK;AACL;AAAA,MACF,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,MACF,KAAK;AACH,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AACA,SAAK;AACL,eAAW;AAAA,EACb;AAAA,EACA,mBAAmB,SAAS,oBAAoB;AAC9C,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,MACF,KAAK;AACH,aAAK;AACL,eAAO,SAAS,KAAK;AAAA,IACzB;AACA,SAAK;AAAA,EACP;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,MACtC,KAAK;AACH,aAAK;AACL,gBAAQ,KAAK;AACb,eAAO,SAAS,QAAQ,IAAI;AAAA,MAC9B,KAAK;AACH,aAAK;AACL,gBAAQ,KAAK;AACb,eAAO,SAAS,WAAW,IAAI;AAAA,MACjC,KAAK;AACH,aAAK;AACL,gBAAQ,MAAM;AACd,eAAO,SAAS,WAAW,KAAK;AAAA,MAClC,KAAK;AAAA,MACL,KAAK;AACH,YAAI,KAAK,MAAM,KAAK;AAClB,kBAAQ;AAAA,QACV;AACA,mBAAW;AACX;AAAA,MACF,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AACH,aAAK;AACL,gBAAQ,SAAS;AACjB,eAAO,SAAS,WAAW,QAAQ;AAAA,MACrC,KAAK;AACH,aAAK;AACL,gBAAQ,IAAI;AACZ,eAAO,SAAS,WAAW,GAAG;AAAA,MAChC,KAAK;AAAA,MACL,KAAK;AACH,sBAAc,KAAK,MAAM;AACzB,iBAAS;AACT,mBAAW;AACX;AAAA,IACJ;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,2BAA2B,SAAS,4BAA4B;AAC9D,QAAI,MAAM,KAAK;AACb,YAAM,YAAY,KAAK,CAAC;AAAA,IAC1B;AACA,SAAK;AACL,QAAI,IAAI,cAAc;AACtB,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF;AACE,YAAI,CAAC,KAAK,cAAc,CAAC,GAAG;AAC1B,gBAAM,kBAAkB;AAAA,QAC1B;AACA;AAAA,IACJ;AACA,cAAU;AACV,eAAW;AAAA,EACb;AAAA,EACA,gBAAgB,SAAS,iBAAiB;AACxC,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf;AAAA,MACF,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,IACJ;AACA,QAAI,KAAK,iBAAiB,CAAC,GAAG;AAC5B,gBAAU,KAAK;AACf;AAAA,IACF;AACA,WAAO,SAAS,cAAc,MAAM;AAAA,EACtC;AAAA,EACA,sBAAsB,SAAS,uBAAuB;AACpD,QAAI,MAAM,KAAK;AACb,YAAM,YAAY,KAAK,CAAC;AAAA,IAC1B;AACA,SAAK;AACL,QAAI,IAAI,cAAc;AACtB,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF;AACE,YAAI,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC7B,gBAAM,kBAAkB;AAAA,QAC1B;AACA;AAAA,IACJ;AACA,cAAU;AACV,eAAW;AAAA,EACb;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AACH,aAAK;AACL,gBAAQ,SAAS;AACjB,eAAO,SAAS,WAAW,QAAQ,QAAQ;AAAA,MAC7C,KAAK;AACH,aAAK;AACL,gBAAQ,IAAI;AACZ,eAAO,SAAS,WAAW,GAAG;AAAA,IAClC;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AACA,WAAO,SAAS,WAAW,QAAQ,CAAC;AAAA,EACtC;AAAA,EACA,gBAAgB,SAAS,iBAAiB;AACxC,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf;AAAA,IACF;AACA,WAAO,SAAS,WAAW,QAAQ,OAAO,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,qBAAqB,SAAS,sBAAsB;AAClD,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACF;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACF;AACA,WAAO,SAAS,WAAW,QAAQ,OAAO,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf;AAAA,IACF;AACA,WAAO,SAAS,WAAW,QAAQ,OAAO,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACF;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,qBAAqB,SAAS,sBAAsB;AAClD,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACF;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,wBAAwB,SAAS,yBAAyB;AACxD,QAAI,KAAK,QAAQ,CAAC,GAAG;AACnB,gBAAU,KAAK;AACf;AAAA,IACF;AACA,WAAO,SAAS,WAAW,QAAQ,OAAO,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,KAAK,WAAW,CAAC,GAAG;AACtB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACF;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,oBAAoB,SAAS,qBAAqB;AAChD,QAAI,KAAK,WAAW,CAAC,GAAG;AACtB,gBAAU,KAAK;AACf;AAAA,IACF;AACA,WAAO,SAAS,WAAW,QAAQ,OAAO,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,aAAK;AACL,kBAAU,OAAO;AACjB;AAAA,MACF,KAAK;AACH,YAAI,aAAa;AACf,eAAK;AACL,iBAAO,SAAS,UAAU,MAAM;AAAA,QAClC;AACA,kBAAU,KAAK;AACf;AAAA,MACF,KAAK;AACH,YAAI,CAAC,aAAa;AAChB,eAAK;AACL,iBAAO,SAAS,UAAU,MAAM;AAAA,QAClC;AACA,kBAAU,KAAK;AACf;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,cAAM,YAAY,KAAK,CAAC;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK;AACH,sBAAc,CAAC;AACf;AAAA,MACF,KAAK;AACH,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AACA,cAAU,KAAK;AAAA,EACjB;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AACA,eAAW;AAAA,EACb;AAAA,EACA,oBAAoB,SAAS,qBAAqB;AAChD,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MACF,KAAK;AACH,aAAK;AACL,mBAAW;AACX;AAAA,MACF,KAAK;AACH,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,MACtC,KAAK;AAAA,MACL,KAAK;AACH,sBAAc,KAAK,MAAM;AACzB,mBAAW;AACX;AAAA,IACJ;AACA,QAAI,KAAK,cAAc,CAAC,GAAG;AACzB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACF;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,mBAAmB,SAAS,oBAAoB;AAC9C,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACtC;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,qBAAqB,SAAS,sBAAsB;AAClD,eAAW;AAAA,EACb;AAAA,EACA,oBAAoB,SAAS,qBAAqB;AAChD,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,kBAAkB,SAAS,mBAAmB;AAC5C,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACtC;AACA,eAAW;AAAA,EACb;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,YAAQ,GAAG;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AACA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AAAA,EACA,KAAK,SAAS,MAAM;AAClB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,SAASG,OAAMD,QAAO;AAC7B,SAAO;AAAA,IACL,MAAMC;AAAA,IACN,OAAOD;AAAA,IACP;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,QAAQ,GAAG;AAClB,MAAI,YAAY,2BAA2B,CAAC,GAC1C;AACF,MAAI;AACF,SAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,UAAI,KAAK,MAAM;AACf,UAAI,IAAI,KAAK;AACb,UAAI,MAAM,IAAI;AACZ,cAAM,YAAY,KAAK,CAAC;AAAA,MAC1B;AACA,WAAK;AAAA,IACP;AAAA,EACF,SAAS,KAAK;AACZ,cAAU,EAAE,GAAG;AAAA,EACjB,UAAE;AACA,cAAU,EAAE;AAAA,EACd;AACF;AACA,SAAS,SAAS;AAChB,MAAIF,KAAI,KAAK;AACb,UAAQA,IAAG;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,UAAI,KAAK,QAAQ,KAAK,CAAC,GAAG;AACxB,cAAM,YAAY,KAAK,CAAC;AAAA,MAC1B;AACA,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,aAAO,UAAU;AAAA,IACnB,KAAK;AACH,WAAK;AACL,aAAO,cAAc;AAAA,IACvB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,WAAK;AACL,aAAO;AAAA,IACT,KAAK;AACH,WAAK;AACL,UAAI,KAAK,MAAM,MAAM;AACnB,aAAK;AAAA,MACP;AACA,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,YAAM,YAAY,KAAK,CAAC;AAAA,IAC1B,KAAK;AACH,YAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AACA,SAAO,KAAK;AACd;AACA,SAAS,YAAY;AACnB,MAAII,UAAS;AACb,MAAIJ,KAAI,KAAK;AACb,MAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACvB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AACA,EAAAI,WAAU,KAAK;AACf,EAAAJ,KAAI,KAAK;AACT,MAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACvB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC1B;AACA,EAAAI,WAAU,KAAK;AACf,SAAO,OAAO,cAAc,SAASA,SAAQ,EAAE,CAAC;AAClD;AACA,SAAS,gBAAgB;AACvB,MAAIA,UAAS;AACb,MAAI,QAAQ;AACZ,SAAO,UAAU,GAAG;AAClB,QAAI,MAAM,KAAK;AACf,QAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,YAAM,YAAY,KAAK,CAAC;AAAA,IAC1B;AACA,IAAAA,WAAU,KAAK;AAAA,EACjB;AACA,SAAO,OAAO,cAAc,SAASA,SAAQ,EAAE,CAAC;AAClD;AACA,IAAI,cAAc;AAAA,EAChB,OAAO,SAASC,SAAQ;AACtB,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,WAAW;AAAA,IACnB;AACA,SAAK;AAAA,EACP;AAAA,EACA,oBAAoB,SAASC,sBAAqB;AAChD,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AACH,cAAM,MAAM;AACZ,qBAAa;AACb;AAAA,MACF,KAAK;AACH,YAAI;AACJ;AAAA,MACF,KAAK;AACH,cAAM,WAAW;AAAA,IACrB;AAAA,EACF;AAAA,EACA,mBAAmB,SAASC,qBAAoB;AAC9C,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,WAAW;AAAA,IACnB;AACA,iBAAa;AAAA,EACf;AAAA,EACA,qBAAqB,SAASC,uBAAsB;AAClD,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,WAAW;AAAA,IACnB;AACA,SAAK;AAAA,EACP;AAAA,EACA,kBAAkB,SAASC,oBAAmB;AAC5C,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,WAAW;AAAA,IACnB;AACA,QAAI,MAAM,SAAS,gBAAgB,MAAM,UAAU,KAAK;AACtD,UAAI;AACJ;AAAA,IACF;AACA,SAAK;AAAA,EACP;AAAA,EACA,oBAAoB,SAASC,sBAAqB;AAChD,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,WAAW;AAAA,IACnB;AACA,YAAQ,MAAM,OAAO;AAAA,MACnB,KAAK;AACH,qBAAa;AACb;AAAA,MACF,KAAK;AACH,YAAI;AAAA,IACR;AAAA,EACF;AAAA,EACA,iBAAiB,SAASC,mBAAkB;AAC1C,QAAI,MAAM,SAAS,OAAO;AACxB,YAAM,WAAW;AAAA,IACnB;AACA,YAAQ,MAAM,OAAO;AAAA,MACnB,KAAK;AACH,qBAAa;AACb;AAAA,MACF,KAAK;AACH,YAAI;AAAA,IACR;AAAA,EACF;AAAA,EACA,KAAK,SAASC,OAAM;AAAA,EAAC;AACvB;AACA,SAAS,OAAO;AACd,MAAIV;AACJ,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK;AACH,cAAQ,MAAM,OAAO;AAAA,QACnB,KAAK;AACH,UAAAA,SAAQ,CAAC;AACT;AAAA,QACF,KAAK;AACH,UAAAA,SAAQ,CAAC;AACT;AAAA,MACJ;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,MAAAA,SAAQ,MAAM;AACd;AAAA,EACJ;AACA,MAAI,SAAS,QAAW;AACtB,WAAOA;AAAA,EACT,OAAO;AACL,QAAI,SAAS,MAAM,MAAM,SAAS,CAAC;AACnC,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO,KAAKA,MAAK;AAAA,IACnB,OAAO;AACL,aAAO,eAAe,QAAQ,KAAK;AAAA,QACjC,OAAOA;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAIA,WAAU,QAAQ,QAAQA,MAAK,MAAM,UAAU;AACjD,UAAM,KAAKA,MAAK;AAChB,QAAI,MAAM,QAAQA,MAAK,GAAG;AACxB,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa;AAAA,IACf;AAAA,EACF,OAAO;AACL,QAAI,UAAU,MAAM,MAAM,SAAS,CAAC;AACpC,QAAI,WAAW,MAAM;AACnB,mBAAa;AAAA,IACf,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,MAAM;AACb,QAAM,IAAI;AACV,MAAI,UAAU,MAAM,MAAM,SAAS,CAAC;AACpC,MAAI,WAAW,MAAM;AACnB,iBAAa;AAAA,EACf,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa;AAAA,EACf;AACF;AACA,SAAS,YAAYF,IAAG;AACtB,MAAIA,OAAM,QAAW;AACnB,WAAO,YAAY,kCAAkC,OAAO,MAAM,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,EACvF;AACA,SAAO,YAAY,6BAA6B,OAAO,WAAWA,EAAC,GAAG,OAAO,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,MAAM,CAAC;AACjH;AACA,SAAS,aAAa;AACpB,SAAO,YAAY,kCAAkC,OAAO,MAAM,GAAG,EAAE,OAAO,MAAM,CAAC;AACvF;AACA,SAAS,oBAAoB;AAC3B,YAAU;AACV,SAAO,YAAY,0CAA0C,OAAO,MAAM,GAAG,EAAE,OAAO,MAAM,CAAC;AAC/F;AACA,SAAS,cAAcA,IAAG;AACxB,UAAQ,KAAK,WAAW,OAAO,WAAWA,EAAC,GAAG,yDAAyD,CAAC;AAC1G;AACA,SAAS,WAAWA,IAAG;AACrB,MAAI,eAAe;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AACA,MAAI,aAAaA,EAAC,GAAG;AACnB,WAAO,aAAaA,EAAC;AAAA,EACvB;AACA,MAAIA,KAAI,KAAK;AACX,QAAI,YAAYA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AAC3C,WAAO,SAAS,OAAO,WAAW,UAAU,UAAU,MAAM;AAAA,EAC9D;AACA,SAAOA;AACT;AACA,SAAS,YAAY,SAAS;AAC5B,MAAI,MAAM,IAAI,YAAY,OAAO;AACjC,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,SAAO;AACT;AACA,IAAI,YAAY,SAASa,WAAUX,QAAO,UAAU,OAAO;AACzD,MAAIY,SAAQ,CAAC;AACb,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM;AACV,MAAI;AACJ,MAAI,YAAY,QAAQ,QAAQ,QAAQ,MAAM,YAAY,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAClF,YAAQ,SAAS;AACjB,YAAQ,SAAS;AACjB,eAAW,SAAS;AAAA,EACtB;AACA,MAAI,OAAO,aAAa,YAAY;AAClC,mBAAe;AAAA,EACjB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAClC,mBAAe,CAAC;AAChB,QAAI,aAAa,2BAA2B,QAAQ,GAClD;AACF,QAAI;AACF,WAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,YAAI,IAAI,OAAO;AACf,YAAI,OAAO;AACX,YAAI,OAAO,MAAM,UAAU;AACzB,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,YAAY,aAAa,UAAU,aAAa,QAAQ;AAC9E,iBAAO,OAAO,CAAC;AAAA,QACjB;AACA,YAAI,SAAS,UAAa,aAAa,QAAQ,IAAI,IAAI,GAAG;AACxD,uBAAa,KAAK,IAAI;AAAA,QACxB;AAAA,MACF;AAAA,IACF,SAAS,KAAK;AACZ,iBAAW,EAAE,GAAG;AAAA,IAClB,UAAE;AACA,iBAAW,EAAE;AAAA,IACf;AAAA,EACF;AACA,MAAI,iBAAiB,QAAQ;AAC3B,YAAQ,OAAO,KAAK;AAAA,EACtB,WAAW,iBAAiB,QAAQ;AAClC,YAAQ,OAAO,KAAK;AAAA,EACtB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,QAAQ,GAAG;AACb,cAAQ,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACtC,YAAM,aAAa,OAAO,GAAG,KAAK;AAAA,IACpC;AAAA,EACF,WAAW,OAAO,UAAU,UAAU;AACpC,UAAM,MAAM,OAAO,GAAG,EAAE;AAAA,EAC1B;AACA,SAAO,kBAAkB,IAAI;AAAA,IAC3B,IAAIZ;AAAA,EACN,CAAC;AACD,WAAS,kBAAkBa,MAAK,QAAQ;AACtC,QAAIb,SAAQ,OAAOa,IAAG;AACtB,QAAIb,UAAS,MAAM;AACjB,UAAI,OAAOA,OAAM,YAAY,YAAY;AACvC,QAAAA,SAAQA,OAAM,QAAQa,IAAG;AAAA,MAC3B,WAAW,OAAOb,OAAM,WAAW,YAAY;AAC7C,QAAAA,SAAQA,OAAM,OAAOa,IAAG;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,cAAc;AAChB,MAAAb,SAAQ,aAAa,KAAK,QAAQa,MAAKb,MAAK;AAAA,IAC9C;AACA,QAAIA,kBAAiB,QAAQ;AAC3B,MAAAA,SAAQ,OAAOA,MAAK;AAAA,IACtB,WAAWA,kBAAiB,QAAQ;AAClC,MAAAA,SAAQ,OAAOA,MAAK;AAAA,IACtB,WAAWA,kBAAiB,SAAS;AACnC,MAAAA,SAAQA,OAAM,QAAQ;AAAA,IACxB;AACA,YAAQA,QAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACX;AACA,QAAI,OAAOA,WAAU,UAAU;AAC7B,aAAO,YAAYA,QAAO,KAAK;AAAA,IACjC;AACA,QAAI,OAAOA,WAAU,UAAU;AAC7B,aAAO,OAAOA,MAAK;AAAA,IACrB;AACA,QAAI,QAAQA,MAAK,MAAM,UAAU;AAC/B,aAAO,MAAM,QAAQA,MAAK,IAAI,eAAeA,MAAK,IAAI,gBAAgBA,MAAK;AAAA,IAC7E;AACA,WAAO;AAAA,EACT;AACA,WAAS,YAAYA,QAAO;AAC1B,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,eAAe;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AACA,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,UAAI,MAAMA,OAAM,CAAC;AACjB,cAAQ,KAAK;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,GAAG;AACV,qBAAW;AACX;AAAA,QACF,KAAK;AACH,cAAI,KAAK,QAAQA,OAAM,IAAI,CAAC,CAAC,GAAG;AAC9B,uBAAW;AACX;AAAA,UACF;AAAA,MACJ;AACA,UAAI,aAAa,GAAG,GAAG;AACrB,mBAAW,aAAa,GAAG;AAC3B;AAAA,MACF;AACA,UAAI,MAAM,KAAK;AACb,YAAI,YAAY,IAAI,WAAW,CAAC,EAAE,SAAS,EAAE;AAC7C,mBAAW,SAAS,OAAO,WAAW,UAAU,UAAU,MAAM;AAChE;AAAA,MACF;AACA,iBAAW;AAAA,IACb;AACA,QAAI,YAAY,SAAS,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,GAAG,GAAG;AAClE,aAAO,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI;AAAA,IACrC,CAAC;AACD,cAAU,QAAQ,QAAQ,IAAI,OAAO,WAAW,GAAG,GAAG,aAAa,SAAS,CAAC;AAC7E,WAAO,YAAY,UAAU;AAAA,EAC/B;AACA,WAAS,gBAAgBA,QAAO;AAC9B,QAAIY,OAAM,QAAQZ,MAAK,KAAK,GAAG;AAC7B,YAAM,UAAU,wCAAwC;AAAA,IAC1D;AACA,IAAAY,OAAM,KAAKZ,MAAK;AAChB,QAAI,WAAW;AACf,aAAS,SAAS;AAClB,QAAI,OAAO,gBAAgB,OAAO,KAAKA,MAAK;AAC5C,QAAI,UAAU,CAAC;AACf,QAAI,aAAa,2BAA2B,IAAI,GAC9C;AACF,QAAI;AACF,WAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,YAAI,QAAQ,OAAO;AACnB,YAAI,iBAAiB,kBAAkB,OAAOA,MAAK;AACnD,YAAI,mBAAmB,QAAW;AAChC,cAAI,SAAS,aAAa,KAAK,IAAI;AACnC,cAAI,QAAQ,IAAI;AACd,sBAAU;AAAA,UACZ;AACA,oBAAU;AACV,kBAAQ,KAAK,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF,SAAS,KAAK;AACZ,iBAAW,EAAE,GAAG;AAAA,IAClB,UAAE;AACA,iBAAW,EAAE;AAAA,IACf;AACA,QAAI;AACJ,QAAI,QAAQ,WAAW,GAAG;AACxB,cAAQ;AAAA,IACV,OAAO;AACL,UAAI;AACJ,UAAI,QAAQ,IAAI;AACd,qBAAa,QAAQ,KAAK,GAAG;AAC7B,gBAAQ,MAAM,aAAa;AAAA,MAC7B,OAAO;AACL,YAAI,YAAY,QAAQ;AACxB,qBAAa,QAAQ,KAAK,SAAS;AACnC,gBAAQ,QAAQ,SAAS,aAAa,QAAQ,WAAW;AAAA,MAC3D;AAAA,IACF;AACA,IAAAY,OAAM,IAAI;AACV,aAAS;AACT,WAAO;AAAA,EACT;AACA,WAAS,aAAaC,MAAK;AACzB,QAAIA,KAAI,WAAW,GAAG;AACpB,aAAO,YAAYA,MAAK,IAAI;AAAA,IAC9B;AACA,QAAI,YAAY,OAAO,cAAcA,KAAI,YAAY,CAAC,CAAC;AACvD,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAClC,aAAO,YAAYA,MAAK,IAAI;AAAA,IAC9B;AACA,aAAS,IAAI,UAAU,QAAQ,IAAIA,KAAI,QAAQ,KAAK;AAClD,UAAI,CAAC,KAAK,iBAAiB,OAAO,cAAcA,KAAI,YAAY,CAAC,CAAC,CAAC,GAAG;AACpE,eAAO,YAAYA,MAAK,IAAI;AAAA,MAC9B;AAAA,IACF;AACA,WAAOA;AAAA,EACT;AACA,WAAS,eAAeb,QAAO;AAC7B,QAAIY,OAAM,QAAQZ,MAAK,KAAK,GAAG;AAC7B,YAAM,UAAU,wCAAwC;AAAA,IAC1D;AACA,IAAAY,OAAM,KAAKZ,MAAK;AAChB,QAAI,WAAW;AACf,aAAS,SAAS;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,UAAI,iBAAiB,kBAAkB,OAAO,CAAC,GAAGA,MAAK;AACvD,cAAQ,KAAK,mBAAmB,SAAY,iBAAiB,MAAM;AAAA,IACrE;AACA,QAAI;AACJ,QAAI,QAAQ,WAAW,GAAG;AACxB,cAAQ;AAAA,IACV,OAAO;AACL,UAAI,QAAQ,IAAI;AACd,YAAI,aAAa,QAAQ,KAAK,GAAG;AACjC,gBAAQ,MAAM,aAAa;AAAA,MAC7B,OAAO;AACL,YAAI,YAAY,QAAQ;AACxB,YAAI,cAAc,QAAQ,KAAK,SAAS;AACxC,gBAAQ,QAAQ,SAAS,cAAc,QAAQ,WAAW;AAAA,MAC5D;AAAA,IACF;AACA,IAAAY,OAAM,IAAI;AACV,aAAS;AACT,WAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV;AAAA,EACA;AACF;AACA,IAAI,MAAM;AACV,IAAO,gBAAQ;;;ACrlCf,SAAS,cAAc,MAAM;AAC3B,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACvF,MAAI,MAAM,KAAK;AACf,MAAIE,OAAM,GACRC,SAAQ,IACR,cAAc,GACdC,SAAQ,IACR,aAAa,GACb,kBAAkB,GAClB,uBAAuB,GACvB,2BAA2B,GAC3B,YAAY;AACd,WAAS,cAAc,OAAO,OAAO;AACnC,QAAI,SAAS;AACb,QAAIC,UAAS;AACb,WAAO,SAAS,SAAS,CAAC,OAAO;AAC/B,UAAI,KAAK,KAAK,WAAWH,IAAG;AAC5B,UAAI,MAAM,MAAM,MAAM,IAAI;AACxB,QAAAG,UAASA,UAAS,KAAK,KAAK;AAAA,MAC9B,WAAW,MAAM,MAAM,MAAM,IAAI;AAC/B,QAAAA,UAASA,UAAS,KAAK,KAAK,KAAK;AAAA,MACnC,WAAW,MAAM,MAAM,MAAM,KAAK;AAChC,QAAAA,UAASA,UAAS,KAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL;AAAA,MACF;AACA,MAAAH;AACA;AAAA,IACF;AACA,QAAI,SAAS,OAAO;AAClB,MAAAG,UAAS;AAAA,IACX;AACA,WAAOA;AAAA,EACT;AACA,WAAS,YAAY,aAAa;AAChC,IAAAH,OAAM;AACN,IAAAC,SAAQ;AACR,kBAAc;AACd,IAAAC,SAAQ;AACR,gBAAY;AAAA,EACd;AACA,WAAS,aAAa;AACpB,QAAIE,SAAQJ;AACZ,QAAI,KAAK,WAAWA,IAAG,MAAM,IAAI;AAC/B,MAAAA;AAAA,IACF,OAAO;AACL,MAAAA;AACA,aAAOA,OAAM,KAAK,UAAUK,SAAQ,KAAK,WAAWL,IAAG,CAAC,GAAG;AACzD,QAAAA;AAAA,MACF;AAAA,IACF;AACA,QAAIA,OAAM,KAAK,UAAU,KAAK,WAAWA,IAAG,MAAM,IAAI;AACpD,MAAAA;AACA,UAAIA,OAAM,KAAK,UAAUK,SAAQ,KAAK,WAAWL,IAAG,CAAC,GAAG;AACtD,QAAAA;AACA,eAAOA,OAAM,KAAK,UAAUK,SAAQ,KAAK,WAAWL,IAAG,CAAC,GAAG;AACzD,UAAAA;AAAA,QACF;AAAA,MACF,OAAO;AACL,oBAAY;AACZ,eAAO,KAAK,UAAUI,QAAOJ,IAAG;AAAA,MAClC;AAAA,IACF;AACA,QAAIM,OAAMN;AACV,QAAIA,OAAM,KAAK,WAAW,KAAK,WAAWA,IAAG,MAAM,MAAM,KAAK,WAAWA,IAAG,MAAM,MAAM;AACtF,MAAAA;AACA,UAAIA,OAAM,KAAK,UAAU,KAAK,WAAWA,IAAG,MAAM,MAAM,KAAK,WAAWA,IAAG,MAAM,IAAI;AACnF,QAAAA;AAAA,MACF;AACA,UAAIA,OAAM,KAAK,UAAUK,SAAQ,KAAK,WAAWL,IAAG,CAAC,GAAG;AACtD,QAAAA;AACA,eAAOA,OAAM,KAAK,UAAUK,SAAQ,KAAK,WAAWL,IAAG,CAAC,GAAG;AACzD,UAAAA;AAAA,QACF;AACA,QAAAM,OAAMN;AAAA,MACR,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,KAAK,UAAUI,QAAOE,IAAG;AAAA,EAClC;AACA,WAAS,aAAa;AACpB,QAAI,SAAS,IACXF,SAAQJ;AACV,WAAO,MAAM;AACX,UAAIA,QAAO,KAAK;AACd,kBAAU,KAAK,UAAUI,QAAOJ,IAAG;AACnC,oBAAY;AACZ;AAAA,MACF;AACA,UAAI,KAAK,KAAK,WAAWA,IAAG;AAC5B,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAUI,QAAOJ,IAAG;AACnC,QAAAA;AACA;AAAA,MACF;AACA,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAUI,QAAOJ,IAAG;AACnC,QAAAA;AACA,YAAIA,QAAO,KAAK;AACd,sBAAY;AACZ;AAAA,QACF;AACA,YAAI,MAAM,KAAK,WAAWA,MAAK;AAC/B,gBAAQ,KAAK;AAAA,UACX,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,gBAAI,MAAM,cAAc,GAAG,IAAI;AAC/B,gBAAI,OAAO,GAAG;AACZ,wBAAU,OAAO,aAAa,GAAG;AAAA,YACnC,OAAO;AACL,0BAAY;AAAA,YACd;AACA;AAAA,UACF;AACE,wBAAY;AAAA,QAChB;AACA,QAAAI,SAAQJ;AACR;AAAA,MACF;AACA,UAAI,MAAM,KAAK,MAAM,IAAI;AACvB,YAAI,YAAY,EAAE,GAAG;AACnB,oBAAU,KAAK,UAAUI,QAAOJ,IAAG;AACnC,sBAAY;AACZ;AAAA,QACF,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF;AACA,MAAAA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,WAAW;AAClB,IAAAC,SAAQ;AACR,gBAAY;AACZ,kBAAcD;AACd,sBAAkB;AAClB,+BAA2B;AAC3B,QAAIA,QAAO,KAAK;AACd,oBAAc;AACd,aAAOE,SAAQ;AAAA,IACjB;AACA,QAAI,OAAO,KAAK,WAAWF,IAAG;AAC9B,QAAI,aAAa,IAAI,GAAG;AACtB,SAAG;AACD,QAAAA;AACA,QAAAC,UAAS,OAAO,aAAa,IAAI;AACjC,eAAO,KAAK,WAAWD,IAAG;AAAA,MAC5B,SAAS,aAAa,IAAI;AAC1B,aAAOE,SAAQ;AAAA,IACjB;AACA,QAAI,YAAY,IAAI,GAAG;AACrB,MAAAF;AACA,MAAAC,UAAS,OAAO,aAAa,IAAI;AACjC,UAAI,SAAS,MAAM,KAAK,WAAWD,IAAG,MAAM,IAAI;AAC9C,QAAAA;AACA,QAAAC,UAAS;AAAA,MACX;AACA;AACA,6BAAuBD;AACvB,aAAOE,SAAQ;AAAA,IACjB;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,QAAAF;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAF;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAF;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAF;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAF;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAF;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAF;AACA,QAAAC,SAAQ,WAAW;AACnB,eAAOC,SAAQ;AAAA,MACjB,KAAK;AACH,YAAIE,SAAQJ,OAAM;AAClB,YAAI,KAAK,WAAWA,OAAM,CAAC,MAAM,IAAI;AACnC,UAAAA,QAAO;AACP,iBAAOA,OAAM,KAAK;AAChB,gBAAI,YAAY,KAAK,WAAWA,IAAG,CAAC,GAAG;AACrC;AAAA,YACF;AACA,YAAAA;AAAA,UACF;AACA,UAAAC,SAAQ,KAAK,UAAUG,QAAOJ,IAAG;AACjC,iBAAOE,SAAQ;AAAA,QACjB;AACA,YAAI,KAAK,WAAWF,OAAM,CAAC,MAAM,IAAI;AACnC,UAAAA,QAAO;AACP,cAAI,aAAa,MAAM;AACvB,cAAI,gBAAgB;AACpB,iBAAOA,OAAM,YAAY;AACvB,gBAAI,KAAK,KAAK,WAAWA,IAAG;AAC5B,gBAAI,OAAO,MAAM,KAAK,WAAWA,OAAM,CAAC,MAAM,IAAI;AAChD,cAAAA,QAAO;AACP,8BAAgB;AAChB;AAAA,YACF;AACA,YAAAA;AACA,gBAAI,YAAY,EAAE,GAAG;AACnB,kBAAI,OAAO,MAAM,KAAK,WAAWA,IAAG,MAAM,IAAI;AAC5C,gBAAAA;AAAA,cACF;AACA;AACA,qCAAuBA;AAAA,YACzB;AAAA,UACF;AACA,cAAI,CAAC,eAAe;AAClB,YAAAA;AACA,wBAAY;AAAA,UACd;AACA,UAAAC,SAAQ,KAAK,UAAUG,QAAOJ,IAAG;AACjC,iBAAOE,SAAQ;AAAA,QACjB;AACA,QAAAD,UAAS,OAAO,aAAa,IAAI;AACjC,QAAAD;AACA,eAAOE,SAAQ;AAAA,MACjB,KAAK;AACH,QAAAD,UAAS,OAAO,aAAa,IAAI;AACjC,QAAAD;AACA,YAAIA,SAAQ,OAAO,CAACK,SAAQ,KAAK,WAAWL,IAAG,CAAC,GAAG;AACjD,iBAAOE,SAAQ;AAAA,QACjB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,QAAAD,UAAS,WAAW;AACpB,eAAOC,SAAQ;AAAA,MACjB;AACE,eAAOF,OAAM,OAAO,0BAA0B,IAAI,GAAG;AACnD,UAAAA;AACA,iBAAO,KAAK,WAAWA,IAAG;AAAA,QAC5B;AACA,YAAI,gBAAgBA,MAAK;AACvB,UAAAC,SAAQ,KAAK,UAAU,aAAaD,IAAG;AACvC,kBAAQC,QAAO;AAAA,YACb,KAAK;AACH,qBAAOC,SAAQ;AAAA,YACjB,KAAK;AACH,qBAAOA,SAAQ;AAAA,YACjB,KAAK;AACH,qBAAOA,SAAQ;AAAA,UACnB;AACA,iBAAOA,SAAQ;AAAA,QACjB;AACA,QAAAD,UAAS,OAAO,aAAa,IAAI;AACjC,QAAAD;AACA,eAAOE,SAAQ;AAAA,IACnB;AAAA,EACF;AACA,WAAS,0BAA0B,MAAM;AACvC,QAAI,aAAa,IAAI,KAAK,YAAY,IAAI,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,oBAAoB;AAC3B,QAAI;AACJ,OAAG;AACD,eAAS,SAAS;AAAA,IACpB,SAAS,UAAU,MAAM,UAAU;AACnC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,SAAS,cAAc;AAClC,aAAOF;AAAA,IACT;AAAA,IACA,MAAM,eAAe,oBAAoB;AAAA,IACzC,UAAU,SAAS,WAAW;AAC5B,aAAOE;AAAA,IACT;AAAA,IACA,eAAe,SAAS,gBAAgB;AACtC,aAAOD;AAAA,IACT;AAAA,IACA,gBAAgB,SAAS,iBAAiB;AACxC,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,SAAS,iBAAiB;AACxC,aAAOD,OAAM;AAAA,IACf;AAAA,IACA,mBAAmB,SAAS,oBAAoB;AAC9C,aAAO;AAAA,IACT;AAAA,IACA,wBAAwB,SAAS,yBAAyB;AACxD,aAAO,cAAc;AAAA,IACvB;AAAA,IACA,eAAe,SAAS,gBAAgB;AACtC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,OAAO,MAAM,OAAO;AAC7B;AACA,SAAS,YAAY,IAAI;AACvB,SAAO,OAAO,MAAM,OAAO;AAC7B;AACA,SAASK,SAAQ,IAAI;AACnB,SAAO,MAAM,MAAM,MAAM;AAC3B;AACA,IAAI;AAAA,CACH,SAAU,iBAAiB;AAC1B,kBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,kBAAgB,gBAAgB,gBAAgB,IAAI,EAAE,IAAI;AAC1D,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,kBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,kBAAgB,gBAAgB,WAAW,IAAI,EAAE,IAAI;AACrD,kBAAgB,gBAAgB,YAAY,IAAI,GAAG,IAAI;AACvD,kBAAgB,gBAAgB,cAAc,IAAI,EAAE,IAAI;AACxD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,KAAK,IAAI,EAAE,IAAI;AAC/C,kBAAgB,gBAAgB,aAAa,IAAI,EAAE,IAAI;AACvD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,WAAW,IAAI,GAAG,IAAI;AACtD,kBAAgB,gBAAgB,aAAa,IAAI,EAAE,IAAI;AACvD,kBAAgB,gBAAgB,MAAM,IAAI,EAAE,IAAI;AAChD,kBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,kBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,kBAAgB,gBAAgB,KAAK,IAAI,CAAC,IAAI;AAChD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI,eAAe,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AAC/D,SAAO,IAAI,OAAO,KAAK;AACzB,CAAC;AACD,IAAI,kBAAkB;AACtB,IAAI,6BAA6B;AAAA,EAC/B,KAAK;AAAA,IACH,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AAC/D,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AAC/D,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,QAAQ,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AACjE,aAAO,SAAS,IAAI,OAAO,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,KAAK;AAAA,IACH,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AAC/D,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AAC/D,aAAO,OAAO,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AAAA,IACD,QAAQ,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU,GAAG,OAAO;AACjE,aAAO,SAAS,IAAI,OAAO,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAiPA,IAAI;AAAA,CACH,SAAU,eAAe;AACxB,gBAAc,UAAU;AAAA,IACtB,oBAAoB;AAAA,EACtB;AACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAiHtC,SAASE,OAAM,MAAM;AACnB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,aAAa;AAC/F,MAAI,kBAAkB;AACtB,MAAI,gBAAgB,CAAC;AACrB,MAAI,kBAAkB,CAAC;AACvB,WAAS,QAAQC,QAAO;AACtB,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,oBAAc,KAAKA,MAAK;AAAA,IAC1B,WAAW,oBAAoB,MAAM;AACnC,oBAAc,eAAe,IAAIA;AAAA,IACnC;AAAA,EACF;AACA,MAAI,UAAU;AAAA,IACZ,eAAe,SAAS,gBAAgB;AACtC,UAAI,SAAS,CAAC;AACd,cAAQ,MAAM;AACd,sBAAgB,KAAK,aAAa;AAClC,sBAAgB;AAChB,wBAAkB;AAAA,IACpB;AAAA,IACA,kBAAkB,SAAS,iBAAiB,MAAM;AAChD,wBAAkB;AAAA,IACpB;AAAA,IACA,aAAa,SAAS,cAAc;AAClC,sBAAgB,gBAAgB,IAAI;AAAA,IACtC;AAAA,IACA,cAAc,SAAS,eAAe;AACpC,UAAI,QAAQ,CAAC;AACb,cAAQ,KAAK;AACb,sBAAgB,KAAK,aAAa;AAClC,sBAAgB;AAChB,wBAAkB;AAAA,IACpB;AAAA,IACA,YAAY,SAAS,aAAa;AAChC,sBAAgB,gBAAgB,IAAI;AAAA,IACtC;AAAA,IACA,gBAAgB;AAAA,IAChB,SAAS,SAAS,QAAQ,OAAO,QAAQ,QAAQ;AAC/C,aAAO,KAAK;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,MAAM,SAAS,OAAO;AAC5B,SAAO,cAAc,CAAC;AACxB;AAyNA,SAAS,MAAM,MAAM,SAAS;AAC5B,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,aAAa;AAC/F,MAAI,WAAW,cAAc,MAAM,KAAK;AACxC,MAAI,YAAY,CAAC;AACjB,MAAI,sBAAsB;AAC1B,WAAS,aAAa,eAAe;AACnC,WAAO,gBAAgB,WAAY;AACjC,aAAO,wBAAwB,KAAK,cAAc,SAAS,eAAe,GAAG,SAAS,eAAe,GAAG,SAAS,kBAAkB,GAAG,SAAS,uBAAuB,CAAC;AAAA,IACzK,IAAI,WAAY;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,cAAc,eAAe;AACpC,WAAO,gBAAgB,SAAU,KAAK;AACpC,aAAO,wBAAwB,KAAK,cAAc,KAAK,SAAS,eAAe,GAAG,SAAS,eAAe,GAAG,SAAS,kBAAkB,GAAG,SAAS,uBAAuB,CAAC;AAAA,IAC9K,IAAI,WAAY;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,sBAAsB,eAAe;AAC5C,WAAO,gBAAgB,SAAU,KAAK;AACpC,aAAO,wBAAwB,KAAK,cAAc,KAAK,SAAS,eAAe,GAAG,SAAS,eAAe,GAAG,SAAS,kBAAkB,GAAG,SAAS,uBAAuB,GAAG,WAAY;AACxL,eAAO,UAAU,MAAM;AAAA,MACzB,CAAC;AAAA,IACH,IAAI,WAAY;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,aAAa,eAAe;AACnC,WAAO,gBAAgB,WAAY;AACjC,UAAI,sBAAsB,GAAG;AAC3B;AAAA,MACF,OAAO;AACL,YAAI,WAAW,cAAc,SAAS,eAAe,GAAG,SAAS,eAAe,GAAG,SAAS,kBAAkB,GAAG,SAAS,uBAAuB,GAAG,WAAY;AAC9J,iBAAO,UAAU,MAAM;AAAA,QACzB,CAAC;AACD,YAAI,aAAa,OAAO;AACtB,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF,IAAI,WAAY;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,WAAW,eAAe;AACjC,WAAO,gBAAgB,WAAY;AACjC,UAAI,sBAAsB,GAAG;AAC3B;AAAA,MACF;AACA,UAAI,wBAAwB,GAAG;AAC7B,sBAAc,SAAS,eAAe,GAAG,SAAS,eAAe,GAAG,SAAS,kBAAkB,GAAG,SAAS,uBAAuB,CAAC;AAAA,MACrI;AAAA,IACF,IAAI,WAAY;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,gBAAgB,aAAa,QAAQ,aAAa,GACpD,mBAAmB,sBAAsB,QAAQ,gBAAgB,GACjE,cAAc,WAAW,QAAQ,WAAW,GAC5C,eAAe,aAAa,QAAQ,YAAY,GAChD,aAAa,WAAW,QAAQ,UAAU,GAC1C,iBAAiB,sBAAsB,QAAQ,cAAc,GAC7D,cAAc,cAAc,QAAQ,WAAW,GAC/C,YAAY,aAAa,QAAQ,SAAS,GAC1C,UAAU,cAAc,QAAQ,OAAO;AACzC,MAAI,mBAAmB,WAAW,QAAQ;AAC1C,MAAI,qBAAqB,WAAW,QAAQ;AAC5C,WAAS,WAAW;AAClB,WAAO,MAAM;AACX,UAAIC,SAAQ,SAAS,KAAK;AAC1B,cAAQ,SAAS,cAAc,GAAG;AAAA,QAChC,KAAK;AACH,sBAAY,EAAE;AACd;AAAA,QACF,KAAK;AACH,sBAAY,EAAE;AACd;AAAA,QACF,KAAK;AACH,sBAAY,EAAE;AACd;AAAA,QACF,KAAK;AACH,cAAI,CAAC,kBAAkB;AACrB,wBAAY,EAAE;AAAA,UAChB;AACA;AAAA,QACF,KAAK;AACH,sBAAY,EAAE;AACd;AAAA,QACF,KAAK;AACH,sBAAY,EAAE;AACd;AAAA,MACJ;AACA,cAAQA,QAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,cAAI,kBAAkB;AACpB,wBAAY,EAAE;AAAA,UAChB,OAAO;AACL,sBAAU;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AACH,sBAAY,CAAC;AACb;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH;AAAA,QACF;AACE,iBAAOA;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,WAAS,YAAY,OAAO;AAC1B,QAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC1F,QAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,YAAQ,KAAK;AACb,QAAI,eAAe,SAAS,UAAU,SAAS,GAAG;AAChD,UAAIA,SAAQ,SAAS,SAAS;AAC9B,aAAOA,WAAU,IAAI;AACnB,YAAI,eAAe,QAAQA,MAAK,MAAM,IAAI;AACxC,mBAAS;AACT;AAAA,QACF,WAAW,UAAU,QAAQA,MAAK,MAAM,IAAI;AAC1C;AAAA,QACF;AACA,QAAAA,SAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,WAAS,YAAY,SAAS;AAC5B,QAAIC,SAAQ,SAAS,cAAc;AACnC,QAAI,SAAS;AACX,qBAAeA,MAAK;AAAA,IACtB,OAAO;AACL,uBAAiBA,MAAK;AACtB,gBAAU,KAAKA,MAAK;AAAA,IACtB;AACA,aAAS;AACT,WAAO;AAAA,EACT;AACA,WAAS,eAAe;AACtB,YAAQ,SAAS,SAAS,GAAG;AAAA,MAC3B,KAAK;AACH,YAAI,aAAa,SAAS,cAAc;AACxC,YAAIA,SAAQ,OAAO,UAAU;AAC7B,YAAI,MAAMA,MAAK,GAAG;AAChB,sBAAY,CAAC;AACb,UAAAA,SAAQ;AAAA,QACV;AACA,uBAAeA,MAAK;AACpB;AAAA,MACF,KAAK;AACH,uBAAe,IAAI;AACnB;AAAA,MACF,KAAK;AACH,uBAAe,IAAI;AACnB;AAAA,MACF,KAAK;AACH,uBAAe,KAAK;AACpB;AAAA,MACF;AACE,eAAO;AAAA,IACX;AACA,aAAS;AACT,WAAO;AAAA,EACT;AACA,WAAS,gBAAgB;AACvB,QAAI,SAAS,SAAS,MAAM,IAAI;AAC9B,kBAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,aAAO;AAAA,IACT;AACA,gBAAY,KAAK;AACjB,QAAI,SAAS,SAAS,MAAM,GAAG;AAC7B,kBAAY,GAAG;AACf,eAAS;AACT,UAAI,CAAC,WAAW,GAAG;AACjB,oBAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,kBAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,IAC3B;AACA,cAAU,IAAI;AACd,WAAO;AAAA,EACT;AACA,WAAS,cAAc;AACrB,kBAAc;AACd,aAAS;AACT,QAAI,aAAa;AACjB,WAAO,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,MAAM,IAAI;AAC9D,UAAI,SAAS,SAAS,MAAM,GAAG;AAC7B,YAAI,CAAC,YAAY;AACf,sBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,QACvB;AACA,oBAAY,GAAG;AACf,iBAAS;AACT,YAAI,SAAS,SAAS,MAAM,KAAK,oBAAoB;AACnD;AAAA,QACF;AAAA,MACF,WAAW,YAAY;AACrB,oBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,MACvB;AACA,UAAI,CAAC,cAAc,GAAG;AACpB,oBAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,MAC3B;AACA,mBAAa;AAAA,IACf;AACA,gBAAY;AACZ,QAAI,SAAS,SAAS,MAAM,GAAG;AAC7B,kBAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,IACxB,OAAO;AACL,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,aAAa;AACpB,iBAAa;AACb,aAAS;AACT,QAAI,iBAAiB;AACrB,QAAI,aAAa;AACjB,WAAO,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,MAAM,IAAI;AAC9D,UAAI,SAAS,SAAS,MAAM,GAAG;AAC7B,YAAI,CAAC,YAAY;AACf,sBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,QACvB;AACA,oBAAY,GAAG;AACf,iBAAS;AACT,YAAI,SAAS,SAAS,MAAM,KAAK,oBAAoB;AACnD;AAAA,QACF;AAAA,MACF,WAAW,YAAY;AACrB,oBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,MACvB;AACA,UAAI,gBAAgB;AAClB,kBAAU,KAAK,CAAC;AAChB,yBAAiB;AAAA,MACnB,OAAO;AACL,kBAAU,UAAU,SAAS,CAAC;AAAA,MAChC;AACA,UAAI,CAAC,WAAW,GAAG;AACjB,oBAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,MAC3B;AACA,mBAAa;AAAA,IACf;AACA,eAAW;AACX,QAAI,CAAC,gBAAgB;AACnB,gBAAU,IAAI;AAAA,IAChB;AACA,QAAI,SAAS,SAAS,MAAM,GAAG;AAC7B,kBAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,IACxB,OAAO;AACL,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,aAAa;AACpB,YAAQ,SAAS,SAAS,GAAG;AAAA,MAC3B,KAAK;AACH,eAAO,WAAW;AAAA,MACpB,KAAK;AACH,eAAO,YAAY;AAAA,MACrB,KAAK;AACH,eAAO,YAAY,IAAI;AAAA,MACzB;AACE,eAAO,aAAa;AAAA,IACxB;AAAA,EACF;AACA,WAAS;AACT,MAAI,SAAS,SAAS,MAAM,IAAI;AAC9B,QAAI,QAAQ,mBAAmB;AAC7B,aAAO;AAAA,IACT;AACA,gBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,WAAW,GAAG;AACjB,gBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,SAAS,MAAM,IAAI;AAC9B,gBAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA,EACvB;AACA,SAAO;AACT;AAiQA,IAAI;AAAA,CACH,SAAU,YAAY;AACrB,aAAW,WAAW,MAAM,IAAI,CAAC,IAAI;AACrC,aAAW,WAAW,wBAAwB,IAAI,CAAC,IAAI;AACvD,aAAW,WAAW,uBAAuB,IAAI,CAAC,IAAI;AACtD,aAAW,WAAW,uBAAuB,IAAI,CAAC,IAAI;AACtD,aAAW,WAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,aAAW,WAAW,wBAAwB,IAAI,CAAC,IAAI;AACvD,aAAW,WAAW,kBAAkB,IAAI,CAAC,IAAI;AACnD,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA,CACH,SAAU,aAAa;AACtB,cAAY,YAAY,gBAAgB,IAAI,CAAC,IAAI;AACjD,cAAY,YAAY,iBAAiB,IAAI,CAAC,IAAI;AAClD,cAAY,YAAY,kBAAkB,IAAI,CAAC,IAAI;AACnD,cAAY,YAAY,mBAAmB,IAAI,CAAC,IAAI;AACpD,cAAY,YAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,cAAY,YAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,cAAY,YAAY,aAAa,IAAI,CAAC,IAAI;AAC9C,cAAY,YAAY,aAAa,IAAI,CAAC,IAAI;AAC9C,cAAY,YAAY,cAAc,IAAI,CAAC,IAAI;AAC/C,cAAY,YAAY,eAAe,IAAI,EAAE,IAAI;AACjD,cAAY,YAAY,gBAAgB,IAAI,EAAE,IAAI;AAClD,cAAY,YAAY,mBAAmB,IAAI,EAAE,IAAI;AACrD,cAAY,YAAY,oBAAoB,IAAI,EAAE,IAAI;AACtD,cAAY,YAAY,iBAAiB,IAAI,EAAE,IAAI;AACnD,cAAY,YAAY,QAAQ,IAAI,EAAE,IAAI;AAC1C,cAAY,YAAY,SAAS,IAAI,EAAE,IAAI;AAC3C,cAAY,YAAY,KAAK,IAAI,EAAE,IAAI;AACzC,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,IAAIC,UAASC;AAQb,IAAI;AAAA,CACH,SAAU,iBAAiB;AAC1B,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,qBAAqB,IAAI,CAAC,IAAI;AAC9D,kBAAgB,gBAAgB,sBAAsB,IAAI,CAAC,IAAI;AAC/D,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,kBAAgB,gBAAgB,oBAAoB,IAAI,CAAC,IAAI;AAC7D,kBAAgB,gBAAgB,sBAAsB,IAAI,CAAC,IAAI;AAC/D,kBAAgB,gBAAgB,mBAAmB,IAAI,CAAC,IAAI;AAC5D,kBAAgB,gBAAgB,qBAAqB,IAAI,EAAE,IAAI;AAC/D,kBAAgB,gBAAgB,wBAAwB,IAAI,EAAE,IAAI;AAClE,kBAAgB,gBAAgB,uBAAuB,IAAI,EAAE,IAAI;AACjE,kBAAgB,gBAAgB,uBAAuB,IAAI,EAAE,IAAI;AACjE,kBAAgB,gBAAgB,gBAAgB,IAAI,EAAE,IAAI;AAC1D,kBAAgB,gBAAgB,wBAAwB,IAAI,EAAE,IAAI;AAClE,kBAAgB,gBAAgB,kBAAkB,IAAI,EAAE,IAAI;AAC9D,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;;;AC7pD1C,SAASC,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAE7T,SAAS,UAAU,SAAS;AAC1B,SAAO,OAAO,YAAY,eAAe,YAAY;AACvD;AACA,SAAS,SAAS,SAAS;AACzB,SAAOA,SAAQ,OAAO,MAAM,YAAY,YAAY;AACtD;AACA,SAAS,QAAQ,UAAU;AACzB,MAAI,MAAM,QAAQ,QAAQ,EAAG,QAAO;AAAA,WAAkB,UAAU,QAAQ,EAAG,QAAO,CAAC;AACnF,SAAO,CAAC,QAAQ;AAClB;AACA,SAAS,OAAO,QAAQE,SAAQ;AAC9B,MAAI,OAAO,QAAQC,MAAK;AACxB,MAAID,SAAQ;AACV,iBAAa,OAAO,KAAKA,OAAM;AAC/B,SAAK,QAAQ,GAAG,SAAS,WAAW,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACtE,MAAAC,OAAM,WAAW,KAAK;AACtB,aAAOA,IAAG,IAAID,QAAOC,IAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,OAAOC,SAAQ,OAAO;AAC7B,MAAI,SAAS,IACX;AACF,OAAK,QAAQ,GAAG,QAAQ,OAAO,SAAS,GAAG;AACzC,cAAUA;AAAA,EACZ;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,SAAO,WAAW,KAAK,OAAO,sBAAsB,IAAI;AAC1D;AACA,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,WAAW;AACf,IAAI,SAAS;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,QAAQ;AACV;AACA,SAAS,YAAYC,YAAW,SAAS;AACvC,MAAI,QAAQ,IACV,UAAUA,WAAU,UAAU;AAChC,MAAI,CAACA,WAAU,KAAM,QAAO;AAC5B,MAAIA,WAAU,KAAK,MAAM;AACvB,aAAS,SAASA,WAAU,KAAK,OAAO;AAAA,EAC1C;AACA,WAAS,OAAOA,WAAU,KAAK,OAAO,KAAK,OAAOA,WAAU,KAAK,SAAS,KAAK;AAC/E,MAAI,CAAC,WAAWA,WAAU,KAAK,SAAS;AACtC,aAAS,SAASA,WAAU,KAAK;AAAA,EACnC;AACA,SAAO,UAAU,MAAM;AACzB;AACA,SAAS,gBAAgB,QAAQ,MAAM;AACrC,QAAM,KAAK,IAAI;AACf,OAAK,OAAO;AACZ,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,UAAU,YAAY,MAAM,KAAK;AACtC,MAAI,MAAM,mBAAmB;AAC3B,UAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,EAChD,OAAO;AACL,SAAK,QAAQ,IAAI,MAAM,EAAE,SAAS;AAAA,EACpC;AACF;AACA,gBAAgB,YAAY,OAAO,OAAO,MAAM,SAAS;AACzD,gBAAgB,UAAU,cAAc;AACxC,gBAAgB,UAAU,WAAW,SAAS,SAAS,SAAS;AAC9D,SAAO,KAAK,OAAO,OAAO,YAAY,MAAM,OAAO;AACrD;AACA,IAAI,YAAY;AAChB,SAAS,QAAQC,SAAQ,WAAW,SAAS,UAAU,eAAe;AACpE,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,gBAAgB,KAAK,MAAM,gBAAgB,CAAC,IAAI;AACpD,MAAI,WAAW,YAAY,eAAe;AACxC,WAAO;AACP,gBAAY,WAAW,gBAAgB,KAAK;AAAA,EAC9C;AACA,MAAI,UAAU,WAAW,eAAe;AACtC,WAAO;AACP,cAAU,WAAW,gBAAgB,KAAK;AAAA,EAC5C;AACA,SAAO;AAAA,IACL,KAAK,OAAOA,QAAO,MAAM,WAAW,OAAO,EAAE,QAAQ,OAAO,GAAG,IAAI;AAAA,IACnE,KAAK,WAAW,YAAY,KAAK;AAAA,EACnC;AACF;AACA,SAAS,SAASF,SAAQ,KAAK;AAC7B,SAAO,OAAO,OAAO,KAAK,MAAMA,QAAO,MAAM,IAAIA;AACnD;AACA,SAAS,YAAY,MAAM,SAAS;AAClC,YAAU,OAAO,OAAO,WAAW,IAAI;AACvC,MAAI,CAAC,KAAK,OAAQ,QAAO;AACzB,MAAI,CAAC,QAAQ,UAAW,SAAQ,YAAY;AAC5C,MAAI,OAAO,QAAQ,WAAW,SAAU,SAAQ,SAAS;AACzD,MAAI,OAAO,QAAQ,gBAAgB,SAAU,SAAQ,cAAc;AACnE,MAAI,OAAO,QAAQ,eAAe,SAAU,SAAQ,aAAa;AACjE,MAAI,KAAK;AACT,MAAI,aAAa,CAAC,CAAC;AACnB,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,cAAc;AAClB,SAAO,QAAQ,GAAG,KAAK,KAAK,MAAM,GAAG;AACnC,aAAS,KAAK,MAAM,KAAK;AACzB,eAAW,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM;AAC7C,QAAI,KAAK,YAAY,MAAM,SAAS,cAAc,GAAG;AACnD,oBAAc,WAAW,SAAS;AAAA,IACpC;AAAA,EACF;AACA,MAAI,cAAc,EAAG,eAAc,WAAW,SAAS;AACvD,MAAI,SAAS,IACX,GACAG;AACF,MAAI,eAAe,KAAK,IAAI,KAAK,OAAO,QAAQ,YAAY,SAAS,MAAM,EAAE,SAAS,EAAE;AACxF,MAAI,gBAAgB,QAAQ,aAAa,QAAQ,SAAS,eAAe;AACzE,OAAK,IAAI,GAAG,KAAK,QAAQ,aAAa,KAAK;AACzC,QAAI,cAAc,IAAI,EAAG;AACzB,IAAAA,QAAO,QAAQ,KAAK,QAAQ,WAAW,cAAc,CAAC,GAAG,SAAS,cAAc,CAAC,GAAG,KAAK,YAAY,WAAW,WAAW,IAAI,WAAW,cAAc,CAAC,IAAI,aAAa;AAC1K,aAAS,OAAO,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU,KAAK,OAAO,IAAI,GAAG,SAAS,GAAG,YAAY,IAAI,QAAQA,MAAK,MAAM,OAAO;AAAA,EACnI;AACA,EAAAA,QAAO,QAAQ,KAAK,QAAQ,WAAW,WAAW,GAAG,SAAS,WAAW,GAAG,KAAK,UAAU,aAAa;AACxG,YAAU,OAAO,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU,KAAK,OAAO,GAAG,SAAS,GAAG,YAAY,IAAI,QAAQA,MAAK,MAAM;AACvH,YAAU,OAAO,OAAO,KAAK,QAAQ,SAAS,eAAe,IAAIA,MAAK,GAAG,IAAI;AAC7E,OAAK,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK;AACxC,QAAI,cAAc,KAAK,SAAS,OAAQ;AACxC,IAAAA,QAAO,QAAQ,KAAK,QAAQ,WAAW,cAAc,CAAC,GAAG,SAAS,cAAc,CAAC,GAAG,KAAK,YAAY,WAAW,WAAW,IAAI,WAAW,cAAc,CAAC,IAAI,aAAa;AAC1K,cAAU,OAAO,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU,KAAK,OAAO,IAAI,GAAG,SAAS,GAAG,YAAY,IAAI,QAAQA,MAAK,MAAM;AAAA,EAC7H;AACA,SAAO,OAAO,QAAQ,OAAO,EAAE;AACjC;AACA,IAAI,UAAU;AACd,IAAI,2BAA2B,CAAC,QAAQ,SAAS,WAAW,aAAa,cAAc,aAAa,aAAa,iBAAiB,gBAAgB,cAAc;AAChK,IAAI,kBAAkB,CAAC,UAAU,YAAY,SAAS;AACtD,SAAS,oBAAoBC,MAAK;AAChC,MAAI,SAAS,CAAC;AACd,MAAIA,SAAQ,MAAM;AAChB,WAAO,KAAKA,IAAG,EAAE,QAAQ,SAAU,OAAO;AACxC,MAAAA,KAAI,KAAK,EAAE,QAAQ,SAAU,OAAO;AAClC,eAAO,OAAO,KAAK,CAAC,IAAI;AAAA,MAC1B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,OAAO,KAAK,SAAS;AAC5B,YAAU,WAAW,CAAC;AACtB,SAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,MAAM;AAC3C,QAAI,yBAAyB,QAAQ,IAAI,MAAM,IAAI;AACjD,YAAM,IAAI,UAAU,qBAAqB,OAAO,gCAAgC,MAAM,cAAc;AAAA,IACtG;AAAA,EACF,CAAC;AACD,OAAK,UAAU;AACf,OAAK,MAAM;AACX,OAAK,OAAO,QAAQ,MAAM,KAAK;AAC/B,OAAK,UAAU,QAAQ,SAAS,KAAK,WAAY;AAC/C,WAAO;AAAA,EACT;AACA,OAAK,YAAY,QAAQ,WAAW,KAAK,SAAU,MAAM;AACvD,WAAO;AAAA,EACT;AACA,OAAK,aAAa,QAAQ,YAAY,KAAK;AAC3C,OAAK,YAAY,QAAQ,WAAW,KAAK;AACzC,OAAK,YAAY,QAAQ,WAAW,KAAK;AACzC,OAAK,gBAAgB,QAAQ,eAAe,KAAK;AACjD,OAAK,eAAe,QAAQ,cAAc,KAAK;AAC/C,OAAK,QAAQ,QAAQ,OAAO,KAAK;AACjC,OAAK,eAAe,oBAAoB,QAAQ,cAAc,KAAK,IAAI;AACvE,MAAI,gBAAgB,QAAQ,KAAK,IAAI,MAAM,IAAI;AAC7C,UAAM,IAAI,UAAU,mBAAmB,KAAK,OAAO,yBAAyB,MAAM,cAAc;AAAA,EAClG;AACF;AACA,IAAI,OAAO;AACX,SAAS,YAAYC,SAAQ,MAAM;AACjC,MAAI,SAAS,CAAC;AACd,EAAAA,QAAO,IAAI,EAAE,QAAQ,SAAU,aAAa;AAC1C,QAAI,WAAW,OAAO;AACtB,WAAO,QAAQ,SAAU,cAAc,eAAe;AACpD,UAAI,aAAa,QAAQ,YAAY,OAAO,aAAa,SAAS,YAAY,QAAQ,aAAa,UAAU,YAAY,OAAO;AAC9H,mBAAW;AAAA,MACb;AAAA,IACF,CAAC;AACD,WAAO,QAAQ,IAAI;AAAA,EACrB,CAAC;AACD,SAAO;AACT;AACA,SAAS,aAAa;AACpB,MAAI,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,IACV,UAAU,CAAC;AAAA,IACX,OAAO;AAAA,MACL,QAAQ,CAAC;AAAA,MACT,UAAU,CAAC;AAAA,MACX,SAAS,CAAC;AAAA,MACV,UAAU,CAAC;AAAA,IACb;AAAA,EACF,GACA,OACA;AACF,WAAS,YAAYC,OAAM;AACzB,QAAIA,MAAK,OAAO;AACd,aAAO,MAAMA,MAAK,IAAI,EAAE,KAAKA,KAAI;AACjC,aAAO,MAAM,UAAU,EAAE,KAAKA,KAAI;AAAA,IACpC,OAAO;AACL,aAAOA,MAAK,IAAI,EAAEA,MAAK,GAAG,IAAI,OAAO,UAAU,EAAEA,MAAK,GAAG,IAAIA;AAAA,IAC/D;AAAA,EACF;AACA,OAAK,QAAQ,GAAG,SAAS,UAAU,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACrE,cAAU,KAAK,EAAE,QAAQ,WAAW;AAAA,EACtC;AACA,SAAO;AACT;AACA,SAAS,SAAS,YAAY;AAC5B,SAAO,KAAK,OAAO,UAAU;AAC/B;AACA,SAAS,UAAU,SAAS,SAASC,QAAO,YAAY;AACtD,MAAI,WAAW,CAAC;AAChB,MAAI,WAAW,CAAC;AAChB,MAAI,sBAAsB,MAAM;AAC9B,aAAS,KAAK,UAAU;AAAA,EAC1B,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,eAAW,SAAS,OAAO,UAAU;AAAA,EACvC,WAAW,eAAe,MAAM,QAAQ,WAAW,QAAQ,KAAK,MAAM,QAAQ,WAAW,QAAQ,IAAI;AACnG,QAAI,WAAW,SAAU,YAAW,SAAS,OAAO,WAAW,QAAQ;AACvE,QAAI,WAAW,SAAU,YAAW,SAAS,OAAO,WAAW,QAAQ;AAAA,EACzE,OAAO;AACL,UAAM,IAAI,UAAU,kHAAuH;AAAA,EAC7I;AACA,WAAS,QAAQ,SAAU,QAAQ;AACjC,QAAI,EAAE,kBAAkB,OAAO;AAC7B,YAAM,IAAI,UAAU,oFAAoF;AAAA,IAC1G;AACA,QAAI,OAAO,YAAY,OAAO,aAAa,UAAU;AACnD,YAAM,IAAI,UAAU,iHAAiH;AAAA,IACvI;AACA,QAAI,OAAO,OAAO;AAChB,YAAM,IAAI,UAAU,oGAAoG;AAAA,IAC1H;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,SAAU,QAAQ;AACjC,QAAI,EAAE,kBAAkB,OAAO;AAC7B,YAAM,IAAI,UAAU,oFAAoF;AAAA,IAC1G;AAAA,EACF,CAAC;AACD,MAAI,SAAS,OAAO,OAAO,SAAS,SAAS;AAC7C,SAAO,YAAY,KAAK,YAAY,CAAC,GAAG,OAAO,QAAQ;AACvD,SAAO,YAAY,KAAK,YAAY,CAAC,GAAG,OAAO,QAAQ;AACvD,SAAO,mBAAmB,YAAY,QAAQ,UAAU;AACxD,SAAO,mBAAmB,YAAY,QAAQ,UAAU;AACxD,SAAO,kBAAkB,WAAW,OAAO,kBAAkB,OAAO,gBAAgB;AACpF,SAAO;AACT;AACA,IAAI,SAAS;AACb,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW,SAAS,UAAU,MAAM;AAClC,WAAO,SAAS,OAAO,OAAO;AAAA,EAChC;AACF,CAAC;AACD,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW,SAASC,WAAU,MAAM;AAClC,WAAO,SAAS,OAAO,OAAO,CAAC;AAAA,EACjC;AACF,CAAC;AACD,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW,SAASA,WAAU,MAAM;AAClC,WAAO,SAAS,OAAO,OAAO,CAAC;AAAA,EACjC;AACF,CAAC;AACD,IAAI,WAAW,IAAI,OAAO;AAAA,EACxB,UAAU,CAAC,KAAK,KAAK,GAAG;AAC1B,CAAC;AACD,SAAS,gBAAgB,MAAM;AAC7B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,MAAM,KAAK;AACf,SAAO,QAAQ,KAAK,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,SAAS,UAAU,SAAS;AACnG;AACA,SAAS,oBAAoB;AAC3B,SAAO;AACT;AACA,SAAS,OAAO,QAAQ;AACtB,SAAO,WAAW;AACpB;AACA,IAAI,QAAQ,IAAI,KAAK,0BAA0B;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,WAAW,SAAS,YAAY;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,YAAY;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,YAAY;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,YAAY;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc;AAChB,CAAC;AACD,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,MAAM,KAAK;AACf,SAAO,QAAQ,MAAM,SAAS,UAAU,SAAS,UAAU,SAAS,WAAW,QAAQ,MAAM,SAAS,WAAW,SAAS,WAAW,SAAS;AAChJ;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO,SAAS,UAAU,SAAS,UAAU,SAAS;AACxD;AACA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM;AACpD;AACA,IAAI,OAAO,IAAI,KAAK,0BAA0B;AAAA,EAC5C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,WAAW,SAASC,WAAU,QAAQ;AACpC,aAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,IACA,WAAW,SAASC,WAAU,QAAQ;AACpC,aAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,IACA,WAAW,SAASC,WAAU,QAAQ;AACpC,aAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,cAAc;AAChB,CAAC;AACD,SAAS,UAAUC,IAAG;AACpB,SAAO,MAAQA,MAAKA,MAAK,MAAQ,MAAQA,MAAKA,MAAK,MAAQ,MAAQA,MAAKA,MAAK;AAC/E;AACA,SAAS,UAAUA,IAAG;AACpB,SAAO,MAAQA,MAAKA,MAAK;AAC3B;AACA,SAAS,UAAUA,IAAG;AACpB,SAAO,MAAQA,MAAKA,MAAK;AAC3B;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,MAAM,KAAK,QACb,QAAQ,GACR,YAAY,OACZ;AACF,MAAI,CAAC,IAAK,QAAO;AACjB,OAAK,KAAK,KAAK;AACf,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,SAAK,KAAK,EAAE,KAAK;AAAA,EACnB;AACA,MAAI,OAAO,KAAK;AACd,QAAI,QAAQ,MAAM,IAAK,QAAO;AAC9B,SAAK,KAAK,EAAE,KAAK;AACjB,QAAI,OAAO,KAAK;AACd;AACA,aAAO,QAAQ,KAAK,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,YAAI,OAAO,IAAK;AAChB,YAAI,OAAO,OAAO,OAAO,IAAK,QAAO;AACrC,oBAAY;AAAA,MACd;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AACA,QAAI,OAAO,KAAK;AACd;AACA,aAAO,QAAQ,KAAK,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,YAAI,OAAO,IAAK;AAChB,YAAI,CAAC,UAAU,KAAK,WAAW,KAAK,CAAC,EAAG,QAAO;AAC/C,oBAAY;AAAA,MACd;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AACA,QAAI,OAAO,KAAK;AACd;AACA,aAAO,QAAQ,KAAK,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,YAAI,OAAO,IAAK;AAChB,YAAI,CAAC,UAAU,KAAK,WAAW,KAAK,CAAC,EAAG,QAAO;AAC/C,oBAAY;AAAA,MACd;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,OAAO,IAAK,QAAO;AACvB,SAAO,QAAQ,KAAK,SAAS;AAC3B,SAAK,KAAK,KAAK;AACf,QAAI,OAAO,IAAK;AAChB,QAAI,CAAC,UAAU,KAAK,WAAW,KAAK,CAAC,GAAG;AACtC,aAAO;AAAA,IACT;AACA,gBAAY;AAAA,EACd;AACA,MAAI,CAAC,aAAa,OAAO,IAAK,QAAO;AACrC,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAClC,MAAIC,SAAQ,MACVC,QAAO,GACP;AACF,MAAID,OAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,IAAAA,SAAQA,OAAM,QAAQ,MAAM,EAAE;AAAA,EAChC;AACA,OAAKA,OAAM,CAAC;AACZ,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,QAAI,OAAO,IAAK,CAAAC,QAAO;AACvB,IAAAD,SAAQA,OAAM,MAAM,CAAC;AACrB,SAAKA,OAAM,CAAC;AAAA,EACd;AACA,MAAIA,WAAU,IAAK,QAAO;AAC1B,MAAI,OAAO,KAAK;AACd,QAAIA,OAAM,CAAC,MAAM,IAAK,QAAOC,QAAO,SAASD,OAAM,MAAM,CAAC,GAAG,CAAC;AAC9D,QAAIA,OAAM,CAAC,MAAM,IAAK,QAAOC,QAAO,SAASD,OAAM,MAAM,CAAC,GAAG,EAAE;AAC/D,QAAIA,OAAM,CAAC,MAAM,IAAK,QAAOC,QAAO,SAASD,OAAM,MAAM,CAAC,GAAG,CAAC;AAAA,EAChE;AACA,SAAOC,QAAO,SAASD,QAAO,EAAE;AAClC;AACA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,qBAAqB,SAAS,MAAM,KAAK,CAAC,OAAO,eAAe,MAAM;AAC1H;AACA,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,QAAQ,SAAS,OAAO,KAAK;AAC3B,aAAO,OAAO,IAAI,OAAO,IAAI,SAAS,CAAC,IAAI,QAAQ,IAAI,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,IAC5E;AAAA,IACA,OAAO,SAAS,MAAM,KAAK;AACzB,aAAO,OAAO,IAAI,OAAO,IAAI,SAAS,CAAC,IAAI,QAAQ,IAAI,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,IAC5E;AAAA,IACA,SAAS,SAAS,QAAQ,KAAK;AAC7B,aAAO,IAAI,SAAS,EAAE;AAAA,IACxB;AAAA,IACA,aAAa,SAASE,aAAY,KAAK;AACrC,aAAO,OAAO,IAAI,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY,IAAI,QAAQ,IAAI,SAAS,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC;AAAA,IAC1G;AAAA,EACF;AAAA,EACA,cAAc;AAAA,EACd,cAAc;AAAA,IACZ,QAAQ,CAAC,GAAG,KAAK;AAAA,IACjB,OAAO,CAAC,GAAG,KAAK;AAAA,IAChB,SAAS,CAAC,IAAI,KAAK;AAAA,IACnB,aAAa,CAAC,IAAI,KAAK;AAAA,EACzB;AACF,CAAC;AACD,IAAI,qBAAqB,IAAI,OAAO,0IAAyJ;AAC7L,SAAS,iBAAiB,MAAM;AAC9B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,CAAC,mBAAmB,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,CAAC,MAAM,KAAK;AACnE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAIF,QAAOC;AACX,EAAAD,SAAQ,KAAK,QAAQ,MAAM,EAAE,EAAE,YAAY;AAC3C,EAAAC,QAAOD,OAAM,CAAC,MAAM,MAAM,KAAK;AAC/B,MAAI,KAAK,QAAQA,OAAM,CAAC,CAAC,KAAK,GAAG;AAC/B,IAAAA,SAAQA,OAAM,MAAM,CAAC;AAAA,EACvB;AACA,MAAIA,WAAU,QAAQ;AACpB,WAAOC,UAAS,IAAI,OAAO,oBAAoB,OAAO;AAAA,EACxD,WAAWD,WAAU,QAAQ;AAC3B,WAAO;AAAA,EACT;AACA,SAAOC,QAAO,WAAWD,QAAO,EAAE;AACpC;AACA,IAAI,yBAAyB;AAC7B,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI;AACJ,MAAI,MAAM,MAAM,GAAG;AACjB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACX;AAAA,EACF,WAAW,OAAO,sBAAsB,QAAQ;AAC9C,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACX;AAAA,EACF,WAAW,OAAO,sBAAsB,QAAQ;AAC9C,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACX;AAAA,EACF,WAAW,OAAO,eAAe,MAAM,GAAG;AACxC,WAAO;AAAA,EACT;AACA,QAAM,OAAO,SAAS,EAAE;AACxB,SAAO,uBAAuB,KAAK,GAAG,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI;AACrE;AACA,SAAS,QAAQ,QAAQ;AACvB,SAAO,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,sBAAsB,SAAS,MAAM,KAAK,OAAO,eAAe,MAAM;AAC1H;AACA,IAAI,QAAQ,IAAI,KAAK,2BAA2B;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAChB,CAAC;AACD,IAAI,OAAO,SAAS,OAAO;AAAA,EACzB,UAAU,CAAC,OAAO,MAAM,KAAK,KAAK;AACpC,CAAC;AACD,IAAI,OAAO;AACX,IAAI,mBAAmB,IAAI,OAAO,oDAA8D;AAChG,IAAI,wBAAwB,IAAI,OAAO,kLAA+N;AACtQ,SAAS,qBAAqB,MAAM;AAClC,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,iBAAiB,KAAK,IAAI,MAAM,KAAM,QAAO;AACjD,MAAI,sBAAsB,KAAK,IAAI,MAAM,KAAM,QAAO;AACtD,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM;AACpC,MAAI,OACF,MACA,OACA,KACA,MACA,QACA,QACA,WAAW,GACX,QAAQ,MACR,SACA,WACA;AACF,UAAQ,iBAAiB,KAAK,IAAI;AAClC,MAAI,UAAU,KAAM,SAAQ,sBAAsB,KAAK,IAAI;AAC3D,MAAI,UAAU,KAAM,OAAM,IAAI,MAAM,oBAAoB;AACxD,SAAO,CAAC,MAAM,CAAC;AACf,UAAQ,CAAC,MAAM,CAAC,IAAI;AACpB,QAAM,CAAC,MAAM,CAAC;AACd,MAAI,CAAC,MAAM,CAAC,GAAG;AACb,WAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,EAC5C;AACA,SAAO,CAAC,MAAM,CAAC;AACf,WAAS,CAAC,MAAM,CAAC;AACjB,WAAS,CAAC,MAAM,CAAC;AACjB,MAAI,MAAM,CAAC,GAAG;AACZ,eAAW,MAAM,CAAC,EAAE,MAAM,GAAG,CAAC;AAC9B,WAAO,SAAS,SAAS,GAAG;AAC1B,kBAAY;AAAA,IACd;AACA,eAAW,CAAC;AAAA,EACd;AACA,MAAI,MAAM,CAAC,GAAG;AACZ,cAAU,CAAC,MAAM,EAAE;AACnB,gBAAY,EAAE,MAAM,EAAE,KAAK;AAC3B,aAAS,UAAU,KAAK,aAAa;AACrC,QAAI,MAAM,CAAC,MAAM,IAAK,SAAQ,CAAC;AAAA,EACjC;AACA,SAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,QAAQ,CAAC;AAC1E,MAAI,MAAO,MAAK,QAAQ,KAAK,QAAQ,IAAI,KAAK;AAC9C,SAAO;AACT;AACA,SAAS,uBAAuB,QAAQ;AACtC,SAAO,OAAO,YAAY;AAC5B;AACA,IAAI,YAAY,IAAI,KAAK,+BAA+B;AAAA,EACtD,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AACb,CAAC;AACD,SAAS,iBAAiB,MAAM;AAC9B,SAAO,SAAS,QAAQ,SAAS;AACnC;AACA,IAAI,QAAQ,IAAI,KAAK,2BAA2B;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AACX,CAAC;AACD,IAAI,aAAa;AACjB,SAAS,kBAAkB,MAAM;AAC/B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,MACF,KACA,SAAS,GACT,MAAM,KAAK,QACXT,OAAM;AACR,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,WAAOA,KAAI,QAAQ,KAAK,OAAO,GAAG,CAAC;AACnC,QAAI,OAAO,GAAI;AACf,QAAI,OAAO,EAAG,QAAO;AACrB,cAAU;AAAA,EACZ;AACA,SAAO,SAAS,MAAM;AACxB;AACA,SAAS,oBAAoB,MAAM;AACjC,MAAI,KACF,UACA,QAAQ,KAAK,QAAQ,YAAY,EAAE,GACnC,MAAM,MAAM,QACZA,OAAM,YACN,OAAO,GACP,SAAS,CAAC;AACZ,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,QAAI,MAAM,MAAM,KAAK,KAAK;AACxB,aAAO,KAAK,QAAQ,KAAK,GAAI;AAC7B,aAAO,KAAK,QAAQ,IAAI,GAAI;AAC5B,aAAO,KAAK,OAAO,GAAI;AAAA,IACzB;AACA,WAAO,QAAQ,IAAIA,KAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AAAA,EAClD;AACA,aAAW,MAAM,IAAI;AACrB,MAAI,aAAa,GAAG;AAClB,WAAO,KAAK,QAAQ,KAAK,GAAI;AAC7B,WAAO,KAAK,QAAQ,IAAI,GAAI;AAC5B,WAAO,KAAK,OAAO,GAAI;AAAA,EACzB,WAAW,aAAa,IAAI;AAC1B,WAAO,KAAK,QAAQ,KAAK,GAAI;AAC7B,WAAO,KAAK,QAAQ,IAAI,GAAI;AAAA,EAC9B,WAAW,aAAa,IAAI;AAC1B,WAAO,KAAK,QAAQ,IAAI,GAAI;AAAA,EAC9B;AACA,SAAO,IAAI,WAAW,MAAM;AAC9B;AACA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,SAAS,IACX,OAAO,GACP,KACA,MACA,MAAM,OAAO,QACbA,OAAM;AACR,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,QAAI,MAAM,MAAM,KAAK,KAAK;AACxB,gBAAUA,KAAI,QAAQ,KAAK,EAAI;AAC/B,gBAAUA,KAAI,QAAQ,KAAK,EAAI;AAC/B,gBAAUA,KAAI,QAAQ,IAAI,EAAI;AAC9B,gBAAUA,KAAI,OAAO,EAAI;AAAA,IAC3B;AACA,YAAQ,QAAQ,KAAK,OAAO,GAAG;AAAA,EACjC;AACA,SAAO,MAAM;AACb,MAAI,SAAS,GAAG;AACd,cAAUA,KAAI,QAAQ,KAAK,EAAI;AAC/B,cAAUA,KAAI,QAAQ,KAAK,EAAI;AAC/B,cAAUA,KAAI,QAAQ,IAAI,EAAI;AAC9B,cAAUA,KAAI,OAAO,EAAI;AAAA,EAC3B,WAAW,SAAS,GAAG;AACrB,cAAUA,KAAI,QAAQ,KAAK,EAAI;AAC/B,cAAUA,KAAI,QAAQ,IAAI,EAAI;AAC9B,cAAUA,KAAI,QAAQ,IAAI,EAAI;AAC9B,cAAUA,KAAI,EAAE;AAAA,EAClB,WAAW,SAAS,GAAG;AACrB,cAAUA,KAAI,QAAQ,IAAI,EAAI;AAC9B,cAAUA,KAAI,QAAQ,IAAI,EAAI;AAC9B,cAAUA,KAAI,EAAE;AAChB,cAAUA,KAAI,EAAE;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AACA,IAAIY,UAAS,IAAI,KAAK,4BAA4B;AAAA,EAChD,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACb,CAAC;AACD,IAAI,oBAAoB,OAAO,UAAU;AACzC,IAAI,cAAc,OAAO,UAAU;AACnC,SAAS,gBAAgB,MAAM;AAC7B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,aAAa,CAAC,GAChB,OACA,QACA,MACA,SACA,YACA,SAAS;AACX,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,WAAO,OAAO,KAAK;AACnB,iBAAa;AACb,QAAI,YAAY,KAAK,IAAI,MAAM,kBAAmB,QAAO;AACzD,SAAK,WAAW,MAAM;AACpB,UAAI,kBAAkB,KAAK,MAAM,OAAO,GAAG;AACzC,YAAI,CAAC,WAAY,cAAa;AAAA,YAAU,QAAO;AAAA,MACjD;AAAA,IACF;AACA,QAAI,CAAC,WAAY,QAAO;AACxB,QAAI,WAAW,QAAQ,OAAO,MAAM,GAAI,YAAW,KAAK,OAAO;AAAA,QAAO,QAAO;AAAA,EAC/E;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM;AAC/B,SAAO,SAAS,OAAO,OAAO,CAAC;AACjC;AACA,IAAI,OAAO,IAAI,KAAK,0BAA0B;AAAA,EAC5C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,CAAC;AACD,IAAI,cAAc,OAAO,UAAU;AACnC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,OACF,QACA,MACA,MACA,QACA,SAAS;AACX,WAAS,IAAI,MAAM,OAAO,MAAM;AAChC,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,WAAO,OAAO,KAAK;AACnB,QAAI,YAAY,KAAK,IAAI,MAAM,kBAAmB,QAAO;AACzD,WAAO,OAAO,KAAK,IAAI;AACvB,QAAI,KAAK,WAAW,EAAG,QAAO;AAC9B,WAAO,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAM,QAAO,CAAC;AAC3B,MAAI,OACF,QACA,MACA,MACA,QACA,SAAS;AACX,WAAS,IAAI,MAAM,OAAO,MAAM;AAChC,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,WAAO,OAAO,KAAK;AACnB,WAAO,OAAO,KAAK,IAAI;AACvB,WAAO,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,EACzC;AACA,SAAO;AACT;AACA,IAAI,QAAQ,IAAI,KAAK,2BAA2B;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,CAAC;AACD,IAAI,oBAAoB,OAAO,UAAU;AACzC,SAAS,eAAe,MAAM;AAC5B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAIjB,MACF,SAAS;AACX,OAAKA,QAAO,QAAQ;AAClB,QAAI,kBAAkB,KAAK,QAAQA,IAAG,GAAG;AACvC,UAAI,OAAOA,IAAG,MAAM,KAAM,QAAO;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,SAAS,OAAO,OAAO,CAAC;AACjC;AACA,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,CAAC;AACD,IAAIkB,YAAW,KAAK,OAAO;AAAA,EACzB,UAAU,CAAC,WAAW,KAAK;AAAA,EAC3B,UAAU,CAACD,SAAQ,MAAM,OAAO,GAAG;AACrC,CAAC;AACD,IAAI,oBAAoB,OAAO,UAAU;AACzC,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,wBAAwB;AAC5B,IAAI,gCAAgC;AACpC,IAAI,0BAA0B;AAC9B,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,SAAS,OAAO,KAAK;AACnB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAC3C;AACA,SAAS,OAAOJ,IAAG;AACjB,SAAOA,OAAM,MAAQA,OAAM;AAC7B;AACA,SAAS,eAAeA,IAAG;AACzB,SAAOA,OAAM,KAAQA,OAAM;AAC7B;AACA,SAAS,aAAaA,IAAG;AACvB,SAAOA,OAAM,KAAQA,OAAM,MAAQA,OAAM,MAAQA,OAAM;AACzD;AACA,SAAS,kBAAkBA,IAAG;AAC5B,SAAOA,OAAM,MAAQA,OAAM,MAAQA,OAAM,MAAQA,OAAM,OAAQA,OAAM;AACvE;AACA,SAAS,YAAYA,IAAG;AACtB,MAAI;AACJ,MAAI,MAAQA,MAAKA,MAAK,IAAM;AAC1B,WAAOA,KAAI;AAAA,EACb;AACA,OAAKA,KAAI;AACT,MAAI,MAAQ,MAAM,MAAM,KAAM;AAC5B,WAAO,KAAK,KAAO;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,cAAcA,IAAG;AACxB,MAAIA,OAAM,KAAM;AACd,WAAO;AAAA,EACT;AACA,MAAIA,OAAM,KAAM;AACd,WAAO;AAAA,EACT;AACA,MAAIA,OAAM,IAAM;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,gBAAgBA,IAAG;AAC1B,MAAI,MAAQA,MAAKA,MAAK,IAAM;AAC1B,WAAOA,KAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,qBAAqBA,IAAG;AAC/B,SAAOA,OAAM,KAAO,OAASA,OAAM,KAAO,SAASA,OAAM,KAAO,OAASA,OAAM,MAAO,MAASA,OAAM,IAAO,MAASA,OAAM,MAAO,OAASA,OAAM,MAAO,OAASA,OAAM,MAAO,OAASA,OAAM,MAAO,OAASA,OAAM,MAAO,SAASA,OAAM,KAAO,MAAMA,OAAM,KAAO,MAASA,OAAM,KAAO,MAAMA,OAAM,KAAO,OAASA,OAAM,KAAO,MAASA,OAAM,KAAO,MAASA,OAAM,KAAO,WAAWA,OAAM,KAAO,WAAW;AACnZ;AACA,SAAS,kBAAkBA,IAAG;AAC5B,MAAIA,MAAK,OAAQ;AACf,WAAO,OAAO,aAAaA,EAAC;AAAA,EAC9B;AACA,SAAO,OAAO,cAAcA,KAAI,SAAY,MAAM,QAASA,KAAI,QAAW,QAAU,KAAM;AAC5F;AACA,IAAI,oBAAoB,IAAI,MAAM,GAAG;AACrC,IAAI,kBAAkB,IAAI,MAAM,GAAG;AACnC,KAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,oBAAkB,CAAC,IAAI,qBAAqB,CAAC,IAAI,IAAI;AACrD,kBAAgB,CAAC,IAAI,qBAAqB,CAAC;AAC7C;AAHS;AAIT,SAAS,QAAQ,OAAO,SAAS;AAC/B,OAAK,QAAQ;AACb,OAAK,WAAW,QAAQ,UAAU,KAAK;AACvC,OAAK,SAAS,QAAQ,QAAQ,KAAKK;AACnC,OAAK,YAAY,QAAQ,WAAW,KAAK;AACzC,OAAK,SAAS,QAAQ,QAAQ,KAAK;AACnC,OAAK,OAAO,QAAQ,MAAM,KAAK;AAC/B,OAAK,WAAW,QAAQ,UAAU,KAAK;AACvC,OAAK,gBAAgB,KAAK,OAAO;AACjC,OAAK,UAAU,KAAK,OAAO;AAC3B,OAAK,SAAS,MAAM;AACpB,OAAK,WAAW;AAChB,OAAK,OAAO;AACZ,OAAK,YAAY;AACjB,OAAK,aAAa;AAClB,OAAK,iBAAiB;AACtB,OAAK,YAAY,CAAC;AACpB;AACA,SAAS,cAAc,OAAO,SAAS;AACrC,MAAI,OAAO;AAAA,IACT,MAAM,MAAM;AAAA,IACZ,QAAQ,MAAM,MAAM,MAAM,GAAG,EAAE;AAAA,IAC/B,UAAU,MAAM;AAAA,IAChB,MAAM,MAAM;AAAA,IACZ,QAAQ,MAAM,WAAW,MAAM;AAAA,EACjC;AACA,OAAK,UAAU,QAAQ,IAAI;AAC3B,SAAO,IAAI,UAAU,SAAS,IAAI;AACpC;AACA,SAAS,WAAW,OAAO,SAAS;AAClC,QAAM,cAAc,OAAO,OAAO;AACpC;AACA,SAAS,aAAa,OAAO,SAAS;AACpC,MAAI,MAAM,WAAW;AACnB,UAAM,UAAU,KAAK,MAAM,cAAc,OAAO,OAAO,CAAC;AAAA,EAC1D;AACF;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM,SAAS,oBAAoB,OAAO,MAAM,MAAM;AACpD,QAAI,OAAO,OAAO;AAClB,QAAI,MAAM,YAAY,MAAM;AAC1B,iBAAW,OAAO,gCAAgC;AAAA,IACpD;AACA,QAAI,KAAK,WAAW,GAAG;AACrB,iBAAW,OAAO,6CAA6C;AAAA,IACjE;AACA,YAAQ,uBAAuB,KAAK,KAAK,CAAC,CAAC;AAC3C,QAAI,UAAU,MAAM;AAClB,iBAAW,OAAO,2CAA2C;AAAA,IAC/D;AACA,YAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AAC7B,YAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AAC7B,QAAI,UAAU,GAAG;AACf,iBAAW,OAAO,2CAA2C;AAAA,IAC/D;AACA,UAAM,UAAU,KAAK,CAAC;AACtB,UAAM,kBAAkB,QAAQ;AAChC,QAAI,UAAU,KAAK,UAAU,GAAG;AAC9B,mBAAa,OAAO,0CAA0C;AAAA,IAChE;AAAA,EACF;AAAA,EACA,KAAK,SAAS,mBAAmB,OAAO,MAAM,MAAM;AAClD,QAAI,QAAQ;AACZ,QAAI,KAAK,WAAW,GAAG;AACrB,iBAAW,OAAO,6CAA6C;AAAA,IACjE;AACA,aAAS,KAAK,CAAC;AACf,aAAS,KAAK,CAAC;AACf,QAAI,CAAC,mBAAmB,KAAK,MAAM,GAAG;AACpC,iBAAW,OAAO,6DAA6D;AAAA,IACjF;AACA,QAAI,kBAAkB,KAAK,MAAM,QAAQ,MAAM,GAAG;AAChD,iBAAW,OAAO,gDAAgD,SAAS,cAAc;AAAA,IAC3F;AACA,QAAI,CAAC,gBAAgB,KAAK,MAAM,GAAG;AACjC,iBAAW,OAAO,8DAA8D;AAAA,IAClF;AACA,QAAI;AACF,eAAS,mBAAmB,MAAM;AAAA,IACpC,SAAS,KAAK;AACZ,iBAAW,OAAO,8BAA8B,MAAM;AAAA,IACxD;AACA,UAAM,OAAO,MAAM,IAAI;AAAA,EACzB;AACF;AACA,SAAS,eAAe,OAAOC,QAAOC,MAAK,WAAW;AACpD,MAAI,WAAW,SAAS,YAAY;AACpC,MAAID,SAAQC,MAAK;AACf,cAAU,MAAM,MAAM,MAAMD,QAAOC,IAAG;AACtC,QAAI,WAAW;AACb,WAAK,YAAY,GAAG,UAAU,QAAQ,QAAQ,YAAY,SAAS,aAAa,GAAG;AACjF,qBAAa,QAAQ,WAAW,SAAS;AACzC,YAAI,EAAE,eAAe,KAAQ,MAAQ,cAAc,cAAc,UAAW;AAC1E,qBAAW,OAAO,+BAA+B;AAAA,QACnD;AAAA,MACF;AAAA,IACF,WAAW,sBAAsB,KAAK,OAAO,GAAG;AAC9C,iBAAW,OAAO,8CAA8C;AAAA,IAClE;AACA,UAAM,UAAU;AAAA,EAClB;AACF;AACA,SAAS,cAAc,OAAO,aAAarB,SAAQ,iBAAiB;AAClE,MAAI,YAAYC,MAAK,OAAO;AAC5B,MAAI,CAAC,OAAO,SAASD,OAAM,GAAG;AAC5B,eAAW,OAAO,mEAAmE;AAAA,EACvF;AACA,eAAa,OAAO,KAAKA,OAAM;AAC/B,OAAK,QAAQ,GAAG,WAAW,WAAW,QAAQ,QAAQ,UAAU,SAAS,GAAG;AAC1E,IAAAC,OAAM,WAAW,KAAK;AACtB,QAAI,CAAC,kBAAkB,KAAK,aAAaA,IAAG,GAAG;AAC7C,kBAAYA,IAAG,IAAID,QAAOC,IAAG;AAC7B,sBAAgBA,IAAG,IAAI;AAAA,IACzB;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,WAAW,WAAW,gBAAgB,UAAU;AAC1H,MAAI,OAAO;AACX,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAU,MAAM,UAAU,MAAM,KAAK,OAAO;AAC5C,SAAK,QAAQ,GAAG,WAAW,QAAQ,QAAQ,QAAQ,UAAU,SAAS,GAAG;AACvE,UAAI,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG;AACjC,mBAAW,OAAO,6CAA6C;AAAA,MACjE;AACA,UAAIH,SAAQ,OAAO,MAAM,YAAY,OAAO,QAAQ,KAAK,CAAC,MAAM,mBAAmB;AACjF,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,MAAIA,SAAQ,OAAO,MAAM,YAAY,OAAO,OAAO,MAAM,mBAAmB;AAC1E,cAAU;AAAA,EACZ;AACA,YAAU,OAAO,OAAO;AACxB,MAAI,YAAY,MAAM;AACpB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,WAAW,2BAA2B;AACxC,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,WAAK,QAAQ,GAAG,WAAW,UAAU,QAAQ,QAAQ,UAAU,SAAS,GAAG;AACzE,sBAAc,OAAO,SAAS,UAAU,KAAK,GAAG,eAAe;AAAA,MACjE;AAAA,IACF,OAAO;AACL,oBAAc,OAAO,SAAS,WAAW,eAAe;AAAA,IAC1D;AAAA,EACF,OAAO;AACL,QAAI,CAAC,MAAM,QAAQ,CAAC,kBAAkB,KAAK,iBAAiB,OAAO,KAAK,kBAAkB,KAAK,SAAS,OAAO,GAAG;AAChH,YAAM,OAAO,aAAa,MAAM;AAChC,YAAM,YAAY,kBAAkB,MAAM;AAC1C,YAAM,WAAW,YAAY,MAAM;AACnC,iBAAW,OAAO,wBAAwB;AAAA,IAC5C;AACA,QAAI,YAAY,aAAa;AAC3B,aAAO,eAAe,SAAS,SAAS;AAAA,QACtC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,OAAO,IAAI;AAAA,IACrB;AACA,WAAO,gBAAgB,OAAO;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI;AACJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,IAAM;AACf,UAAM;AAAA,EACR,WAAW,OAAO,IAAM;AACtB,UAAM;AACN,QAAI,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,IAAM;AACnD,YAAM;AAAA,IACR;AAAA,EACF,OAAO;AACL,eAAW,OAAO,0BAA0B;AAAA,EAC9C;AACA,QAAM,QAAQ;AACd,QAAM,YAAY,MAAM;AACxB,QAAM,iBAAiB;AACzB;AACA,SAAS,oBAAoB,OAAO,eAAe,aAAa;AAC9D,MAAI,aAAa,GACf,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC5C,SAAO,OAAO,GAAG;AACf,WAAO,eAAe,EAAE,GAAG;AACzB,UAAI,OAAO,KAAQ,MAAM,mBAAmB,IAAI;AAC9C,cAAM,iBAAiB,MAAM;AAAA,MAC/B;AACA,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AACA,QAAI,iBAAiB,OAAO,IAAM;AAChC,SAAG;AACD,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C,SAAS,OAAO,MAAQ,OAAO,MAAQ,OAAO;AAAA,IAChD;AACA,QAAI,OAAO,EAAE,GAAG;AACd,oBAAc,KAAK;AACnB,WAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C;AACA,YAAM,aAAa;AACnB,aAAO,OAAO,IAAM;AAClB,cAAM;AACN,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB,MAAM,eAAe,KAAK,MAAM,aAAa,aAAa;AAC5E,iBAAa,OAAO,uBAAuB;AAAA,EAC7C;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,OAAO;AACpC,MAAI,YAAY,MAAM,UACpB;AACF,OAAK,MAAM,MAAM,WAAW,SAAS;AACrC,OAAK,OAAO,MAAQ,OAAO,OAAS,OAAO,MAAM,MAAM,WAAW,YAAY,CAAC,KAAK,OAAO,MAAM,MAAM,WAAW,YAAY,CAAC,GAAG;AAChI,iBAAa;AACb,SAAK,MAAM,MAAM,WAAW,SAAS;AACrC,QAAI,OAAO,KAAK,aAAa,EAAE,GAAG;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO,OAAO;AACtC,MAAI,UAAU,GAAG;AACf,UAAM,UAAU;AAAA,EAClB,WAAW,QAAQ,GAAG;AACpB,UAAM,UAAU,OAAO,OAAO,MAAM,QAAQ,CAAC;AAAA,EAC/C;AACF;AACA,SAAS,gBAAgB,OAAO,YAAY,sBAAsB;AAChE,MAAI,WACF,WACA,cACA,YACA,mBACA,OACA,YACA,aACA,QAAQ,MAAM,MACd,UAAU,MAAM,QAChB;AACF,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,aAAa,EAAE,KAAK,kBAAkB,EAAE,KAAK,OAAO,MAAQ,OAAO,MAAQ,OAAO,MAAQ,OAAO,MAAQ,OAAO,OAAQ,OAAO,MAAQ,OAAO,MAAQ,OAAO,MAAQ,OAAO,MAAQ,OAAO,MAAQ,OAAO,IAAM;AAClN,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAQ,OAAO,IAAM;AAC9B,gBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,QAAI,aAAa,SAAS,KAAK,wBAAwB,kBAAkB,SAAS,GAAG;AACnF,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,iBAAe,aAAa,MAAM;AAClC,sBAAoB;AACpB,SAAO,OAAO,GAAG;AACf,QAAI,OAAO,IAAM;AACf,kBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,UAAI,aAAa,SAAS,KAAK,wBAAwB,kBAAkB,SAAS,GAAG;AACnF;AAAA,MACF;AAAA,IACF,WAAW,OAAO,IAAM;AACtB,kBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,UAAI,aAAa,SAAS,GAAG;AAC3B;AAAA,MACF;AAAA,IACF,WAAW,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,KAAK,wBAAwB,kBAAkB,EAAE,GAAG;AAC9H;AAAA,IACF,WAAW,OAAO,EAAE,GAAG;AACrB,cAAQ,MAAM;AACd,mBAAa,MAAM;AACnB,oBAAc,MAAM;AACpB,0BAAoB,OAAO,OAAO,EAAE;AACpC,UAAI,MAAM,cAAc,YAAY;AAClC,4BAAoB;AACpB,aAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C;AAAA,MACF,OAAO;AACL,cAAM,WAAW;AACjB,cAAM,OAAO;AACb,cAAM,YAAY;AAClB,cAAM,aAAa;AACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB;AACrB,qBAAe,OAAO,cAAc,YAAY,KAAK;AACrD,uBAAiB,OAAO,MAAM,OAAO,KAAK;AAC1C,qBAAe,aAAa,MAAM;AAClC,0BAAoB;AAAA,IACtB;AACA,QAAI,CAAC,eAAe,EAAE,GAAG;AACvB,mBAAa,MAAM,WAAW;AAAA,IAChC;AACA,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C;AACA,iBAAe,OAAO,cAAc,YAAY,KAAK;AACrD,MAAI,MAAM,QAAQ;AAChB,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,SAAO;AACT;AACA,SAAS,uBAAuB,OAAO,YAAY;AACjD,MAAI,IAAI,cAAc;AACtB,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,IAAM;AACf,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,QAAM;AACN,iBAAe,aAAa,MAAM;AAClC,UAAQ,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC1D,QAAI,OAAO,IAAM;AACf,qBAAe,OAAO,cAAc,MAAM,UAAU,IAAI;AACxD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,UAAI,OAAO,IAAM;AACf,uBAAe,MAAM;AACrB,cAAM;AACN,qBAAa,MAAM;AAAA,MACrB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,WAAW,OAAO,EAAE,GAAG;AACrB,qBAAe,OAAO,cAAc,YAAY,IAAI;AACpD,uBAAiB,OAAO,oBAAoB,OAAO,OAAO,UAAU,CAAC;AACrE,qBAAe,aAAa,MAAM;AAAA,IACpC,WAAW,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,GAAG;AAC7E,iBAAW,OAAO,8DAA8D;AAAA,IAClF,OAAO;AACL,YAAM;AACN,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF;AACA,aAAW,OAAO,4DAA4D;AAChF;AACA,SAAS,uBAAuB,OAAO,YAAY;AACjD,MAAI,cAAc,YAAY,WAAW,WAAW,KAAK;AACzD,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,IAAM;AACf,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,QAAM;AACN,iBAAe,aAAa,MAAM;AAClC,UAAQ,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC1D,QAAI,OAAO,IAAM;AACf,qBAAe,OAAO,cAAc,MAAM,UAAU,IAAI;AACxD,YAAM;AACN,aAAO;AAAA,IACT,WAAW,OAAO,IAAM;AACtB,qBAAe,OAAO,cAAc,MAAM,UAAU,IAAI;AACxD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,UAAI,OAAO,EAAE,GAAG;AACd,4BAAoB,OAAO,OAAO,UAAU;AAAA,MAC9C,WAAW,KAAK,OAAO,kBAAkB,EAAE,GAAG;AAC5C,cAAM,UAAU,gBAAgB,EAAE;AAClC,cAAM;AAAA,MACR,YAAY,MAAM,cAAc,EAAE,KAAK,GAAG;AACxC,oBAAY;AACZ,oBAAY;AACZ,eAAO,YAAY,GAAG,aAAa;AACjC,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,eAAK,MAAM,YAAY,EAAE,MAAM,GAAG;AAChC,yBAAa,aAAa,KAAK;AAAA,UACjC,OAAO;AACL,uBAAW,OAAO,gCAAgC;AAAA,UACpD;AAAA,QACF;AACA,cAAM,UAAU,kBAAkB,SAAS;AAC3C,cAAM;AAAA,MACR,OAAO;AACL,mBAAW,OAAO,yBAAyB;AAAA,MAC7C;AACA,qBAAe,aAAa,MAAM;AAAA,IACpC,WAAW,OAAO,EAAE,GAAG;AACrB,qBAAe,OAAO,cAAc,YAAY,IAAI;AACpD,uBAAiB,OAAO,oBAAoB,OAAO,OAAO,UAAU,CAAC;AACrE,qBAAe,aAAa,MAAM;AAAA,IACpC,WAAW,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,GAAG;AAC7E,iBAAW,OAAO,8DAA8D;AAAA,IAClF,OAAO;AACL,YAAM;AACN,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF;AACA,aAAW,OAAO,4DAA4D;AAChF;AACA,SAAS,mBAAmB,OAAO,YAAY;AAC7C,MAAI,WAAW,MACb,OACA,YACA,MACA,OAAO,MAAM,KACb,SACA,UAAU,MAAM,QAChB,WACA,YACA,QACA,gBACA,WACA,kBAAkB,uBAAO,OAAO,IAAI,GACpC,SACA,QACA,WACA;AACF,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,IAAM;AACf,iBAAa;AACb,gBAAY;AACZ,cAAU,CAAC;AAAA,EACb,WAAW,OAAO,KAAM;AACtB,iBAAa;AACb,gBAAY;AACZ,cAAU,CAAC;AAAA,EACb,OAAO;AACL,WAAO;AAAA,EACT;AACA,MAAI,MAAM,WAAW,MAAM;AACzB,UAAM,UAAU,MAAM,MAAM,IAAI;AAAA,EAClC;AACA,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,SAAO,OAAO,GAAG;AACf,wBAAoB,OAAO,MAAM,UAAU;AAC3C,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,QAAI,OAAO,YAAY;AACrB,YAAM;AACN,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,YAAM,OAAO,YAAY,YAAY;AACrC,YAAM,SAAS;AACf,aAAO;AAAA,IACT,WAAW,CAAC,UAAU;AACpB,iBAAW,OAAO,8CAA8C;AAAA,IAClE,WAAW,OAAO,IAAM;AACtB,iBAAW,OAAO,0CAA0C;AAAA,IAC9D;AACA,aAAS,UAAU,YAAY;AAC/B,aAAS,iBAAiB;AAC1B,QAAI,OAAO,IAAM;AACf,kBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,UAAI,aAAa,SAAS,GAAG;AAC3B,iBAAS,iBAAiB;AAC1B,cAAM;AACN,4BAAoB,OAAO,MAAM,UAAU;AAAA,MAC7C;AAAA,IACF;AACA,YAAQ,MAAM;AACd,iBAAa,MAAM;AACnB,WAAO,MAAM;AACb,gBAAY,OAAO,YAAY,iBAAiB,OAAO,IAAI;AAC3D,aAAS,MAAM;AACf,cAAU,MAAM;AAChB,wBAAoB,OAAO,MAAM,UAAU;AAC3C,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,SAAK,kBAAkB,MAAM,SAAS,UAAU,OAAO,IAAM;AAC3D,eAAS;AACT,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,0BAAoB,OAAO,MAAM,UAAU;AAC3C,kBAAY,OAAO,YAAY,iBAAiB,OAAO,IAAI;AAC3D,kBAAY,MAAM;AAAA,IACpB;AACA,QAAI,WAAW;AACb,uBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,WAAW,OAAO,YAAY,IAAI;AAAA,IACvG,WAAW,QAAQ;AACjB,cAAQ,KAAK,iBAAiB,OAAO,MAAM,iBAAiB,QAAQ,SAAS,WAAW,OAAO,YAAY,IAAI,CAAC;AAAA,IAClH,OAAO;AACL,cAAQ,KAAK,OAAO;AAAA,IACtB;AACA,wBAAoB,OAAO,MAAM,UAAU;AAC3C,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,QAAI,OAAO,IAAM;AACf,iBAAW;AACX,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C,OAAO;AACL,iBAAW;AAAA,IACb;AAAA,EACF;AACA,aAAW,OAAO,uDAAuD;AAC3E;AACA,SAAS,gBAAgB,OAAO,YAAY;AAC1C,MAAI,cACF,SACA,WAAW,eACX,iBAAiB,OACjB,iBAAiB,OACjB,aAAa,YACb,aAAa,GACb,iBAAiB,OACjB,KACA;AACF,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,KAAM;AACf,cAAU;AAAA,EACZ,WAAW,OAAO,IAAM;AACtB,cAAU;AAAA,EACZ,OAAO;AACL,WAAO;AAAA,EACT;AACA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,SAAO,OAAO,GAAG;AACf,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,QAAI,OAAO,MAAQ,OAAO,IAAM;AAC9B,UAAI,kBAAkB,UAAU;AAC9B,mBAAW,OAAO,KAAO,gBAAgB;AAAA,MAC3C,OAAO;AACL,mBAAW,OAAO,sCAAsC;AAAA,MAC1D;AAAA,IACF,YAAY,MAAM,gBAAgB,EAAE,MAAM,GAAG;AAC3C,UAAI,QAAQ,GAAG;AACb,mBAAW,OAAO,8EAA8E;AAAA,MAClG,WAAW,CAAC,gBAAgB;AAC1B,qBAAa,aAAa,MAAM;AAChC,yBAAiB;AAAA,MACnB,OAAO;AACL,mBAAW,OAAO,2CAA2C;AAAA,MAC/D;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe,EAAE,GAAG;AACtB,OAAG;AACD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C,SAAS,eAAe,EAAE;AAC1B,QAAI,OAAO,IAAM;AACf,SAAG;AACD,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C,SAAS,CAAC,OAAO,EAAE,KAAK,OAAO;AAAA,IACjC;AAAA,EACF;AACA,SAAO,OAAO,GAAG;AACf,kBAAc,KAAK;AACnB,UAAM,aAAa;AACnB,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,YAAQ,CAAC,kBAAkB,MAAM,aAAa,eAAe,OAAO,IAAM;AACxE,YAAM;AACN,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AACA,QAAI,CAAC,kBAAkB,MAAM,aAAa,YAAY;AACpD,mBAAa,MAAM;AAAA,IACrB;AACA,QAAI,OAAO,EAAE,GAAG;AACd;AACA;AAAA,IACF;AACA,QAAI,MAAM,aAAa,YAAY;AACjC,UAAI,aAAa,eAAe;AAC9B,cAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,IAAI,aAAa,UAAU;AAAA,MAClF,WAAW,aAAa,eAAe;AACrC,YAAI,gBAAgB;AAClB,gBAAM,UAAU;AAAA,QAClB;AAAA,MACF;AACA;AAAA,IACF;AACA,QAAI,SAAS;AACX,UAAI,eAAe,EAAE,GAAG;AACtB,yBAAiB;AACjB,cAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,IAAI,aAAa,UAAU;AAAA,MAClF,WAAW,gBAAgB;AACzB,yBAAiB;AACjB,cAAM,UAAU,OAAO,OAAO,MAAM,aAAa,CAAC;AAAA,MACpD,WAAW,eAAe,GAAG;AAC3B,YAAI,gBAAgB;AAClB,gBAAM,UAAU;AAAA,QAClB;AAAA,MACF,OAAO;AACL,cAAM,UAAU,OAAO,OAAO,MAAM,UAAU;AAAA,MAChD;AAAA,IACF,OAAO;AACL,YAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,IAAI,aAAa,UAAU;AAAA,IAClF;AACA,qBAAiB;AACjB,qBAAiB;AACjB,iBAAa;AACb,mBAAe,MAAM;AACrB,WAAO,CAAC,OAAO,EAAE,KAAK,OAAO,GAAG;AAC9B,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AACA,mBAAe,OAAO,cAAc,MAAM,UAAU,KAAK;AAAA,EAC3D;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,OAAO,YAAY;AAC5C,MAAI,OACF,OAAO,MAAM,KACb,UAAU,MAAM,QAChB,UAAU,CAAC,GACX,WACA,WAAW,OACX;AACF,MAAI,MAAM,mBAAmB,GAAI,QAAO;AACxC,MAAI,MAAM,WAAW,MAAM;AACzB,UAAM,UAAU,MAAM,MAAM,IAAI;AAAA,EAClC;AACA,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,SAAO,OAAO,GAAG;AACf,QAAI,MAAM,mBAAmB,IAAI;AAC/B,YAAM,WAAW,MAAM;AACvB,iBAAW,OAAO,gDAAgD;AAAA,IACpE;AACA,QAAI,OAAO,IAAM;AACf;AAAA,IACF;AACA,gBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,QAAI,CAAC,aAAa,SAAS,GAAG;AAC5B;AAAA,IACF;AACA,eAAW;AACX,UAAM;AACN,QAAI,oBAAoB,OAAO,MAAM,EAAE,GAAG;AACxC,UAAI,MAAM,cAAc,YAAY;AAClC,gBAAQ,KAAK,IAAI;AACjB,aAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C;AAAA,MACF;AAAA,IACF;AACA,YAAQ,MAAM;AACd,gBAAY,OAAO,YAAY,kBAAkB,OAAO,IAAI;AAC5D,YAAQ,KAAK,MAAM,MAAM;AACzB,wBAAoB,OAAO,MAAM,EAAE;AACnC,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,SAAK,MAAM,SAAS,SAAS,MAAM,aAAa,eAAe,OAAO,GAAG;AACvE,iBAAW,OAAO,qCAAqC;AAAA,IACzD,WAAW,MAAM,aAAa,YAAY;AACxC;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACZ,UAAM,MAAM;AACZ,UAAM,SAAS;AACf,UAAM,OAAO;AACb,UAAM,SAAS;AACf,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO,YAAY,YAAY;AACvD,MAAI,WACF,cACA,OACA,UACA,eACA,SACA,OAAO,MAAM,KACb,UAAU,MAAM,QAChB,UAAU,CAAC,GACX,kBAAkB,uBAAO,OAAO,IAAI,GACpC,SAAS,MACT,UAAU,MACV,YAAY,MACZ,gBAAgB,OAChB,WAAW,OACX;AACF,MAAI,MAAM,mBAAmB,GAAI,QAAO;AACxC,MAAI,MAAM,WAAW,MAAM;AACzB,UAAM,UAAU,MAAM,MAAM,IAAI;AAAA,EAClC;AACA,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,SAAO,OAAO,GAAG;AACf,QAAI,CAAC,iBAAiB,MAAM,mBAAmB,IAAI;AACjD,YAAM,WAAW,MAAM;AACvB,iBAAW,OAAO,gDAAgD;AAAA,IACpE;AACA,gBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,YAAQ,MAAM;AACd,SAAK,OAAO,MAAQ,OAAO,OAAS,aAAa,SAAS,GAAG;AAC3D,UAAI,OAAO,IAAM;AACf,YAAI,eAAe;AACjB,2BAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,MAAM,UAAU,eAAe,OAAO;AACzG,mBAAS,UAAU,YAAY;AAAA,QACjC;AACA,mBAAW;AACX,wBAAgB;AAChB,uBAAe;AAAA,MACjB,WAAW,eAAe;AACxB,wBAAgB;AAChB,uBAAe;AAAA,MACjB,OAAO;AACL,mBAAW,OAAO,mGAAmG;AAAA,MACvH;AACA,YAAM,YAAY;AAClB,WAAK;AAAA,IACP,OAAO;AACL,iBAAW,MAAM;AACjB,sBAAgB,MAAM;AACtB,gBAAU,MAAM;AAChB,UAAI,CAAC,YAAY,OAAO,YAAY,kBAAkB,OAAO,IAAI,GAAG;AAClE;AAAA,MACF;AACA,UAAI,MAAM,SAAS,OAAO;AACxB,aAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,eAAO,eAAe,EAAE,GAAG;AACzB,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,QAC9C;AACA,YAAI,OAAO,IAAM;AACf,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,cAAI,CAAC,aAAa,EAAE,GAAG;AACrB,uBAAW,OAAO,yFAAyF;AAAA,UAC7G;AACA,cAAI,eAAe;AACjB,6BAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,MAAM,UAAU,eAAe,OAAO;AACzG,qBAAS,UAAU,YAAY;AAAA,UACjC;AACA,qBAAW;AACX,0BAAgB;AAChB,yBAAe;AACf,mBAAS,MAAM;AACf,oBAAU,MAAM;AAAA,QAClB,WAAW,UAAU;AACnB,qBAAW,OAAO,0DAA0D;AAAA,QAC9E,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,SAAS;AACf,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,UAAU;AACnB,mBAAW,OAAO,gFAAgF;AAAA,MACpG,OAAO;AACL,cAAM,MAAM;AACZ,cAAM,SAAS;AACf,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,MAAM,SAAS,SAAS,MAAM,aAAa,YAAY;AACzD,UAAI,eAAe;AACjB,mBAAW,MAAM;AACjB,wBAAgB,MAAM;AACtB,kBAAU,MAAM;AAAA,MAClB;AACA,UAAI,YAAY,OAAO,YAAY,mBAAmB,MAAM,YAAY,GAAG;AACzE,YAAI,eAAe;AACjB,oBAAU,MAAM;AAAA,QAClB,OAAO;AACL,sBAAY,MAAM;AAAA,QACpB;AAAA,MACF;AACA,UAAI,CAAC,eAAe;AAClB,yBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,WAAW,UAAU,eAAe,OAAO;AAC9G,iBAAS,UAAU,YAAY;AAAA,MACjC;AACA,0BAAoB,OAAO,MAAM,EAAE;AACnC,WAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAAA,IAC5C;AACA,SAAK,MAAM,SAAS,SAAS,MAAM,aAAa,eAAe,OAAO,GAAG;AACvE,iBAAW,OAAO,oCAAoC;AAAA,IACxD,WAAW,MAAM,aAAa,YAAY;AACxC;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe;AACjB,qBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,MAAM,UAAU,eAAe,OAAO;AAAA,EAC3G;AACA,MAAI,UAAU;AACZ,UAAM,MAAM;AACZ,UAAM,SAAS;AACf,UAAM,OAAO;AACb,UAAM,SAAS;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,WACF,aAAa,OACb,UAAU,OACV,WACA,SACA;AACF,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,GAAM,QAAO;AACxB,MAAI,MAAM,QAAQ,MAAM;AACtB,eAAW,OAAO,+BAA+B;AAAA,EACnD;AACA,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,MAAI,OAAO,IAAM;AACf,iBAAa;AACb,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C,WAAW,OAAO,IAAM;AACtB,cAAU;AACV,gBAAY;AACZ,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C,OAAO;AACL,gBAAY;AAAA,EACd;AACA,cAAY,MAAM;AAClB,MAAI,YAAY;AACd,OAAG;AACD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C,SAAS,OAAO,KAAK,OAAO;AAC5B,QAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,gBAAU,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AACrD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C,OAAO;AACL,iBAAW,OAAO,oDAAoD;AAAA,IACxE;AAAA,EACF,OAAO;AACL,WAAO,OAAO,KAAK,CAAC,aAAa,EAAE,GAAG;AACpC,UAAI,OAAO,IAAM;AACf,YAAI,CAAC,SAAS;AACZ,sBAAY,MAAM,MAAM,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC;AAC/D,cAAI,CAAC,mBAAmB,KAAK,SAAS,GAAG;AACvC,uBAAW,OAAO,iDAAiD;AAAA,UACrE;AACA,oBAAU;AACV,sBAAY,MAAM,WAAW;AAAA,QAC/B,OAAO;AACL,qBAAW,OAAO,6CAA6C;AAAA,QACjE;AAAA,MACF;AACA,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AACA,cAAU,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AACrD,QAAI,wBAAwB,KAAK,OAAO,GAAG;AACzC,iBAAW,OAAO,qDAAqD;AAAA,IACzE;AAAA,EACF;AACA,MAAI,WAAW,CAAC,gBAAgB,KAAK,OAAO,GAAG;AAC7C,eAAW,OAAO,8CAA8C,OAAO;AAAA,EACzE;AACA,MAAI;AACF,cAAU,mBAAmB,OAAO;AAAA,EACtC,SAAS,KAAK;AACZ,eAAW,OAAO,4BAA4B,OAAO;AAAA,EACvD;AACA,MAAI,YAAY;AACd,UAAM,MAAM;AAAA,EACd,WAAW,kBAAkB,KAAK,MAAM,QAAQ,SAAS,GAAG;AAC1D,UAAM,MAAM,MAAM,OAAO,SAAS,IAAI;AAAA,EACxC,WAAW,cAAc,KAAK;AAC5B,UAAM,MAAM,MAAM;AAAA,EACpB,WAAW,cAAc,MAAM;AAC7B,UAAM,MAAM,uBAAuB;AAAA,EACrC,OAAO;AACL,eAAW,OAAO,4BAA4B,YAAY,GAAG;AAAA,EAC/D;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AACjC,MAAI,WAAW;AACf,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,GAAM,QAAO;AACxB,MAAI,MAAM,WAAW,MAAM;AACzB,eAAW,OAAO,mCAAmC;AAAA,EACvD;AACA,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,cAAY,MAAM;AAClB,SAAO,OAAO,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,kBAAkB,EAAE,GAAG;AAC9D,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C;AACA,MAAI,MAAM,aAAa,WAAW;AAChC,eAAW,OAAO,4DAA4D;AAAA,EAChF;AACA,QAAM,SAAS,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1D,SAAO;AACT;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,WAAW,OAAO;AACtB,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,MAAI,OAAO,GAAM,QAAO;AACxB,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,cAAY,MAAM;AAClB,SAAO,OAAO,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,kBAAkB,EAAE,GAAG;AAC9D,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C;AACA,MAAI,MAAM,aAAa,WAAW;AAChC,eAAW,OAAO,2DAA2D;AAAA,EAC/E;AACA,UAAQ,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AACnD,MAAI,CAAC,kBAAkB,KAAK,MAAM,WAAW,KAAK,GAAG;AACnD,eAAW,OAAO,yBAAyB,QAAQ,GAAG;AAAA,EACxD;AACA,QAAM,SAAS,MAAM,UAAU,KAAK;AACpC,sBAAoB,OAAO,MAAM,EAAE;AACnC,SAAO;AACT;AACA,SAAS,YAAY,OAAO,cAAc,aAAa,aAAa,cAAc;AAChF,MAAI,kBACF,mBACA,uBACA,eAAe,GACf,YAAY,OACZ,aAAa,OACb,WACA,cACA,UACAU,OACA,YACA;AACF,MAAI,MAAM,aAAa,MAAM;AAC3B,UAAM,SAAS,QAAQ,KAAK;AAAA,EAC9B;AACA,QAAM,MAAM;AACZ,QAAM,SAAS;AACf,QAAM,OAAO;AACb,QAAM,SAAS;AACf,qBAAmB,oBAAoB,wBAAwB,sBAAsB,eAAe,qBAAqB;AACzH,MAAI,aAAa;AACf,QAAI,oBAAoB,OAAO,MAAM,EAAE,GAAG;AACxC,kBAAY;AACZ,UAAI,MAAM,aAAa,cAAc;AACnC,uBAAe;AAAA,MACjB,WAAW,MAAM,eAAe,cAAc;AAC5C,uBAAe;AAAA,MACjB,WAAW,MAAM,aAAa,cAAc;AAC1C,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,MAAI,iBAAiB,GAAG;AACtB,WAAO,gBAAgB,KAAK,KAAK,mBAAmB,KAAK,GAAG;AAC1D,UAAI,oBAAoB,OAAO,MAAM,EAAE,GAAG;AACxC,oBAAY;AACZ,gCAAwB;AACxB,YAAI,MAAM,aAAa,cAAc;AACnC,yBAAe;AAAA,QACjB,WAAW,MAAM,eAAe,cAAc;AAC5C,yBAAe;AAAA,QACjB,WAAW,MAAM,aAAa,cAAc;AAC1C,yBAAe;AAAA,QACjB;AAAA,MACF,OAAO;AACL,gCAAwB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,MAAI,uBAAuB;AACzB,4BAAwB,aAAa;AAAA,EACvC;AACA,MAAI,iBAAiB,KAAK,sBAAsB,aAAa;AAC3D,QAAI,oBAAoB,eAAe,qBAAqB,aAAa;AACvE,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa,eAAe;AAAA,IAC9B;AACA,kBAAc,MAAM,WAAW,MAAM;AACrC,QAAI,iBAAiB,GAAG;AACtB,UAAI,0BAA0B,kBAAkB,OAAO,WAAW,KAAK,iBAAiB,OAAO,aAAa,UAAU,MAAM,mBAAmB,OAAO,UAAU,GAAG;AACjK,qBAAa;AAAA,MACf,OAAO;AACL,YAAI,qBAAqB,gBAAgB,OAAO,UAAU,KAAK,uBAAuB,OAAO,UAAU,KAAK,uBAAuB,OAAO,UAAU,GAAG;AACrJ,uBAAa;AAAA,QACf,WAAW,UAAU,KAAK,GAAG;AAC3B,uBAAa;AACb,cAAI,MAAM,QAAQ,QAAQ,MAAM,WAAW,MAAM;AAC/C,uBAAW,OAAO,2CAA2C;AAAA,UAC/D;AAAA,QACF,WAAW,gBAAgB,OAAO,YAAY,oBAAoB,WAAW,GAAG;AAC9E,uBAAa;AACb,cAAI,MAAM,QAAQ,MAAM;AACtB,kBAAM,MAAM;AAAA,UACd;AAAA,QACF;AACA,YAAI,MAAM,WAAW,MAAM;AACzB,gBAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,QACxC;AAAA,MACF;AAAA,IACF,WAAW,iBAAiB,GAAG;AAC7B,mBAAa,yBAAyB,kBAAkB,OAAO,WAAW;AAAA,IAC5E;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,MAAM;AACtB,QAAI,MAAM,WAAW,MAAM;AACzB,YAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,IACxC;AAAA,EACF,WAAW,MAAM,QAAQ,KAAK;AAC5B,QAAI,MAAM,WAAW,QAAQ,MAAM,SAAS,UAAU;AACpD,iBAAW,OAAO,sEAAsE,MAAM,OAAO,GAAG;AAAA,IAC1G;AACA,SAAK,YAAY,GAAG,eAAe,MAAM,cAAc,QAAQ,YAAY,cAAc,aAAa,GAAG;AACvG,MAAAA,QAAO,MAAM,cAAc,SAAS;AACpC,UAAIA,MAAK,QAAQ,MAAM,MAAM,GAAG;AAC9B,cAAM,SAASA,MAAK,UAAU,MAAM,MAAM;AAC1C,cAAM,MAAMA,MAAK;AACjB,YAAI,MAAM,WAAW,MAAM;AACzB,gBAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,QACxC;AACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,MAAM,QAAQ,KAAK;AAC5B,QAAI,kBAAkB,KAAK,MAAM,QAAQ,MAAM,QAAQ,UAAU,GAAG,MAAM,GAAG,GAAG;AAC9E,MAAAA,QAAO,MAAM,QAAQ,MAAM,QAAQ,UAAU,EAAE,MAAM,GAAG;AAAA,IAC1D,OAAO;AACL,MAAAA,QAAO;AACP,iBAAW,MAAM,QAAQ,MAAM,MAAM,QAAQ,UAAU;AACvD,WAAK,YAAY,GAAG,eAAe,SAAS,QAAQ,YAAY,cAAc,aAAa,GAAG;AAC5F,YAAI,MAAM,IAAI,MAAM,GAAG,SAAS,SAAS,EAAE,IAAI,MAAM,MAAM,SAAS,SAAS,EAAE,KAAK;AAClF,UAAAA,QAAO,SAAS,SAAS;AACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAACA,OAAM;AACT,iBAAW,OAAO,mBAAmB,MAAM,MAAM,GAAG;AAAA,IACtD;AACA,QAAI,MAAM,WAAW,QAAQA,MAAK,SAAS,MAAM,MAAM;AACrD,iBAAW,OAAO,kCAAkC,MAAM,MAAM,0BAA0BA,MAAK,OAAO,aAAa,MAAM,OAAO,GAAG;AAAA,IACrI;AACA,QAAI,CAACA,MAAK,QAAQ,MAAM,QAAQ,MAAM,GAAG,GAAG;AAC1C,iBAAW,OAAO,kCAAkC,MAAM,MAAM,gBAAgB;AAAA,IAClF,OAAO;AACL,YAAM,SAASA,MAAK,UAAU,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAI,MAAM,WAAW,MAAM;AACzB,cAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,aAAa,MAAM;AAC3B,UAAM,SAAS,SAAS,KAAK;AAAA,EAC/B;AACA,SAAO,MAAM,QAAQ,QAAQ,MAAM,WAAW,QAAQ;AACxD;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,gBAAgB,MAAM,UACxB,WACA,eACA,eACA,gBAAgB,OAChB;AACF,QAAM,UAAU;AAChB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,SAAS,uBAAO,OAAO,IAAI;AACjC,QAAM,YAAY,uBAAO,OAAO,IAAI;AACpC,UAAQ,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC1D,wBAAoB,OAAO,MAAM,EAAE;AACnC,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C,QAAI,MAAM,aAAa,KAAK,OAAO,IAAM;AACvC;AAAA,IACF;AACA,oBAAgB;AAChB,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,gBAAY,MAAM;AAClB,WAAO,OAAO,KAAK,CAAC,aAAa,EAAE,GAAG;AACpC,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AACA,oBAAgB,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC3D,oBAAgB,CAAC;AACjB,QAAI,cAAc,SAAS,GAAG;AAC5B,iBAAW,OAAO,8DAA8D;AAAA,IAClF;AACA,WAAO,OAAO,GAAG;AACf,aAAO,eAAe,EAAE,GAAG;AACzB,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C;AACA,UAAI,OAAO,IAAM;AACf,WAAG;AACD,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,QAC9C,SAAS,OAAO,KAAK,CAAC,OAAO,EAAE;AAC/B;AAAA,MACF;AACA,UAAI,OAAO,EAAE,EAAG;AAChB,kBAAY,MAAM;AAClB,aAAO,OAAO,KAAK,CAAC,aAAa,EAAE,GAAG;AACpC,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C;AACA,oBAAc,KAAK,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ,CAAC;AAAA,IACjE;AACA,QAAI,OAAO,EAAG,eAAc,KAAK;AACjC,QAAI,kBAAkB,KAAK,mBAAmB,aAAa,GAAG;AAC5D,wBAAkB,aAAa,EAAE,OAAO,eAAe,aAAa;AAAA,IACtE,OAAO;AACL,mBAAa,OAAO,iCAAiC,gBAAgB,GAAG;AAAA,IAC1E;AAAA,EACF;AACA,sBAAoB,OAAO,MAAM,EAAE;AACnC,MAAI,MAAM,eAAe,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,MAAQ,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC,MAAM,MAAQ,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC,MAAM,IAAM;AAC3L,UAAM,YAAY;AAClB,wBAAoB,OAAO,MAAM,EAAE;AAAA,EACrC,WAAW,eAAe;AACxB,eAAW,OAAO,iCAAiC;AAAA,EACrD;AACA,cAAY,OAAO,MAAM,aAAa,GAAG,mBAAmB,OAAO,IAAI;AACvE,sBAAoB,OAAO,MAAM,EAAE;AACnC,MAAI,MAAM,mBAAmB,8BAA8B,KAAK,MAAM,MAAM,MAAM,eAAe,MAAM,QAAQ,CAAC,GAAG;AACjH,iBAAa,OAAO,kDAAkD;AAAA,EACxE;AACA,QAAM,UAAU,KAAK,MAAM,MAAM;AACjC,MAAI,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,GAAG;AACtE,QAAI,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,IAAM;AACnD,YAAM,YAAY;AAClB,0BAAoB,OAAO,MAAM,EAAE;AAAA,IACrC;AACA;AAAA,EACF;AACA,MAAI,MAAM,WAAW,MAAM,SAAS,GAAG;AACrC,eAAW,OAAO,uDAAuD;AAAA,EAC3E,OAAO;AACL;AAAA,EACF;AACF;AACA,SAAS,cAAc,OAAO,SAAS;AACrC,UAAQ,OAAO,KAAK;AACpB,YAAU,WAAW,CAAC;AACtB,MAAI,MAAM,WAAW,GAAG;AACtB,QAAI,MAAM,WAAW,MAAM,SAAS,CAAC,MAAM,MAAQ,MAAM,WAAW,MAAM,SAAS,CAAC,MAAM,IAAM;AAC9F,eAAS;AAAA,IACX;AACA,QAAI,MAAM,WAAW,CAAC,MAAM,OAAQ;AAClC,cAAQ,MAAM,MAAM,CAAC;AAAA,IACvB;AAAA,EACF;AACA,MAAI,QAAQ,IAAI,QAAQ,OAAO,OAAO;AACtC,MAAI,UAAU,MAAM,QAAQ,IAAI;AAChC,MAAI,YAAY,IAAI;AAClB,UAAM,WAAW;AACjB,eAAW,OAAO,mCAAmC;AAAA,EACvD;AACA,QAAM,SAAS;AACf,SAAO,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,IAAM;AACtD,UAAM,cAAc;AACpB,UAAM,YAAY;AAAA,EACpB;AACA,SAAO,MAAM,WAAW,MAAM,SAAS,GAAG;AACxC,iBAAa,KAAK;AAAA,EACpB;AACA,SAAO,MAAM;AACf;AACA,SAAS,UAAU,OAAO,UAAU,SAAS;AAC3C,MAAI,aAAa,QAAQV,SAAQ,QAAQ,MAAM,YAAY,OAAO,YAAY,aAAa;AACzF,cAAU;AACV,eAAW;AAAA,EACb;AACA,MAAI,YAAY,cAAc,OAAO,OAAO;AAC5C,MAAI,OAAO,aAAa,YAAY;AAClC,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,GAAG,SAAS,UAAU,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,aAAS,UAAU,KAAK,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,OAAO,OAAO,SAAS;AAC9B,MAAI,YAAY,cAAc,OAAO,OAAO;AAC5C,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO;AAAA,EACT,WAAW,UAAU,WAAW,GAAG;AACjC,WAAO,UAAU,CAAC;AAAA,EACpB;AACA,QAAM,IAAI,UAAU,0DAA0D;AAChF;AACA,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,SAAS;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAI,YAAY,OAAO,UAAU;AACjC,IAAI,kBAAkB,OAAO,UAAU;AACvC,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI,aAAa;AACjB,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;AACxB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,qBAAqB;AACzB,IAAI,2BAA2B;AAC/B,IAAI,4BAA4B;AAChC,IAAI,oBAAoB;AACxB,IAAI,0BAA0B;AAC9B,IAAI,qBAAqB;AACzB,IAAI,2BAA2B;AAC/B,IAAI,mBAAmB,CAAC;AACxB,iBAAiB,CAAI,IAAI;AACzB,iBAAiB,CAAI,IAAI;AACzB,iBAAiB,CAAI,IAAI;AACzB,iBAAiB,CAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,EAAI,IAAI;AACzB,iBAAiB,GAAI,IAAI;AACzB,iBAAiB,GAAI,IAAI;AACzB,iBAAiB,IAAM,IAAI;AAC3B,iBAAiB,IAAM,IAAI;AAC3B,IAAI,6BAA6B,CAAC,KAAK,KAAK,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK;AAClI,IAAI,2BAA2B;AAC/B,SAAS,gBAAgBS,SAAQD,MAAK;AACpC,MAAI,QAAQ,MAAM,OAAO,QAAQ,KAAK,OAAOE;AAC7C,MAAIF,SAAQ,KAAM,QAAO,CAAC;AAC1B,WAAS,CAAC;AACV,SAAO,OAAO,KAAKA,IAAG;AACtB,OAAK,QAAQ,GAAG,SAAS,KAAK,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAChE,UAAM,KAAK,KAAK;AAChB,YAAQ,OAAOA,KAAI,GAAG,CAAC;AACvB,QAAI,IAAI,MAAM,GAAG,CAAC,MAAM,MAAM;AAC5B,YAAM,uBAAuB,IAAI,MAAM,CAAC;AAAA,IAC1C;AACA,IAAAE,QAAOD,QAAO,gBAAgB,UAAU,EAAE,GAAG;AAC7C,QAAIC,SAAQ,gBAAgB,KAAKA,MAAK,cAAc,KAAK,GAAG;AAC1D,cAAQA,MAAK,aAAa,KAAK;AAAA,IACjC;AACA,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,UAAU,WAAW;AAC5B,MAAIN,SAAQ,QAAQ;AACpB,EAAAA,UAAS,UAAU,SAAS,EAAE,EAAE,YAAY;AAC5C,MAAI,aAAa,KAAM;AACrB,aAAS;AACT,aAAS;AAAA,EACX,WAAW,aAAa,OAAQ;AAC9B,aAAS;AACT,aAAS;AAAA,EACX,WAAW,aAAa,YAAY;AAClC,aAAS;AACT,aAAS;AAAA,EACX,OAAO;AACL,UAAM,IAAI,UAAU,+DAA+D;AAAA,EACrF;AACA,SAAO,OAAO,SAAS,OAAO,OAAO,KAAK,SAASA,QAAO,MAAM,IAAIA;AACtE;AACA,IAAI,sBAAsB;AAA1B,IACE,sBAAsB;AACxB,SAAS,MAAM,SAAS;AACtB,OAAK,SAAS,QAAQ,QAAQ,KAAKiB;AACnC,OAAK,SAAS,KAAK,IAAI,GAAG,QAAQ,QAAQ,KAAK,CAAC;AAChD,OAAK,gBAAgB,QAAQ,eAAe,KAAK;AACjD,OAAK,cAAc,QAAQ,aAAa,KAAK;AAC7C,OAAK,YAAY,OAAO,UAAU,QAAQ,WAAW,CAAC,IAAI,KAAK,QAAQ,WAAW;AAClF,OAAK,WAAW,gBAAgB,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI;AACtE,OAAK,WAAW,QAAQ,UAAU,KAAK;AACvC,OAAK,YAAY,QAAQ,WAAW,KAAK;AACzC,OAAK,SAAS,QAAQ,QAAQ,KAAK;AACnC,OAAK,eAAe,QAAQ,cAAc,KAAK;AAC/C,OAAK,eAAe,QAAQ,cAAc,KAAK;AAC/C,OAAK,cAAc,QAAQ,aAAa,MAAM,MAAM,sBAAsB;AAC1E,OAAK,cAAc,QAAQ,aAAa,KAAK;AAC7C,OAAK,WAAW,OAAO,QAAQ,UAAU,MAAM,aAAa,QAAQ,UAAU,IAAI;AAClF,OAAK,gBAAgB,KAAK,OAAO;AACjC,OAAK,gBAAgB,KAAK,OAAO;AACjC,OAAK,MAAM;AACX,OAAK,SAAS;AACd,OAAK,aAAa,CAAC;AACnB,OAAK,iBAAiB;AACxB;AACA,SAAS,aAAajB,SAAQ,QAAQ;AACpC,MAAI,MAAM,OAAO,OAAO,KAAK,MAAM,GACjC,WAAW,GACX,OAAO,IACP,SAAS,IACTG,OACA,SAASH,QAAO;AAClB,SAAO,WAAW,QAAQ;AACxB,WAAOA,QAAO,QAAQ,MAAM,QAAQ;AACpC,QAAI,SAAS,IAAI;AACf,MAAAG,QAAOH,QAAO,MAAM,QAAQ;AAC5B,iBAAW;AAAA,IACb,OAAO;AACL,MAAAG,QAAOH,QAAO,MAAM,UAAU,OAAO,CAAC;AACtC,iBAAW,OAAO;AAAA,IACpB;AACA,QAAIG,MAAK,UAAUA,UAAS,KAAM,WAAU;AAC5C,cAAUA;AAAA,EACZ;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO,OAAO;AACtC,SAAO,OAAO,OAAO,OAAO,KAAK,MAAM,SAAS,KAAK;AACvD;AACA,SAAS,sBAAsB,OAAOiB,MAAK;AACzC,MAAI,OAAO,QAAQd;AACnB,OAAK,QAAQ,GAAG,SAAS,MAAM,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAC/E,IAAAA,QAAO,MAAM,cAAc,KAAK;AAChC,QAAIA,MAAK,QAAQc,IAAG,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAaR,IAAG;AACvB,SAAOA,OAAM,cAAcA,OAAM;AACnC;AACA,SAAS,YAAYA,IAAG;AACtB,SAAO,MAAWA,MAAKA,MAAK,OAAY,OAAWA,MAAKA,MAAK,SAAYA,OAAM,QAAUA,OAAM,QAAU,SAAWA,MAAKA,MAAK,SAAYA,OAAM,YAAY,SAAWA,MAAKA,MAAK;AACnL;AACA,SAAS,qBAAqBA,IAAG;AAC/B,SAAO,YAAYA,EAAC,KAAKA,OAAM,YAAYA,OAAM,wBAAwBA,OAAM;AACjF;AACA,SAAS,YAAYA,IAAG,MAAM,SAAS;AACrC,MAAI,wBAAwB,qBAAqBA,EAAC;AAClD,MAAI,YAAY,yBAAyB,CAAC,aAAaA,EAAC;AACxD,UAAQ,UAAU,wBAAwB,yBAAyBA,OAAM,cAAcA,OAAM,4BAA4BA,OAAM,6BAA6BA,OAAM,2BAA2BA,OAAM,6BAA6BA,OAAM,cAAc,EAAE,SAAS,cAAc,CAAC,cAAc,qBAAqB,IAAI,KAAK,CAAC,aAAa,IAAI,KAAKA,OAAM,cAAc,SAAS,cAAc;AAC9X;AACA,SAAS,iBAAiBA,IAAG;AAC3B,SAAO,YAAYA,EAAC,KAAKA,OAAM,YAAY,CAAC,aAAaA,EAAC,KAAKA,OAAM,cAAcA,OAAM,iBAAiBA,OAAM,cAAcA,OAAM,cAAcA,OAAM,4BAA4BA,OAAM,6BAA6BA,OAAM,2BAA2BA,OAAM,4BAA4BA,OAAM,cAAcA,OAAM,kBAAkBA,OAAM,iBAAiBA,OAAM,oBAAoBA,OAAM,sBAAsBA,OAAM,eAAeA,OAAM,qBAAqBA,OAAM,qBAAqBA,OAAM,qBAAqBA,OAAM,gBAAgBA,OAAM,sBAAsBA,OAAM;AACnjB;AACA,SAAS,gBAAgBA,IAAG;AAC1B,SAAO,CAAC,aAAaA,EAAC,KAAKA,OAAM;AACnC;AACA,SAAS,YAAYZ,SAAQqB,MAAK;AAChC,MAAI,QAAQrB,QAAO,WAAWqB,IAAG,GAC/B;AACF,MAAI,SAAS,SAAU,SAAS,SAAUA,OAAM,IAAIrB,QAAO,QAAQ;AACjE,aAASA,QAAO,WAAWqB,OAAM,CAAC;AAClC,QAAI,UAAU,SAAU,UAAU,OAAQ;AACxC,cAAQ,QAAQ,SAAU,OAAQ,SAAS,QAAS;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoBrB,SAAQ;AACnC,MAAI,iBAAiB;AACrB,SAAO,eAAe,KAAKA,OAAM;AACnC;AACA,IAAI,cAAc;AAAlB,IACE,eAAe;AADjB,IAEE,gBAAgB;AAFlB,IAGE,eAAe;AAHjB,IAIE,eAAe;AACjB,SAAS,kBAAkBA,SAAQ,gBAAgB,gBAAgB,WAAW,mBAAmB,aAAa,aAAa,SAAS;AAClI,MAAI;AACJ,MAAI,OAAO;AACX,MAAI,WAAW;AACf,MAAI,eAAe;AACnB,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,cAAc;AACrC,MAAI,oBAAoB;AACxB,MAAI,QAAQ,iBAAiB,YAAYA,SAAQ,CAAC,CAAC,KAAK,gBAAgB,YAAYA,SAAQA,QAAO,SAAS,CAAC,CAAC;AAC9G,MAAI,kBAAkB,aAAa;AACjC,SAAK,IAAI,GAAG,IAAIA,QAAO,QAAQ,QAAQ,QAAU,KAAK,IAAI,KAAK;AAC7D,aAAO,YAAYA,SAAQ,CAAC;AAC5B,UAAI,CAAC,YAAY,IAAI,GAAG;AACtB,eAAO;AAAA,MACT;AACA,cAAQ,SAAS,YAAY,MAAM,UAAU,OAAO;AACpD,iBAAW;AAAA,IACb;AAAA,EACF,OAAO;AACL,SAAK,IAAI,GAAG,IAAIA,QAAO,QAAQ,QAAQ,QAAU,KAAK,IAAI,KAAK;AAC7D,aAAO,YAAYA,SAAQ,CAAC;AAC5B,UAAI,SAAS,gBAAgB;AAC3B,uBAAe;AACf,YAAI,kBAAkB;AACpB,4BAAkB,mBAAmB,IAAI,oBAAoB,IAAI,aAAaA,QAAO,oBAAoB,CAAC,MAAM;AAChH,8BAAoB;AAAA,QACtB;AAAA,MACF,WAAW,CAAC,YAAY,IAAI,GAAG;AAC7B,eAAO;AAAA,MACT;AACA,cAAQ,SAAS,YAAY,MAAM,UAAU,OAAO;AACpD,iBAAW;AAAA,IACb;AACA,sBAAkB,mBAAmB,oBAAoB,IAAI,oBAAoB,IAAI,aAAaA,QAAO,oBAAoB,CAAC,MAAM;AAAA,EACtI;AACA,MAAI,CAAC,gBAAgB,CAAC,iBAAiB;AACrC,QAAI,SAAS,CAAC,eAAe,CAAC,kBAAkBA,OAAM,GAAG;AACvD,aAAO;AAAA,IACT;AACA,WAAO,gBAAgB,sBAAsB,eAAe;AAAA,EAC9D;AACA,MAAI,iBAAiB,KAAK,oBAAoBA,OAAM,GAAG;AACrD,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa;AAChB,WAAO,kBAAkB,eAAe;AAAA,EAC1C;AACA,SAAO,gBAAgB,sBAAsB,eAAe;AAC9D;AACA,SAAS,YAAY,OAAOA,SAAQ,OAAO,OAAO,SAAS;AACzD,QAAM,OAAO,WAAY;AACvB,QAAIA,QAAO,WAAW,GAAG;AACvB,aAAO,MAAM,gBAAgB,sBAAsB,OAAO;AAAA,IAC5D;AACA,QAAI,CAAC,MAAM,cAAc;AACvB,UAAI,2BAA2B,QAAQA,OAAM,MAAM,MAAM,yBAAyB,KAAKA,OAAM,GAAG;AAC9F,eAAO,MAAM,gBAAgB,sBAAsB,MAAMA,UAAS,MAAM,MAAMA,UAAS;AAAA,MACzF;AAAA,IACF;AACA,QAAI,SAAS,MAAM,SAAS,KAAK,IAAI,GAAG,KAAK;AAC7C,QAAI,YAAY,MAAM,cAAc,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,WAAW,EAAE,GAAG,MAAM,YAAY,MAAM;AAC9G,QAAI,iBAAiB,SAAS,MAAM,YAAY,MAAM,SAAS,MAAM;AACrE,aAAS,cAAcA,SAAQ;AAC7B,aAAO,sBAAsB,OAAOA,OAAM;AAAA,IAC5C;AACA,YAAQ,kBAAkBA,SAAQ,gBAAgB,MAAM,QAAQ,WAAW,eAAe,MAAM,aAAa,MAAM,eAAe,CAAC,OAAO,OAAO,GAAG;AAAA,MAClJ,KAAK;AACH,eAAOA;AAAA,MACT,KAAK;AACH,eAAO,MAAMA,QAAO,QAAQ,MAAM,IAAI,IAAI;AAAA,MAC5C,KAAK;AACH,eAAO,MAAM,YAAYA,SAAQ,MAAM,MAAM,IAAI,kBAAkB,aAAaA,SAAQ,MAAM,CAAC;AAAA,MACjG,KAAK;AACH,eAAO,MAAM,YAAYA,SAAQ,MAAM,MAAM,IAAI,kBAAkB,aAAa,WAAWA,SAAQ,SAAS,GAAG,MAAM,CAAC;AAAA,MACxH,KAAK;AACH,eAAO,MAAM,aAAaA,OAAM,IAAI;AAAA,MACtC;AACE,cAAM,IAAI,UAAU,wCAAwC;AAAA,IAChE;AAAA,EACF,EAAE;AACJ;AACA,SAAS,YAAYA,SAAQ,gBAAgB;AAC3C,MAAI,kBAAkB,oBAAoBA,OAAM,IAAI,OAAO,cAAc,IAAI;AAC7E,MAAI,OAAOA,QAAOA,QAAO,SAAS,CAAC,MAAM;AACzC,MAAI,OAAO,SAASA,QAAOA,QAAO,SAAS,CAAC,MAAM,QAAQA,YAAW;AACrE,MAAI,QAAQ,OAAO,MAAM,OAAO,KAAK;AACrC,SAAO,kBAAkB,QAAQ;AACnC;AACA,SAAS,kBAAkBA,SAAQ;AACjC,SAAOA,QAAOA,QAAO,SAAS,CAAC,MAAM,OAAOA,QAAO,MAAM,GAAG,EAAE,IAAIA;AACpE;AACA,SAAS,WAAWA,SAAQ,OAAO;AACjC,MAAI,SAAS;AACb,MAAI,SAAS,WAAY;AACvB,QAAI,SAASA,QAAO,QAAQ,IAAI;AAChC,aAAS,WAAW,KAAK,SAASA,QAAO;AACzC,WAAO,YAAY;AACnB,WAAO,SAASA,QAAO,MAAM,GAAG,MAAM,GAAG,KAAK;AAAA,EAChD,EAAE;AACF,MAAI,mBAAmBA,QAAO,CAAC,MAAM,QAAQA,QAAO,CAAC,MAAM;AAC3D,MAAI;AACJ,MAAI;AACJ,SAAO,QAAQ,OAAO,KAAKA,OAAM,GAAG;AAClC,QAAI,SAAS,MAAM,CAAC,GAClBG,QAAO,MAAM,CAAC;AAChB,mBAAeA,MAAK,CAAC,MAAM;AAC3B,cAAU,UAAU,CAAC,oBAAoB,CAAC,gBAAgBA,UAAS,KAAK,OAAO,MAAM,SAASA,OAAM,KAAK;AACzG,uBAAmB;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,SAASA,OAAM,OAAO;AAC7B,MAAIA,UAAS,MAAMA,MAAK,CAAC,MAAM,IAAK,QAAOA;AAC3C,MAAI,UAAU;AACd,MAAI;AACJ,MAAIe,SAAQ,GACVC,MACA,OAAO,GACP,OAAO;AACT,MAAI,SAAS;AACb,SAAO,QAAQ,QAAQ,KAAKhB,KAAI,GAAG;AACjC,WAAO,MAAM;AACb,QAAI,OAAOe,SAAQ,OAAO;AACxB,MAAAC,OAAM,OAAOD,SAAQ,OAAO;AAC5B,gBAAU,OAAOf,MAAK,MAAMe,QAAOC,IAAG;AACtC,MAAAD,SAAQC,OAAM;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACA,YAAU;AACV,MAAIhB,MAAK,SAASe,SAAQ,SAAS,OAAOA,QAAO;AAC/C,cAAUf,MAAK,MAAMe,QAAO,IAAI,IAAI,OAAOf,MAAK,MAAM,OAAO,CAAC;AAAA,EAChE,OAAO;AACL,cAAUA,MAAK,MAAMe,MAAK;AAAA,EAC5B;AACA,SAAO,OAAO,MAAM,CAAC;AACvB;AACA,SAAS,aAAalB,SAAQ;AAC5B,MAAI,SAAS;AACb,MAAI,OAAO;AACX,MAAI;AACJ,WAAS,IAAI,GAAG,IAAIA,QAAO,QAAQ,QAAQ,QAAU,KAAK,IAAI,KAAK;AACjE,WAAO,YAAYA,SAAQ,CAAC;AAC5B,gBAAY,iBAAiB,IAAI;AACjC,QAAI,CAAC,aAAa,YAAY,IAAI,GAAG;AACnC,gBAAUA,QAAO,CAAC;AAClB,UAAI,QAAQ,MAAS,WAAUA,QAAO,IAAI,CAAC;AAAA,IAC7C,OAAO;AACL,gBAAU,aAAa,UAAU,IAAI;AAAA,IACvC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,OAAO,OAAO,QAAQ;AAC/C,MAAI,UAAU,IACZ,OAAO,MAAM,KACb,OACA,QACAa;AACF,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,IAAAA,SAAQ,OAAO,KAAK;AACpB,QAAI,MAAM,UAAU;AAClB,MAAAA,SAAQ,MAAM,SAAS,KAAK,QAAQ,OAAO,KAAK,GAAGA,MAAK;AAAA,IAC1D;AACA,QAAI,UAAU,OAAO,OAAOA,QAAO,OAAO,KAAK,KAAK,OAAOA,WAAU,eAAe,UAAU,OAAO,OAAO,MAAM,OAAO,KAAK,GAAG;AAC/H,UAAI,YAAY,GAAI,YAAW,OAAO,CAAC,MAAM,eAAe,MAAM;AAClE,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AACA,QAAM,MAAM;AACZ,QAAM,OAAO,MAAM,UAAU;AAC/B;AACA,SAAS,mBAAmB,OAAO,OAAO,QAAQ,SAAS;AACzD,MAAI,UAAU,IACZ,OAAO,MAAM,KACb,OACA,QACAA;AACF,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,IAAAA,SAAQ,OAAO,KAAK;AACpB,QAAI,MAAM,UAAU;AAClB,MAAAA,SAAQ,MAAM,SAAS,KAAK,QAAQ,OAAO,KAAK,GAAGA,MAAK;AAAA,IAC1D;AACA,QAAI,UAAU,OAAO,QAAQ,GAAGA,QAAO,MAAM,MAAM,OAAO,IAAI,KAAK,OAAOA,WAAU,eAAe,UAAU,OAAO,QAAQ,GAAG,MAAM,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7J,UAAI,CAAC,WAAW,YAAY,IAAI;AAC9B,mBAAW,iBAAiB,OAAO,KAAK;AAAA,MAC1C;AACA,UAAI,MAAM,QAAQ,mBAAmB,MAAM,KAAK,WAAW,CAAC,GAAG;AAC7D,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW;AAAA,MACb;AACA,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AACA,QAAM,MAAM;AACZ,QAAM,OAAO,WAAW;AAC1B;AACA,SAAS,iBAAiB,OAAO,OAAO,QAAQ;AAC9C,MAAI,UAAU,IACZ,OAAO,MAAM,KACb,gBAAgB,OAAO,KAAK,MAAM,GAClC,OACA,QACA,WACA,aACA;AACF,OAAK,QAAQ,GAAG,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,iBAAa;AACb,QAAI,YAAY,GAAI,eAAc;AAClC,QAAI,MAAM,aAAc,eAAc;AACtC,gBAAY,cAAc,KAAK;AAC/B,kBAAc,OAAO,SAAS;AAC9B,QAAI,MAAM,UAAU;AAClB,oBAAc,MAAM,SAAS,KAAK,QAAQ,WAAW,WAAW;AAAA,IAClE;AACA,QAAI,CAAC,UAAU,OAAO,OAAO,WAAW,OAAO,KAAK,GAAG;AACrD;AAAA,IACF;AACA,QAAI,MAAM,KAAK,SAAS,KAAM,eAAc;AAC5C,kBAAc,MAAM,QAAQ,MAAM,eAAe,MAAM,MAAM,OAAO,MAAM,eAAe,KAAK;AAC9F,QAAI,CAAC,UAAU,OAAO,OAAO,aAAa,OAAO,KAAK,GAAG;AACvD;AAAA,IACF;AACA,kBAAc,MAAM;AACpB,eAAW;AAAA,EACb;AACA,QAAM,MAAM;AACZ,QAAM,OAAO,MAAM,UAAU;AAC/B;AACA,SAAS,kBAAkB,OAAO,OAAO,QAAQ,SAAS;AACxD,MAAI,UAAU,IACZ,OAAO,MAAM,KACb,gBAAgB,OAAO,KAAK,MAAM,GAClC,OACA,QACA,WACA,aACA,cACA;AACF,MAAI,MAAM,aAAa,MAAM;AAC3B,kBAAc,KAAK;AAAA,EACrB,WAAW,OAAO,MAAM,aAAa,YAAY;AAC/C,kBAAc,KAAK,MAAM,QAAQ;AAAA,EACnC,WAAW,MAAM,UAAU;AACzB,UAAM,IAAI,UAAU,0CAA0C;AAAA,EAChE;AACA,OAAK,QAAQ,GAAG,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,iBAAa;AACb,QAAI,CAAC,WAAW,YAAY,IAAI;AAC9B,oBAAc,iBAAiB,OAAO,KAAK;AAAA,IAC7C;AACA,gBAAY,cAAc,KAAK;AAC/B,kBAAc,OAAO,SAAS;AAC9B,QAAI,MAAM,UAAU;AAClB,oBAAc,MAAM,SAAS,KAAK,QAAQ,WAAW,WAAW;AAAA,IAClE;AACA,QAAI,CAAC,UAAU,OAAO,QAAQ,GAAG,WAAW,MAAM,MAAM,IAAI,GAAG;AAC7D;AAAA,IACF;AACA,mBAAe,MAAM,QAAQ,QAAQ,MAAM,QAAQ,OAAO,MAAM,QAAQ,MAAM,KAAK,SAAS;AAC5F,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,mBAAmB,MAAM,KAAK,WAAW,CAAC,GAAG;AAC7D,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,kBAAc,MAAM;AACpB,QAAI,cAAc;AAChB,oBAAc,iBAAiB,OAAO,KAAK;AAAA,IAC7C;AACA,QAAI,CAAC,UAAU,OAAO,QAAQ,GAAG,aAAa,MAAM,YAAY,GAAG;AACjE;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,mBAAmB,MAAM,KAAK,WAAW,CAAC,GAAG;AAC7D,oBAAc;AAAA,IAChB,OAAO;AACL,oBAAc;AAAA,IAChB;AACA,kBAAc,MAAM;AACpB,eAAW;AAAA,EACb;AACA,QAAM,MAAM;AACZ,QAAM,OAAO,WAAW;AAC1B;AACA,SAAS,WAAW,OAAO,QAAQ,UAAU;AAC3C,MAAI,SAAS,UAAU,OAAO,QAAQP,OAAM;AAC5C,aAAW,WAAW,MAAM,gBAAgB,MAAM;AAClD,OAAK,QAAQ,GAAG,SAAS,SAAS,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACpE,IAAAA,QAAO,SAAS,KAAK;AACrB,SAAKA,MAAK,cAAcA,MAAK,eAAe,CAACA,MAAK,cAAcV,SAAQ,MAAM,MAAM,YAAY,kBAAkBU,MAAK,gBAAgB,CAACA,MAAK,aAAaA,MAAK,UAAU,MAAM,IAAI;AACjL,UAAI,UAAU;AACZ,YAAIA,MAAK,SAASA,MAAK,eAAe;AACpC,gBAAM,MAAMA,MAAK,cAAc,MAAM;AAAA,QACvC,OAAO;AACL,gBAAM,MAAMA,MAAK;AAAA,QACnB;AAAA,MACF,OAAO;AACL,cAAM,MAAM;AAAA,MACd;AACA,UAAIA,MAAK,WAAW;AAClB,gBAAQ,MAAM,SAASA,MAAK,GAAG,KAAKA,MAAK;AACzC,YAAI,UAAU,KAAKA,MAAK,SAAS,MAAM,qBAAqB;AAC1D,oBAAUA,MAAK,UAAU,QAAQ,KAAK;AAAA,QACxC,WAAW,gBAAgB,KAAKA,MAAK,WAAW,KAAK,GAAG;AACtD,oBAAUA,MAAK,UAAU,KAAK,EAAE,QAAQ,KAAK;AAAA,QAC/C,OAAO;AACL,gBAAM,IAAI,UAAU,OAAOA,MAAK,MAAM,iCAAiC,QAAQ,SAAS;AAAA,QAC1F;AACA,cAAM,OAAO;AAAA,MACf;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO,OAAO,QAAQ,OAAO,SAAS,OAAO,YAAY;AAC1E,QAAM,MAAM;AACZ,QAAM,OAAO;AACb,MAAI,CAAC,WAAW,OAAO,QAAQ,KAAK,GAAG;AACrC,eAAW,OAAO,QAAQ,IAAI;AAAA,EAChC;AACA,MAAIA,QAAO,UAAU,KAAK,MAAM,IAAI;AACpC,MAAI,UAAU;AACd,MAAI;AACJ,MAAI,OAAO;AACT,YAAQ,MAAM,YAAY,KAAK,MAAM,YAAY;AAAA,EACnD;AACA,MAAI,gBAAgBA,UAAS,qBAAqBA,UAAS,kBACzD,gBACA;AACF,MAAI,eAAe;AACjB,qBAAiB,MAAM,WAAW,QAAQ,MAAM;AAChD,gBAAY,mBAAmB;AAAA,EACjC;AACA,MAAI,MAAM,QAAQ,QAAQ,MAAM,QAAQ,OAAO,aAAa,MAAM,WAAW,KAAK,QAAQ,GAAG;AAC3F,cAAU;AAAA,EACZ;AACA,MAAI,aAAa,MAAM,eAAe,cAAc,GAAG;AACrD,UAAM,OAAO,UAAU;AAAA,EACzB,OAAO;AACL,QAAI,iBAAiB,aAAa,CAAC,MAAM,eAAe,cAAc,GAAG;AACvE,YAAM,eAAe,cAAc,IAAI;AAAA,IACzC;AACA,QAAIA,UAAS,mBAAmB;AAC9B,UAAI,SAAS,OAAO,KAAK,MAAM,IAAI,EAAE,WAAW,GAAG;AACjD,0BAAkB,OAAO,OAAO,MAAM,MAAM,OAAO;AACnD,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM;AAAA,QAChD;AAAA,MACF,OAAO;AACL,yBAAiB,OAAO,OAAO,MAAM,IAAI;AACzC,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM,MAAM;AAAA,QACtD;AAAA,MACF;AAAA,IACF,WAAWA,UAAS,kBAAkB;AACpC,UAAI,SAAS,MAAM,KAAK,WAAW,GAAG;AACpC,YAAI,MAAM,iBAAiB,CAAC,cAAc,QAAQ,GAAG;AACnD,6BAAmB,OAAO,QAAQ,GAAG,MAAM,MAAM,OAAO;AAAA,QAC1D,OAAO;AACL,6BAAmB,OAAO,OAAO,MAAM,MAAM,OAAO;AAAA,QACtD;AACA,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM;AAAA,QAChD;AAAA,MACF,OAAO;AACL,0BAAkB,OAAO,OAAO,MAAM,IAAI;AAC1C,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM,MAAM;AAAA,QACtD;AAAA,MACF;AAAA,IACF,WAAWA,UAAS,mBAAmB;AACrC,UAAI,MAAM,QAAQ,KAAK;AACrB,oBAAY,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO;AAAA,MACtD;AAAA,IACF,WAAWA,UAAS,sBAAsB;AACxC,aAAO;AAAA,IACT,OAAO;AACL,UAAI,MAAM,YAAa,QAAO;AAC9B,YAAM,IAAI,UAAU,4CAA4CA,KAAI;AAAA,IACtE;AACA,QAAI,MAAM,QAAQ,QAAQ,MAAM,QAAQ,KAAK;AAC3C,eAAS,UAAU,MAAM,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,MAAM,GAAG,EAAE,QAAQ,MAAM,KAAK;AAC7F,UAAI,MAAM,IAAI,CAAC,MAAM,KAAK;AACxB,iBAAS,MAAM;AAAA,MACjB,WAAW,OAAO,MAAM,GAAG,EAAE,MAAM,sBAAsB;AACvD,iBAAS,OAAO,OAAO,MAAM,EAAE;AAAA,MACjC,OAAO;AACL,iBAAS,OAAO,SAAS;AAAA,MAC3B;AACA,YAAM,OAAO,SAAS,MAAM,MAAM;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,QAAQ,OAAO;AAC7C,MAAI,UAAU,CAAC,GACb,oBAAoB,CAAC,GACrB,OACA;AACF,cAAY,QAAQ,SAAS,iBAAiB;AAC9C,OAAK,QAAQ,GAAG,SAAS,kBAAkB,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAC7E,UAAM,WAAW,KAAK,QAAQ,kBAAkB,KAAK,CAAC,CAAC;AAAA,EACzD;AACA,QAAM,iBAAiB,IAAI,MAAM,MAAM;AACzC;AACA,SAAS,YAAY,QAAQ,SAAS,mBAAmB;AACvD,MAAI,eAAe,OAAO;AAC1B,MAAI,WAAW,QAAQV,SAAQ,MAAM,MAAM,UAAU;AACnD,YAAQ,QAAQ,QAAQ,MAAM;AAC9B,QAAI,UAAU,IAAI;AAChB,UAAI,kBAAkB,QAAQ,KAAK,MAAM,IAAI;AAC3C,0BAAkB,KAAK,KAAK;AAAA,MAC9B;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,MAAM;AACnB,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,sBAAY,OAAO,KAAK,GAAG,SAAS,iBAAiB;AAAA,QACvD;AAAA,MACF,OAAO;AACL,wBAAgB,OAAO,KAAK,MAAM;AAClC,aAAK,QAAQ,GAAG,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,sBAAY,OAAO,cAAc,KAAK,CAAC,GAAG,SAAS,iBAAiB;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,OAAO,OAAO,SAAS;AAC9B,YAAU,WAAW,CAAC;AACtB,MAAI,QAAQ,IAAI,MAAM,OAAO;AAC7B,MAAI,CAAC,MAAM,OAAQ,wBAAuB,OAAO,KAAK;AACtD,MAAIiB,SAAQ;AACZ,MAAI,MAAM,UAAU;AAClB,IAAAA,SAAQ,MAAM,SAAS,KAAK;AAAA,MAC1B,IAAIA;AAAA,IACN,GAAG,IAAIA,MAAK;AAAA,EACd;AACA,MAAI,UAAU,OAAO,GAAGA,QAAO,MAAM,IAAI,EAAG,QAAO,MAAM,OAAO;AAChE,SAAO;AACT;AACA,IAAI,SAAS;AACb,IAAI,SAAS;AAAA,EACX,MAAM;AACR;AACA,SAAS,QAAQ,MAAM,IAAI;AACzB,SAAO,WAAY;AACjB,UAAM,IAAI,MAAM,mBAAmB,OAAO,wCAA6C,KAAK,yCAAyC;AAAA,EACvI;AACF;AACA,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,iBAAiBI;AACrB,IAAI,OAAO,OAAO;AAClB,IAAI,UAAU,OAAO;AACrB,IAAI,OAAO,OAAO;AAClB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AAAA,EACV,QAAQD;AAAA,EACR;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,WAAW,QAAQ,YAAY,MAAM;AACzC,IAAI,cAAc,QAAQ,eAAe,SAAS;AAClD,IAAI,WAAW,QAAQ,YAAY,MAAM;AACzC,IAAI,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAO,eAAQ;;;ACxoFf,IAAO,kBAAS,SAAU,UAAU;AAClC,MAAI,SAAS,QAAQ,GAAG,IAAI,EAAG,QAAO;AACtC,SAAO,IAAI,OAAO,SAAS,MAAM,GAAG,EAAE,IAAI,CAAC;AAC7C;;;ACCA,IAAI,SAAS,OAAO,SAAS;AAC7B,IAAI,QAAQ,OAAO,QAAQ;AAC3B,IAAI,OAAO,OAAO,iBAAW,eAAe,aAAO,OAAO,eAAS;AACnE,IAAI,KAAK,CAAC,UAAU,MAAM,OAAO,uBAAS,GAAG,UAAU;AACvD,IAAI,YAAY;AAChB,IAAI,qBAAqB,SAASM,oBAAmB,UAAU;AAC7D,MAAI,OAAO,GAAG,aAAa,UAAU,MAAM;AAC3C,MAAI;AACJ,MAAI;AACF,WAAO,GAAG,SAAS,QAAQ;AAAA,EAC7B,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,SAASC,gBAAe,UAAU;AACrD,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,OAAG,SAAS,UAAU,QAAQ,SAAU,KAAK,MAAM;AACjD,UAAI,IAAK,QAAO,OAAO,GAAG;AAC1B,SAAG,KAAK,UAAU,SAAUC,MAAK,MAAM;AACrC,YAAIA,KAAK,QAAO,QAAQ;AAAA,UACtB;AAAA,QACF,CAAC;AACD,eAAO,QAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,qBAAqB,SAASC,oBAAmB,UAAU;AAC7D,MAAI,UAAU,IAAI,YAAY,OAAO;AACrC,MAAI,IAAI,KAAK,aAAa,QAAQ;AAClC,MAAI,OAAO,QAAQ,OAAO,CAAC;AAC3B,MAAI;AACJ,MAAI;AACF,WAAO,KAAK,SAAS,QAAQ;AAAA,EAC/B,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,SAASC,gBAAe,UAAU;AACrD,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,QAAI,UAAU,IAAI,YAAY,OAAO;AACrC,SAAK,SAAS,QAAQ,EAAE,KAAK,SAAU,GAAG;AACxC,UAAI,OAAO,QAAQ,OAAO,CAAC;AAC3B,WAAK,KAAK,QAAQ,EAAE,KAAK,SAAU,MAAM;AACvC,eAAO,QAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,WAAY;AACnB,eAAO,QAAQ;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,MAAM;AAAA,EACjB,CAAC;AACH;AACA,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,cAAc,SAASC,aAAYC,MAAK,MAAM,SAAS;AACzD,MAAI,QAAQA,KAAI,YAAY,IAAI;AAChC,MAAI,QAAQ,IAAI;AACd,WAAOA,KAAI,UAAU,GAAG,KAAK,IAAI,UAAUA,KAAI,UAAU,QAAQ,KAAK,MAAM;AAAA,EAC9E;AACA,SAAOA,KAAI,SAAS;AACtB;AACA,IAAI,YAAY,SAASC,WAAU,WAAW,MAAM,SAAS;AAC3D,SAAO,KAAK,QAAQ,WAAW,EAAE;AACjC,MAAI,SAAS,CAAC;AACd,UAAQ,WAAW;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AACH,UAAI,OAAO,WAAW,aAAa;AACjC,YAAI,KAAK,QAAQ,SAAS,IAAI,IAAI;AAChC,iBAAO,IAAI,OAAO,YAAY,KAAK,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG;AAAA,QACrF,WAAW,KAAK,QAAQ,iBAAiB,IAAI,IAAI;AAC/C,iBAAO,IAAI,OAAO,YAAY,KAAK,UAAU,KAAK,QAAQ,iBAAiB,IAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AAAA,QACpG;AAAA,MACF;AACA,eAAS,UAAU,IAAI;AACvB;AAAA,IACF,KAAK;AACH,eAAS,cAAM,MAAM,IAAI;AACzB;AAAA,IACF,KAAK;AACH,eAASC,QAAW,IAAI;AACxB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,eAAS,KAAK,KAAK,IAAI;AACvB;AAAA,IACF;AACE,eAAS,QAAQ,MAAM,IAAI;AAAA,EAC/B;AACA,SAAO;AACT;AACO,SAAS,aAAa,UAAU,SAAS;AAC9C,MAAI,MAAM,gBAAQ,QAAQ;AAC1B,MAAI,MAAM;AACV,MAAI,OAAO;AACT,QAAI,MAAM,kBAAkB,QAAQ;AACpC,WAAO,IAAI;AACX,WAAO,IAAI;AAAA,EACb,WAAW,QAAQ;AACjB,QAAI,OAAO,mBAAmB,QAAQ;AACtC,WAAO,KAAK;AACZ,WAAO,KAAK;AAAA,EACd,OAAO;AACL,QAAI,QAAQ,mBAAmB,QAAQ;AACvC,WAAO,MAAM;AACb,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AAAA,IACL,MAAM,UAAU,KAAK,MAAM,OAAO;AAAA,IAClC;AAAA,EACF;AACF;AACO,SAAS,SAAS,UAAU;AACjC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAChF,OAAO,KAAK;AAAA,EACd;AACA,MAAI,MAAM,gBAAQ,QAAQ;AAC1B,MAAI,KAAK,QAAQ,gBAAgB,SAAS,iBAAiB;AAC3D,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,OAAG,QAAQ,EAAE,KAAK,SAAU,MAAM;AAChC,UAAI,OAAO,KAAK,MACd,OAAO,KAAK;AACd,UAAI;AACF,YAAI,MAAM,UAAU,KAAK,MAAM,OAAO;AACtC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,YAAI,UAAU,mBAAmB,WAAW,OAAO,IAAI;AACvD,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC,EAAE,MAAM,MAAM;AAAA,EACjB,CAAC;AACH;;;AClJA,IAAIC,UAAS,OAAO,SAAS;AAC7B,IAAIC,SAAQ,OAAO,QAAQ;AAC3B,IAAIC,QAAO,OAAO,iBAAW,eAAe,aAAO,OAAO,eAAS;AACnE,IAAIC,MAAK,CAACH,WAAU,MAAM,OAAO,uBAAS,GAAG,UAAU;AACvD,SAAS,QAAQ,MAAM;AACrB,MAAI,KAAK,WAAW,EAAG,QAAO;AAC9B,MAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,MAAI,UAAU,SAAS;AACvB,MAAII,OAAM;AACV,MAAI,eAAe;AACnB,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,WAAO,KAAK,WAAW,CAAC;AACxB,QAAI,SAAS,IAAI;AACf,UAAI,CAAC,cAAc;AACjB,QAAAA,OAAM;AACN;AAAA,MACF;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,MAAIA,SAAQ,GAAI,QAAO,UAAU,MAAM;AACvC,MAAI,WAAWA,SAAQ,EAAG,QAAO;AACjC,SAAO,KAAK,MAAM,GAAGA,IAAG;AAC1B;AASA,IAAI,kBAAkB,SAASC,iBAAgB,UAAU,SAAS;AAChE,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,IAAAC,IAAG,MAAM,QAAQ,QAAQ,GAAG;AAAA,MAC1B,WAAW;AAAA,IACb,GAAG,WAAY;AACb,MAAAA,IAAG,UAAU,UAAU,SAAS,QAAQ,SAAU,KAAK,MAAM;AAC3D,eAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,IAAI;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AAIA,IAAI,mBAAmB,SAASC,kBAAiB,UAAU;AACzD,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,WAAOC,IAAG,OAAO,UAAU,SAAU,KAAK;AACxC,aAAO,MAAM,OAAO,GAAG,IAAI,QAAQ;AAAA,IACrC,CAAC;AAAA,EACH,CAAC;AACH;AAWA,IAAI,kBAAkB,SAASC,iBAAgB,UAAU,SAAS;AAChE,MAAI,UAAU,IAAI,YAAY;AAC9B,MAAI,OAAO,QAAQ,OAAO,OAAO;AACjC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,SAAK,MAAM,QAAQ,QAAQ,GAAG;AAAA,MAC5B,WAAW;AAAA,IACb,CAAC,EAAE,KAAK,WAAY;AAClB,WAAK,UAAU,UAAU,IAAI,EAAE,KAAK,SAAS,MAAM;AAAA,IACrD,CAAC,EAAE,MAAM,WAAY;AACnB,WAAK,UAAU,UAAU,IAAI,EAAE,KAAK,SAAS,MAAM;AAAA,IACrD,CAAC;AAAA,EACH,CAAC;AACH;AAIA,IAAI,mBAAmB,SAASC,kBAAiB,UAAU;AACzD,SAAO,KAAK,OAAO,QAAQ;AAC7B;AAEA,IAAI,iBAAiB;AAErB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB,SAASC,eAAc,WAAW,MAAM,SAAS;AACnE,MAAI,SAAS;AACb,UAAQ,WAAW;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AACH,UAAI,OAAO,WAAW,aAAa;AACjC,iBAAS,kBAAkB,OAAO,QAAQ,UAAU,MAAM,MAAM,QAAQ,KAAK,CAAC;AAAA,MAChF,OAAO;AACL,iBAAS,oBAAoB,OAAO,QAAQ,UAAU,MAAM,MAAM,QAAQ,KAAK,CAAC;AAAA,MAClF;AACA;AAAA,IACF,KAAK;AACH,eAAS,cAAM,UAAU,MAAM,MAAM,QAAQ,KAAK;AAClD;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,eAASC,MAAK,KAAK,MAAM;AAAA,QACvB,OAAO,QAAQ;AAAA,MACjB,CAAC;AACD;AAAA,IACF;AACE,eAAS,QAAQ,UAAU,MAAM,MAAM,QAAQ,KAAK;AAAA,EACxD;AACA,SAAO;AACT;AAkBO,SAAS,UAAU,UAAU,SAAS;AAC3C,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAChF,WAAW,KAAK;AAAA,IAChB,OAAO;AAAA,EACT;AACA,MAAI,MAAM,gBAAQ,QAAQ;AAC1B,MAAI;AACJ,MAAI;AACF,WAAO,cAAc,KAAK,SAAS,OAAO;AAAA,EAC5C,SAAS,KAAK;AACZ,QAAI,UAAU,wBAAwB,WAAW,OAAO,IAAI;AAC5D,UAAM;AAAA,EACR;AACA,MAAI,KAAKC,SAAQ,iBAAiBC,UAAS,kBAAkB;AAC7D,SAAO,GAAG,UAAU,IAAI;AAC1B;AAUO,SAAS,WAAW,UAAU;AACnC,MAAI,KAAKC,SAAQ,kBAAkBC,UAAS,mBAAmB;AAC/D,SAAO,GAAG,QAAQ;AACpB;;;AChKA,SAASC,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAC7T,SAAS,gBAAgB,GAAG,GAAG;AAAE,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAAG;AAClH,SAAS,kBAAkB,GAAG,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAAE,QAAI,IAAI,EAAE,CAAC;AAAG,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,eAAe,EAAE,GAAG,GAAG,CAAC;AAAA,EAAG;AAAE;AACvO,SAAS,aAAa,GAAG,GAAG,GAAG;AAAE,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG;AAAG;AAC1K,SAAS,eAAe,GAAG;AAAE,MAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,SAAO,YAAYA,SAAQ,CAAC,IAAI,IAAI,IAAI;AAAI;AAC5G,SAAS,aAAa,GAAG,GAAG;AAAE,MAAI,YAAYA,SAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,QAAI,YAAYA,SAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAG;AAI3T,IAAI,cAAc,SAASE,eAAc;AACvC,SAAO;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,OAAO,KAAK;AAAA,IACZ,WAAW,KAAK;AAAA,EAClB;AACF;AACA,IAAI,UAAU,WAAY;AACxB,WAASC,SAAQ,UAAU;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,oBAAgB,MAAMA,QAAO;AAC7B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,KAAK,UAAU,SAAS,UAAU;AAAA,EACzC;AACA,SAAO,aAAaA,UAAS,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU;AAC7B,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,WAAK,WAAW;AAChB,WAAK,UAAU,SAAS,SAAS,KAAK,WAAW,CAAC,GAAG,YAAY,CAAC;AAClE,WAAK,aAAa;AAClB,WAAK,eAAe,CAAC;AACrB,WAAK,iBAAiB,SAAS,KAAK,OAAO,GAAG;AAAA,IAChD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAASC,MAAK,UAAU,WAAW,UAAU;AAClD,UAAI,QAAQ;AACZ,UAAI,WAAW,KAAK,QAAQ;AAC5B,UAAI,OAAO,KAAK,QAAQ,aAAa,YAAY;AAC/C,mBAAW,KAAK,QAAQ,SAAS,UAAU,SAAS;AAAA,MACtD;AACA,UAAI,WAAW,KAAK,SAAS,aAAa,YAAY,UAAU;AAAA,QAC9D,KAAK;AAAA,QACL,IAAI;AAAA,MACN,CAAC;AACD,UAAI,KAAK,WAAW,cAAc,SAAS,KAAK,WAAW,kBAAkB,OAAO;AAClF,YAAI;AACF,cAAI,gBAAgB,aAAa,UAAU,KAAK,OAAO,GACrD,OAAO,cAAc,MACrB,OAAO,cAAc;AACvB,cAAIC,aAAY,QAAQ,KAAK,SAAS,KAAK,MAAM,QAAQ;AACzD,cAAI,KAAK,QAAQ,kBAAkBA,cAAaA,aAAY,KAAK,QAAQ,iBAAiB,KAAK,IAAI,GAAG;AACpG,iBAAK,WAAW,UAAU,SAAS;AACnC,mBAAO,SAAS,IAAI,MAAM,eAAe,GAAG,KAAK;AAAA,UACnD;AACA,mBAAS,MAAM,MAAMA,UAAS;AAAA,QAChC,SAAS,KAAK;AACZ,mBAAS,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACF;AACA,eAAS,UAAU,KAAK,OAAO,EAAE,KAAK,SAAU,MAAM;AACpD,YAAIC,QAAO,KAAK,MACdC,QAAO,KAAK;AACd,YAAIF,aAAYE,SAAQA,MAAK,SAASA,MAAK,MAAM,QAAQ;AACzD,YAAI,MAAM,QAAQ,kBAAkBF,cAAaA,aAAY,MAAM,QAAQ,iBAAiB,KAAK,IAAI,GAAG;AACtG,gBAAM,WAAW,UAAU,SAAS;AACpC,iBAAO,SAAS,IAAI,MAAM,eAAe,GAAG,KAAK;AAAA,QACnD;AACA,iBAAS,MAAMC,OAAMD,UAAS;AAAA,MAChC,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,eAAO,SAAS,KAAK,KAAK;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,WAAW,WAAWG,MAAK,eAAe,UAAU;AACzE,UAAI,SAAS;AACb,UAAI,OAAO,aAAa,WAAY,YAAW,SAASC,YAAW;AAAA,MAAC;AACpE,UAAI,OAAO,cAAc,SAAU,aAAY,CAAC,SAAS;AACzD,UAAI,OAAO,UAAU;AACrB,UAAI,OAAO,SAASC,QAAO;AACzB,YAAI,CAAE,EAAE,KAAM,UAAS;AAAA,MACzB;AACA,gBAAU,QAAQ,SAAU,KAAK;AAC/B,eAAO,MAAM,KAAK,QAAQ,KAAK,WAAWF,MAAK,eAAe,IAAI;AAAA,MACpE,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU,WAAW,MAAM,UAAU;AACxD,UAAI,SAAS;AACb,UAAI,CAAC,SAAU,YAAW,SAASC,YAAW;AAAA,MAAC;AAC/C,UAAI,OAAO,OAAO,KAAK,IAAI;AAC3B,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,SAASC,QAAO;AACzB,YAAI,CAAE,EAAE,KAAM,UAAS;AAAA,MACzB;AACA,WAAK,QAAQ,SAAUF,MAAK;AAC1B,eAAO,MAAM,KAAK,QAAQ,UAAU,WAAWA,MAAK,KAAKA,IAAG,GAAG,IAAI;AAAA,MACrE,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAASG,YAAW,UAAU,WAAW;AAC9C,UAAI,UAAU,KAAK,QAAQ;AAC3B,UAAI,OAAO,KAAK,QAAQ,YAAY,YAAY;AAC9C,kBAAU,KAAK,QAAQ,QAAQ,UAAU,SAAS;AAAA,MACpD;AACA,UAAI,WAAW,KAAK,SAAS,aAAa,YAAY,SAAS;AAAA,QAC7D,KAAK;AAAA,QACL,IAAI;AAAA,MACN,CAAC;AACD,iBAAY,UAAU,KAAK,OAAO,EAAE,KAAK,WAAY;AAAA,MAAC,CAAC,EAAE,MAAM,WAAY;AAAA,MAAC,CAAC;AAAA,IAC/E;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,eAAS,OAAO,KAAK,cAAc;AACjC,YAAI,aAAa,KAAK,aAAa,GAAG;AACtC,YAAI,QAAQ,SAAS;AACnB,mBAAS,MAAM,YAAY;AACzB,iBAAK,UAAU,KAAK,EAAE;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAASC,WAAU,KAAK,WAAW;AACxC,UAAI,SAAS;AACb,UAAI,OAAO,QAAQ,KAAK,cAAc,CAAC,SAAS,KAAK,SAAS,CAAC;AAC/D,UAAI,KAAM;AACV,UAAI,UAAU,KAAK,QAAQ;AAC3B,UAAI,OAAO,KAAK,QAAQ,YAAY,YAAY;AAC9C,kBAAU,KAAK,QAAQ,QAAQ,KAAK,SAAS;AAAA,MAC/C;AACA,UAAI,WAAW,KAAK,SAAS,aAAa,YAAY,SAAS;AAAA,QAC7D;AAAA,QACA,IAAI;AAAA,MACN,CAAC;AACD,UAAI,WAAW,QAAQ,KAAK,cAAc,CAAC,KAAK,SAAS,CAAC;AAC1D,cAAQ,KAAK,cAAc,CAAC,KAAK,SAAS,GAAG,CAAC,CAAC;AAC/C,UAAI,SAAS,QAAQ;AACnB,gBAAQ,KAAK,cAAc,CAAC,SAAS,KAAK,SAAS,GAAG,IAAI;AAC1D,YAAI,UAAU,SAASC,SAAQ,OAAO;AACpC,cAAI,OAAO,MAAM;AACjB,mBAAS,QAAQ,SAAU,SAAS;AAClC,gBAAI,OAAO,OAAO,WAAW,iBAAiB,QAAQ,CAAC,QAAQ,GAAG,IAAI,QAAQ,IAAI,MAAM,OAAO,WAAW,gBAAgB,GAAG;AAC7H,gBAAI;AACF,sBAAQ,MAAM,MAAM,QAAQ,aAAa;AAAA,YAC3C,SAAS,GAAG;AACV,kBAAI,KAAK,SAAS,KAAK,CAAC,EAAE,WAAW,EAAE,QAAQ,QAAQ,wBAAwB,IAAI,EAAG,OAAM;AAC5F,sBAAQ,MAAM,CAAC,QAAQ,GAAG,GAAG,QAAQ,aAAa;AAAA,YACpD;AAAA,UACF,CAAC;AACD,cAAI,eAAe,SAASC,gBAAe;AACzC,oBAAQ,OAAO,cAAc,CAAC,SAAS,KAAK,SAAS,GAAG,KAAK;AAC7D,qBAAS,QAAQ,SAAU,SAAS;AAClC,kBAAI,QAAQ,SAAU,SAAQ,SAAS;AAAA,YACzC,CAAC;AACD,mBAAO,eAAe;AAAA,UACxB;AACA,oBAAW,UAAU,MAAM,OAAO,OAAO,EAAE,KAAK,YAAY,EAAE,MAAM,YAAY;AAAA,QAClF;AACA,iBAAS,UAAU,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE,MAAM,WAAY;AAC/D,iBAAO,QAAQ;AAAA,YACb,MAAM,CAAC;AAAA,UACT,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,MAAM,KAAK,WAAWN,MAAK,eAAe,UAAU;AAClE,eAAS,KAAK,cAAc,CAAC,KAAK,SAAS,GAAG;AAAA,QAC5C,KAAKA;AAAA,QACL,eAAe,iBAAiB;AAAA,QAChC;AAAA,MACF,CAAC;AACD,WAAK,eAAe;AAAA,IACtB;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AACF,QAAQ,OAAO;AACf,IAAO,cAAQ;", "names": ["source", "later", "key", "stack", "F", "e", "r", "o", "c", "parse", "value", "type", "buffer", "start", "beforePropertyName", "afterPropertyName", "beforePropertyV<PERSON>ue", "beforeArrayValue", "afterProperty<PERSON><PERSON>ue", "afterArrayValue", "end", "stringify", "stack", "key", "pos", "value", "token", "value2", "start", "isDigit", "end", "parse", "value", "token", "value", "parse2", "parse", "_typeof", "o", "source", "key", "string", "exception", "buffer", "line", "map", "schema", "type", "extend", "construct", "lowercase", "uppercase", "camelcase", "c", "value", "sign", "hexadecimal", "binary", "_default", "start", "end", "str", "pos", "readFileInNodeSync", "readFileInNode", "err", "readFileInDenoSync", "readFileInDeno", "replaceLast", "str", "parseData", "parse2", "isDeno", "isBun", "YAML", "fs", "end", "writeFileInNode", "fs", "removeFileInNode", "fs", "writeFileInDeno", "removeFileInDeno", "stringifyData", "YAML", "isBun", "isDeno", "isBun", "isDeno", "_typeof", "o", "getDefaults", "Backend", "read", "timestamp", "data", "stat", "key", "callback", "done", "removeFile", "writeFile", "proceed", "proceedWrite"]}
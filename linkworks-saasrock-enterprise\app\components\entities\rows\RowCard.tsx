import clsx from "clsx";
import { Fragment, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ColumnDto } from "~/application/dtos/data/ColumnDto";
import { RowHeaderDisplayDto } from "~/application/dtos/data/RowHeaderDisplayDto";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowWithDetails } from "~/utils/db/entities/rows.db.server";
import RowDisplayHeaderHelper from "~/utils/helpers/RowDisplayHeaderHelper";
import RowDisplayValueHelper from "~/utils/helpers/RowDisplayValueHelper";

interface Props {
  entity: EntityWithDetails;
  item: RowWithDetails;
  layout: string;
  columns?: ColumnDto[];
  allEntities: EntityWithDetails[];
  routes?: EntitiesApi.Routes;
  actions?: (row: RowWithDetails) => {
    title?: string;
    href?: string;
    onClick?: () => void;
    isLoading?: boolean;
    render?: React.ReactNode;
  }[];
}
export default function RowCard({ entity, item, columns, layout, allEntities, routes, actions }: Props) {
  const { t } = useTranslation();
  const [headers, setHeaders] = useState<RowHeaderDisplayDto<RowWithDetails>[]>([]);

  useEffect(() => {
    setHeaders(RowDisplayHeaderHelper.getDisplayedHeaders({ entity, columns, layout, allEntities: allEntities, t, routes }));
  }, [entity, columns, layout, allEntities, t, routes]);

  // Check if we have only location fields to apply special formatting
  const nonDefaultHeaders = headers.filter((f) => !f.name?.startsWith("default."));
  const hasOnlyLocationField = nonDefaultHeaders.length === 1 &&
    (nonDefaultHeaders[0].name?.includes("location") || nonDefaultHeaders[0].name?.includes("Location"));

  // Debug logging
  const locationFields = headers.filter(h => h.name?.includes("location") || h.name?.includes("Location"));
  if (locationFields.length > 0) {
    console.log("RowCard: Location fields detected", {
      locationFieldCount: locationFields.length,
      locationFieldNames: locationFields.map(f => f.name),
      totalHeaders: headers.length,
      hasOnlyLocationField
    });
  }

  return (
    <div className={clsx("text-muted-foreground flex flex-row flex-wrap space-y-2 text-sm", !hasOnlyLocationField && "whitespace-nowrap justify-between")}>
      {headers.map((header, idx) => {
        const isLocationField = header.name?.includes("location") || header.name?.includes("Location");
        const shouldUseFullWidth = header.className?.includes("w-full") || hasOnlyLocationField || isLocationField;

        return (
          <div key={idx} className={clsx(
            "flex flex-col",
            isLocationField ? "w-full !w-full" : shouldUseFullWidth ? "w-full" : "w-full sm:w-1/2 md:w-1/3 lg:w-1/4",
            isLocationField && "min-w-0", // Allow text wrapping for location fields
            !isLocationField && header.className // Only apply header className if not location field
          )}>
            <div className="text-muted-foreground text-xs font-medium">{t(header.title)}</div>
            <div className={clsx(
              "text-foreground font-medium",
              isLocationField && "min-w-0 break-words" // Allow text wrapping for location fields
            )}>
              {RowDisplayValueHelper.displayRowValue(t, header, item, idx)}
            </div>
          </div>
        );
      })}
      {actions && (
        <div className="flex flex-col space-y-2">
          {actions(item).map((action, idx) => {
            return (
              <Fragment key={idx}>
                {action.render ?? (
                  <ButtonSecondary
                    className="w-full"
                    to={action.href}
                    isLoading={action.isLoading}
                    onClick={(e) => {
                      if (action.onClick) {
                        e.stopPropagation();
                        e.preventDefault();
                        action.onClick();
                      }
                    }}
                  >
                    <div className="flex w-full justify-center">{action.title}</div>
                  </ButtonSecondary>
                )}
              </Fragment>
            );
          })}
        </div>
      )}
    </div>
  );
}

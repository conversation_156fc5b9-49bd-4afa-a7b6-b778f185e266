{"version": 3, "sources": ["../../cookie/index.js", "../../@remix-run/server-runtime/dist/esm/warnings.js", "../../@remix-run/server-runtime/dist/esm/cookies.js", "../../@web3-storage/multipart-parser/esm/src/utils.js", "../../@web3-storage/multipart-parser/esm/src/search.js", "../../@web3-storage/multipart-parser/esm/src/index.js", "../../@remix-run/server-runtime/dist/esm/formData.js", "../../@remix-run/router/history.ts", "../../@remix-run/router/utils.ts", "../../@remix-run/router/router.ts", "../../@remix-run/server-runtime/dist/esm/mode.js", "../../@remix-run/server-runtime/dist/esm/errors.js", "../../@remix-run/server-runtime/dist/esm/responses.js", "../../turbo-stream/dist/turbo-stream.mjs", "../../@remix-run/server-runtime/dist/esm/headers.js", "../../@remix-run/server-runtime/dist/esm/single-fetch.js", "../../@remix-run/server-runtime/dist/esm/entry.js", "../../@remix-run/server-runtime/dist/esm/invariant.js", "../../@remix-run/server-runtime/dist/esm/routeMatching.js", "../../@remix-run/server-runtime/dist/esm/data.js", "../../@remix-run/server-runtime/dist/esm/routes.js", "../../@remix-run/server-runtime/dist/esm/markup.js", "../../@remix-run/server-runtime/dist/esm/serverHandoff.js", "../../@remix-run/server-runtime/dist/esm/dev.js", "../../@remix-run/server-runtime/dist/esm/deprecations.js", "../../@remix-run/server-runtime/dist/esm/server.js", "../../@remix-run/server-runtime/dist/esm/sessions.js", "../../@remix-run/server-runtime/dist/esm/sessions/cookieStorage.js", "../../@remix-run/server-runtime/dist/esm/sessions/memoryStorage.js", "../../@remix-run/server-runtime/dist/esm/upload/errors.js", "../../@remix-run/server-runtime/dist/esm/upload/memoryUploadHandler.js", "../../@remix-run/server-runtime/dist/esm/index.js"], "sourcesContent": ["/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar __toString = Object.prototype.toString\nvar __hasOwnProperty = Object.prototype.hasOwnProperty\n\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n */\n\nvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n */\n\nvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\n\nvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\n\nvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [opt]\n * @return {object}\n * @public\n */\n\nfunction parse(str, opt) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n\n  var obj = {};\n  var len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  var dec = (opt && opt.decode) || decode;\n  var index = 0;\n  var eqIdx = 0;\n  var endIdx = 0;\n\n  do {\n    eqIdx = str.indexOf('=', index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    endIdx = str.indexOf(';', index);\n\n    if (endIdx === -1) {\n      endIdx = len;\n    } else if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n      continue;\n    }\n\n    var keyStartIdx = startIndex(str, index, eqIdx);\n    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    var key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (!__hasOwnProperty.call(obj, key)) {\n      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n        valStartIdx++;\n        valEndIdx--;\n      }\n\n      var val = str.slice(valStartIdx, valEndIdx);\n      obj[key] = tryDecode(val, dec);\n    }\n\n    index = endIdx + 1\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str, index, max) {\n  do {\n    var code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str, index, min) {\n  while (index > min) {\n    var code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [opt]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, opt) {\n  var enc = (opt && opt.encode) || encodeURIComponent;\n\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n\n  var value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n\n  var str = name + '=' + value;\n  if (!opt) return str;\n\n  if (null != opt.maxAge) {\n    var maxAge = Math.floor(opt.maxAge);\n\n    if (!isFinite(maxAge)) {\n      throw new TypeError('option maxAge is invalid')\n    }\n\n    str += '; Max-Age=' + maxAge;\n  }\n\n  if (opt.domain) {\n    if (!domainValueRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!pathValueRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    var expires = opt.expires\n\n    if (!isDate(expires) || isNaN(expires.valueOf())) {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + expires.toUTCString()\n  }\n\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n\n  if (opt.secure) {\n    str += '; Secure';\n  }\n\n  if (opt.partitioned) {\n    str += '; Partitioned'\n  }\n\n  if (opt.priority) {\n    var priority = typeof opt.priority === 'string'\n      ? opt.priority.toLowerCase() : opt.priority;\n\n    switch (priority) {\n      case 'low':\n        str += '; Priority=Low'\n        break\n      case 'medium':\n        str += '; Priority=Medium'\n        break\n      case 'high':\n        str += '; Priority=High'\n        break\n      default:\n        throw new TypeError('option priority is invalid')\n    }\n  }\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string'\n      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction decode (str) {\n  return str.indexOf('%') !== -1\n    ? decodeURIComponent(str)\n    : str\n}\n\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */\n\nfunction isDate (val) {\n  return __toString.call(val) === '[object Date]';\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nconst alreadyWarned = {};\nfunction warnOnce(condition, message) {\n  if (!condition && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nexport { warnOnce };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { parse, serialize } from 'cookie';\nimport { warnOnce } from './warnings.js';\n\n/**\n * A HTTP cookie.\n *\n * A Cookie is a logical container for metadata about a HTTP cookie; its name\n * and options. But it doesn't contain a value. Instead, it has `parse()` and\n * `serialize()` methods that allow a single instance to be reused for\n * parsing/encoding multiple different values.\n *\n * @see https://remix.run/utils/cookies#cookie-api\n */\n\n/**\n * Creates a logical container for managing a browser cookie from the server.\n *\n * @see https://remix.run/utils/cookies#createcookie\n */\nconst createCookieFactory = ({\n  sign,\n  unsign\n}) => (name, cookieOptions = {}) => {\n  let {\n    secrets = [],\n    ...options\n  } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      // Max-Age takes precedence over Expires\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1000) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, {\n        ...options,\n        ...parseOptions\n      });\n      return name in cookies ? cookies[name] === \"\" ? \"\" : await decodeCookieValue(unsign, cookies[name], secrets) : null;\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(sign, value, secrets), {\n        ...options,\n        ...serializeOptions\n      });\n    }\n  };\n};\n/**\n * Returns true if an object is a Remix cookie container.\n *\n * @see https://remix.run/utils/cookies#iscookie\n */\nconst isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(sign, value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(unsign, value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\n\n// See: https://github.com/zloirock/core-js/blob/master/packages/core-js/modules/es.escape.js\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\n\n// See: https://github.com/zloirock/core-js/blob/master/packages/core-js/modules/es.unescape.js\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, `The \"${name}\" cookie has an \"expires\" property set. ` + `This will cause the expires value to not be updated when the session is committed. ` + `Instead, you should set the expires value when serializing the cookie. ` + `You can use \\`commitSession(session, { expires })\\` if using a session storage object, ` + `or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`);\n}\n\nexport { createCookieFactory, isCookie };\n", "export function stringToArray(s) {\n  const utf8 = unescape(encodeURIComponent(s));\n  return Uint8Array.from(utf8, (_, i) => utf8.charCodeAt(i));\n}\nexport function arrayToString(a) {\n  const utf8 = String.fromCharCode.apply(null, a);\n  return decodeURIComponent(escape(utf8));\n}\nexport function mergeArrays(...arrays) {\n  const out = new Uint8Array(arrays.reduce((total, arr) => total + arr.length, 0));\n  let offset = 0;\n  for (const arr of arrays) {\n    out.set(arr, offset);\n    offset += arr.length;\n  }\n  return out;\n}\nexport function arraysEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n  return true;\n}", "import {\n  stringToArray,\n  mergeArrays,\n  arrayToString\n} from './utils.js';\nfunction coerce(a) {\n  if (a instanceof Uint8Array) {\n    return index => a[index];\n  }\n  return a;\n}\nfunction jsmemcmp(buf1, pos1, buf2, pos2, len) {\n  const fn1 = coerce(buf1);\n  const fn2 = coerce(buf2);\n  for (let i = 0; i < len; ++i) {\n    if (fn1(pos1 + i) !== fn2(pos2 + i)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction createOccurenceTable(s) {\n  const table = new Array(256).fill(s.length);\n  if (s.length > 1) {\n    for (let i = 0; i < s.length - 1; i++) {\n      table[s[i]] = s.length - 1 - i;\n    }\n  }\n  return table;\n}\nconst MATCH = Symbol('Match');\nclass StreamSearch {\n  constructor(needle) {\n    this._lookbehind = new Uint8Array();\n    if (typeof needle === 'string') {\n      this._needle = needle = stringToArray(needle);\n    } else {\n      this._needle = needle;\n    }\n    this._lastChar = needle[needle.length - 1];\n    this._occ = createOccurenceTable(needle);\n  }\n  feed(chunk) {\n    let pos = 0;\n    let tokens;\n    const allTokens = [];\n    while (pos !== chunk.length) {\n      ;\n      [pos, ...tokens] = this._feed(chunk, pos);\n      allTokens.push(...tokens);\n    }\n    return allTokens;\n  }\n  end() {\n    const tail = this._lookbehind;\n    this._lookbehind = new Uint8Array();\n    return tail;\n  }\n  _feed(data, bufPos) {\n    const tokens = [];\n    let pos = -this._lookbehind.length;\n    if (pos < 0) {\n      while (pos < 0 && pos <= data.length - this._needle.length) {\n        const ch = this._charAt(data, pos + this._needle.length - 1);\n        if (ch === this._lastChar && this._memcmp(data, pos, this._needle.length - 1)) {\n          if (pos > -this._lookbehind.length) {\n            tokens.push(this._lookbehind.slice(0, this._lookbehind.length + pos));\n          }\n          tokens.push(MATCH);\n          this._lookbehind = new Uint8Array();\n          return [\n            pos + this._needle.length,\n            ...tokens\n          ];\n        } else {\n          pos += this._occ[ch];\n        }\n      }\n      if (pos < 0) {\n        while (pos < 0 && !this._memcmp(data, pos, data.length - pos)) {\n          pos++;\n        }\n      }\n      if (pos >= 0) {\n        tokens.push(this._lookbehind);\n        this._lookbehind = new Uint8Array();\n      } else {\n        const bytesToCutOff = this._lookbehind.length + pos;\n        if (bytesToCutOff > 0) {\n          tokens.push(this._lookbehind.slice(0, bytesToCutOff));\n          this._lookbehind = this._lookbehind.slice(bytesToCutOff);\n        }\n        this._lookbehind = Uint8Array.from(new Array(this._lookbehind.length + data.length), (_, i) => this._charAt(data, i - this._lookbehind.length));\n        return [\n          data.length,\n          ...tokens\n        ];\n      }\n    }\n    pos += bufPos;\n    while (pos <= data.length - this._needle.length) {\n      const ch = data[pos + this._needle.length - 1];\n      if (ch === this._lastChar && data[pos] === this._needle[0] && jsmemcmp(this._needle, 0, data, pos, this._needle.length - 1)) {\n        if (pos > bufPos) {\n          tokens.push(data.slice(bufPos, pos));\n        }\n        tokens.push(MATCH);\n        return [\n          pos + this._needle.length,\n          ...tokens\n        ];\n      } else {\n        pos += this._occ[ch];\n      }\n    }\n    if (pos < data.length) {\n      while (pos < data.length && (data[pos] !== this._needle[0] || !jsmemcmp(data, pos, this._needle, 0, data.length - pos))) {\n        ++pos;\n      }\n      if (pos < data.length) {\n        this._lookbehind = data.slice(pos);\n      }\n    }\n    if (pos > 0) {\n      tokens.push(data.slice(bufPos, pos < data.length ? pos : data.length));\n    }\n    return [\n      data.length,\n      ...tokens\n    ];\n  }\n  _charAt(data, pos) {\n    if (pos < 0) {\n      return this._lookbehind[this._lookbehind.length + pos];\n    }\n    return data[pos];\n  }\n  _memcmp(data, pos, len) {\n    return jsmemcmp(this._charAt.bind(this, data), pos, this._needle, 0, len);\n  }\n}\nclass ReadableStreamSearch {\n  constructor(needle, _readableStream) {\n    this._readableStream = _readableStream;\n    this._search = new StreamSearch(needle);\n  }\n  async *[Symbol.asyncIterator]() {\n    const reader = this._readableStream.getReader();\n    try {\n      while (true) {\n        const result = await reader.read();\n        if (result.done) {\n          break;\n        }\n        yield* this._search.feed(result.value);\n      }\n      const tail = this._search.end();\n      if (tail.length) {\n        yield tail;\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  }\n}\nconst EOQ = Symbol('End of Queue');\nclass QueueableStreamSearch {\n  constructor(needle) {\n    this._chunksQueue = [];\n    this._closed = false;\n    this._search = new StreamSearch(needle);\n  }\n  push(...chunks) {\n    if (this._closed) {\n      throw new Error('cannot call push after close');\n    }\n    this._chunksQueue.push(...chunks);\n    if (this._notify) {\n      this._notify();\n    }\n  }\n  close() {\n    if (this._closed) {\n      throw new Error('close was already called');\n    }\n    this._closed = true;\n    this._chunksQueue.push(EOQ);\n    if (this._notify) {\n      this._notify();\n    }\n  }\n  async *[Symbol.asyncIterator]() {\n    while (true) {\n      let chunk;\n      while (!(chunk = this._chunksQueue.shift())) {\n        await new Promise(resolve => this._notify = resolve);\n        this._notify = undefined;\n      }\n      if (chunk === EOQ) {\n        break;\n      }\n      yield* this._search.feed(chunk);\n    }\n    const tail = this._search.end();\n    if (tail.length) {\n      yield tail;\n    }\n  }\n}\nfunction splitChunks(chunks, needle) {\n  const search = new StreamSearch(needle);\n  const outchunks = [[]];\n  for (const chunk of chunks) {\n    for (const token of search.feed(chunk)) {\n      if (token === MATCH) {\n        outchunks.push([]);\n      } else {\n        outchunks[outchunks.length - 1].push(token);\n      }\n    }\n  }\n  const end = search.end();\n  outchunks[outchunks.length - 1].push(end);\n  return outchunks.map(chunks => mergeArrays(...chunks));\n}\nfunction split(buf, needle) {\n  return splitChunks([buf], needle);\n}\nasync function* chunksIterator(iter) {\n  let chunks = [];\n  for await (const value of iter) {\n    if (value === MATCH) {\n      yield chunks;\n      chunks = [];\n    } else {\n      chunks.push(value);\n    }\n  }\n  yield chunks;\n}\nasync function* stringIterator(iter) {\n  for await (const chunk of chunksIterator(iter)) {\n    yield chunk.map(arrayToString).join('');\n  }\n}\nasync function allStrings(iter) {\n  const segments = [];\n  for await (const value of stringIterator(iter)) {\n    segments.push(value);\n  }\n  return segments;\n}\nasync function* arrayIterator(iter) {\n  for await (const chunk of chunksIterator(iter)) {\n    yield mergeArrays(...chunk);\n  }\n}\nexport {\n  MATCH,\n  QueueableStreamSearch,\n  ReadableStreamSearch,\n  StreamSearch,\n  allStrings,\n  arrayIterator,\n  chunksIterator,\n  split,\n  splitChunks,\n  stringIterator\n};", "import {\n  ReadableStreamSearch,\n  StreamSearch,\n  MATCH\n} from './search.js';\nimport {\n  arraysEqual,\n  stringToArray,\n  arrayToString,\n  mergeArrays\n} from './utils.js';\nconst mergeArrays2 = Function.prototype.apply.bind(mergeArrays, undefined);\nconst dash = stringToArray('--');\nconst CRLF = stringToArray('\\r\\n');\nfunction parseContentDisposition(header) {\n  const parts = header.split(';').map(part => part.trim());\n  if (parts.shift() !== 'form-data') {\n    throw new Error('malformed content-disposition header: missing \"form-data\" in `' + JSON.stringify(parts) + '`');\n  }\n  const out = {};\n  for (const part of parts) {\n    const kv = part.split('=', 2);\n    if (kv.length !== 2) {\n      throw new Error('malformed content-disposition header: key-value pair not found - ' + part + ' in `' + header + '`');\n    }\n    const [name, value] = kv;\n    if (value[0] === '\"' && value[value.length - 1] === '\"') {\n      out[name] = value.slice(1, -1).replace(/\\\\\"/g, '\"');\n    } else if (value[0] !== '\"' && value[value.length - 1] !== '\"') {\n      out[name] = value;\n    } else if (value[0] === '\"' && value[value.length - 1] !== '\"' || value[0] !== '\"' && value[value.length - 1] === '\"') {\n      throw new Error('malformed content-disposition header: mismatched quotations in `' + header + '`');\n    }\n  }\n  if (!out.name) {\n    throw new Error('malformed content-disposition header: missing field name in `' + header + '`');\n  }\n  return out;\n}\nfunction parsePartHeaders(lines) {\n  const entries = [];\n  let disposition = false;\n  let line;\n  while (typeof (line = lines.shift()) !== 'undefined') {\n    const colon = line.indexOf(':');\n    if (colon === -1) {\n      throw new Error('malformed multipart-form header: missing colon');\n    }\n    const header = line.slice(0, colon).trim().toLowerCase();\n    const value = line.slice(colon + 1).trim();\n    switch (header) {\n    case 'content-disposition':\n      disposition = true;\n      entries.push(...Object.entries(parseContentDisposition(value)));\n      break;\n    case 'content-type':\n      entries.push([\n        'contentType',\n        value\n      ]);\n    }\n  }\n  if (!disposition) {\n    throw new Error('malformed multipart-form header: missing content-disposition');\n  }\n  return Object.fromEntries(entries);\n}\nasync function readHeaderLines(it, needle) {\n  let firstChunk = true;\n  let lastTokenWasMatch = false;\n  const headerLines = [[]];\n  const crlfSearch = new StreamSearch(CRLF);\n  for (;;) {\n    const result = await it.next();\n    if (result.done) {\n      throw new Error('malformed multipart-form data: unexpected end of stream');\n    }\n    if (firstChunk && result.value !== MATCH && arraysEqual(result.value.slice(0, 2), dash)) {\n      return [\n        undefined,\n        new Uint8Array()\n      ];\n    }\n    let chunk;\n    if (result.value !== MATCH) {\n      chunk = result.value;\n    } else if (!lastTokenWasMatch) {\n      chunk = needle;\n    } else {\n      throw new Error('malformed multipart-form data: unexpected boundary');\n    }\n    if (!chunk.length) {\n      continue;\n    }\n    if (firstChunk) {\n      firstChunk = false;\n    }\n    const tokens = crlfSearch.feed(chunk);\n    for (const [i, token] of tokens.entries()) {\n      const isMatch = token === MATCH;\n      if (!isMatch && !token.length) {\n        continue;\n      }\n      if (lastTokenWasMatch && isMatch) {\n        tokens.push(crlfSearch.end());\n        return [\n          headerLines.filter(chunks => chunks.length).map(mergeArrays2).map(arrayToString),\n          mergeArrays(...tokens.slice(i + 1).map(token => token === MATCH ? CRLF : token))\n        ];\n      }\n      if (lastTokenWasMatch = isMatch) {\n        headerLines.push([]);\n      } else {\n        headerLines[headerLines.length - 1].push(token);\n      }\n    }\n  }\n}\nexport async function* streamMultipart(body, boundary) {\n  const needle = mergeArrays(dash, stringToArray(boundary));\n  const it = new ReadableStreamSearch(needle, body)[Symbol.asyncIterator]();\n  for (;;) {\n    const result = await it.next();\n    if (result.done) {\n      return;\n    }\n    if (result.value === MATCH) {\n      break;\n    }\n  }\n  const crlfSearch = new StreamSearch(CRLF);\n  for (;;) {\n    const [headerLines, tail] = await readHeaderLines(it, needle);\n    if (!headerLines) {\n      return;\n    }\n    async function nextToken() {\n      const result = await it.next();\n      if (result.done) {\n        throw new Error('malformed multipart-form data: unexpected end of stream');\n      }\n      return result;\n    }\n    let trailingCRLF = false;\n    function feedChunk(chunk) {\n      const chunks = [];\n      for (const token of crlfSearch.feed(chunk)) {\n        if (trailingCRLF) {\n          chunks.push(CRLF);\n        }\n        if (!(trailingCRLF = token === MATCH)) {\n          chunks.push(token);\n        }\n      }\n      return mergeArrays(...chunks);\n    }\n    let done = false;\n    async function nextChunk() {\n      const result = await nextToken();\n      let chunk;\n      if (result.value !== MATCH) {\n        chunk = result.value;\n      } else if (!trailingCRLF) {\n        chunk = CRLF;\n      } else {\n        done = true;\n        return { value: crlfSearch.end() };\n      }\n      return { value: feedChunk(chunk) };\n    }\n    const bufferedChunks = [{ value: feedChunk(tail) }];\n    yield {\n      ...parsePartHeaders(headerLines),\n      data: {\n        [Symbol.asyncIterator]() {\n          return this;\n        },\n        async next() {\n          for (;;) {\n            const result = bufferedChunks.shift();\n            if (!result) {\n              break;\n            }\n            if (result.value.length > 0) {\n              return result;\n            }\n          }\n          for (;;) {\n            if (done) {\n              return {\n                done,\n                value: undefined\n              };\n            }\n            const result = await nextChunk();\n            if (result.value.length > 0) {\n              return result;\n            }\n          }\n        }\n      }\n    };\n    while (!done) {\n      bufferedChunks.push(await nextChunk());\n    }\n  }\n}\nexport async function* iterateMultipart(body, boundary) {\n  for await (const part of streamMultipart(body, boundary)) {\n    const chunks = [];\n    for await (const chunk of part.data) {\n      chunks.push(chunk);\n    }\n    yield {\n      ...part,\n      data: mergeArrays(...chunks)\n    };\n  }\n}", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { streamMultipart } from '@web3-storage/multipart-parser';\n\n// @ts-ignore\nfunction composeUploadHandlers(...handlers) {\n  return async part => {\n    for (let handler of handlers) {\n      let value = await handler(part);\n      if (typeof value !== \"undefined\" && value !== null) {\n        return value;\n      }\n    }\n    return undefined;\n  };\n}\n\n/**\n * Allows you to handle multipart forms (file uploads) for your app.\n *\n * TODO: Update this comment\n * @see https://remix.run/utils/parse-multipart-form-data\n */\nasync function parseMultipartFormData(request, uploadHandler) {\n  let contentType = request.headers.get(\"Content-Type\") || \"\";\n  let [type, boundary] = contentType.split(/\\s*;\\s*boundary=/);\n  if (!request.body || !boundary || type !== \"multipart/form-data\") {\n    throw new TypeError(\"Could not parse content as FormData.\");\n  }\n  let formData = new FormData();\n  let parts = streamMultipart(request.body, boundary);\n  for await (let part of parts) {\n    if (part.done) break;\n    if (typeof part.filename === \"string\") {\n      // only pass basename as the multipart/form-data spec recommends\n      // https://datatracker.ietf.org/doc/html/rfc7578#section-4.2\n      part.filename = part.filename.split(/[/\\\\]/).pop();\n    }\n    let value = await uploadHandler(part);\n    if (typeof value !== \"undefined\" && value !== null) {\n      formData.append(part.name, value);\n    }\n  }\n  return formData;\n}\n\nexport { composeUploadHandlers, parseMultipartFormData };\n", "////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  // We keep the raw Response for redirects so we can return it verbatim\n  response: Response;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\ntype DataFunctionReturnValue = Promise<DataFunctionValue> | DataFunctionValue;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (\n    args: LoaderFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (\n    args: ActionFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  actionStatus?: number;\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\nexport interface DataStrategyMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {\n  shouldLoad: boolean;\n  resolve: (\n    handlerOverride?: (\n      handler: (ctx?: unknown) => DataFunctionReturnValue\n    ) => DataFunctionReturnValue\n  ) => Promise<DataStrategyResult>;\n}\n\nexport interface DataStrategyFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {\n  matches: DataStrategyMatch[];\n  fetcherKey: string | null;\n}\n\n/**\n * Result from a loader or action called via dataStrategy\n */\nexport interface DataStrategyResult {\n  type: \"data\" | \"error\";\n  result: unknown; // data, Error, Response, DeferredData, DataWithResponseInit\n}\n\nexport interface DataStrategyFunction {\n  (args: DataStrategyFunctionArgs): Promise<Record<string, DataStrategyResult>>;\n}\n\nexport type AgnosticPatchRoutesOnNavigationFunctionArgs<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = {\n  signal: AbortSignal;\n  path: string;\n  matches: M[];\n  fetcherKey: string | undefined;\n  patch: (routeId: string | null, children: O[]) => void;\n};\n\nexport type AgnosticPatchRoutesOnNavigationFunction<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = (\n  opts: AgnosticPatchRoutesOnNavigationFunctionArgs<O, M>\n) => void | Promise<void>;\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction | boolean;\n  action?: ActionFunction | boolean;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: string[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/v6/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\n\nexport function matchRoutesImpl<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename: string,\n  allowPartial: boolean\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string,\n  allowPartial = false\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    let route = meta.route;\n\n    if (\n      !match &&\n      end &&\n      allowPartial &&\n      !routesMeta[routesMeta.length - 1].route.index\n    ) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false,\n        },\n        remainingPathname\n      );\n    }\n\n    if (!match) {\n      return null;\n    }\n\n    Object.assign(matchedParams, match.params);\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/v6/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/v6/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nexport function decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/v6/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n *\n * @deprecated The `json` method is deprecated in favor of returning raw objects.\n * This method will be removed in v7.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport class DataWithResponseInit<D> {\n  type: string = \"DataWithResponseInit\";\n  data: D;\n  init: ResponseInit | null;\n\n  constructor(data: D, init?: ResponseInit) {\n    this.data = data;\n    this.init = init || null;\n  }\n}\n\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nexport function data<D>(data: D, init?: number | ResponseInit) {\n  return new DataWithResponseInit(\n    data,\n    typeof init === \"number\" ? { status: init } : init\n  );\n}\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\n/**\n * @deprecated The `defer` method is deprecated in favor of returning raw\n * objects. This method will be removed in v7.\n */\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const replace: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DataStrategyMatch,\n  AgnosticRouteObject,\n  DataResult,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  DataStrategyResult,\n  ImmutableRouteKey,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n  AgnosticPatchRoutesOnNavigationFunction,\n  DataWithResponseInit,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  matchRoutesImpl,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE DO NOT USE\n   *\n   * Patch additional children routes into an existing parent route\n   * @param routeId The parent route id or a callback function accepting `patch`\n   *                to perform batch patching\n   * @param children The additional children routes\n   */\n  patchRoutes(routeId: string | null, children: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n  v7_skipActionErrorRevalidation: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: AgnosticPatchRoutesOnNavigationFunction;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: {\n      routeId?: string;\n      requestContext?: unknown;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      viewTransitionOpts?: ViewTransitionOpts;\n      flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ntype PendingActionResult = [string, SuccessResult | ErrorResult];\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * Tuple for the returned or thrown value from the current action.  The routeId\n   * is the action route for success and the bubbled boundary route for errors.\n   */\n  pendingActionResult?: PendingActionResult;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialMatchesIsFOW = false;\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(\n      initialMatches,\n      dataRoutes,\n      init.history.location.pathname\n    );\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n\n  let initialized: boolean;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(\n        null,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatchesIsFOW = true;\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some((m) => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some((m) => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(\n        (m) => errors![m.route.id] !== undefined\n      );\n      initialized = initialMatches\n        .slice(0, idx + 1)\n        .every((m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(\n        (m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors)\n      );\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: Set<string> = new Set();\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Map of pending patchRoutesOnNavigation() promises (keyed by path/matches) so\n  // that we only kick them off once for a given combo\n  let pendingPatchRoutes = new Map<\n    string,\n    ReturnType<AgnosticPatchRoutesOnNavigationFunction>\n  >();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate: (() => void) | undefined = undefined;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (unblockBlockerHistoryUpdate) {\n          unblockBlockerHistoryUpdate();\n          unblockBlockerHistoryUpdate = undefined;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          let nextHistoryUpdatePromise = new Promise<void>((resolve) => {\n            unblockBlockerHistoryUpdate = resolve;\n          });\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked, after the url\n              // restoration is also complete.  See:\n              // https://github.com/remix-run/react-router/issues/11613\n              nextHistoryUpdatePromise.then(() => init.history.go(delta));\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Remove any lingering deleted fetchers that have already been removed\n    // from state.fetchers\n    deletedFetchers.forEach((key) => {\n      if (!state.fetchers.has(key) && !fetchControllers.has(key)) {\n        deletedFetchersKeys.push(key);\n      }\n    });\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        viewTransitionOpts: opts.viewTransitionOpts,\n        flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    } else {\n      // We already called deleteFetcher() on these, can remove them from this\n      // Set now that we've handed the keys off to the data layer\n      deletedFetchersKeys.forEach((key) => deletedFetchers.delete(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      {\n        overrideNavigation: state.navigation,\n        // Proxy through any rending view transition\n        enableViewTransition: pendingViewTransitionEnabled === true,\n      }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches =\n      opts?.initialHydration &&\n      state.matches &&\n      state.matches.length > 0 &&\n      !initialMatchesIsFOW\n        ? // `matchRoutes()` has already been called if we're in here via `router.initialize()`\n          state.matches\n        : matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      matches &&\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionResult: PendingActionResult | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: ResultType.error, error: opts.pendingError },\n      ];\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        fogOfWar.active,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionResult.shortCircuited) {\n        return;\n      }\n\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (\n          isErrorResult(result) &&\n          isRouteErrorResponse(result.error) &&\n          result.error.status === 404\n        ) {\n          pendingNavigationController = null;\n\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error,\n            },\n          });\n          return;\n        }\n      }\n\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n\n      // Create a GET request for the loaders\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors,\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [\n            boundaryId,\n            {\n              type: ResultType.error,\n              error: discoverResult.error,\n            },\n          ],\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        state,\n        request,\n        [actionMatch],\n        matches,\n        null\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\")!,\n          new URL(request.url),\n          basename\n        );\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result],\n      };\n    }\n\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result],\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionResult?: PendingActionResult\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState =\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration);\n\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...(actionData !== undefined ? { actionData } : {}),\n          },\n          {\n            flushSync,\n          }\n        );\n      }\n\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error,\n          },\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionResult\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors:\n            pendingActionResult && isErrorResult(pendingActionResult[1])\n              ? { [pendingActionResult[0]]: pendingActionResult[1].error }\n              : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    if (shouldUpdateNavigationState) {\n      let updates: Partial<RouterState> = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = { ...state.errors, ...errors };\n    }\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getUpdatedActionData(\n    pendingActionResult: PendingActionResult | undefined\n  ): Record<string, RouteData> | null | undefined {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data as any,\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n\n  function getUpdatedRevalidatingFetchers(\n    revalidatingFetchers: RevalidatingFetcher[]\n  ) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        undefined,\n        fetcher ? fetcher.data : undefined\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    abortFetcher(key);\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        fogOfWar.active,\n        flushSync,\n        preventScrollReset,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      fogOfWar.active,\n      flushSync,\n      preventScrollReset,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    function detectAndHandle405Error(m: AgnosticDataRouteMatch) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId,\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        new URL(fetchRequest.url).pathname,\n        fetchRequest.signal,\n        key\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\n      \"action\",\n      state,\n      fetchRequest,\n      [match],\n      requestMatches,\n      key\n    );\n    let actionResult = actionResults[match.route.id];\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      [match.route.id, actionResult]\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        abortFetcher(staleKey);\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        new URL(fetchRequest.url).pathname,\n        fetchRequest.signal,\n        key\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\n      \"loader\",\n      state,\n      fetchRequest,\n      [match],\n      matches,\n      key\n    );\n    let result = results[match.route.id];\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset,\n        });\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    request: Request,\n    redirect: RedirectResult,\n    isNavigation: boolean,\n    {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true,\n    });\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true || redirect.response.headers.has(\"X-Remix-Replace\")\n        ? HistoryAction.Replace\n        : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.response.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location,\n        },\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    }\n  }\n\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    state: RouterState,\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    fetcherKey: string | null\n  ): Promise<Record<string, DataResult>> {\n    let results: Record<string, DataStrategyResult>;\n    let dataResults: Record<string, DataResult> = {};\n    try {\n      results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        type,\n        state,\n        request,\n        matchesToLoad,\n        matches,\n        fetcherKey,\n        manifest,\n        mapRouteProperties\n      );\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach((m) => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e,\n        };\n      });\n      return dataResults;\n    }\n\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result as Response;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            routeId,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          ),\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(\n          result\n        );\n      }\n    }\n\n    return dataResults;\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    state: RouterState,\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    let currentMatches = state.matches;\n\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\n      \"loader\",\n      state,\n      request,\n      matchesToLoad,\n      matches,\n      null\n    );\n\n    let fetcherResultsPromise = Promise.all(\n      fetchersToLoad.map(async (f) => {\n        if (f.matches && f.match && f.controller) {\n          let results = await callDataStrategy(\n            \"loader\",\n            state,\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            [f.match],\n            f.matches,\n            f.key\n          );\n          let result = results[f.match.route.id];\n          // Fetcher results are keyed by fetcher key from here on out, not routeId\n          return { [f.key]: result };\n        } else {\n          return Promise.resolve({\n            [f.key]: {\n              type: ResultType.error,\n              error: getInternalRouterError(404, {\n                pathname: f.path,\n              }),\n            } as ErrorResult,\n          });\n        }\n      })\n    );\n\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce(\n      (acc, r) => Object.assign(acc, r),\n      {}\n    );\n\n    await Promise.all([\n      resolveNavigationDeferredResults(\n        matches,\n        loaderResults,\n        request.signal,\n        currentMatches,\n        state.loaderData\n      ),\n      resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad),\n    ]);\n\n    return {\n      loaderResults,\n      fetcherResults,\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n    // If this fetcher was previously marked for deletion, unmark it since we\n    // have a new instance\n    if (deletedFetchers.has(key)) {\n      deletedFetchers.delete(key);\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n\n    // If we opted into the flag we can clear this now since we're calling\n    // deleteFetcher() at the end of updateState() and we've already handed the\n    // deleted fetcher keys off to the data layer.\n    // If not, we're eagerly calling deleteFetcher() and we need to keep this\n    // Set populated until the next updateState call, and we'll clear\n    // `deletedFetchers` then\n    if (future.v7_fetcherPersist) {\n      deletedFetchers.delete(key);\n    }\n\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    let count = (activeFetchers.get(key) || 0) - 1;\n    if (count <= 0) {\n      activeFetchers.delete(key);\n      deletedFetchers.add(key);\n      if (!future.v7_fetcherPersist) {\n        deleteFetcher(key);\n      }\n    } else {\n      activeFetchers.set(key, count);\n    }\n\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function handleNavigational404(pathname: string) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function checkFogOfWar(\n    matches: AgnosticDataRouteMatch[] | null,\n    routesToUse: AgnosticDataRouteObject[],\n    pathname: string\n  ): { active: boolean; matches: AgnosticDataRouteMatch[] | null } {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n\n    return { active: false, matches: null };\n  }\n\n  type DiscoverRoutesSuccessResult = {\n    type: \"success\";\n    matches: AgnosticDataRouteMatch[] | null;\n  };\n  type DiscoverRoutesErrorResult = {\n    type: \"error\";\n    error: any;\n    partialMatches: AgnosticDataRouteMatch[];\n  };\n  type DiscoverRoutesAbortedResult = { type: \"aborted\" };\n  type DiscoverRoutesResult =\n    | DiscoverRoutesSuccessResult\n    | DiscoverRoutesErrorResult\n    | DiscoverRoutesAbortedResult;\n\n  async function discoverRoutes(\n    matches: AgnosticDataRouteMatch[],\n    pathname: string,\n    signal: AbortSignal,\n    fetcherKey?: string\n  ): Promise<DiscoverRoutesResult> {\n    if (!patchRoutesOnNavigationImpl) {\n      return { type: \"success\", matches };\n    }\n\n    let partialMatches: AgnosticDataRouteMatch[] | null = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          signal,\n          path: pathname,\n          matches: partialMatches,\n          fetcherKey,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(\n              routeId,\n              children,\n              routesToUse,\n              localManifest,\n              mapRouteProperties\n            );\n          },\n        });\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return { type: \"success\", matches: newMatches };\n      }\n\n      let newPartialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n\n      // Avoid loops if the second pass results in the same partial matches\n      if (\n        !newPartialMatches ||\n        (partialMatches.length === newPartialMatches.length &&\n          partialMatches.every(\n            (m, i) => m.route.id === newPartialMatches![i].route.id\n          ))\n      ) {\n        return { type: \"success\", matches: null };\n      }\n\n      partialMatches = newPartialMatches;\n    }\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  function patchRoutes(\n    routeId: string | null,\n    children: AgnosticRouteObject[]\n  ): void {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties\n    );\n\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(\n    request: Request,\n    {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      routeId?: string;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      false,\n      match\n    );\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches,\n        isRouteRequest,\n        requestContext,\n        dataStrategy\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")!,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling\n        ? actionMatch\n        : findNearestBoundary(matches, actionMatch.route.id);\n\n      let context = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        [boundaryMatch.route.id, result]\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : result.statusCode != null\n          ? result.statusCode\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      dataStrategy,\n      skipLoaderErrorBubbling,\n      null\n    );\n\n    return {\n      ...context,\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionHeaders: result.headers\n        ? { [actionMatch.route.id]: result.headers }\n        : {},\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null,\n    pendingActionResult?: PendingActionResult\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0])\n      : matches;\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors:\n          pendingActionResult && isErrorResult(pendingActionResult[1])\n            ? {\n                [pendingActionResult[0]]: pendingActionResult[1].error,\n              }\n            : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await callDataStrategy(\n      \"loader\",\n      request,\n      matchesToLoad,\n      matches,\n      isRouteRequest,\n      requestContext,\n      dataStrategy\n    );\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      results,\n      pendingActionResult,\n      activeDeferreds,\n      skipLoaderErrorBubbling\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    isRouteRequest: boolean,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null\n  ): Promise<Record<string, DataResult>> {\n    let results = await callDataStrategyImpl(\n      dataStrategy || defaultDataStrategy,\n      type,\n      null,\n      request,\n      matchesToLoad,\n      matches,\n      null,\n      manifest,\n      mapRouteProperties,\n      requestContext\n    );\n\n    let dataResults: Record<string, DataResult> = {};\n    await Promise.all(\n      matches.map(async (match) => {\n        if (!(match.route.id in results)) {\n          return;\n        }\n        let result = results[match.route.id];\n        if (isRedirectDataStrategyResultResult(result)) {\n          let response = result.result as Response;\n          // Throw redirects and let the server handle them with an HTTP redirect\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            match.route.id,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          // For SSR single-route requests, we want to hand Responses back\n          // directly without unwrapping\n          throw result;\n        }\n\n        dataResults[match.route.id] =\n          await convertDataStrategyResultToDataResult(result);\n      })\n    );\n    return dataResults;\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search\n        ? path.search.replace(/^\\?/, \"?index&\")\n        : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId: string,\n  includeBoundary = false\n) {\n  let index = matches.findIndex((m) => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  initialHydration: boolean,\n  skipActionErrorRevalidation: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: Set<string>,\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionResult?: PendingActionResult\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingActionResult\n    ? isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : pendingActionResult[1].data\n    : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      Object.keys(state.errors)[0],\n      true\n    );\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      pendingActionResult[0]\n    );\n  }\n\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult\n    ? pendingActionResult[1].statusCode\n    : undefined;\n  let shouldSkipRevalidation =\n    skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation\n        ? false\n        : // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n          isRevalidationRequired ||\n          currentUrl.pathname + currentUrl.search ===\n            nextUrl.pathname + nextUrl.search ||\n          // Search params affect all loaders\n          currentUrl.search !== nextUrl.search ||\n          isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      initialHydration ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation\n          ? false\n          : isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction shouldLoadRouteOnHydration(\n  route: AgnosticDataRouteObject,\n  loaderData: RouteData | null | undefined,\n  errors: RouteData | null | undefined\n) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\nfunction patchRoutesImpl(\n  routeId: string | null,\n  children: AgnosticRouteObject[],\n  routesToUse: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction\n) {\n  let childrenToPatch: AgnosticDataRouteObject[];\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(\n    (newRoute) =>\n      !childrenToPatch.some((existingRoute) =>\n        isSameRoute(newRoute, existingRoute)\n      )\n  );\n\n  let newRoutes = convertRoutesToDataRoutes(\n    uniqueChildren,\n    mapRouteProperties,\n    [routeId || \"_\", \"patch\", String(childrenToPatch?.length || \"0\")],\n    manifest\n  );\n\n  childrenToPatch.push(...newRoutes);\n}\n\nfunction isSameRoute(\n  newRoute: AgnosticRouteObject,\n  existingRoute: AgnosticRouteObject\n): boolean {\n  // Most optimal check is by id\n  if (\n    \"id\" in newRoute &&\n    \"id\" in existingRoute &&\n    newRoute.id === existingRoute.id\n  ) {\n    return true;\n  }\n\n  // Second is by pathing differences\n  if (\n    !(\n      newRoute.index === existingRoute.index &&\n      newRoute.path === existingRoute.path &&\n      newRoute.caseSensitive === existingRoute.caseSensitive\n    )\n  ) {\n    return false;\n  }\n\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if (\n    (!newRoute.children || newRoute.children.length === 0) &&\n    (!existingRoute.children || existingRoute.children.length === 0)\n  ) {\n    return true;\n  }\n\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children!.every((aChild, i) =>\n    existingRoute.children?.some((bChild) => isSameRoute(aChild, bChild))\n  );\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy({\n  matches,\n}: DataStrategyFunctionArgs): ReturnType<DataStrategyFunction> {\n  let matchesToLoad = matches.filter((m) => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map((m) => m.resolve()));\n  return results.reduce(\n    (acc, result, i) =>\n      Object.assign(acc, { [matchesToLoad[i].route.id]: result }),\n    {}\n  );\n}\n\nasync function callDataStrategyImpl(\n  dataStrategyImpl: DataStrategyFunction,\n  type: \"loader\" | \"action\",\n  state: RouterState | null,\n  request: Request,\n  matchesToLoad: AgnosticDataRouteMatch[],\n  matches: AgnosticDataRouteMatch[],\n  fetcherKey: string | null,\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  requestContext?: unknown\n): Promise<Record<string, DataStrategyResult>> {\n  let loadRouteDefinitionsPromises = matches.map((m) =>\n    m.route.lazy\n      ? loadLazyRouteModule(m.route, mapRouteProperties, manifest)\n      : undefined\n  );\n\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some((m) => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve: DataStrategyMatch[\"resolve\"] = async (handlerOverride) => {\n      if (\n        handlerOverride &&\n        request.method === \"GET\" &&\n        (match.route.lazy || match.route.loader)\n      ) {\n        shouldLoad = true;\n      }\n      return shouldLoad\n        ? callLoaderOrAction(\n            type,\n            request,\n            match,\n            loadRoutePromise,\n            handlerOverride,\n            requestContext\n          )\n        : Promise.resolve({ type: ResultType.data, result: undefined });\n    };\n\n    return {\n      ...match,\n      shouldLoad,\n      resolve,\n    };\n  });\n\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext,\n  });\n\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n\n  return results;\n}\n\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  loadRoutePromise: Promise<void> | undefined,\n  handlerOverride: Parameters<DataStrategyMatch[\"resolve\"]>[0],\n  staticContext?: unknown\n): Promise<DataStrategyResult> {\n  let result: DataStrategyResult;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (\n    handler: AgnosticRouteObject[\"loader\"] | AgnosticRouteObject[\"action\"]\n  ): Promise<DataStrategyResult> => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise<DataStrategyResult>((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n\n    let actualHandler = (ctx?: unknown) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean ` +\n              `\"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: staticContext,\n        },\n        ...(ctx !== undefined ? [ctx] : [])\n      );\n    };\n\n    let handlerPromise: Promise<DataStrategyResult> = (async () => {\n      try {\n        let val = await (handlerOverride\n          ? handlerOverride((ctx: unknown) => actualHandler(ctx))\n          : actualHandler());\n        return { type: \"data\", result: val };\n      } catch (e) {\n        return { type: \"error\", result: e };\n      }\n    })();\n\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadRoutePromise,\n        ]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value!;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, result: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result.result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return { type: ResultType.error, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  return result;\n}\n\nasync function convertDataStrategyResultToDataResult(\n  dataStrategyResult: DataStrategyResult\n): Promise<DataResult> {\n  let { result, type } = dataStrategyResult;\n\n  if (isResponse(result)) {\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      if (result.data instanceof Error) {\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: result.init?.status,\n          headers: result.init?.headers\n            ? new Headers(result.init.headers)\n            : undefined,\n        };\n      }\n\n      // Convert thrown data() to ErrorResponse instances\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(\n          result.init?.status || 500,\n          undefined,\n          result.data\n        ),\n        statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n        headers: result.init?.headers\n          ? new Headers(result.init.headers)\n          : undefined,\n      };\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n    };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  if (isDataWithResponseInit(result)) {\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: result.init?.status,\n      headers: result.init?.headers\n        ? new Headers(result.init.headers)\n        : undefined,\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(\n  response: Response,\n  request: Request,\n  routeId: string,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  v7_relativeSplatPath: boolean\n) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      true,\n      location,\n      v7_relativeSplatPath\n    );\n    response.headers.set(\"Location\", location);\n  }\n\n  return response;\n}\n\nfunction normalizeRedirectLocation(\n  location: string,\n  currentUrl: URL,\n  basename: string\n): string {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\")\n      ? new URL(currentUrl.protocol + normalizedLocation)\n      : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  activeDeferreds: Map<string, DeferredData>,\n  skipLoaderErrorBubbling: boolean\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n  let pendingError =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : undefined;\n\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach((match) => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (\n          result.statusCode != null &&\n          result.statusCode !== 200 &&\n          !foundError\n        ) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: Record<string, DataResult>,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    results,\n    pendingActionResult,\n    activeDeferreds,\n    false // This method is only called client side so we always want to bubble\n  );\n\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach((rf) => {\n    let { key, match, controller } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\nfunction getActionDataForCommit(\n  pendingActionResult: PendingActionResult | undefined\n) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1])\n    ? {\n        // Clear out prior actionData on errors\n        actionData: {},\n      }\n    : {\n        actionData: {\n          [pendingActionResult[0]]: pendingActionResult[1].data,\n        },\n      };\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n    message,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\";\n    message?: string;\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: Record<string, DataResult>\n): { key: string; result: RedirectResult } | undefined {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return { key, result };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isPromise<T = unknown>(val: unknown): val is Promise<T> {\n  return typeof val === \"object\" && val != null && \"then\" in val;\n}\n\nfunction isDataStrategyResult(result: unknown): result is DataStrategyResult {\n  return (\n    result != null &&\n    typeof result === \"object\" &&\n    \"type\" in result &&\n    \"result\" in result &&\n    (result.type === ResultType.data || result.type === ResultType.error)\n  );\n}\n\nfunction isRedirectDataStrategyResultResult(result: DataStrategyResult) {\n  return (\n    isResponse(result.result) && redirectStatusCodes.has(result.result.status)\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDataWithResponseInit(\n  value: any\n): value is DataWithResponseInit<unknown> {\n  return (\n    typeof value === \"object\" &&\n    value != null &&\n    \"type\" in value &&\n    \"data\" in value &&\n    \"init\" in value &&\n    value.type === \"DataWithResponseInit\"\n  );\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveNavigationDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  signal: AbortSignal,\n  currentMatches: AgnosticDataRouteMatch[],\n  currentLoaderData: RouteData\n) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then((result) => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\n\nasync function resolveFetcherDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  revalidatingFetchers: RevalidatingFetcher[]\n) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, routeId, controller } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(\n        controller,\n        \"Expected an AbortController for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, controller.signal, true).then(\n        (result) => {\n          if (result) {\n            results[key] = result;\n          }\n        }\n      );\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n//#endregion\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n/**\n * The mode to use when running the server.\n */\nlet ServerMode = /*#__PURE__*/function (ServerMode) {\n  ServerMode[\"Development\"] = \"development\";\n  ServerMode[\"Production\"] = \"production\";\n  ServerMode[\"Test\"] = \"test\";\n  return ServerMode;\n}({});\nfunction isServerMode(value) {\n  return value === ServerMode.Development || value === ServerMode.Production || value === ServerMode.Test;\n}\n\nexport { ServerMode, isServerMode };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { isRouteErrorResponse } from '@remix-run/router';\nimport { ServerMode } from './mode.js';\n\n/**\n * This thing probably warrants some explanation.\n *\n * The whole point here is to emulate componentDidCatch for server rendering and\n * data loading. It can get tricky. React can do this on component boundaries\n * but doesn't support it for server rendering or data loading. We know enough\n * with nested routes to be able to emulate the behavior (because we know them\n * statically before rendering.)\n *\n * Each route can export an `ErrorBoundary`.\n *\n * - When rendering throws an error, the nearest error boundary will render\n *   (normal react componentDidCatch). This will be the route's own boundary, but\n *   if none is provided, it will bubble up to the parents.\n * - When data loading throws an error, the nearest error boundary will render\n * - When performing an action, the nearest error boundary for the action's\n *   route tree will render (no redirect happens)\n *\n * During normal react rendering, we do nothing special, just normal\n * componentDidCatch.\n *\n * For server rendering, we mutate `renderBoundaryRouteId` to know the last\n * layout that has an error boundary that tried to render. This emulates which\n * layout would catch a thrown error. If the rendering fails, we catch the error\n * on the server, and go again a second time with the emulator holding on to the\n * information it needs to render the same error boundary as a dynamically\n * thrown render error.\n *\n * When data loading, server or client side, we use the emulator to likewise\n * hang on to the error and re-render at the appropriate layout (where a thrown\n * error would have been caught by cDC).\n *\n * When actions throw, it all works the same. There's an edge case to be aware\n * of though. Actions normally are required to redirect, but in the case of\n * errors, we render the action's route with the emulator holding on to the\n * error. If during this render a parent route/loader throws we ignore that new\n * error and render the action's original error as deeply as possible. In other\n * words, we simply ignore the new error and use the action's error in place\n * because it came first, and that just wouldn't be fair to let errors cut in\n * line.\n */\n\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== ServerMode.Development) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = undefined;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\n\n// must be type alias due to inference issues on interfaces\n// https://github.com/microsoft/TypeScript/issues/15300\n\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // deserializeErrors in remix-react/errors.ts :)\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...(sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport { sanitizeError, sanitizeErrors, serializeError, serializeErrors };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { json as json$1, defer as defer$1, redirect as redirect$1, replace as replace$1, redirectDocument as redirectDocument$1 } from '@remix-run/router';\nimport { serializeError } from './errors.js';\n\n// must be a type since this is a subtype of response\n// interfaces must conform to the types they extend\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n *\n * @deprecated This utility is deprecated in favor of opting into Single Fetch\n * via `future.v3_singleFetch` and returning raw objects.  This method will be\n * removed in React Router v7.\n *\n * If you need to return custom headers or status code, you can use the new `data`\n * utility (https://remix.run/docs/en/main/utils/data).\n *\n * If you need to return a JSON Response from a resource route, you can use\n * `Response.json` (https://developer.mozilla.org/en-US/docs/Web/API/Response/json_static).\n *\n * @see https://remix.run/utils/json\n */\nconst json = (data, init = {}) => {\n  return json$1(data, init);\n};\n\n/**\n * This is a shortcut for creating Remix deferred responses\n *\n * @deprecated This utility is deprecated in favor of opting into Single Fetch\n * via `future.v3_singleFetch` and returning raw objects.  This method will be\n * removed in React Router v7.\n *\n * @see https://remix.run/utils/defer\n */\nconst defer = (data, init = {}) => {\n  return defer$1(data, init);\n};\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n *\n * @see https://remix.run/utils/redirect\n */\nconst redirect = (url, init = 302) => {\n  return redirect$1(url, init);\n};\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n *\n * @see https://remix.run/utils/redirect\n */\nconst replace = (url, init = 302) => {\n  return replace$1(url, init);\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n *\n * @see https://remix.run/utils/redirect\n */\nconst redirectDocument = (url, init = 302) => {\n  return redirectDocument$1(url, init);\n};\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nfunction isRedirectStatusCode(statusCode) {\n  return redirectStatusCodes.has(statusCode);\n}\nfunction isRedirectResponse(response) {\n  return isRedirectStatusCode(response.status);\n}\nfunction isTrackedPromise(value) {\n  return value != null && typeof value.then === \"function\" && value._tracked === true;\n}\n\n// TODO: Figure out why ReadableStream types are borked sooooooo badly\n// in this file. Probably related to our TS configurations and configs\n// bleeding into each other.\nconst DEFERRED_VALUE_PLACEHOLDER_PREFIX = \"__deferred_promise:\";\nfunction createDeferredReadableStream(deferredData, signal, serverMode) {\n  let encoder = new TextEncoder();\n  let stream = new ReadableStream({\n    async start(controller) {\n      let criticalData = {};\n      let preresolvedKeys = [];\n      for (let [key, value] of Object.entries(deferredData.data)) {\n        if (isTrackedPromise(value)) {\n          criticalData[key] = `${DEFERRED_VALUE_PLACEHOLDER_PREFIX}${key}`;\n          if (typeof value._data !== \"undefined\" || typeof value._error !== \"undefined\") {\n            preresolvedKeys.push(key);\n          }\n        } else {\n          criticalData[key] = value;\n        }\n      }\n\n      // Send the critical data\n      controller.enqueue(encoder.encode(JSON.stringify(criticalData) + \"\\n\\n\"));\n      for (let preresolvedKey of preresolvedKeys) {\n        enqueueTrackedPromise(controller, encoder, preresolvedKey, deferredData.data[preresolvedKey], serverMode);\n      }\n      let unsubscribe = deferredData.subscribe((aborted, settledKey) => {\n        if (settledKey) {\n          enqueueTrackedPromise(controller, encoder, settledKey, deferredData.data[settledKey], serverMode);\n        }\n      });\n      await deferredData.resolveData(signal);\n      unsubscribe();\n      controller.close();\n    }\n  });\n  return stream;\n}\nfunction enqueueTrackedPromise(controller, encoder, settledKey, promise, serverMode) {\n  if (\"_error\" in promise) {\n    controller.enqueue(encoder.encode(\"error:\" + JSON.stringify({\n      [settledKey]: promise._error instanceof Error ? serializeError(promise._error, serverMode) : promise._error\n    }) + \"\\n\\n\"));\n  } else {\n    controller.enqueue(encoder.encode(\"data:\" + JSON.stringify({\n      [settledKey]: promise._data ?? null\n    }) + \"\\n\\n\"));\n  }\n}\n\nexport { createDeferredReadableStream, defer, isDeferredData, isRedirectResponse, isRedirectStatusCode, isResponse, json, redirect, redirectDocument, replace };\n", "// src/utils.ts\nvar HOLE = -1;\nvar NAN = -2;\nvar NEGATIVE_INFINITY = -3;\nvar NEGATIVE_ZERO = -4;\nvar NULL = -5;\nvar POSITIVE_INFINITY = -6;\nvar UNDEFINED = -7;\nvar TYPE_BIGINT = \"B\";\nvar TYPE_DATE = \"D\";\nvar TYPE_ERROR = \"E\";\nvar TYPE_MAP = \"M\";\nvar TYPE_NULL_OBJECT = \"N\";\nvar TYPE_PROMISE = \"P\";\nvar TYPE_REGEXP = \"R\";\nvar TYPE_SET = \"S\";\nvar TYPE_SYMBOL = \"Y\";\nvar TYPE_URL = \"U\";\nvar TYPE_PREVIOUS_RESOLVED = \"Z\";\nvar Deferred = class {\n  promise;\n  resolve;\n  reject;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n};\nfunction createLineSplittingTransform() {\n  const decoder = new TextDecoder();\n  let leftover = \"\";\n  return new TransformStream({\n    transform(chunk, controller) {\n      const str = decoder.decode(chunk, { stream: true });\n      const parts = (leftover + str).split(\"\\n\");\n      leftover = parts.pop() || \"\";\n      for (const part of parts) {\n        controller.enqueue(part);\n      }\n    },\n    flush(controller) {\n      if (leftover) {\n        controller.enqueue(leftover);\n      }\n    }\n  });\n}\n\n// src/flatten.ts\nfunction flatten(input) {\n  const { indices } = this;\n  const existing = indices.get(input);\n  if (existing)\n    return [existing];\n  if (input === void 0)\n    return UNDEFINED;\n  if (input === null)\n    return NULL;\n  if (Number.isNaN(input))\n    return NAN;\n  if (input === Number.POSITIVE_INFINITY)\n    return POSITIVE_INFINITY;\n  if (input === Number.NEGATIVE_INFINITY)\n    return NEGATIVE_INFINITY;\n  if (input === 0 && 1 / input < 0)\n    return NEGATIVE_ZERO;\n  const index = this.index++;\n  indices.set(input, index);\n  stringify.call(this, input, index);\n  return index;\n}\nfunction stringify(input, index) {\n  const { deferred, plugins, postPlugins } = this;\n  const str = this.stringified;\n  const stack = [[input, index]];\n  while (stack.length > 0) {\n    const [input2, index2] = stack.pop();\n    const partsForObj = (obj) => Object.keys(obj).map((k) => `\"_${flatten.call(this, k)}\":${flatten.call(this, obj[k])}`).join(\",\");\n    let error = null;\n    switch (typeof input2) {\n      case \"boolean\":\n      case \"number\":\n      case \"string\":\n        str[index2] = JSON.stringify(input2);\n        break;\n      case \"bigint\":\n        str[index2] = `[\"${TYPE_BIGINT}\",\"${input2}\"]`;\n        break;\n      case \"symbol\": {\n        const keyFor = Symbol.keyFor(input2);\n        if (!keyFor) {\n          error = new Error(\n            \"Cannot encode symbol unless created with Symbol.for()\"\n          );\n        } else {\n          str[index2] = `[\"${TYPE_SYMBOL}\",${JSON.stringify(keyFor)}]`;\n        }\n        break;\n      }\n      case \"object\": {\n        if (!input2) {\n          str[index2] = `${NULL}`;\n          break;\n        }\n        const isArray = Array.isArray(input2);\n        let pluginHandled = false;\n        if (!isArray && plugins) {\n          for (const plugin of plugins) {\n            const pluginResult = plugin(input2);\n            if (Array.isArray(pluginResult)) {\n              pluginHandled = true;\n              const [pluginIdentifier, ...rest] = pluginResult;\n              str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n              if (rest.length > 0) {\n                str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n              }\n              str[index2] += \"]\";\n              break;\n            }\n          }\n        }\n        if (!pluginHandled) {\n          let result = isArray ? \"[\" : \"{\";\n          if (isArray) {\n            for (let i = 0; i < input2.length; i++)\n              result += (i ? \",\" : \"\") + (i in input2 ? flatten.call(this, input2[i]) : HOLE);\n            str[index2] = `${result}]`;\n          } else if (input2 instanceof Date) {\n            str[index2] = `[\"${TYPE_DATE}\",${input2.getTime()}]`;\n          } else if (input2 instanceof URL) {\n            str[index2] = `[\"${TYPE_URL}\",${JSON.stringify(input2.href)}]`;\n          } else if (input2 instanceof RegExp) {\n            str[index2] = `[\"${TYPE_REGEXP}\",${JSON.stringify(\n              input2.source\n            )},${JSON.stringify(input2.flags)}]`;\n          } else if (input2 instanceof Set) {\n            if (input2.size > 0) {\n              str[index2] = `[\"${TYPE_SET}\",${[...input2].map((val) => flatten.call(this, val)).join(\",\")}]`;\n            } else {\n              str[index2] = `[\"${TYPE_SET}\"]`;\n            }\n          } else if (input2 instanceof Map) {\n            if (input2.size > 0) {\n              str[index2] = `[\"${TYPE_MAP}\",${[...input2].flatMap(([k, v]) => [\n                flatten.call(this, k),\n                flatten.call(this, v)\n              ]).join(\",\")}]`;\n            } else {\n              str[index2] = `[\"${TYPE_MAP}\"]`;\n            }\n          } else if (input2 instanceof Promise) {\n            str[index2] = `[\"${TYPE_PROMISE}\",${index2}]`;\n            deferred[index2] = input2;\n          } else if (input2 instanceof Error) {\n            str[index2] = `[\"${TYPE_ERROR}\",${JSON.stringify(input2.message)}`;\n            if (input2.name !== \"Error\") {\n              str[index2] += `,${JSON.stringify(input2.name)}`;\n            }\n            str[index2] += \"]\";\n          } else if (Object.getPrototypeOf(input2) === null) {\n            str[index2] = `[\"${TYPE_NULL_OBJECT}\",{${partsForObj(input2)}}]`;\n          } else if (isPlainObject(input2)) {\n            str[index2] = `{${partsForObj(input2)}}`;\n          } else {\n            error = new Error(\"Cannot encode object with prototype\");\n          }\n        }\n        break;\n      }\n      default: {\n        const isArray = Array.isArray(input2);\n        let pluginHandled = false;\n        if (!isArray && plugins) {\n          for (const plugin of plugins) {\n            const pluginResult = plugin(input2);\n            if (Array.isArray(pluginResult)) {\n              pluginHandled = true;\n              const [pluginIdentifier, ...rest] = pluginResult;\n              str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n              if (rest.length > 0) {\n                str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n              }\n              str[index2] += \"]\";\n              break;\n            }\n          }\n        }\n        if (!pluginHandled) {\n          error = new Error(\"Cannot encode function or unexpected type\");\n        }\n      }\n    }\n    if (error) {\n      let pluginHandled = false;\n      if (postPlugins) {\n        for (const plugin of postPlugins) {\n          const pluginResult = plugin(input2);\n          if (Array.isArray(pluginResult)) {\n            pluginHandled = true;\n            const [pluginIdentifier, ...rest] = pluginResult;\n            str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n            if (rest.length > 0) {\n              str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n            }\n            str[index2] += \"]\";\n            break;\n          }\n        }\n      }\n      if (!pluginHandled) {\n        throw error;\n      }\n    }\n  }\n}\nvar objectProtoNames = Object.getOwnPropertyNames(Object.prototype).sort().join(\"\\0\");\nfunction isPlainObject(thing) {\n  const proto = Object.getPrototypeOf(thing);\n  return proto === Object.prototype || proto === null || Object.getOwnPropertyNames(proto).sort().join(\"\\0\") === objectProtoNames;\n}\n\n// src/unflatten.ts\nvar globalObj = typeof window !== \"undefined\" ? window : typeof globalThis !== \"undefined\" ? globalThis : void 0;\nfunction unflatten(parsed) {\n  const { hydrated, values } = this;\n  if (typeof parsed === \"number\")\n    return hydrate.call(this, parsed);\n  if (!Array.isArray(parsed) || !parsed.length)\n    throw new SyntaxError();\n  const startIndex = values.length;\n  for (const value of parsed) {\n    values.push(value);\n  }\n  hydrated.length = values.length;\n  return hydrate.call(this, startIndex);\n}\nfunction hydrate(index) {\n  const { hydrated, values, deferred, plugins } = this;\n  let result;\n  const stack = [\n    [\n      index,\n      (v) => {\n        result = v;\n      }\n    ]\n  ];\n  let postRun = [];\n  while (stack.length > 0) {\n    const [index2, set] = stack.pop();\n    switch (index2) {\n      case UNDEFINED:\n        set(void 0);\n        continue;\n      case NULL:\n        set(null);\n        continue;\n      case NAN:\n        set(NaN);\n        continue;\n      case POSITIVE_INFINITY:\n        set(Infinity);\n        continue;\n      case NEGATIVE_INFINITY:\n        set(-Infinity);\n        continue;\n      case NEGATIVE_ZERO:\n        set(-0);\n        continue;\n    }\n    if (hydrated[index2]) {\n      set(hydrated[index2]);\n      continue;\n    }\n    const value = values[index2];\n    if (!value || typeof value !== \"object\") {\n      hydrated[index2] = value;\n      set(value);\n      continue;\n    }\n    if (Array.isArray(value)) {\n      if (typeof value[0] === \"string\") {\n        const [type, b, c] = value;\n        switch (type) {\n          case TYPE_DATE:\n            set(hydrated[index2] = new Date(b));\n            continue;\n          case TYPE_URL:\n            set(hydrated[index2] = new URL(b));\n            continue;\n          case TYPE_BIGINT:\n            set(hydrated[index2] = BigInt(b));\n            continue;\n          case TYPE_REGEXP:\n            set(hydrated[index2] = new RegExp(b, c));\n            continue;\n          case TYPE_SYMBOL:\n            set(hydrated[index2] = Symbol.for(b));\n            continue;\n          case TYPE_SET:\n            const newSet = /* @__PURE__ */ new Set();\n            hydrated[index2] = newSet;\n            for (let i = 1; i < value.length; i++)\n              stack.push([\n                value[i],\n                (v) => {\n                  newSet.add(v);\n                }\n              ]);\n            set(newSet);\n            continue;\n          case TYPE_MAP:\n            const map = /* @__PURE__ */ new Map();\n            hydrated[index2] = map;\n            for (let i = 1; i < value.length; i += 2) {\n              const r = [];\n              stack.push([\n                value[i + 1],\n                (v) => {\n                  r[1] = v;\n                }\n              ]);\n              stack.push([\n                value[i],\n                (k) => {\n                  r[0] = k;\n                }\n              ]);\n              postRun.push(() => {\n                map.set(r[0], r[1]);\n              });\n            }\n            set(map);\n            continue;\n          case TYPE_NULL_OBJECT:\n            const obj = /* @__PURE__ */ Object.create(null);\n            hydrated[index2] = obj;\n            for (const key of Object.keys(b).reverse()) {\n              const r = [];\n              stack.push([\n                b[key],\n                (v) => {\n                  r[1] = v;\n                }\n              ]);\n              stack.push([\n                Number(key.slice(1)),\n                (k) => {\n                  r[0] = k;\n                }\n              ]);\n              postRun.push(() => {\n                obj[r[0]] = r[1];\n              });\n            }\n            set(obj);\n            continue;\n          case TYPE_PROMISE:\n            if (hydrated[b]) {\n              set(hydrated[index2] = hydrated[b]);\n            } else {\n              const d = new Deferred();\n              deferred[b] = d;\n              set(hydrated[index2] = d.promise);\n            }\n            continue;\n          case TYPE_ERROR:\n            const [, message, errorType] = value;\n            let error = errorType && globalObj && globalObj[errorType] ? new globalObj[errorType](message) : new Error(message);\n            hydrated[index2] = error;\n            set(error);\n            continue;\n          case TYPE_PREVIOUS_RESOLVED:\n            set(hydrated[index2] = hydrated[b]);\n            continue;\n          default:\n            if (Array.isArray(plugins)) {\n              const r = [];\n              const vals = value.slice(1);\n              for (let i = 0; i < vals.length; i++) {\n                const v = vals[i];\n                stack.push([\n                  v,\n                  (v2) => {\n                    r[i] = v2;\n                  }\n                ]);\n              }\n              postRun.push(() => {\n                for (const plugin of plugins) {\n                  const result2 = plugin(value[0], ...r);\n                  if (result2) {\n                    set(hydrated[index2] = result2.value);\n                    return;\n                  }\n                }\n                throw new SyntaxError();\n              });\n              continue;\n            }\n            throw new SyntaxError();\n        }\n      } else {\n        const array = [];\n        hydrated[index2] = array;\n        for (let i = 0; i < value.length; i++) {\n          const n = value[i];\n          if (n !== HOLE) {\n            stack.push([\n              n,\n              (v) => {\n                array[i] = v;\n              }\n            ]);\n          }\n        }\n        set(array);\n        continue;\n      }\n    } else {\n      const object = {};\n      hydrated[index2] = object;\n      for (const key of Object.keys(value).reverse()) {\n        const r = [];\n        stack.push([\n          value[key],\n          (v) => {\n            r[1] = v;\n          }\n        ]);\n        stack.push([\n          Number(key.slice(1)),\n          (k) => {\n            r[0] = k;\n          }\n        ]);\n        postRun.push(() => {\n          object[r[0]] = r[1];\n        });\n      }\n      set(object);\n      continue;\n    }\n  }\n  while (postRun.length > 0) {\n    postRun.pop()();\n  }\n  return result;\n}\n\n// src/turbo-stream.ts\nasync function decode(readable, options) {\n  const { plugins } = options ?? {};\n  const done = new Deferred();\n  const reader = readable.pipeThrough(createLineSplittingTransform()).getReader();\n  const decoder = {\n    values: [],\n    hydrated: [],\n    deferred: {},\n    plugins\n  };\n  const decoded = await decodeInitial.call(decoder, reader);\n  let donePromise = done.promise;\n  if (decoded.done) {\n    done.resolve();\n  } else {\n    donePromise = decodeDeferred.call(decoder, reader).then(done.resolve).catch((reason) => {\n      for (const deferred of Object.values(decoder.deferred)) {\n        deferred.reject(reason);\n      }\n      done.reject(reason);\n    });\n  }\n  return {\n    done: donePromise.then(() => reader.closed),\n    value: decoded.value\n  };\n}\nasync function decodeInitial(reader) {\n  const read = await reader.read();\n  if (!read.value) {\n    throw new SyntaxError();\n  }\n  let line;\n  try {\n    line = JSON.parse(read.value);\n  } catch (reason) {\n    throw new SyntaxError();\n  }\n  return {\n    done: read.done,\n    value: unflatten.call(this, line)\n  };\n}\nasync function decodeDeferred(reader) {\n  let read = await reader.read();\n  while (!read.done) {\n    if (!read.value)\n      continue;\n    const line = read.value;\n    switch (line[0]) {\n      case TYPE_PROMISE: {\n        const colonIndex = line.indexOf(\":\");\n        const deferredId = Number(line.slice(1, colonIndex));\n        const deferred = this.deferred[deferredId];\n        if (!deferred) {\n          throw new Error(`Deferred ID ${deferredId} not found in stream`);\n        }\n        const lineData = line.slice(colonIndex + 1);\n        let jsonLine;\n        try {\n          jsonLine = JSON.parse(lineData);\n        } catch (reason) {\n          throw new SyntaxError();\n        }\n        const value = unflatten.call(this, jsonLine);\n        deferred.resolve(value);\n        break;\n      }\n      case TYPE_ERROR: {\n        const colonIndex = line.indexOf(\":\");\n        const deferredId = Number(line.slice(1, colonIndex));\n        const deferred = this.deferred[deferredId];\n        if (!deferred) {\n          throw new Error(`Deferred ID ${deferredId} not found in stream`);\n        }\n        const lineData = line.slice(colonIndex + 1);\n        let jsonLine;\n        try {\n          jsonLine = JSON.parse(lineData);\n        } catch (reason) {\n          throw new SyntaxError();\n        }\n        const value = unflatten.call(this, jsonLine);\n        deferred.reject(value);\n        break;\n      }\n      default:\n        throw new SyntaxError();\n    }\n    read = await reader.read();\n  }\n}\nfunction encode(input, options) {\n  const { plugins, postPlugins, signal } = options ?? {};\n  const encoder = {\n    deferred: {},\n    index: 0,\n    indices: /* @__PURE__ */ new Map(),\n    stringified: [],\n    plugins,\n    postPlugins,\n    signal\n  };\n  const textEncoder = new TextEncoder();\n  let lastSentIndex = 0;\n  const readable = new ReadableStream({\n    async start(controller) {\n      const id = flatten.call(encoder, input);\n      if (Array.isArray(id)) {\n        throw new Error(\"This should never happen\");\n      }\n      if (id < 0) {\n        controller.enqueue(textEncoder.encode(`${id}\n`));\n      } else {\n        controller.enqueue(\n          textEncoder.encode(`[${encoder.stringified.join(\",\")}]\n`)\n        );\n        lastSentIndex = encoder.stringified.length - 1;\n      }\n      const seenPromises = /* @__PURE__ */ new WeakSet();\n      if (Object.keys(encoder.deferred).length) {\n        let raceDone;\n        const racePromise = new Promise((resolve, reject) => {\n          raceDone = resolve;\n          if (signal) {\n            const rejectPromise = () => reject(signal.reason || new Error(\"Signal was aborted.\"));\n            if (signal.aborted) {\n              rejectPromise();\n            } else {\n              signal.addEventListener(\"abort\", (event) => {\n                rejectPromise();\n              });\n            }\n          }\n        });\n        while (Object.keys(encoder.deferred).length > 0) {\n          for (const [deferredId, deferred] of Object.entries(\n            encoder.deferred\n          )) {\n            if (seenPromises.has(deferred))\n              continue;\n            seenPromises.add(\n              // biome-ignore lint/suspicious/noAssignInExpressions: <explanation>\n              encoder.deferred[Number(deferredId)] = Promise.race([\n                racePromise,\n                deferred\n              ]).then(\n                (resolved) => {\n                  const id2 = flatten.call(encoder, resolved);\n                  if (Array.isArray(id2)) {\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_PROMISE}${deferredId}:[[\"${TYPE_PREVIOUS_RESOLVED}\",${id2[0]}]]\n`\n                      )\n                    );\n                    encoder.index++;\n                    lastSentIndex++;\n                  } else if (id2 < 0) {\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_PROMISE}${deferredId}:${id2}\n`\n                      )\n                    );\n                  } else {\n                    const values = encoder.stringified.slice(lastSentIndex + 1).join(\",\");\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_PROMISE}${deferredId}:[${values}]\n`\n                      )\n                    );\n                    lastSentIndex = encoder.stringified.length - 1;\n                  }\n                },\n                (reason) => {\n                  if (!reason || typeof reason !== \"object\" || !(reason instanceof Error)) {\n                    reason = new Error(\"An unknown error occurred\");\n                  }\n                  const id2 = flatten.call(encoder, reason);\n                  if (Array.isArray(id2)) {\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_ERROR}${deferredId}:[[\"${TYPE_PREVIOUS_RESOLVED}\",${id2[0]}]]\n`\n                      )\n                    );\n                    encoder.index++;\n                    lastSentIndex++;\n                  } else if (id2 < 0) {\n                    controller.enqueue(\n                      textEncoder.encode(`${TYPE_ERROR}${deferredId}:${id2}\n`)\n                    );\n                  } else {\n                    const values = encoder.stringified.slice(lastSentIndex + 1).join(\",\");\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_ERROR}${deferredId}:[${values}]\n`\n                      )\n                    );\n                    lastSentIndex = encoder.stringified.length - 1;\n                  }\n                }\n              ).finally(() => {\n                delete encoder.deferred[Number(deferredId)];\n              })\n            );\n          }\n          await Promise.race(Object.values(encoder.deferred));\n        }\n        raceDone();\n      }\n      await Promise.all(Object.values(encoder.deferred));\n      controller.close();\n    }\n  });\n  return readable;\n}\nexport {\n  decode,\n  encode\n};\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { splitCookiesString } from 'set-cookie-parser';\n\nfunction getDocumentHeaders(build, context) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    // Look for any errorHeaders from the boundary route down, which can be\n    // identified by the presence of headers but no data\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || actionData[id] === undefined)) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && loaderData[id] === undefined) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let routeModule = build.routes[id].module;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n\n    // Only expose errorHeaders to the leaf headers() function to\n    // avoid duplication via parentHeaders\n    let includeErrorHeaders = errorHeaders != undefined && idx === matches.length - 1;\n    // Only prepend cookies from errorHeaders at the leaf renderable route\n    // when it's not the same as loaderHeaders/actionHeaders to avoid\n    // duplicate cookies\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n\n    // Use the parent headers for any route without a `headers` export\n    if (routeModule.headers == null) {\n      let headers = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers);\n      }\n      prependCookies(actionHeaders, headers);\n      prependCookies(loaderHeaders, headers);\n      return headers;\n    }\n    let headers = new Headers(routeModule.headers ? typeof routeModule.headers === \"function\" ? routeModule.headers({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : undefined\n    }) : routeModule.headers : undefined);\n\n    // Automatically preserve Set-Cookie headers from bubbled responses,\n    // loaders, errors, and parent routes\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers());\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    var _childHeaders$getSetC;\n    let cookies = splitCookiesString(parentSetCookieString);\n    // @ts-expect-error This is not available in the polyfill or Node 18 and below\n    let childCookies = new Set((_childHeaders$getSetC = childHeaders.getSetCookie) === null || _childHeaders$getSetC === void 0 ? void 0 : _childHeaders$getSetC.call(childHeaders));\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\nexport { getDocumentHeaders };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { data as data$1, isRouteErrorResponse, stripBasename, UNSAFE_ErrorResponseImpl } from '@remix-run/router';\nimport { encode } from 'turbo-stream';\nimport { sanitizeErrors, sanitizeError } from './errors.js';\nimport { getDocumentHeaders } from './headers.js';\nimport { ServerMode } from './mode.js';\nimport { isResponse, isRedirectStatusCode } from './responses.js';\n\nconst SingleFetchRedirectSymbol = Symbol(\"SingleFetchRedirect\");\n// We can't use a 3xx status or else the `fetch()` would follow the redirect.\n// We need to communicate the redirect back as data so we can act on it in the\n// client side router.  We use a 202 to avoid any automatic caching we might\n// get from a 200 since a \"temporary\" redirect should not be cached.  This lets\n// the user control cache behavior via Cache-Control\nconst SINGLE_FETCH_REDIRECT_STATUS = 202;\nfunction getSingleFetchDataStrategy({\n  isActionDataRequest,\n  loadRouteIds\n} = {}) {\n  return async ({\n    request,\n    matches\n  }) => {\n    // Don't call loaders on action data requests\n    if (isActionDataRequest && request.method === \"GET\") {\n      return {};\n    }\n\n    // Only run opt-in loaders when fine-grained revalidation is enabled\n    let matchesToLoad = loadRouteIds ? matches.filter(m => loadRouteIds.includes(m.route.id)) : matches;\n    let results = await Promise.all(matchesToLoad.map(match => match.resolve()));\n    return results.reduce((acc, result, i) => Object.assign(acc, {\n      [matchesToLoad[i].route.id]: result\n    }), {});\n  };\n}\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : undefined)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      dataStrategy: getSingleFetchDataStrategy({\n        isActionDataRequest: true\n      })\n    });\n\n    // Unlike `handleDataRequest`, when singleFetch is enabled, query does\n    // let non-Response return values through\n    if (isResponse(result)) {\n      return {\n        result: getSingleFetchRedirect(result.status, result.headers, build.basename),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n    let context = result;\n    let headers = getDocumentHeaders(build, context);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return {\n        result: getSingleFetchRedirect(context.statusCode, headers, build.basename),\n        headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n\n    // Sanitize errors outside of development environments\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = {\n        error: Object.values(context.errors)[0]\n      };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    };\n  } catch (error) {\n    handleError(error);\n    // These should only be internal remix errors, no need to deal with responseStubs\n    return {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    };\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    var _URL$searchParams$get;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let loadRouteIds = ((_URL$searchParams$get = new URL(request.url).searchParams.get(\"_routes\")) === null || _URL$searchParams$get === void 0 ? void 0 : _URL$searchParams$get.split(\",\")) || undefined;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      dataStrategy: getSingleFetchDataStrategy({\n        loadRouteIds\n      })\n    });\n    if (isResponse(result)) {\n      return {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(result.status, result.headers, build.basename)\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n    let context = result;\n    let headers = getDocumentHeaders(build, context);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(context.statusCode, headers, build.basename)\n        },\n        headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n\n    // Sanitize errors outside of development environments\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n\n    // Aggregate results based on the matches we intended to load since we get\n    // `null` values back in `context.loaderData` for routes we didn't load\n    let results = {};\n    let loadedMatches = loadRouteIds ? context.matches.filter(m => m.route.loader && loadRouteIds.includes(m.route.id)) : context.matches;\n    loadedMatches.forEach(m => {\n      var _context$loaderData, _context$errors;\n      let data = (_context$loaderData = context.loaderData) === null || _context$loaderData === void 0 ? void 0 : _context$loaderData[m.route.id];\n      let error = (_context$errors = context.errors) === null || _context$errors === void 0 ? void 0 : _context$errors[m.route.id];\n      if (error !== undefined) {\n        results[m.route.id] = {\n          error\n        };\n      } else if (data !== undefined) {\n        results[m.route.id] = {\n          data\n        };\n      }\n    });\n    return {\n      result: results,\n      headers,\n      status: context.statusCode\n    };\n  } catch (error) {\n    handleError(error);\n    // These should only be internal remix errors, no need to deal with responseStubs\n    return {\n      result: {\n        root: {\n          error\n        }\n      },\n      headers: new Headers(),\n      status: 500\n    };\n  }\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect = headers.get(\"Location\");\n  if (basename) {\n    redirect = stripBasename(redirect, basename) || redirect;\n  }\n  return {\n    redirect,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\n\n// Note: If you change this function please change the corresponding\n// decodeViaTurboStream function in server-runtime\nfunction encodeViaTurboStream(data, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  // How long are we willing to wait for all of the promises in `data` to resolve\n  // before timing out?  We default this to 50ms shorter than the default value for\n  // `ABORT_DELAY` in our built-in `entry.server.tsx` so that once we reject we\n  // have time to flush the rejections down through React's rendering stream before `\n  // we call abort() on that.  If the user provides their own it's up to them to\n  // decouple the aborting of the stream from the aborting of React's renderToPipeableStream\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data, {\n    signal: controller.signal,\n    plugins: [value => {\n      // Even though we sanitized errors on context.errors prior to responding,\n      // we still need to handle this for any deferred data that rejects with an\n      // Error - as those will not be sanitized yet\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === ServerMode.Production ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof UNSAFE_ErrorResponseImpl) {\n        let {\n          data,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\nfunction data(value, init) {\n  return data$1(value, init);\n}\n\n// prettier-ignore\n// eslint-disable-next-line\n\nexport { SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, data, encodeViaTurboStream, getSingleFetchDataStrategy, getSingleFetchRedirect, singleFetchAction, singleFetchLoaders };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    memo[routeId] = manifest[routeId].module;\n    return memo;\n  }, {});\n}\n\nexport { createEntryRouteModules };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in Remix; please open an issue! https://github.com/remix-run/remix/issues/new\");\n    throw new Error(message);\n  }\n}\n\nexport { invariant as default };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { matchRoutes } from '@remix-run/router';\n\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\nexport { matchServerRoutes };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { isResponse, json, isDeferredData, isRedirectStatusCode, redirect } from './responses.js';\n\n/**\n * An object of unknown type for route loaders and actions provided by the\n * server's `getLoadContext()` function.  This is defined as an empty interface\n * specifically so apps can leverage declaration merging to augment this type\n * globally: https://www.typescriptlang.org/docs/handbook/declaration-merging.html\n */\n\n/**\n * Data for a route that was returned from a `loader()`.\n */\n\nasync function callRouteAction({\n  loadContext,\n  action,\n  params,\n  request,\n  routeId,\n  singleFetch\n}) {\n  let result = await action({\n    request: singleFetch ? stripRoutesParam(stripIndexParam(request)) : stripDataParam(stripIndexParam(request)),\n    context: loadContext,\n    params\n  });\n  if (result === undefined) {\n    throw new Error(`You defined an action for route \"${routeId}\" but didn't return ` + `anything from your \\`action\\` function. Please return a value or \\`null\\`.`);\n  }\n\n  // Allow naked object returns when single fetch is enabled\n  if (singleFetch) {\n    return result;\n  }\n  return isResponse(result) ? result : json(result);\n}\nasync function callRouteLoader({\n  loadContext,\n  loader,\n  params,\n  request,\n  routeId,\n  singleFetch\n}) {\n  let result = await loader({\n    request: singleFetch ? stripRoutesParam(stripIndexParam(request)) : stripDataParam(stripIndexParam(request)),\n    context: loadContext,\n    params\n  });\n  if (result === undefined) {\n    throw new Error(`You defined a loader for route \"${routeId}\" but didn't return ` + `anything from your \\`loader\\` function. Please return a value or \\`null\\`.`);\n  }\n  if (isDeferredData(result)) {\n    if (result.init && isRedirectStatusCode(result.init.status || 200)) {\n      return redirect(new Headers(result.init.headers).get(\"Location\"), result.init);\n    }\n    return result;\n  }\n\n  // Allow naked object returns when single fetch is enabled\n  if (singleFetch) {\n    return result;\n  }\n  return isResponse(result) ? result : json(result);\n}\n\n// TODO: Document these search params better\n// and stop stripping these in V2. These break\n// support for running in a SW and also expose\n// valuable info to data funcs that is being asked\n// for such as \"is this a data request?\".\nfunction stripIndexParam(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripDataParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_data\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\nexport { callRouteAction, callRouteLoader };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { callRouteLoader, callRouteAction } from './data.js';\n\n// NOTE: make sure to change the Route in remix-react if you change this\n\n// NOTE: make sure to change the EntryRoute in remix-react if you change this\n\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    let parentId = route.parentId || \"\";\n    if (!routes[parentId]) {\n      routes[parentId] = [];\n    }\n    routes[parentId].push(route);\n  });\n  return routes;\n}\n\n// Create a map of routes by parentId to use recursively instead of\n// repeatedly filtering the manifest.\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\n\n// Convert the Remix ServerManifest into DataRouteObject's for use with\n// createStaticHandler\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      loader: route.module.loader ?\n      // Need to use RR's version here to permit the optional context even\n      // though we know it'll always be provided in remix\n      (args, dataStrategyCtx) => callRouteLoader({\n        request: args.request,\n        params: args.params,\n        loadContext: args.context,\n        loader: route.module.loader,\n        routeId: route.id,\n        singleFetch: future.v3_singleFetch === true\n      }) : undefined,\n      action: route.module.action ? (args, dataStrategyCtx) => callRouteAction({\n        request: args.request,\n        params: args.params,\n        loadContext: args.context,\n        action: route.module.action,\n        routeId: route.id,\n        singleFetch: future.v3_singleFetch === true\n      }) : undefined,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId),\n      ...commonRoute\n    };\n  });\n}\n\nexport { createRoutes, createStaticHandlerDataRoutes };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n// This escapeHtml utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\n// We've chosen to inline the utility here to reduce the number of npm dependencies we have,\n// slightly decrease the code size compared the original package and make it esm compatible.\n\nconst ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\nexport { escapeHtml };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { escapeHtml } from './markup.js';\n\n// TODO: Remove Promises from serialization\nfunction createServerHandoffString(serverHandoff) {\n  // Uses faster alternative of jsesc to escape data returned from the loaders.\n  // This string is inserted directly into the HTML in the `<Scripts>` element.\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\nexport { createServerHandoffString };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nasync function broadcastDevReady(build, origin) {\n  origin ??= process.env.REMIX_DEV_ORIGIN;\n  if (!origin) throw Error(\"Dev server origin not set\");\n  let url = new URL(origin);\n  url.pathname = \"ping\";\n  let response = await fetch(url.href, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\"\n    },\n    body: JSON.stringify({\n      buildHash: build.assets.version\n    })\n  }).catch(error => {\n    console.error(`Could not reach Remix dev server at ${url}`);\n    throw error;\n  });\n  if (!response.ok) {\n    console.error(`Could not reach Remix dev server at ${url} (${response.status})`);\n    throw Error(await response.text());\n  }\n}\nfunction logDevReady(build) {\n  console.log(`[REMIX DEV] ${build.assets.version} ready`);\n}\nconst globalDevServerHooksKey = \"__remix_devServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  // @ts-expect-error\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  // @ts-expect-error\n  return globalThis[globalDevServerHooksKey];\n}\n\nexport { broadcastDevReady, getDevServerHooks, logDevReady, setDevServerHooks };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction resourceRouteJsonWarning(type, routeId) {\n  return \"⚠️ REMIX FUTURE CHANGE: Externally-accessed resource routes will no longer be \" + \"able to return raw JavaScript objects or `null` in React Router v7 when \" + \"Single Fetch becomes the default. You can prepare for this change at your \" + `convenience by wrapping the data returned from your \\`${type}\\` function in ` + `the \\`${routeId}\\` route with \\`json()\\`.  For instructions on making this ` + \"change, see https://remix.run/docs/en/v2.13.1/guides/single-fetch#resource-routes\";\n}\n\nexport { resourceRouteJsonWarning };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { UNSAFE_DEFERRED_SYMBOL, isRouteErrorResponse, json as json$1, UNSAFE_ErrorResponseImpl, getStaticContextFromError, stripBasename, createStaticHandler } from '@remix-run/router';\nimport { createEntryRouteModules } from './entry.js';\nimport { serializeError, sanitizeErrors, serializeErrors } from './errors.js';\nimport { getDocumentHeaders } from './headers.js';\nimport invariant from './invariant.js';\nimport { ServerMode, isServerMode } from './mode.js';\nimport { matchServerRoutes } from './routeMatching.js';\nimport { createRoutes, createStaticHandlerDataRoutes } from './routes.js';\nimport { isRedirectResponse, json, createDeferredReadableStream, isResponse } from './responses.js';\nimport { createServerHandoffString } from './serverHandoff.js';\nimport { getDevServerHooks } from './dev.js';\nimport { getSingleFetchRedirect, encodeViaTurboStream, SINGLE_FETCH_REDIRECT_STATUS, singleFetchAction, singleFetchLoaders, SingleFetchRedirectSymbol } from './single-fetch.js';\nimport { resourceRouteJsonWarning } from './deprecations.js';\n\n// Do not include a response body if the status code is one of these,\n// otherwise `undici` will throw an error when constructing the Response:\n//   https://github.com/nodejs/undici/blob/bd98a6303e45d5e0d44192a93731b1defdb415f3/lib/web/fetch/response.js#L522-L528\n//\n// Specs:\n//   https://datatracker.ietf.org/doc/html/rfc9110#name-informational-1xx\n//   https://datatracker.ietf.org/doc/html/rfc9110#name-204-no-content\n//   https://datatracker.ietf.org/doc/html/rfc9110#name-205-reset-content\n//   https://datatracker.ietf.org/doc/html/rfc9110#name-304-not-modified\nconst NO_BODY_STATUS_CODES = new Set([100, 101, 204, 205, 304]);\nfunction derive(build, mode) {\n  var _build$future, _build$future2;\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : ServerMode.Production;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename,\n    future: {\n      v7_relativeSplatPath: ((_build$future = build.future) === null || _build$future === void 0 ? void 0 : _build$future.v3_relativeSplatPath) === true,\n      v7_throwAbortReason: ((_build$future2 = build.future) === null || _build$future2 === void 0 ? void 0 : _build$future2.v3_throwAbortReason) === true\n    }\n  });\n  let errorHandler = build.entry.module.handleError || ((error, {\n    request\n  }) => {\n    if (serverMode !== ServerMode.Test && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nconst createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, loadContext = {}) {\n    _build = typeof build === \"function\" ? await build() : build;\n    mode ??= _build.mode;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let url = new URL(request.url);\n    let params = {};\n    let handleError = error => {\n      if (mode === ServerMode.Development) {\n        var _getDevServerHooks, _getDevServerHooks$pr;\n        (_getDevServerHooks = getDevServerHooks()) === null || _getDevServerHooks === void 0 ? void 0 : (_getDevServerHooks$pr = _getDevServerHooks.processRequestError) === null || _getDevServerHooks$pr === void 0 ? void 0 : _getDevServerHooks$pr.call(_getDevServerHooks, error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n\n    // Manifest request for fog of war\n\n    let manifestUrl = `${_build.basename ?? \"/\"}/__manifest`.replace(/\\/+/g, \"/\");\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, url.pathname, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.searchParams.has(\"_data\")) {\n      if (_build.future.v3_singleFetch) {\n        handleError(new Error(\"Warning: Single fetch-enabled apps should not be making ?_data requests, \" + \"this is likely to break in the future\"));\n      }\n      let routeId = url.searchParams.get(\"_data\");\n      response = await handleDataRequest(serverMode, _build, staticHandler, routeId, request, loadContext, handleError);\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params,\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = createRemixRedirectResponse(response, _build.basename);\n        }\n      }\n    } else if (_build.future.v3_singleFetch && url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = handlerUrl.pathname.replace(/\\.data$/, \"\").replace(/^\\/_root$/, \"/\");\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          let result = getSingleFetchRedirect(response.status, response.headers, _build.basename);\n          if (request.method === \"GET\") {\n            result = {\n              [SingleFetchRedirectSymbol]: result\n            };\n          }\n          let headers = new Headers(response.headers);\n          headers.set(\"Content-Type\", \"text/x-script\");\n          return new Response(encodeViaTurboStream(result, request.signal, _build.entry.module.streamTimeout, serverMode), {\n            status: SINGLE_FETCH_REDIRECT_STATUS,\n            headers\n          });\n        }\n      }\n    } else if (matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      var _getDevServerHooks2, _getDevServerHooks2$g;\n      let criticalCss = mode === ServerMode.Development ? await ((_getDevServerHooks2 = getDevServerHooks()) === null || _getDevServerHooks2 === void 0 ? void 0 : (_getDevServerHooks2$g = _getDevServerHooks2.getCriticalCss) === null || _getDevServerHooks2$g === void 0 ? void 0 : _getDevServerHooks2$g.call(_getDevServerHooks2, _build, url.pathname)) : undefined;\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = new Set();\n\n    // In addition to responding with the patches for the requested paths, we\n    // need to include patches for each partial path so that we pick up any\n    // pathless/index routes below ancestor segments.  So if we\n    // get a request for `/parent/child`, we need to look for a match on `/parent`\n    // so that if a `parent._index` route exists we return it so it's available\n    // for client side matching if the user routes back up to `/parent`.\n    // This is the same thing we do on initial load in <Scripts> via\n    // `getPartialManifest()`\n    url.searchParams.getAll(\"p\").forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          patches[routeId] = build.assets.routes[routeId];\n        }\n      }\n    }\n    return json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    }); // Override the TypedResponse stuff from json()\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleDataRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext\n    });\n    if (isRedirectResponse(response)) {\n      return createRemixRedirectResponse(response, build.basename);\n    }\n    if (UNSAFE_DEFERRED_SYMBOL in response) {\n      let deferredData = response[UNSAFE_DEFERRED_SYMBOL];\n      let body = createDeferredReadableStream(deferredData, request.signal, serverMode);\n      let init = deferredData.init || {};\n      let headers = new Headers(init.headers);\n      headers.set(\"Content-Type\", \"text/remix-deferred\");\n      // Mark successful responses with a header so we can identify in-flight\n      // network errors that are missing this header\n      headers.set(\"X-Remix-Response\", \"yes\");\n      init.headers = headers;\n      return new Response(body, init);\n    }\n\n    // Mark all successful responses with a header so we can identify in-flight\n    // network errors that are missing this header\n    response = safelySetHeader(response, \"X-Remix-Response\", \"yes\");\n    return response;\n  } catch (error) {\n    if (isResponse(error)) {\n      let response = safelySetHeader(error, \"X-Remix-Catch\", \"yes\");\n      return response;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    let errorInstance = error instanceof Error || error instanceof DOMException ? error : new Error(\"Unexpected Server Error\");\n    handleError(errorInstance);\n    return json$1(serializeError(errorInstance, serverMode), {\n      status: 500,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    });\n  }\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let {\n    result,\n    headers,\n    status\n  } = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n\n  // Mark all successful responses with a header so we can identify in-flight\n  // network errors that are missing this header\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n\n  // Skip response body for unsupported status codes\n  if (NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n\n  // We use a less-descriptive `text/x-script` here instead of something like\n  // `text/x-turbo` to enable compression when deployed via Cloudflare.  See:\n  //  - https://github.com/remix-run/remix/issues/9884\n  //  - https://developers.cloudflare.com/speed/optimization/content/brotli/content-compression/\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n\n  // Note: Deferred data is already just Promises, so we don't have to mess\n  // `activeDeferreds` or anything :)\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, criticalCss) {\n  let context;\n  try {\n    context = await staticHandler.query(request, {\n      requestContext: loadContext\n    });\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  if (isResponse(context)) {\n    return context;\n  }\n  let headers = getDocumentHeaders(build, context);\n\n  // Skip response body for unsupported status codes\n  if (NO_BODY_STATUS_CODES.has(context.statusCode)) {\n    return new Response(null, {\n      status: context.statusCode,\n      headers\n    });\n  }\n\n  // Sanitize errors outside of development environments\n  if (context.errors) {\n    Object.values(context.errors).forEach(err => {\n      // @ts-expect-error `err.error` is \"private\" from users but intended for internal use\n      if (!isRouteErrorResponse(err) || err.error) {\n        handleError(err);\n      }\n    });\n    context.errors = sanitizeErrors(context.errors, serverMode);\n  }\n\n  // Server UI state to send to the client.\n  // - When single fetch is enabled, this is streamed down via `serverHandoffStream`\n  // - Otherwise it's stringified into `serverHandoffString`\n  let state = {\n    loaderData: context.loaderData,\n    actionData: context.actionData,\n    errors: serializeErrors(context.errors, serverMode)\n  };\n  let entryContext = {\n    manifest: build.assets,\n    routeModules: createEntryRouteModules(build.routes),\n    staticHandlerContext: context,\n    criticalCss,\n    serverHandoffString: createServerHandoffString({\n      basename: build.basename,\n      criticalCss,\n      future: build.future,\n      isSpaMode: build.isSpaMode,\n      ...(!build.future.v3_singleFetch ? {\n        state\n      } : null)\n    }),\n    ...(build.future.v3_singleFetch ? {\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {}\n    } : null),\n    future: build.future,\n    isSpaMode: build.isSpaMode,\n    serializeError: err => serializeError(err, serverMode)\n  };\n  let handleDocumentRequestFunction = build.entry.module.default;\n  try {\n    return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n  } catch (error) {\n    handleError(error);\n    let errorForSecondRender = error;\n\n    // If they threw a response, unwrap it into an ErrorResponse like we would\n    // have for a loader/action\n    if (isResponse(error)) {\n      try {\n        let data = await unwrapResponse(error);\n        errorForSecondRender = new UNSAFE_ErrorResponseImpl(error.status, error.statusText, data);\n      } catch (e) {\n        // If we can't unwrap the response - just leave it as-is\n      }\n    }\n\n    // Get a new StaticHandlerContext that contains the error at the right boundary\n    context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n\n    // Sanitize errors outside of development environments\n    if (context.errors) {\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n\n    // Get a new entryContext for the second render pass\n    // Server UI state to send to the client.\n    // - When single fetch is enabled, this is streamed down via `serverHandoffStream`\n    // - Otherwise it's stringified into `serverHandoffString`\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    entryContext = {\n      ...entryContext,\n      staticHandlerContext: context,\n      serverHandoffString: createServerHandoffString({\n        basename: build.basename,\n        future: build.future,\n        isSpaMode: build.isSpaMode,\n        ...(!build.future.v3_singleFetch ? {\n          state\n        } : null)\n      }),\n      ...(build.future.v3_singleFetch ? {\n        serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      } : null)\n    };\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      return returnLastResortErrorResponse(error, serverMode);\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    // Note we keep the routeId here to align with the Remix handling of\n    // resource routes which doesn't take ?index into account and just takes\n    // the leaf match\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext\n    });\n    if (typeof response === \"object\" && response !== null) {\n      invariant(!(UNSAFE_DEFERRED_SYMBOL in response), `You cannot return a \\`defer()\\` response from a Resource Route.  Did you ` + `forget to export a default UI component from the \"${routeId}\" route?`);\n    }\n    if (build.future.v3_singleFetch && !isResponse(response)) {\n      console.warn(resourceRouteJsonWarning(request.method === \"GET\" ? \"loader\" : \"action\", routeId));\n      response = json(response);\n    }\n\n    // callRouteLoader/callRouteAction always return responses (w/o single fetch).\n    // With single fetch, users should always be Responses from resource routes\n    invariant(isResponse(response), \"Expected a Response to be returned from queryRoute\");\n    return response;\n  } catch (error) {\n    if (isResponse(error)) {\n      // Note: Not functionally required but ensures that our response headers\n      // match identically to what Remix returns\n      let response = safelySetHeader(error, \"X-Remix-Catch\", \"yes\");\n      return response;\n    }\n    if (isRouteErrorResponse(error)) {\n      if (error) {\n        handleError(error);\n      }\n      return errorResponseToJson(error, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return json$1(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText,\n    headers: {\n      \"X-Remix-Error\": \"yes\"\n    }\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== ServerMode.Production) {\n    message += `\\n\\n${String(error)}`;\n  }\n\n  // Good grief folks, get your act together 😂!\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  // Check between word boundaries instead of startsWith() due to the last\n  // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\nfunction createRemixRedirectResponse(response, basename) {\n  // We don't have any way to prevent a fetch request from following\n  // redirects. So we use the `X-Remix-Redirect` header to indicate the\n  // next URL, and then \"follow\" the redirect manually on the client.\n  let headers = new Headers(response.headers);\n  let redirectUrl = headers.get(\"Location\");\n  headers.set(\"X-Remix-Redirect\", basename ? stripBasename(redirectUrl, basename) || redirectUrl : redirectUrl);\n  headers.set(\"X-Remix-Status\", String(response.status));\n  headers.delete(\"Location\");\n  if (response.headers.get(\"Set-Cookie\") !== null) {\n    headers.set(\"X-Remix-Revalidate\", \"yes\");\n  }\n  return new Response(null, {\n    status: 204,\n    headers\n  });\n}\n\n// Anytime we are setting a header on a `Response` created in the loader/action,\n// we have to so it in this manner since in an `undici` world, if the `Response`\n// came directly from a `fetch` call, the headers are immutable will throw if\n// we try to set a new header.  This is a sort of shallow clone of the `Response`\n// so we can safely set our own header.\nfunction safelySetHeader(response, name, value) {\n  let headers = new Headers(response.headers);\n  headers.set(name, value);\n  return new Response(response.body, {\n    status: response.status,\n    statusText: response.statusText,\n    headers,\n    duplex: response.body ? \"half\" : undefined\n  });\n}\n\nexport { createRequestHandler };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { isCookie } from './cookies.js';\nimport { warnOnce } from './warnings.js';\n\n/**\n * An object of name/value pairs to be used in the session.\n */\n\n/**\n * Session persists data across HTTP requests.\n *\n * @see https://remix.run/utils/sessions#session-api\n */\n\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\n/**\n * Creates a new Session object.\n *\n * Note: This function is typically not invoked directly by application code.\n * Instead, use a `SessionStorage` object's `getSession` method.\n *\n * @see https://remix.run/utils/sessions#createsession\n */\nconst createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return undefined;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\n/**\n * Returns true if an object is a Remix session.\n *\n * @see https://remix.run/utils/sessions#issession\n */\nconst isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\n\n/**\n * SessionStorage stores session data between HTTP requests and knows how to\n * parse and create cookies.\n *\n * A SessionStorage creates Session objects using a `Cookie` header as input.\n * Then, later it generates the `Set-Cookie` header to be used in the response.\n */\n\n/**\n * SessionIdStorageStrategy is designed to allow anyone to easily build their\n * own SessionStorage using `createSessionStorage(strategy)`.\n *\n * This strategy describes a common scenario where the session id is stored in\n * a cookie but the actual session data is stored elsewhere, usually in a\n * database or on disk. A set of create, read, update, and delete operations\n * are provided for managing the session data.\n */\n\n/**\n * Creates a SessionStorage object using a SessionIdStorageStrategy.\n *\n * Note: This is a low-level API that should only be used if none of the\n * existing session storage options meet your requirements.\n *\n * @see https://remix.run/utils/sessions#createsessionstorage\n */\nconst createSessionStorageFactory = createCookie => ({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) => {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie((cookieArg === null || cookieArg === void 0 ? void 0 : cookieArg.name) || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data = id && (await readData(id));\n      return createSession(data || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data\n      } = session;\n      let expires = (options === null || options === void 0 ? void 0 : options.maxAge) != null ? new Date(Date.now() + options.maxAge * 1000) : (options === null || options === void 0 ? void 0 : options.expires) != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data, expires);\n      } else {\n        id = await createData(data, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: undefined,\n        expires: new Date(0)\n      });\n    }\n  };\n};\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, `The \"${cookie.name}\" cookie is not signed, but session cookies should be ` + `signed to prevent tampering on the client before they are sent back to the ` + `server. See https://remix.run/utils/cookies#signing-cookies ` + `for more information.`);\n}\n\nexport { createSession, createSessionStorageFactory, isSession, warnOnceAboutSigningSessionCookie };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { isCookie } from '../cookies.js';\nimport { warnOnceAboutSigningSessionCookie, createSession } from '../sessions.js';\n\n/**\n * Creates and returns a SessionStorage object that stores all session data\n * directly in the session cookie itself.\n *\n * This has the advantage that no database or other backend services are\n * needed, and can help to simplify some load-balanced scenarios. However, it\n * also has the limitation that serialized session data may not exceed the\n * browser's maximum cookie size. Trade-offs!\n *\n * @see https://remix.run/utils/sessions#createcookiesessionstorage\n */\nconst createCookieSessionStorageFactory = createCookie => ({\n  cookie: cookieArg\n} = {}) => {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie((cookieArg === null || cookieArg === void 0 ? void 0 : cookieArg.name) || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: undefined,\n        expires: new Date(0)\n      });\n    }\n  };\n};\n\nexport { createCookieSessionStorageFactory };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n/**\n * Creates and returns a simple in-memory SessionStorage object, mostly useful\n * for testing and as a reference implementation.\n *\n * Note: This storage does not scale beyond a single process, so it is not\n * suitable for most production scenarios.\n *\n * @see https://remix.run/utils/sessions#creatememorysessionstorage\n */\nconst createMemorySessionStorageFactory = createSessionStorage => ({\n  cookie\n} = {}) => {\n  let map = new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data,\n          expires\n        } = map.get(id);\n        if (!expires || expires > new Date()) {\n          return data;\n        }\n\n        // Remove expired session data.\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data, expires) {\n      map.set(id, {\n        data,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n};\n\nexport { createMemorySessionStorageFactory };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nclass MaxPartSizeExceededError extends Error {\n  constructor(field, maxBytes) {\n    super(`Field \"${field}\" exceeded upload size of ${maxBytes} bytes.`);\n    this.field = field;\n    this.maxBytes = maxBytes;\n  }\n}\n\nexport { MaxPartSizeExceededError };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { MaxPartSizeExceededError } from './errors.js';\n\nfunction createMemoryUploadHandler({\n  filter,\n  maxPartSize = 3000000\n} = {}) {\n  return async ({\n    filename,\n    contentType,\n    name,\n    data\n  }) => {\n    if (filter && !(await filter({\n      filename,\n      contentType,\n      name\n    }))) {\n      return undefined;\n    }\n    let size = 0;\n    let chunks = [];\n    for await (let chunk of data) {\n      size += chunk.byteLength;\n      if (size > maxPartSize) {\n        throw new MaxPartSizeExceededError(name, maxPartSize);\n      }\n      chunks.push(chunk);\n    }\n    if (typeof filename === \"string\") {\n      return new File(chunks, filename, {\n        type: contentType\n      });\n    }\n    return await new Blob(chunks, {\n      type: contentType\n    }).text();\n  };\n}\n\nexport { createMemoryUploadHand<PERSON> };\n", "/**\n * @remix-run/server-runtime v2.16.8\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nexport { createCookieFactory, isCookie } from './cookies.js';\nexport { composeUploadHandlers as unstable_composeUploadHandlers, parseMultipartFormData as unstable_parseMultipartFormData } from './formData.js';\nexport { defer, json, redirect, redirectDocument, replace } from './responses.js';\nexport { SingleFetchRedirectSymbol as UNSAFE_SingleFetchRedirectSymbol, data } from './single-fetch.js';\nexport { createRequestHandler } from './server.js';\nexport { createSession, createSessionStorageFactory, isSession } from './sessions.js';\nexport { createCookieSessionStorageFactory } from './sessions/cookieStorage.js';\nexport { createMemorySessionStorageFactory } from './sessions/memoryStorage.js';\nexport { createMemoryUploadHandler as unstable_createMemoryUploadHandler } from './upload/memoryUploadHandler.js';\nexport { MaxPartSizeExceededError } from './upload/errors.js';\nexport { broadcastDevReady, logDevReady, setDevServerHooks as unstable_setDevServerHooks } from './dev.js';\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAcA,YAAQ,QAAQA;AAChB,YAAQ,YAAYC;AAOpB,QAAI,aAAa,OAAO,UAAU;AAClC,QAAI,mBAAmB,OAAO,UAAU;AAcxC,QAAI,mBAAmB;AAYvB,QAAI,oBAAoB;AA0BxB,QAAI,oBAAoB;AAUxB,QAAI,kBAAkB;AActB,aAASD,OAAM,KAAK,KAAK;AACvB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AAEA,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,IAAI;AAEd,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,MAAO,OAAO,IAAI,UAAW;AACjC,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,UAAI,SAAS;AAEb,SAAG;AACD,gBAAQ,IAAI,QAAQ,KAAK,KAAK;AAC9B,YAAI,UAAU,GAAI;AAElB,iBAAS,IAAI,QAAQ,KAAK,KAAK;AAE/B,YAAI,WAAW,IAAI;AACjB,mBAAS;AAAA,QACX,WAAW,QAAQ,QAAQ;AAEzB,kBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;AAAA,QACF;AAEA,YAAI,cAAc,WAAW,KAAK,OAAO,KAAK;AAC9C,YAAI,YAAY,SAAS,KAAK,OAAO,WAAW;AAChD,YAAI,MAAM,IAAI,MAAM,aAAa,SAAS;AAG1C,YAAI,CAAC,iBAAiB,KAAK,KAAK,GAAG,GAAG;AACpC,cAAI,cAAc,WAAW,KAAK,QAAQ,GAAG,MAAM;AACnD,cAAI,YAAY,SAAS,KAAK,QAAQ,WAAW;AAEjD,cAAI,IAAI,WAAW,WAAW,MAAM,MAAgB,IAAI,WAAW,YAAY,CAAC,MAAM,IAAc;AAClG;AACA;AAAA,UACF;AAEA,cAAI,MAAM,IAAI,MAAM,aAAa,SAAS;AAC1C,cAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAAA,QAC/B;AAEA,gBAAQ,SAAS;AAAA,MACnB,SAAS,QAAQ;AAEjB,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,KAAK,OAAO,KAAK;AACnC,SAAG;AACD,YAAI,OAAO,IAAI,WAAW,KAAK;AAC/B,YAAI,SAAS,MAAgB,SAAS,EAAe,QAAO;AAAA,MAC9D,SAAS,EAAE,QAAQ;AACnB,aAAO;AAAA,IACT;AAEA,aAAS,SAAS,KAAK,OAAO,KAAK;AACjC,aAAO,QAAQ,KAAK;AAClB,YAAI,OAAO,IAAI,WAAW,EAAE,KAAK;AACjC,YAAI,SAAS,MAAgB,SAAS,EAAe,QAAO,QAAQ;AAAA,MACtE;AACA,aAAO;AAAA,IACT;AAkBA,aAASC,WAAU,MAAM,KAAK,KAAK;AACjC,UAAI,MAAO,OAAO,IAAI,UAAW;AAEjC,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,CAAC,iBAAiB,KAAK,IAAI,GAAG;AAChC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,QAAQ,IAAI,GAAG;AAEnB,UAAI,CAAC,kBAAkB,KAAK,KAAK,GAAG;AAClC,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC/C;AAEA,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,CAAC,IAAK,QAAO;AAEjB,UAAI,QAAQ,IAAI,QAAQ;AACtB,YAAI,SAAS,KAAK,MAAM,IAAI,MAAM;AAElC,YAAI,CAAC,SAAS,MAAM,GAAG;AACrB,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,eAAO,eAAe;AAAA,MACxB;AAEA,UAAI,IAAI,QAAQ;AACd,YAAI,CAAC,kBAAkB,KAAK,IAAI,MAAM,GAAG;AACvC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,eAAO,cAAc,IAAI;AAAA,MAC3B;AAEA,UAAI,IAAI,MAAM;AACZ,YAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,GAAG;AACnC,gBAAM,IAAI,UAAU,wBAAwB;AAAA,QAC9C;AAEA,eAAO,YAAY,IAAI;AAAA,MACzB;AAEA,UAAI,IAAI,SAAS;AACf,YAAI,UAAU,IAAI;AAElB,YAAI,CAAC,OAAO,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAChD,gBAAM,IAAI,UAAU,2BAA2B;AAAA,QACjD;AAEA,eAAO,eAAe,QAAQ,YAAY;AAAA,MAC5C;AAEA,UAAI,IAAI,UAAU;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,QAAQ;AACd,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,aAAa;AACnB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IAAI,IAAI;AAErC,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AAEA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IAAI,IAAI;AAErC,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,aAAS,OAAQ,KAAK;AACpB,aAAO,IAAI,QAAQ,GAAG,MAAM,KACxB,mBAAmB,GAAG,IACtB;AAAA,IACN;AASA,aAAS,OAAQ,KAAK;AACpB,aAAO,WAAW,KAAK,GAAG,MAAM;AAAA,IAClC;AAUA,aAAS,UAAU,KAAKC,SAAQ;AAC9B,UAAI;AACF,eAAOA,QAAO,GAAG;AAAA,MACnB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACnUA,SAAS,SAAS,WAAW,SAAS;AACpC,MAAI,CAAC,aAAa,CAAC,cAAc,OAAO,GAAG;AACzC,kBAAc,OAAO,IAAI;AACzB,YAAQ,KAAK,OAAO;AAAA,EACtB;AACF;AAhBA,IAUM;AAVN;AAAA;AAUA,IAAM,gBAAgB,CAAC;AAAA;AAAA;;;ACmEvB,eAAe,kBAAkB,MAAM,OAAO,SAAS;AACrD,MAAI,UAAU,WAAW,KAAK;AAC9B,MAAI,QAAQ,SAAS,GAAG;AACtB,cAAU,MAAM,KAAK,SAAS,QAAQ,CAAC,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AACA,eAAe,kBAAkB,QAAQ,OAAO,SAAS;AACvD,MAAI,QAAQ,SAAS,GAAG;AACtB,aAAS,UAAU,SAAS;AAC1B,UAAI,gBAAgB,MAAM,OAAO,OAAO,MAAM;AAC9C,UAAI,kBAAkB,OAAO;AAC3B,eAAO,WAAW,aAAa;AAAA,MACjC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,WAAW,KAAK;AACzB;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,KAAK,WAAW,mBAAmB,KAAK,UAAU,KAAK,CAAC,CAAC,CAAC;AACnE;AACA,SAAS,WAAW,OAAO;AACzB,MAAI;AACF,WAAO,KAAK,MAAM,mBAAmB,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,EAC7D,SAAS,OAAO;AACd,WAAO,CAAC;AAAA,EACV;AACF;AAGA,SAAS,SAAS,OAAO;AACvB,MAAI,MAAM,MAAM,SAAS;AACzB,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,KAAK;AACT,SAAO,QAAQ,IAAI,QAAQ;AACzB,UAAM,IAAI,OAAO,OAAO;AACxB,QAAI,cAAc,KAAK,GAAG,GAAG;AAC3B,gBAAU;AAAA,IACZ,OAAO;AACL,aAAO,IAAI,WAAW,CAAC;AACvB,UAAI,OAAO,KAAK;AACd,kBAAU,MAAM,IAAI,MAAM,CAAC;AAAA,MAC7B,OAAO;AACL,kBAAU,OAAO,IAAI,MAAM,CAAC,EAAE,YAAY;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,IAAI,MAAM,QAAQ;AACzB,MAAI,SAAS,KAAK,SAAS,EAAE;AAC7B,SAAO,OAAO,SAAS,OAAQ,UAAS,MAAM;AAC9C,SAAO;AACT;AAGA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM,MAAM,SAAS;AACzB,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,KAAK;AACT,SAAO,QAAQ,IAAI,QAAQ;AACzB,UAAM,IAAI,OAAO,OAAO;AACxB,QAAI,QAAQ,KAAK;AACf,UAAI,IAAI,OAAO,KAAK,MAAM,KAAK;AAC7B,eAAO,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC;AACrC,YAAI,gBAAgB,KAAK,IAAI,GAAG;AAC9B,oBAAU,OAAO,aAAa,SAAS,MAAM,EAAE,CAAC;AAChD,mBAAS;AACT;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,IAAI,MAAM,OAAO,QAAQ,CAAC;AACjC,YAAI,gBAAgB,KAAK,IAAI,GAAG;AAC9B,oBAAU,OAAO,aAAa,SAAS,MAAM,EAAE,CAAC;AAChD,mBAAS;AACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,cAAU;AAAA,EACZ;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,MAAM,SAAS;AACjD,WAAS,CAAC,SAAS,QAAQ,IAAI,6WAAiY;AACla;AArKA,IAUA,eAmBM,qBA6CA;AA1EN;AAAA;AAUA,oBAAiC;AACjC;AAkBA,IAAM,sBAAsB,CAAC;AAAA,MAC3B;AAAA,MACA;AAAA,IACF,MAAM,CAAC,MAAM,gBAAgB,CAAC,MAAM;AAClC,UAAI;AAAA,QACF,UAAU,CAAC;AAAA,QACX,GAAG;AAAA,MACL,IAAI;AAAA,QACF,MAAM;AAAA,QACN,UAAU;AAAA,QACV,GAAG;AAAA,MACL;AACA,iCAA2B,MAAM,QAAQ,OAAO;AAChD,aAAO;AAAA,QACL,IAAI,OAAO;AACT,iBAAO;AAAA,QACT;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,QAAQ,SAAS;AAAA,QAC1B;AAAA,QACA,IAAI,UAAU;AAEZ,iBAAO,OAAO,QAAQ,WAAW,cAAc,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,SAAS,GAAI,IAAI,QAAQ;AAAA,QACxG;AAAA,QACA,MAAM,MAAM,cAAc,cAAc;AACtC,cAAI,CAAC,aAAc,QAAO;AAC1B,cAAI,cAAU,qBAAM,cAAc;AAAA,YAChC,GAAG;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AACD,iBAAO,QAAQ,UAAU,QAAQ,IAAI,MAAM,KAAK,KAAK,MAAM,kBAAkB,QAAQ,QAAQ,IAAI,GAAG,OAAO,IAAI;AAAA,QACjH;AAAA,QACA,MAAM,UAAU,OAAO,kBAAkB;AACvC,qBAAO,yBAAU,MAAM,UAAU,KAAK,KAAK,MAAM,kBAAkB,MAAM,OAAO,OAAO,GAAG;AAAA,YACxF,GAAG;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAMA,IAAM,WAAW,YAAU;AACzB,aAAO,UAAU,QAAQ,OAAO,OAAO,SAAS,YAAY,OAAO,OAAO,aAAa,aAAa,OAAO,OAAO,UAAU,cAAc,OAAO,OAAO,cAAc;AAAA,IACxK;AAAA;AAAA;;;AC5EO,SAAS,cAAc,GAAG;AAC/B,QAAM,OAAO,SAAS,mBAAmB,CAAC,CAAC;AAC3C,SAAO,WAAW,KAAK,MAAM,CAAC,GAAG,MAAM,KAAK,WAAW,CAAC,CAAC;AAC3D;AACO,SAAS,cAAc,GAAG;AAC/B,QAAM,OAAO,OAAO,aAAa,MAAM,MAAM,CAAC;AAC9C,SAAO,mBAAmB,OAAO,IAAI,CAAC;AACxC;AACO,SAAS,eAAe,QAAQ;AACrC,QAAM,MAAM,IAAI,WAAW,OAAO,OAAO,CAAC,OAAO,QAAQ,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAC/E,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,QAAI,IAAI,KAAK,MAAM;AACnB,cAAU,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AACO,SAAS,YAAY,GAAG,GAAG;AAChC,MAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AA3BA;AAAA;AAAA;AAAA;;;ACKA,SAAS,OAAO,GAAG;AACjB,MAAI,aAAa,YAAY;AAC3B,WAAO,WAAS,EAAE,KAAK;AAAA,EACzB;AACA,SAAO;AACT;AACA,SAAS,SAAS,MAAM,MAAM,MAAM,MAAM,KAAK;AAC7C,QAAM,MAAM,OAAO,IAAI;AACvB,QAAM,MAAM,OAAO,IAAI;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,QAAI,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,GAAG;AAC/B,QAAM,QAAQ,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE,MAAM;AAC1C,MAAI,EAAE,SAAS,GAAG;AAChB,aAAS,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK;AACrC,YAAM,EAAE,CAAC,CAAC,IAAI,EAAE,SAAS,IAAI;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AA7BA,IA8BM,OACA,cA8GA,sBAwBA;AArKN;AAAA;AAAA;AA8BA,IAAM,QAAQ,OAAO,OAAO;AAC5B,IAAM,eAAN,MAAmB;AAAA,MACjB,YAAY,QAAQ;AAClB,aAAK,cAAc,IAAI,WAAW;AAClC,YAAI,OAAO,WAAW,UAAU;AAC9B,eAAK,UAAU,SAAS,cAAc,MAAM;AAAA,QAC9C,OAAO;AACL,eAAK,UAAU;AAAA,QACjB;AACA,aAAK,YAAY,OAAO,OAAO,SAAS,CAAC;AACzC,aAAK,OAAO,qBAAqB,MAAM;AAAA,MACzC;AAAA,MACA,KAAK,OAAO;AACV,YAAI,MAAM;AACV,YAAI;AACJ,cAAM,YAAY,CAAC;AACnB,eAAO,QAAQ,MAAM,QAAQ;AAC3B;AACA,WAAC,KAAK,GAAG,MAAM,IAAI,KAAK,MAAM,OAAO,GAAG;AACxC,oBAAU,KAAK,GAAG,MAAM;AAAA,QAC1B;AACA,eAAO;AAAA,MACT;AAAA,MACA,MAAM;AACJ,cAAM,OAAO,KAAK;AAClB,aAAK,cAAc,IAAI,WAAW;AAClC,eAAO;AAAA,MACT;AAAA,MACA,MAAMC,OAAM,QAAQ;AAClB,cAAM,SAAS,CAAC;AAChB,YAAI,MAAM,CAAC,KAAK,YAAY;AAC5B,YAAI,MAAM,GAAG;AACX,iBAAO,MAAM,KAAK,OAAOA,MAAK,SAAS,KAAK,QAAQ,QAAQ;AAC1D,kBAAM,KAAK,KAAK,QAAQA,OAAM,MAAM,KAAK,QAAQ,SAAS,CAAC;AAC3D,gBAAI,OAAO,KAAK,aAAa,KAAK,QAAQA,OAAM,KAAK,KAAK,QAAQ,SAAS,CAAC,GAAG;AAC7E,kBAAI,MAAM,CAAC,KAAK,YAAY,QAAQ;AAClC,uBAAO,KAAK,KAAK,YAAY,MAAM,GAAG,KAAK,YAAY,SAAS,GAAG,CAAC;AAAA,cACtE;AACA,qBAAO,KAAK,KAAK;AACjB,mBAAK,cAAc,IAAI,WAAW;AAClC,qBAAO;AAAA,gBACL,MAAM,KAAK,QAAQ;AAAA,gBACnB,GAAG;AAAA,cACL;AAAA,YACF,OAAO;AACL,qBAAO,KAAK,KAAK,EAAE;AAAA,YACrB;AAAA,UACF;AACA,cAAI,MAAM,GAAG;AACX,mBAAO,MAAM,KAAK,CAAC,KAAK,QAAQA,OAAM,KAAKA,MAAK,SAAS,GAAG,GAAG;AAC7D;AAAA,YACF;AAAA,UACF;AACA,cAAI,OAAO,GAAG;AACZ,mBAAO,KAAK,KAAK,WAAW;AAC5B,iBAAK,cAAc,IAAI,WAAW;AAAA,UACpC,OAAO;AACL,kBAAM,gBAAgB,KAAK,YAAY,SAAS;AAChD,gBAAI,gBAAgB,GAAG;AACrB,qBAAO,KAAK,KAAK,YAAY,MAAM,GAAG,aAAa,CAAC;AACpD,mBAAK,cAAc,KAAK,YAAY,MAAM,aAAa;AAAA,YACzD;AACA,iBAAK,cAAc,WAAW,KAAK,IAAI,MAAM,KAAK,YAAY,SAASA,MAAK,MAAM,GAAG,CAAC,GAAG,MAAM,KAAK,QAAQA,OAAM,IAAI,KAAK,YAAY,MAAM,CAAC;AAC9I,mBAAO;AAAA,cACLA,MAAK;AAAA,cACL,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,eAAO;AACP,eAAO,OAAOA,MAAK,SAAS,KAAK,QAAQ,QAAQ;AAC/C,gBAAM,KAAKA,MAAK,MAAM,KAAK,QAAQ,SAAS,CAAC;AAC7C,cAAI,OAAO,KAAK,aAAaA,MAAK,GAAG,MAAM,KAAK,QAAQ,CAAC,KAAK,SAAS,KAAK,SAAS,GAAGA,OAAM,KAAK,KAAK,QAAQ,SAAS,CAAC,GAAG;AAC3H,gBAAI,MAAM,QAAQ;AAChB,qBAAO,KAAKA,MAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,YACrC;AACA,mBAAO,KAAK,KAAK;AACjB,mBAAO;AAAA,cACL,MAAM,KAAK,QAAQ;AAAA,cACnB,GAAG;AAAA,YACL;AAAA,UACF,OAAO;AACL,mBAAO,KAAK,KAAK,EAAE;AAAA,UACrB;AAAA,QACF;AACA,YAAI,MAAMA,MAAK,QAAQ;AACrB,iBAAO,MAAMA,MAAK,WAAWA,MAAK,GAAG,MAAM,KAAK,QAAQ,CAAC,KAAK,CAAC,SAASA,OAAM,KAAK,KAAK,SAAS,GAAGA,MAAK,SAAS,GAAG,IAAI;AACvH,cAAE;AAAA,UACJ;AACA,cAAI,MAAMA,MAAK,QAAQ;AACrB,iBAAK,cAAcA,MAAK,MAAM,GAAG;AAAA,UACnC;AAAA,QACF;AACA,YAAI,MAAM,GAAG;AACX,iBAAO,KAAKA,MAAK,MAAM,QAAQ,MAAMA,MAAK,SAAS,MAAMA,MAAK,MAAM,CAAC;AAAA,QACvE;AACA,eAAO;AAAA,UACLA,MAAK;AAAA,UACL,GAAG;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAQA,OAAM,KAAK;AACjB,YAAI,MAAM,GAAG;AACX,iBAAO,KAAK,YAAY,KAAK,YAAY,SAAS,GAAG;AAAA,QACvD;AACA,eAAOA,MAAK,GAAG;AAAA,MACjB;AAAA,MACA,QAAQA,OAAM,KAAK,KAAK;AACtB,eAAO,SAAS,KAAK,QAAQ,KAAK,MAAMA,KAAI,GAAG,KAAK,KAAK,SAAS,GAAG,GAAG;AAAA,MAC1E;AAAA,IACF;AACA,IAAM,uBAAN,MAA2B;AAAA,MACzB,YAAY,QAAQ,iBAAiB;AACnC,aAAK,kBAAkB;AACvB,aAAK,UAAU,IAAI,aAAa,MAAM;AAAA,MACxC;AAAA,MACA,QAAQ,OAAO,aAAa,IAAI;AAC9B,cAAM,SAAS,KAAK,gBAAgB,UAAU;AAC9C,YAAI;AACF,iBAAO,MAAM;AACX,kBAAM,SAAS,MAAM,OAAO,KAAK;AACjC,gBAAI,OAAO,MAAM;AACf;AAAA,YACF;AACA,mBAAO,KAAK,QAAQ,KAAK,OAAO,KAAK;AAAA,UACvC;AACA,gBAAM,OAAO,KAAK,QAAQ,IAAI;AAC9B,cAAI,KAAK,QAAQ;AACf,kBAAM;AAAA,UACR;AAAA,QACF,UAAE;AACA,iBAAO,YAAY;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAM,MAAM,OAAO,cAAc;AAAA;AAAA;;;ACvJjC,SAAS,wBAAwB,QAAQ;AACvC,QAAM,QAAQ,OAAO,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AACvD,MAAI,MAAM,MAAM,MAAM,aAAa;AACjC,UAAM,IAAI,MAAM,mEAAmE,KAAK,UAAU,KAAK,IAAI,GAAG;AAAA,EAChH;AACA,QAAM,MAAM,CAAC;AACb,aAAW,QAAQ,OAAO;AACxB,UAAM,KAAK,KAAK,MAAM,KAAK,CAAC;AAC5B,QAAI,GAAG,WAAW,GAAG;AACnB,YAAM,IAAI,MAAM,sEAAsE,OAAO,UAAU,SAAS,GAAG;AAAA,IACrH;AACA,UAAM,CAAC,MAAM,KAAK,IAAI;AACtB,QAAI,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,MAAM,KAAK;AACvD,UAAI,IAAI,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,QAAQ,QAAQ,GAAG;AAAA,IACpD,WAAW,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,MAAM,KAAK;AAC9D,UAAI,IAAI,IAAI;AAAA,IACd,WAAW,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,MAAM,KAAK;AACrH,YAAM,IAAI,MAAM,qEAAqE,SAAS,GAAG;AAAA,IACnG;AAAA,EACF;AACA,MAAI,CAAC,IAAI,MAAM;AACb,UAAM,IAAI,MAAM,kEAAkE,SAAS,GAAG;AAAA,EAChG;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,cAAc;AAClB,MAAI;AACJ,SAAO,QAAQ,OAAO,MAAM,MAAM,OAAO,aAAa;AACpD,UAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,UAAM,SAAS,KAAK,MAAM,GAAG,KAAK,EAAE,KAAK,EAAE,YAAY;AACvD,UAAM,QAAQ,KAAK,MAAM,QAAQ,CAAC,EAAE,KAAK;AACzC,YAAQ,QAAQ;AAAA,MAChB,KAAK;AACH,sBAAc;AACd,gBAAQ,KAAK,GAAG,OAAO,QAAQ,wBAAwB,KAAK,CAAC,CAAC;AAC9D;AAAA,MACF,KAAK;AACH,gBAAQ,KAAK;AAAA,UACX;AAAA,UACA;AAAA,QACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,8DAA8D;AAAA,EAChF;AACA,SAAO,OAAO,YAAY,OAAO;AACnC;AACA,eAAe,gBAAgB,IAAI,QAAQ;AACzC,MAAI,aAAa;AACjB,MAAI,oBAAoB;AACxB,QAAM,cAAc,CAAC,CAAC,CAAC;AACvB,QAAM,aAAa,IAAI,aAAa,IAAI;AACxC,aAAS;AACP,UAAM,SAAS,MAAM,GAAG,KAAK;AAC7B,QAAI,OAAO,MAAM;AACf,YAAM,IAAI,MAAM,yDAAyD;AAAA,IAC3E;AACA,QAAI,cAAc,OAAO,UAAU,SAAS,YAAY,OAAO,MAAM,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG;AACvF,aAAO;AAAA,QACL;AAAA,QACA,IAAI,WAAW;AAAA,MACjB;AAAA,IACF;AACA,QAAI;AACJ,QAAI,OAAO,UAAU,OAAO;AAC1B,cAAQ,OAAO;AAAA,IACjB,WAAW,CAAC,mBAAmB;AAC7B,cAAQ;AAAA,IACV,OAAO;AACL,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AACA,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AACA,QAAI,YAAY;AACd,mBAAa;AAAA,IACf;AACA,UAAM,SAAS,WAAW,KAAK,KAAK;AACpC,eAAW,CAAC,GAAG,KAAK,KAAK,OAAO,QAAQ,GAAG;AACzC,YAAM,UAAU,UAAU;AAC1B,UAAI,CAAC,WAAW,CAAC,MAAM,QAAQ;AAC7B;AAAA,MACF;AACA,UAAI,qBAAqB,SAAS;AAChC,eAAO,KAAK,WAAW,IAAI,CAAC;AAC5B,eAAO;AAAA,UACL,YAAY,OAAO,YAAU,OAAO,MAAM,EAAE,IAAI,YAAY,EAAE,IAAI,aAAa;AAAA,UAC/E,YAAY,GAAG,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI,CAAAC,WAASA,WAAU,QAAQ,OAAOA,MAAK,CAAC;AAAA,QACjF;AAAA,MACF;AACA,UAAI,oBAAoB,SAAS;AAC/B,oBAAY,KAAK,CAAC,CAAC;AAAA,MACrB,OAAO;AACL,oBAAY,YAAY,SAAS,CAAC,EAAE,KAAK,KAAK;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACF;AACA,gBAAuB,gBAAgB,MAAM,UAAU;AACrD,QAAM,SAAS,YAAY,MAAM,cAAc,QAAQ,CAAC;AACxD,QAAM,KAAK,IAAI,qBAAqB,QAAQ,IAAI,EAAE,OAAO,aAAa,EAAE;AACxE,aAAS;AACP,UAAM,SAAS,MAAM,GAAG,KAAK;AAC7B,QAAI,OAAO,MAAM;AACf;AAAA,IACF;AACA,QAAI,OAAO,UAAU,OAAO;AAC1B;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,IAAI,aAAa,IAAI;AACxC,aAAS;AAaP,QAAS,YAAT,SAAmB,OAAO;AACxB,YAAM,SAAS,CAAC;AAChB,iBAAW,SAAS,WAAW,KAAK,KAAK,GAAG;AAC1C,YAAI,cAAc;AAChB,iBAAO,KAAK,IAAI;AAAA,QAClB;AACA,YAAI,EAAE,eAAe,UAAU,QAAQ;AACrC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF;AACA,aAAO,YAAY,GAAG,MAAM;AAAA,IAC9B;AAvBA,UAAM,CAAC,aAAa,IAAI,IAAI,MAAM,gBAAgB,IAAI,MAAM;AAC5D,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,mBAAe,YAAY;AACzB,YAAM,SAAS,MAAM,GAAG,KAAK;AAC7B,UAAI,OAAO,MAAM;AACf,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AACA,aAAO;AAAA,IACT;AACA,QAAI,eAAe;AAanB,QAAI,OAAO;AACX,mBAAe,YAAY;AACzB,YAAM,SAAS,MAAM,UAAU;AAC/B,UAAI;AACJ,UAAI,OAAO,UAAU,OAAO;AAC1B,gBAAQ,OAAO;AAAA,MACjB,WAAW,CAAC,cAAc;AACxB,gBAAQ;AAAA,MACV,OAAO;AACL,eAAO;AACP,eAAO,EAAE,OAAO,WAAW,IAAI,EAAE;AAAA,MACnC;AACA,aAAO,EAAE,OAAO,UAAU,KAAK,EAAE;AAAA,IACnC;AACA,UAAM,iBAAiB,CAAC,EAAE,OAAO,UAAU,IAAI,EAAE,CAAC;AAClD,UAAM;AAAA,MACJ,GAAG,iBAAiB,WAAW;AAAA,MAC/B,MAAM;AAAA,QACJ,CAAC,OAAO,aAAa,IAAI;AACvB,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,OAAO;AACX,qBAAS;AACP,kBAAM,SAAS,eAAe,MAAM;AACpC,gBAAI,CAAC,QAAQ;AACX;AAAA,YACF;AACA,gBAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,qBAAS;AACP,gBAAI,MAAM;AACR,qBAAO;AAAA,gBACL;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF;AACA,kBAAM,SAAS,MAAM,UAAU;AAC/B,gBAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,CAAC,MAAM;AACZ,qBAAe,KAAK,MAAM,UAAU,CAAC;AAAA,IACvC;AAAA,EACF;AACF;AA9MA,IAWM,cACA,MACA;AAbN;AAAA;AAAA;AAKA;AAMA,IAAM,eAAe,SAAS,UAAU,MAAM,KAAK,aAAa,MAAS;AACzE,IAAM,OAAO,cAAc,IAAI;AAC/B,IAAM,OAAO,cAAc,MAAM;AAAA;AAAA;;;ACAjC,SAAS,yBAAyB,UAAU;AAC1C,SAAO,OAAM,SAAQ;AACnB,aAAS,WAAW,UAAU;AAC5B,UAAI,QAAQ,MAAM,QAAQ,IAAI;AAC9B,UAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAQA,eAAe,uBAAuB,SAAS,eAAe;AAC5D,MAAI,cAAc,QAAQ,QAAQ,IAAI,cAAc,KAAK;AACzD,MAAI,CAAC,MAAM,QAAQ,IAAI,YAAY,MAAM,kBAAkB;AAC3D,MAAI,CAAC,QAAQ,QAAQ,CAAC,YAAY,SAAS,uBAAuB;AAChE,UAAM,IAAI,UAAU,sCAAsC;AAAA,EAC5D;AACA,MAAI,WAAW,IAAI,SAAS;AAC5B,MAAI,QAAQ,gBAAgB,QAAQ,MAAM,QAAQ;AAClD,iBAAe,QAAQ,OAAO;AAC5B,QAAI,KAAK,KAAM;AACf,QAAI,OAAO,KAAK,aAAa,UAAU;AAGrC,WAAK,WAAW,KAAK,SAAS,MAAM,OAAO,EAAE,IAAI;AAAA,IACnD;AACA,QAAI,QAAQ,MAAM,cAAc,IAAI;AACpC,QAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,eAAS,OAAO,KAAK,MAAM,KAAK;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AACT;AApDA;AAAA;AAUA;AAAA;AAAA;;;;;;;;;;;;;;;;;ACiegB,SAAAC,UAAUC,OAAYC,SAAgB;AACpD,MAAID,UAAU,SAASA,UAAU,QAAQ,OAAOA,UAAU,aAAa;AACrE,UAAM,IAAIE,MAAMD,OAAO;EACxB;AACH;AAEgB,SAAAE,QAAQC,MAAWH,SAAe;AAChD,MAAI,CAACG,MAAM;AAET,QAAI,OAAOC,YAAY,YAAaA,SAAQC,KAAKL,OAAO;AAExD,QAAI;AAMF,YAAM,IAAIC,MAAMD,OAAO;IAExB,SAAQM,GAAG;IAAA;EACb;AACH;AAEA,SAASC,YAAS;AAChB,SAAOC,KAAKC,OAAM,EAAGC,SAAS,EAAE,EAAEC,OAAO,GAAG,CAAC;AAC/C;AAgBM,SAAUC,eACdC,SACAC,IACAC,OACAC,KAAY;AAAA,MADZD,UAAA,QAAA;AAAAA,YAAa;EAAI;AAGjB,MAAIE,WAAQC,SAAA;IACVC,UAAU,OAAON,YAAY,WAAWA,UAAUA,QAAQM;IAC1DC,QAAQ;IACRC,MAAM;KACF,OAAOP,OAAO,WAAWQ,UAAUR,EAAE,IAAIA,IAAE;IAC/CC;;;;;IAKAC,KAAMF,MAAOA,GAAgBE,OAAQA,OAAOT,UAAS;GACtD;AACD,SAAOU;AACT;AAKgB,SAAAM,WAAUC,MAIV;AAAA,MAJW;IACzBL,WAAW;IACXC,SAAS;IACTC,OAAO;EACO,IAAAG;AACd,MAAIJ,UAAUA,WAAW,IACvBD,aAAYC,OAAOK,OAAO,CAAC,MAAM,MAAML,SAAS,MAAMA;AACxD,MAAIC,QAAQA,SAAS,IACnBF,aAAYE,KAAKI,OAAO,CAAC,MAAM,MAAMJ,OAAO,MAAMA;AACpD,SAAOF;AACT;AAKM,SAAUG,UAAUI,MAAY;AACpC,MAAIC,aAA4B,CAAA;AAEhC,MAAID,MAAM;AACR,QAAIE,YAAYF,KAAKG,QAAQ,GAAG;AAChC,QAAID,aAAa,GAAG;AAClBD,iBAAWN,OAAOK,KAAKf,OAAOiB,SAAS;AACvCF,aAAOA,KAAKf,OAAO,GAAGiB,SAAS;IAChC;AAED,QAAIE,cAAcJ,KAAKG,QAAQ,GAAG;AAClC,QAAIC,eAAe,GAAG;AACpBH,iBAAWP,SAASM,KAAKf,OAAOmB,WAAW;AAC3CJ,aAAOA,KAAKf,OAAO,GAAGmB,WAAW;IAClC;AAED,QAAIJ,MAAM;AACRC,iBAAWR,WAAWO;IACvB;EACF;AAED,SAAOC;AACT;AC5IA,SAASI,aACPC,OAA0B;AAE1B,SAAOA,MAAMC,UAAU;AACzB;AAIM,SAAUC,0BACdC,QACAC,oBACAC,YACAC,UAA4B;AAAA,MAD5BD,eAAuB,QAAA;AAAvBA,iBAAuB,CAAA;EAAE;AAAA,MACzBC,aAAA,QAAA;AAAAA,eAA0B,CAAA;EAAE;AAE5B,SAAOH,OAAOI,IAAI,CAACP,OAAOC,UAAS;AACjC,QAAIO,WAAW,CAAC,GAAGH,YAAYI,OAAOR,KAAK,CAAC;AAC5C,QAAIS,KAAK,OAAOV,MAAMU,OAAO,WAAWV,MAAMU,KAAKF,SAASG,KAAK,GAAG;AACpE7C,cACEkC,MAAMC,UAAU,QAAQ,CAACD,MAAMY,UAAQ,2CACI;AAE7C9C,cACE,CAACwC,SAASI,EAAE,GACZ,uCAAqCA,KACnC,kEAAwD;AAG5D,QAAIX,aAAaC,KAAK,GAAG;AACvB,UAAIa,aAAU3B,SAAA,CAAA,GACTc,OACAI,mBAAmBJ,KAAK,GAAC;QAC5BU;OACD;AACDJ,eAASI,EAAE,IAAIG;AACf,aAAOA;IACR,OAAM;AACL,UAAIC,oBAAiB5B,SAAA,CAAA,GAChBc,OACAI,mBAAmBJ,KAAK,GAAC;QAC5BU;QACAE,UAAUG;OACX;AACDT,eAASI,EAAE,IAAII;AAEf,UAAId,MAAMY,UAAU;AAClBE,0BAAkBF,WAAWV,0BAC3BF,MAAMY,UACNR,oBACAI,UACAF,QAAQ;MAEX;AAED,aAAOQ;IACR;EACH,CAAC;AACH;AAOM,SAAUE,YAGdb,QACAc,aACAC,UAAc;AAAA,MAAdA,aAAQ,QAAA;AAARA,eAAW;EAAG;AAEd,SAAOC,gBAAgBhB,QAAQc,aAAaC,UAAU,KAAK;AAC7D;AAEM,SAAUC,gBAGdhB,QACAc,aACAC,UACAE,cAAqB;AAErB,MAAInC,WACF,OAAOgC,gBAAgB,WAAW3B,UAAU2B,WAAW,IAAIA;AAE7D,MAAI9B,WAAWkC,cAAcpC,SAASE,YAAY,KAAK+B,QAAQ;AAE/D,MAAI/B,YAAY,MAAM;AACpB,WAAO;EACR;AAED,MAAImC,WAAWC,cAAcpB,MAAM;AACnCqB,oBAAkBF,QAAQ;AAE1B,MAAIG,UAAU;AACd,WAASC,IAAI,GAAGD,WAAW,QAAQC,IAAIJ,SAASK,QAAQ,EAAED,GAAG;AAO3D,QAAIE,UAAUC,WAAW1C,QAAQ;AACjCsC,cAAUK,iBACRR,SAASI,CAAC,GACVE,SACAR,YAAY;EAEf;AAED,SAAOK;AACT;AAyCA,SAASF,cAGPpB,QACAmB,UACAS,aACA1B,YAAe;AAAA,MAFfiB,aAA2C,QAAA;AAA3CA,eAA2C,CAAA;EAAE;AAAA,MAC7CS,gBAAA,QAAA;AAAAA,kBAA4C,CAAA;EAAE;AAAA,MAC9C1B,eAAU,QAAA;AAAVA,iBAAa;EAAE;AAEf,MAAI2B,eAAeA,CACjBhC,OACAC,OACAgC,iBACE;AACF,QAAIC,OAAmC;MACrCD,cACEA,iBAAiBlB,SAAYf,MAAMN,QAAQ,KAAKuC;MAClDE,eAAenC,MAAMmC,kBAAkB;MACvCC,eAAenC;MACfD;;AAGF,QAAIkC,KAAKD,aAAaI,WAAW,GAAG,GAAG;AACrCvE,gBACEoE,KAAKD,aAAaI,WAAWhC,UAAU,GACvC,0BAAwB6B,KAAKD,eAAY,0BAAA,MACnC5B,aAAU,mDAA+C,6DACA;AAGjE6B,WAAKD,eAAeC,KAAKD,aAAaK,MAAMjC,WAAWsB,MAAM;IAC9D;AAED,QAAIjC,OAAO6C,UAAU,CAAClC,YAAY6B,KAAKD,YAAY,CAAC;AACpD,QAAIO,aAAaT,YAAYU,OAAOP,IAAI;AAKxC,QAAIlC,MAAMY,YAAYZ,MAAMY,SAASe,SAAS,GAAG;AAC/C7D;;;QAGEkC,MAAMC,UAAU;QAChB,6DACuCP,uCAAAA,OAAI;MAAI;AAEjD6B,oBAAcvB,MAAMY,UAAUU,UAAUkB,YAAY9C,IAAI;IACzD;AAID,QAAIM,MAAMN,QAAQ,QAAQ,CAACM,MAAMC,OAAO;AACtC;IACD;AAEDqB,aAASoB,KAAK;MACZhD;MACAiD,OAAOC,aAAalD,MAAMM,MAAMC,KAAK;MACrCuC;IACD,CAAA;;AAEHrC,SAAO0C,QAAQ,CAAC7C,OAAOC,UAAS;AAAA,QAAA6C;AAE9B,QAAI9C,MAAMN,SAAS,MAAM,GAAAoD,cAAC9C,MAAMN,SAAI,QAAVoD,YAAYC,SAAS,GAAG,IAAG;AACnDf,mBAAahC,OAAOC,KAAK;IAC1B,OAAM;AACL,eAAS+C,YAAYC,wBAAwBjD,MAAMN,IAAI,GAAG;AACxDsC,qBAAahC,OAAOC,OAAO+C,QAAQ;MACpC;IACF;EACH,CAAC;AAED,SAAO1B;AACT;AAgBA,SAAS2B,wBAAwBvD,MAAY;AAC3C,MAAIwD,WAAWxD,KAAKyD,MAAM,GAAG;AAC7B,MAAID,SAASvB,WAAW,EAAG,QAAO,CAAA;AAElC,MAAI,CAACyB,OAAO,GAAGC,IAAI,IAAIH;AAGvB,MAAII,aAAaF,MAAMG,SAAS,GAAG;AAEnC,MAAIC,WAAWJ,MAAMK,QAAQ,OAAO,EAAE;AAEtC,MAAIJ,KAAK1B,WAAW,GAAG;AAGrB,WAAO2B,aAAa,CAACE,UAAU,EAAE,IAAI,CAACA,QAAQ;EAC/C;AAED,MAAIE,eAAeT,wBAAwBI,KAAK1C,KAAK,GAAG,CAAC;AAEzD,MAAIgD,SAAmB,CAAA;AASvBA,SAAOjB,KACL,GAAGgB,aAAanD,IAAKqD,aACnBA,YAAY,KAAKJ,WAAW,CAACA,UAAUI,OAAO,EAAEjD,KAAK,GAAG,CAAC,CAC1D;AAIH,MAAI2C,YAAY;AACdK,WAAOjB,KAAK,GAAGgB,YAAY;EAC5B;AAGD,SAAOC,OAAOpD,IAAKyC,cACjBtD,KAAK2C,WAAW,GAAG,KAAKW,aAAa,KAAK,MAAMA,QAAQ;AAE5D;AAEA,SAASxB,kBAAkBF,UAAuB;AAChDA,WAASuC,KAAK,CAACC,GAAGC,MAChBD,EAAEnB,UAAUoB,EAAEpB,QACVoB,EAAEpB,QAAQmB,EAAEnB,QACZqB,eACEF,EAAEtB,WAAWjC,IAAK2B,UAASA,KAAKE,aAAa,GAC7C2B,EAAEvB,WAAWjC,IAAK2B,UAASA,KAAKE,aAAa,CAAC,CAC/C;AAET;AAUA,SAASQ,aAAalD,MAAcO,OAA0B;AAC5D,MAAIiD,WAAWxD,KAAKyD,MAAM,GAAG;AAC7B,MAAIc,eAAef,SAASvB;AAC5B,MAAIuB,SAASgB,KAAKC,OAAO,GAAG;AAC1BF,oBAAgBG;EACjB;AAED,MAAInE,OAAO;AACTgE,oBAAgBI;EACjB;AAED,SAAOnB,SACJoB,OAAQC,OAAM,CAACJ,QAAQI,CAAC,CAAC,EACzBC,OACC,CAAC7B,OAAO8B,YACN9B,SACC+B,QAAQC,KAAKF,OAAO,IACjBG,sBACAH,YAAY,KACZI,oBACAC,qBACNb,YAAY;AAElB;AAEA,SAASD,eAAeF,GAAaC,GAAW;AAC9C,MAAIgB,WACFjB,EAAEnC,WAAWoC,EAAEpC,UAAUmC,EAAExB,MAAM,GAAG,EAAE,EAAE0C,MAAM,CAACC,GAAGvD,MAAMuD,MAAMlB,EAAErC,CAAC,CAAC;AAEpE,SAAOqD;;;;;IAKHjB,EAAEA,EAAEnC,SAAS,CAAC,IAAIoC,EAAEA,EAAEpC,SAAS,CAAC;;;;IAGhC;;AACN;AAEA,SAASG,iBAIPoD,QACA/F,UACAiC,cAAoB;AAAA,MAApBA,iBAAY,QAAA;AAAZA,mBAAe;EAAK;AAEpB,MAAI;IAAEoB;EAAY,IAAG0C;AAErB,MAAIC,gBAAgB,CAAA;AACpB,MAAIC,kBAAkB;AACtB,MAAI3D,UAA2D,CAAA;AAC/D,WAASC,IAAI,GAAGA,IAAIc,WAAWb,QAAQ,EAAED,GAAG;AAC1C,QAAIQ,OAAOM,WAAWd,CAAC;AACvB,QAAI2D,MAAM3D,MAAMc,WAAWb,SAAS;AACpC,QAAI2D,oBACFF,oBAAoB,MAChBjG,WACAA,SAASmD,MAAM8C,gBAAgBzD,MAAM,KAAK;AAChD,QAAI4D,QAAQC,UACV;MAAE9F,MAAMwC,KAAKD;MAAcE,eAAeD,KAAKC;MAAekD;OAC9DC,iBAAiB;AAGnB,QAAItF,QAAQkC,KAAKlC;AAEjB,QACE,CAACuF,SACDF,OACAjE,gBACA,CAACoB,WAAWA,WAAWb,SAAS,CAAC,EAAE3B,MAAMC,OACzC;AACAsF,cAAQC,UACN;QACE9F,MAAMwC,KAAKD;QACXE,eAAeD,KAAKC;QACpBkD,KAAK;SAEPC,iBAAiB;IAEpB;AAED,QAAI,CAACC,OAAO;AACV,aAAO;IACR;AAEDE,WAAOC,OAAOP,eAAeI,MAAMI,MAAM;AAEzClE,YAAQiB,KAAK;;MAEXiD,QAAQR;MACRhG,UAAUoD,UAAU,CAAC6C,iBAAiBG,MAAMpG,QAAQ,CAAC;MACrDyG,cAAcC,kBACZtD,UAAU,CAAC6C,iBAAiBG,MAAMK,YAAY,CAAC,CAAC;MAElD5F;IACD,CAAA;AAED,QAAIuF,MAAMK,iBAAiB,KAAK;AAC9BR,wBAAkB7C,UAAU,CAAC6C,iBAAiBG,MAAMK,YAAY,CAAC;IAClE;EACF;AAED,SAAOnE;AACT;AAiHgB,SAAA+D,UAIdM,SACA3G,UAAgB;AAEhB,MAAI,OAAO2G,YAAY,UAAU;AAC/BA,cAAU;MAAEpG,MAAMoG;MAAS3D,eAAe;MAAOkD,KAAK;;EACvD;AAED,MAAI,CAACU,SAASC,cAAc,IAAIC,YAC9BH,QAAQpG,MACRoG,QAAQ3D,eACR2D,QAAQT,GAAG;AAGb,MAAIE,QAAQpG,SAASoG,MAAMQ,OAAO;AAClC,MAAI,CAACR,MAAO,QAAO;AAEnB,MAAIH,kBAAkBG,MAAM,CAAC;AAC7B,MAAIK,eAAeR,gBAAgB3B,QAAQ,WAAW,IAAI;AAC1D,MAAIyC,gBAAgBX,MAAMjD,MAAM,CAAC;AACjC,MAAIqD,SAAiBK,eAAexB,OAClC,CAAC2B,MAAI3G,MAA6BS,UAAS;AAAA,QAApC;MAAEmG;MAAW9C;QAAY9D;AAG9B,QAAI4G,cAAc,KAAK;AACrB,UAAIC,aAAaH,cAAcjG,KAAK,KAAK;AACzC2F,qBAAeR,gBACZ9C,MAAM,GAAG8C,gBAAgBzD,SAAS0E,WAAW1E,MAAM,EACnD8B,QAAQ,WAAW,IAAI;IAC3B;AAED,UAAM1F,QAAQmI,cAAcjG,KAAK;AACjC,QAAIqD,cAAc,CAACvF,OAAO;AACxBoI,WAAKC,SAAS,IAAIrF;IACnB,OAAM;AACLoF,WAAKC,SAAS,KAAKrI,SAAS,IAAI0F,QAAQ,QAAQ,GAAG;IACpD;AACD,WAAO0C;KAET,CAAA,CAAE;AAGJ,SAAO;IACLR;IACAxG,UAAUiG;IACVQ;IACAE;;AAEJ;AAIA,SAASG,YACPvG,MACAyC,eACAkD,KAAU;AAAA,MADVlD,kBAAa,QAAA;AAAbA,oBAAgB;EAAK;AAAA,MACrBkD,QAAG,QAAA;AAAHA,UAAM;EAAI;AAEVnH,UACEwB,SAAS,OAAO,CAACA,KAAK6D,SAAS,GAAG,KAAK7D,KAAK6D,SAAS,IAAI,GACzD,iBAAe7D,OACTA,sCAAAA,MAAAA,KAAK+D,QAAQ,OAAO,IAAI,IAAsC,uCAAA,sEACE,sCAChC/D,KAAK+D,QAAQ,OAAO,IAAI,IAAC,KAAI;AAGrE,MAAIkC,SAA8B,CAAA;AAClC,MAAIW,eACF,MACA5G,KACG+D,QAAQ,WAAW,EAAE,EACrBA,QAAQ,QAAQ,GAAG,EACnBA,QAAQ,sBAAsB,MAAM,EACpCA,QACC,qBACA,CAAC8C,GAAWH,WAAmB9C,eAAc;AAC3CqC,WAAOjD,KAAK;MAAE0D;MAAW9C,YAAYA,cAAc;IAAI,CAAE;AACzD,WAAOA,aAAa,iBAAiB;EACvC,CAAC;AAGP,MAAI5D,KAAK6D,SAAS,GAAG,GAAG;AACtBoC,WAAOjD,KAAK;MAAE0D,WAAW;IAAK,CAAA;AAC9BE,oBACE5G,SAAS,OAAOA,SAAS,OACrB,UACA;aACG2F,KAAK;AAEdiB,oBAAgB;aACP5G,SAAS,MAAMA,SAAS,KAAK;AAQtC4G,oBAAgB;EACjB,MAAM;AAIP,MAAIP,UAAU,IAAIS,OAAOF,cAAcnE,gBAAgBpB,SAAY,GAAG;AAEtE,SAAO,CAACgF,SAASJ,MAAM;AACzB;AAEM,SAAU9D,WAAW9D,OAAa;AACtC,MAAI;AACF,WAAOA,MACJoF,MAAM,GAAG,EACT5C,IAAKkG,OAAMC,mBAAmBD,CAAC,EAAEhD,QAAQ,OAAO,KAAK,CAAC,EACtD9C,KAAK,GAAG;WACJgG,OAAO;AACdzI,YACE,OACA,mBAAiBH,QACgD,6GAAA,eAClD4I,QAAK,KAAI;AAG1B,WAAO5I;EACR;AACH;AAKgB,SAAAsD,cACdlC,UACA+B,UAAgB;AAEhB,MAAIA,aAAa,IAAK,QAAO/B;AAE7B,MAAI,CAACA,SAASyH,YAAW,EAAGvE,WAAWnB,SAAS0F,YAAW,CAAE,GAAG;AAC9D,WAAO;EACR;AAID,MAAIC,aAAa3F,SAASqC,SAAS,GAAG,IAClCrC,SAASS,SAAS,IAClBT,SAASS;AACb,MAAImF,WAAW3H,SAASM,OAAOoH,UAAU;AACzC,MAAIC,YAAYA,aAAa,KAAK;AAEhC,WAAO;EACR;AAED,SAAO3H,SAASmD,MAAMuE,UAAU,KAAK;AACvC;SAOgBE,YAAYjI,IAAQkI,cAAkB;AAAA,MAAlBA,iBAAY,QAAA;AAAZA,mBAAe;EAAG;AACpD,MAAI;IACF7H,UAAU8H;IACV7H,SAAS;IACTC,OAAO;MACL,OAAOP,OAAO,WAAWQ,UAAUR,EAAE,IAAIA;AAE7C,MAAIK,WAAW8H,aACXA,WAAW5E,WAAW,GAAG,IACvB4E,aACAC,gBAAgBD,YAAYD,YAAY,IAC1CA;AAEJ,SAAO;IACL7H;IACAC,QAAQ+H,gBAAgB/H,MAAM;IAC9BC,MAAM+H,cAAc/H,IAAI;;AAE5B;AAEA,SAAS6H,gBAAgBjF,cAAsB+E,cAAoB;AACjE,MAAI9D,WAAW8D,aAAavD,QAAQ,QAAQ,EAAE,EAAEN,MAAM,GAAG;AACzD,MAAIkE,mBAAmBpF,aAAakB,MAAM,GAAG;AAE7CkE,mBAAiBxE,QAAS4B,aAAW;AACnC,QAAIA,YAAY,MAAM;AAEpB,UAAIvB,SAASvB,SAAS,EAAGuB,UAASoE,IAAG;IACtC,WAAU7C,YAAY,KAAK;AAC1BvB,eAASR,KAAK+B,OAAO;IACtB;EACH,CAAC;AAED,SAAOvB,SAASvB,SAAS,IAAIuB,SAASvC,KAAK,GAAG,IAAI;AACpD;AAEA,SAAS4G,oBACPC,MACAC,OACAC,MACAhI,MAAmB;AAEnB,SACE,uBAAqB8H,OACbC,0CAAAA,SAAAA,QAAK,cAAaE,KAAKC,UAC7BlI,IAAI,IACL,yCACOgI,SAAAA,OAAI,8DACuD;AAEvE;AAyBM,SAAUG,2BAEdpG,SAAY;AACZ,SAAOA,QAAQ6C,OACb,CAACiB,OAAOtF,UACNA,UAAU,KAAMsF,MAAMvF,MAAMN,QAAQ6F,MAAMvF,MAAMN,KAAKiC,SAAS,CAAE;AAEtE;AAIgB,SAAAmG,oBAEdrG,SAAcsG,sBAA6B;AAC3C,MAAIC,cAAcH,2BAA2BpG,OAAO;AAKpD,MAAIsG,sBAAsB;AACxB,WAAOC,YAAYzH,IAAI,CAACgF,OAAO0C,QAC7BA,QAAQD,YAAYrG,SAAS,IAAI4D,MAAMpG,WAAWoG,MAAMK,YAAY;EAEvE;AAED,SAAOoC,YAAYzH,IAAKgF,WAAUA,MAAMK,YAAY;AACtD;AAKM,SAAUsC,UACdC,OACAC,gBACAC,kBACAC,gBAAsB;AAAA,MAAtBA,mBAAc,QAAA;AAAdA,qBAAiB;EAAK;AAEtB,MAAIxJ;AACJ,MAAI,OAAOqJ,UAAU,UAAU;AAC7BrJ,SAAKQ,UAAU6I,KAAK;EACrB,OAAM;AACLrJ,SAAEI,SAAQiJ,CAAAA,GAAAA,KAAK;AAEfrK,cACE,CAACgB,GAAGK,YAAY,CAACL,GAAGK,SAAS4D,SAAS,GAAG,GACzCwE,oBAAoB,KAAK,YAAY,UAAUzI,EAAE,CAAC;AAEpDhB,cACE,CAACgB,GAAGK,YAAY,CAACL,GAAGK,SAAS4D,SAAS,GAAG,GACzCwE,oBAAoB,KAAK,YAAY,QAAQzI,EAAE,CAAC;AAElDhB,cACE,CAACgB,GAAGM,UAAU,CAACN,GAAGM,OAAO2D,SAAS,GAAG,GACrCwE,oBAAoB,KAAK,UAAU,QAAQzI,EAAE,CAAC;EAEjD;AAED,MAAIyJ,cAAcJ,UAAU,MAAMrJ,GAAGK,aAAa;AAClD,MAAI8H,aAAasB,cAAc,MAAMzJ,GAAGK;AAExC,MAAIqJ;AAWJ,MAAIvB,cAAc,MAAM;AACtBuB,WAAOH;EACR,OAAM;AACL,QAAII,qBAAqBL,eAAezG,SAAS;AAMjD,QAAI,CAAC2G,kBAAkBrB,WAAW5E,WAAW,IAAI,GAAG;AAClD,UAAIqG,aAAazB,WAAW9D,MAAM,GAAG;AAErC,aAAOuF,WAAW,CAAC,MAAM,MAAM;AAC7BA,mBAAWC,MAAK;AAChBF,8BAAsB;MACvB;AAED3J,SAAGK,WAAWuJ,WAAW/H,KAAK,GAAG;IAClC;AAED6H,WAAOC,sBAAsB,IAAIL,eAAeK,kBAAkB,IAAI;EACvE;AAED,MAAI/I,OAAOqH,YAAYjI,IAAI0J,IAAI;AAG/B,MAAII,2BACF3B,cAAcA,eAAe,OAAOA,WAAW1D,SAAS,GAAG;AAE7D,MAAIsF,2BACDN,eAAetB,eAAe,QAAQoB,iBAAiB9E,SAAS,GAAG;AACtE,MACE,CAAC7D,KAAKP,SAASoE,SAAS,GAAG,MAC1BqF,4BAA4BC,0BAC7B;AACAnJ,SAAKP,YAAY;EAClB;AAED,SAAOO;AACT;AAmFgB,SAAAoJ,KAAQA,OAASC,MAA4B;AAC3D,SAAO,IAAIC,qBACTF,OACA,OAAOC,SAAS,WAAW;IAAEE,QAAQF;MAASA,IAAI;AAEtD;AAqLA,SAASG,iBAAiBnL,OAAU;AAClC,SACEA,iBAAiBoL,WAAYpL,MAAyBqL,aAAa;AAEvE;AAEA,SAASC,qBAAqBtL,OAAU;AACtC,MAAI,CAACmL,iBAAiBnL,KAAK,GAAG;AAC5B,WAAOA;EACR;AAED,MAAIA,MAAMuL,QAAQ;AAChB,UAAMvL,MAAMuL;EACb;AACD,SAAOvL,MAAMwL;AACf;AA6GM,SAAUC,qBAAqB7C,OAAU;AAC7C,SACEA,SAAS,QACT,OAAOA,MAAMsC,WAAW,YACxB,OAAOtC,MAAM8C,eAAe,YAC5B,OAAO9C,MAAM+C,aAAa,aAC1B,UAAU/C;AAEd;AC4sDgB,SAAAgD,oBACdxJ,QACAyJ,MAAiC;AAEjC9L,YACEqC,OAAOwB,SAAS,GAChB,kEAAkE;AAGpE,MAAIrB,WAA0B,CAAA;AAC9B,MAAIY,YAAY0I,OAAOA,KAAK1I,WAAW,SAAS;AAChD,MAAId;AACJ,MAAIwJ,QAAI,QAAJA,KAAMxJ,oBAAoB;AAC5BA,yBAAqBwJ,KAAKxJ;EAC3B,WAAUwJ,QAAI,QAAJA,KAAMC,qBAAqB;AAEpC,QAAIA,sBAAsBD,KAAKC;AAC/BzJ,yBAAsBJ,YAAW;MAC/B8J,kBAAkBD,oBAAoB7J,KAAK;IAC5C;EACF,OAAM;AACLI,yBAAqB2J;EACtB;AAED,MAAIC,SAAM9K,SAAA;IACR6I,sBAAsB;IACtBkC,qBAAqB;EAAK,GACtBL,OAAOA,KAAKI,SAAS,IAAI;AAG/B,MAAIE,aAAahK,0BACfC,QACAC,oBACAW,QACAT,QAAQ;AA6BV,iBAAe6J,MACbC,SAAgBC,QASV;AAAA,QARN;MACEC;MACAC;MACAC;IAAY,IAAAH,WAAA,SAKV,CAAA,IAAEA;AAEN,QAAII,MAAM,IAAIC,IAAIN,QAAQK,GAAG;AAC7B,QAAIE,SAASP,QAAQO;AACrB,QAAI1L,WAAWL,eAAe,IAAIW,WAAWkL,GAAG,GAAG,MAAM,SAAS;AAClE,QAAIhJ,UAAUT,YAAYkJ,YAAYjL,UAAUiC,QAAQ;AAGxD,QAAI,CAAC0J,cAAcD,MAAM,KAAKA,WAAW,QAAQ;AAC/C,UAAIhE,QAAQkE,uBAAuB,KAAK;QAAEF;MAAQ,CAAA;AAClD,UAAI;QAAElJ,SAASqJ;QAAyB9K;MAAO,IAC7C+K,uBAAuBb,UAAU;AACnC,aAAO;QACLhJ;QACAjC;QACAwC,SAASqJ;QACTE,YAAY,CAAA;QACZC,YAAY;QACZC,QAAQ;UACN,CAAClL,MAAMU,EAAE,GAAGiG;;QAEdwE,YAAYxE,MAAMsC;QAClBmC,eAAe,CAAA;QACfC,eAAe,CAAA;QACfC,iBAAiB;;IAEpB,WAAU,CAAC7J,SAAS;AACnB,UAAIkF,QAAQkE,uBAAuB,KAAK;QAAE1L,UAAUF,SAASE;MAAQ,CAAE;AACvE,UAAI;QAAEsC,SAAS8J;QAAiBvL;MAAO,IACrC+K,uBAAuBb,UAAU;AACnC,aAAO;QACLhJ;QACAjC;QACAwC,SAAS8J;QACTP,YAAY,CAAA;QACZC,YAAY;QACZC,QAAQ;UACN,CAAClL,MAAMU,EAAE,GAAGiG;;QAEdwE,YAAYxE,MAAMsC;QAClBmC,eAAe,CAAA;QACfC,eAAe,CAAA;QACfC,iBAAiB;;IAEpB;AAED,QAAI3H,SAAS,MAAM6H,UACjBpB,SACAnL,UACAwC,SACA6I,gBACAE,gBAAgB,MAChBD,4BAA4B,MAC5B,IAAI;AAEN,QAAIkB,WAAW9H,MAAM,GAAG;AACtB,aAAOA;IACR;AAKD,WAAAzE,SAAA;MAASD;MAAUiC;IAAQ,GAAKyC,MAAM;EACxC;AA4BA,iBAAe+H,WACbtB,SAAgBuB,QASV;AAAA,QARN;MACEC;MACAtB;MACAE;IAAY,IAAAmB,WAAA,SAKV,CAAA,IAAEA;AAEN,QAAIlB,MAAM,IAAIC,IAAIN,QAAQK,GAAG;AAC7B,QAAIE,SAASP,QAAQO;AACrB,QAAI1L,WAAWL,eAAe,IAAIW,WAAWkL,GAAG,GAAG,MAAM,SAAS;AAClE,QAAIhJ,UAAUT,YAAYkJ,YAAYjL,UAAUiC,QAAQ;AAGxD,QAAI,CAAC0J,cAAcD,MAAM,KAAKA,WAAW,UAAUA,WAAW,WAAW;AACvE,YAAME,uBAAuB,KAAK;QAAEF;MAAM,CAAE;IAC7C,WAAU,CAAClJ,SAAS;AACnB,YAAMoJ,uBAAuB,KAAK;QAAE1L,UAAUF,SAASE;MAAU,CAAA;IAClE;AAED,QAAIoG,QAAQqG,UACRnK,QAAQoK,KAAMC,OAAMA,EAAE9L,MAAMU,OAAOkL,OAAO,IAC1CG,eAAetK,SAASxC,QAAQ;AAEpC,QAAI2M,WAAW,CAACrG,OAAO;AACrB,YAAMsF,uBAAuB,KAAK;QAChC1L,UAAUF,SAASE;QACnByM;MACD,CAAA;IACF,WAAU,CAACrG,OAAO;AAEjB,YAAMsF,uBAAuB,KAAK;QAAE1L,UAAUF,SAASE;MAAU,CAAA;IAClE;AAED,QAAIwE,SAAS,MAAM6H,UACjBpB,SACAnL,UACAwC,SACA6I,gBACAE,gBAAgB,MAChB,OACAjF,KAAK;AAGP,QAAIkG,WAAW9H,MAAM,GAAG;AACtB,aAAOA;IACR;AAED,QAAIgD,QAAQhD,OAAOuH,SAASzF,OAAOuG,OAAOrI,OAAOuH,MAAM,EAAE,CAAC,IAAInK;AAC9D,QAAI4F,UAAU5F,QAAW;AAKvB,YAAM4F;IACP;AAGD,QAAIhD,OAAOsH,YAAY;AACrB,aAAOxF,OAAOuG,OAAOrI,OAAOsH,UAAU,EAAE,CAAC;IAC1C;AAED,QAAItH,OAAOqH,YAAY;AAAA,UAAAiB;AACrB,UAAInD,QAAOrD,OAAOuG,OAAOrI,OAAOqH,UAAU,EAAE,CAAC;AAC7C,WAAAiB,wBAAItI,OAAO2H,oBAAPW,QAAAA,sBAAyB1G,MAAMvF,MAAMU,EAAE,GAAG;AAC5CoI,QAAAA,MAAKoD,sBAAsB,IAAIvI,OAAO2H,gBAAgB/F,MAAMvF,MAAMU,EAAE;MACrE;AACD,aAAOoI;IACR;AAED,WAAO/H;EACT;AAEA,iBAAeyK,UACbpB,SACAnL,UACAwC,SACA6I,gBACAE,cACAD,yBACA4B,YAAyC;AAEzCrO,cACEsM,QAAQgC,QACR,sEAAsE;AAGxE,QAAI;AACF,UAAIC,iBAAiBjC,QAAQO,OAAO/D,YAAW,CAAE,GAAG;AAClD,YAAIjD,UAAS,MAAM2I,OACjBlC,SACA3I,SACA0K,cAAcJ,eAAetK,SAASxC,QAAQ,GAC9CqL,gBACAE,cACAD,yBACA4B,cAAc,IAAI;AAEpB,eAAOxI;MACR;AAED,UAAIA,SAAS,MAAM4I,cACjBnC,SACA3I,SACA6I,gBACAE,cACAD,yBACA4B,UAAU;AAEZ,aAAOV,WAAW9H,MAAM,IACpBA,SAAMzE,SAAA,CAAA,GAEDyE,QAAM;QACTsH,YAAY;QACZI,eAAe,CAAA;OAChB;aACE/M,GAAG;AAIV,UAAIkO,qBAAqBlO,CAAC,KAAKmN,WAAWnN,EAAEqF,MAAM,GAAG;AACnD,YAAIrF,EAAEmO,SAASC,WAAW/F,OAAO;AAC/B,gBAAMrI,EAAEqF;QACT;AACD,eAAOrF,EAAEqF;MACV;AAGD,UAAIgJ,mBAAmBrO,CAAC,GAAG;AACzB,eAAOA;MACR;AACD,YAAMA;IACP;EACH;AAEA,iBAAegO,OACblC,SACA3I,SACAmL,aACAtC,gBACAE,cACAD,yBACAsC,gBAAuB;AAEvB,QAAIlJ;AAEJ,QAAI,CAACiJ,YAAY5M,MAAM8M,UAAU,CAACF,YAAY5M,MAAM+M,MAAM;AACxD,UAAIpG,QAAQkE,uBAAuB,KAAK;QACtCF,QAAQP,QAAQO;QAChBxL,UAAU,IAAIuL,IAAIN,QAAQK,GAAG,EAAEtL;QAC/ByM,SAASgB,YAAY5M,MAAMU;MAC5B,CAAA;AACD,UAAImM,gBAAgB;AAClB,cAAMlG;MACP;AACDhD,eAAS;QACP8I,MAAMC,WAAW/F;QACjBA;;IAEH,OAAM;AACL,UAAIqG,UAAU,MAAMC,iBAClB,UACA7C,SACA,CAACwC,WAAW,GACZnL,SACAoL,gBACAvC,gBACAE,YAAY;AAEd7G,eAASqJ,QAAQJ,YAAY5M,MAAMU,EAAE;AAErC,UAAI0J,QAAQgC,OAAOc,SAAS;AAC1BC,uCAA+B/C,SAASyC,gBAAgB7C,MAAM;MAC/D;IACF;AAED,QAAIoD,iBAAiBzJ,MAAM,GAAG;AAK5B,YAAM,IAAI0J,SAAS,MAAM;QACvBpE,QAAQtF,OAAO2J,SAASrE;QACxBsE,SAAS;UACPC,UAAU7J,OAAO2J,SAASC,QAAQE,IAAI,UAAU;QACjD;MACF,CAAA;IACF;AAED,QAAIC,iBAAiB/J,MAAM,GAAG;AAC5B,UAAIgD,QAAQkE,uBAAuB,KAAK;QAAE4B,MAAM;MAAgB,CAAA;AAChE,UAAII,gBAAgB;AAClB,cAAMlG;MACP;AACDhD,eAAS;QACP8I,MAAMC,WAAW/F;QACjBA;;IAEH;AAED,QAAIkG,gBAAgB;AAGlB,UAAIc,cAAchK,MAAM,GAAG;AACzB,cAAMA,OAAOgD;MACd;AAED,aAAO;QACLlF,SAAS,CAACmL,WAAW;QACrB5B,YAAY,CAAA;QACZC,YAAY;UAAE,CAAC2B,YAAY5M,MAAMU,EAAE,GAAGiD,OAAOmF;;QAC7CoC,QAAQ;;;QAGRC,YAAY;QACZC,eAAe,CAAA;QACfC,eAAe,CAAA;QACfC,iBAAiB;;IAEpB;AAGD,QAAIsC,gBAAgB,IAAIC,QAAQzD,QAAQK,KAAK;MAC3C8C,SAASnD,QAAQmD;MACjBO,UAAU1D,QAAQ0D;MAClB1B,QAAQhC,QAAQgC;IACjB,CAAA;AAED,QAAIuB,cAAchK,MAAM,GAAG;AAGzB,UAAIoK,gBAAgBxD,0BAChBqC,cACAoB,oBAAoBvM,SAASmL,YAAY5M,MAAMU,EAAE;AAErD,UAAIuN,WAAU,MAAM1B,cAClBqB,eACAnM,SACA6I,gBACAE,cACAD,yBACA,MACA,CAACwD,cAAc/N,MAAMU,IAAIiD,MAAM,CAAC;AAIlC,aAAAzE,SAAA,CAAA,GACK+O,UAAO;QACV9C,YAAY3B,qBAAqB7F,OAAOgD,KAAK,IACzChD,OAAOgD,MAAMsC,SACbtF,OAAOwH,cAAc,OACrBxH,OAAOwH,aACP;QACJF,YAAY;QACZI,eAAanM,SAAA,CAAA,GACPyE,OAAO4J,UAAU;UAAE,CAACX,YAAY5M,MAAMU,EAAE,GAAGiD,OAAO4J;YAAY,CAAA,CAAE;MACrE,CAAA;IAEJ;AAED,QAAIU,UAAU,MAAM1B,cAClBqB,eACAnM,SACA6I,gBACAE,cACAD,yBACA,IAAI;AAGN,WAAArL,SAAA,CAAA,GACK+O,SAAO;MACVhD,YAAY;QACV,CAAC2B,YAAY5M,MAAMU,EAAE,GAAGiD,OAAOmF;MAChC;OAEGnF,OAAOwH,aAAa;MAAEA,YAAYxH,OAAOwH;QAAe,CAAA,GAAE;MAC9DE,eAAe1H,OAAO4J,UAClB;QAAE,CAACX,YAAY5M,MAAMU,EAAE,GAAGiD,OAAO4J;MAAS,IAC1C,CAAA;IAAE,CAAA;EAEV;AAEA,iBAAehB,cACbnC,SACA3I,SACA6I,gBACAE,cACAD,yBACA4B,YACA+B,qBAAyC;AAQzC,QAAIrB,iBAAiBV,cAAc;AAGnC,QACEU,kBACA,EAACV,cAAAA,QAAAA,WAAYnM,MAAMmO,WACnB,EAAChC,cAAAA,QAAAA,WAAYnM,MAAM+M,OACnB;AACA,YAAMlC,uBAAuB,KAAK;QAChCF,QAAQP,QAAQO;QAChBxL,UAAU,IAAIuL,IAAIN,QAAQK,GAAG,EAAEtL;QAC/ByM,SAASO,cAAU,OAAA,SAAVA,WAAYnM,MAAMU;MAC5B,CAAA;IACF;AAED,QAAI0N,iBAAiBjC,aACjB,CAACA,UAAU,IACX+B,uBAAuBP,cAAcO,oBAAoB,CAAC,CAAC,IAC3DG,8BAA8B5M,SAASyM,oBAAoB,CAAC,CAAC,IAC7DzM;AACJ,QAAI6M,gBAAgBF,eAAe9J,OAChCwH,OAAMA,EAAE9L,MAAMmO,UAAUrC,EAAE9L,MAAM+M,IAAI;AAIvC,QAAIuB,cAAc3M,WAAW,GAAG;AAC9B,aAAO;QACLF;;QAEAuJ,YAAYvJ,QAAQ+C,OAClB,CAAC+J,KAAKzC,MAAMrG,OAAOC,OAAO6I,KAAK;UAAE,CAACzC,EAAE9L,MAAMU,EAAE,GAAG;QAAI,CAAE,GACrD,CAAA,CAAE;QAEJwK,QACEgD,uBAAuBP,cAAcO,oBAAoB,CAAC,CAAC,IACvD;UACE,CAACA,oBAAoB,CAAC,CAAC,GAAGA,oBAAoB,CAAC,EAAEvH;QAClD,IACD;QACNwE,YAAY;QACZC,eAAe,CAAA;QACfE,iBAAiB;;IAEpB;AAED,QAAI0B,UAAU,MAAMC,iBAClB,UACA7C,SACAkE,eACA7M,SACAoL,gBACAvC,gBACAE,YAAY;AAGd,QAAIJ,QAAQgC,OAAOc,SAAS;AAC1BC,qCAA+B/C,SAASyC,gBAAgB7C,MAAM;IAC/D;AAGD,QAAIsB,kBAAkB,oBAAIkD,IAAG;AAC7B,QAAIP,UAAUQ,uBACZhN,SACAuL,SACAkB,qBACA5C,iBACAf,uBAAuB;AAIzB,QAAImE,kBAAkB,IAAIC,IACxBL,cAAc/N,IAAKgF,WAAUA,MAAMvF,MAAMU,EAAE,CAAC;AAE9Ce,YAAQoB,QAAS0C,WAAS;AACxB,UAAI,CAACmJ,gBAAgBE,IAAIrJ,MAAMvF,MAAMU,EAAE,GAAG;AACxCuN,gBAAQjD,WAAWzF,MAAMvF,MAAMU,EAAE,IAAI;MACtC;IACH,CAAC;AAED,WAAAxB,SAAA,CAAA,GACK+O,SAAO;MACVxM;MACA6J,iBACEA,gBAAgBuD,OAAO,IACnBpJ,OAAOqJ,YAAYxD,gBAAgByD,QAAO,CAAE,IAC5C;IAAI,CAAA;EAEd;AAIA,iBAAe9B,iBACbR,MACArC,SACAkE,eACA7M,SACAoL,gBACAvC,gBACAE,cAAyC;AAEzC,QAAIwC,UAAU,MAAMgC,qBAClBxE,gBAAgByE,qBAChBxC,MACA,MACArC,SACAkE,eACA7M,SACA,MACAnB,UACAF,oBACAkK,cAAc;AAGhB,QAAI4E,cAA0C,CAAA;AAC9C,UAAM/F,QAAQgG,IACZ1N,QAAQlB,IAAI,OAAOgF,UAAS;AAC1B,UAAI,EAAEA,MAAMvF,MAAMU,MAAMsM,UAAU;AAChC;MACD;AACD,UAAIrJ,SAASqJ,QAAQzH,MAAMvF,MAAMU,EAAE;AACnC,UAAI0O,mCAAmCzL,MAAM,GAAG;AAC9C,YAAI2J,WAAW3J,OAAOA;AAEtB,cAAM0L,yCACJ/B,UACAlD,SACA7E,MAAMvF,MAAMU,IACZe,SACAP,UACA8I,OAAOjC,oBAAoB;MAE9B;AACD,UAAI0D,WAAW9H,OAAOA,MAAM,KAAKkJ,gBAAgB;AAG/C,cAAMlJ;MACP;AAEDuL,kBAAY3J,MAAMvF,MAAMU,EAAE,IACxB,MAAM4O,sCAAsC3L,MAAM;IACtD,CAAC,CAAC;AAEJ,WAAOuL;EACT;AAEA,SAAO;IACLhF;IACAC;IACAuB;;AAEJ;SAYgB6D,0BACdpP,QACA8N,SACAtH,OAAU;AAEV,MAAI6I,aAAUtQ,SAAA,CAAA,GACT+O,SAAO;IACV9C,YAAY3B,qBAAqB7C,KAAK,IAAIA,MAAMsC,SAAS;IACzDiC,QAAQ;MACN,CAAC+C,QAAQwB,8BAA8BtP,OAAO,CAAC,EAAEO,EAAE,GAAGiG;IACvD;GACF;AACD,SAAO6I;AACT;AAEA,SAASrC,+BACP/C,SACAyC,gBACA7C,QAAiC;AAEjC,MAAIA,OAAOC,uBAAuBG,QAAQgC,OAAOsD,WAAW3O,QAAW;AACrE,UAAMqJ,QAAQgC,OAAOsD;EACtB;AAED,MAAI/E,SAASkC,iBAAiB,eAAe;AAC7C,QAAM,IAAI5O,MAAS0M,SAA0BP,sBAAAA,QAAQO,SAAUP,MAAAA,QAAQK,GAAK;AAC9E;AAYA,SAASkF,YACP1Q,UACAwC,SACAP,UACA0O,iBACA9Q,IACAiJ,sBACA8H,aACAC,UAA8B;AAE9B,MAAIC;AACJ,MAAIC;AACJ,MAAIH,aAAa;AAGfE,wBAAoB,CAAA;AACpB,aAASxK,SAAS9D,SAAS;AACzBsO,wBAAkBrN,KAAK6C,KAAK;AAC5B,UAAIA,MAAMvF,MAAMU,OAAOmP,aAAa;AAClCG,2BAAmBzK;AACnB;MACD;IACF;EACF,OAAM;AACLwK,wBAAoBtO;AACpBuO,uBAAmBvO,QAAQA,QAAQE,SAAS,CAAC;EAC9C;AAGD,MAAIjC,OAAOwI,UACTpJ,KAAKA,KAAK,KACVgJ,oBAAoBiI,mBAAmBhI,oBAAoB,GAC3D1G,cAAcpC,SAASE,UAAU+B,QAAQ,KAAKjC,SAASE,UACvD2Q,aAAa,MAAM;AAMrB,MAAIhR,MAAM,MAAM;AACdY,SAAKN,SAASH,SAASG;AACvBM,SAAKL,OAAOJ,SAASI;EACtB;AAGD,OAAKP,MAAM,QAAQA,OAAO,MAAMA,OAAO,QAAQkR,kBAAkB;AAC/D,QAAIC,aAAaC,mBAAmBxQ,KAAKN,MAAM;AAC/C,QAAI4Q,iBAAiBhQ,MAAMC,SAAS,CAACgQ,YAAY;AAE/CvQ,WAAKN,SAASM,KAAKN,SACfM,KAAKN,OAAOqE,QAAQ,OAAO,SAAS,IACpC;eACK,CAACuM,iBAAiBhQ,MAAMC,SAASgQ,YAAY;AAEtD,UAAItK,SAAS,IAAIwK,gBAAgBzQ,KAAKN,MAAM;AAC5C,UAAIgR,cAAczK,OAAO0K,OAAO,OAAO;AACvC1K,aAAO2K,OAAO,OAAO;AACrBF,kBAAY9L,OAAQmC,OAAMA,CAAC,EAAE5D,QAAS4D,OAAMd,OAAO4K,OAAO,SAAS9J,CAAC,CAAC;AACrE,UAAI+J,KAAK7K,OAAOjH,SAAQ;AACxBgB,WAAKN,SAASoR,KAASA,MAAAA,KAAO;IAC/B;EACF;AAMD,MAAIZ,mBAAmB1O,aAAa,KAAK;AACvCxB,SAAKP,WACHO,KAAKP,aAAa,MAAM+B,WAAWqB,UAAU,CAACrB,UAAUxB,KAAKP,QAAQ,CAAC;EACzE;AAED,SAAOI,WAAWG,IAAI;AACxB;AAyJA,SAAS2O,8BACP5M,SACAgP,YACAC,iBAAuB;AAAA,MAAvBA,oBAAe,QAAA;AAAfA,sBAAkB;EAAK;AAEvB,MAAIzQ,QAAQwB,QAAQkP,UAAW7E,OAAMA,EAAE9L,MAAMU,OAAO+P,UAAU;AAC9D,MAAIxQ,SAAS,GAAG;AACd,WAAOwB,QAAQa,MAAM,GAAGoO,kBAAkBzQ,QAAQ,IAAIA,KAAK;EAC5D;AACD,SAAOwB;AACT;AA8WA,eAAemP,oBACb5Q,OACAI,oBACAE,UAAuB;AAEvB,MAAI,CAACN,MAAM+M,MAAM;AACf;EACD;AAED,MAAI8D,YAAY,MAAM7Q,MAAM+M,KAAI;AAKhC,MAAI,CAAC/M,MAAM+M,MAAM;AACf;EACD;AAED,MAAI+D,gBAAgBxQ,SAASN,MAAMU,EAAE;AACrC5C,YAAUgT,eAAe,4BAA4B;AAUrD,MAAIC,eAAoC,CAAA;AACxC,WAASC,qBAAqBH,WAAW;AACvC,QAAII,mBACFH,cAAcE,iBAA+C;AAE/D,QAAIE,8BACFD,qBAAqBlQ;;IAGrBiQ,sBAAsB;AAExB9S,YACE,CAACgT,6BACD,YAAUJ,cAAcpQ,KAAE,8BAA4BsQ,oBAAiB,mFAEzCA,8BAAAA,oBAAiB,qBAAoB;AAGrE,QACE,CAACE,+BACD,CAACC,mBAAmBvC,IAAIoC,iBAAsC,GAC9D;AACAD,mBAAaC,iBAAiB,IAC5BH,UAAUG,iBAA2C;IACxD;EACF;AAIDvL,SAAOC,OAAOoL,eAAeC,YAAY;AAKzCtL,SAAOC,OAAOoL,eAAa5R,SAKtBkB,CAAAA,GAAAA,mBAAmB0Q,aAAa,GAAC;IACpC/D,MAAMhM;EAAS,CAAA,CAChB;AACH;AAGA,eAAekO,oBAAmBmC,OAEP;AAAA,MAFQ;IACjC3P;EACyB,IAAA2P;AACzB,MAAI9C,gBAAgB7M,QAAQ6C,OAAQwH,OAAMA,EAAEuF,UAAU;AACtD,MAAIrE,UAAU,MAAM7D,QAAQgG,IAAIb,cAAc/N,IAAKuL,OAAMA,EAAEwF,QAAO,CAAE,CAAC;AACrE,SAAOtE,QAAQxI,OACb,CAAC+J,KAAK5K,QAAQjC,MACZ+D,OAAOC,OAAO6I,KAAK;IAAE,CAACD,cAAc5M,CAAC,EAAE1B,MAAMU,EAAE,GAAGiD;EAAM,CAAE,GAC5D,CAAA,CAAE;AAEN;AAEA,eAAeqL,qBACbuC,kBACA9E,MACA1N,OACAqL,SACAkE,eACA7M,SACA+P,YACAlR,UACAF,oBACAkK,gBAAwB;AAExB,MAAImH,+BAA+BhQ,QAAQlB,IAAKuL,OAC9CA,EAAE9L,MAAM+M,OACJ6D,oBAAoB9E,EAAE9L,OAAOI,oBAAoBE,QAAQ,IACzDS,MAAS;AAGf,MAAI2Q,YAAYjQ,QAAQlB,IAAI,CAACgF,OAAO7D,MAAK;AACvC,QAAIiQ,mBAAmBF,6BAA6B/P,CAAC;AACrD,QAAI2P,aAAa/C,cAAcpK,KAAM4H,OAAMA,EAAE9L,MAAMU,OAAO6E,MAAMvF,MAAMU,EAAE;AAKxE,QAAI4Q,UAAwC,OAAOM,oBAAmB;AACpE,UACEA,mBACAxH,QAAQO,WAAW,UAClBpF,MAAMvF,MAAM+M,QAAQxH,MAAMvF,MAAMmO,SACjC;AACAkD,qBAAa;MACd;AACD,aAAOA,aACHQ,mBACEpF,MACArC,SACA7E,OACAoM,kBACAC,iBACAtH,cAAc,IAEhBnB,QAAQmI,QAAQ;QAAE7E,MAAMC,WAAW5D;QAAMnF,QAAQ5C;MAAS,CAAE;;AAGlE,WAAA7B,SAAA,CAAA,GACKqG,OAAK;MACR8L;MACAC;IAAO,CAAA;EAEX,CAAC;AAKD,MAAItE,UAAU,MAAMuE,iBAAiB;IACnC9P,SAASiQ;IACTtH;IACAzE,QAAQlE,QAAQ,CAAC,EAAEkE;IACnB6L;IACAvD,SAAS3D;EACV,CAAA;AAKD,MAAI;AACF,UAAMnB,QAAQgG,IAAIsC,4BAA4B;WACvCnT,GAAG;EACV;AAGF,SAAO0O;AACT;AAGA,eAAe6E,mBACbpF,MACArC,SACA7E,OACAoM,kBACAC,iBACAE,eAAuB;AAEvB,MAAInO;AACJ,MAAIoO;AAEJ,MAAIC,aACFC,aAC+B;AAE/B,QAAIC;AAGJ,QAAIC,eAAe,IAAIhJ,QAA4B,CAAC5C,GAAG6L,MAAOF,SAASE,CAAE;AACzEL,eAAWA,MAAMG,OAAM;AACvB9H,YAAQgC,OAAOiG,iBAAiB,SAASN,QAAQ;AAEjD,QAAIO,gBAAiBC,SAAiB;AACpC,UAAI,OAAON,YAAY,YAAY;AACjC,eAAO9I,QAAQ+I,OACb,IAAIjU,MACF,sEAAA,MACMwO,OAAI,iBAAelH,MAAMvF,MAAMU,KAAE,IAAG,CAC3C;MAEJ;AACD,aAAOuR,QACL;QACE7H;QACAzE,QAAQJ,MAAMI;QACdsI,SAAS6D;MACV,GACD,GAAIS,QAAQxR,SAAY,CAACwR,GAAG,IAAI,CAAA,CAAG;;AAIvC,QAAIC,kBAA+C,YAAW;AAC5D,UAAI;AACF,YAAIC,MAAM,OAAOb,kBACbA,gBAAiBW,SAAiBD,cAAcC,GAAG,CAAC,IACpDD,cAAa;AACjB,eAAO;UAAE7F,MAAM;UAAQ9I,QAAQ8O;;eACxBnU,GAAG;AACV,eAAO;UAAEmO,MAAM;UAAS9I,QAAQrF;;MACjC;IACH,GAAC;AAED,WAAO6K,QAAQuJ,KAAK,CAACF,gBAAgBL,YAAY,CAAC;;AAGpD,MAAI;AACF,QAAIF,UAAU1M,MAAMvF,MAAMyM,IAAI;AAG9B,QAAIkF,kBAAkB;AACpB,UAAIM,SAAS;AAEX,YAAIU;AACJ,YAAI,CAAC5U,KAAK,IAAI,MAAMoL,QAAQgG,IAAI;;;;UAI9B6C,WAAWC,OAAO,EAAEW,MAAOtU,OAAK;AAC9BqU,2BAAerU;UACjB,CAAC;UACDqT;QAAgB,CACjB;AACD,YAAIgB,iBAAiB5R,QAAW;AAC9B,gBAAM4R;QACP;AACDhP,iBAAS5F;MACV,OAAM;AAEL,cAAM4T;AAENM,kBAAU1M,MAAMvF,MAAMyM,IAAI;AAC1B,YAAIwF,SAAS;AAIXtO,mBAAS,MAAMqO,WAAWC,OAAO;QAClC,WAAUxF,SAAS,UAAU;AAC5B,cAAIhC,MAAM,IAAIC,IAAIN,QAAQK,GAAG;AAC7B,cAAItL,WAAWsL,IAAItL,WAAWsL,IAAIrL;AAClC,gBAAMyL,uBAAuB,KAAK;YAChCF,QAAQP,QAAQO;YAChBxL;YACAyM,SAASrG,MAAMvF,MAAMU;UACtB,CAAA;QACF,OAAM;AAGL,iBAAO;YAAE+L,MAAMC,WAAW5D;YAAMnF,QAAQ5C;;QACzC;MACF;IACF,WAAU,CAACkR,SAAS;AACnB,UAAIxH,MAAM,IAAIC,IAAIN,QAAQK,GAAG;AAC7B,UAAItL,WAAWsL,IAAItL,WAAWsL,IAAIrL;AAClC,YAAMyL,uBAAuB,KAAK;QAChC1L;MACD,CAAA;IACF,OAAM;AACLwE,eAAS,MAAMqO,WAAWC,OAAO;IAClC;AAEDnU,cACE6F,OAAOA,WAAW5C,QAClB,kBAAe0L,SAAS,WAAW,cAAc,cAC3ClH,iBAAAA,MAAAA,MAAMvF,MAAMU,KAA8C+L,8CAAAA,OAAS,QAAA,4CACzB;WAE3CnO,GAAG;AAIV,WAAO;MAAEmO,MAAMC,WAAW/F;MAAOhD,QAAQrF;;EAC1C,UAAA;AACC,QAAIyT,UAAU;AACZ3H,cAAQgC,OAAOyG,oBAAoB,SAASd,QAAQ;IACrD;EACF;AAED,SAAOpO;AACT;AAEA,eAAe2L,sCACbwD,oBAAsC;AAEtC,MAAI;IAAEnP;IAAQ8I;EAAM,IAAGqG;AAEvB,MAAIrH,WAAW9H,MAAM,GAAG;AACtB,QAAImF;AAEJ,QAAI;AACF,UAAIiK,cAAcpP,OAAO4J,QAAQE,IAAI,cAAc;AAGnD,UAAIsF,eAAe,wBAAwBpO,KAAKoO,WAAW,GAAG;AAC5D,YAAIpP,OAAOqP,QAAQ,MAAM;AACvBlK,UAAAA,QAAO;QACR,OAAM;AACLA,UAAAA,QAAO,MAAMnF,OAAOsP,KAAI;QACzB;MACF,OAAM;AACLnK,QAAAA,QAAO,MAAMnF,OAAOuP,KAAI;MACzB;aACM5U,GAAG;AACV,aAAO;QAAEmO,MAAMC,WAAW/F;QAAOA,OAAOrI;;IACzC;AAED,QAAImO,SAASC,WAAW/F,OAAO;AAC7B,aAAO;QACL8F,MAAMC,WAAW/F;QACjBA,OAAO,IAAIwM,kBAAkBxP,OAAOsF,QAAQtF,OAAO8F,YAAYX,KAAI;QACnEqC,YAAYxH,OAAOsF;QACnBsE,SAAS5J,OAAO4J;;IAEnB;AAED,WAAO;MACLd,MAAMC,WAAW5D;MACjBA,MAAAA;MACAqC,YAAYxH,OAAOsF;MACnBsE,SAAS5J,OAAO4J;;EAEnB;AAED,MAAId,SAASC,WAAW/F,OAAO;AAC7B,QAAIyM,uBAAuBzP,MAAM,GAAG;AAAA,UAAA0P,eAAAC;AAClC,UAAI3P,OAAOmF,gBAAgB7K,OAAO;AAAA,YAAAsV,cAAAC;AAChC,eAAO;UACL/G,MAAMC,WAAW/F;UACjBA,OAAOhD,OAAOmF;UACdqC,aAAUoI,eAAE5P,OAAOoF,SAAI,OAAA,SAAXwK,aAAatK;UACzBsE,UAASiG,gBAAA7P,OAAOoF,SAAI,QAAXyK,cAAajG,UAClB,IAAIkG,QAAQ9P,OAAOoF,KAAKwE,OAAO,IAC/BxM;;MAEP;AAGD,aAAO;QACL0L,MAAMC,WAAW/F;QACjBA,OAAO,IAAIwM,oBACTE,gBAAA1P,OAAOoF,SAAI,OAAA,SAAXsK,cAAapK,WAAU,KACvBlI,QACA4C,OAAOmF,IAAI;QAEbqC,YAAY3B,qBAAqB7F,MAAM,IAAIA,OAAOsF,SAASlI;QAC3DwM,UAAS+F,gBAAA3P,OAAOoF,SAAI,QAAXuK,cAAa/F,UAClB,IAAIkG,QAAQ9P,OAAOoF,KAAKwE,OAAO,IAC/BxM;;IAEP;AACD,WAAO;MACL0L,MAAMC,WAAW/F;MACjBA,OAAOhD;MACPwH,YAAY3B,qBAAqB7F,MAAM,IAAIA,OAAOsF,SAASlI;;EAE9D;AAED,MAAI2S,eAAe/P,MAAM,GAAG;AAAA,QAAAgQ,eAAAC;AAC1B,WAAO;MACLnH,MAAMC,WAAWmH;MACjBC,cAAcnQ;MACdwH,aAAUwI,gBAAEhQ,OAAOoF,SAAI,OAAA,SAAX4K,cAAa1K;MACzBsE,WAASqG,gBAAAjQ,OAAOoF,SAAP6K,OAAAA,SAAAA,cAAarG,YAAW,IAAIkG,QAAQ9P,OAAOoF,KAAKwE,OAAO;;EAEnE;AAED,MAAI6F,uBAAuBzP,MAAM,GAAG;AAAA,QAAAoQ,eAAAC;AAClC,WAAO;MACLvH,MAAMC,WAAW5D;MACjBA,MAAMnF,OAAOmF;MACbqC,aAAU4I,gBAAEpQ,OAAOoF,SAAI,OAAA,SAAXgL,cAAa9K;MACzBsE,UAASyG,gBAAArQ,OAAOoF,SAAI,QAAXiL,cAAazG,UAClB,IAAIkG,QAAQ9P,OAAOoF,KAAKwE,OAAO,IAC/BxM;;EAEP;AAED,SAAO;IAAE0L,MAAMC,WAAW5D;IAAMA,MAAMnF;;AACxC;AAGA,SAAS0L,yCACP/B,UACAlD,SACAwB,SACAnK,SACAP,UACA6G,sBAA6B;AAE7B,MAAI9I,WAAWqO,SAASC,QAAQE,IAAI,UAAU;AAC9C3P,YACEmB,UACA,4EAA4E;AAG9E,MAAI,CAACgV,mBAAmBtP,KAAK1F,QAAQ,GAAG;AACtC,QAAIiV,iBAAiBzS,QAAQa,MAC3B,GACAb,QAAQkP,UAAW7E,OAAMA,EAAE9L,MAAMU,OAAOkL,OAAO,IAAI,CAAC;AAEtD3M,eAAW0Q,YACT,IAAIjF,IAAIN,QAAQK,GAAG,GACnByJ,gBACAhT,UACA,MACAjC,UACA8I,oBAAoB;AAEtBuF,aAASC,QAAQ4G,IAAI,YAAYlV,QAAQ;EAC1C;AAED,SAAOqO;AACT;AAkFA,SAASmB,uBACPhN,SACAuL,SACAkB,qBACA5C,iBACAf,yBAAgC;AAQhC,MAAIS,aAAwC,CAAA;AAC5C,MAAIE,SAAuC;AAC3C,MAAIC;AACJ,MAAIiJ,aAAa;AACjB,MAAIhJ,gBAAyC,CAAA;AAC7C,MAAIiJ,eACFnG,uBAAuBP,cAAcO,oBAAoB,CAAC,CAAC,IACvDA,oBAAoB,CAAC,EAAEvH,QACvB5F;AAGNU,UAAQoB,QAAS0C,WAAS;AACxB,QAAI,EAAEA,MAAMvF,MAAMU,MAAMsM,UAAU;AAChC;IACD;AACD,QAAItM,KAAK6E,MAAMvF,MAAMU;AACrB,QAAIiD,SAASqJ,QAAQtM,EAAE;AACvB5C,cACE,CAACsP,iBAAiBzJ,MAAM,GACxB,qDAAqD;AAEvD,QAAIgK,cAAchK,MAAM,GAAG;AACzB,UAAIgD,QAAQhD,OAAOgD;AAInB,UAAI0N,iBAAiBtT,QAAW;AAC9B4F,gBAAQ0N;AACRA,uBAAetT;MAChB;AAEDmK,eAASA,UAAU,CAAA;AAEnB,UAAIX,yBAAyB;AAC3BW,eAAOxK,EAAE,IAAIiG;MACd,OAAM;AAIL,YAAIoH,gBAAgBC,oBAAoBvM,SAASf,EAAE;AACnD,YAAIwK,OAAO6C,cAAc/N,MAAMU,EAAE,KAAK,MAAM;AAC1CwK,iBAAO6C,cAAc/N,MAAMU,EAAE,IAAIiG;QAClC;MACF;AAGDqE,iBAAWtK,EAAE,IAAIK;AAIjB,UAAI,CAACqT,YAAY;AACfA,qBAAa;AACbjJ,qBAAa3B,qBAAqB7F,OAAOgD,KAAK,IAC1ChD,OAAOgD,MAAMsC,SACb;MACL;AACD,UAAItF,OAAO4J,SAAS;AAClBnC,sBAAc1K,EAAE,IAAIiD,OAAO4J;MAC5B;IACF,OAAM;AACL,UAAIG,iBAAiB/J,MAAM,GAAG;AAC5B2H,wBAAgB6I,IAAIzT,IAAIiD,OAAOmQ,YAAY;AAC3C9I,mBAAWtK,EAAE,IAAIiD,OAAOmQ,aAAahL;AAGrC,YACEnF,OAAOwH,cAAc,QACrBxH,OAAOwH,eAAe,OACtB,CAACiJ,YACD;AACAjJ,uBAAaxH,OAAOwH;QACrB;AACD,YAAIxH,OAAO4J,SAAS;AAClBnC,wBAAc1K,EAAE,IAAIiD,OAAO4J;QAC5B;MACF,OAAM;AACLvC,mBAAWtK,EAAE,IAAIiD,OAAOmF;AAGxB,YAAInF,OAAOwH,cAAcxH,OAAOwH,eAAe,OAAO,CAACiJ,YAAY;AACjEjJ,uBAAaxH,OAAOwH;QACrB;AACD,YAAIxH,OAAO4J,SAAS;AAClBnC,wBAAc1K,EAAE,IAAIiD,OAAO4J;QAC5B;MACF;IACF;EACH,CAAC;AAKD,MAAI8G,iBAAiBtT,UAAamN,qBAAqB;AACrDhD,aAAS;MAAE,CAACgD,oBAAoB,CAAC,CAAC,GAAGmG;;AACrCrJ,eAAWkD,oBAAoB,CAAC,CAAC,IAAInN;EACtC;AAED,SAAO;IACLiK;IACAE;IACAC,YAAYA,cAAc;IAC1BC;;AAEJ;AA8GA,SAAS4C,oBACPvM,SACAmK,SAAgB;AAEhB,MAAI0I,kBAAkB1I,UAClBnK,QAAQa,MAAM,GAAGb,QAAQkP,UAAW7E,OAAMA,EAAE9L,MAAMU,OAAOkL,OAAO,IAAI,CAAC,IACrE,CAAC,GAAGnK,OAAO;AACf,SACE6S,gBAAgBC,QAAO,EAAG1I,KAAMC,OAAMA,EAAE9L,MAAM8J,qBAAqB,IAAI,KACvErI,QAAQ,CAAC;AAEb;AAEA,SAASsJ,uBAAuB5K,QAAiC;AAK/D,MAAIH,QACFG,OAAOwB,WAAW,IACdxB,OAAO,CAAC,IACRA,OAAO0L,KAAMuG,OAAMA,EAAEnS,SAAS,CAACmS,EAAE1S,QAAQ0S,EAAE1S,SAAS,GAAG,KAAK;IAC1DgB,IAAE;;AAGV,SAAO;IACLe,SAAS,CACP;MACEkE,QAAQ,CAAA;MACRxG,UAAU;MACVyG,cAAc;MACd5F;IACD,CAAA;IAEHA;;AAEJ;AAEA,SAAS6K,uBACP5B,QAAcuL,QAaR;AAAA,MAZN;IACErV;IACAyM;IACAjB;IACA8B;IACAzO;0BAOE,CAAA,IAAEwW;AAEN,MAAI/K,aAAa;AACjB,MAAIgL,eAAe;AAEnB,MAAIxL,WAAW,KAAK;AAClBQ,iBAAa;AACb,QAAIkB,UAAUxL,YAAYyM,SAAS;AACjC6I,qBACE,gBAAc9J,SAAM,kBAAgBxL,WACOyM,YAAAA,2CAAAA,UAAO,SACP;IAC9C,WAAUa,SAAS,gBAAgB;AAClCgI,qBAAe;IAChB,WAAUhI,SAAS,gBAAgB;AAClCgI,qBAAe;IAChB;EACF,WAAUxL,WAAW,KAAK;AACzBQ,iBAAa;AACbgL,mBAAyB7I,YAAAA,UAAgCzM,2BAAAA,WAAW;EACrE,WAAU8J,WAAW,KAAK;AACzBQ,iBAAa;AACbgL,mBAAY,2BAA4BtV,WAAW;EACpD,WAAU8J,WAAW,KAAK;AACzBQ,iBAAa;AACb,QAAIkB,UAAUxL,YAAYyM,SAAS;AACjC6I,qBACE,gBAAc9J,OAAO+J,YAAW,IAAE,kBAAgBvV,WAAQ,YAAA,4CACdyM,UAAO,SACR;eACpCjB,QAAQ;AACjB8J,qBAAY,6BAA8B9J,OAAO+J,YAAW,IAAK;IAClE;EACF;AAED,SAAO,IAAIvB,kBACTlK,UAAU,KACVQ,YACA,IAAIxL,MAAMwW,YAAY,GACtB,IAAI;AAER;AA6CA,SAASjI,qBAAqB7I,QAAe;AAC3C,SACEA,UAAU,QACV,OAAOA,WAAW,YAClB,UAAUA,UACV,YAAYA,WACXA,OAAO8I,SAASC,WAAW5D,QAAQnF,OAAO8I,SAASC,WAAW/F;AAEnE;AAEA,SAASyI,mCAAmCzL,QAA0B;AACpE,SACE8H,WAAW9H,OAAOA,MAAM,KAAKgR,oBAAoB/F,IAAIjL,OAAOA,OAAOsF,MAAM;AAE7E;AAEA,SAASyE,iBAAiB/J,QAAkB;AAC1C,SAAOA,OAAO8I,SAASC,WAAWmH;AACpC;AAEA,SAASlG,cAAchK,QAAkB;AACvC,SAAOA,OAAO8I,SAASC,WAAW/F;AACpC;AAEA,SAASyG,iBAAiBzJ,QAAmB;AAC3C,UAAQA,UAAUA,OAAO8I,UAAUC,WAAWoB;AAChD;AAEM,SAAUsF,uBACdrV,OAAU;AAEV,SACE,OAAOA,UAAU,YACjBA,SAAS,QACT,UAAUA,SACV,UAAUA,SACV,UAAUA,SACVA,MAAM0O,SAAS;AAEnB;AAEM,SAAUiH,eAAe3V,OAAU;AACvC,MAAI8V,WAAyB9V;AAC7B,SACE8V,YACA,OAAOA,aAAa,YACpB,OAAOA,SAAS/K,SAAS,YACzB,OAAO+K,SAASe,cAAc,cAC9B,OAAOf,SAASgB,WAAW,cAC3B,OAAOhB,SAASiB,gBAAgB;AAEpC;AAEA,SAASrJ,WAAW1N,OAAU;AAC5B,SACEA,SAAS,QACT,OAAOA,MAAMkL,WAAW,YACxB,OAAOlL,MAAM0L,eAAe,YAC5B,OAAO1L,MAAMwP,YAAY,YACzB,OAAOxP,MAAMiV,SAAS;AAE1B;AAEA,SAASrG,mBAAmBhJ,QAAW;AACrC,MAAI,CAAC8H,WAAW9H,MAAM,GAAG;AACvB,WAAO;EACR;AAED,MAAIsF,SAAStF,OAAOsF;AACpB,MAAIhK,WAAW0E,OAAO4J,QAAQE,IAAI,UAAU;AAC5C,SAAOxE,UAAU,OAAOA,UAAU,OAAOhK,YAAY;AACvD;AAEA,SAAS2L,cAAcD,QAAc;AACnC,SAAOoK,oBAAoBnG,IAAIjE,OAAO/D,YAAW,CAAgB;AACnE;AAEA,SAASyF,iBACP1B,QAAc;AAEd,SAAOqK,qBAAqBpG,IAAIjE,OAAO/D,YAAW,CAAwB;AAC5E;AA2GA,SAASsJ,mBAAmB9Q,QAAc;AACxC,SAAO,IAAI+Q,gBAAgB/Q,MAAM,EAAEiR,OAAO,OAAO,EAAEnM,KAAMuC,OAAMA,MAAM,EAAE;AACzE;AAEA,SAASsF,eACPtK,SACAxC,UAA2B;AAE3B,MAAIG,SACF,OAAOH,aAAa,WAAWK,UAAUL,QAAQ,EAAEG,SAASH,SAASG;AACvE,MACEqC,QAAQA,QAAQE,SAAS,CAAC,EAAE3B,MAAMC,SAClCiQ,mBAAmB9Q,UAAU,EAAE,GAC/B;AAEA,WAAOqC,QAAQA,QAAQE,SAAS,CAAC;EAClC;AAGD,MAAIqG,cAAcH,2BAA2BpG,OAAO;AACpD,SAAOuG,YAAYA,YAAYrG,SAAS,CAAC;AAC3C;IFvqLYsT,QCGAvI,YAgSCyE,oBAmcPzM,SACAE,qBACAP,iBACAQ,mBACAC,oBACAV,cACAD,SAskBO5B,WAMAsD,mBAMAsB,iBAUAC,eAeA6L,MAcAjK,sBA4BAkM,sBAEAC,cAqMAC,OAeAtH,UAsBAuH,kBAYA5R,SAoBA0P,mBC/9BPmC,yBAMAN,sBAIAO,wBAIAR,qBAEAJ,qBAgCAV,oBAEAlK,2BA0oFOmC;;;AF12Gb,KAAA,SAAY+I,SAAM;AAQhBA,MAAAA,QAAA,KAAA,IAAA;AAOAA,MAAAA,QAAA,MAAA,IAAA;AAMAA,MAAAA,QAAA,SAAA,IAAA;IACF,GAtBYA,WAAAA,SAsBX,CAAA,EAAA;ACnBD,KAAA,SAAYvI,aAAU;AACpBA,MAAAA,YAAA,MAAA,IAAA;AACAA,MAAAA,YAAA,UAAA,IAAA;AACAA,MAAAA,YAAA,UAAA,IAAA;AACAA,MAAAA,YAAA,OAAA,IAAA;IACF,GALYA,eAAAA,aAKX,CAAA,EAAA;AA2RM,IAAMyE,qBAAqB,oBAAIxC,IAAuB,CAC3D,QACA,iBACA,QACA,MACA,SACA,UAAU,CACX;AA4bD,IAAMjK,UAAU;AAChB,IAAME,sBAAsB;AAC5B,IAAMP,kBAAkB;AACxB,IAAMQ,oBAAoB;AAC1B,IAAMC,qBAAqB;AAC3B,IAAMV,eAAe;AACrB,IAAMD,UAAWI,OAAcA,MAAM;IAskBxBhC,YAAaiT,WACxBA,MAAM7U,KAAK,GAAG,EAAE8C,QAAQ,UAAU,GAAG;IAK1BoC,oBAAqB1G,cAChCA,SAASsE,QAAQ,QAAQ,EAAE,EAAEA,QAAQ,QAAQ,GAAG;AAK3C,IAAM0D,kBAAmB/H,YAC9B,CAACA,UAAUA,WAAW,MAClB,KACAA,OAAOiD,WAAW,GAAG,IACrBjD,SACA,MAAMA;AAKL,IAAMgI,gBAAiB/H,UAC5B,CAACA,QAAQA,SAAS,MAAM,KAAKA,KAAKgD,WAAW,GAAG,IAAIhD,OAAO,MAAMA;AAc5D,IAAM4T,OAAqB,SAArBA,MAAsBnK,OAAMC,MAAa;AAAA,UAAbA,SAAI,QAAA;AAAJA,eAAO,CAAA;MAAE;AAChD,UAAI0M,eAAe,OAAO1M,SAAS,WAAW;QAAEE,QAAQF;MAAI,IAAKA;AAEjE,UAAIwE,UAAU,IAAIkG,QAAQgC,aAAalI,OAAO;AAC9C,UAAI,CAACA,QAAQqB,IAAI,cAAc,GAAG;AAChCrB,gBAAQ4G,IAAI,gBAAgB,iCAAiC;MAC9D;AAED,aAAO,IAAI9G,SAAS1F,KAAKC,UAAUkB,KAAI,GAAC5J,SAAA,CAAA,GACnCuW,cAAY;QACflI;MAAO,CAAA,CACR;IACH;IAEavE,6BAAoB;MAK/B0M,YAAY5M,OAASC,MAAmB;AAJxC,aAAI0D,OAAW;AAKb,aAAK3D,OAAOA;AACZ,aAAKC,OAAOA,QAAQ;MACtB;IACD;AAmBK,IAAOmM,uBAAP,cAAoCjX,MAAK;IAAA;IAElCkX,qBAAY;MAWvBO,YAAY5M,OAA+B2M,cAA2B;AAV9D,aAAAE,iBAA8B,oBAAIhH,IAAG;AAIrC,aAAAiH,cACN,oBAAIjH,IAAG;AAGT,aAAYkH,eAAa,CAAA;AAGvB/X,kBACEgL,SAAQ,OAAOA,UAAS,YAAY,CAACgN,MAAMC,QAAQjN,KAAI,GACvD,oCAAoC;AAKtC,YAAIoJ;AACJ,aAAKC,eAAe,IAAIhJ,QAAQ,CAAC5C,GAAG6L,MAAOF,SAASE,CAAE;AACtD,aAAK4D,aAAa,IAAIC,gBAAe;AACrC,YAAIC,UAAUA,MACZhE,OAAO,IAAIgD,qBAAqB,uBAAuB,CAAC;AAC1D,aAAKiB,sBAAsB,MACzB,KAAKH,WAAW5J,OAAOyG,oBAAoB,SAASqD,OAAO;AAC7D,aAAKF,WAAW5J,OAAOiG,iBAAiB,SAAS6D,OAAO;AAExD,aAAKpN,OAAOrD,OAAOsJ,QAAQjG,KAAI,EAAEtE,OAC/B,CAAC+J,KAAG6H,UAAA;AAAA,cAAE,CAACpX,KAAKjB,KAAK,IAACqY;AAAA,iBAChB3Q,OAAOC,OAAO6I,KAAK;YACjB,CAACvP,GAAG,GAAG,KAAKqX,aAAarX,KAAKjB,KAAK;WACpC;WACH,CAAA,CAAE;AAGJ,YAAI,KAAKuY,MAAM;AAEb,eAAKH,oBAAmB;QACzB;AAED,aAAKpN,OAAO0M;MACd;MAEQY,aACNrX,KACAjB,OAAiC;AAEjC,YAAI,EAAEA,iBAAiBoL,UAAU;AAC/B,iBAAOpL;QACR;AAED,aAAK8X,aAAanT,KAAK1D,GAAG;AAC1B,aAAK2W,eAAeY,IAAIvX,GAAG;AAI3B,YAAIwX,UAA0BrN,QAAQuJ,KAAK,CAAC3U,OAAO,KAAKoU,YAAY,CAAC,EAAEsE,KACpE3N,CAAAA,UAAS,KAAK4N,SAASF,SAASxX,KAAK+B,QAAW+H,KAAe,GAC/DnC,WAAU,KAAK+P,SAASF,SAASxX,KAAK2H,KAAgB,CAAC;AAK1D6P,gBAAQ5D,MAAM,MAAO;QAAA,CAAC;AAEtBnN,eAAOkR,eAAeH,SAAS,YAAY;UAAE/I,KAAKA,MAAM;QAAI,CAAE;AAC9D,eAAO+I;MACT;MAEQE,SACNF,SACAxX,KACA2H,OACAmC,OAAc;AAEd,YACE,KAAKkN,WAAW5J,OAAOc,WACvBvG,iBAAiBuO,sBACjB;AACA,eAAKiB,oBAAmB;AACxB1Q,iBAAOkR,eAAeH,SAAS,UAAU;YAAE/I,KAAKA,MAAM9G;UAAK,CAAE;AAC7D,iBAAOwC,QAAQ+I,OAAOvL,KAAK;QAC5B;AAED,aAAKgP,eAAerF,OAAOtR,GAAG;AAE9B,YAAI,KAAKsX,MAAM;AAEb,eAAKH,oBAAmB;QACzB;AAID,YAAIxP,UAAU5F,UAAa+H,UAAS/H,QAAW;AAC7C,cAAI6V,iBAAiB,IAAI3Y,MACvB,4BAA0Be,MAAG,uFACwB;AAEvDyG,iBAAOkR,eAAeH,SAAS,UAAU;YAAE/I,KAAKA,MAAMmJ;UAAc,CAAE;AACtE,eAAKC,KAAK,OAAO7X,GAAG;AACpB,iBAAOmK,QAAQ+I,OAAO0E,cAAc;QACrC;AAED,YAAI9N,UAAS/H,QAAW;AACtB0E,iBAAOkR,eAAeH,SAAS,UAAU;YAAE/I,KAAKA,MAAM9G;UAAK,CAAE;AAC7D,eAAKkQ,KAAK,OAAO7X,GAAG;AACpB,iBAAOmK,QAAQ+I,OAAOvL,KAAK;QAC5B;AAEDlB,eAAOkR,eAAeH,SAAS,SAAS;UAAE/I,KAAKA,MAAM3E;QAAI,CAAE;AAC3D,aAAK+N,KAAK,OAAO7X,GAAG;AACpB,eAAO8J;MACT;MAEQ+N,KAAK3J,SAAkB4J,YAAmB;AAChD,aAAKlB,YAAY/S,QAASkU,gBAAeA,WAAW7J,SAAS4J,UAAU,CAAC;MAC1E;MAEAlC,UAAUoC,IAAmD;AAC3D,aAAKpB,YAAYW,IAAIS,EAAE;AACvB,eAAO,MAAM,KAAKpB,YAAYtF,OAAO0G,EAAE;MACzC;MAEAnC,SAAM;AACJ,aAAKmB,WAAWiB,MAAK;AACrB,aAAKtB,eAAe9S,QAAQ,CAAC4D,GAAGyQ,MAAM,KAAKvB,eAAerF,OAAO4G,CAAC,CAAC;AACnE,aAAKL,KAAK,IAAI;MAChB;MAEA,MAAM/B,YAAY1I,QAAmB;AACnC,YAAIc,UAAU;AACd,YAAI,CAAC,KAAKoJ,MAAM;AACd,cAAIJ,UAAUA,MAAM,KAAKrB,OAAM;AAC/BzI,iBAAOiG,iBAAiB,SAAS6D,OAAO;AACxChJ,oBAAU,MAAM,IAAI/D,QAASmI,aAAW;AACtC,iBAAKsD,UAAW1H,CAAAA,aAAW;AACzBd,qBAAOyG,oBAAoB,SAASqD,OAAO;AAC3C,kBAAIhJ,YAAW,KAAKoJ,MAAM;AACxBhF,wBAAQpE,QAAO;cAChB;YACH,CAAC;UACH,CAAC;QACF;AACD,eAAOA;MACT;MAEA,IAAIoJ,OAAI;AACN,eAAO,KAAKX,eAAe9G,SAAS;MACtC;MAEA,IAAIsI,gBAAa;AACfrZ,kBACE,KAAKgL,SAAS,QAAQ,KAAKwN,MAC3B,2DAA2D;AAG7D,eAAO7Q,OAAOsJ,QAAQ,KAAKjG,IAAI,EAAEtE,OAC/B,CAAC+J,KAAG6I,UAAA;AAAA,cAAE,CAACpY,KAAKjB,KAAK,IAACqZ;AAAA,iBAChB3R,OAAOC,OAAO6I,KAAK;YACjB,CAACvP,GAAG,GAAGqK,qBAAqBtL,KAAK;WAClC;WACH,CAAA,CAAE;MAEN;MAEA,IAAIsZ,cAAW;AACb,eAAOvB,MAAMtN,KAAK,KAAKmN,cAAc;MACvC;IACD;AA4BM,IAAMP,QAAuB,SAAvBA,OAAwBtM,OAAMC,MAAa;AAAA,UAAbA,SAAI,QAAA;AAAJA,eAAO,CAAA;MAAE;AAClD,UAAI0M,eAAe,OAAO1M,SAAS,WAAW;QAAEE,QAAQF;MAAI,IAAKA;AAEjE,aAAO,IAAIoM,aAAarM,OAAM2M,YAAY;IAC5C;AAWO,IAAM3H,WAA6B,SAA7BA,UAA8BrD,KAAK1B,MAAc;AAAA,UAAdA,SAAI,QAAA;AAAJA,eAAO;MAAG;AACxD,UAAI0M,eAAe1M;AACnB,UAAI,OAAO0M,iBAAiB,UAAU;AACpCA,uBAAe;UAAExM,QAAQwM;;iBAChB,OAAOA,aAAaxM,WAAW,aAAa;AACrDwM,qBAAaxM,SAAS;MACvB;AAED,UAAIsE,UAAU,IAAIkG,QAAQgC,aAAalI,OAAO;AAC9CA,cAAQ4G,IAAI,YAAY1J,GAAG;AAE3B,aAAO,IAAI4C,SAAS,MAAInO,SAAA,CAAA,GACnBuW,cAAY;QACflI;MAAO,CAAA,CACR;IACH;IAOa8H,mBAAqCA,CAAC5K,KAAK1B,SAAQ;AAC9D,UAAIuE,WAAWQ,SAASrD,KAAK1B,IAAI;AACjCuE,eAASC,QAAQ4G,IAAI,2BAA2B,MAAM;AACtD,aAAO7G;IACT;IAQa7J,UAA4BA,CAACgH,KAAK1B,SAAQ;AACrD,UAAIuE,WAAWQ,SAASrD,KAAK1B,IAAI;AACjCuE,eAASC,QAAQ4G,IAAI,mBAAmB,MAAM;AAC9C,aAAO7G;IACT;IAgBa6F,0BAAiB;MAO5BuC,YACEzM,QACAQ,YACAX,OACAY,UAAgB;AAAA,YAAhBA,aAAQ,QAAA;AAARA,qBAAW;QAAK;AAEhB,aAAKT,SAASA;AACd,aAAKQ,aAAaA,cAAc;AAChC,aAAKC,WAAWA;AAChB,YAAIZ,iBAAgB7K,OAAO;AACzB,eAAK6K,OAAOA,MAAKpK,SAAQ;AACzB,eAAKiI,QAAQmC;QACd,OAAM;AACL,eAAKA,OAAOA;QACb;MACH;IACD;ACt/BD,IAAMwM,0BAAgD,CACpD,QACA,OACA,SACA,QAAQ;AAEV,IAAMN,uBAAuB,IAAIrG,IAC/B2G,uBAAuB;AAGzB,IAAMC,yBAAuC,CAC3C,OACA,GAAGD,uBAAuB;AAE5B,IAAMP,sBAAsB,IAAIpG,IAAgB4G,sBAAsB;AAEtE,IAAMZ,sBAAsB,oBAAIhG,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAgC7D,IAAMsF,qBAAqB;AAE3B,IAAMlK,4BAAyD/J,YAAW;MACxE8J,kBAAkBwN,QAAQtX,MAAM8J,gBAAgB;IACjD;IAwoFYoC,yBAAyBqL,OAAO,UAAU;;;;;AC91GvD,SAAS,aAAa,OAAO;AAC3B,SAAO,UAAU,WAAW,eAAe,UAAU,WAAW,cAAc,UAAU,WAAW;AACrG;AArBA,IAaI;AAbJ;AAAA;AAaA,IAAI,aAA0B,SAAUC,aAAY;AAClD,MAAAA,YAAW,aAAa,IAAI;AAC5B,MAAAA,YAAW,YAAY,IAAI;AAC3B,MAAAA,YAAW,MAAM,IAAI;AACrB,aAAOA;AAAA,IACT,EAAE,CAAC,CAAC;AAAA;AAAA;;;ACqCJ,SAAS,cAAc,OAAO,YAAY;AACxC,MAAI,iBAAiB,SAAS,eAAe,WAAW,aAAa;AACnE,QAAI,YAAY,IAAI,MAAM,yBAAyB;AACnD,cAAU,QAAQ;AAClB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,YAAY;AAC1C,SAAO,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM;AAC9D,WAAO,OAAO,OAAO,KAAK;AAAA,MACxB,CAAC,OAAO,GAAG,cAAc,OAAO,UAAU;AAAA,IAC5C,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACP;AAKA,SAAS,eAAe,OAAO,YAAY;AACzC,MAAI,YAAY,cAAc,OAAO,UAAU;AAC/C,SAAO;AAAA,IACL,SAAS,UAAU;AAAA,IACnB,OAAO,UAAU;AAAA,EACnB;AACF;AACA,SAAS,gBAAgB,QAAQ,YAAY;AAC3C,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,UAAU,OAAO,QAAQ,MAAM;AACnC,MAAI,aAAa,CAAC;AAClB,WAAS,CAAC,KAAK,GAAG,KAAK,SAAS;AAG9B,QAAI,qBAAqB,GAAG,GAAG;AAC7B,iBAAW,GAAG,IAAI;AAAA,QAChB,GAAG;AAAA,QACH,QAAQ;AAAA,MACV;AAAA,IACF,WAAW,eAAe,OAAO;AAC/B,UAAI,YAAY,cAAc,KAAK,UAAU;AAC7C,iBAAW,GAAG,IAAI;AAAA,QAChB,SAAS,UAAU;AAAA,QACnB,OAAO,UAAU;AAAA,QACjB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,QAKR,GAAI,UAAU,SAAS,UAAU;AAAA,UAC/B,WAAW,UAAU;AAAA,QACvB,IAAI,CAAC;AAAA,MACP;AAAA,IACF,OAAO;AACL,iBAAW,GAAG,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AAhHA;AAAA;AAUA;AACA;AAAA;AAAA;;;ACmEA,SAASC,gBAAe,OAAO;AAC7B,MAAI,WAAW;AACf,SAAO,YAAY,OAAO,aAAa,YAAY,OAAO,SAAS,SAAS,YAAY,OAAO,SAAS,cAAc,cAAc,OAAO,SAAS,WAAW,cAAc,OAAO,SAAS,gBAAgB;AAC/M;AACA,SAASC,YAAW,OAAO;AACzB,SAAO,SAAS,QAAQ,OAAO,MAAM,WAAW,YAAY,OAAO,MAAM,eAAe,YAAY,OAAO,MAAM,YAAY,YAAY,OAAO,MAAM,SAAS;AACjK;AAEA,SAAS,qBAAqB,YAAY;AACxC,SAAOC,qBAAoB,IAAI,UAAU;AAC3C;AACA,SAASC,oBAAmB,UAAU;AACpC,SAAO,qBAAqB,SAAS,MAAM;AAC7C;AACA,SAASC,kBAAiB,OAAO;AAC/B,SAAO,SAAS,QAAQ,OAAO,MAAM,SAAS,cAAc,MAAM,aAAa;AACjF;AAMA,SAAS,6BAA6B,cAAc,QAAQ,YAAY;AACtE,MAAI,UAAU,IAAI,YAAY;AAC9B,MAAI,SAAS,IAAI,eAAe;AAAA,IAC9B,MAAM,MAAM,YAAY;AACtB,UAAI,eAAe,CAAC;AACpB,UAAI,kBAAkB,CAAC;AACvB,eAAS,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,aAAa,IAAI,GAAG;AAC1D,YAAIA,kBAAiB,KAAK,GAAG;AAC3B,uBAAa,GAAG,IAAI,GAAG,iCAAiC,GAAG,GAAG;AAC9D,cAAI,OAAO,MAAM,UAAU,eAAe,OAAO,MAAM,WAAW,aAAa;AAC7E,4BAAgB,KAAK,GAAG;AAAA,UAC1B;AAAA,QACF,OAAO;AACL,uBAAa,GAAG,IAAI;AAAA,QACtB;AAAA,MACF;AAGA,iBAAW,QAAQ,QAAQ,OAAO,KAAK,UAAU,YAAY,IAAI,MAAM,CAAC;AACxE,eAAS,kBAAkB,iBAAiB;AAC1C,8BAAsB,YAAY,SAAS,gBAAgB,aAAa,KAAK,cAAc,GAAG,UAAU;AAAA,MAC1G;AACA,UAAI,cAAc,aAAa,UAAU,CAAC,SAAS,eAAe;AAChE,YAAI,YAAY;AACd,gCAAsB,YAAY,SAAS,YAAY,aAAa,KAAK,UAAU,GAAG,UAAU;AAAA,QAClG;AAAA,MACF,CAAC;AACD,YAAM,aAAa,YAAY,MAAM;AACrC,kBAAY;AACZ,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,sBAAsB,YAAY,SAAS,YAAY,SAAS,YAAY;AACnF,MAAI,YAAY,SAAS;AACvB,eAAW,QAAQ,QAAQ,OAAO,WAAW,KAAK,UAAU;AAAA,MAC1D,CAAC,UAAU,GAAG,QAAQ,kBAAkB,QAAQ,eAAe,QAAQ,QAAQ,UAAU,IAAI,QAAQ;AAAA,IACvG,CAAC,IAAI,MAAM,CAAC;AAAA,EACd,OAAO;AACL,eAAW,QAAQ,QAAQ,OAAO,UAAU,KAAK,UAAU;AAAA,MACzD,CAAC,UAAU,GAAG,QAAQ,SAAS;AAAA,IACjC,CAAC,IAAI,MAAM,CAAC;AAAA,EACd;AACF;AAhJA,IAgCMC,OAaAC,QASAC,WAUAC,UAWAC,mBAUAP,sBAcA;AAnGN;AAAA;AAUA;AACA;AAqBA,IAAMG,QAAO,CAACK,OAAM,OAAO,CAAC,MAAM;AAChC,aAAO,KAAOA,OAAM,IAAI;AAAA,IAC1B;AAWA,IAAMJ,SAAQ,CAACI,OAAM,OAAO,CAAC,MAAM;AACjC,aAAO,MAAQA,OAAM,IAAI;AAAA,IAC3B;AAOA,IAAMH,YAAW,CAAC,KAAK,OAAO,QAAQ;AACpC,aAAO,SAAW,KAAK,IAAI;AAAA,IAC7B;AAQA,IAAMC,WAAU,CAAC,KAAK,OAAO,QAAQ;AACnC,aAAO,QAAU,KAAK,IAAI;AAAA,IAC5B;AASA,IAAMC,oBAAmB,CAAC,KAAK,OAAO,QAAQ;AAC5C,aAAO,iBAAmB,KAAK,IAAI;AAAA,IACrC;AAQA,IAAMP,uBAAsB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAc7D,IAAM,oCAAoC;AAAA;AAAA;;;AChD1C,SAAS,QAAQ,OAAO;AACtB,QAAM,EAAE,QAAQ,IAAI;AACpB,QAAM,WAAW,QAAQ,IAAI,KAAK;AAClC,MAAI;AACF,WAAO,CAAC,QAAQ;AAClB,MAAI,UAAU;AACZ,WAAO;AACT,MAAI,UAAU;AACZ,WAAO;AACT,MAAI,OAAO,MAAM,KAAK;AACpB,WAAO;AACT,MAAI,UAAU,OAAO;AACnB,WAAO;AACT,MAAI,UAAU,OAAO;AACnB,WAAO;AACT,MAAI,UAAU,KAAK,IAAI,QAAQ;AAC7B,WAAO;AACT,QAAM,QAAQ,KAAK;AACnB,UAAQ,IAAI,OAAO,KAAK;AACxB,YAAU,KAAK,MAAM,OAAO,KAAK;AACjC,SAAO;AACT;AACA,SAAS,UAAU,OAAO,OAAO;AAC/B,QAAM,EAAE,UAAU,SAAS,YAAY,IAAI;AAC3C,QAAM,MAAM,KAAK;AACjB,QAAM,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC;AAC7B,SAAO,MAAM,SAAS,GAAG;AACvB,UAAM,CAAC,QAAQ,MAAM,IAAI,MAAM,IAAI;AACnC,UAAM,cAAc,CAAC,QAAQ,OAAO,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC,CAAC,KAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG;AAC9H,QAAI,QAAQ;AACZ,YAAQ,OAAO,QAAQ;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI,MAAM,IAAI,KAAK,UAAU,MAAM;AACnC;AAAA,MACF,KAAK;AACH,YAAI,MAAM,IAAI,KAAK,WAAW,MAAM,MAAM;AAC1C;AAAA,MACF,KAAK,UAAU;AACb,cAAM,SAAS,OAAO,OAAO,MAAM;AACnC,YAAI,CAAC,QAAQ;AACX,kBAAQ,IAAI;AAAA,YACV;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,MAAM,IAAI,KAAK,WAAW,KAAK,KAAK,UAAU,MAAM,CAAC;AAAA,QAC3D;AACA;AAAA,MACF;AAAA,MACA,KAAK,UAAU;AACb,YAAI,CAAC,QAAQ;AACX,cAAI,MAAM,IAAI,GAAG,IAAI;AACrB;AAAA,QACF;AACA,cAAM,UAAU,MAAM,QAAQ,MAAM;AACpC,YAAI,gBAAgB;AACpB,YAAI,CAAC,WAAW,SAAS;AACvB,qBAAW,UAAU,SAAS;AAC5B,kBAAM,eAAe,OAAO,MAAM;AAClC,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,8BAAgB;AAChB,oBAAM,CAAC,kBAAkB,GAAG,IAAI,IAAI;AACpC,kBAAI,MAAM,IAAI,IAAI,KAAK,UAAU,gBAAgB,CAAC;AAClD,kBAAI,KAAK,SAAS,GAAG;AACnB,oBAAI,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,cACrE;AACA,kBAAI,MAAM,KAAK;AACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,eAAe;AAClB,cAAI,SAAS,UAAU,MAAM;AAC7B,cAAI,SAAS;AACX,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AACjC,yBAAW,IAAI,MAAM,OAAO,KAAK,SAAS,QAAQ,KAAK,MAAM,OAAO,CAAC,CAAC,IAAI;AAC5E,gBAAI,MAAM,IAAI,GAAG,MAAM;AAAA,UACzB,WAAW,kBAAkB,MAAM;AACjC,gBAAI,MAAM,IAAI,KAAK,SAAS,KAAK,OAAO,QAAQ,CAAC;AAAA,UACnD,WAAW,kBAAkB,KAAK;AAChC,gBAAI,MAAM,IAAI,KAAK,QAAQ,KAAK,KAAK,UAAU,OAAO,IAAI,CAAC;AAAA,UAC7D,WAAW,kBAAkB,QAAQ;AACnC,gBAAI,MAAM,IAAI,KAAK,WAAW,KAAK,KAAK;AAAA,cACtC,OAAO;AAAA,YACT,CAAC,IAAI,KAAK,UAAU,OAAO,KAAK,CAAC;AAAA,UACnC,WAAW,kBAAkB,KAAK;AAChC,gBAAI,OAAO,OAAO,GAAG;AACnB,kBAAI,MAAM,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,QAAQ,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YAC7F,OAAO;AACL,kBAAI,MAAM,IAAI,KAAK,QAAQ;AAAA,YAC7B;AAAA,UACF,WAAW,kBAAkB,KAAK;AAChC,gBAAI,OAAO,OAAO,GAAG;AACnB,kBAAI,MAAM,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AAAA,gBAC9D,QAAQ,KAAK,MAAM,CAAC;AAAA,gBACpB,QAAQ,KAAK,MAAM,CAAC;AAAA,cACtB,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACd,OAAO;AACL,kBAAI,MAAM,IAAI,KAAK,QAAQ;AAAA,YAC7B;AAAA,UACF,WAAW,kBAAkB,SAAS;AACpC,gBAAI,MAAM,IAAI,KAAK,YAAY,KAAK,MAAM;AAC1C,qBAAS,MAAM,IAAI;AAAA,UACrB,WAAW,kBAAkB,OAAO;AAClC,gBAAI,MAAM,IAAI,KAAK,UAAU,KAAK,KAAK,UAAU,OAAO,OAAO,CAAC;AAChE,gBAAI,OAAO,SAAS,SAAS;AAC3B,kBAAI,MAAM,KAAK,IAAI,KAAK,UAAU,OAAO,IAAI,CAAC;AAAA,YAChD;AACA,gBAAI,MAAM,KAAK;AAAA,UACjB,WAAW,OAAO,eAAe,MAAM,MAAM,MAAM;AACjD,gBAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,YAAY,MAAM,CAAC;AAAA,UAC9D,WAAW,cAAc,MAAM,GAAG;AAChC,gBAAI,MAAM,IAAI,IAAI,YAAY,MAAM,CAAC;AAAA,UACvC,OAAO;AACL,oBAAQ,IAAI,MAAM,qCAAqC;AAAA,UACzD;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,UAAU,MAAM,QAAQ,MAAM;AACpC,YAAI,gBAAgB;AACpB,YAAI,CAAC,WAAW,SAAS;AACvB,qBAAW,UAAU,SAAS;AAC5B,kBAAM,eAAe,OAAO,MAAM;AAClC,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,8BAAgB;AAChB,oBAAM,CAAC,kBAAkB,GAAG,IAAI,IAAI;AACpC,kBAAI,MAAM,IAAI,IAAI,KAAK,UAAU,gBAAgB,CAAC;AAClD,kBAAI,KAAK,SAAS,GAAG;AACnB,oBAAI,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,cACrE;AACA,kBAAI,MAAM,KAAK;AACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,eAAe;AAClB,kBAAQ,IAAI,MAAM,2CAA2C;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO;AACT,UAAI,gBAAgB;AACpB,UAAI,aAAa;AACf,mBAAW,UAAU,aAAa;AAChC,gBAAM,eAAe,OAAO,MAAM;AAClC,cAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,4BAAgB;AAChB,kBAAM,CAAC,kBAAkB,GAAG,IAAI,IAAI;AACpC,gBAAI,MAAM,IAAI,IAAI,KAAK,UAAU,gBAAgB,CAAC;AAClD,gBAAI,KAAK,SAAS,GAAG;AACnB,kBAAI,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACrE;AACA,gBAAI,MAAM,KAAK;AACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,eAAe;AAClB,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,cAAc,OAAO;AAC5B,QAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,SAAO,UAAU,OAAO,aAAa,UAAU,QAAQ,OAAO,oBAAoB,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,MAAM;AACjH;AAoUA,SAAS,OAAO,OAAO,SAAS;AAC9B,QAAM,EAAE,SAAS,aAAa,OAAO,IAAI,WAAW,CAAC;AACrD,QAAM,UAAU;AAAA,IACd,UAAU,CAAC;AAAA,IACX,OAAO;AAAA,IACP,SAAyB,oBAAI,IAAI;AAAA,IACjC,aAAa,CAAC;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI,gBAAgB;AACpB,QAAM,WAAW,IAAI,eAAe;AAAA,IAClC,MAAM,MAAM,YAAY;AACtB,YAAM,KAAK,QAAQ,KAAK,SAAS,KAAK;AACtC,UAAI,MAAM,QAAQ,EAAE,GAAG;AACrB,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AACA,UAAI,KAAK,GAAG;AACV,mBAAW,QAAQ,YAAY,OAAO,GAAG,EAAE;AAAA,CAClD,CAAC;AAAA,MACI,OAAO;AACL,mBAAW;AAAA,UACT,YAAY,OAAO,IAAI,QAAQ,YAAY,KAAK,GAAG,CAAC;AAAA,CAC7D;AAAA,QACO;AACA,wBAAgB,QAAQ,YAAY,SAAS;AAAA,MAC/C;AACA,YAAM,eAA+B,oBAAI,QAAQ;AACjD,UAAI,OAAO,KAAK,QAAQ,QAAQ,EAAE,QAAQ;AACxC,YAAI;AACJ,cAAM,cAAc,IAAI,QAAQ,CAAC,SAAS,WAAW;AACnD,qBAAW;AACX,cAAI,QAAQ;AACV,kBAAM,gBAAgB,MAAM,OAAO,OAAO,UAAU,IAAI,MAAM,qBAAqB,CAAC;AACpF,gBAAI,OAAO,SAAS;AAClB,4BAAc;AAAA,YAChB,OAAO;AACL,qBAAO,iBAAiB,SAAS,CAAC,UAAU;AAC1C,8BAAc;AAAA,cAChB,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO,OAAO,KAAK,QAAQ,QAAQ,EAAE,SAAS,GAAG;AAC/C,qBAAW,CAAC,YAAY,QAAQ,KAAK,OAAO;AAAA,YAC1C,QAAQ;AAAA,UACV,GAAG;AACD,gBAAI,aAAa,IAAI,QAAQ;AAC3B;AACF,yBAAa;AAAA;AAAA,cAEX,QAAQ,SAAS,OAAO,UAAU,CAAC,IAAI,QAAQ,KAAK;AAAA,gBAClD;AAAA,gBACA;AAAA,cACF,CAAC,EAAE;AAAA,gBACD,CAAC,aAAa;AACZ,wBAAM,MAAM,QAAQ,KAAK,SAAS,QAAQ;AAC1C,sBAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,+BAAW;AAAA,sBACT,YAAY;AAAA,wBACV,GAAG,YAAY,GAAG,UAAU,OAAO,sBAAsB,KAAK,IAAI,CAAC,CAAC;AAAA;AAAA,sBAEtE;AAAA,oBACF;AACA,4BAAQ;AACR;AAAA,kBACF,WAAW,MAAM,GAAG;AAClB,+BAAW;AAAA,sBACT,YAAY;AAAA,wBACV,GAAG,YAAY,GAAG,UAAU,IAAI,GAAG;AAAA;AAAA,sBAErC;AAAA,oBACF;AAAA,kBACF,OAAO;AACL,0BAAM,SAAS,QAAQ,YAAY,MAAM,gBAAgB,CAAC,EAAE,KAAK,GAAG;AACpE,+BAAW;AAAA,sBACT,YAAY;AAAA,wBACV,GAAG,YAAY,GAAG,UAAU,KAAK,MAAM;AAAA;AAAA,sBAEzC;AAAA,oBACF;AACA,oCAAgB,QAAQ,YAAY,SAAS;AAAA,kBAC/C;AAAA,gBACF;AAAA,gBACA,CAAC,WAAW;AACV,sBAAI,CAAC,UAAU,OAAO,WAAW,YAAY,EAAE,kBAAkB,QAAQ;AACvE,6BAAS,IAAI,MAAM,2BAA2B;AAAA,kBAChD;AACA,wBAAM,MAAM,QAAQ,KAAK,SAAS,MAAM;AACxC,sBAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,+BAAW;AAAA,sBACT,YAAY;AAAA,wBACV,GAAG,UAAU,GAAG,UAAU,OAAO,sBAAsB,KAAK,IAAI,CAAC,CAAC;AAAA;AAAA,sBAEpE;AAAA,oBACF;AACA,4BAAQ;AACR;AAAA,kBACF,WAAW,MAAM,GAAG;AAClB,+BAAW;AAAA,sBACT,YAAY,OAAO,GAAG,UAAU,GAAG,UAAU,IAAI,GAAG;AAAA,CACzE;AAAA,oBACmB;AAAA,kBACF,OAAO;AACL,0BAAM,SAAS,QAAQ,YAAY,MAAM,gBAAgB,CAAC,EAAE,KAAK,GAAG;AACpE,+BAAW;AAAA,sBACT,YAAY;AAAA,wBACV,GAAG,UAAU,GAAG,UAAU,KAAK,MAAM;AAAA;AAAA,sBAEvC;AAAA,oBACF;AACA,oCAAgB,QAAQ,YAAY,SAAS;AAAA,kBAC/C;AAAA,gBACF;AAAA,cACF,EAAE,QAAQ,MAAM;AACd,uBAAO,QAAQ,SAAS,OAAO,UAAU,CAAC;AAAA,cAC5C,CAAC;AAAA,YACH;AAAA,UACF;AACA,gBAAM,QAAQ,KAAK,OAAO,OAAO,QAAQ,QAAQ,CAAC;AAAA,QACpD;AACA,iBAAS;AAAA,MACX;AACA,YAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,QAAQ,CAAC;AACjD,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAnqBA,IACI,MACA,KACA,mBACA,eACA,MACA,mBACA,WACA,aACA,WACA,YACA,UACA,kBACA,cACA,aACA,UACA,aACA,UACA,wBAuMA;AAzNJ;AAAA;AACA,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,OAAO;AACX,IAAI,oBAAoB;AACxB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,yBAAyB;AAuM7B,IAAI,mBAAmB,OAAO,oBAAoB,OAAO,SAAS,EAAE,KAAK,EAAE,KAAK,IAAI;AAAA;AAAA;;;AC7MpF,SAAS,mBAAmB,OAAO,SAAS;AAC1C,MAAI,cAAc,QAAQ,SAAS,QAAQ,QAAQ,UAAU,OAAK,QAAQ,OAAO,EAAE,MAAM,EAAE,CAAC,IAAI;AAChG,MAAI,UAAU,eAAe,IAAI,QAAQ,QAAQ,MAAM,GAAG,cAAc,CAAC,IAAI,QAAQ;AACrF,MAAI;AACJ,MAAI,eAAe,GAAG;AAGpB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,YAAQ,QAAQ,MAAM,WAAW,EAAE,KAAK,WAAS;AAC/C,UAAI,KAAK,MAAM,MAAM;AACrB,UAAI,cAAc,EAAE,MAAM,CAAC,cAAc,WAAW,EAAE,MAAM,SAAY;AACtE,uBAAe,cAAc,EAAE;AAAA,MACjC,WAAW,cAAc,EAAE,KAAK,WAAW,EAAE,MAAM,QAAW;AAC5D,uBAAe,cAAc,EAAE;AAAA,MACjC;AACA,aAAO,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH;AACA,SAAO,QAAQ,OAAO,CAAC,eAAe,OAAO,QAAQ;AACnD,QAAI;AAAA,MACF;AAAA,IACF,IAAI,MAAM;AACV,QAAI,cAAc,MAAM,OAAO,EAAE,EAAE;AACnC,QAAI,gBAAgB,QAAQ,cAAc,EAAE,KAAK,IAAI,QAAQ;AAC7D,QAAI,gBAAgB,QAAQ,cAAc,EAAE,KAAK,IAAI,QAAQ;AAI7D,QAAI,sBAAsB,gBAAgB,UAAa,QAAQ,QAAQ,SAAS;AAIhF,QAAI,sBAAsB,uBAAuB,iBAAiB,iBAAiB,iBAAiB;AAGpG,QAAI,YAAY,WAAW,MAAM;AAC/B,UAAIS,WAAU,IAAI,QAAQ,aAAa;AACvC,UAAI,qBAAqB;AACvB,uBAAe,cAAcA,QAAO;AAAA,MACtC;AACA,qBAAe,eAAeA,QAAO;AACrC,qBAAe,eAAeA,QAAO;AACrC,aAAOA;AAAA,IACT;AACA,QAAI,UAAU,IAAI,QAAQ,YAAY,UAAU,OAAO,YAAY,YAAY,aAAa,YAAY,QAAQ;AAAA,MAC9G;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,sBAAsB,eAAe;AAAA,IACrD,CAAC,IAAI,YAAY,UAAU,MAAS;AAIpC,QAAI,qBAAqB;AACvB,qBAAe,cAAc,OAAO;AAAA,IACtC;AACA,mBAAe,eAAe,OAAO;AACrC,mBAAe,eAAe,OAAO;AACrC,mBAAe,eAAe,OAAO;AACrC,WAAO;AAAA,EACT,GAAG,IAAI,QAAQ,CAAC;AAClB;AACA,SAAS,eAAe,eAAe,cAAc;AACnD,MAAI,wBAAwB,cAAc,IAAI,YAAY;AAC1D,MAAI,uBAAuB;AACzB,QAAI;AACJ,QAAI,cAAU,6CAAmB,qBAAqB;AAEtD,QAAI,eAAe,IAAI,KAAK,wBAAwB,aAAa,kBAAkB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,YAAY,CAAC;AAC/K,YAAQ,QAAQ,YAAU;AACxB,UAAI,CAAC,aAAa,IAAI,MAAM,GAAG;AAC7B,qBAAa,OAAO,cAAc,MAAM;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AA5FA,IAUA;AAVA;AAAA;AAUA,+BAAmC;AAAA;AAAA;;;ACcnC,SAAS,2BAA2B;AAAA,EAClC;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,EACF,MAAM;AAEJ,QAAI,uBAAuB,QAAQ,WAAW,OAAO;AACnD,aAAO,CAAC;AAAA,IACV;AAGA,QAAI,gBAAgB,eAAe,QAAQ,OAAO,OAAK,aAAa,SAAS,EAAE,MAAM,EAAE,CAAC,IAAI;AAC5F,QAAI,UAAU,MAAM,QAAQ,IAAI,cAAc,IAAI,WAAS,MAAM,QAAQ,CAAC,CAAC;AAC3E,WAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,OAAO,OAAO,KAAK;AAAA,MAC3D,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,GAAG;AAAA,IAC/B,CAAC,GAAG,CAAC,CAAC;AAAA,EACR;AACF;AACA,eAAe,kBAAkB,OAAO,YAAY,eAAe,SAAS,YAAY,aAAa,aAAa;AAChH,MAAI;AACF,QAAI,iBAAiB,IAAI,QAAQ,YAAY;AAAA,MAC3C,QAAQ,QAAQ;AAAA,MAChB,MAAM,QAAQ;AAAA,MACd,SAAS,QAAQ;AAAA,MACjB,QAAQ,QAAQ;AAAA,MAChB,GAAI,QAAQ,OAAO;AAAA,QACjB,QAAQ;AAAA,MACV,IAAI;AAAA,IACN,CAAC;AACD,QAAI,SAAS,MAAM,cAAc,MAAM,gBAAgB;AAAA,MACrD,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,cAAc,2BAA2B;AAAA,QACvC,qBAAqB;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAID,QAAIC,YAAW,MAAM,GAAG;AACtB,aAAO;AAAA,QACL,QAAQ,uBAAuB,OAAO,QAAQ,OAAO,SAAS,MAAM,QAAQ;AAAA,QAC5E,SAAS,OAAO;AAAA,QAChB,QAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,UAAU;AACd,QAAI,UAAU,mBAAmB,OAAO,OAAO;AAC/C,QAAI,qBAAqB,QAAQ,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG;AACvE,aAAO;AAAA,QACL,QAAQ,uBAAuB,QAAQ,YAAY,SAAS,MAAM,QAAQ;AAAA,QAC1E;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAGA,QAAI,QAAQ,QAAQ;AAClB,aAAO,OAAO,QAAQ,MAAM,EAAE,QAAQ,SAAO;AAE3C,YAAI,CAAC,qBAAqB,GAAG,KAAK,IAAI,OAAO;AAC3C,sBAAY,GAAG;AAAA,QACjB;AAAA,MACF,CAAC;AACD,cAAQ,SAAS,eAAe,QAAQ,QAAQ,UAAU;AAAA,IAC5D;AACA,QAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,0BAAoB;AAAA,QAClB,OAAO,OAAO,OAAO,QAAQ,MAAM,EAAE,CAAC;AAAA,MACxC;AAAA,IACF,OAAO;AACL,0BAAoB;AAAA,QAClB,MAAM,OAAO,OAAO,QAAQ,cAAc,CAAC,CAAC,EAAE,CAAC;AAAA,MACjD;AAAA,IACF;AACA,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,MACA,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF,SAAS,OAAO;AACd,gBAAY,KAAK;AAEjB,WAAO;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,SAAS,IAAI,QAAQ;AAAA,MACrB,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,eAAe,mBAAmB,OAAO,YAAY,eAAe,SAAS,YAAY,aAAa,aAAa;AACjH,MAAI;AACF,QAAI;AACJ,QAAI,iBAAiB,IAAI,QAAQ,YAAY;AAAA,MAC3C,SAAS,QAAQ;AAAA,MACjB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,QAAI,iBAAiB,wBAAwB,IAAI,IAAI,QAAQ,GAAG,EAAE,aAAa,IAAI,SAAS,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,MAAM,GAAG,MAAM;AAC5L,QAAI,SAAS,MAAM,cAAc,MAAM,gBAAgB;AAAA,MACrD,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,cAAc,2BAA2B;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,QAAIA,YAAW,MAAM,GAAG;AACtB,aAAO;AAAA,QACL,QAAQ;AAAA,UACN,CAAC,yBAAyB,GAAG,uBAAuB,OAAO,QAAQ,OAAO,SAAS,MAAM,QAAQ;AAAA,QACnG;AAAA,QACA,SAAS,OAAO;AAAA,QAChB,QAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,UAAU;AACd,QAAI,UAAU,mBAAmB,OAAO,OAAO;AAC/C,QAAI,qBAAqB,QAAQ,UAAU,KAAK,QAAQ,IAAI,UAAU,GAAG;AACvE,aAAO;AAAA,QACL,QAAQ;AAAA,UACN,CAAC,yBAAyB,GAAG,uBAAuB,QAAQ,YAAY,SAAS,MAAM,QAAQ;AAAA,QACjG;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAGA,QAAI,QAAQ,QAAQ;AAClB,aAAO,OAAO,QAAQ,MAAM,EAAE,QAAQ,SAAO;AAE3C,YAAI,CAAC,qBAAqB,GAAG,KAAK,IAAI,OAAO;AAC3C,sBAAY,GAAG;AAAA,QACjB;AAAA,MACF,CAAC;AACD,cAAQ,SAAS,eAAe,QAAQ,QAAQ,UAAU;AAAA,IAC5D;AAIA,QAAI,UAAU,CAAC;AACf,QAAI,gBAAgB,eAAe,QAAQ,QAAQ,OAAO,OAAK,EAAE,MAAM,UAAU,aAAa,SAAS,EAAE,MAAM,EAAE,CAAC,IAAI,QAAQ;AAC9H,kBAAc,QAAQ,OAAK;AACzB,UAAI,qBAAqB;AACzB,UAAIC,SAAQ,sBAAsB,QAAQ,gBAAgB,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,EAAE,MAAM,EAAE;AAC1I,UAAI,SAAS,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,EAAE,MAAM,EAAE;AAC3H,UAAI,UAAU,QAAW;AACvB,gBAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,UACpB;AAAA,QACF;AAAA,MACF,WAAWA,UAAS,QAAW;AAC7B,gBAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,UACpB,MAAAA;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,MACA,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF,SAAS,OAAO;AACd,gBAAY,KAAK;AAEjB,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,MAAM;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS,IAAI,QAAQ;AAAA,MACrB,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,QAAQ,SAAS,UAAU;AACzD,MAAIC,YAAW,QAAQ,IAAI,UAAU;AACrC,MAAI,UAAU;AACZ,IAAAA,YAAW,cAAcA,WAAU,QAAQ,KAAKA;AAAA,EAClD;AACA,SAAO;AAAA,IACL,UAAAA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,IAAI,oBAAoB,KAAK,QAAQ,IAAI,YAAY;AAAA;AAAA,IAC7D,QAAQ,QAAQ,IAAI,yBAAyB;AAAA,IAC7C,SAAS,QAAQ,IAAI,iBAAiB;AAAA,EACxC;AACF;AAIA,SAAS,qBAAqBD,OAAM,eAAe,eAAe,YAAY;AAC5E,MAAI,aAAa,IAAI,gBAAgB;AAOrC,MAAI,YAAY,WAAW,MAAM,WAAW,MAAM,IAAI,MAAM,gBAAgB,CAAC,GAAG,OAAO,kBAAkB,WAAW,gBAAgB,IAAI;AACxI,gBAAc,iBAAiB,SAAS,MAAM,aAAa,SAAS,CAAC;AACrE,SAAO,OAAOA,OAAM;AAAA,IAClB,QAAQ,WAAW;AAAA,IACnB,SAAS,CAAC,WAAS;AAIjB,UAAI,iBAAiB,OAAO;AAC1B,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,eAAe,WAAW,aAAa,cAAc,OAAO,UAAU,IAAI;AAC9E,eAAO,CAAC,kBAAkB,MAAM,SAAS,KAAK;AAAA,MAChD;AACA,UAAI,iBAAiB,mBAA0B;AAC7C,YAAI;AAAA,UACF,MAAAA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,CAAC,iBAAiBA,OAAM,QAAQ,UAAU;AAAA,MACnD;AACA,UAAI,SAAS,OAAO,UAAU,YAAY,6BAA6B,OAAO;AAC5E,eAAO,CAAC,uBAAuB,MAAM,yBAAyB,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,IACD,aAAa,CAAC,WAAS;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,OAAO,UAAU,SAAU;AAC/B,aAAO,CAAC,4BAA4B,OAAO,YAAY,OAAO,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC/E,GAAG,MAAM,CAAC,qBAAqB,CAAC;AAAA,EAClC,CAAC;AACH;AACA,SAASA,MAAK,OAAO,MAAM;AACzB,SAAO,KAAO,OAAO,IAAI;AAC3B;AAjRA,IAiBM,2BAMA;AAvBN;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAM,4BAA4B,OAAO,qBAAqB;AAM9D,IAAM,+BAA+B;AAAA;AAAA;;;ACbrC,SAAS,wBAAwB,UAAU;AACzC,SAAO,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,MAAM,YAAY;AACrD,SAAK,OAAO,IAAI,SAAS,OAAO,EAAE;AAClC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAfA;AAAA;AAAA;AAAA;;;ACUA,SAASE,WAAU,OAAO,SAAS;AACjC,MAAI,UAAU,SAAS,UAAU,QAAQ,OAAO,UAAU,aAAa;AACrE,YAAQ,MAAM,4GAA4G;AAC1H,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AAfA;AAAA;AAAA;AAAA;;;ACYA,SAAS,kBAAkB,QAAQ,UAAU,UAAU;AACrD,MAAI,UAAU,YAAY,QAAQ,UAAU,QAAQ;AACpD,MAAI,CAAC,QAAS,QAAO;AACrB,SAAO,QAAQ,IAAI,YAAU;AAAA,IAC3B,QAAQ,MAAM;AAAA,IACd,UAAU,MAAM;AAAA,IAChB,OAAO,MAAM;AAAA,EACf,EAAE;AACJ;AApBA;AAAA;AAUA;AAAA;AAAA;;;ACaA,eAAe,gBAAgB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,SAAS,MAAM,OAAO;AAAA,IACxB,SAAS,cAAc,iBAAiB,gBAAgB,OAAO,CAAC,IAAI,eAAe,gBAAgB,OAAO,CAAC;AAAA,IAC3G,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI,MAAM,oCAAoC,OAAO,gGAAqG;AAAA,EAClK;AAGA,MAAI,aAAa;AACf,WAAO;AAAA,EACT;AACA,SAAOC,YAAW,MAAM,IAAI,SAASC,MAAK,MAAM;AAClD;AACA,eAAe,gBAAgB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,SAAS,MAAM,OAAO;AAAA,IACxB,SAAS,cAAc,iBAAiB,gBAAgB,OAAO,CAAC,IAAI,eAAe,gBAAgB,OAAO,CAAC;AAAA,IAC3G,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI,MAAM,mCAAmC,OAAO,gGAAqG;AAAA,EACjK;AACA,MAAIC,gBAAe,MAAM,GAAG;AAC1B,QAAI,OAAO,QAAQ,qBAAqB,OAAO,KAAK,UAAU,GAAG,GAAG;AAClE,aAAOC,UAAS,IAAI,QAAQ,OAAO,KAAK,OAAO,EAAE,IAAI,UAAU,GAAG,OAAO,IAAI;AAAA,IAC/E;AACA,WAAO;AAAA,EACT;AAGA,MAAI,aAAa;AACf,WAAO;AAAA,EACT;AACA,SAAOH,YAAW,MAAM,IAAI,SAASC,MAAK,MAAM;AAClD;AAOA,SAAS,gBAAgB,SAAS;AAChC,MAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,MAAI,cAAc,IAAI,aAAa,OAAO,OAAO;AACjD,MAAI,aAAa,OAAO,OAAO;AAC/B,MAAI,oBAAoB,CAAC;AACzB,WAAS,cAAc,aAAa;AAClC,QAAI,YAAY;AACd,wBAAkB,KAAK,UAAU;AAAA,IACnC;AAAA,EACF;AACA,WAAS,UAAU,mBAAmB;AACpC,QAAI,aAAa,OAAO,SAAS,MAAM;AAAA,EACzC;AACA,MAAI,OAAO;AAAA,IACT,QAAQ,QAAQ;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,KAAK,MAAM;AACb,SAAK,SAAS;AAAA,EAChB;AACA,SAAO,IAAI,QAAQ,IAAI,MAAM,IAAI;AACnC;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,MAAI,aAAa,OAAO,OAAO;AAC/B,MAAI,OAAO;AAAA,IACT,QAAQ,QAAQ;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,KAAK,MAAM;AACb,SAAK,SAAS;AAAA,EAChB;AACA,SAAO,IAAI,QAAQ,IAAI,MAAM,IAAI;AACnC;AACA,SAAS,iBAAiB,SAAS;AACjC,MAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,MAAI,aAAa,OAAO,SAAS;AACjC,MAAI,OAAO;AAAA,IACT,QAAQ,QAAQ;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,KAAK,MAAM;AACb,SAAK,SAAS;AAAA,EAChB;AACA,SAAO,IAAI,QAAQ,IAAI,MAAM,IAAI;AACnC;AApIA;AAAA;AAUA;AAAA;AAAA;;;ACMA,SAAS,sBAAsB,UAAU;AACvC,MAAI,SAAS,CAAC;AACd,SAAO,OAAO,QAAQ,EAAE,QAAQ,WAAS;AACvC,QAAI,WAAW,MAAM,YAAY;AACjC,QAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,aAAO,QAAQ,IAAI,CAAC;AAAA,IACtB;AACA,WAAO,QAAQ,EAAE,KAAK,KAAK;AAAA,EAC7B,CAAC;AACD,SAAO;AACT;AAIA,SAAS,aAAa,UAAU,WAAW,IAAI,mBAAmB,sBAAsB,QAAQ,GAAG;AACjG,UAAQ,iBAAiB,QAAQ,KAAK,CAAC,GAAG,IAAI,YAAU;AAAA,IACtD,GAAG;AAAA,IACH,UAAU,aAAa,UAAU,MAAM,IAAI,gBAAgB;AAAA,EAC7D,EAAE;AACJ;AAIA,SAAS,8BAA8B,UAAU,QAAQ,WAAW,IAAI,mBAAmB,sBAAsB,QAAQ,GAAG;AAC1H,UAAQ,iBAAiB,QAAQ,KAAK,CAAC,GAAG,IAAI,WAAS;AACrD,QAAI,cAAc;AAAA;AAAA,MAEhB,kBAAkB,MAAM,OAAO,UAAU,MAAM,OAAO,iBAAiB;AAAA,MACvE,IAAI,MAAM;AAAA,MACV,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM,OAAO;AAAA;AAAA;AAAA,QAGrB,CAAC,MAAM,oBAAoB,gBAAgB;AAAA,UACzC,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,aAAa,KAAK;AAAA,UAClB,QAAQ,MAAM,OAAO;AAAA,UACrB,SAAS,MAAM;AAAA,UACf,aAAa,OAAO,mBAAmB;AAAA,QACzC,CAAC;AAAA,UAAI;AAAA,MACL,QAAQ,MAAM,OAAO,SAAS,CAAC,MAAM,oBAAoB,gBAAgB;AAAA,QACvE,SAAS,KAAK;AAAA,QACd,QAAQ,KAAK;AAAA,QACb,aAAa,KAAK;AAAA,QAClB,QAAQ,MAAM,OAAO;AAAA,QACrB,SAAS,MAAM;AAAA,QACf,aAAa,OAAO,mBAAmB;AAAA,MACzC,CAAC,IAAI;AAAA,MACL,QAAQ,MAAM,OAAO;AAAA,IACvB;AACA,WAAO,MAAM,QAAQ;AAAA,MACnB,OAAO;AAAA,MACP,GAAG;AAAA,IACL,IAAI;AAAA,MACF,eAAe,MAAM;AAAA,MACrB,UAAU,8BAA8B,UAAU,QAAQ,MAAM,IAAI,gBAAgB;AAAA,MACpF,GAAG;AAAA,IACL;AAAA,EACF,CAAC;AACH;AA5EA;AAAA;AAUA;AAAA;AAAA;;;ACcA,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,QAAQ,cAAc,WAAS,cAAc,KAAK,CAAC;AACjE;AA1BA,IAgBM,eAOA;AAvBN;AAAA;AAgBA,IAAM,gBAAgB;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AACA,IAAM,eAAe;AAAA;AAAA;;;ACVrB,SAAS,0BAA0B,eAAe;AAGhD,SAAO,WAAW,KAAK,UAAU,aAAa,CAAC;AACjD;AAjBA;AAAA;AAUA;AAAA;AAAA;;;ACAA,eAAe,kBAAkB,OAAO,QAAQ;AAC9C,aAAW,QAAQ,IAAI;AACvB,MAAI,CAAC,OAAQ,OAAM,MAAM,2BAA2B;AACpD,MAAI,MAAM,IAAI,IAAI,MAAM;AACxB,MAAI,WAAW;AACf,MAAI,WAAW,MAAM,MAAM,IAAI,MAAM;AAAA,IACnC,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM,KAAK,UAAU;AAAA,MACnB,WAAW,MAAM,OAAO;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,EAAE,MAAM,WAAS;AAChB,YAAQ,MAAM,uCAAuC,GAAG,EAAE;AAC1D,UAAM;AAAA,EACR,CAAC;AACD,MAAI,CAAC,SAAS,IAAI;AAChB,YAAQ,MAAM,uCAAuC,GAAG,KAAK,SAAS,MAAM,GAAG;AAC/E,UAAM,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,EACnC;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,UAAQ,IAAI,eAAe,MAAM,OAAO,OAAO,QAAQ;AACzD;AAEA,SAAS,kBAAkB,gBAAgB;AAEzC,aAAW,uBAAuB,IAAI;AACxC;AACA,SAAS,oBAAoB;AAE3B,SAAO,WAAW,uBAAuB;AAC3C;AA3CA,IAmCM;AAnCN;AAAA;AAmCA,IAAM,0BAA0B;AAAA;AAAA;;;ACzBhC,SAAS,yBAAyB,MAAM,SAAS;AAC/C,SAAO,2RAAwS,IAAI,wBAA6B,OAAO;AACzV;AAZA;AAAA;AAAA;AAAA;;;ACkCA,SAAS,OAAO,OAAO,MAAM;AAC3B,MAAI,eAAe;AACnB,MAAI,SAAS,aAAa,MAAM,MAAM;AACtC,MAAI,aAAa,8BAA8B,MAAM,QAAQ,MAAM,MAAM;AACzE,MAAI,aAAa,aAAa,IAAI,IAAI,OAAO,WAAW;AACxD,MAAI,gBAAgB,oBAAoB,YAAY;AAAA,IAClD,UAAU,MAAM;AAAA,IAChB,QAAQ;AAAA,MACN,wBAAwB,gBAAgB,MAAM,YAAY,QAAQ,kBAAkB,SAAS,SAAS,cAAc,0BAA0B;AAAA,MAC9I,uBAAuB,iBAAiB,MAAM,YAAY,QAAQ,mBAAmB,SAAS,SAAS,eAAe,yBAAyB;AAAA,IACjJ;AAAA,EACF,CAAC;AACD,MAAI,eAAe,MAAM,MAAM,OAAO,gBAAgB,CAAC,OAAO;AAAA,IAC5D;AAAA,EACF,MAAM;AACJ,QAAI,eAAe,WAAW,QAAQ,CAAC,QAAQ,OAAO,SAAS;AAC7D,cAAQ;AAAA;AAAA,QAER,qBAAqB,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ;AAAA,MAAK;AAAA,IAClE;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAmHA,eAAe,sBAAsB,OAAO,QAAQ,KAAK;AACvD,MAAI,MAAM,OAAO,YAAY,IAAI,aAAa,IAAI,SAAS,GAAG;AAC5D,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,2BAA2B;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,UAAU,CAAC;AACf,MAAI,IAAI,aAAa,IAAI,GAAG,GAAG;AAC7B,QAAI,QAAQ,oBAAI,IAAI;AAUpB,QAAI,aAAa,OAAO,GAAG,EAAE,QAAQ,UAAQ;AAC3C,UAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,eAAO,IAAI,IAAI;AAAA,MACjB;AACA,UAAI,WAAW,KAAK,MAAM,GAAG,EAAE,MAAM,CAAC;AACtC,eAAS,QAAQ,CAAC,GAAG,MAAM;AACzB,YAAI,cAAc,SAAS,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG;AACnD,cAAM,IAAI,IAAI,WAAW,EAAE;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AACD,aAAS,QAAQ,OAAO;AACtB,UAAI,UAAU,kBAAkB,QAAQ,MAAM,MAAM,QAAQ;AAC5D,UAAI,SAAS;AACX,iBAAS,SAAS,SAAS;AACzB,cAAI,UAAU,MAAM,MAAM;AAC1B,kBAAQ,OAAO,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AACA,WAAOG,MAAK,SAAS;AAAA,MACnB,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,IAAI,SAAS,mBAAmB;AAAA,IACrC,QAAQ;AAAA,EACV,CAAC;AACH;AACA,eAAe,kBAAkB,YAAY,OAAO,eAAe,SAAS,SAAS,aAAa,aAAa;AAC7G,MAAI;AACF,QAAI,WAAW,MAAM,cAAc,WAAW,SAAS;AAAA,MACrD;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AACD,QAAIC,oBAAmB,QAAQ,GAAG;AAChC,aAAO,4BAA4B,UAAU,MAAM,QAAQ;AAAA,IAC7D;AACA,QAAI,0BAA0B,UAAU;AACtC,UAAI,eAAe,SAAS,sBAAsB;AAClD,UAAI,OAAO,6BAA6B,cAAc,QAAQ,QAAQ,UAAU;AAChF,UAAI,OAAO,aAAa,QAAQ,CAAC;AACjC,UAAI,UAAU,IAAI,QAAQ,KAAK,OAAO;AACtC,cAAQ,IAAI,gBAAgB,qBAAqB;AAGjD,cAAQ,IAAI,oBAAoB,KAAK;AACrC,WAAK,UAAU;AACf,aAAO,IAAI,SAAS,MAAM,IAAI;AAAA,IAChC;AAIA,eAAW,gBAAgB,UAAU,oBAAoB,KAAK;AAC9D,WAAO;AAAA,EACT,SAAS,OAAO;AACd,QAAIC,YAAW,KAAK,GAAG;AACrB,UAAI,WAAW,gBAAgB,OAAO,iBAAiB,KAAK;AAC5D,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,KAAK,GAAG;AAC/B,kBAAY,KAAK;AACjB,aAAO,oBAAoB,OAAO,UAAU;AAAA,IAC9C;AACA,QAAI,gBAAgB,iBAAiB,SAAS,iBAAiB,eAAe,QAAQ,IAAI,MAAM,yBAAyB;AACzH,gBAAY,aAAa;AACzB,WAAO,KAAO,eAAe,eAAe,UAAU,GAAG;AAAA,MACvD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,eAAe,yBAAyB,YAAY,OAAO,eAAe,SAAS,YAAY,aAAa,aAAa;AACvH,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,QAAQ,WAAW,QAAQ,MAAM,kBAAkB,OAAO,YAAY,eAAe,SAAS,YAAY,aAAa,WAAW,IAAI,MAAM,mBAAmB,OAAO,YAAY,eAAe,SAAS,YAAY,aAAa,WAAW;AAIlP,MAAI,gBAAgB,IAAI,QAAQ,OAAO;AACvC,gBAAc,IAAI,oBAAoB,KAAK;AAG3C,MAAI,qBAAqB,IAAI,MAAM,GAAG;AACpC,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAMA,gBAAc,IAAI,gBAAgB,eAAe;AAIjD,SAAO,IAAI,SAAS,qBAAqB,QAAQ,QAAQ,QAAQ,MAAM,MAAM,OAAO,eAAe,UAAU,GAAG;AAAA,IAC9G,QAAQ,UAAU;AAAA,IAClB,SAAS;AAAA,EACX,CAAC;AACH;AACA,eAAe,sBAAsB,YAAY,OAAO,eAAe,SAAS,aAAa,aAAa,aAAa;AACrH,MAAI;AACJ,MAAI;AACF,cAAU,MAAM,cAAc,MAAM,SAAS;AAAA,MAC3C,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH,SAAS,OAAO;AACd,gBAAY,KAAK;AACjB,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,MAAIA,YAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,UAAU,mBAAmB,OAAO,OAAO;AAG/C,MAAI,qBAAqB,IAAI,QAAQ,UAAU,GAAG;AAChD,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB,QAAQ,QAAQ;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MAAI,QAAQ,QAAQ;AAClB,WAAO,OAAO,QAAQ,MAAM,EAAE,QAAQ,SAAO;AAE3C,UAAI,CAAC,qBAAqB,GAAG,KAAK,IAAI,OAAO;AAC3C,oBAAY,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AACD,YAAQ,SAAS,eAAe,QAAQ,QAAQ,UAAU;AAAA,EAC5D;AAKA,MAAI,QAAQ;AAAA,IACV,YAAY,QAAQ;AAAA,IACpB,YAAY,QAAQ;AAAA,IACpB,QAAQ,gBAAgB,QAAQ,QAAQ,UAAU;AAAA,EACpD;AACA,MAAI,eAAe;AAAA,IACjB,UAAU,MAAM;AAAA,IAChB,cAAc,wBAAwB,MAAM,MAAM;AAAA,IAClD,sBAAsB;AAAA,IACtB;AAAA,IACA,qBAAqB,0BAA0B;AAAA,MAC7C,UAAU,MAAM;AAAA,MAChB;AAAA,MACA,QAAQ,MAAM;AAAA,MACd,WAAW,MAAM;AAAA,MACjB,GAAI,CAAC,MAAM,OAAO,iBAAiB;AAAA,QACjC;AAAA,MACF,IAAI;AAAA,IACN,CAAC;AAAA,IACD,GAAI,MAAM,OAAO,iBAAiB;AAAA,MAChC,qBAAqB,qBAAqB,OAAO,QAAQ,QAAQ,MAAM,MAAM,OAAO,eAAe,UAAU;AAAA,MAC7G,YAAY,CAAC;AAAA,IACf,IAAI;AAAA,IACJ,QAAQ,MAAM;AAAA,IACd,WAAW,MAAM;AAAA,IACjB,gBAAgB,SAAO,eAAe,KAAK,UAAU;AAAA,EACvD;AACA,MAAI,gCAAgC,MAAM,MAAM,OAAO;AACvD,MAAI;AACF,WAAO,MAAM,8BAA8B,SAAS,QAAQ,YAAY,SAAS,cAAc,WAAW;AAAA,EAC5G,SAAS,OAAO;AACd,gBAAY,KAAK;AACjB,QAAI,uBAAuB;AAI3B,QAAIA,YAAW,KAAK,GAAG;AACrB,UAAI;AACF,YAAIC,QAAO,MAAM,eAAe,KAAK;AACrC,+BAAuB,IAAI,kBAAyB,MAAM,QAAQ,MAAM,YAAYA,KAAI;AAAA,MAC1F,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAGA,cAAU,0BAA0B,cAAc,YAAY,SAAS,oBAAoB;AAG3F,QAAI,QAAQ,QAAQ;AAClB,cAAQ,SAAS,eAAe,QAAQ,QAAQ,UAAU;AAAA,IAC5D;AAMA,QAAIC,SAAQ;AAAA,MACV,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ;AAAA,MACpB,QAAQ,gBAAgB,QAAQ,QAAQ,UAAU;AAAA,IACpD;AACA,mBAAe;AAAA,MACb,GAAG;AAAA,MACH,sBAAsB;AAAA,MACtB,qBAAqB,0BAA0B;AAAA,QAC7C,UAAU,MAAM;AAAA,QAChB,QAAQ,MAAM;AAAA,QACd,WAAW,MAAM;AAAA,QACjB,GAAI,CAAC,MAAM,OAAO,iBAAiB;AAAA,UACjC,OAAAA;AAAA,QACF,IAAI;AAAA,MACN,CAAC;AAAA,MACD,GAAI,MAAM,OAAO,iBAAiB;AAAA,QAChC,qBAAqB,qBAAqBA,QAAO,QAAQ,QAAQ,MAAM,MAAM,OAAO,eAAe,UAAU;AAAA,QAC7G,YAAY,CAAC;AAAA,MACf,IAAI;AAAA,IACN;AACA,QAAI;AACF,aAAO,MAAM,8BAA8B,SAAS,QAAQ,YAAY,SAAS,cAAc,WAAW;AAAA,IAC5G,SAASC,QAAO;AACd,kBAAYA,MAAK;AACjB,aAAO,8BAA8BA,QAAO,UAAU;AAAA,IACxD;AAAA,EACF;AACF;AACA,eAAe,sBAAsB,YAAY,OAAO,eAAe,SAAS,SAAS,aAAa,aAAa;AACjH,MAAI;AAIF,QAAI,WAAW,MAAM,cAAc,WAAW,SAAS;AAAA,MACrD;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AACD,QAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,MAAAC,WAAU,EAAE,0BAA0B,WAAW,8HAAmI,OAAO,UAAU;AAAA,IACvM;AACA,QAAI,MAAM,OAAO,kBAAkB,CAACJ,YAAW,QAAQ,GAAG;AACxD,cAAQ,KAAK,yBAAyB,QAAQ,WAAW,QAAQ,WAAW,UAAU,OAAO,CAAC;AAC9F,iBAAWF,MAAK,QAAQ;AAAA,IAC1B;AAIA,IAAAM,WAAUJ,YAAW,QAAQ,GAAG,oDAAoD;AACpF,WAAO;AAAA,EACT,SAAS,OAAO;AACd,QAAIA,YAAW,KAAK,GAAG;AAGrB,UAAI,WAAW,gBAAgB,OAAO,iBAAiB,KAAK;AAC5D,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,KAAK,GAAG;AAC/B,UAAI,OAAO;AACT,oBAAY,KAAK;AAAA,MACnB;AACA,aAAO,oBAAoB,OAAO,UAAU;AAAA,IAC9C;AACA,gBAAY,KAAK;AACjB,WAAO,8BAA8B,OAAO,UAAU;AAAA,EACxD;AACF;AACA,SAAS,oBAAoB,eAAe,YAAY;AACtD,SAAO,KAAO;AAAA;AAAA,IAEd,cAAc,SAAS,IAAI,MAAM,yBAAyB;AAAA,IAAG;AAAA,EAAU,GAAG;AAAA,IACxE,QAAQ,cAAc;AAAA,IACtB,YAAY,cAAc;AAAA,IAC1B,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AACA,SAAS,8BAA8B,OAAO,YAAY;AACxD,MAAI,UAAU;AACd,MAAI,eAAe,WAAW,YAAY;AACxC,eAAW;AAAA;AAAA,EAAO,OAAO,KAAK,CAAC;AAAA,EACjC;AAGA,SAAO,IAAI,SAAS,SAAS;AAAA,IAC3B,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AACA,SAAS,eAAe,UAAU;AAChC,MAAI,cAAc,SAAS,QAAQ,IAAI,cAAc;AAGrD,SAAO,eAAe,wBAAwB,KAAK,WAAW,IAAI,SAAS,QAAQ,OAAO,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK;AACnI;AACA,SAAS,4BAA4B,UAAU,UAAU;AAIvD,MAAI,UAAU,IAAI,QAAQ,SAAS,OAAO;AAC1C,MAAI,cAAc,QAAQ,IAAI,UAAU;AACxC,UAAQ,IAAI,oBAAoB,WAAW,cAAc,aAAa,QAAQ,KAAK,cAAc,WAAW;AAC5G,UAAQ,IAAI,kBAAkB,OAAO,SAAS,MAAM,CAAC;AACrD,UAAQ,OAAO,UAAU;AACzB,MAAI,SAAS,QAAQ,IAAI,YAAY,MAAM,MAAM;AAC/C,YAAQ,IAAI,sBAAsB,KAAK;AAAA,EACzC;AACA,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR;AAAA,EACF,CAAC;AACH;AAOA,SAAS,gBAAgB,UAAU,MAAM,OAAO;AAC9C,MAAI,UAAU,IAAI,QAAQ,SAAS,OAAO;AAC1C,UAAQ,IAAI,MAAM,KAAK;AACvB,SAAO,IAAI,SAAS,SAAS,MAAM;AAAA,IACjC,QAAQ,SAAS;AAAA,IACjB,YAAY,SAAS;AAAA,IACrB;AAAA,IACA,QAAQ,SAAS,OAAO,SAAS;AAAA,EACnC,CAAC;AACH;AAnhBA,IAiCM,sBA8BA;AA/DN;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAWA,IAAM,uBAAuB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AA8B9D,IAAM,uBAAuB,CAAC,OAAO,SAAS;AAC5C,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO,eAAe,eAAe,SAAS,cAAc,CAAC,GAAG;AAC9D,iBAAS,OAAO,UAAU,aAAa,MAAM,MAAM,IAAI;AACvD,iBAAS,OAAO;AAChB,YAAI,OAAO,UAAU,YAAY;AAC/B,cAAI,UAAU,OAAO,QAAQ,IAAI;AACjC,mBAAS,QAAQ;AACjB,uBAAa,QAAQ;AACrB,0BAAgB,QAAQ;AACxB,yBAAe,QAAQ;AAAA,QACzB,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAC,cAAc;AACpE,cAAI,UAAU,OAAO,QAAQ,IAAI;AACjC,mBAAS,QAAQ;AACjB,uBAAa,QAAQ;AACrB,0BAAgB,QAAQ;AACxB,yBAAe,QAAQ;AAAA,QACzB;AACA,YAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,YAAI,SAAS,CAAC;AACd,YAAI,cAAc,WAAS;AACzB,cAAI,SAAS,WAAW,aAAa;AACnC,gBAAI,oBAAoB;AACxB,aAAC,qBAAqB,kBAAkB,OAAO,QAAQ,uBAAuB,SAAS,UAAU,wBAAwB,mBAAmB,yBAAyB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,oBAAoB,KAAK;AAAA,UAC/Q;AACA,uBAAa,OAAO;AAAA,YAClB,SAAS;AAAA,YACT;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAIA,YAAI,cAAc,GAAG,OAAO,YAAY,GAAG,cAAc,QAAQ,QAAQ,GAAG;AAC5E,YAAI,IAAI,aAAa,aAAa;AAChC,cAAI;AACF,gBAAI,MAAM,MAAM,sBAAsB,QAAQ,QAAQ,GAAG;AACzD,mBAAO;AAAA,UACT,SAAS,GAAG;AACV,wBAAY,CAAC;AACb,mBAAO,IAAI,SAAS,wBAAwB;AAAA,cAC1C,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAAA,QACF;AACA,YAAI,UAAU,kBAAkB,QAAQ,IAAI,UAAU,OAAO,QAAQ;AACrE,YAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,iBAAO,OAAO,QAAQ,QAAQ,CAAC,EAAE,MAAM;AAAA,QACzC;AACA,YAAI;AACJ,YAAI,IAAI,aAAa,IAAI,OAAO,GAAG;AACjC,cAAI,OAAO,OAAO,gBAAgB;AAChC,wBAAY,IAAI,MAAM,gHAAqH,CAAC;AAAA,UAC9I;AACA,cAAI,UAAU,IAAI,aAAa,IAAI,OAAO;AAC1C,qBAAW,MAAM,kBAAkB,YAAY,QAAQ,eAAe,SAAS,SAAS,aAAa,WAAW;AAChH,cAAI,OAAO,MAAM,OAAO,mBAAmB;AACzC,uBAAW,MAAM,OAAO,MAAM,OAAO,kBAAkB,UAAU;AAAA,cAC/D,SAAS;AAAA,cACT;AAAA,cACA;AAAA,YACF,CAAC;AACD,gBAAID,oBAAmB,QAAQ,GAAG;AAChC,yBAAW,4BAA4B,UAAU,OAAO,QAAQ;AAAA,YAClE;AAAA,UACF;AAAA,QACF,WAAW,OAAO,OAAO,kBAAkB,IAAI,SAAS,SAAS,OAAO,GAAG;AACzE,cAAI,aAAa,IAAI,IAAI,QAAQ,GAAG;AACpC,qBAAW,WAAW,WAAW,SAAS,QAAQ,WAAW,EAAE,EAAE,QAAQ,aAAa,GAAG;AACzF,cAAI,qBAAqB,kBAAkB,QAAQ,WAAW,UAAU,OAAO,QAAQ;AACvF,qBAAW,MAAM,yBAAyB,YAAY,QAAQ,eAAe,SAAS,YAAY,aAAa,WAAW;AAC1H,cAAI,OAAO,MAAM,OAAO,mBAAmB;AACzC,uBAAW,MAAM,OAAO,MAAM,OAAO,kBAAkB,UAAU;AAAA,cAC/D,SAAS;AAAA,cACT,QAAQ,qBAAqB,mBAAmB,CAAC,EAAE,SAAS,CAAC;AAAA,cAC7D;AAAA,YACF,CAAC;AACD,gBAAIA,oBAAmB,QAAQ,GAAG;AAChC,kBAAI,SAAS,uBAAuB,SAAS,QAAQ,SAAS,SAAS,OAAO,QAAQ;AACtF,kBAAI,QAAQ,WAAW,OAAO;AAC5B,yBAAS;AAAA,kBACP,CAAC,yBAAyB,GAAG;AAAA,gBAC/B;AAAA,cACF;AACA,kBAAI,UAAU,IAAI,QAAQ,SAAS,OAAO;AAC1C,sBAAQ,IAAI,gBAAgB,eAAe;AAC3C,qBAAO,IAAI,SAAS,qBAAqB,QAAQ,QAAQ,QAAQ,OAAO,MAAM,OAAO,eAAe,UAAU,GAAG;AAAA,gBAC/G,QAAQ;AAAA,gBACR;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,WAAW,WAAW,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,OAAO,WAAW,QAAQ,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,OAAO,iBAAiB,MAAM;AAChJ,qBAAW,MAAM,sBAAsB,YAAY,QAAQ,eAAe,QAAQ,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,IAAI,SAAS,aAAa,WAAW;AAAA,QAC5I,OAAO;AACL,cAAI,qBAAqB;AACzB,cAAI,cAAc,SAAS,WAAW,cAAc,QAAQ,sBAAsB,kBAAkB,OAAO,QAAQ,wBAAwB,SAAS,UAAU,wBAAwB,oBAAoB,oBAAoB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,qBAAqB,QAAQ,IAAI,QAAQ,KAAK;AAC3V,qBAAW,MAAM,sBAAsB,YAAY,QAAQ,eAAe,SAAS,aAAa,aAAa,WAAW;AAAA,QAC1H;AACA,YAAI,QAAQ,WAAW,QAAQ;AAC7B,iBAAO,IAAI,SAAS,MAAM;AAAA,YACxB,SAAS,SAAS;AAAA,YAClB,QAAQ,SAAS;AAAA,YACjB,YAAY,SAAS;AAAA,UACvB,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACzJA,SAAS,MAAM,MAAM;AACnB,SAAO,WAAW,IAAI;AACxB;AAmHA,SAAS,kCAAkC,QAAQ;AACjD,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,oNAAmO;AAClR;AA9IA,IAkCM,eAsCA,WA8BA;AAtGN;AAAA;AAUA;AACA;AAuBA,IAAM,gBAAgB,CAAC,cAAc,CAAC,GAAG,KAAK,OAAO;AACnD,UAAI,MAAM,IAAI,IAAI,OAAO,QAAQ,WAAW,CAAC;AAC7C,aAAO;AAAA,QACL,IAAI,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,QACA,IAAI,OAAO;AACT,iBAAO,OAAO,YAAY,GAAG;AAAA,QAC/B;AAAA,QACA,IAAI,MAAM;AACR,iBAAO,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,CAAC;AAAA,QAC7C;AAAA,QACA,IAAI,MAAM;AACR,cAAI,IAAI,IAAI,IAAI,EAAG,QAAO,IAAI,IAAI,IAAI;AACtC,cAAI,YAAY,MAAM,IAAI;AAC1B,cAAI,IAAI,IAAI,SAAS,GAAG;AACtB,gBAAI,QAAQ,IAAI,IAAI,SAAS;AAC7B,gBAAI,OAAO,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,IAAI,MAAM,OAAO;AACf,cAAI,IAAI,MAAM,KAAK;AAAA,QACrB;AAAA,QACA,MAAM,MAAM,OAAO;AACjB,cAAI,IAAI,MAAM,IAAI,GAAG,KAAK;AAAA,QAC5B;AAAA,QACA,MAAM,MAAM;AACV,cAAI,OAAO,IAAI;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAMA,IAAM,YAAY,YAAU;AAC1B,aAAO,UAAU,QAAQ,OAAO,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,eAAe,OAAO,OAAO,QAAQ,cAAc,OAAO,OAAO,QAAQ,cAAc,OAAO,OAAO,QAAQ,cAAc,OAAO,OAAO,UAAU,cAAc,OAAO,OAAO,UAAU;AAAA,IAC5Q;AA4BA,IAAM,8BAA8B,kBAAgB,CAAC;AAAA,MACnD,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,SAAS,SAAS,SAAS,IAAI,YAAY,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,aAAa,SAAS;AAC5J,wCAAkC,MAAM;AACxC,aAAO;AAAA,QACL,MAAM,WAAW,cAAc,SAAS;AACtC,cAAI,KAAK,gBAAiB,MAAM,OAAO,MAAM,cAAc,OAAO;AAClE,cAAIM,QAAO,MAAO,MAAM,SAAS,EAAE;AACnC,iBAAO,cAAcA,SAAQ,CAAC,GAAG,MAAM,EAAE;AAAA,QAC3C;AAAA,QACA,MAAM,cAAc,SAAS,SAAS;AACpC,cAAI;AAAA,YACF;AAAA,YACA,MAAAA;AAAA,UACF,IAAI;AACJ,cAAI,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,OAAO,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,SAAS,GAAI,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,OAAO,QAAQ,UAAU,OAAO;AACjP,cAAI,IAAI;AACN,kBAAM,WAAW,IAAIA,OAAM,OAAO;AAAA,UACpC,OAAO;AACL,iBAAK,MAAM,WAAWA,OAAM,OAAO;AAAA,UACrC;AACA,iBAAO,OAAO,UAAU,IAAI,OAAO;AAAA,QACrC;AAAA,QACA,MAAM,eAAe,SAAS,SAAS;AACrC,gBAAM,WAAW,QAAQ,EAAE;AAC3B,iBAAO,OAAO,UAAU,IAAI;AAAA,YAC1B,GAAG;AAAA,YACH,QAAQ;AAAA,YACR,SAAS,oBAAI,KAAK,CAAC;AAAA,UACrB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC3IA,IAwBM;AAxBN;AAAA;AAUA;AACA;AAaA,IAAM,oCAAoC,kBAAgB,CAAC;AAAA,MACzD,QAAQ;AAAA,IACV,IAAI,CAAC,MAAM;AACT,UAAI,SAAS,SAAS,SAAS,IAAI,YAAY,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,aAAa,SAAS;AAC5J,wCAAkC,MAAM;AACxC,aAAO;AAAA,QACL,MAAM,WAAW,cAAc,SAAS;AACtC,iBAAO,cAAc,gBAAiB,MAAM,OAAO,MAAM,cAAc,OAAO,KAAM,CAAC,CAAC;AAAA,QACxF;AAAA,QACA,MAAM,cAAc,SAAS,SAAS;AACpC,cAAI,mBAAmB,MAAM,OAAO,UAAU,QAAQ,MAAM,OAAO;AACnE,cAAI,iBAAiB,SAAS,MAAM;AAClC,kBAAM,IAAI,MAAM,wDAAwD,iBAAiB,MAAM;AAAA,UACjG;AACA,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,eAAe,UAAU,SAAS;AACtC,iBAAO,OAAO,UAAU,IAAI;AAAA,YAC1B,GAAG;AAAA,YACH,QAAQ;AAAA,YACR,SAAS,oBAAI,KAAK,CAAC;AAAA,UACrB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AChDA,IAmBM;AAnBN;AAAA;AAmBA,IAAM,oCAAoC,0BAAwB,CAAC;AAAA,MACjE;AAAA,IACF,IAAI,CAAC,MAAM;AACT,UAAI,MAAM,oBAAI,IAAI;AAClB,aAAO,qBAAqB;AAAA,QAC1B;AAAA,QACA,MAAM,WAAWC,OAAM,SAAS;AAC9B,cAAI,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AACnD,cAAI,IAAI,IAAI;AAAA,YACV,MAAAA;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,SAAS,IAAI;AACjB,cAAI,IAAI,IAAI,EAAE,GAAG;AACf,gBAAI;AAAA,cACF,MAAAA;AAAA,cACA;AAAA,YACF,IAAI,IAAI,IAAI,EAAE;AACd,gBAAI,CAAC,WAAW,UAAU,oBAAI,KAAK,GAAG;AACpC,qBAAOA;AAAA,YACT;AAGA,gBAAI,QAAS,KAAI,OAAO,EAAE;AAAA,UAC5B;AACA,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,WAAW,IAAIA,OAAM,SAAS;AAClC,cAAI,IAAI,IAAI;AAAA,YACV,MAAAA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,MAAM,WAAW,IAAI;AACnB,cAAI,OAAO,EAAE;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AC1DA,IAUM;AAVN,IAAAC,eAAA;AAAA;AAUA,IAAM,2BAAN,cAAuC,MAAM;AAAA,MAC3C,YAAY,OAAO,UAAU;AAC3B,cAAM,UAAU,KAAK,6BAA6B,QAAQ,SAAS;AACnE,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA;AAAA;;;ACJA,SAAS,0BAA0B;AAAA,EACjC;AAAA,EACA,cAAc;AAChB,IAAI,CAAC,GAAG;AACN,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAAC;AAAA,EACF,MAAM;AACJ,QAAI,UAAU,CAAE,MAAM,OAAO;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,GAAI;AACH,aAAO;AAAA,IACT;AACA,QAAI,OAAO;AACX,QAAI,SAAS,CAAC;AACd,mBAAe,SAASA,OAAM;AAC5B,cAAQ,MAAM;AACd,UAAI,OAAO,aAAa;AACtB,cAAM,IAAI,yBAAyB,MAAM,WAAW;AAAA,MACtD;AACA,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,aAAO,IAAI,KAAK,QAAQ,UAAU;AAAA,QAChC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,MAAM,IAAI,KAAK,QAAQ;AAAA,MAC5B,MAAM;AAAA,IACR,CAAC,EAAE,KAAK;AAAA,EACV;AACF;AA/CA;AAAA;AAUA,IAAAC;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA;AAAA;AAAA,cAAAC;AAAA,EAAA;AAAA,kBAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAAC;AACA;AAAA;AAAA;", "names": ["parse", "serialize", "decode", "data", "token", "invariant", "value", "message", "Error", "warning", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "Math", "random", "toString", "substr", "createLocation", "current", "to", "state", "key", "location", "_extends", "pathname", "search", "hash", "parsePath", "createPath", "_ref", "char<PERSON>t", "path", "parsed<PERSON><PERSON>", "hashIndex", "indexOf", "searchIndex", "isIndexRoute", "route", "index", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "map", "treePath", "String", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "undefined", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "length", "decoded", "decodePath", "matchRouteBranch", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "startsWith", "slice", "joinPaths", "routesMeta", "concat", "push", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "replace", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "initialScore", "some", "isSplat", "splatPenalty", "indexRouteValue", "filter", "s", "reduce", "segment", "paramRe", "test", "dynamicSegmentValue", "emptySegmentValue", "staticSegmentValue", "siblings", "every", "n", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "assign", "params", "pathnameBase", "normalizePathname", "pattern", "matcher", "compiledParams", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "regexpSource", "_", "RegExp", "v", "decodeURIComponent", "error", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "JSON", "stringify", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "idx", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "data", "init", "DataWithResponseInit", "status", "isTrackedPromise", "Promise", "_tracked", "unwrapTrackedPromise", "_error", "_data", "isRouteErrorResponse", "statusText", "internal", "createStaticHandler", "opts", "detectErrorBoundary", "hasErrorBou<PERSON>ry", "defaultMapRouteProperties", "future", "v7_throwAbortReason", "dataRoutes", "query", "request", "_temp3", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "dataStrategy", "url", "URL", "method", "isValidMethod", "getInternalRouterError", "methodNotAllowedMatches", "getShortCircuitMatches", "loaderData", "actionData", "errors", "statusCode", "loaderHeaders", "actionHeaders", "activeDeferreds", "notFoundMatches", "queryImpl", "isResponse", "queryRoute", "_temp4", "routeId", "find", "m", "getTargetMatch", "values", "_result$activeDeferre", "UNSAFE_DEFERRED_SYMBOL", "routeMatch", "signal", "isMutationMethod", "submit", "loadRouteData", "isDataStrategyResult", "type", "ResultType", "isRedirectResponse", "actionMatch", "isRouteRequest", "action", "lazy", "results", "callDataStrategy", "aborted", "throwStaticHandlerAbortedError", "isRedirectResult", "Response", "response", "headers", "Location", "get", "isDeferredResult", "isErrorResult", "loaderRequest", "Request", "redirect", "boundaryMatch", "findNearestBoundary", "context", "pendingActionResult", "loader", "requestMatches", "getLoaderMatchesUntilBoundary", "matchesToLoad", "acc", "Map", "processRouteLoaderData", "executedLoaders", "Set", "has", "size", "fromEntries", "entries", "callDataStrategyImpl", "defaultDataStrategy", "dataResults", "all", "isRedirectDataStrategyResultResult", "normalizeRelativeRoutingRedirectResponse", "convertDataStrategyResultToDataResult", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "reason", "normalizeTo", "prependBasename", "fromRouteId", "relative", "contextualMatches", "activeRouteMatch", "nakedIndex", "hasNakedIndexQuery", "URLSearchParams", "indexValues", "getAll", "delete", "append", "qs", "boundaryId", "includeBoundary", "findIndex", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "immutableRouteKeys", "_ref4", "shouldLoad", "resolve", "dataStrategyImpl", "fetcher<PERSON>ey", "loadRouteDefinitionsPromises", "dsMatches", "loadRoutePromise", "handlerOverride", "callLoaderOrAction", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reject", "abortPromise", "r", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "handlerPromise", "val", "race", "handlerError", "catch", "removeEventListener", "dataStrategyResult", "contentType", "body", "json", "text", "ErrorResponseImpl", "isDataWithResponseInit", "_result$init3", "_result$init4", "_result$init", "_result$init2", "Headers", "isDeferredData", "_result$init5", "_result$init6", "deferred", "deferredData", "_result$init7", "_result$init8", "ABSOLUTE_URL_REGEX", "trimmedMatches", "set", "found<PERSON><PERSON>r", "pendingError", "eligibleMatches", "reverse", "_temp5", "errorMessage", "toUpperCase", "redirectStatusCodes", "subscribe", "cancel", "resolveData", "validRequestMethods", "validMutationMethods", "Action", "Aborted<PERSON>eferredError", "DeferredData", "defer", "redirectDocument", "validMutationMethodsArr", "validRequestMethodsArr", "paths", "responseInit", "constructor", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "_ref2", "trackPromise", "done", "add", "promise", "then", "onSettle", "defineProperty", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "fn", "abort", "k", "unwrappedData", "_ref3", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "Symbol", "ServerMode", "isDeferredData", "isResponse", "redirectStatusCodes", "isRedirectResponse", "isTrackedPromise", "json", "defer", "redirect", "replace", "redirectDocument", "data", "headers", "isResponse", "data", "redirect", "invariant", "isResponse", "json", "isDeferredData", "redirect", "json", "isRedirectResponse", "isResponse", "data", "state", "error", "invariant", "data", "data", "init_errors", "data", "init_errors", "data", "defer", "json", "redirect", "redirectDocument", "replace", "init_errors"]}
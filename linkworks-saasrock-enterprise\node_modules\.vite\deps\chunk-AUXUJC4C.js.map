{"version": 3, "sources": ["../../set-cookie-parser/lib/set-cookie.js"], "sourcesContent": ["\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else if (key === \"partitioned\") {\n      cookie.partitioned = true;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,QAAI,sBAAsB;AAAA,MACxB,cAAc;AAAA,MACd,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAEA,aAAS,iBAAiB,KAAK;AAC7B,aAAO,OAAO,QAAQ,YAAY,CAAC,CAAC,IAAI,KAAK;AAAA,IAC/C;AAEA,aAAS,YAAY,gBAAgB,SAAS;AAC5C,UAAI,QAAQ,eAAe,MAAM,GAAG,EAAE,OAAO,gBAAgB;AAE7D,UAAI,mBAAmB,MAAM,MAAM;AACnC,UAAI,SAAS,mBAAmB,gBAAgB;AAChD,UAAI,OAAO,OAAO;AAClB,UAAI,QAAQ,OAAO;AAEnB,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI;AACF,gBAAQ,QAAQ,eAAe,mBAAmB,KAAK,IAAI;AAAA,MAC7D,SAAS,GAAG;AACV,gBAAQ;AAAA,UACN,gFACE,QACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAEA,YAAM,QAAQ,SAAU,MAAM;AAC5B,YAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,YAAI,MAAM,MAAM,MAAM,EAAE,SAAS,EAAE,YAAY;AAC/C,YAAIA,SAAQ,MAAM,KAAK,GAAG;AAC1B,YAAI,QAAQ,WAAW;AACrB,iBAAO,UAAU,IAAI,KAAKA,MAAK;AAAA,QACjC,WAAW,QAAQ,WAAW;AAC5B,iBAAO,SAAS,SAASA,QAAO,EAAE;AAAA,QACpC,WAAW,QAAQ,UAAU;AAC3B,iBAAO,SAAS;AAAA,QAClB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAW;AAAA,QACpB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAWA;AAAA,QACpB,WAAW,QAAQ,eAAe;AAChC,iBAAO,cAAc;AAAA,QACvB,OAAO;AACL,iBAAO,GAAG,IAAIA;AAAA,QAChB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,kBAAkB;AAG5C,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,UAAI,eAAe,iBAAiB,MAAM,GAAG;AAC7C,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO,aAAa,MAAM;AAC1B,gBAAQ,aAAa,KAAK,GAAG;AAAA,MAC/B,OAAO;AACL,gBAAQ;AAAA,MACV;AAEA,aAAO,EAAE,MAAY,MAAa;AAAA,IACpC;AAEA,aAAS,MAAM,OAAO,SAAS;AAC7B,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,OAAO;AACV,YAAI,CAAC,QAAQ,KAAK;AAChB,iBAAO,CAAC;AAAA,QACV,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAEA,UAAI,MAAM,SAAS;AACjB,YAAI,OAAO,MAAM,QAAQ,iBAAiB,YAAY;AAGpD,kBAAQ,MAAM,QAAQ,aAAa;AAAA,QACrC,WAAW,MAAM,QAAQ,YAAY,GAAG;AAEtC,kBAAQ,MAAM,QAAQ,YAAY;AAAA,QACpC,OAAO;AAEL,cAAI,MACF,MAAM,QACJ,OAAO,KAAK,MAAM,OAAO,EAAE,KAAK,SAAU,KAAK;AAC7C,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC,CACH;AAEF,cAAI,CAAC,OAAO,MAAM,QAAQ,UAAU,CAAC,QAAQ,QAAQ;AACnD,oBAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,CAAC,KAAK;AAAA,MAChB;AAEA,UAAI,CAAC,QAAQ,KAAK;AAChB,eAAO,MAAM,OAAO,gBAAgB,EAAE,IAAI,SAAU,KAAK;AACvD,iBAAO,YAAY,KAAK,OAAO;AAAA,QACjC,CAAC;AAAA,MACH,OAAO;AACL,YAAI,UAAU,CAAC;AACf,eAAO,MAAM,OAAO,gBAAgB,EAAE,OAAO,SAAUC,UAAS,KAAK;AACnE,cAAI,SAAS,YAAY,KAAK,OAAO;AACrC,UAAAA,SAAQ,OAAO,IAAI,IAAI;AACvB,iBAAOA;AAAA,QACT,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AAaA,aAAS,mBAAmB,eAAe;AACzC,UAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAO;AAAA,MACT;AACA,UAAI,OAAO,kBAAkB,UAAU;AACrC,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,iBAAiB;AACxB,eAAO,MAAM,cAAc,UAAU,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AACzE,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,cAAc;AAAA,MAC7B;AAEA,eAAS,iBAAiB;AACxB,aAAK,cAAc,OAAO,GAAG;AAE7B,eAAO,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,MAC5C;AAEA,aAAO,MAAM,cAAc,QAAQ;AACjC,gBAAQ;AACR,gCAAwB;AAExB,eAAO,eAAe,GAAG;AACvB,eAAK,cAAc,OAAO,GAAG;AAC7B,cAAI,OAAO,KAAK;AAEd,wBAAY;AACZ,mBAAO;AAEP,2BAAe;AACf,wBAAY;AAEZ,mBAAO,MAAM,cAAc,UAAU,eAAe,GAAG;AACrD,qBAAO;AAAA,YACT;AAGA,gBAAI,MAAM,cAAc,UAAU,cAAc,OAAO,GAAG,MAAM,KAAK;AAEnE,sCAAwB;AAExB,oBAAM;AACN,6BAAe,KAAK,cAAc,UAAU,OAAO,SAAS,CAAC;AAC7D,sBAAQ;AAAA,YACV,OAAO;AAGL,oBAAM,YAAY;AAAA,YACpB;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,YAAI,CAAC,yBAAyB,OAAO,cAAc,QAAQ;AACzD,yBAAe,KAAK,cAAc,UAAU,OAAO,cAAc,MAAM,CAAC;AAAA,QAC1E;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,QAAQ;AACvB,WAAO,QAAQ,cAAc;AAC7B,WAAO,QAAQ,qBAAqB;AAAA;AAAA;", "names": ["value", "cookies"]}
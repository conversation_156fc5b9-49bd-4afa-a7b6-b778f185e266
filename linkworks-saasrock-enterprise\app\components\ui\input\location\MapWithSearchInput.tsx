import { forwardRef, Ref, useImperativeHandle, useRef, useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { cn } from "~/lib/utils";
import HintTooltip from "../../tooltips/HintTooltip";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import { MapPin, Crosshair, Loader2, Move, Map, Shield } from "lucide-react";
import { Input } from "../../input";
import { useGoogleMaps } from "~/hooks/useGoogleMaps";
import { useMapZoomPersistence } from "~/hooks/useMapZoomPersistence";
import { LocationData } from "~/types/location";
import LocationHelper from "~/utils/helpers/LocationHelper";

export interface RefMapWithSearchInput {
  input: HTMLInputElement | null;
}

export interface MapWithSearchInputProps {
  name?: string;
  title?: string;
  value?: LocationData;
  defaultValue?: LocationData;
  onChange?: (value: LocationData | undefined) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: string;
  icon?: string;
  autoFocus?: boolean;
  mapHeight?: string;
  placeholder?: string;
  zoom?: number;
  showCurrentLocationButton?: boolean;
  columns?: number;
  group?: string;
  // Security & Validation
  minSearchLength?: number;
  validatePlaceDetails?: boolean;
  storePlaceId?: boolean;
  showPlaceDetails?: boolean;
}

const MapWithSearchInput = (
  {
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    autoFocus = false,
    mapHeight = "300px",
    placeholder,
    zoom = 15,
    showCurrentLocationButton = true,
    columns,
    group,
    // Security & Validation defaults
    minSearchLength = 1,
    validatePlaceDetails = true,
    storePlaceId = true,
    showPlaceDetails = false,
  }: MapWithSearchInputProps,
  ref: Ref<RefMapWithSearchInput>
) => {
  const { t } = useTranslation();
  const input = useRef<HTMLInputElement>(null);
  const mapRef = useRef<HTMLDivElement>(null);
  const autocompleteRef = useRef<any>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerRef = useRef<any>(null);
  const placesServiceRef = useRef<any>(null);

  const isDraggingRef = useRef(false);
  const dragIndicatorRef = useRef<HTMLDivElement>(null);

  useImperativeHandle(ref, () => ({ input: input.current }), []);

  const [actualValue, setActualValue] = useState<LocationData | undefined>(
    value || defaultValue
  );
  const [searchValue, setSearchValue] = useState<string>(
    value?.address || defaultValue?.address || ""
  );
  const [isGettingCurrentLocation, setIsGettingCurrentLocation] = useState(false);
  const [isValidatingPlace, setIsValidatingPlace] = useState(false);
  const [placeDetails, setPlaceDetails] = useState<any>(null);
  const [searchError, setSearchError] = useState<string>("");
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isLoaded: isGoogleMapsLoaded, isLoading, error, getCurrentLocation } = useGoogleMaps();

  // Zoom persistence management
  const zoomPersistence = useMapZoomPersistence({
    componentId: `map-with-search-${name || 'default'}`,
    defaultZoom: zoom,
    enablePersistence: true
  });

  // Update map location function (will be defined after createDraggableMarker)
  const updateMapLocationRef = useRef<((location: LocationData, forceStreetZoom?: boolean) => void) | null>(null);

  // Sync with external value changes (only when truly different and not being dragged)
  useEffect(() => {
    // Only update if the value is genuinely different (not just a reference change)
    const valueChanged = JSON.stringify(value) !== JSON.stringify(actualValue);
    if (valueChanged && !isDraggingRef.current) {
      setActualValue(value);
      setSearchValue(value?.address || "");
      if (value && mapInstanceRef.current && updateMapLocationRef.current) {
        updateMapLocationRef.current(value, false); // Don't force zoom on external value changes
      }
    }
  }, [value]);



  // Simple validation function (no artificial debouncing needed for Google autocomplete)
  const validateSearchLength = useCallback((searchTerm: string) => {
    if (searchTerm.length > 0 && searchTerm.length < minSearchLength) {
      setSearchError(`Please enter at least ${minSearchLength} characters to search.`);
      return false;
    } else {
      setSearchError("");
      return true;
    }
  }, [minSearchLength]);

  // Search for places using Google Places API
  const searchPlaces = useCallback(async (query: string) => {
    if (!isGoogleMapsLoaded || !query || query.length < minSearchLength) {
      setSuggestions([]);
      setShowDropdown(false);
      return;
    }

    try {
      const service = new (window as any).google.maps.places.AutocompleteService();
      const request = {
        input: query,
        types: ['establishment', 'geocode'],
        fields: ['place_id', 'description', 'structured_formatting']
      };

      service.getPlacePredictions(request, (predictions: any[], status: any) => {
        if (status === (window as any).google.maps.places.PlacesServiceStatus.OK && predictions) {
          setSuggestions(predictions);
          setShowDropdown(true);
          setSelectedIndex(-1);
        } else {
          setSuggestions([]);
          setShowDropdown(false);
        }
      });
    } catch (error) {
      console.error('Error searching places:', error);
      setSuggestions([]);
      setShowDropdown(false);
    }
  }, [isGoogleMapsLoaded, minSearchLength]);

  // Debounced search
  const debouncedSearch = useCallback((query: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchPlaces(query);
    }, 300);
  }, [searchPlaces]);

  // Handle suggestion selection
  const selectSuggestion = useCallback(async (suggestion: any) => {
    if (!isGoogleMapsLoaded) return;

    try {
      setIsValidatingPlace(true);
      setShowDropdown(false);
      setSearchValue(suggestion.description);

      // Get place details
      const service = new (window as any).google.maps.places.PlacesService(
        document.createElement('div')
      );

      const request = {
        placeId: suggestion.place_id,
        fields: ['geometry', 'address_components', 'formatted_address', 'name', 'types', 'business_status', 'rating', 'website', 'formatted_phone_number']
      };

      service.getDetails(request, (place: any, status: any) => {
        setIsValidatingPlace(false);

        if (status === (window as any).google.maps.places.PlacesServiceStatus.OK && place) {
          let locationData = LocationHelper.extractAddressComponents(place);

          if (!storePlaceId) {
            locationData.placeId = undefined;
          }

          if (validatePlaceDetails) {
            locationData.name = place.name;
            locationData.types = place.types;
            locationData.businessStatus = place.business_status;
            locationData.rating = place.rating;
            locationData.website = place.website;
            locationData.phoneNumber = place.formatted_phone_number;
          }

          setActualValue(locationData);
          setSearchValue(locationData.formattedAddress || locationData.address);
          setSearchError("");

          // Update map location with street-level zoom
          if (mapInstanceRef.current && updateMapLocationRef.current) {
            // Reset user zoom adjustment flag for new location selection
            zoomPersistence.resetUserAdjustment();
            updateMapLocationRef.current(locationData, true);
          }

          if (onChange) {
            onChange(locationData);
          }
        } else {
          setSearchError("Failed to get place details");
        }
      });
    } catch (error) {
      setIsValidatingPlace(false);
      setSearchError("Error getting place details");
      console.error('Error getting place details:', error);
    }
  }, [isGoogleMapsLoaded, onChange, validatePlaceDetails, storePlaceId]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showDropdown || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => prev < suggestions.length - 1 ? prev + 1 : prev);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          selectSuggestion(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowDropdown(false);
        setSelectedIndex(-1);
        break;
    }
  }, [showDropdown, suggestions, selectedIndex, selectSuggestion]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          input.current && !input.current.contains(event.target as Node)) {
        setShowDropdown(false);
        setSelectedIndex(-1);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  // Cleanup search timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Enhanced place details fetching
  const fetchPlaceDetails = useCallback(async (placeId: string): Promise<any> => {
    if (!validatePlaceDetails || !placesServiceRef.current) return null;

    return new Promise((resolve, reject) => {
      setIsValidatingPlace(true);
      placesServiceRef.current.getDetails(
        {
          placeId,
          fields: [
            'place_id',
            'formatted_address',
            'geometry',
            'address_components',
            'name',
            'types',
            'business_status',
            'rating',
            'user_ratings_total',
            'website',
            'formatted_phone_number',
            'opening_hours',
            'photos'
          ]
        },
        (place: any, status: any) => {
          setIsValidatingPlace(false);
          if (status === (window as any).google.maps.places.PlacesServiceStatus.OK) {
            setPlaceDetails(place);
            resolve(place);
          } else {
            reject(new Error(`Place details fetch failed: ${status}`));
          }
        }
      );
    });
  }, [validatePlaceDetails]);

  const updateDragIndicator = useCallback((isDragging: boolean) => {
    if (dragIndicatorRef.current) {
      const indicator = dragIndicatorRef.current;
      if (isDragging) {
        indicator.className = indicator.className.replace('text-muted-foreground', 'text-primary border-primary shadow-lg scale-105');
        indicator.innerHTML = `
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span class="font-medium">Adjusting location...</span>
          </div>
        `;
      } else {
        indicator.className = indicator.className.replace('text-primary border-primary shadow-lg scale-105', 'text-muted-foreground');
        indicator.innerHTML = `
          <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
            </svg>
            <span>Drag marker to adjust</span>
          </div>
        `;
      }
    }
  }, []);



  const clearMarker = useCallback(() => {
    if (markerRef.current) {
      // Clear all event listeners
      (window as any).google?.maps?.event?.clearInstanceListeners(markerRef.current);
      // Remove marker from map
      markerRef.current.setMap(null);
      markerRef.current = null;
    }
    isDraggingRef.current = false;
    updateDragIndicator(false);
  }, [updateDragIndicator]);

  // Update map location function
  const updateMapLocation = useCallback((location: LocationData, forceStreetZoom: boolean = true) => {
    if (!mapInstanceRef.current) return;

    const position = { lat: location.lat, lng: location.lng };

    // Update map center
    mapInstanceRef.current.setCenter(position);

    // Only update zoom if user hasn't manually adjusted it or if we're forcing street zoom
    if (forceStreetZoom && !zoomPersistence.isUserAdjusted()) {
      const targetZoom = zoomPersistence.STREET_LEVEL_ZOOM;
      mapInstanceRef.current.setZoom(targetZoom);
      zoomPersistence.updateZoom(targetZoom);
    } else if (!zoomPersistence.isUserAdjusted()) {
      // Use current zoom or default to street level
      const targetZoom = zoomPersistence.getCurrentZoom() || zoomPersistence.STREET_LEVEL_ZOOM;
      mapInstanceRef.current.setZoom(targetZoom);
      zoomPersistence.updateZoom(targetZoom);
    }
  }, [zoomPersistence]);

  // Set the updateMapLocation function reference
  useEffect(() => {
    updateMapLocationRef.current = updateMapLocation;
  }, [updateMapLocation]);

  // Handle current location with useCallback to prevent re-renders
  const handleGetCurrentLocation = useCallback(async () => {
    if (!isGoogleMapsLoaded || isGettingCurrentLocation) return;

    setIsGettingCurrentLocation(true);
    try {
      const location = await getCurrentLocation();
      setActualValue(location);
      setSearchValue(location.address);
      if (onChange) {
        onChange(location);
      }
      // Update map if it exists with street-level zoom
      if (mapInstanceRef.current && updateMapLocationRef.current) {
        zoomPersistence.resetUserAdjustment();
        updateMapLocationRef.current(location, true);
      }
    } catch (error) {
      console.error("Error getting current location:", error);
    } finally {
      setIsGettingCurrentLocation(false);
    }
  }, [isGoogleMapsLoaded, isGettingCurrentLocation, getCurrentLocation, onChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchValue(newValue);

    // Clear location data if input is cleared
    if (!newValue) {
      setActualValue(undefined);
      setSearchError("");
      setPlaceDetails(null);
      setSuggestions([]);
      setShowDropdown(false);
      clearMarker();
      if (onChange) {
        onChange(undefined);
      }
    } else {
      // Validate search length and trigger search
      if (validateSearchLength(newValue)) {
        debouncedSearch(newValue);
      } else {
        setSuggestions([]);
        setShowDropdown(false);
      }
    }
  };

  const handleClearLocation = () => {
    setActualValue(undefined);
    setSearchValue("");
    setSearchError("");
    setPlaceDetails(null);
    clearMarker();
    if (onChange) {
      onChange(undefined);
    }
  };

  // Initialize autocomplete and map
  useEffect(() => {
    if (!isGoogleMapsLoaded || !input.current || !mapRef.current) return;

    // Initialize autocomplete
    const autocompleteOptions: any = {
      // No type restrictions - allow all types of places
      fields: [
        "place_id",
        "formatted_address",
        "geometry",
        "address_components",
        "name",
        "types",
        "business_status"
      ],
    };

    // No restrictions - allow global search for all types of places

    const autocomplete = new (window as any).google.maps.places.Autocomplete(input.current, autocompleteOptions);

    // Initialize map
    const defaultLocation = actualValue || { lat: 17.3850, lng: 78.4867 }; // Default to Hyderabad, India

    // Initialize zoom level with persistence
    const initialZoom = zoomPersistence.initializeZoom(!!actualValue);

    const mapOptions: any = {
      center: { lat: defaultLocation.lat, lng: defaultLocation.lng },
      zoom: initialZoom,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      zoomControl: true,
      gestureHandling: "cooperative",
      styles: [
        {
          featureType: "poi",
          elementType: "labels",
          stylers: [{ visibility: "off" }]
        }
      ]
    };

    // No map restrictions - allow global navigation

    const map = new (window as any).google.maps.Map(mapRef.current, mapOptions);

    // Initialize Places Service for place details
    if (validatePlaceDetails) {
      placesServiceRef.current = new (window as any).google.maps.places.PlacesService(map);
    }

    mapInstanceRef.current = map;

    // Add zoom change listener to track user adjustments
    map.addListener("zoom_changed", () => {
      const newZoom = map.getZoom();
      if (newZoom !== zoomPersistence.getCurrentZoom()) {
        zoomPersistence.updateZoom(newZoom, true); // Mark as user-adjusted
      }
    });

    autocomplete.addListener("place_changed", async () => {
      const place = autocomplete.getPlace();
      if (place.geometry && place.geometry.location) {
        // Use LocationHelper to extract enhanced address components
        let locationData = LocationHelper.extractAddressComponents(place);

        // Override placeId storage based on component setting
        if (!storePlaceId) {
          locationData.placeId = undefined;
        }

        // Fetch detailed place information if enabled
        if (validatePlaceDetails && place.place_id) {
          try {
            const detailedPlace = await fetchPlaceDetails(place.place_id);
            if (detailedPlace) {
              // Add additional metadata from place details
              locationData.name = detailedPlace.name;
              locationData.types = detailedPlace.types;
              locationData.businessStatus = detailedPlace.business_status;
              locationData.rating = detailedPlace.rating;
              locationData.website = detailedPlace.website;
              locationData.phoneNumber = detailedPlace.formatted_phone_number;

              // Re-extract address components from detailed place if available
              if (detailedPlace.address_components) {
                locationData = LocationHelper.extractAddressComponents(detailedPlace);
                if (!storePlaceId) {
                  locationData.placeId = undefined;
                }
              }
            }
          } catch (error) {
            console.warn("Failed to fetch place details:", error);
          }
        }

        setActualValue(locationData);
        setSearchValue(locationData.formattedAddress || locationData.address);
        setSearchError("");
        if (onChange) {
          onChange(locationData);
        }

        // Update map with street-level zoom for autocomplete selection
        zoomPersistence.resetUserAdjustment();
        updateMapLocation(locationData, true);
      }
    });

    autocompleteRef.current = autocomplete;

    // Add drag end listener for map to get center location
    map.addListener("dragend", () => {
      const center = map.getCenter();
      if (center) {
        const lat = center.lat();
        const lng = center.lng();

        // Reverse geocode to get address
        const geocoder = new (window as any).google.maps.Geocoder();
        geocoder.geocode({
          location: { lat, lng },
          language: 'en'
        }, async (results: any, status: any) => {
          if (status === "OK" && results && results[0]) {
            // Use LocationHelper to extract enhanced address components
            let locationData = LocationHelper.extractAddressFromGeocodingResult(results[0], lat, lng);

            // Override placeId storage based on component setting
            if (!storePlaceId) {
              locationData.placeId = undefined;
            }

            setActualValue(locationData);
            setSearchValue(locationData.formattedAddress || locationData.address);
            if (onChange) {
              onChange(locationData);
            }
          }
        });
      }
    });

    // Set initial map center if value exists
    if (actualValue && updateMapLocationRef.current) {
      updateMapLocationRef.current(actualValue, false); // Don't force zoom on initial load
    }

    return () => {
      clearMarker();
      if (autocompleteRef.current && (window as any).google) {
        (window as any).google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
      if (mapInstanceRef.current) {
        // Cleanup map event listeners
        (window as any).google?.maps?.event?.clearInstanceListeners(mapInstanceRef.current);
      }
    };
  }, [isGoogleMapsLoaded, onChange, validatePlaceDetails, storePlaceId, fetchPlaceDetails, actualValue, zoom]);

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <div className="flex justify-between space-x-2 items-center mb-1">
          <label htmlFor={name} className="mb-1 flex justify-between space-x-2 truncate text-xs font-medium">
            {title}
            {required && <span className="ml-1 text-red-500">*</span>}
          </label>

          <div className="flex items-center space-x-1">
            {hint && <HintTooltip text={hint} />}
          </div>
        </div>
      )}

      {/* Hidden input for form submission - JSON data */}
      {name && (
        <input
          type="hidden"
          name={name}
          value={actualValue ? JSON.stringify(actualValue) : ""}
          required={required}
        />
      )}

      {/* Map Container with Search Overlay */}
      <div className="relative">
        {/* Map Background */}
        <div
          ref={mapRef}
          style={{ height: mapHeight }}
          className="w-full rounded-xl border border-gray-200 bg-gray-50 shadow-sm overflow-hidden"
        />

        {/* Loading State */}
        {(!isGoogleMapsLoaded || isLoading) && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm rounded-xl z-30">
            <div className="flex flex-col items-center space-y-3">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <div className="absolute inset-0 h-8 w-8 rounded-full border-2 border-primary/20"></div>
              </div>
              <span className="text-sm font-medium text-gray-700">Loading interactive map...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/95 backdrop-blur-sm rounded-xl z-30">
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-red-50 rounded-full flex items-center justify-center">
                <MapPin className="w-8 h-8 text-red-500" />
              </div>
              <p className="text-sm font-semibold text-red-800 mb-2">Map unavailable</p>
              <p className="text-xs text-red-600 max-w-xs">{error}</p>
            </div>
          </div>
        )}

        {/* Map Instructions */}
        {isGoogleMapsLoaded && !error && (
          <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-600 border border-gray-200 shadow-sm z-10">
            <div className="flex items-center space-x-1">
              <Move className="w-3 h-3" />
              <span>Drag map to select location</span>
            </div>
          </div>
        )}

        {/* Fixed center pointer - Red pin */}
        {isGoogleMapsLoaded && !error && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-10">
            <div className="relative">
              <MapPin className="w-8 h-8 text-red-500 drop-shadow-lg fill-red-500" />
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-white rounded-full border border-red-600"></div>
            </div>
          </div>
        )}

        {/* Search Bar Overlay */}
        <div className="absolute top-4 left-4 right-4 z-20">
          <div className="relative group">
            {/* Maps icon - always at the left start */}
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2 z-10">
              <Map className="text-gray-600 h-4 w-4 flex-shrink-0 group-focus-within:text-gray-800 transition-colors" />
            </div>

            {/* Custom entity icon (if provided) - positioned after location icon */}
            {icon && (
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-8 z-10">
                <EntityIcon className="text-gray-400 group-focus-within:text-primary h-3 w-3 transition-colors" icon={icon} />
              </div>
            )}

            <Input
              ref={input}
              type="text"
              id={name}
              name={`${name}_search`}
              value={searchValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              required={required}
              disabled={disabled || readOnly || isLoading || isValidatingPlace}
              readOnly={readOnly}
              autoFocus={autoFocus}
              placeholder={placeholder || t("location.searchPlaceholder", "Search for a location...")}
              className={cn(
                "pl-8 h-11 border-gray-200 focus:border-primary focus:ring-primary/20 bg-white/95 backdrop-blur-sm shadow-lg",
                icon && "pl-12", // Extra padding when custom icon is present
                searchError && "border-red-300 focus:border-red-500",
                isValidatingPlace && "border-yellow-300",
                showCurrentLocationButton ? "pr-20" : "pr-12"
              )}
            />

            {/* Right side buttons container */}
            <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-2">
              {/* Validation indicator */}
              {isValidatingPlace && (
                <div className="flex items-center">
                  <Shield className="h-4 w-4 text-yellow-500 animate-pulse" />
                </div>
              )}

              {/* Current Location Button */}
              {showCurrentLocationButton && !readOnly && !disabled && (
                <button
                  type="button"
                  onClick={handleGetCurrentLocation}
                  disabled={!isGoogleMapsLoaded || isGettingCurrentLocation}
                  className="flex items-center justify-center w-7 h-7 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Use current location"
                >
                  {isGettingCurrentLocation ? (
                    <Loader2 className="h-3.5 w-3.5 animate-spin" />
                  ) : (
                    <Crosshair className="h-3.5 w-3.5" />
                  )}
                </button>
              )}

              {/* Clear button */}
              {actualValue && !readOnly && !disabled && (
                <button
                  type="button"
                  onClick={handleClearLocation}
                  className="flex items-center justify-center w-7 h-7 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-all duration-200"
                  title="Clear location"
                >
                  <span className="text-base font-medium">×</span>
                </button>
              )}
            </div>

            {/* Suggestions Dropdown */}
            {showDropdown && suggestions.length > 0 && (
              <div
                ref={dropdownRef}
                className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
              >
                {suggestions.map((suggestion, index) => (
                  <button
                    key={suggestion.place_id}
                    type="button"
                    className={cn(
                      "w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0 transition-colors cursor-pointer",
                      index === selectedIndex && "bg-blue-50 hover:bg-blue-100"
                    )}
                    onClick={() => selectSuggestion(suggestion)}
                    onMouseEnter={() => setSelectedIndex(index)}
                  >
                    <div className="flex items-start space-x-3">
                      <Map className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {suggestion.structured_formatting?.main_text || suggestion.description}
                        </div>
                        {suggestion.structured_formatting?.secondary_text && (
                          <div className="text-xs text-gray-500 truncate mt-0.5">
                            {suggestion.structured_formatting.secondary_text}
                          </div>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Search Error Display */}
        {searchError && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
              <span className="text-sm text-red-700">{searchError}</span>
            </div>
          </div>
        )}

        {/* Search status hint */}
        {searchValue && searchValue.length > 0 && !actualValue && (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">i</span>
              </div>
              <span className="text-sm text-blue-700">
                Start typing to see location suggestions...
              </span>
            </div>
          </div>
        )}

        {/* Place Details Display */}
        {showPlaceDetails && placeDetails && actualValue && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <MapPin className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 space-y-2">
                {placeDetails.name && (
                  <h4 className="font-medium text-blue-900">{placeDetails.name}</h4>
                )}
                {placeDetails.rating && (
                  <div className="flex items-center space-x-1">
                    <span className="text-sm text-blue-700">Rating:</span>
                    <span className="text-sm font-medium text-blue-900">{placeDetails.rating}/5</span>
                    {placeDetails.user_ratings_total && (
                      <span className="text-xs text-blue-600">({placeDetails.user_ratings_total} reviews)</span>
                    )}
                  </div>
                )}
                {placeDetails.business_status && (
                  <div className="flex items-center space-x-1">
                    <span className="text-sm text-blue-700">Status:</span>
                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full",
                      placeDetails.business_status === "OPERATIONAL" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    )}>
                      {placeDetails.business_status}
                    </span>
                  </div>
                )}
                {placeDetails.website && (
                  <a 
                    href={placeDetails.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 underline"
                  >
                    Visit Website
                  </a>
                )}
              </div>
            </div>
          </div>
        )}


      </div>

      {help && (
        <div className="mt-1">
          <div className="text-xs text-muted-foreground">{help}</div>
        </div>
      )}
    </div>
  );
};

export default forwardRef(MapWithSearchInput);

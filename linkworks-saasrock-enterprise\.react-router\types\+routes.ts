// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/api/workflows/executions/:id/continue": {
    params: {
      "id": string;
    };
  };
  "/api/workflows/stream/:executionId": {
    params: {
      "executionId": string;
    };
  };
  "/api/workflows/executions/:id": {
    params: {
      "id": string;
    };
  };
  "/help/:slug/:lang?/categories/:category": {
    params: {
      "slug": string;
      "lang"?: string;
      "category": string;
    };
  };
  "/help/:slug/:lang?/articles/:article": {
    params: {
      "slug": string;
      "lang"?: string;
      "article": string;
    };
  };
  "/subscribe/:tenant/:session/success": {
    params: {
      "tenant": string;
      "session": string;
    };
  };
  "/api/workflows/run/:id": {
    params: {
      "id": string;
    };
  };
  "/docs/:lang?/categories/:category": {
    params: {
      "lang"?: string;
      "category": string;
    };
  };
  "/b/:tenant/:postSlug": {
    params: {
      "tenant": string;
      "postSlug": string;
    };
  };
  "/api/events/webhooks/attempts/:id": {
    params: {
      "id": string;
    };
  };
  "/unauthorized/:permission/:tenant": {
    params: {
      "permission": string;
      "tenant": string;
    };
  };
  "/webhooks/events/accounts/created": {
    params: {};
  };
  "/docs/:lang?/articles/:article": {
    params: {
      "lang"?: string;
      "article": string;
    };
  };
  "/webhooks/events/roles/assigned": {
    params: {};
  };
  "/settings/subscription": {
    params: {};
  };
  "/my-subscription": {
    params: {};
  };
  "/account/:tenant/:page/:id1": {
    params: {
      "tenant": string;
      "page": string;
      "id1": string;
    };
  };
  "/api/code-generator/:entity": {
    params: {
      "entity": string;
    };
  };
  "/api/agent-from-row/:rowId": {
    params: {
      "rowId": string;
    };
  };
  "/postman_collection.json": {
    params: {};
  };
  "/api/analytics/page-views": {
    params: {};
  };
  "/pricing/:session/success": {
    params: {
      "session": string;
    };
  };
  "/unauthorized/:permission": {
    params: {
      "permission": string;
    };
  };
  "/my-dashboard": {
    params: {};
  };
  "/api/relationships": {
    params: {};
  };
  "/webhooks/email/postmark": {
    params: {};
  };
  "/my-api-keys": {
    params: {};
  };
  "/b/:tenant": {
    params: {
      "tenant": string;
    };
  };
  "/api/auth/refresh-token": {
    params: {};
  };
  "/webhooks/email/inbound": {
    params: {};
  };
  "/my-account": {
    params: {};
  };
  "/my-profile": {
    params: {};
  };
  "/account/:tenant/:page": {
    params: {
      "tenant": string;
      "page": string;
    };
  };
  "/api/ai/openai/chatgpt": {
    params: {};
  };
  "/api/auth/verify-token": {
    params: {};
  };
  "/api/relationships/:id": {
    params: {
      "id": string;
    };
  };
  "/oauth/github/callback": {
    params: {};
  };
  "/oauth/google/callback": {
    params: {};
  };
  "/api/analytics/events": {
    params: {};
  };
  "/api/analytics/public": {
    params: {};
  };
  "/api/entities/:entity": {
    params: {
      "entity": string;
    };
  };
  "/api/rewards/:agentId": {
    params: {
      "agentId": string;
    };
  };
  "/oauth/azure/callback": {
    params: {};
  };
  "/step-form-wizard/:id": {
    params: {
      "id": string;
    };
  };
  "/terms-and-conditions": {
    params: {};
  };
  "/api/stripe/webhooks": {
    params: {};
  };
  "/deactivated/:tenant": {
    params: {
      "tenant": string;
    };
  };
  "/api/accounts": {
    params: {};
  };
  "/help/:slug/:lang?": {
    params: {
      "slug": string;
      "lang"?: string;
    };
  };
  "/public/:entity/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/affiliate-program": {
    params: {};
  };
  "/api/:entity": {
    params: {
      "entity": string;
    };
  };
  "/api/keys/validate": {
    params: {};
  };
  "/subscribe/:tenant": {
    params: {
      "tenant": string;
    };
  };
  "/api/accounts/:id": {
    params: {
      "id": string;
    };
  };
  "/api/logicApp/:id": {
    params: {
      "id": string;
    };
  };
  "/api/:entity/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/api/ai/generate": {
    params: {};
  };
  "/api/s3/download": {
    params: {};
  };
  "/forgot-password": {
    params: {};
  };
  "/api/auth/login": {
    params: {};
  };
  "/api/widget/:id": {
    params: {
      "id": string;
    };
  };
  "/invitation/:id": {
    params: {
      "id": string;
    };
  };
  "/onboarding/:id": {
    params: {
      "id": string;
    };
  };
  "/privacy-policy": {
    params: {};
  };
  "/settings": {
    params: {};
  };
  "/api/ai/upload": {
    params: {};
  };
  "/surveys/:slug": {
    params: {
      "slug": string;
    };
  };
  "/surveys": {
    params: {};
  };
  "/docs/:lang?": {
    params: {
      "lang"?: string;
    };
  };
  "/oauth/google": {
    params: {};
  };
  "/api/auth/me": {
    params: {};
  };
  "/app/:tenant": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/:entity/relationships": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/:entity/all-in-one": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/:entity/:id/edit": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/:entity/export": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/:entity/import": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/:entity/:id": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/:entity/:id/share": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/:entity/:id/tags": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/:entity/new": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/:entity": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/workflow-engine": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/workflows/:id/executions": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/workflow-engine/workflows/:id/run/manual": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/workflow-engine/workflows/:id/run/stream": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/workflow-engine/workflows/:id/run/api": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/workflow-engine/workflows/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/workflow-engine/credentials": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/credentials/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/executions": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/templates": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/variables": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/variables/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/workflow-engine/variables/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/workflows": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/workflow-engine/danger": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/portals/:portal": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/pricing/edit/:product": {
    params: {
      "tenant": string;
      "portal": string;
      "product": string;
    };
  };
  "/app/:tenant/portals/:portal/analytics/page-views": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/analytics/visitors": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/analytics/events": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/pricing/stripe": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/pricing/new": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/analytics": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/settings": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/domains": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/pricing": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/danger": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/pages": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/pages/:name": {
    params: {
      "tenant": string;
      "portal": string;
      "name": string;
    };
  };
  "/app/:tenant/portals/:portal/users": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/:portal/users/:userId": {
    params: {
      "tenant": string;
      "portal": string;
      "userId": string;
    };
  };
  "/app/:tenant/portals/:portal/users/new": {
    params: {
      "tenant": string;
      "portal": string;
    };
  };
  "/app/:tenant/portals/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/portals": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/widgets": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/widgets/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/email-marketing": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/email-marketing/campaigns": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/email-marketing/campaigns/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/email-marketing/campaigns/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/email-marketing/activity": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/email-marketing/senders": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/email-marketing/senders/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/email-marketing/senders/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/blog/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/blog/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/rewardsClean": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/rewardsSeed": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/blog": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/dashboard": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/g/:group": {
    params: {
      "tenant": string;
      "group": string;
    };
  };
  "/app/:tenant/g/:group/:entity/relationships": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity/import-xlsx": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity/all-in-one": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity/cleanData": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity/:id/edit": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/g/:group/:entity/export": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity/import": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity/:id": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/g/:group/:entity/:id/share": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/g/:group/:entity/:id/tags": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/g/:group/:entity/new": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/:entity": {
    params: {
      "tenant": string;
      "group": string;
      "entity": string;
    };
  };
  "/app/:tenant/g/:group/milestones": {
    params: {
      "tenant": string;
      "group": string;
    };
  };
  "/app/:tenant/g/:group/milestones/:id": {
    params: {
      "tenant": string;
      "group": string;
      "id": string;
    };
  };
  "/app/:tenant/g/:group/rewards": {
    params: {
      "tenant": string;
      "group": string;
    };
  };
  "/app/:tenant/g/:group/rewards/:id": {
    params: {
      "tenant": string;
      "group": string;
      "id": string;
    };
  };
  "/app/:tenant/g/:group/all": {
    params: {
      "tenant": string;
      "group": string;
    };
  };
  "/app/:tenant/settings": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/entities/:entity/templates": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/settings/entities/:entity/templates/:id": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/entities/:entity/templates/new": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/settings/roles-and-permissions": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/roles-and-permissions/permissions": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/roles-and-permissions/roles": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/roles-and-permissions/users": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/entities/:entity": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/settings/entities/:entity/:id": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/entities/:entity/new": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/settings/logs/events/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/entities": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/subscription": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/logs/events": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/appearance": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/logs": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/api": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/api/docs": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/api/keys": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/api/keys/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/api/keys/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/api/logs": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/account": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/credits": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/members": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/members/groups/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/members/groups/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/members/edit/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/members/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/profile": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/groups": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/groups/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/settings/groups/new": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/settings/debug": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/emails": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/emails/:id": {
    params: {
      "tenant": string;
      "id": string;
    };
  };
  "/app/:tenant/404": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/crm": {
    params: {
      "tenant": string;
    };
  };
  "/app/:tenant/crm/:entity/relationships": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/crm/:entity/all-in-one": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/crm/:entity/:id/edit": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/crm/:entity/export": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/crm/:entity/import": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/crm/:entity/:id": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/crm/:entity/:id/share": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/crm/:entity/:id/tags": {
    params: {
      "tenant": string;
      "entity": string;
      "id": string;
    };
  };
  "/app/:tenant/crm/:entity/new": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/app/:tenant/crm/:entity": {
    params: {
      "tenant": string;
      "entity": string;
    };
  };
  "/healthcheck": {
    params: {};
  };
  "/new-account": {
    params: {};
  };
  "/:page/:id1": {
    params: {
      "page": string;
      "id1": string;
    };
  };
  "/blog/:slug": {
    params: {
      "slug": string;
    };
  };
  "/blog.rss": {
    params: {};
  };
  "/newsletter": {
    params: {};
  };
  "/verify/:id": {
    params: {
      "id": string;
    };
  };
  "/api/usage": {
    params: {};
  };
  "/changelog": {
    params: {};
  };
  "/api/docs": {
    params: {};
  };
  "/register": {
    params: {};
  };
  "/contact": {
    params: {};
  };
  "/pricing": {
    params: {};
  };
  "/swagger": {
    params: {};
  };
  "/logout": {
    params: {};
  };
  "/:page": {
    params: {
      "page": string;
    };
  };
  "/admin": {
    params: {};
  };
  "/admin/workflow-engine": {
    params: {};
  };
  "/admin/workflow-engine/workflows/:id/executions": {
    params: {
      "id": string;
    };
  };
  "/admin/workflow-engine/workflows/:id/run/manual": {
    params: {
      "id": string;
    };
  };
  "/admin/workflow-engine/workflows/:id/run/stream": {
    params: {
      "id": string;
    };
  };
  "/admin/workflow-engine/workflows/:id/run/api": {
    params: {
      "id": string;
    };
  };
  "/admin/workflow-engine/workflows/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/workflow-engine/credentials": {
    params: {};
  };
  "/admin/workflow-engine/credentials/new": {
    params: {};
  };
  "/admin/workflow-engine/executions": {
    params: {};
  };
  "/admin/workflow-engine/templates": {
    params: {};
  };
  "/admin/workflow-engine/variables": {
    params: {};
  };
  "/admin/workflow-engine/variables/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/workflow-engine/variables/new": {
    params: {};
  };
  "/admin/workflow-engine/workflows": {
    params: {};
  };
  "/admin/workflow-engine/danger": {
    params: {};
  };
  "/admin/setup/pricing/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard": {
    params: {};
  };
  "/admin/step-form-wizard/step-form-wizards/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/step-form-wizards/:id/sessions": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/step-form-wizards/:id/settings": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/step-form-wizards/:id/filters": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/step-form-wizards/:id/danger": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/step-form-wizards/:id/steps": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/step-form-wizards": {
    params: {};
  };
  "/admin/step-form-wizard/sessions/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/step-form-wizard/sessions": {
    params: {};
  };
  "/admin/email-marketing": {
    params: {};
  };
  "/admin/email-marketing/campaigns": {
    params: {};
  };
  "/admin/email-marketing/campaigns/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/email-marketing/campaigns/new": {
    params: {};
  };
  "/admin/email-marketing/activity": {
    params: {};
  };
  "/admin/email-marketing/senders": {
    params: {};
  };
  "/admin/email-marketing/senders/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/email-marketing/senders/new": {
    params: {};
  };
  "/admin/blog/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/blog/new": {
    params: {};
  };
  "/admin/knowledge-base": {
    params: {};
  };
  "/admin/knowledge-base/bases/:slug/articles/:lang/:id/edit": {
    params: {
      "slug": string;
      "lang": string;
      "id": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/articles/:lang/:id": {
    params: {
      "slug": string;
      "lang": string;
      "id": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/articles/:lang/:id/settings": {
    params: {
      "slug": string;
      "lang": string;
      "id": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/categories/:lang": {
    params: {
      "slug": string;
      "lang": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/:section": {
    params: {
      "slug": string;
      "lang": string;
      "id": string;
      "section": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/new": {
    params: {
      "slug": string;
      "lang": string;
      "id": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/categories/:lang/:id": {
    params: {
      "slug": string;
      "lang": string;
      "id": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/categories/:lang/new": {
    params: {
      "slug": string;
      "lang": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/articles/:lang": {
    params: {
      "slug": string;
      "lang": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/categories": {
    params: {
      "slug": string;
    };
  };
  "/admin/knowledge-base/bases/:slug/articles": {
    params: {
      "slug": string;
    };
  };
  "/admin/knowledge-base/bases/import": {
    params: {};
  };
  "/admin/knowledge-base/articles": {
    params: {};
  };
  "/admin/knowledge-base/danger": {
    params: {};
  };
  "/admin/knowledge-base/bases": {
    params: {};
  };
  "/admin/knowledge-base/bases/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/knowledge-base/bases/new": {
    params: {};
  };
  "/admin/feature-flags": {
    params: {};
  };
  "/admin/feature-flags/settings": {
    params: {};
  };
  "/admin/feature-flags/flags": {
    params: {};
  };
  "/admin/feature-flags/flags/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/feature-flags/flags/new": {
    params: {};
  };
  "/admin/notifications": {
    params: {};
  };
  "/admin/notifications/subscribers": {
    params: {};
  };
  "/admin/notifications/channels": {
    params: {};
  };
  "/admin/notifications/messages": {
    params: {};
  };
  "/admin/audit-trails": {
    params: {};
  };
  "/admin/entities/new": {
    params: {};
  };
  "/admin/blog": {
    params: {};
  };
  "/admin/affiliates": {
    params: {};
  };
  "/admin/components": {
    params: {};
  };
  "/admin/events/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/navigation": {
    params: {};
  };
  "/admin/onboarding": {
    params: {};
  };
  "/admin/onboarding/onboardings/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/onboardings/:id/sessions": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/onboardings/:id/settings": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/onboardings/:id/filters": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/onboardings/:id/danger": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/onboardings/:id/steps": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/sessions/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/onboarding/onboardings": {
    params: {};
  };
  "/admin/onboarding/sessions": {
    params: {};
  };
  "/admin/playground": {
    params: {};
  };
  "/admin/playground/repositories-and-models/row-repository": {
    params: {};
  };
  "/admin/playground/repositories-and-models/row-model": {
    params: {};
  };
  "/admin/playground/repositories-and-models": {
    params: {};
  };
  "/admin/playground/supabase/storage/buckets/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/playground/long-running-tasks": {
    params: {};
  };
  "/admin/playground/supabase/storage/buckets": {
    params: {};
  };
  "/admin/playground/enhanced-location-test": {
    params: {};
  };
  "/admin/playground/supabase/storage": {
    params: {};
  };
  "/admin/playground/crud/projects": {
    params: {};
  };
  "/admin/playground/location-input-test": {
    params: {};
  };
  "/admin/playground/ai/openai/chatgpt": {
    params: {};
  };
  "/admin/playground/crud/projects/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/playground/crud/projects/new": {
    params: {};
  };
  "/admin/playground/handlebars": {
    params: {};
  };
  "/admin/playground/monaco-editor": {
    params: {};
  };
  "/admin/playground/novel-editor": {
    params: {};
  };
  "/admin/playground/chat": {
    params: {};
  };
  "/admin/playground/crud": {
    params: {};
  };
  "/admin/analytics": {
    params: {};
  };
  "/admin/analytics/page-views": {
    params: {};
  };
  "/admin/analytics/overview": {
    params: {};
  };
  "/admin/analytics/settings": {
    params: {};
  };
  "/admin/analytics/visitors": {
    params: {};
  };
  "/admin/analytics/events": {
    params: {};
  };
  "/admin/dashboard": {
    params: {};
  };
  "/admin/help-desk": {
    params: {};
  };
  "/admin/help-desk/surveys/:id/submissions": {
    params: {
      "id": string;
    };
  };
  "/admin/help-desk/surveys/:id/edit": {
    params: {
      "id": string;
    };
  };
  "/admin/help-desk/inbound-emails": {
    params: {};
  };
  "/admin/help-desk/inbound-emails/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/help-desk/surveys": {
    params: {};
  };
  "/admin/help-desk/surveys/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/help-desk/surveys/new": {
    params: {};
  };
  "/admin/help-desk/feedback": {
    params: {};
  };
  "/admin/accounts": {
    params: {};
  };
  "/admin/accounts/ip-addresses/logs": {
    params: {};
  };
  "/admin/accounts/ip-addresses": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/account-users/:account": {
    params: {
      "account": string;
    };
  };
  "/admin/accounts/roles-and-permissions/account-users": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/admin-users": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/permissions": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/permissions/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/accounts/roles-and-permissions/permissions/new": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/roles": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/roles/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/accounts/roles-and-permissions/roles/new": {
    params: {};
  };
  "/admin/accounts/roles-and-permissions/seed": {
    params: {};
  };
  "/admin/accounts/subscriptions-revenue": {
    params: {};
  };
  "/admin/accounts/subscriptions": {
    params: {};
  };
  "/admin/accounts/subscriptions/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/accounts/blacklist": {
    params: {};
  };
  "/admin/accounts/users": {
    params: {};
  };
  "/admin/accounts/users/:user/roles": {
    params: {
      "user": string;
    };
  };
  "/admin/accounts/users/new": {
    params: {};
  };
  "/admin/accounts/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/entities": {
    params: {};
  };
  "/admin/entities/code-generator/generate/:entity": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/code-generator/files/:entity": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/code-generator": {
    params: {};
  };
  "/admin/entities/templates/manual": {
    params: {};
  };
  "/admin/entities/:entity/no-code": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/relationships": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/all-in-one": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/:id/edit": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/export": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/import": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/:id/share": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/:id/tags": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity/new": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/no-code/:entity": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/templates": {
    params: {};
  };
  "/admin/entities/formulas/logs": {
    params: {};
  };
  "/admin/entities/relationships": {
    params: {};
  };
  "/admin/entities/fake-rows": {
    params: {};
  };
  "/admin/entities/formulas": {
    params: {};
  };
  "/admin/entities/formulas/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/entities/formulas/new": {
    params: {};
  };
  "/admin/entities/:entity": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/relationships": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/relationships/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/relationships/new": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/properties": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/properties/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/properties/new": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/templates": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/templates/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/templates/new": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/webhooks": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/webhooks/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/entities/:entity/webhooks/new": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/details": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/danger": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/routes": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/views": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/logs": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/rows": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/:entity/api": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/no-code": {
    params: {};
  };
  "/admin/entities/no-code/lists/tasks": {
    params: {};
  };
  "/admin/entities/no-code/stats/count": {
    params: {};
  };
  "/admin/entities/groups": {
    params: {};
  };
  "/admin/entities/groups/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/entities/groups/new": {
    params: {};
  };
  "/admin/entities/views": {
    params: {};
  };
  "/admin/entities/views/new/:entity": {
    params: {
      "entity": string;
    };
  };
  "/admin/entities/views/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/entities/code": {
    params: {};
  };
  "/admin/entities/logs": {
    params: {};
  };
  "/admin/entities/rows": {
    params: {};
  };
  "/admin/entities/api": {
    params: {};
  };
  "/admin/settings": {
    params: {};
  };
  "/admin/settings/internationalization": {
    params: {};
  };
  "/admin/settings/transactional-emails": {
    params: {};
  };
  "/admin/settings/pricing/edit/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/settings/pricing/features": {
    params: {};
  };
  "/admin/settings/accounts/types": {
    params: {};
  };
  "/admin/settings/accounts/types/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/settings/accounts/types/new": {
    params: {};
  };
  "/admin/settings/authentication": {
    params: {};
  };
  "/admin/settings/pricing/new": {
    params: {};
  };
  "/admin/settings/analytics": {
    params: {};
  };
  "/admin/settings/accounts": {
    params: {};
  };
  "/admin/settings/cookies": {
    params: {};
  };
  "/admin/settings/general": {
    params: {};
  };
  "/admin/settings/pricing": {
    params: {};
  };
  "/admin/settings/profile": {
    params: {};
  };
  "/admin/settings/danger": {
    params: {};
  };
  "/admin/settings/cache": {
    params: {};
  };
  "/admin/settings/seo": {
    params: {};
  };
  "/admin/supabase": {
    params: {};
  };
  "/admin/metrics": {
    params: {};
  };
  "/admin/metrics/settings": {
    params: {};
  };
  "/admin/metrics/summary": {
    params: {};
  };
  "/admin/metrics/logs": {
    params: {};
  };
  "/admin/portals": {
    params: {};
  };
  "/admin/prompts": {
    params: {};
  };
  "/admin/prompts/executions/:id/results": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/executions/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder/:id/templates": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder/:id/variables": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder/:id/variables/:variable": {
    params: {
      "id": string;
      "variable": string;
    };
  };
  "/admin/prompts/builder/:id/variables/new": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder/:id/outputs": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping": {
    params: {
      "id": string;
      "output": string;
      "mapping": string;
    };
  };
  "/admin/prompts/builder/:id/outputs/:output/mappings/new": {
    params: {
      "id": string;
      "output": string;
    };
  };
  "/admin/prompts/builder/:id/outputs/:output": {
    params: {
      "id": string;
      "output": string;
    };
  };
  "/admin/prompts/builder/:id/outputs/new": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/results/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/builder": {
    params: {};
  };
  "/admin/prompts/builder/new": {
    params: {};
  };
  "/admin/prompts/groups": {
    params: {};
  };
  "/admin/prompts/groups/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/prompts/groups/new": {
    params: {};
  };
  "/admin/events": {
    params: {};
  };
  "/admin/pages": {
    params: {};
  };
  "/admin/pages/edit/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/pages/edit/:id/settings": {
    params: {
      "id": string;
    };
  };
  "/admin/pages/edit/:id/blocks": {
    params: {
      "id": string;
    };
  };
  "/admin/pages/edit/:id/seo": {
    params: {
      "id": string;
    };
  };
  "/admin/pages/seo": {
    params: {};
  };
  "/admin/pages/ab": {
    params: {};
  };
  "/admin/setup": {
    params: {};
  };
  "/admin/404": {
    params: {};
  };
  "/admin/api": {
    params: {};
  };
  "/admin/api/credits": {
    params: {};
  };
  "/admin/api/docs": {
    params: {};
  };
  "/admin/api/keys": {
    params: {};
  };
  "/admin/api/keys/:id": {
    params: {
      "id": string;
    };
  };
  "/admin/api/keys/new": {
    params: {};
  };
  "/admin/api/logs": {
    params: {};
  };
  "/admin/crm": {
    params: {};
  };
  "/admin/crm/:entity/relationships": {
    params: {
      "entity": string;
    };
  };
  "/admin/crm/:entity/all-in-one": {
    params: {
      "entity": string;
    };
  };
  "/admin/crm/:entity/:id/edit": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/crm/:entity/export": {
    params: {
      "entity": string;
    };
  };
  "/admin/crm/:entity/import": {
    params: {
      "entity": string;
    };
  };
  "/admin/crm/:entity/:id": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/crm/:entity/:id/share": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/crm/:entity/:id/tags": {
    params: {
      "entity": string;
      "id": string;
    };
  };
  "/admin/crm/:entity/new": {
    params: {
      "entity": string;
    };
  };
  "/admin/crm/:entity": {
    params: {
      "entity": string;
    };
  };
  "/admin/crm/sync": {
    params: {};
  };
  "/brand": {
    params: {};
  };
  "/debug": {
    params: {};
  };
  "/login": {
    params: {};
  };
  "/reset": {
    params: {};
  };
  "/blog": {
    params: {};
  };
  "/401": {
    params: {};
  };
  "/404": {
    params: {};
  };
  "/app": {
    params: {};
  };
  "/dev": {
    params: {};
  };
  "/*": {
    params: {
      "*": string;
    };
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/api/workflows/executions/:id/continue" | "/api/workflows/stream/:executionId" | "/api/workflows/executions/:id" | "/help/:slug/:lang?/categories/:category" | "/help/:slug/:lang?/articles/:article" | "/subscribe/:tenant/:session/success" | "/api/workflows/run/:id" | "/docs/:lang?/categories/:category" | "/b/:tenant/:postSlug" | "/api/events/webhooks/attempts/:id" | "/unauthorized/:permission/:tenant" | "/webhooks/events/accounts/created" | "/docs/:lang?/articles/:article" | "/webhooks/events/roles/assigned" | "/settings/subscription" | "/my-subscription" | "/account/:tenant/:page/:id1" | "/api/code-generator/:entity" | "/api/agent-from-row/:rowId" | "/postman_collection.json" | "/api/analytics/page-views" | "/pricing/:session/success" | "/unauthorized/:permission" | "/my-dashboard" | "/api/relationships" | "/webhooks/email/postmark" | "/my-api-keys" | "/b/:tenant" | "/api/auth/refresh-token" | "/webhooks/email/inbound" | "/my-account" | "/my-profile" | "/account/:tenant/:page" | "/api/ai/openai/chatgpt" | "/api/auth/verify-token" | "/api/relationships/:id" | "/oauth/github/callback" | "/oauth/google/callback" | "/api/analytics/events" | "/api/analytics/public" | "/api/entities/:entity" | "/api/rewards/:agentId" | "/oauth/azure/callback" | "/step-form-wizard/:id" | "/terms-and-conditions" | "/api/stripe/webhooks" | "/deactivated/:tenant" | "/api/accounts" | "/help/:slug/:lang?" | "/public/:entity/:id" | "/affiliate-program" | "/api/:entity" | "/api/keys/validate" | "/subscribe/:tenant" | "/api/accounts/:id" | "/api/logicApp/:id" | "/api/:entity/:id" | "/api/ai/generate" | "/api/s3/download" | "/forgot-password" | "/api/auth/login" | "/api/widget/:id" | "/invitation/:id" | "/onboarding/:id" | "/privacy-policy" | "/settings" | "/api/ai/upload" | "/surveys/:slug" | "/surveys" | "/docs/:lang?" | "/oauth/google" | "/api/auth/me" | "/app/:tenant" | "/app/:tenant/:entity/relationships" | "/app/:tenant/:entity/all-in-one" | "/app/:tenant/:entity/:id/edit" | "/app/:tenant/:entity/export" | "/app/:tenant/:entity/import" | "/app/:tenant/:entity/:id" | "/app/:tenant/:entity/:id/share" | "/app/:tenant/:entity/:id/tags" | "/app/:tenant/:entity/new" | "/app/:tenant/:entity" | "/app/:tenant/workflow-engine" | "/app/:tenant/workflow-engine/workflows/:id/executions" | "/app/:tenant/workflow-engine/workflows/:id/run/manual" | "/app/:tenant/workflow-engine/workflows/:id/run/stream" | "/app/:tenant/workflow-engine/workflows/:id/run/api" | "/app/:tenant/workflow-engine/workflows/:id" | "/app/:tenant/workflow-engine/credentials" | "/app/:tenant/workflow-engine/credentials/new" | "/app/:tenant/workflow-engine/executions" | "/app/:tenant/workflow-engine/templates" | "/app/:tenant/workflow-engine/variables" | "/app/:tenant/workflow-engine/variables/:id" | "/app/:tenant/workflow-engine/variables/new" | "/app/:tenant/workflow-engine/workflows" | "/app/:tenant/workflow-engine/danger" | "/app/:tenant/portals/:portal" | "/app/:tenant/portals/:portal/pricing/edit/:product" | "/app/:tenant/portals/:portal/analytics/page-views" | "/app/:tenant/portals/:portal/analytics/visitors" | "/app/:tenant/portals/:portal/analytics/events" | "/app/:tenant/portals/:portal/pricing/stripe" | "/app/:tenant/portals/:portal/pricing/new" | "/app/:tenant/portals/:portal/analytics" | "/app/:tenant/portals/:portal/settings" | "/app/:tenant/portals/:portal/domains" | "/app/:tenant/portals/:portal/pricing" | "/app/:tenant/portals/:portal/danger" | "/app/:tenant/portals/:portal/pages" | "/app/:tenant/portals/:portal/pages/:name" | "/app/:tenant/portals/:portal/users" | "/app/:tenant/portals/:portal/users/:userId" | "/app/:tenant/portals/:portal/users/new" | "/app/:tenant/portals/new" | "/app/:tenant/portals" | "/app/:tenant/widgets" | "/app/:tenant/widgets/:id" | "/app/:tenant/email-marketing" | "/app/:tenant/email-marketing/campaigns" | "/app/:tenant/email-marketing/campaigns/:id" | "/app/:tenant/email-marketing/campaigns/new" | "/app/:tenant/email-marketing/activity" | "/app/:tenant/email-marketing/senders" | "/app/:tenant/email-marketing/senders/:id" | "/app/:tenant/email-marketing/senders/new" | "/app/:tenant/blog/:id" | "/app/:tenant/blog/new" | "/app/:tenant/rewardsClean" | "/app/:tenant/rewardsSeed" | "/app/:tenant/blog" | "/app/:tenant/dashboard" | "/app/:tenant/g/:group" | "/app/:tenant/g/:group/:entity/relationships" | "/app/:tenant/g/:group/:entity/import-xlsx" | "/app/:tenant/g/:group/:entity/all-in-one" | "/app/:tenant/g/:group/:entity/cleanData" | "/app/:tenant/g/:group/:entity/:id/edit" | "/app/:tenant/g/:group/:entity/export" | "/app/:tenant/g/:group/:entity/import" | "/app/:tenant/g/:group/:entity/:id" | "/app/:tenant/g/:group/:entity/:id/share" | "/app/:tenant/g/:group/:entity/:id/tags" | "/app/:tenant/g/:group/:entity/new" | "/app/:tenant/g/:group/:entity" | "/app/:tenant/g/:group/milestones" | "/app/:tenant/g/:group/milestones/:id" | "/app/:tenant/g/:group/rewards" | "/app/:tenant/g/:group/rewards/:id" | "/app/:tenant/g/:group/all" | "/app/:tenant/settings" | "/app/:tenant/settings/entities/:entity/templates" | "/app/:tenant/settings/entities/:entity/templates/:id" | "/app/:tenant/settings/entities/:entity/templates/new" | "/app/:tenant/settings/roles-and-permissions" | "/app/:tenant/settings/roles-and-permissions/permissions" | "/app/:tenant/settings/roles-and-permissions/roles" | "/app/:tenant/settings/roles-and-permissions/users" | "/app/:tenant/settings/entities/:entity" | "/app/:tenant/settings/entities/:entity/:id" | "/app/:tenant/settings/entities/:entity/new" | "/app/:tenant/settings/logs/events/:id" | "/app/:tenant/settings/entities" | "/app/:tenant/settings/subscription" | "/app/:tenant/settings/logs/events" | "/app/:tenant/settings/appearance" | "/app/:tenant/settings/logs" | "/app/:tenant/settings/api" | "/app/:tenant/settings/api/docs" | "/app/:tenant/settings/api/keys" | "/app/:tenant/settings/api/keys/:id" | "/app/:tenant/settings/api/keys/new" | "/app/:tenant/settings/api/logs" | "/app/:tenant/settings/account" | "/app/:tenant/settings/credits" | "/app/:tenant/settings/members" | "/app/:tenant/settings/members/groups/:id" | "/app/:tenant/settings/members/groups/new" | "/app/:tenant/settings/members/edit/:id" | "/app/:tenant/settings/members/new" | "/app/:tenant/settings/profile" | "/app/:tenant/settings/groups" | "/app/:tenant/settings/groups/:id" | "/app/:tenant/settings/groups/new" | "/app/:tenant/settings/debug" | "/app/:tenant/emails" | "/app/:tenant/emails/:id" | "/app/:tenant/404" | "/app/:tenant/crm" | "/app/:tenant/crm/:entity/relationships" | "/app/:tenant/crm/:entity/all-in-one" | "/app/:tenant/crm/:entity/:id/edit" | "/app/:tenant/crm/:entity/export" | "/app/:tenant/crm/:entity/import" | "/app/:tenant/crm/:entity/:id" | "/app/:tenant/crm/:entity/:id/share" | "/app/:tenant/crm/:entity/:id/tags" | "/app/:tenant/crm/:entity/new" | "/app/:tenant/crm/:entity" | "/healthcheck" | "/new-account" | "/:page/:id1" | "/blog/:slug" | "/blog.rss" | "/newsletter" | "/verify/:id" | "/api/usage" | "/changelog" | "/api/docs" | "/register" | "/contact" | "/pricing" | "/swagger" | "/logout" | "/:page" | "/admin" | "/admin/workflow-engine" | "/admin/workflow-engine/workflows/:id/executions" | "/admin/workflow-engine/workflows/:id/run/manual" | "/admin/workflow-engine/workflows/:id/run/stream" | "/admin/workflow-engine/workflows/:id/run/api" | "/admin/workflow-engine/workflows/:id" | "/admin/workflow-engine/credentials" | "/admin/workflow-engine/credentials/new" | "/admin/workflow-engine/executions" | "/admin/workflow-engine/templates" | "/admin/workflow-engine/variables" | "/admin/workflow-engine/variables/:id" | "/admin/workflow-engine/variables/new" | "/admin/workflow-engine/workflows" | "/admin/workflow-engine/danger" | "/admin/setup/pricing/:id" | "/admin/step-form-wizard" | "/admin/step-form-wizard/step-form-wizards/:id" | "/admin/step-form-wizard/step-form-wizards/:id/sessions" | "/admin/step-form-wizard/step-form-wizards/:id/settings" | "/admin/step-form-wizard/step-form-wizards/:id/filters" | "/admin/step-form-wizard/step-form-wizards/:id/danger" | "/admin/step-form-wizard/step-form-wizards/:id/steps" | "/admin/step-form-wizard/step-form-wizards" | "/admin/step-form-wizard/sessions/:id" | "/admin/step-form-wizard/sessions" | "/admin/email-marketing" | "/admin/email-marketing/campaigns" | "/admin/email-marketing/campaigns/:id" | "/admin/email-marketing/campaigns/new" | "/admin/email-marketing/activity" | "/admin/email-marketing/senders" | "/admin/email-marketing/senders/:id" | "/admin/email-marketing/senders/new" | "/admin/blog/:id" | "/admin/blog/new" | "/admin/knowledge-base" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/edit" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/settings" | "/admin/knowledge-base/bases/:slug/categories/:lang" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/:section" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/new" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id" | "/admin/knowledge-base/bases/:slug/categories/:lang/new" | "/admin/knowledge-base/bases/:slug/articles/:lang" | "/admin/knowledge-base/bases/:slug/categories" | "/admin/knowledge-base/bases/:slug/articles" | "/admin/knowledge-base/bases/import" | "/admin/knowledge-base/articles" | "/admin/knowledge-base/danger" | "/admin/knowledge-base/bases" | "/admin/knowledge-base/bases/:id" | "/admin/knowledge-base/bases/new" | "/admin/feature-flags" | "/admin/feature-flags/settings" | "/admin/feature-flags/flags" | "/admin/feature-flags/flags/:id" | "/admin/feature-flags/flags/new" | "/admin/notifications" | "/admin/notifications/subscribers" | "/admin/notifications/channels" | "/admin/notifications/messages" | "/admin/audit-trails" | "/admin/entities/new" | "/admin/blog" | "/admin/affiliates" | "/admin/components" | "/admin/events/:id" | "/admin/navigation" | "/admin/onboarding" | "/admin/onboarding/onboardings/:id" | "/admin/onboarding/onboardings/:id/sessions" | "/admin/onboarding/onboardings/:id/settings" | "/admin/onboarding/onboardings/:id/filters" | "/admin/onboarding/onboardings/:id/danger" | "/admin/onboarding/onboardings/:id/steps" | "/admin/onboarding/sessions/:id" | "/admin/onboarding/onboardings" | "/admin/onboarding/sessions" | "/admin/playground" | "/admin/playground/repositories-and-models/row-repository" | "/admin/playground/repositories-and-models/row-model" | "/admin/playground/repositories-and-models" | "/admin/playground/supabase/storage/buckets/:id" | "/admin/playground/long-running-tasks" | "/admin/playground/supabase/storage/buckets" | "/admin/playground/enhanced-location-test" | "/admin/playground/supabase/storage" | "/admin/playground/crud/projects" | "/admin/playground/location-input-test" | "/admin/playground/ai/openai/chatgpt" | "/admin/playground/crud/projects/:id" | "/admin/playground/crud/projects/new" | "/admin/playground/handlebars" | "/admin/playground/monaco-editor" | "/admin/playground/novel-editor" | "/admin/playground/chat" | "/admin/playground/crud" | "/admin/analytics" | "/admin/analytics/page-views" | "/admin/analytics/overview" | "/admin/analytics/settings" | "/admin/analytics/visitors" | "/admin/analytics/events" | "/admin/dashboard" | "/admin/help-desk" | "/admin/help-desk/surveys/:id/submissions" | "/admin/help-desk/surveys/:id/edit" | "/admin/help-desk/inbound-emails" | "/admin/help-desk/inbound-emails/:id" | "/admin/help-desk/surveys" | "/admin/help-desk/surveys/:id" | "/admin/help-desk/surveys/new" | "/admin/help-desk/feedback" | "/admin/accounts" | "/admin/accounts/ip-addresses/logs" | "/admin/accounts/ip-addresses" | "/admin/accounts/roles-and-permissions" | "/admin/accounts/roles-and-permissions/account-users/:account" | "/admin/accounts/roles-and-permissions/account-users" | "/admin/accounts/roles-and-permissions/admin-users" | "/admin/accounts/roles-and-permissions/permissions" | "/admin/accounts/roles-and-permissions/permissions/:id" | "/admin/accounts/roles-and-permissions/permissions/new" | "/admin/accounts/roles-and-permissions/roles" | "/admin/accounts/roles-and-permissions/roles/:id" | "/admin/accounts/roles-and-permissions/roles/new" | "/admin/accounts/roles-and-permissions/seed" | "/admin/accounts/subscriptions-revenue" | "/admin/accounts/subscriptions" | "/admin/accounts/subscriptions/:id" | "/admin/accounts/blacklist" | "/admin/accounts/users" | "/admin/accounts/users/:user/roles" | "/admin/accounts/users/new" | "/admin/accounts/:id" | "/admin/entities" | "/admin/entities/code-generator/generate/:entity" | "/admin/entities/code-generator/files/:entity" | "/admin/entities/code-generator" | "/admin/entities/templates/manual" | "/admin/entities/:entity/no-code" | "/admin/entities/:entity/no-code/:entity/relationships" | "/admin/entities/:entity/no-code/:entity/all-in-one" | "/admin/entities/:entity/no-code/:entity/:id/edit" | "/admin/entities/:entity/no-code/:entity/export" | "/admin/entities/:entity/no-code/:entity/import" | "/admin/entities/:entity/no-code/:entity/:id" | "/admin/entities/:entity/no-code/:entity/:id/share" | "/admin/entities/:entity/no-code/:entity/:id/tags" | "/admin/entities/:entity/no-code/:entity/new" | "/admin/entities/:entity/no-code/:entity" | "/admin/entities/templates" | "/admin/entities/formulas/logs" | "/admin/entities/relationships" | "/admin/entities/fake-rows" | "/admin/entities/formulas" | "/admin/entities/formulas/:id" | "/admin/entities/formulas/new" | "/admin/entities/:entity" | "/admin/entities/:entity/relationships" | "/admin/entities/:entity/relationships/:id" | "/admin/entities/:entity/relationships/new" | "/admin/entities/:entity/properties" | "/admin/entities/:entity/properties/:id" | "/admin/entities/:entity/properties/new" | "/admin/entities/:entity/templates" | "/admin/entities/:entity/templates/:id" | "/admin/entities/:entity/templates/new" | "/admin/entities/:entity/webhooks" | "/admin/entities/:entity/webhooks/:id" | "/admin/entities/:entity/webhooks/new" | "/admin/entities/:entity/details" | "/admin/entities/:entity/danger" | "/admin/entities/:entity/routes" | "/admin/entities/:entity/views" | "/admin/entities/:entity/logs" | "/admin/entities/:entity/rows" | "/admin/entities/:entity/api" | "/admin/entities/no-code" | "/admin/entities/no-code/lists/tasks" | "/admin/entities/no-code/stats/count" | "/admin/entities/groups" | "/admin/entities/groups/:id" | "/admin/entities/groups/new" | "/admin/entities/views" | "/admin/entities/views/new/:entity" | "/admin/entities/views/:id" | "/admin/entities/code" | "/admin/entities/logs" | "/admin/entities/rows" | "/admin/entities/api" | "/admin/settings" | "/admin/settings/internationalization" | "/admin/settings/transactional-emails" | "/admin/settings/pricing/edit/:id" | "/admin/settings/pricing/features" | "/admin/settings/accounts/types" | "/admin/settings/accounts/types/:id" | "/admin/settings/accounts/types/new" | "/admin/settings/authentication" | "/admin/settings/pricing/new" | "/admin/settings/analytics" | "/admin/settings/accounts" | "/admin/settings/cookies" | "/admin/settings/general" | "/admin/settings/pricing" | "/admin/settings/profile" | "/admin/settings/danger" | "/admin/settings/cache" | "/admin/settings/seo" | "/admin/supabase" | "/admin/metrics" | "/admin/metrics/settings" | "/admin/metrics/summary" | "/admin/metrics/logs" | "/admin/portals" | "/admin/prompts" | "/admin/prompts/executions/:id/results" | "/admin/prompts/executions/:id" | "/admin/prompts/builder/:id" | "/admin/prompts/builder/:id/templates" | "/admin/prompts/builder/:id/variables" | "/admin/prompts/builder/:id/variables/:variable" | "/admin/prompts/builder/:id/variables/new" | "/admin/prompts/builder/:id/outputs" | "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping" | "/admin/prompts/builder/:id/outputs/:output/mappings/new" | "/admin/prompts/builder/:id/outputs/:output" | "/admin/prompts/builder/:id/outputs/new" | "/admin/prompts/results/:id" | "/admin/prompts/builder" | "/admin/prompts/builder/new" | "/admin/prompts/groups" | "/admin/prompts/groups/:id" | "/admin/prompts/groups/new" | "/admin/events" | "/admin/pages" | "/admin/pages/edit/:id" | "/admin/pages/edit/:id/settings" | "/admin/pages/edit/:id/blocks" | "/admin/pages/edit/:id/seo" | "/admin/pages/seo" | "/admin/pages/ab" | "/admin/setup" | "/admin/404" | "/admin/api" | "/admin/api/credits" | "/admin/api/docs" | "/admin/api/keys" | "/admin/api/keys/:id" | "/admin/api/keys/new" | "/admin/api/logs" | "/admin/crm" | "/admin/crm/:entity/relationships" | "/admin/crm/:entity/all-in-one" | "/admin/crm/:entity/:id/edit" | "/admin/crm/:entity/export" | "/admin/crm/:entity/import" | "/admin/crm/:entity/:id" | "/admin/crm/:entity/:id/share" | "/admin/crm/:entity/:id/tags" | "/admin/crm/:entity/new" | "/admin/crm/:entity" | "/admin/crm/sync" | "/brand" | "/debug" | "/login" | "/reset" | "/blog" | "/401" | "/404" | "/app" | "/dev" | "/*";
  };
  "routes\\api\\__workflows\\workflows.executions.$id.continue.tsx": {
    id: "routes/api/__workflows/workflows.executions.$id.continue";
    page: "/api/workflows/executions/:id/continue";
  };
  "routes\\api\\__workflows\\workflows.stream.$executionId.tsx": {
    id: "routes/api/__workflows/workflows.stream.$executionId";
    page: "/api/workflows/stream/:executionId";
  };
  "routes\\api\\__workflows\\workflows.executions.$id.tsx": {
    id: "routes/api/__workflows/workflows.executions.$id";
    page: "/api/workflows/executions/:id";
  };
  "routes\\help\\$slug.($lang).categories.$category.tsx": {
    id: "routes/help/$slug.($lang).categories.$category";
    page: "/help/:slug/:lang?/categories/:category";
  };
  "routes\\help\\$slug.($lang).articles.$article.tsx": {
    id: "routes/help/$slug.($lang).articles.$article";
    page: "/help/:slug/:lang?/articles/:article";
  };
  "routes\\subscribe.$tenant.$session.success.tsx": {
    id: "routes/subscribe.$tenant.$session.success";
    page: "/subscribe/:tenant/:session/success";
  };
  "routes\\api\\__workflows\\workflows.run.$id.tsx": {
    id: "routes/api/__workflows/workflows.run.$id";
    page: "/api/workflows/run/:id";
  };
  "routes\\docs\\($lang).categories.$category.tsx": {
    id: "routes/docs/($lang).categories.$category";
    page: "/docs/:lang?/categories/:category";
  };
  "routes\\__tenant-blog\\b.$tenant.$postSlug.tsx": {
    id: "routes/__tenant-blog/b.$tenant.$postSlug";
    page: "/b/:tenant/:postSlug";
  };
  "routes\\api\\events.webhooks.attempts.$id.ts": {
    id: "routes/api/events.webhooks.attempts.$id";
    page: "/api/events/webhooks/attempts/:id";
  };
  "routes\\unauthorized.$permission.$tenant.tsx": {
    id: "routes/unauthorized.$permission.$tenant";
    page: "/unauthorized/:permission/:tenant";
  };
  "routes\\webhooks\\events\\accounts\\created.ts": {
    id: "routes/webhooks/events/accounts/created";
    page: "/webhooks/events/accounts/created";
  };
  "routes\\docs\\($lang).articles.$article.tsx": {
    id: "routes/docs/($lang).articles.$article";
    page: "/docs/:lang?/articles/:article";
  };
  "routes\\webhooks\\events\\roles\\assigned.ts": {
    id: "routes/webhooks/events/roles/assigned";
    page: "/webhooks/events/roles/assigned";
  };
  "routes\\__app\\settings.subscription.tsx": {
    id: "routes/__app/settings.subscription";
    page: "/settings/subscription";
  };
  "routes\\__redirects\\my-subscription.tsx": {
    id: "routes/__redirects/my-subscription";
    page: "/my-subscription";
  };
  "routes\\account\\$tenant.$page.$id1.tsx": {
    id: "routes/account/$tenant.$page.$id1";
    page: "/account/:tenant/:page/:id1";
  };
  "routes\\api\\code-generator.$entity.tsx": {
    id: "routes/api/code-generator.$entity";
    page: "/api/code-generator/:entity";
  };
  "routes\\api\\agent-from-row.$rowId.ts": {
    id: "routes/api/agent-from-row.$rowId";
    page: "/api/agent-from-row/:rowId";
  };
  "routes\\postman_collection[.]json.ts": {
    id: "routes/postman_collection[.]json";
    page: "/postman_collection.json";
  };
  "routes\\api\\analytics\\page-views.tsx": {
    id: "routes/api/analytics/page-views";
    page: "/api/analytics/page-views";
  };
  "routes\\pricing.$session.success.tsx": {
    id: "routes/pricing.$session.success";
    page: "/pricing/:session/success";
  };
  "routes\\unauthorized.$permission.tsx": {
    id: "routes/unauthorized.$permission";
    page: "/unauthorized/:permission";
  };
  "routes\\__redirects\\my-dashboard.tsx": {
    id: "routes/__redirects/my-dashboard";
    page: "/my-dashboard";
  };
  "routes\\api\\relationships\\index.tsx": {
    id: "routes/api/relationships/index";
    page: "/api/relationships";
  };
  "routes\\webhooks\\email\\postmark.ts": {
    id: "routes/webhooks/email/postmark";
    page: "/webhooks/email/postmark";
  };
  "routes\\__redirects\\my-api-keys.tsx": {
    id: "routes/__redirects/my-api-keys";
    page: "/my-api-keys";
  };
  "routes\\__tenant-blog\\b.$tenant.tsx": {
    id: "routes/__tenant-blog/b.$tenant";
    page: "/b/:tenant";
  };
  "routes\\api\\auth\\refresh-token.ts": {
    id: "routes/api/auth/refresh-token";
    page: "/api/auth/refresh-token";
  };
  "routes\\webhooks\\email\\inbound.tsx": {
    id: "routes/webhooks/email/inbound";
    page: "/webhooks/email/inbound";
  };
  "routes\\__redirects\\my-account.tsx": {
    id: "routes/__redirects/my-account";
    page: "/my-account";
  };
  "routes\\__redirects\\my-profile.tsx": {
    id: "routes/__redirects/my-profile";
    page: "/my-profile";
  };
  "routes\\account\\$tenant.$page.tsx": {
    id: "routes/account/$tenant.$page";
    page: "/account/:tenant/:page";
  };
  "routes\\api\\ai\\openai\\chatgpt.ts": {
    id: "routes/api/ai/openai/chatgpt";
    page: "/api/ai/openai/chatgpt";
  };
  "routes\\api\\auth\\verify-token.ts": {
    id: "routes/api/auth/verify-token";
    page: "/api/auth/verify-token";
  };
  "routes\\api\\relationships\\$id.tsx": {
    id: "routes/api/relationships/$id";
    page: "/api/relationships/:id";
  };
  "routes\\oauth\\github\\callback.tsx": {
    id: "routes/oauth/github/callback";
    page: "/oauth/github/callback";
  };
  "routes\\oauth\\google.callback.tsx": {
    id: "routes/oauth/google.callback";
    page: "/oauth/google/callback";
  };
  "routes\\api\\analytics\\events.tsx": {
    id: "routes/api/analytics/events";
    page: "/api/analytics/events";
  };
  "routes\\api\\analytics\\public.ts": {
    id: "routes/api/analytics/public";
    page: "/api/analytics/public";
  };
  "routes\\api\\entities\\$entity.ts": {
    id: "routes/api/entities/$entity";
    page: "/api/entities/:entity";
  };
  "routes\\api\\rewards.$agentId.tsx": {
    id: "routes/api/rewards.$agentId";
    page: "/api/rewards/:agentId";
  };
  "routes\\oauth\\azure\\callback.tsx": {
    id: "routes/oauth/azure/callback";
    page: "/oauth/azure/callback";
  };
  "routes\\step-form-wizard.$id.tsx": {
    id: "routes/step-form-wizard.$id";
    page: "/step-form-wizard/:id";
  };
  "routes\\terms-and-conditions.tsx": {
    id: "routes/terms-and-conditions";
    page: "/terms-and-conditions";
  };
  "routes\\api\\stripe.webhooks.tsx": {
    id: "routes/api/stripe.webhooks";
    page: "/api/stripe/webhooks";
  };
  "routes\\deactivated.$tenant.tsx": {
    id: "routes/deactivated.$tenant";
    page: "/deactivated/:tenant";
  };
  "routes\\api\\accounts\\index.tsx": {
    id: "routes/api/accounts/index";
    page: "/api/accounts";
  };
  "routes\\help\\$slug.($lang).tsx": {
    id: "routes/help/$slug.($lang)";
    page: "/help/:slug/:lang?";
  };
  "routes\\public\\$entity.$id.tsx": {
    id: "routes/public/$entity.$id";
    page: "/public/:entity/:id";
  };
  "routes\\affiliate-program.tsx": {
    id: "routes/affiliate-program";
    page: "/affiliate-program";
  };
  "routes\\api\\$entity\\index.tsx": {
    id: "routes/api/$entity/index";
    page: "/api/:entity";
  };
  "routes\\api\\keys.validate.tsx": {
    id: "routes/api/keys.validate";
    page: "/api/keys/validate";
  };
  "routes\\subscribe.$tenant.tsx": {
    id: "routes/subscribe.$tenant";
    page: "/subscribe/:tenant";
  };
  "routes\\api\\accounts\\$id.tsx": {
    id: "routes/api/accounts/$id";
    page: "/api/accounts/:id";
  };
  "routes\\api\\logicApp\\$id.tsx": {
    id: "routes/api/logicApp/$id";
    page: "/api/logicApp/:id";
  };
  "routes\\api\\$entity\\$id.tsx": {
    id: "routes/api/$entity/$id";
    page: "/api/:entity/:id";
  };
  "routes\\api\\ai\\generate.tsx": {
    id: "routes/api/ai/generate";
    page: "/api/ai/generate";
  };
  "routes\\api\\s3\\download.tsx": {
    id: "routes/api/s3/download";
    page: "/api/s3/download";
  };
  "routes\\forgot-password.tsx": {
    id: "routes/forgot-password";
    page: "/forgot-password";
  };
  "routes\\api\\auth\\login.ts": {
    id: "routes/api/auth/login";
    page: "/api/auth/login";
  };
  "routes\\api\\widget.$id.ts": {
    id: "routes/api/widget.$id";
    page: "/api/widget/:id";
  };
  "routes\\invitation.$id.tsx": {
    id: "routes/invitation.$id";
    page: "/invitation/:id";
  };
  "routes\\onboarding.$id.tsx": {
    id: "routes/onboarding.$id";
    page: "/onboarding/:id";
  };
  "routes\\privacy-policy.tsx": {
    id: "routes/privacy-policy";
    page: "/privacy-policy";
  };
  "routes\\__app\\settings.tsx": {
    id: "routes/__app/settings";
    page: "/settings";
  };
  "routes\\api\\ai\\upload.tsx": {
    id: "routes/api/ai/upload";
    page: "/api/ai/upload";
  };
  "routes\\surveys\\$slug.tsx": {
    id: "routes/surveys/$slug";
    page: "/surveys/:slug";
  };
  "routes\\surveys\\index.tsx": {
    id: "routes/surveys/index";
    page: "/surveys";
  };
  "routes\\docs\\($lang).tsx": {
    id: "routes/docs/($lang)";
    page: "/docs/:lang?";
  };
  "routes\\oauth\\google.tsx": {
    id: "routes/oauth/google";
    page: "/oauth/google";
  };
  "routes\\api\\auth\\me.ts": {
    id: "routes/api/auth/me";
    page: "/api/auth/me";
  };
  "routes\\app.$tenant.tsx": {
    id: "routes/app.$tenant";
    page: "/app/:tenant" | "/app/:tenant/:entity/relationships" | "/app/:tenant/:entity/all-in-one" | "/app/:tenant/:entity/:id/edit" | "/app/:tenant/:entity/export" | "/app/:tenant/:entity/import" | "/app/:tenant/:entity/:id" | "/app/:tenant/:entity/:id/share" | "/app/:tenant/:entity/:id/tags" | "/app/:tenant/:entity/new" | "/app/:tenant/:entity" | "/app/:tenant/workflow-engine" | "/app/:tenant/workflow-engine/workflows/:id/executions" | "/app/:tenant/workflow-engine/workflows/:id/run/manual" | "/app/:tenant/workflow-engine/workflows/:id/run/stream" | "/app/:tenant/workflow-engine/workflows/:id/run/api" | "/app/:tenant/workflow-engine/workflows/:id" | "/app/:tenant/workflow-engine/credentials" | "/app/:tenant/workflow-engine/credentials/new" | "/app/:tenant/workflow-engine/executions" | "/app/:tenant/workflow-engine/templates" | "/app/:tenant/workflow-engine/variables" | "/app/:tenant/workflow-engine/variables/:id" | "/app/:tenant/workflow-engine/variables/new" | "/app/:tenant/workflow-engine/workflows" | "/app/:tenant/workflow-engine/danger" | "/app/:tenant/portals/:portal" | "/app/:tenant/portals/:portal/pricing/edit/:product" | "/app/:tenant/portals/:portal/analytics/page-views" | "/app/:tenant/portals/:portal/analytics/visitors" | "/app/:tenant/portals/:portal/analytics/events" | "/app/:tenant/portals/:portal/pricing/stripe" | "/app/:tenant/portals/:portal/pricing/new" | "/app/:tenant/portals/:portal/analytics" | "/app/:tenant/portals/:portal/settings" | "/app/:tenant/portals/:portal/domains" | "/app/:tenant/portals/:portal/pricing" | "/app/:tenant/portals/:portal/danger" | "/app/:tenant/portals/:portal/pages" | "/app/:tenant/portals/:portal/pages/:name" | "/app/:tenant/portals/:portal/users" | "/app/:tenant/portals/:portal/users/:userId" | "/app/:tenant/portals/:portal/users/new" | "/app/:tenant/portals/new" | "/app/:tenant/portals" | "/app/:tenant/widgets" | "/app/:tenant/widgets/:id" | "/app/:tenant/email-marketing" | "/app/:tenant/email-marketing/campaigns" | "/app/:tenant/email-marketing/campaigns/:id" | "/app/:tenant/email-marketing/campaigns/new" | "/app/:tenant/email-marketing/activity" | "/app/:tenant/email-marketing/senders" | "/app/:tenant/email-marketing/senders/:id" | "/app/:tenant/email-marketing/senders/new" | "/app/:tenant/blog/:id" | "/app/:tenant/blog/new" | "/app/:tenant/rewardsClean" | "/app/:tenant/rewardsSeed" | "/app/:tenant/blog" | "/app/:tenant/dashboard" | "/app/:tenant/g/:group" | "/app/:tenant/g/:group/:entity/relationships" | "/app/:tenant/g/:group/:entity/import-xlsx" | "/app/:tenant/g/:group/:entity/all-in-one" | "/app/:tenant/g/:group/:entity/cleanData" | "/app/:tenant/g/:group/:entity/:id/edit" | "/app/:tenant/g/:group/:entity/export" | "/app/:tenant/g/:group/:entity/import" | "/app/:tenant/g/:group/:entity/:id" | "/app/:tenant/g/:group/:entity/:id/share" | "/app/:tenant/g/:group/:entity/:id/tags" | "/app/:tenant/g/:group/:entity/new" | "/app/:tenant/g/:group/:entity" | "/app/:tenant/g/:group/milestones" | "/app/:tenant/g/:group/milestones/:id" | "/app/:tenant/g/:group/rewards" | "/app/:tenant/g/:group/rewards/:id" | "/app/:tenant/g/:group/all" | "/app/:tenant/settings" | "/app/:tenant/settings/entities/:entity/templates" | "/app/:tenant/settings/entities/:entity/templates/:id" | "/app/:tenant/settings/entities/:entity/templates/new" | "/app/:tenant/settings/roles-and-permissions" | "/app/:tenant/settings/roles-and-permissions/permissions" | "/app/:tenant/settings/roles-and-permissions/roles" | "/app/:tenant/settings/roles-and-permissions/users" | "/app/:tenant/settings/entities/:entity" | "/app/:tenant/settings/entities/:entity/:id" | "/app/:tenant/settings/entities/:entity/new" | "/app/:tenant/settings/logs/events/:id" | "/app/:tenant/settings/entities" | "/app/:tenant/settings/subscription" | "/app/:tenant/settings/logs/events" | "/app/:tenant/settings/appearance" | "/app/:tenant/settings/logs" | "/app/:tenant/settings/api" | "/app/:tenant/settings/api/docs" | "/app/:tenant/settings/api/keys" | "/app/:tenant/settings/api/keys/:id" | "/app/:tenant/settings/api/keys/new" | "/app/:tenant/settings/api/logs" | "/app/:tenant/settings/account" | "/app/:tenant/settings/credits" | "/app/:tenant/settings/members" | "/app/:tenant/settings/members/groups/:id" | "/app/:tenant/settings/members/groups/new" | "/app/:tenant/settings/members/edit/:id" | "/app/:tenant/settings/members/new" | "/app/:tenant/settings/profile" | "/app/:tenant/settings/groups" | "/app/:tenant/settings/groups/:id" | "/app/:tenant/settings/groups/new" | "/app/:tenant/settings/debug" | "/app/:tenant/emails" | "/app/:tenant/emails/:id" | "/app/:tenant/404" | "/app/:tenant/crm" | "/app/:tenant/crm/:entity/relationships" | "/app/:tenant/crm/:entity/all-in-one" | "/app/:tenant/crm/:entity/:id/edit" | "/app/:tenant/crm/:entity/export" | "/app/:tenant/crm/:entity/import" | "/app/:tenant/crm/:entity/:id" | "/app/:tenant/crm/:entity/:id/share" | "/app/:tenant/crm/:entity/:id/tags" | "/app/:tenant/crm/:entity/new" | "/app/:tenant/crm/:entity";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.relationships.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.relationships";
    page: "/app/:tenant/:entity/relationships";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.all-in-one.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.all-in-one";
    page: "/app/:tenant/:entity/all-in-one";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.$id.edit.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.$id.edit";
    page: "/app/:tenant/:entity/:id/edit";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.export.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.export";
    page: "/app/:tenant/:entity/export";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.import.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.import";
    page: "/app/:tenant/:entity/import";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.$id.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.$id";
    page: "/app/:tenant/:entity/:id" | "/app/:tenant/:entity/:id/share" | "/app/:tenant/:entity/:id/tags";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.$id\\share.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.$id/share";
    page: "/app/:tenant/:entity/:id/share";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.$id\\tags.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.$id/tags";
    page: "/app/:tenant/:entity/:id/tags";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.new.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity.new";
    page: "/app/:tenant/:entity/new";
  };
  "routes\\app.$tenant\\$entity.__autogenerated\\__$entity.tsx": {
    id: "routes/app.$tenant/$entity.__autogenerated/__$entity";
    page: "/app/:tenant/:entity";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine";
    page: "/app/:tenant/workflow-engine" | "/app/:tenant/workflow-engine/workflows/:id/executions" | "/app/:tenant/workflow-engine/workflows/:id/run/manual" | "/app/:tenant/workflow-engine/workflows/:id/run/stream" | "/app/:tenant/workflow-engine/workflows/:id/run/api" | "/app/:tenant/workflow-engine/workflows/:id" | "/app/:tenant/workflow-engine/credentials" | "/app/:tenant/workflow-engine/credentials/new" | "/app/:tenant/workflow-engine/executions" | "/app/:tenant/workflow-engine/templates" | "/app/:tenant/workflow-engine/variables" | "/app/:tenant/workflow-engine/variables/:id" | "/app/:tenant/workflow-engine/variables/new" | "/app/:tenant/workflow-engine/workflows" | "/app/:tenant/workflow-engine/danger";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\__workflow\\workflows.$id.executions.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/__workflow/workflows.$id.executions";
    page: "/app/:tenant/workflow-engine/workflows/:id/executions";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\__workflow\\workflows.$id.run.manual.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/__workflow/workflows.$id.run.manual";
    page: "/app/:tenant/workflow-engine/workflows/:id/run/manual";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\__workflow\\workflows.$id.run.stream.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/__workflow/workflows.$id.run.stream";
    page: "/app/:tenant/workflow-engine/workflows/:id/run/stream";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\__workflow\\workflows.$id.run.api.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/__workflow/workflows.$id.run.api";
    page: "/app/:tenant/workflow-engine/workflows/:id/run/api";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\__workflow\\workflows.$id.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/__workflow/workflows.$id";
    page: "/app/:tenant/workflow-engine/workflows/:id";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\credentials.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/credentials";
    page: "/app/:tenant/workflow-engine/credentials" | "/app/:tenant/workflow-engine/credentials/new";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\credentials\\new.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/credentials/new";
    page: "/app/:tenant/workflow-engine/credentials/new";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\executions.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/executions";
    page: "/app/:tenant/workflow-engine/executions";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\templates.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/templates";
    page: "/app/:tenant/workflow-engine/templates";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\variables.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/variables";
    page: "/app/:tenant/workflow-engine/variables" | "/app/:tenant/workflow-engine/variables/:id" | "/app/:tenant/workflow-engine/variables/new";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\variables\\$id.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/variables/$id";
    page: "/app/:tenant/workflow-engine/variables/:id";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\variables\\new.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/variables/new";
    page: "/app/:tenant/workflow-engine/variables/new";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\workflows.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/workflows";
    page: "/app/:tenant/workflow-engine/workflows";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\danger.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/danger";
    page: "/app/:tenant/workflow-engine/danger";
  };
  "routes\\app.$tenant\\__workflows\\workflow-engine\\index.tsx": {
    id: "routes/app.$tenant/__workflows/workflow-engine/index";
    page: "/app/:tenant/workflow-engine";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal";
    page: "/app/:tenant/portals/:portal" | "/app/:tenant/portals/:portal/pricing/edit/:product" | "/app/:tenant/portals/:portal/analytics/page-views" | "/app/:tenant/portals/:portal/analytics/visitors" | "/app/:tenant/portals/:portal/analytics/events" | "/app/:tenant/portals/:portal/pricing/stripe" | "/app/:tenant/portals/:portal/pricing/new" | "/app/:tenant/portals/:portal/analytics" | "/app/:tenant/portals/:portal/settings" | "/app/:tenant/portals/:portal/domains" | "/app/:tenant/portals/:portal/pricing" | "/app/:tenant/portals/:portal/danger" | "/app/:tenant/portals/:portal/pages" | "/app/:tenant/portals/:portal/pages/:name" | "/app/:tenant/portals/:portal/users" | "/app/:tenant/portals/:portal/users/:userId" | "/app/:tenant/portals/:portal/users/new";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\pricing.edit.$product.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/pricing.edit.$product";
    page: "/app/:tenant/portals/:portal/pricing/edit/:product";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\analytics.page-views.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/analytics.page-views";
    page: "/app/:tenant/portals/:portal/analytics/page-views";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\analytics.visitors.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/analytics.visitors";
    page: "/app/:tenant/portals/:portal/analytics/visitors";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\analytics.events.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/analytics.events";
    page: "/app/:tenant/portals/:portal/analytics/events";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\pricing.stripe.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/pricing.stripe";
    page: "/app/:tenant/portals/:portal/pricing/stripe";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\pricing.new.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/pricing.new";
    page: "/app/:tenant/portals/:portal/pricing/new";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\analytics.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/analytics";
    page: "/app/:tenant/portals/:portal/analytics";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\settings.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/settings";
    page: "/app/:tenant/portals/:portal/settings";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\domains.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/domains";
    page: "/app/:tenant/portals/:portal/domains";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\pricing.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/pricing";
    page: "/app/:tenant/portals/:portal/pricing";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\danger.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/danger";
    page: "/app/:tenant/portals/:portal/danger";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\index.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/index";
    page: "/app/:tenant/portals/:portal";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\pages.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/pages";
    page: "/app/:tenant/portals/:portal/pages" | "/app/:tenant/portals/:portal/pages/:name";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\pages\\$name.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/pages/$name";
    page: "/app/:tenant/portals/:portal/pages/:name";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\users.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/users";
    page: "/app/:tenant/portals/:portal/users" | "/app/:tenant/portals/:portal/users/:userId" | "/app/:tenant/portals/:portal/users/new";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\users\\$userId.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/users/$userId";
    page: "/app/:tenant/portals/:portal/users/:userId";
  };
  "routes\\app.$tenant\\__portals\\portals.$portal\\users\\new.tsx": {
    id: "routes/app.$tenant/__portals/portals.$portal/users/new";
    page: "/app/:tenant/portals/:portal/users/new";
  };
  "routes\\app.$tenant\\__portals\\portals.new.tsx": {
    id: "routes/app.$tenant/__portals/portals.new";
    page: "/app/:tenant/portals/new";
  };
  "routes\\app.$tenant\\__portals\\portals.tsx": {
    id: "routes/app.$tenant/__portals/portals";
    page: "/app/:tenant/portals";
  };
  "routes\\app.$tenant\\__widgets\\widgets.tsx": {
    id: "routes/app.$tenant/__widgets/widgets";
    page: "/app/:tenant/widgets" | "/app/:tenant/widgets/:id";
  };
  "routes\\app.$tenant\\__widgets\\widgets\\index.tsx": {
    id: "routes/app.$tenant/__widgets/widgets/index";
    page: "/app/:tenant/widgets";
  };
  "routes\\app.$tenant\\__widgets\\widgets\\$id.tsx": {
    id: "routes/app.$tenant/__widgets/widgets/$id";
    page: "/app/:tenant/widgets/:id";
  };
  "routes\\app.$tenant\\email-marketing.tsx": {
    id: "routes/app.$tenant/email-marketing";
    page: "/app/:tenant/email-marketing" | "/app/:tenant/email-marketing/campaigns" | "/app/:tenant/email-marketing/campaigns/:id" | "/app/:tenant/email-marketing/campaigns/new" | "/app/:tenant/email-marketing/activity" | "/app/:tenant/email-marketing/senders" | "/app/:tenant/email-marketing/senders/:id" | "/app/:tenant/email-marketing/senders/new";
  };
  "routes\\app.$tenant\\email-marketing\\campaigns\\index.tsx": {
    id: "routes/app.$tenant/email-marketing/campaigns/index";
    page: "/app/:tenant/email-marketing/campaigns";
  };
  "routes\\app.$tenant\\email-marketing\\campaigns\\$id.tsx": {
    id: "routes/app.$tenant/email-marketing/campaigns/$id";
    page: "/app/:tenant/email-marketing/campaigns/:id";
  };
  "routes\\app.$tenant\\email-marketing\\campaigns\\new.tsx": {
    id: "routes/app.$tenant/email-marketing/campaigns/new";
    page: "/app/:tenant/email-marketing/campaigns/new";
  };
  "routes\\app.$tenant\\email-marketing\\activity.tsx": {
    id: "routes/app.$tenant/email-marketing/activity";
    page: "/app/:tenant/email-marketing/activity";
  };
  "routes\\app.$tenant\\email-marketing\\senders.tsx": {
    id: "routes/app.$tenant/email-marketing/senders";
    page: "/app/:tenant/email-marketing/senders" | "/app/:tenant/email-marketing/senders/:id" | "/app/:tenant/email-marketing/senders/new";
  };
  "routes\\app.$tenant\\email-marketing\\senders\\$id.tsx": {
    id: "routes/app.$tenant/email-marketing/senders/$id";
    page: "/app/:tenant/email-marketing/senders/:id";
  };
  "routes\\app.$tenant\\email-marketing\\senders\\new.tsx": {
    id: "routes/app.$tenant/email-marketing/senders/new";
    page: "/app/:tenant/email-marketing/senders/new";
  };
  "routes\\app.$tenant\\email-marketing\\index.tsx": {
    id: "routes/app.$tenant/email-marketing/index";
    page: "/app/:tenant/email-marketing";
  };
  "routes\\app.$tenant\\__blog\\blog.$id.tsx": {
    id: "routes/app.$tenant/__blog/blog.$id";
    page: "/app/:tenant/blog/:id";
  };
  "routes\\app.$tenant\\__blog\\blog.new.tsx": {
    id: "routes/app.$tenant/__blog/blog.new";
    page: "/app/:tenant/blog/new";
  };
  "routes\\app.$tenant\\rewardsClean.tsx": {
    id: "routes/app.$tenant/rewardsClean";
    page: "/app/:tenant/rewardsClean";
  };
  "routes\\app.$tenant\\rewardsSeed.tsx": {
    id: "routes/app.$tenant/rewardsSeed";
    page: "/app/:tenant/rewardsSeed";
  };
  "routes\\app.$tenant\\__blog\\blog.tsx": {
    id: "routes/app.$tenant/__blog/blog";
    page: "/app/:tenant/blog";
  };
  "routes\\app.$tenant\\dashboard.tsx": {
    id: "routes/app.$tenant/dashboard";
    page: "/app/:tenant/dashboard";
  };
  "routes\\app.$tenant\\g.$group.tsx": {
    id: "routes/app.$tenant/g.$group";
    page: "/app/:tenant/g/:group" | "/app/:tenant/g/:group/:entity/relationships" | "/app/:tenant/g/:group/:entity/import-xlsx" | "/app/:tenant/g/:group/:entity/all-in-one" | "/app/:tenant/g/:group/:entity/cleanData" | "/app/:tenant/g/:group/:entity/:id/edit" | "/app/:tenant/g/:group/:entity/export" | "/app/:tenant/g/:group/:entity/import" | "/app/:tenant/g/:group/:entity/:id" | "/app/:tenant/g/:group/:entity/:id/share" | "/app/:tenant/g/:group/:entity/:id/tags" | "/app/:tenant/g/:group/:entity/new" | "/app/:tenant/g/:group/:entity" | "/app/:tenant/g/:group/milestones" | "/app/:tenant/g/:group/milestones/:id" | "/app/:tenant/g/:group/rewards" | "/app/:tenant/g/:group/rewards/:id" | "/app/:tenant/g/:group/all";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.relationships.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.relationships";
    page: "/app/:tenant/g/:group/:entity/relationships";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.import-xlsx.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.import-xlsx";
    page: "/app/:tenant/g/:group/:entity/import-xlsx";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.all-in-one.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.all-in-one";
    page: "/app/:tenant/g/:group/:entity/all-in-one";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.cleanData.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.cleanData";
    page: "/app/:tenant/g/:group/:entity/cleanData";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.$id.edit.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.$id.edit";
    page: "/app/:tenant/g/:group/:entity/:id/edit";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.export.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.export";
    page: "/app/:tenant/g/:group/:entity/export";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.import.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.import";
    page: "/app/:tenant/g/:group/:entity/import";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.$id.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.$id";
    page: "/app/:tenant/g/:group/:entity/:id" | "/app/:tenant/g/:group/:entity/:id/share" | "/app/:tenant/g/:group/:entity/:id/tags";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.$id\\share.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.$id/share";
    page: "/app/:tenant/g/:group/:entity/:id/share";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.$id\\tags.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.$id/tags";
    page: "/app/:tenant/g/:group/:entity/:id/tags";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.new.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity.new";
    page: "/app/:tenant/g/:group/:entity/new";
  };
  "routes\\app.$tenant\\g.$group\\$entity.__autogenerated\\__$entity.tsx": {
    id: "routes/app.$tenant/g.$group/$entity.__autogenerated/__$entity";
    page: "/app/:tenant/g/:group/:entity";
  };
  "routes\\app.$tenant\\g.$group\\milestones\\index.tsx": {
    id: "routes/app.$tenant/g.$group/milestones/index";
    page: "/app/:tenant/g/:group/milestones";
  };
  "routes\\app.$tenant\\g.$group\\milestones\\$id.tsx": {
    id: "routes/app.$tenant/g.$group/milestones/$id";
    page: "/app/:tenant/g/:group/milestones/:id";
  };
  "routes\\app.$tenant\\g.$group\\rewards\\index.tsx": {
    id: "routes/app.$tenant/g.$group/rewards/index";
    page: "/app/:tenant/g/:group/rewards";
  };
  "routes\\app.$tenant\\g.$group\\rewards\\$id.tsx": {
    id: "routes/app.$tenant/g.$group/rewards/$id";
    page: "/app/:tenant/g/:group/rewards/:id";
  };
  "routes\\app.$tenant\\g.$group\\index.tsx": {
    id: "routes/app.$tenant/g.$group/index";
    page: "/app/:tenant/g/:group";
  };
  "routes\\app.$tenant\\g.$group\\all.tsx": {
    id: "routes/app.$tenant/g.$group/all";
    page: "/app/:tenant/g/:group/all";
  };
  "routes\\app.$tenant\\settings.tsx": {
    id: "routes/app.$tenant/settings";
    page: "/app/:tenant/settings" | "/app/:tenant/settings/entities/:entity/templates" | "/app/:tenant/settings/entities/:entity/templates/:id" | "/app/:tenant/settings/entities/:entity/templates/new" | "/app/:tenant/settings/roles-and-permissions" | "/app/:tenant/settings/roles-and-permissions/permissions" | "/app/:tenant/settings/roles-and-permissions/roles" | "/app/:tenant/settings/roles-and-permissions/users" | "/app/:tenant/settings/entities/:entity" | "/app/:tenant/settings/entities/:entity/:id" | "/app/:tenant/settings/entities/:entity/new" | "/app/:tenant/settings/logs/events/:id" | "/app/:tenant/settings/entities" | "/app/:tenant/settings/subscription" | "/app/:tenant/settings/logs/events" | "/app/:tenant/settings/appearance" | "/app/:tenant/settings/logs" | "/app/:tenant/settings/api" | "/app/:tenant/settings/api/docs" | "/app/:tenant/settings/api/keys" | "/app/:tenant/settings/api/keys/:id" | "/app/:tenant/settings/api/keys/new" | "/app/:tenant/settings/api/logs" | "/app/:tenant/settings/account" | "/app/:tenant/settings/credits" | "/app/:tenant/settings/members" | "/app/:tenant/settings/members/groups/:id" | "/app/:tenant/settings/members/groups/new" | "/app/:tenant/settings/members/edit/:id" | "/app/:tenant/settings/members/new" | "/app/:tenant/settings/profile" | "/app/:tenant/settings/groups" | "/app/:tenant/settings/groups/:id" | "/app/:tenant/settings/groups/new" | "/app/:tenant/settings/debug";
  };
  "routes\\app.$tenant\\settings\\entities\\$entity.templates.tsx": {
    id: "routes/app.$tenant/settings/entities/$entity.templates";
    page: "/app/:tenant/settings/entities/:entity/templates" | "/app/:tenant/settings/entities/:entity/templates/:id" | "/app/:tenant/settings/entities/:entity/templates/new";
  };
  "routes\\app.$tenant\\settings\\entities\\$entity.templates\\$id.tsx": {
    id: "routes/app.$tenant/settings/entities/$entity.templates/$id";
    page: "/app/:tenant/settings/entities/:entity/templates/:id";
  };
  "routes\\app.$tenant\\settings\\entities\\$entity.templates\\new.tsx": {
    id: "routes/app.$tenant/settings/entities/$entity.templates/new";
    page: "/app/:tenant/settings/entities/:entity/templates/new";
  };
  "routes\\app.$tenant\\settings\\roles-and-permissions.tsx": {
    id: "routes/app.$tenant/settings/roles-and-permissions";
    page: "/app/:tenant/settings/roles-and-permissions" | "/app/:tenant/settings/roles-and-permissions/permissions" | "/app/:tenant/settings/roles-and-permissions/roles" | "/app/:tenant/settings/roles-and-permissions/users";
  };
  "routes\\app.$tenant\\settings\\roles-and-permissions\\permissions.tsx": {
    id: "routes/app.$tenant/settings/roles-and-permissions/permissions";
    page: "/app/:tenant/settings/roles-and-permissions/permissions";
  };
  "routes\\app.$tenant\\settings\\roles-and-permissions\\roles.tsx": {
    id: "routes/app.$tenant/settings/roles-and-permissions/roles";
    page: "/app/:tenant/settings/roles-and-permissions/roles";
  };
  "routes\\app.$tenant\\settings\\roles-and-permissions\\users.tsx": {
    id: "routes/app.$tenant/settings/roles-and-permissions/users";
    page: "/app/:tenant/settings/roles-and-permissions/users";
  };
  "routes\\app.$tenant\\settings\\entities\\$entity.tsx": {
    id: "routes/app.$tenant/settings/entities/$entity";
    page: "/app/:tenant/settings/entities/:entity" | "/app/:tenant/settings/entities/:entity/:id" | "/app/:tenant/settings/entities/:entity/new";
  };
  "routes\\app.$tenant\\settings\\entities\\$entity\\$id.tsx": {
    id: "routes/app.$tenant/settings/entities/$entity/$id";
    page: "/app/:tenant/settings/entities/:entity/:id";
  };
  "routes\\app.$tenant\\settings\\entities\\$entity\\new.tsx": {
    id: "routes/app.$tenant/settings/entities/$entity/new";
    page: "/app/:tenant/settings/entities/:entity/new";
  };
  "routes\\app.$tenant\\settings\\logs\\events.$id.tsx": {
    id: "routes/app.$tenant/settings/logs/events.$id";
    page: "/app/:tenant/settings/logs/events/:id";
  };
  "routes\\app.$tenant\\settings\\entities\\index.tsx": {
    id: "routes/app.$tenant/settings/entities/index";
    page: "/app/:tenant/settings/entities";
  };
  "routes\\app.$tenant\\settings\\subscription.tsx": {
    id: "routes/app.$tenant/settings/subscription";
    page: "/app/:tenant/settings/subscription";
  };
  "routes\\app.$tenant\\settings\\logs\\events.tsx": {
    id: "routes/app.$tenant/settings/logs/events";
    page: "/app/:tenant/settings/logs/events";
  };
  "routes\\app.$tenant\\settings\\appearance.tsx": {
    id: "routes/app.$tenant/settings/appearance";
    page: "/app/:tenant/settings/appearance";
  };
  "routes\\app.$tenant\\settings\\logs\\index.tsx": {
    id: "routes/app.$tenant/settings/logs/index";
    page: "/app/:tenant/settings/logs";
  };
  "routes\\app.$tenant\\settings\\api\\index.tsx": {
    id: "routes/app.$tenant/settings/api/index";
    page: "/app/:tenant/settings/api";
  };
  "routes\\app.$tenant\\settings\\api\\docs.tsx": {
    id: "routes/app.$tenant/settings/api/docs";
    page: "/app/:tenant/settings/api/docs";
  };
  "routes\\app.$tenant\\settings\\api\\keys.tsx": {
    id: "routes/app.$tenant/settings/api/keys";
    page: "/app/:tenant/settings/api/keys" | "/app/:tenant/settings/api/keys/:id" | "/app/:tenant/settings/api/keys/new";
  };
  "routes\\app.$tenant\\settings\\api\\keys\\$id.tsx": {
    id: "routes/app.$tenant/settings/api/keys/$id";
    page: "/app/:tenant/settings/api/keys/:id";
  };
  "routes\\app.$tenant\\settings\\api\\keys\\new.tsx": {
    id: "routes/app.$tenant/settings/api/keys/new";
    page: "/app/:tenant/settings/api/keys/new";
  };
  "routes\\app.$tenant\\settings\\api\\logs.tsx": {
    id: "routes/app.$tenant/settings/api/logs";
    page: "/app/:tenant/settings/api/logs";
  };
  "routes\\app.$tenant\\settings\\account.tsx": {
    id: "routes/app.$tenant/settings/account";
    page: "/app/:tenant/settings/account";
  };
  "routes\\app.$tenant\\settings\\credits.tsx": {
    id: "routes/app.$tenant/settings/credits";
    page: "/app/:tenant/settings/credits";
  };
  "routes\\app.$tenant\\settings\\members.tsx": {
    id: "routes/app.$tenant/settings/members";
    page: "/app/:tenant/settings/members" | "/app/:tenant/settings/members/groups/:id" | "/app/:tenant/settings/members/groups/new" | "/app/:tenant/settings/members/edit/:id" | "/app/:tenant/settings/members/new";
  };
  "routes\\app.$tenant\\settings\\members\\groups\\$id.tsx": {
    id: "routes/app.$tenant/settings/members/groups/$id";
    page: "/app/:tenant/settings/members/groups/:id";
  };
  "routes\\app.$tenant\\settings\\members\\groups\\new.tsx": {
    id: "routes/app.$tenant/settings/members/groups/new";
    page: "/app/:tenant/settings/members/groups/new";
  };
  "routes\\app.$tenant\\settings\\members\\edit.$id.tsx": {
    id: "routes/app.$tenant/settings/members/edit.$id";
    page: "/app/:tenant/settings/members/edit/:id";
  };
  "routes\\app.$tenant\\settings\\members\\new.tsx": {
    id: "routes/app.$tenant/settings/members/new";
    page: "/app/:tenant/settings/members/new";
  };
  "routes\\app.$tenant\\settings\\profile.tsx": {
    id: "routes/app.$tenant/settings/profile";
    page: "/app/:tenant/settings/profile";
  };
  "routes\\app.$tenant\\settings\\groups.tsx": {
    id: "routes/app.$tenant/settings/groups";
    page: "/app/:tenant/settings/groups" | "/app/:tenant/settings/groups/:id" | "/app/:tenant/settings/groups/new";
  };
  "routes\\app.$tenant\\settings\\groups\\$id.tsx": {
    id: "routes/app.$tenant/settings/groups/$id";
    page: "/app/:tenant/settings/groups/:id";
  };
  "routes\\app.$tenant\\settings\\groups\\new.tsx": {
    id: "routes/app.$tenant/settings/groups/new";
    page: "/app/:tenant/settings/groups/new";
  };
  "routes\\app.$tenant\\settings\\debug.tsx": {
    id: "routes/app.$tenant/settings/debug";
    page: "/app/:tenant/settings/debug";
  };
  "routes\\app.$tenant\\emails.tsx": {
    id: "routes/app.$tenant/emails";
    page: "/app/:tenant/emails" | "/app/:tenant/emails/:id";
  };
  "routes\\app.$tenant\\emails\\$id.tsx": {
    id: "routes/app.$tenant/emails/$id";
    page: "/app/:tenant/emails/:id";
  };
  "routes\\app.$tenant\\404.tsx": {
    id: "routes/app.$tenant/404";
    page: "/app/:tenant/404";
  };
  "routes\\app.$tenant\\crm.tsx": {
    id: "routes/app.$tenant/crm";
    page: "/app/:tenant/crm" | "/app/:tenant/crm/:entity/relationships" | "/app/:tenant/crm/:entity/all-in-one" | "/app/:tenant/crm/:entity/:id/edit" | "/app/:tenant/crm/:entity/export" | "/app/:tenant/crm/:entity/import" | "/app/:tenant/crm/:entity/:id" | "/app/:tenant/crm/:entity/:id/share" | "/app/:tenant/crm/:entity/:id/tags" | "/app/:tenant/crm/:entity/new" | "/app/:tenant/crm/:entity";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.relationships.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.relationships";
    page: "/app/:tenant/crm/:entity/relationships";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.all-in-one.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.all-in-one";
    page: "/app/:tenant/crm/:entity/all-in-one";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.$id.edit.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.$id.edit";
    page: "/app/:tenant/crm/:entity/:id/edit";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.export.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.export";
    page: "/app/:tenant/crm/:entity/export";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.import.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.import";
    page: "/app/:tenant/crm/:entity/import";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.$id.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.$id";
    page: "/app/:tenant/crm/:entity/:id" | "/app/:tenant/crm/:entity/:id/share" | "/app/:tenant/crm/:entity/:id/tags";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.$id\\share.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.$id/share";
    page: "/app/:tenant/crm/:entity/:id/share";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.$id\\tags.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.$id/tags";
    page: "/app/:tenant/crm/:entity/:id/tags";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.new.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity.new";
    page: "/app/:tenant/crm/:entity/new";
  };
  "routes\\app.$tenant\\crm\\$entity.__autogenerated\\__$entity.tsx": {
    id: "routes/app.$tenant/crm/$entity.__autogenerated/__$entity";
    page: "/app/:tenant/crm/:entity";
  };
  "routes\\app.$tenant\\crm\\index.tsx": {
    id: "routes/app.$tenant/crm/index";
    page: "/app/:tenant/crm";
  };
  "routes\\healthcheck.tsx": {
    id: "routes/healthcheck";
    page: "/healthcheck";
  };
  "routes\\new-account.tsx": {
    id: "routes/new-account";
    page: "/new-account";
  };
  "routes\\$page.$id1.tsx": {
    id: "routes/$page.$id1";
    page: "/:page/:id1";
  };
  "routes\\blog.$slug.tsx": {
    id: "routes/blog.$slug";
    page: "/blog/:slug";
  };
  "routes\\blog[.]rss.tsx": {
    id: "routes/blog[.]rss";
    page: "/blog.rss";
  };
  "routes\\newsletter.tsx": {
    id: "routes/newsletter";
    page: "/newsletter";
  };
  "routes\\verify.$id.tsx": {
    id: "routes/verify.$id";
    page: "/verify/:id";
  };
  "routes\\api\\usage.tsx": {
    id: "routes/api/usage";
    page: "/api/usage";
  };
  "routes\\changelog.tsx": {
    id: "routes/changelog";
    page: "/changelog";
  };
  "routes\\api\\docs.tsx": {
    id: "routes/api/docs";
    page: "/api/docs";
  };
  "routes\\register.tsx": {
    id: "routes/register";
    page: "/register";
  };
  "routes\\contact.tsx": {
    id: "routes/contact";
    page: "/contact";
  };
  "routes\\pricing.tsx": {
    id: "routes/pricing";
    page: "/pricing";
  };
  "routes\\swagger.ts": {
    id: "routes/swagger";
    page: "/swagger";
  };
  "routes\\logout.tsx": {
    id: "routes/logout";
    page: "/logout";
  };
  "routes\\$page.tsx": {
    id: "routes/$page";
    page: "/:page";
  };
  "routes\\admin.tsx": {
    id: "routes/admin";
    page: "/admin" | "/admin/workflow-engine" | "/admin/workflow-engine/workflows/:id/executions" | "/admin/workflow-engine/workflows/:id/run/manual" | "/admin/workflow-engine/workflows/:id/run/stream" | "/admin/workflow-engine/workflows/:id/run/api" | "/admin/workflow-engine/workflows/:id" | "/admin/workflow-engine/credentials" | "/admin/workflow-engine/credentials/new" | "/admin/workflow-engine/executions" | "/admin/workflow-engine/templates" | "/admin/workflow-engine/variables" | "/admin/workflow-engine/variables/:id" | "/admin/workflow-engine/variables/new" | "/admin/workflow-engine/workflows" | "/admin/workflow-engine/danger" | "/admin/setup/pricing/:id" | "/admin/step-form-wizard" | "/admin/step-form-wizard/step-form-wizards/:id" | "/admin/step-form-wizard/step-form-wizards/:id/sessions" | "/admin/step-form-wizard/step-form-wizards/:id/settings" | "/admin/step-form-wizard/step-form-wizards/:id/filters" | "/admin/step-form-wizard/step-form-wizards/:id/danger" | "/admin/step-form-wizard/step-form-wizards/:id/steps" | "/admin/step-form-wizard/step-form-wizards" | "/admin/step-form-wizard/sessions/:id" | "/admin/step-form-wizard/sessions" | "/admin/email-marketing" | "/admin/email-marketing/campaigns" | "/admin/email-marketing/campaigns/:id" | "/admin/email-marketing/campaigns/new" | "/admin/email-marketing/activity" | "/admin/email-marketing/senders" | "/admin/email-marketing/senders/:id" | "/admin/email-marketing/senders/new" | "/admin/blog/:id" | "/admin/blog/new" | "/admin/knowledge-base" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/edit" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/settings" | "/admin/knowledge-base/bases/:slug/categories/:lang" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/:section" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/new" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id" | "/admin/knowledge-base/bases/:slug/categories/:lang/new" | "/admin/knowledge-base/bases/:slug/articles/:lang" | "/admin/knowledge-base/bases/:slug/categories" | "/admin/knowledge-base/bases/:slug/articles" | "/admin/knowledge-base/bases/import" | "/admin/knowledge-base/articles" | "/admin/knowledge-base/danger" | "/admin/knowledge-base/bases" | "/admin/knowledge-base/bases/:id" | "/admin/knowledge-base/bases/new" | "/admin/feature-flags" | "/admin/feature-flags/settings" | "/admin/feature-flags/flags" | "/admin/feature-flags/flags/:id" | "/admin/feature-flags/flags/new" | "/admin/notifications" | "/admin/notifications/subscribers" | "/admin/notifications/channels" | "/admin/notifications/messages" | "/admin/audit-trails" | "/admin/entities/new" | "/admin/blog" | "/admin/affiliates" | "/admin/components" | "/admin/events/:id" | "/admin/navigation" | "/admin/onboarding" | "/admin/onboarding/onboardings/:id" | "/admin/onboarding/onboardings/:id/sessions" | "/admin/onboarding/onboardings/:id/settings" | "/admin/onboarding/onboardings/:id/filters" | "/admin/onboarding/onboardings/:id/danger" | "/admin/onboarding/onboardings/:id/steps" | "/admin/onboarding/sessions/:id" | "/admin/onboarding/onboardings" | "/admin/onboarding/sessions" | "/admin/playground" | "/admin/playground/repositories-and-models/row-repository" | "/admin/playground/repositories-and-models/row-model" | "/admin/playground/repositories-and-models" | "/admin/playground/supabase/storage/buckets/:id" | "/admin/playground/long-running-tasks" | "/admin/playground/supabase/storage/buckets" | "/admin/playground/enhanced-location-test" | "/admin/playground/supabase/storage" | "/admin/playground/crud/projects" | "/admin/playground/location-input-test" | "/admin/playground/ai/openai/chatgpt" | "/admin/playground/crud/projects/:id" | "/admin/playground/crud/projects/new" | "/admin/playground/handlebars" | "/admin/playground/monaco-editor" | "/admin/playground/novel-editor" | "/admin/playground/chat" | "/admin/playground/crud" | "/admin/analytics" | "/admin/analytics/page-views" | "/admin/analytics/overview" | "/admin/analytics/settings" | "/admin/analytics/visitors" | "/admin/analytics/events" | "/admin/dashboard" | "/admin/help-desk" | "/admin/help-desk/surveys/:id/submissions" | "/admin/help-desk/surveys/:id/edit" | "/admin/help-desk/inbound-emails" | "/admin/help-desk/inbound-emails/:id" | "/admin/help-desk/surveys" | "/admin/help-desk/surveys/:id" | "/admin/help-desk/surveys/new" | "/admin/help-desk/feedback" | "/admin/accounts" | "/admin/accounts/ip-addresses/logs" | "/admin/accounts/ip-addresses" | "/admin/accounts/roles-and-permissions" | "/admin/accounts/roles-and-permissions/account-users/:account" | "/admin/accounts/roles-and-permissions/account-users" | "/admin/accounts/roles-and-permissions/admin-users" | "/admin/accounts/roles-and-permissions/permissions" | "/admin/accounts/roles-and-permissions/permissions/:id" | "/admin/accounts/roles-and-permissions/permissions/new" | "/admin/accounts/roles-and-permissions/roles" | "/admin/accounts/roles-and-permissions/roles/:id" | "/admin/accounts/roles-and-permissions/roles/new" | "/admin/accounts/roles-and-permissions/seed" | "/admin/accounts/subscriptions-revenue" | "/admin/accounts/subscriptions" | "/admin/accounts/subscriptions/:id" | "/admin/accounts/blacklist" | "/admin/accounts/users" | "/admin/accounts/users/:user/roles" | "/admin/accounts/users/new" | "/admin/accounts/:id" | "/admin/entities" | "/admin/entities/code-generator/generate/:entity" | "/admin/entities/code-generator/files/:entity" | "/admin/entities/code-generator" | "/admin/entities/templates/manual" | "/admin/entities/:entity/no-code" | "/admin/entities/:entity/no-code/:entity/relationships" | "/admin/entities/:entity/no-code/:entity/all-in-one" | "/admin/entities/:entity/no-code/:entity/:id/edit" | "/admin/entities/:entity/no-code/:entity/export" | "/admin/entities/:entity/no-code/:entity/import" | "/admin/entities/:entity/no-code/:entity/:id" | "/admin/entities/:entity/no-code/:entity/:id/share" | "/admin/entities/:entity/no-code/:entity/:id/tags" | "/admin/entities/:entity/no-code/:entity/new" | "/admin/entities/:entity/no-code/:entity" | "/admin/entities/templates" | "/admin/entities/formulas/logs" | "/admin/entities/relationships" | "/admin/entities/fake-rows" | "/admin/entities/formulas" | "/admin/entities/formulas/:id" | "/admin/entities/formulas/new" | "/admin/entities/:entity" | "/admin/entities/:entity/relationships" | "/admin/entities/:entity/relationships/:id" | "/admin/entities/:entity/relationships/new" | "/admin/entities/:entity/properties" | "/admin/entities/:entity/properties/:id" | "/admin/entities/:entity/properties/new" | "/admin/entities/:entity/templates" | "/admin/entities/:entity/templates/:id" | "/admin/entities/:entity/templates/new" | "/admin/entities/:entity/webhooks" | "/admin/entities/:entity/webhooks/:id" | "/admin/entities/:entity/webhooks/new" | "/admin/entities/:entity/details" | "/admin/entities/:entity/danger" | "/admin/entities/:entity/routes" | "/admin/entities/:entity/views" | "/admin/entities/:entity/logs" | "/admin/entities/:entity/rows" | "/admin/entities/:entity/api" | "/admin/entities/no-code" | "/admin/entities/no-code/lists/tasks" | "/admin/entities/no-code/stats/count" | "/admin/entities/groups" | "/admin/entities/groups/:id" | "/admin/entities/groups/new" | "/admin/entities/views" | "/admin/entities/views/new/:entity" | "/admin/entities/views/:id" | "/admin/entities/code" | "/admin/entities/logs" | "/admin/entities/rows" | "/admin/entities/api" | "/admin/settings" | "/admin/settings/internationalization" | "/admin/settings/transactional-emails" | "/admin/settings/pricing/edit/:id" | "/admin/settings/pricing/features" | "/admin/settings/accounts/types" | "/admin/settings/accounts/types/:id" | "/admin/settings/accounts/types/new" | "/admin/settings/authentication" | "/admin/settings/pricing/new" | "/admin/settings/analytics" | "/admin/settings/accounts" | "/admin/settings/cookies" | "/admin/settings/general" | "/admin/settings/pricing" | "/admin/settings/profile" | "/admin/settings/danger" | "/admin/settings/cache" | "/admin/settings/seo" | "/admin/supabase" | "/admin/metrics" | "/admin/metrics/settings" | "/admin/metrics/summary" | "/admin/metrics/logs" | "/admin/portals" | "/admin/prompts" | "/admin/prompts/executions/:id/results" | "/admin/prompts/executions/:id" | "/admin/prompts/builder/:id" | "/admin/prompts/builder/:id/templates" | "/admin/prompts/builder/:id/variables" | "/admin/prompts/builder/:id/variables/:variable" | "/admin/prompts/builder/:id/variables/new" | "/admin/prompts/builder/:id/outputs" | "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping" | "/admin/prompts/builder/:id/outputs/:output/mappings/new" | "/admin/prompts/builder/:id/outputs/:output" | "/admin/prompts/builder/:id/outputs/new" | "/admin/prompts/results/:id" | "/admin/prompts/builder" | "/admin/prompts/builder/new" | "/admin/prompts/groups" | "/admin/prompts/groups/:id" | "/admin/prompts/groups/new" | "/admin/events" | "/admin/pages" | "/admin/pages/edit/:id" | "/admin/pages/edit/:id/settings" | "/admin/pages/edit/:id/blocks" | "/admin/pages/edit/:id/seo" | "/admin/pages/seo" | "/admin/pages/ab" | "/admin/setup" | "/admin/404" | "/admin/api" | "/admin/api/credits" | "/admin/api/docs" | "/admin/api/keys" | "/admin/api/keys/:id" | "/admin/api/keys/new" | "/admin/api/logs" | "/admin/crm" | "/admin/crm/:entity/relationships" | "/admin/crm/:entity/all-in-one" | "/admin/crm/:entity/:id/edit" | "/admin/crm/:entity/export" | "/admin/crm/:entity/import" | "/admin/crm/:entity/:id" | "/admin/crm/:entity/:id/share" | "/admin/crm/:entity/:id/tags" | "/admin/crm/:entity/new" | "/admin/crm/:entity" | "/admin/crm/sync";
  };
  "routes\\admin\\__workflows\\workflow-engine.tsx": {
    id: "routes/admin/__workflows/workflow-engine";
    page: "/admin/workflow-engine" | "/admin/workflow-engine/workflows/:id/executions" | "/admin/workflow-engine/workflows/:id/run/manual" | "/admin/workflow-engine/workflows/:id/run/stream" | "/admin/workflow-engine/workflows/:id/run/api" | "/admin/workflow-engine/workflows/:id" | "/admin/workflow-engine/credentials" | "/admin/workflow-engine/credentials/new" | "/admin/workflow-engine/executions" | "/admin/workflow-engine/templates" | "/admin/workflow-engine/variables" | "/admin/workflow-engine/variables/:id" | "/admin/workflow-engine/variables/new" | "/admin/workflow-engine/workflows" | "/admin/workflow-engine/danger";
  };
  "routes\\admin\\__workflows\\workflow-engine\\__workflow\\workflows.$id.executions.tsx": {
    id: "routes/admin/__workflows/workflow-engine/__workflow/workflows.$id.executions";
    page: "/admin/workflow-engine/workflows/:id/executions";
  };
  "routes\\admin\\__workflows\\workflow-engine\\__workflow\\workflows.$id.run.manual.tsx": {
    id: "routes/admin/__workflows/workflow-engine/__workflow/workflows.$id.run.manual";
    page: "/admin/workflow-engine/workflows/:id/run/manual";
  };
  "routes\\admin\\__workflows\\workflow-engine\\__workflow\\workflows.$id.run.stream.tsx": {
    id: "routes/admin/__workflows/workflow-engine/__workflow/workflows.$id.run.stream";
    page: "/admin/workflow-engine/workflows/:id/run/stream";
  };
  "routes\\admin\\__workflows\\workflow-engine\\__workflow\\workflows.$id.run.api.tsx": {
    id: "routes/admin/__workflows/workflow-engine/__workflow/workflows.$id.run.api";
    page: "/admin/workflow-engine/workflows/:id/run/api";
  };
  "routes\\admin\\__workflows\\workflow-engine\\__workflow\\workflows.$id.tsx": {
    id: "routes/admin/__workflows/workflow-engine/__workflow/workflows.$id";
    page: "/admin/workflow-engine/workflows/:id";
  };
  "routes\\admin\\__workflows\\workflow-engine\\credentials.tsx": {
    id: "routes/admin/__workflows/workflow-engine/credentials";
    page: "/admin/workflow-engine/credentials" | "/admin/workflow-engine/credentials/new";
  };
  "routes\\admin\\__workflows\\workflow-engine\\credentials\\new.tsx": {
    id: "routes/admin/__workflows/workflow-engine/credentials/new";
    page: "/admin/workflow-engine/credentials/new";
  };
  "routes\\admin\\__workflows\\workflow-engine\\executions.tsx": {
    id: "routes/admin/__workflows/workflow-engine/executions";
    page: "/admin/workflow-engine/executions";
  };
  "routes\\admin\\__workflows\\workflow-engine\\templates.tsx": {
    id: "routes/admin/__workflows/workflow-engine/templates";
    page: "/admin/workflow-engine/templates";
  };
  "routes\\admin\\__workflows\\workflow-engine\\variables.tsx": {
    id: "routes/admin/__workflows/workflow-engine/variables";
    page: "/admin/workflow-engine/variables" | "/admin/workflow-engine/variables/:id" | "/admin/workflow-engine/variables/new";
  };
  "routes\\admin\\__workflows\\workflow-engine\\variables\\$id.tsx": {
    id: "routes/admin/__workflows/workflow-engine/variables/$id";
    page: "/admin/workflow-engine/variables/:id";
  };
  "routes\\admin\\__workflows\\workflow-engine\\variables\\new.tsx": {
    id: "routes/admin/__workflows/workflow-engine/variables/new";
    page: "/admin/workflow-engine/variables/new";
  };
  "routes\\admin\\__workflows\\workflow-engine\\workflows.tsx": {
    id: "routes/admin/__workflows/workflow-engine/workflows";
    page: "/admin/workflow-engine/workflows";
  };
  "routes\\admin\\__workflows\\workflow-engine\\danger.tsx": {
    id: "routes/admin/__workflows/workflow-engine/danger";
    page: "/admin/workflow-engine/danger";
  };
  "routes\\admin\\__workflows\\workflow-engine\\index.tsx": {
    id: "routes/admin/__workflows/workflow-engine/index";
    page: "/admin/workflow-engine";
  };
  "routes\\admin\\setup.pricing\\$id.tsx": {
    id: "routes/admin/setup.pricing/$id";
    page: "/admin/setup/pricing/:id";
  };
  "routes\\admin\\step-form-wizard.tsx": {
    id: "routes/admin/step-form-wizard";
    page: "/admin/step-form-wizard" | "/admin/step-form-wizard/step-form-wizards/:id" | "/admin/step-form-wizard/step-form-wizards/:id/sessions" | "/admin/step-form-wizard/step-form-wizards/:id/settings" | "/admin/step-form-wizard/step-form-wizards/:id/filters" | "/admin/step-form-wizard/step-form-wizards/:id/danger" | "/admin/step-form-wizard/step-form-wizards/:id/steps" | "/admin/step-form-wizard/step-form-wizards" | "/admin/step-form-wizard/sessions/:id" | "/admin/step-form-wizard/sessions";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id";
    page: "/admin/step-form-wizard/step-form-wizards/:id" | "/admin/step-form-wizard/step-form-wizards/:id/sessions" | "/admin/step-form-wizard/step-form-wizards/:id/settings" | "/admin/step-form-wizard/step-form-wizards/:id/filters" | "/admin/step-form-wizard/step-form-wizards/:id/danger" | "/admin/step-form-wizard/step-form-wizards/:id/steps";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id\\sessions.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id/sessions";
    page: "/admin/step-form-wizard/step-form-wizards/:id/sessions";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id\\settings.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id/settings";
    page: "/admin/step-form-wizard/step-form-wizards/:id/settings";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id\\filters.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id/filters";
    page: "/admin/step-form-wizard/step-form-wizards/:id/filters";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id\\danger.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id/danger";
    page: "/admin/step-form-wizard/step-form-wizards/:id/danger";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id\\index.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id/index";
    page: "/admin/step-form-wizard/step-form-wizards/:id";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.$id\\steps.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards.$id/steps";
    page: "/admin/step-form-wizard/step-form-wizards/:id/steps";
  };
  "routes\\admin\\step-form-wizard\\step-form-wizards.tsx": {
    id: "routes/admin/step-form-wizard/step-form-wizards";
    page: "/admin/step-form-wizard/step-form-wizards";
  };
  "routes\\admin\\step-form-wizard\\sessions.$id.tsx": {
    id: "routes/admin/step-form-wizard/sessions.$id";
    page: "/admin/step-form-wizard/sessions/:id";
  };
  "routes\\admin\\step-form-wizard\\sessions.tsx": {
    id: "routes/admin/step-form-wizard/sessions";
    page: "/admin/step-form-wizard/sessions";
  };
  "routes\\admin\\step-form-wizard\\index.tsx": {
    id: "routes/admin/step-form-wizard/index";
    page: "/admin/step-form-wizard";
  };
  "routes\\admin\\email-marketing.tsx": {
    id: "routes/admin/email-marketing";
    page: "/admin/email-marketing" | "/admin/email-marketing/campaigns" | "/admin/email-marketing/campaigns/:id" | "/admin/email-marketing/campaigns/new" | "/admin/email-marketing/activity" | "/admin/email-marketing/senders" | "/admin/email-marketing/senders/:id" | "/admin/email-marketing/senders/new";
  };
  "routes\\admin\\email-marketing\\campaigns\\index.tsx": {
    id: "routes/admin/email-marketing/campaigns/index";
    page: "/admin/email-marketing/campaigns";
  };
  "routes\\admin\\email-marketing\\campaigns\\$id.tsx": {
    id: "routes/admin/email-marketing/campaigns/$id";
    page: "/admin/email-marketing/campaigns/:id";
  };
  "routes\\admin\\email-marketing\\campaigns\\new.tsx": {
    id: "routes/admin/email-marketing/campaigns/new";
    page: "/admin/email-marketing/campaigns/new";
  };
  "routes\\admin\\email-marketing\\activity.tsx": {
    id: "routes/admin/email-marketing/activity";
    page: "/admin/email-marketing/activity";
  };
  "routes\\admin\\email-marketing\\senders.tsx": {
    id: "routes/admin/email-marketing/senders";
    page: "/admin/email-marketing/senders" | "/admin/email-marketing/senders/:id" | "/admin/email-marketing/senders/new";
  };
  "routes\\admin\\email-marketing\\senders\\$id.tsx": {
    id: "routes/admin/email-marketing/senders/$id";
    page: "/admin/email-marketing/senders/:id";
  };
  "routes\\admin\\email-marketing\\senders\\new.tsx": {
    id: "routes/admin/email-marketing/senders/new";
    page: "/admin/email-marketing/senders/new";
  };
  "routes\\admin\\email-marketing\\index.tsx": {
    id: "routes/admin/email-marketing/index";
    page: "/admin/email-marketing";
  };
  "routes\\admin\\__blog\\blog.$id.tsx": {
    id: "routes/admin/__blog/blog.$id";
    page: "/admin/blog/:id";
  };
  "routes\\admin\\__blog\\blog.new.tsx": {
    id: "routes/admin/__blog/blog.new";
    page: "/admin/blog/new";
  };
  "routes\\admin\\knowledge-base.tsx": {
    id: "routes/admin/knowledge-base";
    page: "/admin/knowledge-base" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/edit" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/settings" | "/admin/knowledge-base/bases/:slug/categories/:lang" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/:section" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/new" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id" | "/admin/knowledge-base/bases/:slug/categories/:lang/new" | "/admin/knowledge-base/bases/:slug/articles/:lang" | "/admin/knowledge-base/bases/:slug/categories" | "/admin/knowledge-base/bases/:slug/articles" | "/admin/knowledge-base/bases/import" | "/admin/knowledge-base/articles" | "/admin/knowledge-base/danger" | "/admin/knowledge-base/bases" | "/admin/knowledge-base/bases/:id" | "/admin/knowledge-base/bases/new";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\articles.$lang.$id.edit.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/articles.$lang.$id.edit";
    page: "/admin/knowledge-base/bases/:slug/articles/:lang/:id/edit";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\articles.$lang.$id.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/articles.$lang.$id";
    page: "/admin/knowledge-base/bases/:slug/articles/:lang/:id" | "/admin/knowledge-base/bases/:slug/articles/:lang/:id/settings";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\articles.$lang.$id\\settings.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/articles.$lang.$id/settings";
    page: "/admin/knowledge-base/bases/:slug/articles/:lang/:id/settings";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\categories.$lang.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/categories.$lang";
    page: "/admin/knowledge-base/bases/:slug/categories/:lang" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/:section" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/new" | "/admin/knowledge-base/bases/:slug/categories/:lang/:id" | "/admin/knowledge-base/bases/:slug/categories/:lang/new";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\categories.$lang\\$id.sections.$section.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/categories.$lang/$id.sections.$section";
    page: "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/:section";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\categories.$lang\\$id.sections.new.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/categories.$lang/$id.sections.new";
    page: "/admin/knowledge-base/bases/:slug/categories/:lang/:id/sections/new";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\categories.$lang\\$id.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/categories.$lang/$id";
    page: "/admin/knowledge-base/bases/:slug/categories/:lang/:id";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\categories.$lang\\new.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/categories.$lang/new";
    page: "/admin/knowledge-base/bases/:slug/categories/:lang/new";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\articles.$lang.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/articles.$lang";
    page: "/admin/knowledge-base/bases/:slug/articles/:lang";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\categories.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/categories";
    page: "/admin/knowledge-base/bases/:slug/categories";
  };
  "routes\\admin\\knowledge-base\\bases.$slug\\articles.tsx": {
    id: "routes/admin/knowledge-base/bases.$slug/articles";
    page: "/admin/knowledge-base/bases/:slug/articles";
  };
  "routes\\admin\\knowledge-base\\bases.import.tsx": {
    id: "routes/admin/knowledge-base/bases.import";
    page: "/admin/knowledge-base/bases/import";
  };
  "routes\\admin\\knowledge-base\\articles.tsx": {
    id: "routes/admin/knowledge-base/articles";
    page: "/admin/knowledge-base/articles";
  };
  "routes\\admin\\knowledge-base\\danger.tsx": {
    id: "routes/admin/knowledge-base/danger";
    page: "/admin/knowledge-base/danger";
  };
  "routes\\admin\\knowledge-base\\bases.tsx": {
    id: "routes/admin/knowledge-base/bases";
    page: "/admin/knowledge-base/bases" | "/admin/knowledge-base/bases/:id" | "/admin/knowledge-base/bases/new";
  };
  "routes\\admin\\knowledge-base\\bases\\$id.tsx": {
    id: "routes/admin/knowledge-base/bases/$id";
    page: "/admin/knowledge-base/bases/:id";
  };
  "routes\\admin\\knowledge-base\\bases\\new.tsx": {
    id: "routes/admin/knowledge-base/bases/new";
    page: "/admin/knowledge-base/bases/new";
  };
  "routes\\admin\\knowledge-base\\index.tsx": {
    id: "routes/admin/knowledge-base/index";
    page: "/admin/knowledge-base";
  };
  "routes\\admin\\feature-flags.tsx": {
    id: "routes/admin/feature-flags";
    page: "/admin/feature-flags" | "/admin/feature-flags/settings" | "/admin/feature-flags/flags" | "/admin/feature-flags/flags/:id" | "/admin/feature-flags/flags/new";
  };
  "routes\\admin\\feature-flags\\settings.tsx": {
    id: "routes/admin/feature-flags/settings";
    page: "/admin/feature-flags/settings";
  };
  "routes\\admin\\feature-flags\\flags.tsx": {
    id: "routes/admin/feature-flags/flags";
    page: "/admin/feature-flags/flags" | "/admin/feature-flags/flags/:id" | "/admin/feature-flags/flags/new";
  };
  "routes\\admin\\feature-flags\\flags\\$id.tsx": {
    id: "routes/admin/feature-flags/flags/$id";
    page: "/admin/feature-flags/flags/:id";
  };
  "routes\\admin\\feature-flags\\flags\\new.tsx": {
    id: "routes/admin/feature-flags/flags/new";
    page: "/admin/feature-flags/flags/new";
  };
  "routes\\admin\\feature-flags\\index.tsx": {
    id: "routes/admin/feature-flags/index";
    page: "/admin/feature-flags";
  };
  "routes\\admin\\notifications.tsx": {
    id: "routes/admin/notifications";
    page: "/admin/notifications" | "/admin/notifications/subscribers" | "/admin/notifications/channels" | "/admin/notifications/messages";
  };
  "routes\\admin\\notifications\\subscribers.tsx": {
    id: "routes/admin/notifications/subscribers";
    page: "/admin/notifications/subscribers";
  };
  "routes\\admin\\notifications\\channels.tsx": {
    id: "routes/admin/notifications/channels";
    page: "/admin/notifications/channels";
  };
  "routes\\admin\\notifications\\messages.tsx": {
    id: "routes/admin/notifications/messages";
    page: "/admin/notifications/messages";
  };
  "routes\\admin\\notifications\\index.tsx": {
    id: "routes/admin/notifications/index";
    page: "/admin/notifications";
  };
  "routes\\admin\\audit-trails.tsx": {
    id: "routes/admin/audit-trails";
    page: "/admin/audit-trails";
  };
  "routes\\admin\\entities.new.tsx": {
    id: "routes/admin/entities.new";
    page: "/admin/entities/new";
  };
  "routes\\admin\\__blog\\blog.tsx": {
    id: "routes/admin/__blog/blog";
    page: "/admin/blog";
  };
  "routes\\admin\\affiliates.tsx": {
    id: "routes/admin/affiliates";
    page: "/admin/affiliates";
  };
  "routes\\admin\\affiliates\\index.tsx": {
    id: "routes/admin/affiliates/index";
    page: "/admin/affiliates";
  };
  "routes\\admin\\components.tsx": {
    id: "routes/admin/components";
    page: "/admin/components";
  };
  "routes\\admin\\events.$id.tsx": {
    id: "routes/admin/events.$id";
    page: "/admin/events/:id";
  };
  "routes\\admin\\navigation.tsx": {
    id: "routes/admin/navigation";
    page: "/admin/navigation";
  };
  "routes\\admin\\onboarding.tsx": {
    id: "routes/admin/onboarding";
    page: "/admin/onboarding" | "/admin/onboarding/onboardings/:id" | "/admin/onboarding/onboardings/:id/sessions" | "/admin/onboarding/onboardings/:id/settings" | "/admin/onboarding/onboardings/:id/filters" | "/admin/onboarding/onboardings/:id/danger" | "/admin/onboarding/onboardings/:id/steps" | "/admin/onboarding/sessions/:id" | "/admin/onboarding/onboardings" | "/admin/onboarding/sessions";
  };
  "routes\\admin\\onboarding\\onboardings.$id.tsx": {
    id: "routes/admin/onboarding/onboardings.$id";
    page: "/admin/onboarding/onboardings/:id" | "/admin/onboarding/onboardings/:id/sessions" | "/admin/onboarding/onboardings/:id/settings" | "/admin/onboarding/onboardings/:id/filters" | "/admin/onboarding/onboardings/:id/danger" | "/admin/onboarding/onboardings/:id/steps";
  };
  "routes\\admin\\onboarding\\onboardings.$id\\sessions.tsx": {
    id: "routes/admin/onboarding/onboardings.$id/sessions";
    page: "/admin/onboarding/onboardings/:id/sessions";
  };
  "routes\\admin\\onboarding\\onboardings.$id\\settings.tsx": {
    id: "routes/admin/onboarding/onboardings.$id/settings";
    page: "/admin/onboarding/onboardings/:id/settings";
  };
  "routes\\admin\\onboarding\\onboardings.$id\\filters.tsx": {
    id: "routes/admin/onboarding/onboardings.$id/filters";
    page: "/admin/onboarding/onboardings/:id/filters";
  };
  "routes\\admin\\onboarding\\onboardings.$id\\danger.tsx": {
    id: "routes/admin/onboarding/onboardings.$id/danger";
    page: "/admin/onboarding/onboardings/:id/danger";
  };
  "routes\\admin\\onboarding\\onboardings.$id\\index.tsx": {
    id: "routes/admin/onboarding/onboardings.$id/index";
    page: "/admin/onboarding/onboardings/:id";
  };
  "routes\\admin\\onboarding\\onboardings.$id\\steps.tsx": {
    id: "routes/admin/onboarding/onboardings.$id/steps";
    page: "/admin/onboarding/onboardings/:id/steps";
  };
  "routes\\admin\\onboarding\\sessions.$id.tsx": {
    id: "routes/admin/onboarding/sessions.$id";
    page: "/admin/onboarding/sessions/:id";
  };
  "routes\\admin\\onboarding\\onboardings.tsx": {
    id: "routes/admin/onboarding/onboardings";
    page: "/admin/onboarding/onboardings";
  };
  "routes\\admin\\onboarding\\sessions.tsx": {
    id: "routes/admin/onboarding/sessions";
    page: "/admin/onboarding/sessions";
  };
  "routes\\admin\\onboarding\\index.tsx": {
    id: "routes/admin/onboarding/index";
    page: "/admin/onboarding";
  };
  "routes\\admin\\playground.tsx": {
    id: "routes/admin/playground";
    page: "/admin/playground" | "/admin/playground/repositories-and-models/row-repository" | "/admin/playground/repositories-and-models/row-model" | "/admin/playground/repositories-and-models" | "/admin/playground/supabase/storage/buckets/:id" | "/admin/playground/long-running-tasks" | "/admin/playground/supabase/storage/buckets" | "/admin/playground/enhanced-location-test" | "/admin/playground/supabase/storage" | "/admin/playground/crud/projects" | "/admin/playground/location-input-test" | "/admin/playground/ai/openai/chatgpt" | "/admin/playground/crud/projects/:id" | "/admin/playground/crud/projects/new" | "/admin/playground/handlebars" | "/admin/playground/monaco-editor" | "/admin/playground/novel-editor" | "/admin/playground/chat" | "/admin/playground/crud";
  };
  "routes\\admin\\playground\\repositories-and-models\\row-repository.tsx": {
    id: "routes/admin/playground/repositories-and-models/row-repository";
    page: "/admin/playground/repositories-and-models/row-repository";
  };
  "routes\\admin\\playground\\repositories-and-models\\row-model.tsx": {
    id: "routes/admin/playground/repositories-and-models/row-model";
    page: "/admin/playground/repositories-and-models/row-model";
  };
  "routes\\admin\\playground\\repositories-and-models\\index.tsx": {
    id: "routes/admin/playground/repositories-and-models/index";
    page: "/admin/playground/repositories-and-models";
  };
  "routes\\admin\\playground\\supabase\\storage\\buckets.$id.tsx": {
    id: "routes/admin/playground/supabase/storage/buckets.$id";
    page: "/admin/playground/supabase/storage/buckets/:id";
  };
  "routes\\admin\\playground\\long-running-tasks\\index.tsx": {
    id: "routes/admin/playground/long-running-tasks/index";
    page: "/admin/playground/long-running-tasks";
  };
  "routes\\admin\\playground\\supabase\\storage\\buckets.tsx": {
    id: "routes/admin/playground/supabase/storage/buckets";
    page: "/admin/playground/supabase/storage/buckets";
  };
  "routes\\admin\\playground\\enhanced-location-test.tsx": {
    id: "routes/admin/playground/enhanced-location-test";
    page: "/admin/playground/enhanced-location-test";
  };
  "routes\\admin\\playground\\supabase\\storage\\index.tsx": {
    id: "routes/admin/playground/supabase/storage/index";
    page: "/admin/playground/supabase/storage";
  };
  "routes\\admin\\playground\\crud.projects\\index.tsx": {
    id: "routes/admin/playground/crud.projects/index";
    page: "/admin/playground/crud/projects";
  };
  "routes\\admin\\playground\\location-input-test.tsx": {
    id: "routes/admin/playground/location-input-test";
    page: "/admin/playground/location-input-test";
  };
  "routes\\admin\\playground\\ai\\openai\\chatgpt.tsx": {
    id: "routes/admin/playground/ai/openai/chatgpt";
    page: "/admin/playground/ai/openai/chatgpt";
  };
  "routes\\admin\\playground\\crud.projects\\$id.tsx": {
    id: "routes/admin/playground/crud.projects/$id";
    page: "/admin/playground/crud/projects/:id";
  };
  "routes\\admin\\playground\\crud.projects\\new.tsx": {
    id: "routes/admin/playground/crud.projects/new";
    page: "/admin/playground/crud/projects/new";
  };
  "routes\\admin\\playground\\handlebars\\index.tsx": {
    id: "routes/admin/playground/handlebars/index";
    page: "/admin/playground/handlebars";
  };
  "routes\\admin\\playground\\monaco-editor.tsx": {
    id: "routes/admin/playground/monaco-editor";
    page: "/admin/playground/monaco-editor";
  };
  "routes\\admin\\playground\\novel-editor.tsx": {
    id: "routes/admin/playground/novel-editor";
    page: "/admin/playground/novel-editor";
  };
  "routes\\admin\\playground\\chat\\index.tsx": {
    id: "routes/admin/playground/chat/index";
    page: "/admin/playground/chat";
  };
  "routes\\admin\\playground\\index.tsx": {
    id: "routes/admin/playground/index";
    page: "/admin/playground";
  };
  "routes\\admin\\playground\\crud.tsx": {
    id: "routes/admin/playground/crud";
    page: "/admin/playground/crud";
  };
  "routes\\admin\\analytics.tsx": {
    id: "routes/admin/analytics";
    page: "/admin/analytics" | "/admin/analytics/page-views" | "/admin/analytics/overview" | "/admin/analytics/settings" | "/admin/analytics/visitors" | "/admin/analytics/events";
  };
  "routes\\admin\\analytics\\page-views.tsx": {
    id: "routes/admin/analytics/page-views";
    page: "/admin/analytics/page-views";
  };
  "routes\\admin\\analytics\\overview.tsx": {
    id: "routes/admin/analytics/overview";
    page: "/admin/analytics/overview";
  };
  "routes\\admin\\analytics\\settings.tsx": {
    id: "routes/admin/analytics/settings";
    page: "/admin/analytics/settings";
  };
  "routes\\admin\\analytics\\visitors.tsx": {
    id: "routes/admin/analytics/visitors";
    page: "/admin/analytics/visitors";
  };
  "routes\\admin\\analytics\\events.tsx": {
    id: "routes/admin/analytics/events";
    page: "/admin/analytics/events";
  };
  "routes\\admin\\dashboard.tsx": {
    id: "routes/admin/dashboard";
    page: "/admin/dashboard";
  };
  "routes\\admin\\help-desk.tsx": {
    id: "routes/admin/help-desk";
    page: "/admin/help-desk" | "/admin/help-desk/surveys/:id/submissions" | "/admin/help-desk/surveys/:id/edit" | "/admin/help-desk/inbound-emails" | "/admin/help-desk/inbound-emails/:id" | "/admin/help-desk/surveys" | "/admin/help-desk/surveys/:id" | "/admin/help-desk/surveys/new" | "/admin/help-desk/feedback";
  };
  "routes\\admin\\help-desk\\surveys\\$id.submissions.tsx": {
    id: "routes/admin/help-desk/surveys/$id.submissions";
    page: "/admin/help-desk/surveys/:id/submissions";
  };
  "routes\\admin\\help-desk\\surveys\\$id.edit.tsx": {
    id: "routes/admin/help-desk/surveys/$id.edit";
    page: "/admin/help-desk/surveys/:id/edit";
  };
  "routes\\admin\\help-desk\\inbound-emails.tsx": {
    id: "routes/admin/help-desk/inbound-emails";
    page: "/admin/help-desk/inbound-emails" | "/admin/help-desk/inbound-emails/:id";
  };
  "routes\\admin\\help-desk\\inbound-emails\\$id.tsx": {
    id: "routes/admin/help-desk/inbound-emails/$id";
    page: "/admin/help-desk/inbound-emails/:id";
  };
  "routes\\admin\\help-desk\\surveys\\index.tsx": {
    id: "routes/admin/help-desk/surveys/index";
    page: "/admin/help-desk/surveys";
  };
  "routes\\admin\\help-desk\\surveys\\$id.tsx": {
    id: "routes/admin/help-desk/surveys/$id";
    page: "/admin/help-desk/surveys/:id";
  };
  "routes\\admin\\help-desk\\surveys\\new.tsx": {
    id: "routes/admin/help-desk/surveys/new";
    page: "/admin/help-desk/surveys/new";
  };
  "routes\\admin\\help-desk\\feedback.tsx": {
    id: "routes/admin/help-desk/feedback";
    page: "/admin/help-desk/feedback";
  };
  "routes\\admin\\help-desk\\index.tsx": {
    id: "routes/admin/help-desk/index";
    page: "/admin/help-desk";
  };
  "routes\\admin\\accounts.tsx": {
    id: "routes/admin/accounts";
    page: "/admin/accounts" | "/admin/accounts/ip-addresses/logs" | "/admin/accounts/ip-addresses" | "/admin/accounts/roles-and-permissions" | "/admin/accounts/roles-and-permissions/account-users/:account" | "/admin/accounts/roles-and-permissions/account-users" | "/admin/accounts/roles-and-permissions/admin-users" | "/admin/accounts/roles-and-permissions/permissions" | "/admin/accounts/roles-and-permissions/permissions/:id" | "/admin/accounts/roles-and-permissions/permissions/new" | "/admin/accounts/roles-and-permissions/roles" | "/admin/accounts/roles-and-permissions/roles/:id" | "/admin/accounts/roles-and-permissions/roles/new" | "/admin/accounts/roles-and-permissions/seed" | "/admin/accounts/subscriptions-revenue" | "/admin/accounts/subscriptions" | "/admin/accounts/subscriptions/:id" | "/admin/accounts/blacklist" | "/admin/accounts/users" | "/admin/accounts/users/:user/roles" | "/admin/accounts/users/new" | "/admin/accounts/:id";
  };
  "routes\\admin\\accounts\\__ip-addresses\\ip-addresses.logs.tsx": {
    id: "routes/admin/accounts/__ip-addresses/ip-addresses.logs";
    page: "/admin/accounts/ip-addresses/logs";
  };
  "routes\\admin\\accounts\\__ip-addresses\\ip-addresses.tsx": {
    id: "routes/admin/accounts/__ip-addresses/ip-addresses";
    page: "/admin/accounts/ip-addresses";
  };
  "routes\\admin\\accounts\\roles-and-permissions.tsx": {
    id: "routes/admin/accounts/roles-and-permissions";
    page: "/admin/accounts/roles-and-permissions" | "/admin/accounts/roles-and-permissions/account-users/:account" | "/admin/accounts/roles-and-permissions/account-users" | "/admin/accounts/roles-and-permissions/admin-users" | "/admin/accounts/roles-and-permissions/permissions" | "/admin/accounts/roles-and-permissions/permissions/:id" | "/admin/accounts/roles-and-permissions/permissions/new" | "/admin/accounts/roles-and-permissions/roles" | "/admin/accounts/roles-and-permissions/roles/:id" | "/admin/accounts/roles-and-permissions/roles/new" | "/admin/accounts/roles-and-permissions/seed";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\account-users.$account.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/account-users.$account";
    page: "/admin/accounts/roles-and-permissions/account-users/:account";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\account-users.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/account-users";
    page: "/admin/accounts/roles-and-permissions/account-users";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\admin-users.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/admin-users";
    page: "/admin/accounts/roles-and-permissions/admin-users";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\permissions.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/permissions";
    page: "/admin/accounts/roles-and-permissions/permissions" | "/admin/accounts/roles-and-permissions/permissions/:id" | "/admin/accounts/roles-and-permissions/permissions/new";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\permissions\\$id.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/permissions/$id";
    page: "/admin/accounts/roles-and-permissions/permissions/:id";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\permissions\\new.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/permissions/new";
    page: "/admin/accounts/roles-and-permissions/permissions/new";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\roles.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/roles";
    page: "/admin/accounts/roles-and-permissions/roles" | "/admin/accounts/roles-and-permissions/roles/:id" | "/admin/accounts/roles-and-permissions/roles/new";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\roles\\$id.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/roles/$id";
    page: "/admin/accounts/roles-and-permissions/roles/:id";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\roles\\new.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/roles/new";
    page: "/admin/accounts/roles-and-permissions/roles/new";
  };
  "routes\\admin\\accounts\\roles-and-permissions\\seed.tsx": {
    id: "routes/admin/accounts/roles-and-permissions/seed";
    page: "/admin/accounts/roles-and-permissions/seed";
  };
  "routes\\admin\\accounts\\subscriptions-revenue.tsx": {
    id: "routes/admin/accounts/subscriptions-revenue";
    page: "/admin/accounts/subscriptions-revenue";
  };
  "routes\\admin\\accounts\\subscriptions.tsx": {
    id: "routes/admin/accounts/subscriptions";
    page: "/admin/accounts/subscriptions" | "/admin/accounts/subscriptions/:id";
  };
  "routes\\admin\\accounts\\subscriptions\\$id.tsx": {
    id: "routes/admin/accounts/subscriptions/$id";
    page: "/admin/accounts/subscriptions/:id";
  };
  "routes\\admin\\accounts\\blacklist.tsx": {
    id: "routes/admin/accounts/blacklist";
    page: "/admin/accounts/blacklist";
  };
  "routes\\admin\\accounts\\index.tsx": {
    id: "routes/admin/accounts/index";
    page: "/admin/accounts";
  };
  "routes\\admin\\accounts\\users.tsx": {
    id: "routes/admin/accounts/users";
    page: "/admin/accounts/users" | "/admin/accounts/users/:user/roles" | "/admin/accounts/users/new";
  };
  "routes\\admin\\accounts\\users\\$user.roles.tsx": {
    id: "routes/admin/accounts/users/$user.roles";
    page: "/admin/accounts/users/:user/roles";
  };
  "routes\\admin\\accounts\\users\\new.tsx": {
    id: "routes/admin/accounts/users/new";
    page: "/admin/accounts/users/new";
  };
  "routes\\admin\\accounts\\$id.tsx": {
    id: "routes/admin/accounts/$id";
    page: "/admin/accounts/:id";
  };
  "routes\\admin\\entities.tsx": {
    id: "routes/admin/entities";
    page: "/admin/entities" | "/admin/entities/code-generator/generate/:entity" | "/admin/entities/code-generator/files/:entity" | "/admin/entities/code-generator" | "/admin/entities/templates/manual" | "/admin/entities/:entity/no-code" | "/admin/entities/:entity/no-code/:entity/relationships" | "/admin/entities/:entity/no-code/:entity/all-in-one" | "/admin/entities/:entity/no-code/:entity/:id/edit" | "/admin/entities/:entity/no-code/:entity/export" | "/admin/entities/:entity/no-code/:entity/import" | "/admin/entities/:entity/no-code/:entity/:id" | "/admin/entities/:entity/no-code/:entity/:id/share" | "/admin/entities/:entity/no-code/:entity/:id/tags" | "/admin/entities/:entity/no-code/:entity/new" | "/admin/entities/:entity/no-code/:entity" | "/admin/entities/templates" | "/admin/entities/formulas/logs" | "/admin/entities/relationships" | "/admin/entities/fake-rows" | "/admin/entities/formulas" | "/admin/entities/formulas/:id" | "/admin/entities/formulas/new" | "/admin/entities/:entity" | "/admin/entities/:entity/relationships" | "/admin/entities/:entity/relationships/:id" | "/admin/entities/:entity/relationships/new" | "/admin/entities/:entity/properties" | "/admin/entities/:entity/properties/:id" | "/admin/entities/:entity/properties/new" | "/admin/entities/:entity/templates" | "/admin/entities/:entity/templates/:id" | "/admin/entities/:entity/templates/new" | "/admin/entities/:entity/webhooks" | "/admin/entities/:entity/webhooks/:id" | "/admin/entities/:entity/webhooks/new" | "/admin/entities/:entity/details" | "/admin/entities/:entity/danger" | "/admin/entities/:entity/routes" | "/admin/entities/:entity/views" | "/admin/entities/:entity/logs" | "/admin/entities/:entity/rows" | "/admin/entities/:entity/api" | "/admin/entities/no-code" | "/admin/entities/no-code/lists/tasks" | "/admin/entities/no-code/stats/count" | "/admin/entities/groups" | "/admin/entities/groups/:id" | "/admin/entities/groups/new" | "/admin/entities/views" | "/admin/entities/views/new/:entity" | "/admin/entities/views/:id" | "/admin/entities/code" | "/admin/entities/logs" | "/admin/entities/rows" | "/admin/entities/api";
  };
  "routes\\admin\\entities\\code-generator\\generate.$entity.tsx": {
    id: "routes/admin/entities/code-generator/generate.$entity";
    page: "/admin/entities/code-generator/generate/:entity";
  };
  "routes\\admin\\entities\\code-generator\\files.$entity.tsx": {
    id: "routes/admin/entities/code-generator/files.$entity";
    page: "/admin/entities/code-generator/files/:entity";
  };
  "routes\\admin\\entities\\code-generator\\index.tsx": {
    id: "routes/admin/entities/code-generator/index";
    page: "/admin/entities/code-generator";
  };
  "routes\\admin\\entities\\templates\\manual.tsx": {
    id: "routes/admin/entities/templates/manual";
    page: "/admin/entities/templates/manual";
  };
  "routes\\admin\\entities\\$entity.no-code.tsx": {
    id: "routes/admin/entities/$entity.no-code";
    page: "/admin/entities/:entity/no-code" | "/admin/entities/:entity/no-code/:entity/relationships" | "/admin/entities/:entity/no-code/:entity/all-in-one" | "/admin/entities/:entity/no-code/:entity/:id/edit" | "/admin/entities/:entity/no-code/:entity/export" | "/admin/entities/:entity/no-code/:entity/import" | "/admin/entities/:entity/no-code/:entity/:id" | "/admin/entities/:entity/no-code/:entity/:id/share" | "/admin/entities/:entity/no-code/:entity/:id/tags" | "/admin/entities/:entity/no-code/:entity/new" | "/admin/entities/:entity/no-code/:entity";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.relationships.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.relationships";
    page: "/admin/entities/:entity/no-code/:entity/relationships";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.all-in-one.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.all-in-one";
    page: "/admin/entities/:entity/no-code/:entity/all-in-one";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.$id.edit.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.$id.edit";
    page: "/admin/entities/:entity/no-code/:entity/:id/edit";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.export.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.export";
    page: "/admin/entities/:entity/no-code/:entity/export";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.import.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.import";
    page: "/admin/entities/:entity/no-code/:entity/import";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.$id.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.$id";
    page: "/admin/entities/:entity/no-code/:entity/:id" | "/admin/entities/:entity/no-code/:entity/:id/share" | "/admin/entities/:entity/no-code/:entity/:id/tags";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.$id\\share.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.$id/share";
    page: "/admin/entities/:entity/no-code/:entity/:id/share";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.$id\\tags.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.$id/tags";
    page: "/admin/entities/:entity/no-code/:entity/:id/tags";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.new.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity.new";
    page: "/admin/entities/:entity/no-code/:entity/new";
  };
  "routes\\admin\\entities\\$entity.no-code\\$entity.__autogenerated\\__$entity.tsx": {
    id: "routes/admin/entities/$entity.no-code/$entity.__autogenerated/__$entity";
    page: "/admin/entities/:entity/no-code/:entity";
  };
  "routes\\admin\\entities\\$entity.no-code\\index.tsx": {
    id: "routes/admin/entities/$entity.no-code/index";
    page: "/admin/entities/:entity/no-code";
  };
  "routes\\admin\\entities\\templates\\index.tsx": {
    id: "routes/admin/entities/templates/index";
    page: "/admin/entities/templates";
  };
  "routes\\admin\\entities\\formulas.logs.tsx": {
    id: "routes/admin/entities/formulas.logs";
    page: "/admin/entities/formulas/logs";
  };
  "routes\\admin\\entities\\relationships.tsx": {
    id: "routes/admin/entities/relationships";
    page: "/admin/entities/relationships";
  };
  "routes\\admin\\entities\\fake-rows.tsx": {
    id: "routes/admin/entities/fake-rows";
    page: "/admin/entities/fake-rows";
  };
  "routes\\admin\\entities\\formulas.tsx": {
    id: "routes/admin/entities/formulas";
    page: "/admin/entities/formulas" | "/admin/entities/formulas/:id" | "/admin/entities/formulas/new";
  };
  "routes\\admin\\entities\\formulas\\$id.tsx": {
    id: "routes/admin/entities/formulas/$id";
    page: "/admin/entities/formulas/:id";
  };
  "routes\\admin\\entities\\formulas\\new.tsx": {
    id: "routes/admin/entities/formulas/new";
    page: "/admin/entities/formulas/new";
  };
  "routes\\admin\\entities\\$entity.tsx": {
    id: "routes/admin/entities/$entity";
    page: "/admin/entities/:entity" | "/admin/entities/:entity/relationships" | "/admin/entities/:entity/relationships/:id" | "/admin/entities/:entity/relationships/new" | "/admin/entities/:entity/properties" | "/admin/entities/:entity/properties/:id" | "/admin/entities/:entity/properties/new" | "/admin/entities/:entity/templates" | "/admin/entities/:entity/templates/:id" | "/admin/entities/:entity/templates/new" | "/admin/entities/:entity/webhooks" | "/admin/entities/:entity/webhooks/:id" | "/admin/entities/:entity/webhooks/new" | "/admin/entities/:entity/details" | "/admin/entities/:entity/danger" | "/admin/entities/:entity/routes" | "/admin/entities/:entity/views" | "/admin/entities/:entity/logs" | "/admin/entities/:entity/rows" | "/admin/entities/:entity/api";
  };
  "routes\\admin\\entities\\$entity\\relationships.tsx": {
    id: "routes/admin/entities/$entity/relationships";
    page: "/admin/entities/:entity/relationships" | "/admin/entities/:entity/relationships/:id" | "/admin/entities/:entity/relationships/new";
  };
  "routes\\admin\\entities\\$entity\\relationships\\$id.tsx": {
    id: "routes/admin/entities/$entity/relationships/$id";
    page: "/admin/entities/:entity/relationships/:id";
  };
  "routes\\admin\\entities\\$entity\\relationships\\new.tsx": {
    id: "routes/admin/entities/$entity/relationships/new";
    page: "/admin/entities/:entity/relationships/new";
  };
  "routes\\admin\\entities\\$entity\\properties.tsx": {
    id: "routes/admin/entities/$entity/properties";
    page: "/admin/entities/:entity/properties" | "/admin/entities/:entity/properties/:id" | "/admin/entities/:entity/properties/new";
  };
  "routes\\admin\\entities\\$entity\\properties\\$id.tsx": {
    id: "routes/admin/entities/$entity/properties/$id";
    page: "/admin/entities/:entity/properties/:id";
  };
  "routes\\admin\\entities\\$entity\\properties\\new.tsx": {
    id: "routes/admin/entities/$entity/properties/new";
    page: "/admin/entities/:entity/properties/new";
  };
  "routes\\admin\\entities\\$entity\\templates.tsx": {
    id: "routes/admin/entities/$entity/templates";
    page: "/admin/entities/:entity/templates" | "/admin/entities/:entity/templates/:id" | "/admin/entities/:entity/templates/new";
  };
  "routes\\admin\\entities\\$entity\\templates\\$id.tsx": {
    id: "routes/admin/entities/$entity/templates/$id";
    page: "/admin/entities/:entity/templates/:id";
  };
  "routes\\admin\\entities\\$entity\\templates\\new.tsx": {
    id: "routes/admin/entities/$entity/templates/new";
    page: "/admin/entities/:entity/templates/new";
  };
  "routes\\admin\\entities\\$entity\\webhooks.tsx": {
    id: "routes/admin/entities/$entity/webhooks";
    page: "/admin/entities/:entity/webhooks" | "/admin/entities/:entity/webhooks/:id" | "/admin/entities/:entity/webhooks/new";
  };
  "routes\\admin\\entities\\$entity\\webhooks\\$id.tsx": {
    id: "routes/admin/entities/$entity/webhooks/$id";
    page: "/admin/entities/:entity/webhooks/:id";
  };
  "routes\\admin\\entities\\$entity\\webhooks\\new.tsx": {
    id: "routes/admin/entities/$entity/webhooks/new";
    page: "/admin/entities/:entity/webhooks/new";
  };
  "routes\\admin\\entities\\$entity\\details.tsx": {
    id: "routes/admin/entities/$entity/details";
    page: "/admin/entities/:entity/details";
  };
  "routes\\admin\\entities\\$entity\\danger.tsx": {
    id: "routes/admin/entities/$entity/danger";
    page: "/admin/entities/:entity/danger";
  };
  "routes\\admin\\entities\\$entity\\routes.tsx": {
    id: "routes/admin/entities/$entity/routes";
    page: "/admin/entities/:entity/routes";
  };
  "routes\\admin\\entities\\$entity\\views.tsx": {
    id: "routes/admin/entities/$entity/views";
    page: "/admin/entities/:entity/views";
  };
  "routes\\admin\\entities\\$entity\\logs.tsx": {
    id: "routes/admin/entities/$entity/logs";
    page: "/admin/entities/:entity/logs";
  };
  "routes\\admin\\entities\\$entity\\rows.tsx": {
    id: "routes/admin/entities/$entity/rows";
    page: "/admin/entities/:entity/rows";
  };
  "routes\\admin\\entities\\$entity\\api.tsx": {
    id: "routes/admin/entities/$entity/api";
    page: "/admin/entities/:entity/api";
  };
  "routes\\admin\\entities\\no-code.tsx": {
    id: "routes/admin/entities/no-code";
    page: "/admin/entities/no-code" | "/admin/entities/no-code/lists/tasks" | "/admin/entities/no-code/stats/count";
  };
  "routes\\admin\\entities\\no-code\\lists\\tasks.tsx": {
    id: "routes/admin/entities/no-code/lists/tasks";
    page: "/admin/entities/no-code/lists/tasks";
  };
  "routes\\admin\\entities\\no-code\\stats\\count.tsx": {
    id: "routes/admin/entities/no-code/stats/count";
    page: "/admin/entities/no-code/stats/count";
  };
  "routes\\admin\\entities\\no-code\\index.tsx": {
    id: "routes/admin/entities/no-code/index";
    page: "/admin/entities/no-code";
  };
  "routes\\admin\\entities\\groups.tsx": {
    id: "routes/admin/entities/groups";
    page: "/admin/entities/groups" | "/admin/entities/groups/:id" | "/admin/entities/groups/new";
  };
  "routes\\admin\\entities\\groups\\$id.tsx": {
    id: "routes/admin/entities/groups/$id";
    page: "/admin/entities/groups/:id";
  };
  "routes\\admin\\entities\\groups\\new.tsx": {
    id: "routes/admin/entities/groups/new";
    page: "/admin/entities/groups/new";
  };
  "routes\\admin\\entities\\index.tsx": {
    id: "routes/admin/entities/index";
    page: "/admin/entities";
  };
  "routes\\admin\\entities\\views.tsx": {
    id: "routes/admin/entities/views";
    page: "/admin/entities/views" | "/admin/entities/views/new/:entity" | "/admin/entities/views/:id";
  };
  "routes\\admin\\entities\\views\\new.$entity.tsx": {
    id: "routes/admin/entities/views/new.$entity";
    page: "/admin/entities/views/new/:entity";
  };
  "routes\\admin\\entities\\views\\$id.tsx": {
    id: "routes/admin/entities/views/$id";
    page: "/admin/entities/views/:id";
  };
  "routes\\admin\\entities\\code.tsx": {
    id: "routes/admin/entities/code";
    page: "/admin/entities/code";
  };
  "routes\\admin\\entities\\logs.tsx": {
    id: "routes/admin/entities/logs";
    page: "/admin/entities/logs";
  };
  "routes\\admin\\entities\\rows.tsx": {
    id: "routes/admin/entities/rows";
    page: "/admin/entities/rows";
  };
  "routes\\admin\\entities\\api.tsx": {
    id: "routes/admin/entities/api";
    page: "/admin/entities/api";
  };
  "routes\\admin\\settings.tsx": {
    id: "routes/admin/settings";
    page: "/admin/settings" | "/admin/settings/internationalization" | "/admin/settings/transactional-emails" | "/admin/settings/pricing/edit/:id" | "/admin/settings/pricing/features" | "/admin/settings/accounts/types" | "/admin/settings/accounts/types/:id" | "/admin/settings/accounts/types/new" | "/admin/settings/authentication" | "/admin/settings/pricing/new" | "/admin/settings/analytics" | "/admin/settings/accounts" | "/admin/settings/cookies" | "/admin/settings/general" | "/admin/settings/pricing" | "/admin/settings/profile" | "/admin/settings/danger" | "/admin/settings/cache" | "/admin/settings/seo";
  };
  "routes\\admin\\settings\\internationalization.tsx": {
    id: "routes/admin/settings/internationalization";
    page: "/admin/settings/internationalization";
  };
  "routes\\admin\\settings\\transactional-emails.tsx": {
    id: "routes/admin/settings/transactional-emails";
    page: "/admin/settings/transactional-emails";
  };
  "routes\\admin\\settings\\pricing.edit.$id.tsx": {
    id: "routes/admin/settings/pricing.edit.$id";
    page: "/admin/settings/pricing/edit/:id";
  };
  "routes\\admin\\settings\\pricing.features.tsx": {
    id: "routes/admin/settings/pricing.features";
    page: "/admin/settings/pricing/features";
  };
  "routes\\admin\\settings\\accounts.types.tsx": {
    id: "routes/admin/settings/accounts.types";
    page: "/admin/settings/accounts/types" | "/admin/settings/accounts/types/:id" | "/admin/settings/accounts/types/new";
  };
  "routes\\admin\\settings\\accounts.types\\$id.tsx": {
    id: "routes/admin/settings/accounts.types/$id";
    page: "/admin/settings/accounts/types/:id";
  };
  "routes\\admin\\settings\\accounts.types\\new.tsx": {
    id: "routes/admin/settings/accounts.types/new";
    page: "/admin/settings/accounts/types/new";
  };
  "routes\\admin\\settings\\authentication.tsx": {
    id: "routes/admin/settings/authentication";
    page: "/admin/settings/authentication";
  };
  "routes\\admin\\settings\\pricing.new.tsx": {
    id: "routes/admin/settings/pricing.new";
    page: "/admin/settings/pricing/new";
  };
  "routes\\admin\\settings\\analytics.tsx": {
    id: "routes/admin/settings/analytics";
    page: "/admin/settings/analytics";
  };
  "routes\\admin\\settings\\accounts.tsx": {
    id: "routes/admin/settings/accounts";
    page: "/admin/settings/accounts";
  };
  "routes\\admin\\settings\\cookies.tsx": {
    id: "routes/admin/settings/cookies";
    page: "/admin/settings/cookies";
  };
  "routes\\admin\\settings\\general.tsx": {
    id: "routes/admin/settings/general";
    page: "/admin/settings/general";
  };
  "routes\\admin\\settings\\pricing.tsx": {
    id: "routes/admin/settings/pricing";
    page: "/admin/settings/pricing";
  };
  "routes\\admin\\settings\\profile.tsx": {
    id: "routes/admin/settings/profile";
    page: "/admin/settings/profile";
  };
  "routes\\admin\\settings\\danger.tsx": {
    id: "routes/admin/settings/danger";
    page: "/admin/settings/danger";
  };
  "routes\\admin\\settings\\cache.tsx": {
    id: "routes/admin/settings/cache";
    page: "/admin/settings/cache";
  };
  "routes\\admin\\settings\\seo.tsx": {
    id: "routes/admin/settings/seo";
    page: "/admin/settings/seo";
  };
  "routes\\admin\\supabase.tsx": {
    id: "routes/admin/supabase";
    page: "/admin/supabase";
  };
  "routes\\admin\\metrics.tsx": {
    id: "routes/admin/metrics";
    page: "/admin/metrics" | "/admin/metrics/settings" | "/admin/metrics/summary" | "/admin/metrics/logs";
  };
  "routes\\admin\\metrics\\settings.tsx": {
    id: "routes/admin/metrics/settings";
    page: "/admin/metrics/settings";
  };
  "routes\\admin\\metrics\\summary.tsx": {
    id: "routes/admin/metrics/summary";
    page: "/admin/metrics/summary";
  };
  "routes\\admin\\metrics\\logs.tsx": {
    id: "routes/admin/metrics/logs";
    page: "/admin/metrics/logs";
  };
  "routes\\admin\\portals.tsx": {
    id: "routes/admin/portals";
    page: "/admin/portals";
  };
  "routes\\admin\\prompts.tsx": {
    id: "routes/admin/prompts";
    page: "/admin/prompts" | "/admin/prompts/executions/:id/results" | "/admin/prompts/executions/:id" | "/admin/prompts/builder/:id" | "/admin/prompts/builder/:id/templates" | "/admin/prompts/builder/:id/variables" | "/admin/prompts/builder/:id/variables/:variable" | "/admin/prompts/builder/:id/variables/new" | "/admin/prompts/builder/:id/outputs" | "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping" | "/admin/prompts/builder/:id/outputs/:output/mappings/new" | "/admin/prompts/builder/:id/outputs/:output" | "/admin/prompts/builder/:id/outputs/new" | "/admin/prompts/results/:id" | "/admin/prompts/builder" | "/admin/prompts/builder/new" | "/admin/prompts/groups" | "/admin/prompts/groups/:id" | "/admin/prompts/groups/new";
  };
  "routes\\admin\\prompts\\executions.$id.results.tsx": {
    id: "routes/admin/prompts/executions.$id.results";
    page: "/admin/prompts/executions/:id/results";
  };
  "routes\\admin\\prompts\\executions.$id.tsx": {
    id: "routes/admin/prompts/executions.$id";
    page: "/admin/prompts/executions/:id";
  };
  "routes\\admin\\prompts\\builder.$id.tsx": {
    id: "routes/admin/prompts/builder.$id";
    page: "/admin/prompts/builder/:id" | "/admin/prompts/builder/:id/templates" | "/admin/prompts/builder/:id/variables" | "/admin/prompts/builder/:id/variables/:variable" | "/admin/prompts/builder/:id/variables/new" | "/admin/prompts/builder/:id/outputs" | "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping" | "/admin/prompts/builder/:id/outputs/:output/mappings/new" | "/admin/prompts/builder/:id/outputs/:output" | "/admin/prompts/builder/:id/outputs/new";
  };
  "routes\\admin\\prompts\\builder.$id\\templates.tsx": {
    id: "routes/admin/prompts/builder.$id/templates";
    page: "/admin/prompts/builder/:id/templates";
  };
  "routes\\admin\\prompts\\builder.$id\\variables.tsx": {
    id: "routes/admin/prompts/builder.$id/variables";
    page: "/admin/prompts/builder/:id/variables" | "/admin/prompts/builder/:id/variables/:variable" | "/admin/prompts/builder/:id/variables/new";
  };
  "routes\\admin\\prompts\\builder.$id\\variables\\$variable.tsx": {
    id: "routes/admin/prompts/builder.$id/variables/$variable";
    page: "/admin/prompts/builder/:id/variables/:variable";
  };
  "routes\\admin\\prompts\\builder.$id\\variables\\new.tsx": {
    id: "routes/admin/prompts/builder.$id/variables/new";
    page: "/admin/prompts/builder/:id/variables/new";
  };
  "routes\\admin\\prompts\\builder.$id\\outputs.tsx": {
    id: "routes/admin/prompts/builder.$id/outputs";
    page: "/admin/prompts/builder/:id/outputs" | "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping" | "/admin/prompts/builder/:id/outputs/:output/mappings/new" | "/admin/prompts/builder/:id/outputs/:output" | "/admin/prompts/builder/:id/outputs/new";
  };
  "routes\\admin\\prompts\\builder.$id\\outputs\\$output.mappings.$mapping.tsx": {
    id: "routes/admin/prompts/builder.$id/outputs/$output.mappings.$mapping";
    page: "/admin/prompts/builder/:id/outputs/:output/mappings/:mapping";
  };
  "routes\\admin\\prompts\\builder.$id\\outputs\\$output.mappings.new.tsx": {
    id: "routes/admin/prompts/builder.$id/outputs/$output.mappings.new";
    page: "/admin/prompts/builder/:id/outputs/:output/mappings/new";
  };
  "routes\\admin\\prompts\\builder.$id\\outputs\\$output.tsx": {
    id: "routes/admin/prompts/builder.$id/outputs/$output";
    page: "/admin/prompts/builder/:id/outputs/:output";
  };
  "routes\\admin\\prompts\\builder.$id\\outputs\\new.tsx": {
    id: "routes/admin/prompts/builder.$id/outputs/new";
    page: "/admin/prompts/builder/:id/outputs/new";
  };
  "routes\\admin\\prompts\\builder.$id\\index.tsx": {
    id: "routes/admin/prompts/builder.$id/index";
    page: "/admin/prompts/builder/:id";
  };
  "routes\\admin\\prompts\\results.$id.tsx": {
    id: "routes/admin/prompts/results.$id";
    page: "/admin/prompts/results/:id";
  };
  "routes\\admin\\prompts\\builder.tsx": {
    id: "routes/admin/prompts/builder";
    page: "/admin/prompts/builder" | "/admin/prompts/builder/new";
  };
  "routes\\admin\\prompts\\builder\\new.tsx": {
    id: "routes/admin/prompts/builder/new";
    page: "/admin/prompts/builder/new";
  };
  "routes\\admin\\prompts\\groups.tsx": {
    id: "routes/admin/prompts/groups";
    page: "/admin/prompts/groups" | "/admin/prompts/groups/:id" | "/admin/prompts/groups/new";
  };
  "routes\\admin\\prompts\\groups\\$id.tsx": {
    id: "routes/admin/prompts/groups/$id";
    page: "/admin/prompts/groups/:id";
  };
  "routes\\admin\\prompts\\groups\\new.tsx": {
    id: "routes/admin/prompts/groups/new";
    page: "/admin/prompts/groups/new";
  };
  "routes\\admin\\prompts\\index.tsx": {
    id: "routes/admin/prompts/index";
    page: "/admin/prompts";
  };
  "routes\\admin\\events.tsx": {
    id: "routes/admin/events";
    page: "/admin/events";
  };
  "routes\\admin\\pages.tsx": {
    id: "routes/admin/pages";
    page: "/admin/pages" | "/admin/pages/edit/:id" | "/admin/pages/edit/:id/settings" | "/admin/pages/edit/:id/blocks" | "/admin/pages/edit/:id/seo" | "/admin/pages/seo" | "/admin/pages/ab";
  };
  "routes\\admin\\pages\\edit.$id.tsx": {
    id: "routes/admin/pages/edit.$id";
    page: "/admin/pages/edit/:id" | "/admin/pages/edit/:id/settings" | "/admin/pages/edit/:id/blocks" | "/admin/pages/edit/:id/seo";
  };
  "routes\\admin\\pages\\edit.$id\\settings.tsx": {
    id: "routes/admin/pages/edit.$id/settings";
    page: "/admin/pages/edit/:id/settings";
  };
  "routes\\admin\\pages\\edit.$id\\blocks.tsx": {
    id: "routes/admin/pages/edit.$id/blocks";
    page: "/admin/pages/edit/:id/blocks";
  };
  "routes\\admin\\pages\\edit.$id\\seo.tsx": {
    id: "routes/admin/pages/edit.$id/seo";
    page: "/admin/pages/edit/:id/seo";
  };
  "routes\\admin\\pages\\index.tsx": {
    id: "routes/admin/pages/index";
    page: "/admin/pages";
  };
  "routes\\admin\\pages\\seo.tsx": {
    id: "routes/admin/pages/seo";
    page: "/admin/pages/seo";
  };
  "routes\\admin\\pages\\ab.tsx": {
    id: "routes/admin/pages/ab";
    page: "/admin/pages/ab";
  };
  "routes\\admin\\setup.tsx": {
    id: "routes/admin/setup";
    page: "/admin/setup";
  };
  "routes\\admin\\404.tsx": {
    id: "routes/admin/404";
    page: "/admin/404";
  };
  "routes\\admin\\api.tsx": {
    id: "routes/admin/api";
    page: "/admin/api" | "/admin/api/credits" | "/admin/api/docs" | "/admin/api/keys" | "/admin/api/keys/:id" | "/admin/api/keys/new" | "/admin/api/logs";
  };
  "routes\\admin\\api\\credits.tsx": {
    id: "routes/admin/api/credits";
    page: "/admin/api/credits";
  };
  "routes\\admin\\api\\index.tsx": {
    id: "routes/admin/api/index";
    page: "/admin/api";
  };
  "routes\\admin\\api\\docs.tsx": {
    id: "routes/admin/api/docs";
    page: "/admin/api/docs";
  };
  "routes\\admin\\api\\keys.tsx": {
    id: "routes/admin/api/keys";
    page: "/admin/api/keys" | "/admin/api/keys/:id" | "/admin/api/keys/new";
  };
  "routes\\admin\\api\\keys\\$id.tsx": {
    id: "routes/admin/api/keys/$id";
    page: "/admin/api/keys/:id";
  };
  "routes\\admin\\api\\keys\\new.tsx": {
    id: "routes/admin/api/keys/new";
    page: "/admin/api/keys/new";
  };
  "routes\\admin\\api\\logs.tsx": {
    id: "routes/admin/api/logs";
    page: "/admin/api/logs";
  };
  "routes\\admin\\crm.tsx": {
    id: "routes/admin/crm";
    page: "/admin/crm" | "/admin/crm/:entity/relationships" | "/admin/crm/:entity/all-in-one" | "/admin/crm/:entity/:id/edit" | "/admin/crm/:entity/export" | "/admin/crm/:entity/import" | "/admin/crm/:entity/:id" | "/admin/crm/:entity/:id/share" | "/admin/crm/:entity/:id/tags" | "/admin/crm/:entity/new" | "/admin/crm/:entity" | "/admin/crm/sync";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.relationships.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.relationships";
    page: "/admin/crm/:entity/relationships";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.all-in-one.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.all-in-one";
    page: "/admin/crm/:entity/all-in-one";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.$id.edit.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.$id.edit";
    page: "/admin/crm/:entity/:id/edit";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.export.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.export";
    page: "/admin/crm/:entity/export";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.import.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.import";
    page: "/admin/crm/:entity/import";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.$id.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.$id";
    page: "/admin/crm/:entity/:id" | "/admin/crm/:entity/:id/share" | "/admin/crm/:entity/:id/tags";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.$id\\share.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.$id/share";
    page: "/admin/crm/:entity/:id/share";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.$id\\tags.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.$id/tags";
    page: "/admin/crm/:entity/:id/tags";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.new.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity.new";
    page: "/admin/crm/:entity/new";
  };
  "routes\\admin\\crm\\$entity.__autogenerated\\__$entity.tsx": {
    id: "routes/admin/crm/$entity.__autogenerated/__$entity";
    page: "/admin/crm/:entity";
  };
  "routes\\admin\\crm\\index.tsx": {
    id: "routes/admin/crm/index";
    page: "/admin/crm";
  };
  "routes\\admin\\crm\\sync.tsx": {
    id: "routes/admin/crm/sync";
    page: "/admin/crm/sync";
  };
  "routes\\brand.tsx": {
    id: "routes/brand";
    page: "/brand";
  };
  "routes\\debug.tsx": {
    id: "routes/debug";
    page: "/debug";
  };
  "routes\\index.tsx": {
    id: "routes/index";
    page: "/";
  };
  "routes\\login.tsx": {
    id: "routes/login";
    page: "/login";
  };
  "routes\\reset.tsx": {
    id: "routes/reset";
    page: "/reset";
  };
  "routes\\blog.tsx": {
    id: "routes/blog";
    page: "/blog";
  };
  "routes\\401.tsx": {
    id: "routes/401";
    page: "/401";
  };
  "routes\\404.tsx": {
    id: "routes/404";
    page: "/404";
  };
  "routes\\app.tsx": {
    id: "routes/app";
    page: "/app";
  };
  "routes\\dev.tsx": {
    id: "routes/dev";
    page: "/dev";
  };
  "routes\\$.tsx": {
    id: "routes/$";
    page: "/*";
  };
};
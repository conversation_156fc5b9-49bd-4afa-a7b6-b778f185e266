import { forwardRef, Ref, useImperativeHandle, useRef, useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { cn } from "~/lib/utils";
import HintTooltip from "../tooltips/HintTooltip";
import { Input } from "../input";
import { CalendarIcon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";
import { Button } from "../button";
import { Calendar } from "../calendar";
import { ScrollArea } from "../scroll-area";

export interface RefInputDateTime {
  input: HTMLInputElement | null;
}

// Pre-computed constants to avoid repeated array creation
const HOURS_12 = Array.from({ length: 12 }, (_, i) => i + 1);
const HOURS_24 = Array.from({ length: 24 }, (_, i) => i);
const MINUTES_60 = Array.from({ length: 60 }, (_, i) => i);

interface Props {
  timeFormat?: "12h" | "24h";
  name?: string;
  title: string;
  defaultValue?: Date;
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: string;
  icon?: string;
  darkMode?: boolean;
  autoFocus?: boolean;
  classNameInput?: string;
  // Date constraints
  minDate?: Date;
  maxDate?: Date;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  // Time constraints
  minTime?: string;
  maxTime?: string;
}

const InputDateTime = (
  {
    timeFormat = "24h",
    name,
    title,
    value,
    defaultValue,
    onChange,
    className,
    help,
    disabled = false,
    readOnly = false,
    required = false,
    hint,
    icon,
    darkMode,
    autoFocus,
    classNameInput,
    // Date constraints
    minDate,
    maxDate,
    disablePastDates = false,
    disableFutureDates = false,
    // Time constraints
    minTime,
    maxTime,
  }: Props,
  ref: Ref<RefInputDateTime>
) => {
  const { t } = useTranslation();
  const input = useRef<HTMLInputElement>(null);
  useImperativeHandle(ref, () => ({ input: input.current }));

  const [actualValue, setActualValue] = useState<Date | undefined>(value || defaultValue);
  const [date, setDate] = useState<Date>();
  const [hours, setHours] = useState("00");
  const [minutes, setMinutes] = useState("00");
  const [isOpen, setIsOpen] = useState(false);

  // Helper function to calculate effective date constraints
  const getEffectiveDateConstraints = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let effectiveMinDate = minDate;
    let effectiveMaxDate = maxDate;

    // Apply past/future date restrictions
    if (disablePastDates) {
      effectiveMinDate = effectiveMinDate ? new Date(Math.max(effectiveMinDate.getTime(), today.getTime())) : today;
    }

    if (disableFutureDates) {
      effectiveMaxDate = effectiveMaxDate ? new Date(Math.min(effectiveMaxDate.getTime(), today.getTime())) : today;
    }

      // Debug: Log the effective constraints and original values
    if (effectiveMinDate || effectiveMaxDate) {
      console.log("InputDateTime date constraint details:", {
        originalMinDateProp: minDate,
        originalMaxDateProp: maxDate,
        parsedMinDate: minDate?.toISOString(),
        parsedMaxDate: maxDate?.toISOString(),
        effectiveMinDate: effectiveMinDate?.toDateString(),
        effectiveMaxDate: effectiveMaxDate?.toDateString(),
        disablePastDates,
        disableFutureDates,
        today: today.toDateString()
      });
    }

    return { minDate: effectiveMinDate, maxDate: effectiveMaxDate };
  };

  // Helper function to check if a date is disabled
  const isDateDisabled = (date: Date) => {
    const { minDate: effectiveMinDate, maxDate: effectiveMaxDate } = getEffectiveDateConstraints();

    // Normalize dates to compare only the date part (ignore time)
    const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const minDateOnly = effectiveMinDate ? new Date(effectiveMinDate.getFullYear(), effectiveMinDate.getMonth(), effectiveMinDate.getDate()) : null;
    const maxDateOnly = effectiveMaxDate ? new Date(effectiveMaxDate.getFullYear(), effectiveMaxDate.getMonth(), effectiveMaxDate.getDate()) : null;

    // Debug: Log the date comparison
    if (minDateOnly || maxDateOnly) {
      console.log("Date comparison:", {
        checkingDate: dateOnly.toDateString(),
        minDate: minDateOnly?.toDateString(),
        maxDate: maxDateOnly?.toDateString(),
        isBeforeMin: minDateOnly ? dateOnly < minDateOnly : false,
        isAfterMax: maxDateOnly ? dateOnly > maxDateOnly : false
      });
    }

    if (minDateOnly && dateOnly < minDateOnly) return true;
    if (maxDateOnly && dateOnly > maxDateOnly) return true;

    return false;
  };

  // Helper function to check if a time is allowed
  const isTimeAllowed = (hours: number, minutes: number): boolean => {
    if (!minTime && !maxTime) return true;

    const timeInMinutes = hours * 60 + minutes;

    if (minTime) {
      const [minHours, minMinutes] = minTime.split(':').map(Number);
      const minTimeInMinutes = minHours * 60 + minMinutes;
      if (timeInMinutes < minTimeInMinutes) {
        return false;
      }
    }

    if (maxTime) {
      const [maxHours, maxMinutes] = maxTime.split(':').map(Number);
      const maxTimeInMinutes = maxHours * 60 + maxMinutes;
      if (timeInMinutes > maxTimeInMinutes) {
        return false;
      }
    }

    return true;
  };

  const hourList = useMemo(() => timeFormat === "12h" ? HOURS_12 : HOURS_24, [timeFormat]);

  // Helper function to get filtered hours based on time constraints
  const getFilteredHours = () => {
    const filteredHours = hourList.filter(h => {
      if (timeFormat === "12h") {
        // For 12h format, check both AM and PM possibilities
        const amHour = h === 12 ? 0 : h; // 12 AM = 0, 1-11 AM = 1-11
        const pmHour = h === 12 ? 12 : h + 12; // 12 PM = 12, 1-11 PM = 13-23

        // Check if either AM or PM version of this hour has valid minutes
        const amValid = MINUTES_60.some(minute => {
          return isTimeAllowed(amHour, minute);
        });
        const pmValid = MINUTES_60.some(minute => {
          return isTimeAllowed(pmHour, minute);
        });

        return amValid || pmValid;
      } else {
        // For 24h format, direct check
        const isValid = MINUTES_60.some(minute => {
          return isTimeAllowed(h, minute);
        });
        return isValid;
      }
    });

    // Debug: Log time constraints if they exist
    if ((minTime || maxTime) && filteredHours.length !== hourList.length) {
      console.log("Time constraints applied:", { minTime, maxTime });
      console.log("Filtered hours:", filteredHours, "from original:", hourList);
    }

    return filteredHours;
  };

  // Helper function to get filtered minutes based on time constraints
  const getFilteredMinutes = () => {
    if (!date) {
      // If no date is set yet but we have time constraints, show minutes that are valid for ANY available hour
      if (minTime || maxTime) {
        const allValidMinutes = new Set<number>();
        const validHours = getFilteredHours();

        validHours.forEach(h => {
          const currentHour = timeFormat === "12h" ? (h === 12 ? 0 : h) : h;
          MINUTES_60.forEach(minute => {
            if (isTimeAllowed(currentHour, minute)) {
              allValidMinutes.add(minute);
            }
          });
        });

        return Array.from(allValidMinutes).sort((a, b) => a - b);
      }
      return MINUTES_60;
    }

    const currentHour = date.getHours();
    const filteredMinutes = MINUTES_60.filter(m => {
      return isTimeAllowed(currentHour, m);
    });

    return filteredMinutes;
  };

  useEffect(() => {
    if (!isOpen && date) {
      updateDropdownComponents(date);
    }
  }, [isOpen]);

  useEffect(() => {
    if (value !== actualValue) {
      const val = value instanceof Date ? value : value ? new Date(value) : undefined;
      if (val && !isNaN(val.getTime())) {
        setActualValue(val);
      } else {
        setActualValue(undefined);
      }
    }
  }, [value]);

  useEffect(() => {
    if (defaultValue && !actualValue) {
      const def = defaultValue instanceof Date ? defaultValue : new Date(defaultValue);
      if (!isNaN(def.getTime())) {
        setActualValue(def);
      }
    }
  }, [defaultValue]);

  useEffect(() => {
    if (actualValue) {
      updateDropdownComponents(actualValue);
    }
  }, [actualValue, timeFormat]);

  const updateDropdownComponents = (date: Date) => {
    const d = date instanceof Date ? date : new Date(date);
    if (isNaN(d.getTime())) return;

    setDate(d);
    const hour = d.getHours();
    const minute = d.getMinutes().toString().padStart(2, "0");

    setHours(hour.toString().padStart(2, "0"));
    setMinutes(minute);
  };

  const getDateTimeInputValue = (): string => {
    if (!actualValue) return "";

    const d = actualValue instanceof Date ? actualValue : new Date(actualValue);
    if (isNaN(d.getTime())) return "";

    return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, "0")}-${d.getDate().toString().padStart(2, "0")}T${d.getHours().toString().padStart(2, "0")}:${d.getMinutes().toString().padStart(2, "0")}`;
  };

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      const h = parseInt(hours);
      const m = parseInt(minutes);
      const newDate = new Date(selectedDate);
      newDate.setHours(h);
      newDate.setMinutes(m);
      newDate.setSeconds(0);
      newDate.setMilliseconds(0);
      setActualValue(newDate);
      setDate(newDate);
      if (onChange) onChange(newDate);
    }
  };

  const handleTimeChange = (type: "hour" | "minute", val: string) => {
    // Default to midnight (00:00) instead of current time when no date exists
    const defaultDate = new Date();
    defaultDate.setHours(0, 0, 0, 0);

    const base = date ?? defaultDate;
    const h = type === "hour" ? parseInt(val) : parseInt(hours);
    const m = type === "minute" ? parseInt(val) : parseInt(minutes);

    const newDate = new Date(base);
    newDate.setHours(h);
    newDate.setMinutes(m);
    newDate.setSeconds(0);
    newDate.setMilliseconds(0);

    if (type === "hour") setHours(val.padStart(2, "0"));
    if (type === "minute") setMinutes(val.padStart(2, "0"));

    setActualValue(newDate);
    setDate(newDate);
    if (onChange) onChange(newDate);
  };

  const formatDisplayValue = (d: Date): string => {
    const date = new Date(d);
    if (isNaN(date.getTime())) return "";
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");

    if (timeFormat === "12h") {
      const hours12 = date.getHours() % 12 || 12;
      const ampm = date.getHours() >= 12 ? "PM" : "AM";
      return `${day}/${month}/${year} ${hours12.toString().padStart(2, "0")}:${minutes} ${ampm}`;
    }
    return `${day}/${month}/${year} ${hours}:${minutes}`;
  };

  return (
    <div className={clsx(className, !darkMode && "text-foreground")}>
      <div className="flex justify-between space-x-2">
        <label htmlFor={name} className="block text-xs font-medium">
          {title}
          {required && <span className="ml-1 text-red-500">*</span>}
        </label>
        {hint && <HintTooltip text={hint} />}
      </div>

      <div className="relative mt-1">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "hover:bg-accent hover:text-accent-foreground w-full justify-start text-left font-normal",
                !date && "text-muted-foreground",
                (disabled || readOnly) && "cursor-not-allowed opacity-50"
              )}
              disabled={disabled || readOnly}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? formatDisplayValue(date) : <span>Pick a date and time</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="sm:flex">
              <Calendar
                mode="single"
                selected={date}
                onSelect={handleDateSelect}
                initialFocus
                disabled={(date) => {
                  if (disabled || readOnly) return true;
                  return isDateDisabled(date);
                }}
              />
              <div className="flex flex-col divide-y sm:h-[300px] sm:flex-row sm:divide-x sm:divide-y-0">
                {/* Hours */}
                <ScrollArea className="w-64 sm:w-24">
                  <div className="flex p-2 sm:flex-col">
                    {getFilteredHours().map((h) => (
                      <Button
                        key={h}
                        size="icon"
                        variant={date && date.getHours() === h ? "default" : "ghost"}
                        className={cn("aspect-square sm:w-full sm:mb-1", date && date.getHours() === h && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground")}
                        onClick={() => handleTimeChange("hour", h.toString())}
                        disabled={disabled || readOnly}
                      >
                        {h.toString().padStart(2, "0")}
                      </Button>
                    ))}
                  </div>
                </ScrollArea>

                {/* Minutes */}
                <ScrollArea className="w-64 sm:w-24">
                  <div className="flex p-2 sm:flex-col">
                    {getFilteredMinutes().map((m) => (
                      <Button
                        key={m}
                        size="icon"
                        variant={date && date.getMinutes() === m ? "default" : "ghost"}
                        className={cn("aspect-square sm:w-full sm:mb-1", date && date.getMinutes() === m && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground")}
                        onClick={() => handleTimeChange("minute", m.toString())}
                        disabled={disabled || readOnly}
                      >
                        {m.toString().padStart(2, "0")}
                      </Button>
                    ))}
                  </div>
                </ScrollArea>

                {/* AM/PM - Only for 12h format */}
                {timeFormat === "12h" && (
                  <ScrollArea className="w-64 sm:w-24">
                    <div className="flex p-2 sm:flex-col sm:pr-4">
                      {["AM", "PM"].map((ampm) => (
                        <Button
                          key={ampm}
                          size="icon"
                          variant={date && ((ampm === "AM" && date.getHours() < 12) || (ampm === "PM" && date.getHours() >= 12)) ? "default" : "ghost"}
                          className={cn("aspect-square sm:w-full sm:mb-1", date && ((ampm === "AM" && date.getHours() < 12) || (ampm === "PM" && date.getHours() >= 12)) && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground")}
                          onClick={() => {
                            const newHours = date ? (ampm === "AM" ? date.getHours() % 12 : (date.getHours() % 12) + 12) : ampm === "AM" ? 0 : 12;
                            handleTimeChange("hour", newHours.toString());
                          }}
                          disabled={disabled || readOnly}
                        >
                          {ampm}
                        </Button>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={actualValue ? getDateTimeInputValue() : ""} required={required} />

        {actualValue && (
          <div className="text-muted-foreground/80 mt-2 px-1 text-xs">
            {t("dateTime.preview")}: <span className="text-foreground/90">{formatDisplayValue(actualValue)}</span>
          </div>
        )}
      </div>

      {help && <div className="text-muted-foreground mt-2 text-xs">{help}</div>}
    </div>
  );
};

export default forwardRef(InputDateTime);
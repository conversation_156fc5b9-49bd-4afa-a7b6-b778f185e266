{"version": 3, "sources": ["../../lodash/clone.js", "../../lodash/_castFunction.js", "../../lodash/forEach.js", "../../lodash/each.js", "../../lodash/_baseFilter.js", "../../lodash/filter.js", "../../lodash/_baseHas.js", "../../lodash/has.js", "../../lodash/isEmpty.js", "../../lodash/isUndefined.js", "../../lodash/_arrayReduce.js", "../../lodash/_baseReduce.js", "../../lodash/reduce.js", "../../lodash/_asciiSize.js", "../../lodash/_unicodeSize.js", "../../lodash/_stringSize.js", "../../lodash/size.js", "../../lodash/transform.js", "../../lodash/isArrayLikeObject.js", "../../lodash/union.js", "../../lodash/_baseValues.js", "../../lodash/values.js", "../../graphlib/lib/lodash.js", "../../graphlib/lib/graph.js", "../../graphlib/lib/version.js", "../../graphlib/lib/index.js", "../../graphlib/lib/json.js", "../../graphlib/lib/alg/components.js", "../../graphlib/lib/data/priority-queue.js", "../../graphlib/lib/alg/dijkstra.js", "../../graphlib/lib/alg/dijkstra-all.js", "../../graphlib/lib/alg/tarjan.js", "../../graphlib/lib/alg/find-cycles.js", "../../graphlib/lib/alg/floyd-warshall.js", "../../graphlib/lib/alg/topsort.js", "../../graphlib/lib/alg/is-acyclic.js", "../../graphlib/lib/alg/dfs.js", "../../graphlib/lib/alg/postorder.js", "../../graphlib/lib/alg/preorder.js", "../../graphlib/lib/alg/prim.js", "../../graphlib/lib/alg/index.js", "../../graphlib/index.js", "../../dagre/lib/graphlib.js", "../../lodash/cloneDeep.js", "../../lodash/defaults.js", "../../lodash/forIn.js", "../../lodash/_assignMergeValue.js", "../../lodash/_safeGet.js", "../../lodash/toPlainObject.js", "../../lodash/_baseMergeDeep.js", "../../lodash/_baseMerge.js", "../../lodash/_createAssigner.js", "../../lodash/merge.js", "../../lodash/_baseSet.js", "../../lodash/_basePickBy.js", "../../lodash/_basePick.js", "../../lodash/pick.js", "../../lodash/uniqueId.js", "../../lodash/_baseZipObject.js", "../../lodash/zipObject.js", "../../dagre/lib/lodash.js", "../../dagre/lib/data/list.js", "../../dagre/lib/greedy-fas.js", "../../dagre/lib/acyclic.js", "../../dagre/lib/util.js", "../../dagre/lib/normalize.js", "../../dagre/lib/rank/util.js", "../../dagre/lib/rank/feasible-tree.js", "../../dagre/lib/rank/network-simplex.js", "../../dagre/lib/rank/index.js", "../../dagre/lib/parent-dummy-chains.js", "../../dagre/lib/nesting-graph.js", "../../dagre/lib/add-border-segments.js", "../../dagre/lib/coordinate-system.js", "../../dagre/lib/order/init-order.js", "../../dagre/lib/order/cross-count.js", "../../dagre/lib/order/barycenter.js", "../../dagre/lib/order/resolve-conflicts.js", "../../dagre/lib/order/sort.js", "../../dagre/lib/order/sort-subgraph.js", "../../dagre/lib/order/build-layer-graph.js", "../../dagre/lib/order/add-subgraph-constraints.js", "../../dagre/lib/order/index.js", "../../dagre/lib/position/bk.js", "../../dagre/lib/position/index.js", "../../dagre/lib/layout.js", "../../dagre/lib/debug.js", "../../dagre/lib/version.js", "../../dagre/index.js"], "sourcesContent": ["var baseClone = require('./_baseClone');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * Creates a shallow clone of `value`.\n *\n * **Note:** This method is loosely based on the\n * [structured clone algorithm](https://mdn.io/Structured_clone_algorithm)\n * and supports cloning arrays, array buffers, booleans, date objects, maps,\n * numbers, `Object` objects, regexes, sets, strings, symbols, and typed\n * arrays. The own enumerable properties of `arguments` objects are cloned\n * as plain objects. An empty object is returned for uncloneable values such\n * as error objects, functions, DOM nodes, and WeakMaps.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to clone.\n * @returns {*} Returns the cloned value.\n * @see _.cloneDeep\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var shallow = _.clone(objects);\n * console.log(shallow[0] === objects[0]);\n * // => true\n */\nfunction clone(value) {\n  return baseClone(value, CLONE_SYMBOLS_FLAG);\n}\n\nmodule.exports = clone;\n", "var identity = require('./identity');\n\n/**\n * Casts `value` to `identity` if it's not a function.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Function} Returns cast function.\n */\nfunction castFunction(value) {\n  return typeof value == 'function' ? value : identity;\n}\n\nmodule.exports = castFunction;\n", "var arrayEach = require('./_arrayEach'),\n    baseEach = require('./_baseEach'),\n    castFunction = require('./_castFunction'),\n    isArray = require('./isArray');\n\n/**\n * Iterates over elements of `collection` and invokes `iteratee` for each element.\n * The iteratee is invoked with three arguments: (value, index|key, collection).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * **Note:** As with other \"Collections\" methods, objects with a \"length\"\n * property are iterated like arrays. To avoid this behavior use `_.forIn`\n * or `_.forOwn` for object iteration.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @alias each\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n * @see _.forEachRight\n * @example\n *\n * _.forEach([1, 2], function(value) {\n *   console.log(value);\n * });\n * // => Logs `1` then `2`.\n *\n * _.forEach({ 'a': 1, 'b': 2 }, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a' then 'b' (iteration order is not guaranteed).\n */\nfunction forEach(collection, iteratee) {\n  var func = isArray(collection) ? arrayEach : baseEach;\n  return func(collection, castFunction(iteratee));\n}\n\nmodule.exports = forEach;\n", "module.exports = require('./forEach');\n", "var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function(value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\n\nmodule.exports = baseFilter;\n", "var arrayFilter = require('./_arrayFilter'),\n    baseFilter = require('./_baseFilter'),\n    baseIteratee = require('./_baseIteratee'),\n    isArray = require('./isArray');\n\n/**\n * Iterates over elements of `collection`, returning an array of all elements\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * **Note:** Unlike `_.remove`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n * @see _.reject\n * @example\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': true },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * _.filter(users, function(o) { return !o.active; });\n * // => objects for ['fred']\n *\n * // The `_.matches` iteratee shorthand.\n * _.filter(users, { 'age': 36, 'active': true });\n * // => objects for ['barney']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.filter(users, ['active', false]);\n * // => objects for ['fred']\n *\n * // The `_.property` iteratee shorthand.\n * _.filter(users, 'active');\n * // => objects for ['barney']\n *\n * // Combining several predicates using `_.overEvery` or `_.overSome`.\n * _.filter(users, _.overSome([{ 'age': 36 }, ['age', 40]]));\n * // => objects for ['fred', 'barney']\n */\nfunction filter(collection, predicate) {\n  var func = isArray(collection) ? arrayFilter : baseFilter;\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nmodule.exports = filter;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.has` without support for deep paths.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {Array|string} key The key to check.\n * @returns {boolean} Returns `true` if `key` exists, else `false`.\n */\nfunction baseHas(object, key) {\n  return object != null && hasOwnProperty.call(object, key);\n}\n\nmodule.exports = baseHas;\n", "var baseHas = require('./_baseHas'),\n    hasPath = require('./_hasPath');\n\n/**\n * Checks if `path` is a direct property of `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n * @example\n *\n * var object = { 'a': { 'b': 2 } };\n * var other = _.create({ 'a': _.create({ 'b': 2 }) });\n *\n * _.has(object, 'a');\n * // => true\n *\n * _.has(object, 'a.b');\n * // => true\n *\n * _.has(object, ['a', 'b']);\n * // => true\n *\n * _.has(other, 'a');\n * // => false\n */\nfunction has(object, path) {\n  return object != null && hasPath(object, path, baseHas);\n}\n\nmodule.exports = has;\n", "var baseKeys = require('./_baseKeys'),\n    getTag = require('./_getTag'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray'),\n    isArrayLike = require('./isArrayLike'),\n    isBuffer = require('./isBuffer'),\n    isPrototype = require('./_isPrototype'),\n    isTypedArray = require('./isTypedArray');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if `value` is an empty object, collection, map, or set.\n *\n * Objects are considered empty if they have no own enumerable string keyed\n * properties.\n *\n * Array-like values such as `arguments` objects, arrays, buffers, strings, or\n * jQuery-like collections are considered empty if they have a `length` of `0`.\n * Similarly, maps and sets are considered empty if they have a `size` of `0`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is empty, else `false`.\n * @example\n *\n * _.isEmpty(null);\n * // => true\n *\n * _.isEmpty(true);\n * // => true\n *\n * _.isEmpty(1);\n * // => true\n *\n * _.isEmpty([1, 2, 3]);\n * // => false\n *\n * _.isEmpty({ 'a': 1 });\n * // => false\n */\nfunction isEmpty(value) {\n  if (value == null) {\n    return true;\n  }\n  if (isArrayLike(value) &&\n      (isArray(value) || typeof value == 'string' || typeof value.splice == 'function' ||\n        isBuffer(value) || isTypedArray(value) || isArguments(value))) {\n    return !value.length;\n  }\n  var tag = getTag(value);\n  if (tag == mapTag || tag == setTag) {\n    return !value.size;\n  }\n  if (isPrototype(value)) {\n    return !baseKeys(value).length;\n  }\n  for (var key in value) {\n    if (hasOwnProperty.call(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nmodule.exports = isEmpty;\n", "/**\n * Checks if `value` is `undefined`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `undefined`, else `false`.\n * @example\n *\n * _.isUndefined(void 0);\n * // => true\n *\n * _.isUndefined(null);\n * // => false\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\n\nmodule.exports = isUndefined;\n", "/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\n\nmodule.exports = arrayReduce;\n", "/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function(value, index, collection) {\n    accumulator = initAccum\n      ? (initAccum = false, value)\n      : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nmodule.exports = baseReduce;\n", "var arrayReduce = require('./_arrayReduce'),\n    baseEach = require('./_baseEach'),\n    baseIteratee = require('./_baseIteratee'),\n    baseReduce = require('./_baseReduce'),\n    isArray = require('./isArray');\n\n/**\n * Reduces `collection` to a value which is the accumulated result of running\n * each element in `collection` thru `iteratee`, where each successive\n * invocation is supplied the return value of the previous. If `accumulator`\n * is not given, the first element of `collection` is used as the initial\n * value. The iteratee is invoked with four arguments:\n * (accumulator, value, index|key, collection).\n *\n * Many lodash methods are guarded to work as iteratees for methods like\n * `_.reduce`, `_.reduceRight`, and `_.transform`.\n *\n * The guarded methods are:\n * `assign`, `defaults`, `defaultsDeep`, `includes`, `merge`, `orderBy`,\n * and `sortBy`\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @returns {*} Returns the accumulated value.\n * @see _.reduceRight\n * @example\n *\n * _.reduce([1, 2], function(sum, n) {\n *   return sum + n;\n * }, 0);\n * // => 3\n *\n * _.reduce({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n *   return result;\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] } (iteration order is not guaranteed)\n */\nfunction reduce(collection, iteratee, accumulator) {\n  var func = isArray(collection) ? arrayReduce : baseReduce,\n      initAccum = arguments.length < 3;\n\n  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEach);\n}\n\nmodule.exports = reduce;\n", "var baseProperty = require('./_baseProperty');\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\n\nmodule.exports = asciiSize;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsAstral = '[' + rsAstralRange + ']',\n    rsCombo = '[' + rsComboRange + ']',\n    rsFitz = '\\\\ud83c[\\\\udffb-\\\\udfff]',\n    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',\n    rsNonAstral = '[^' + rsAstralRange + ']',\n    rsRegional = '(?:\\\\ud83c[\\\\udde6-\\\\uddff]){2}',\n    rsSurrPair = '[\\\\ud800-\\\\udbff][\\\\udc00-\\\\udfff]',\n    rsZWJ = '\\\\u200d';\n\n/** Used to compose unicode regexes. */\nvar reOptMod = rsModifier + '?',\n    rsOptVar = '[' + rsVarRange + ']?',\n    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',\n    rsSeq = rsOptVar + reOptMod + rsOptJoin,\n    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';\n\n/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */\nvar reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');\n\n/**\n * Gets the size of a Unicode `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nfunction unicodeSize(string) {\n  var result = reUnicode.lastIndex = 0;\n  while (reUnicode.test(string)) {\n    ++result;\n  }\n  return result;\n}\n\nmodule.exports = unicodeSize;\n", "var asciiSize = require('./_asciiSize'),\n    hasUnicode = require('./_hasUnicode'),\n    unicodeSize = require('./_unicodeSize');\n\n/**\n * Gets the number of symbols in `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the string size.\n */\nfunction stringSize(string) {\n  return hasUnicode(string)\n    ? unicodeSize(string)\n    : asciiSize(string);\n}\n\nmodule.exports = stringSize;\n", "var baseKeys = require('./_baseKeys'),\n    getTag = require('./_getTag'),\n    isArrayLike = require('./isArrayLike'),\n    isString = require('./isString'),\n    stringSize = require('./_stringSize');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/**\n * Gets the size of `collection` by returning its length for array-like\n * values or the number of own enumerable string keyed properties for objects.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @returns {number} Returns the collection size.\n * @example\n *\n * _.size([1, 2, 3]);\n * // => 3\n *\n * _.size({ 'a': 1, 'b': 2 });\n * // => 2\n *\n * _.size('pebbles');\n * // => 7\n */\nfunction size(collection) {\n  if (collection == null) {\n    return 0;\n  }\n  if (isArrayLike(collection)) {\n    return isString(collection) ? stringSize(collection) : collection.length;\n  }\n  var tag = getTag(collection);\n  if (tag == mapTag || tag == setTag) {\n    return collection.size;\n  }\n  return baseKeys(collection).length;\n}\n\nmodule.exports = size;\n", "var arrayEach = require('./_arrayEach'),\n    baseCreate = require('./_baseCreate'),\n    baseForOwn = require('./_baseForOwn'),\n    baseIteratee = require('./_baseIteratee'),\n    getPrototype = require('./_getPrototype'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isFunction = require('./isFunction'),\n    isObject = require('./isObject'),\n    isTypedArray = require('./isTypedArray');\n\n/**\n * An alternative to `_.reduce`; this method transforms `object` to a new\n * `accumulator` object which is the result of running each of its own\n * enumerable string keyed properties thru `iteratee`, with each invocation\n * potentially mutating the `accumulator` object. If `accumulator` is not\n * provided, a new object with the same `[[Prototype]]` will be used. The\n * iteratee is invoked with four arguments: (accumulator, value, key, object).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The custom accumulator value.\n * @returns {*} Returns the accumulated value.\n * @example\n *\n * _.transform([2, 3, 4], function(result, n) {\n *   result.push(n *= n);\n *   return n % 2 == 0;\n * }, []);\n * // => [4, 9]\n *\n * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] }\n */\nfunction transform(object, iteratee, accumulator) {\n  var isArr = isArray(object),\n      isArrLike = isArr || isBuffer(object) || isTypedArray(object);\n\n  iteratee = baseIteratee(iteratee, 4);\n  if (accumulator == null) {\n    var Ctor = object && object.constructor;\n    if (isArrLike) {\n      accumulator = isArr ? new Ctor : [];\n    }\n    else if (isObject(object)) {\n      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};\n    }\n    else {\n      accumulator = {};\n    }\n  }\n  (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object) {\n    return iteratee(accumulator, value, index, object);\n  });\n  return accumulator;\n}\n\nmodule.exports = transform;\n", "var isArrayLike = require('./isArrayLike'),\n    isObjectLike = require('./isObjectLike');\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\nmodule.exports = isArrayLikeObject;\n", "var baseFlatten = require('./_baseFlatten'),\n    baseRest = require('./_baseRest'),\n    baseUniq = require('./_baseUniq'),\n    isArrayLikeObject = require('./isArrayLikeObject');\n\n/**\n * Creates an array of unique values, in order, from all given arrays using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * _.union([2], [1, 2]);\n * // => [2, 1]\n */\nvar union = baseRest(function(arrays) {\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));\n});\n\nmodule.exports = union;\n", "var arrayMap = require('./_arrayMap');\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function(key) {\n    return object[key];\n  });\n}\n\nmodule.exports = baseValues;\n", "var baseValues = require('./_baseValues'),\n    keys = require('./keys');\n\n/**\n * Creates an array of the own enumerable string keyed property values of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property values.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.values(new Foo);\n * // => [1, 2] (iteration order is not guaranteed)\n *\n * _.values('hi');\n * // => ['h', 'i']\n */\nfunction values(object) {\n  return object == null ? [] : baseValues(object, keys(object));\n}\n\nmodule.exports = values;\n", "/* global window */\n\nvar lodash;\n\nif (typeof require === \"function\") {\n  try {\n    lodash = {\n      clone: require(\"lodash/clone\"),\n      constant: require(\"lodash/constant\"),\n      each: require(\"lodash/each\"),\n      filter: require(\"lodash/filter\"),\n      has:  require(\"lodash/has\"),\n      isArray: require(\"lodash/isArray\"),\n      isEmpty: require(\"lodash/isEmpty\"),\n      isFunction: require(\"lodash/isFunction\"),\n      isUndefined: require(\"lodash/isUndefined\"),\n      keys: require(\"lodash/keys\"),\n      map: require(\"lodash/map\"),\n      reduce: require(\"lodash/reduce\"),\n      size: require(\"lodash/size\"),\n      transform: require(\"lodash/transform\"),\n      union: require(\"lodash/union\"),\n      values: require(\"lodash/values\")\n    };\n  } catch (e) {\n    // continue regardless of error\n  }\n}\n\nif (!lodash) {\n  lodash = window._;\n}\n\nmodule.exports = lodash;\n", "\"use strict\";\n\nvar _ = require(\"./lodash\");\n\nmodule.exports = Graph;\n\nvar DEFAULT_EDGE_NAME = \"\\x00\";\nvar GRAPH_NODE = \"\\x00\";\nvar EDGE_KEY_DELIM = \"\\x01\";\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\nfunction Graph(opts) {\n  this._isDirected = _.has(opts, \"directed\") ? opts.directed : true;\n  this._isMultigraph = _.has(opts, \"multigraph\") ? opts.multigraph : false;\n  this._isCompound = _.has(opts, \"compound\") ? opts.compound : false;\n\n  // Label for the graph itself\n  this._label = undefined;\n\n  // Defaults to be set when creating a new node\n  this._defaultNodeLabelFn = _.constant(undefined);\n\n  // Defaults to be set when creating a new edge\n  this._defaultEdgeLabelFn = _.constant(undefined);\n\n  // v -> label\n  this._nodes = {};\n\n  if (this._isCompound) {\n    // v -> parent\n    this._parent = {};\n\n    // v -> children\n    this._children = {};\n    this._children[GRAPH_NODE] = {};\n  }\n\n  // v -> edgeObj\n  this._in = {};\n\n  // u -> v -> Number\n  this._preds = {};\n\n  // v -> edgeObj\n  this._out = {};\n\n  // v -> w -> Number\n  this._sucs = {};\n\n  // e -> edgeObj\n  this._edgeObjs = {};\n\n  // e -> label\n  this._edgeLabels = {};\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\n\n/* === Graph functions ========= */\n\nGraph.prototype.isDirected = function() {\n  return this._isDirected;\n};\n\nGraph.prototype.isMultigraph = function() {\n  return this._isMultigraph;\n};\n\nGraph.prototype.isCompound = function() {\n  return this._isCompound;\n};\n\nGraph.prototype.setGraph = function(label) {\n  this._label = label;\n  return this;\n};\n\nGraph.prototype.graph = function() {\n  return this._label;\n};\n\n\n/* === Node functions ========== */\n\nGraph.prototype.setDefaultNodeLabel = function(newDefault) {\n  if (!_.isFunction(newDefault)) {\n    newDefault = _.constant(newDefault);\n  }\n  this._defaultNodeLabelFn = newDefault;\n  return this;\n};\n\nGraph.prototype.nodeCount = function() {\n  return this._nodeCount;\n};\n\nGraph.prototype.nodes = function() {\n  return _.keys(this._nodes);\n};\n\nGraph.prototype.sources = function() {\n  var self = this;\n  return _.filter(this.nodes(), function(v) {\n    return _.isEmpty(self._in[v]);\n  });\n};\n\nGraph.prototype.sinks = function() {\n  var self = this;\n  return _.filter(this.nodes(), function(v) {\n    return _.isEmpty(self._out[v]);\n  });\n};\n\nGraph.prototype.setNodes = function(vs, value) {\n  var args = arguments;\n  var self = this;\n  _.each(vs, function(v) {\n    if (args.length > 1) {\n      self.setNode(v, value);\n    } else {\n      self.setNode(v);\n    }\n  });\n  return this;\n};\n\nGraph.prototype.setNode = function(v, value) {\n  if (_.has(this._nodes, v)) {\n    if (arguments.length > 1) {\n      this._nodes[v] = value;\n    }\n    return this;\n  }\n\n  this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n  if (this._isCompound) {\n    this._parent[v] = GRAPH_NODE;\n    this._children[v] = {};\n    this._children[GRAPH_NODE][v] = true;\n  }\n  this._in[v] = {};\n  this._preds[v] = {};\n  this._out[v] = {};\n  this._sucs[v] = {};\n  ++this._nodeCount;\n  return this;\n};\n\nGraph.prototype.node = function(v) {\n  return this._nodes[v];\n};\n\nGraph.prototype.hasNode = function(v) {\n  return _.has(this._nodes, v);\n};\n\nGraph.prototype.removeNode =  function(v) {\n  var self = this;\n  if (_.has(this._nodes, v)) {\n    var removeEdge = function(e) { self.removeEdge(self._edgeObjs[e]); };\n    delete this._nodes[v];\n    if (this._isCompound) {\n      this._removeFromParentsChildList(v);\n      delete this._parent[v];\n      _.each(this.children(v), function(child) {\n        self.setParent(child);\n      });\n      delete this._children[v];\n    }\n    _.each(_.keys(this._in[v]), removeEdge);\n    delete this._in[v];\n    delete this._preds[v];\n    _.each(_.keys(this._out[v]), removeEdge);\n    delete this._out[v];\n    delete this._sucs[v];\n    --this._nodeCount;\n  }\n  return this;\n};\n\nGraph.prototype.setParent = function(v, parent) {\n  if (!this._isCompound) {\n    throw new Error(\"Cannot set parent in a non-compound graph\");\n  }\n\n  if (_.isUndefined(parent)) {\n    parent = GRAPH_NODE;\n  } else {\n    // Coerce parent to string\n    parent += \"\";\n    for (var ancestor = parent;\n      !_.isUndefined(ancestor);\n      ancestor = this.parent(ancestor)) {\n      if (ancestor === v) {\n        throw new Error(\"Setting \" + parent+ \" as parent of \" + v +\n                        \" would create a cycle\");\n      }\n    }\n\n    this.setNode(parent);\n  }\n\n  this.setNode(v);\n  this._removeFromParentsChildList(v);\n  this._parent[v] = parent;\n  this._children[parent][v] = true;\n  return this;\n};\n\nGraph.prototype._removeFromParentsChildList = function(v) {\n  delete this._children[this._parent[v]][v];\n};\n\nGraph.prototype.parent = function(v) {\n  if (this._isCompound) {\n    var parent = this._parent[v];\n    if (parent !== GRAPH_NODE) {\n      return parent;\n    }\n  }\n};\n\nGraph.prototype.children = function(v) {\n  if (_.isUndefined(v)) {\n    v = GRAPH_NODE;\n  }\n\n  if (this._isCompound) {\n    var children = this._children[v];\n    if (children) {\n      return _.keys(children);\n    }\n  } else if (v === GRAPH_NODE) {\n    return this.nodes();\n  } else if (this.hasNode(v)) {\n    return [];\n  }\n};\n\nGraph.prototype.predecessors = function(v) {\n  var predsV = this._preds[v];\n  if (predsV) {\n    return _.keys(predsV);\n  }\n};\n\nGraph.prototype.successors = function(v) {\n  var sucsV = this._sucs[v];\n  if (sucsV) {\n    return _.keys(sucsV);\n  }\n};\n\nGraph.prototype.neighbors = function(v) {\n  var preds = this.predecessors(v);\n  if (preds) {\n    return _.union(preds, this.successors(v));\n  }\n};\n\nGraph.prototype.isLeaf = function (v) {\n  var neighbors;\n  if (this.isDirected()) {\n    neighbors = this.successors(v);\n  } else {\n    neighbors = this.neighbors(v);\n  }\n  return neighbors.length === 0;\n};\n\nGraph.prototype.filterNodes = function(filter) {\n  var copy = new this.constructor({\n    directed: this._isDirected,\n    multigraph: this._isMultigraph,\n    compound: this._isCompound\n  });\n\n  copy.setGraph(this.graph());\n\n  var self = this;\n  _.each(this._nodes, function(value, v) {\n    if (filter(v)) {\n      copy.setNode(v, value);\n    }\n  });\n\n  _.each(this._edgeObjs, function(e) {\n    if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n      copy.setEdge(e, self.edge(e));\n    }\n  });\n\n  var parents = {};\n  function findParent(v) {\n    var parent = self.parent(v);\n    if (parent === undefined || copy.hasNode(parent)) {\n      parents[v] = parent;\n      return parent;\n    } else if (parent in parents) {\n      return parents[parent];\n    } else {\n      return findParent(parent);\n    }\n  }\n\n  if (this._isCompound) {\n    _.each(copy.nodes(), function(v) {\n      copy.setParent(v, findParent(v));\n    });\n  }\n\n  return copy;\n};\n\n/* === Edge functions ========== */\n\nGraph.prototype.setDefaultEdgeLabel = function(newDefault) {\n  if (!_.isFunction(newDefault)) {\n    newDefault = _.constant(newDefault);\n  }\n  this._defaultEdgeLabelFn = newDefault;\n  return this;\n};\n\nGraph.prototype.edgeCount = function() {\n  return this._edgeCount;\n};\n\nGraph.prototype.edges = function() {\n  return _.values(this._edgeObjs);\n};\n\nGraph.prototype.setPath = function(vs, value) {\n  var self = this;\n  var args = arguments;\n  _.reduce(vs, function(v, w) {\n    if (args.length > 1) {\n      self.setEdge(v, w, value);\n    } else {\n      self.setEdge(v, w);\n    }\n    return w;\n  });\n  return this;\n};\n\n/*\n * setEdge(v, w, [value, [name]])\n * setEdge({ v, w, [name] }, [value])\n */\nGraph.prototype.setEdge = function() {\n  var v, w, name, value;\n  var valueSpecified = false;\n  var arg0 = arguments[0];\n\n  if (typeof arg0 === \"object\" && arg0 !== null && \"v\" in arg0) {\n    v = arg0.v;\n    w = arg0.w;\n    name = arg0.name;\n    if (arguments.length === 2) {\n      value = arguments[1];\n      valueSpecified = true;\n    }\n  } else {\n    v = arg0;\n    w = arguments[1];\n    name = arguments[3];\n    if (arguments.length > 2) {\n      value = arguments[2];\n      valueSpecified = true;\n    }\n  }\n\n  v = \"\" + v;\n  w = \"\" + w;\n  if (!_.isUndefined(name)) {\n    name = \"\" + name;\n  }\n\n  var e = edgeArgsToId(this._isDirected, v, w, name);\n  if (_.has(this._edgeLabels, e)) {\n    if (valueSpecified) {\n      this._edgeLabels[e] = value;\n    }\n    return this;\n  }\n\n  if (!_.isUndefined(name) && !this._isMultigraph) {\n    throw new Error(\"Cannot set a named edge when isMultigraph = false\");\n  }\n\n  // It didn't exist, so we need to create it.\n  // First ensure the nodes exist.\n  this.setNode(v);\n  this.setNode(w);\n\n  this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n  var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n  // Ensure we add undirected edges in a consistent way.\n  v = edgeObj.v;\n  w = edgeObj.w;\n\n  Object.freeze(edgeObj);\n  this._edgeObjs[e] = edgeObj;\n  incrementOrInitEntry(this._preds[w], v);\n  incrementOrInitEntry(this._sucs[v], w);\n  this._in[w][e] = edgeObj;\n  this._out[v][e] = edgeObj;\n  this._edgeCount++;\n  return this;\n};\n\nGraph.prototype.edge = function(v, w, name) {\n  var e = (arguments.length === 1\n    ? edgeObjToId(this._isDirected, arguments[0])\n    : edgeArgsToId(this._isDirected, v, w, name));\n  return this._edgeLabels[e];\n};\n\nGraph.prototype.hasEdge = function(v, w, name) {\n  var e = (arguments.length === 1\n    ? edgeObjToId(this._isDirected, arguments[0])\n    : edgeArgsToId(this._isDirected, v, w, name));\n  return _.has(this._edgeLabels, e);\n};\n\nGraph.prototype.removeEdge = function(v, w, name) {\n  var e = (arguments.length === 1\n    ? edgeObjToId(this._isDirected, arguments[0])\n    : edgeArgsToId(this._isDirected, v, w, name));\n  var edge = this._edgeObjs[e];\n  if (edge) {\n    v = edge.v;\n    w = edge.w;\n    delete this._edgeLabels[e];\n    delete this._edgeObjs[e];\n    decrementOrRemoveEntry(this._preds[w], v);\n    decrementOrRemoveEntry(this._sucs[v], w);\n    delete this._in[w][e];\n    delete this._out[v][e];\n    this._edgeCount--;\n  }\n  return this;\n};\n\nGraph.prototype.inEdges = function(v, u) {\n  var inV = this._in[v];\n  if (inV) {\n    var edges = _.values(inV);\n    if (!u) {\n      return edges;\n    }\n    return _.filter(edges, function(edge) { return edge.v === u; });\n  }\n};\n\nGraph.prototype.outEdges = function(v, w) {\n  var outV = this._out[v];\n  if (outV) {\n    var edges = _.values(outV);\n    if (!w) {\n      return edges;\n    }\n    return _.filter(edges, function(edge) { return edge.w === w; });\n  }\n};\n\nGraph.prototype.nodeEdges = function(v, w) {\n  var inEdges = this.inEdges(v, w);\n  if (inEdges) {\n    return inEdges.concat(this.outEdges(v, w));\n  }\n};\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) { delete map[k]; }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM +\n             (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj =  { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n", "module.exports = '2.1.8';\n", "// Includes only the \"core\" of graphlib\nmodule.exports = {\n  Graph: require(\"./graph\"),\n  version: require(\"./version\")\n};\n", "var _ = require(\"./lodash\");\nvar Graph = require(\"./graph\");\n\nmodule.exports = {\n  write: write,\n  read: read\n};\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound()\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g)\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function(v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function(e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function(entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function(entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = components;\n\nfunction components(g) {\n  var visited = {};\n  var cmpts = [];\n  var cmpt;\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    cmpt.push(v);\n    _.each(g.successors(v), dfs);\n    _.each(g.predecessors(v), dfs);\n  }\n\n  _.each(g.nodes(), function(v) {\n    cmpt = [];\n    dfs(v);\n    if (cmpt.length) {\n      cmpts.push(cmpt);\n    }\n  });\n\n  return cmpts;\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = PriorityQueue;\n\n/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nfunction PriorityQueue() {\n  this._arr = [];\n  this._keyIndices = {};\n}\n\n/**\n * Returns the number of elements in the queue. Takes `O(1)` time.\n */\nPriorityQueue.prototype.size = function() {\n  return this._arr.length;\n};\n\n/**\n * Returns the keys that are in the queue. Takes `O(n)` time.\n */\nPriorityQueue.prototype.keys = function() {\n  return this._arr.map(function(x) { return x.key; });\n};\n\n/**\n * Returns `true` if **key** is in the queue and `false` if not.\n */\nPriorityQueue.prototype.has = function(key) {\n  return _.has(this._keyIndices, key);\n};\n\n/**\n * Returns the priority for **key**. If **key** is not present in the queue\n * then this function returns `undefined`. Takes `O(1)` time.\n *\n * @param {Object} key\n */\nPriorityQueue.prototype.priority = function(key) {\n  var index = this._keyIndices[key];\n  if (index !== undefined) {\n    return this._arr[index].priority;\n  }\n};\n\n/**\n * Returns the key for the minimum element in this queue. If the queue is\n * empty this function throws an Error. Takes `O(1)` time.\n */\nPriorityQueue.prototype.min = function() {\n  if (this.size() === 0) {\n    throw new Error(\"Queue underflow\");\n  }\n  return this._arr[0].key;\n};\n\n/**\n * Inserts a new key into the priority queue. If the key already exists in\n * the queue this function returns `false`; otherwise it will return `true`.\n * Takes `O(n)` time.\n *\n * @param {Object} key the key to add\n * @param {Number} priority the initial priority for the key\n */\nPriorityQueue.prototype.add = function(key, priority) {\n  var keyIndices = this._keyIndices;\n  key = String(key);\n  if (!_.has(keyIndices, key)) {\n    var arr = this._arr;\n    var index = arr.length;\n    keyIndices[key] = index;\n    arr.push({key: key, priority: priority});\n    this._decrease(index);\n    return true;\n  }\n  return false;\n};\n\n/**\n * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n */\nPriorityQueue.prototype.removeMin = function() {\n  this._swap(0, this._arr.length - 1);\n  var min = this._arr.pop();\n  delete this._keyIndices[min.key];\n  this._heapify(0);\n  return min.key;\n};\n\n/**\n * Decreases the priority for **key** to **priority**. If the new priority is\n * greater than the previous priority, this function will throw an Error.\n *\n * @param {Object} key the key for which to raise priority\n * @param {Number} priority the new priority for the key\n */\nPriorityQueue.prototype.decrease = function(key, priority) {\n  var index = this._keyIndices[key];\n  if (priority > this._arr[index].priority) {\n    throw new Error(\"New priority is greater than current priority. \" +\n        \"Key: \" + key + \" Old: \" + this._arr[index].priority + \" New: \" + priority);\n  }\n  this._arr[index].priority = priority;\n  this._decrease(index);\n};\n\nPriorityQueue.prototype._heapify = function(i) {\n  var arr = this._arr;\n  var l = 2 * i;\n  var r = l + 1;\n  var largest = i;\n  if (l < arr.length) {\n    largest = arr[l].priority < arr[largest].priority ? l : largest;\n    if (r < arr.length) {\n      largest = arr[r].priority < arr[largest].priority ? r : largest;\n    }\n    if (largest !== i) {\n      this._swap(i, largest);\n      this._heapify(largest);\n    }\n  }\n};\n\nPriorityQueue.prototype._decrease = function(index) {\n  var arr = this._arr;\n  var priority = arr[index].priority;\n  var parent;\n  while (index !== 0) {\n    parent = index >> 1;\n    if (arr[parent].priority < priority) {\n      break;\n    }\n    this._swap(index, parent);\n    index = parent;\n  }\n};\n\nPriorityQueue.prototype._swap = function(i, j) {\n  var arr = this._arr;\n  var keyIndices = this._keyIndices;\n  var origArrI = arr[i];\n  var origArrJ = arr[j];\n  arr[i] = origArrJ;\n  arr[j] = origArrI;\n  keyIndices[origArrJ.key] = i;\n  keyIndices[origArrI.key] = j;\n};\n", "var _ = require(\"../lodash\");\nvar PriorityQueue = require(\"../data/priority-queue\");\n\nmodule.exports = dijkstra;\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(g, String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn || function(v) { return g.outEdges(v); });\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function(edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\"dijkstra does not allow negative edge weights. \" +\n                      \"Bad edge: \" + edge + \" Weight: \" + weight);\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function(v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n", "var dijkstra = require(\"./dijkstra\");\nvar _ = require(\"../lodash\");\n\nmodule.exports = dijkstraAll;\n\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(g.nodes(), function(acc, v) {\n    acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n  }, {});\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = tarjan;\n\nfunction tarjan(g) {\n  var index = 0;\n  var stack = [];\n  var visited = {}; // node id -> { onStack, lowlink, index }\n  var results = [];\n\n  function dfs(v) {\n    var entry = visited[v] = {\n      onStack: true,\n      lowlink: index,\n      index: index++\n    };\n    stack.push(v);\n\n    g.successors(v).forEach(function(w) {\n      if (!_.has(visited, w)) {\n        dfs(w);\n        entry.lowlink = Math.min(entry.lowlink, visited[w].lowlink);\n      } else if (visited[w].onStack) {\n        entry.lowlink = Math.min(entry.lowlink, visited[w].index);\n      }\n    });\n\n    if (entry.lowlink === entry.index) {\n      var cmpt = [];\n      var w;\n      do {\n        w = stack.pop();\n        visited[w].onStack = false;\n        cmpt.push(w);\n      } while (v !== w);\n      results.push(cmpt);\n    }\n  }\n\n  g.nodes().forEach(function(v) {\n    if (!_.has(visited, v)) {\n      dfs(v);\n    }\n  });\n\n  return results;\n}\n", "var _ = require(\"../lodash\");\nvar tarjan = require(\"./tarjan\");\n\nmodule.exports = findCycles;\n\nfunction findCycles(g) {\n  return _.filter(tarjan(g), function(cmpt) {\n    return cmpt.length > 1 || (cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]));\n  });\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = floyd<PERSON>arshall;\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn || function(v) { return g.outEdges(v); });\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function(v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function(w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function(edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function(k) {\n    var rowK = results[k];\n    nodes.forEach(function(i) {\n      var rowI = results[i];\n      nodes.forEach(function(j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = topsort;\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (_.has(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!_.has(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing", "var topsort = require(\"./topsort\");\n\nmodule.exports = isAcyclic;\n\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof topsort.CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = dfs;\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function(v) {\n    if (!g.hasNode(v)) {\n      throw new Error(\"Graph does not have node: \" + v);\n    }\n\n    doDfs(g, v, order === \"post\", visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!_.has(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) { acc.push(v); }\n    _.each(navigation(v), function(w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) { acc.push(v); }\n  }\n}\n", "var dfs = require(\"./dfs\");\n\nmodule.exports = postorder;\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, \"post\");\n}\n", "var dfs = require(\"./dfs\");\n\nmodule.exports = preorder;\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, \"pre\");\n}\n", "var _ = require(\"../lodash\");\nvar Graph = require(\"../graph\");\nvar PriorityQueue = require(\"../data/priority-queue\");\n\nmodule.exports = prim;\n\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n\n  _.each(g.nodes(), function(v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (_.has(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error(\"Input graph is not connected: \" + g);\n    } else {\n      init = true;\n    }\n\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n\n  return result;\n}\n", "module.exports = {\n  components: require(\"./components\"),\n  dijkstra: require(\"./dijkstra\"),\n  dijkstraAll: require(\"./dijkstra-all\"),\n  findCycles: require(\"./find-cycles\"),\n  floydWarshall: require(\"./floyd-warshall\"),\n  isAcyclic: require(\"./is-acyclic\"),\n  postorder: require(\"./postorder\"),\n  preorder: require(\"./preorder\"),\n  prim: require(\"./prim\"),\n  tarjan: require(\"./tarjan\"),\n  topsort: require(\"./topsort\")\n};\n", "/**\n * Copyright (c) 2014, <PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice, this\n * list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of the copyright holder nor the names of its contributors\n * may be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar lib = require(\"./lib\");\n\nmodule.exports = {\n  Graph: lib.Graph,\n  json: require(\"./lib/json\"),\n  alg: require(\"./lib/alg\"),\n  version: lib.version\n};\n", "/* global window */\n\nvar graphlib;\n\nif (typeof require === \"function\") {\n  try {\n    graphlib = require(\"graphlib\");\n  } catch (e) {\n    // continue regardless of error\n  }\n}\n\nif (!graphlib) {\n  graphlib = window.graphlib;\n}\n\nmodule.exports = graphlib;\n", "var baseClone = require('./_baseClone');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);\n}\n\nmodule.exports = cloneDeep;\n", "var baseRest = require('./_baseRest'),\n    eq = require('./eq'),\n    isIterateeCall = require('./_isIterateeCall'),\n    keysIn = require('./keysIn');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns own and inherited enumerable string keyed properties of source\n * objects to the destination object for all destination properties that\n * resolve to `undefined`. Source objects are applied from left to right.\n * Once a property is set, additional values of the same property are ignored.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaultsDeep\n * @example\n *\n * _.defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar defaults = baseRest(function(object, sources) {\n  object = Object(object);\n\n  var index = -1;\n  var length = sources.length;\n  var guard = length > 2 ? sources[2] : undefined;\n\n  if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n    length = 1;\n  }\n\n  while (++index < length) {\n    var source = sources[index];\n    var props = keysIn(source);\n    var propsIndex = -1;\n    var propsLength = props.length;\n\n    while (++propsIndex < propsLength) {\n      var key = props[propsIndex];\n      var value = object[key];\n\n      if (value === undefined ||\n          (eq(value, objectProto[key]) && !hasOwnProperty.call(object, key))) {\n        object[key] = source[key];\n      }\n    }\n  }\n\n  return object;\n});\n\nmodule.exports = defaults;\n", "var baseFor = require('./_baseFor'),\n    castFunction = require('./_castFunction'),\n    keysIn = require('./keysIn');\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null\n    ? object\n    : baseFor(object, castFunction(iteratee), keysIn);\n}\n\nmodule.exports = forIn;\n", "var baseAssignValue = require('./_baseAssignValue'),\n    eq = require('./eq');\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if ((value !== undefined && !eq(object[key], value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nmodule.exports = assignMergeValue;\n", "/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n\n  if (key == '__proto__') {\n    return;\n  }\n\n  return object[key];\n}\n\nmodule.exports = safeGet;\n", "var copyObject = require('./_copyObject'),\n    keysIn = require('./keysIn');\n\n/**\n * Converts `value` to a plain object flattening inherited enumerable string\n * keyed properties of `value` to own properties of the plain object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Object} Returns the converted plain object.\n * @example\n *\n * function Foo() {\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.assign({ 'a': 1 }, new Foo);\n * // => { 'a': 1, 'b': 2 }\n *\n * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));\n * // => { 'a': 1, 'b': 2, 'c': 3 }\n */\nfunction toPlainObject(value) {\n  return copyObject(value, keysIn(value));\n}\n\nmodule.exports = toPlainObject;\n", "var assignMergeValue = require('./_assignMergeValue'),\n    cloneBuffer = require('./_cloneBuffer'),\n    cloneTypedArray = require('./_cloneTypedArray'),\n    copyArray = require('./_copyArray'),\n    initCloneObject = require('./_initCloneObject'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray'),\n    isArrayLikeObject = require('./isArrayLikeObject'),\n    isBuffer = require('./isBuffer'),\n    isFunction = require('./isFunction'),\n    isObject = require('./isObject'),\n    isPlainObject = require('./isPlainObject'),\n    isTypedArray = require('./isTypedArray'),\n    safeGet = require('./_safeGet'),\n    toPlainObject = require('./toPlainObject');\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n      srcValue = safeGet(source, key),\n      stacked = stack.get(srcValue);\n\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer\n    ? customizer(objValue, srcValue, (key + ''), object, source, stack)\n    : undefined;\n\n  var isCommon = newValue === undefined;\n\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n        isBuff = !isArr && isBuffer(srcValue),\n        isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      }\n      else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      }\n      else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      }\n      else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      }\n      else {\n        newValue = [];\n      }\n    }\n    else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      }\n      else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    }\n    else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\n\nmodule.exports = baseMergeDeep;\n", "var Stack = require('./_Stack'),\n    assignMergeValue = require('./_assignMergeValue'),\n    baseFor = require('./_baseFor'),\n    baseMergeDeep = require('./_baseMergeDeep'),\n    isObject = require('./isObject'),\n    keysIn = require('./keysIn'),\n    safeGet = require('./_safeGet');\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function(srcValue, key) {\n    stack || (stack = new Stack);\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    }\n    else {\n      var newValue = customizer\n        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)\n        : undefined;\n\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\n\nmodule.exports = baseMerge;\n", "var baseRest = require('./_baseRest'),\n    isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\nmodule.exports = createAssigner;\n", "var baseMerge = require('./_baseMerge'),\n    createAssigner = require('./_createAssigner');\n\n/**\n * This method is like `_.assign` except that it recursively merges own and\n * inherited enumerable string keyed properties of source objects into the\n * destination object. Source properties that resolve to `undefined` are\n * skipped if a destination value exists. Array and plain object properties\n * are merged recursively. Other objects and value types are overridden by\n * assignment. Source objects are applied from left to right. Subsequent\n * sources overwrite property assignments of previous sources.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 0.5.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {\n *   'a': [{ 'b': 2 }, { 'd': 4 }]\n * };\n *\n * var other = {\n *   'a': [{ 'c': 3 }, { 'e': 5 }]\n * };\n *\n * _.merge(object, other);\n * // => { 'a': [{ 'b': 2, 'c': 3 }, { 'd': 4, 'e': 5 }] }\n */\nvar merge = createAssigner(function(object, source, srcIndex) {\n  baseMerge(object, source, srcIndex);\n});\n\nmodule.exports = merge;\n", "var assignValue = require('./_assignValue'),\n    castPath = require('./_castPath'),\n    isIndex = require('./_isIndex'),\n    isObject = require('./isObject'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nmodule.exports = baseSet;\n", "var baseGet = require('./_baseGet'),\n    baseSet = require('./_baseSet'),\n    castPath = require('./_castPath');\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = basePickBy;\n", "var basePickBy = require('./_basePickBy'),\n    hasIn = require('./hasIn');\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nmodule.exports = basePick;\n", "var basePick = require('./_basePick'),\n    flatRest = require('./_flatRest');\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nmodule.exports = pick;\n", "var toString = require('./toString');\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nmodule.exports = uniqueId;\n", "/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n      length = props.length,\n      valsLength = values.length,\n      result = {};\n\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\n\nmodule.exports = baseZipObject;\n", "var assignValue = require('./_assignValue'),\n    baseZipObject = require('./_baseZipObject');\n\n/**\n * This method is like `_.fromPairs` except that it accepts two arrays,\n * one of property identifiers and one of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 0.4.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObject(['a', 'b'], [1, 2]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction zipObject(props, values) {\n  return baseZipObject(props || [], values || [], assignValue);\n}\n\nmodule.exports = zipObject;\n", "/* global window */\n\nvar lodash;\n\nif (typeof require === \"function\") {\n  try {\n    lodash = {\n      cloneDeep: require(\"lodash/cloneDeep\"),\n      constant: require(\"lodash/constant\"),\n      defaults: require(\"lodash/defaults\"),\n      each: require(\"lodash/each\"),\n      filter: require(\"lodash/filter\"),\n      find: require(\"lodash/find\"),\n      flatten: require(\"lodash/flatten\"),\n      forEach: require(\"lodash/forEach\"),\n      forIn: require(\"lodash/forIn\"),\n      has:  require(\"lodash/has\"),\n      isUndefined: require(\"lodash/isUndefined\"),\n      last: require(\"lodash/last\"),\n      map: require(\"lodash/map\"),\n      mapValues: require(\"lodash/mapValues\"),\n      max: require(\"lodash/max\"),\n      merge: require(\"lodash/merge\"),\n      min: require(\"lodash/min\"),\n      minBy: require(\"lodash/minBy\"),\n      now: require(\"lodash/now\"),\n      pick: require(\"lodash/pick\"),\n      range: require(\"lodash/range\"),\n      reduce: require(\"lodash/reduce\"),\n      sortBy: require(\"lodash/sortBy\"),\n      uniqueId: require(\"lodash/uniqueId\"),\n      values: require(\"lodash/values\"),\n      zipObject: require(\"lodash/zipObject\"),\n    };\n  } catch (e) {\n    // continue regardless of error\n  }\n}\n\nif (!lodash) {\n  lodash = window._;\n}\n\nmodule.exports = lodash;\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nmodule.exports = List;\n\nfunction List() {\n  var sentinel = {};\n  sentinel._next = sentinel._prev = sentinel;\n  this._sentinel = sentinel;\n}\n\nList.prototype.dequeue = function() {\n  var sentinel = this._sentinel;\n  var entry = sentinel._prev;\n  if (entry !== sentinel) {\n    unlink(entry);\n    return entry;\n  }\n};\n\nList.prototype.enqueue = function(entry) {\n  var sentinel = this._sentinel;\n  if (entry._prev && entry._next) {\n    unlink(entry);\n  }\n  entry._next = sentinel._next;\n  sentinel._next._prev = entry;\n  sentinel._next = entry;\n  entry._prev = sentinel;\n};\n\nList.prototype.toString = function() {\n  var strs = [];\n  var sentinel = this._sentinel;\n  var curr = sentinel._prev;\n  while (curr !== sentinel) {\n    strs.push(JSON.stringify(curr, filterOutLinks));\n    curr = curr._prev;\n  }\n  return \"[\" + strs.join(\", \") + \"]\";\n};\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== \"_next\" && k !== \"_prev\") {\n    return v;\n  }\n}\n", "var _ = require(\"./lodash\");\nvar Graph = require(\"./graphlib\").Graph;\nvar List = require(\"./data/list\");\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nmodule.exports = greedyFAS;\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(_.map(results, function(e) {\n    return g.outEdges(e.v, e.w);\n  }), true);\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue()))   { removeNode(g, buckets, zeroIdx, entry); }\n    while ((entry = sources.dequeue())) { removeNode(g, buckets, zeroIdx, entry); }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function(edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function(edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry[\"in\"] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function(v) {\n    fasGraph.setNode(v, { v: v, \"in\": 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function(e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, fasGraph.node(e.v).out += weight);\n    maxIn  = Math.max(maxIn,  fasGraph.node(e.w)[\"in\"]  += weight);\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function() { return new List(); });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function(v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry[\"in\"]) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry[\"in\"] + zeroIdx].enqueue(entry);\n  }\n}\n", "\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar greedyFAS = require(\"./greedy-fas\");\n\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\nfunction run(g) {\n  var fas = (g.graph().acyclicer === \"greedy\"\n    ? greedyFAS(g, weightFn(g))\n    : dfsFAS(g));\n  _.forEach(fas, function(e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId(\"rev\"));\n  });\n\n  function weightFn(g) {\n    return function(e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (_.has(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function(e) {\n      if (_.has(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function(e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "/* eslint \"no-console\": off */\n\n\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar Graph = require(\"./graphlib\").Graph;\n\nmodule.exports = {\n  addDummyNode: addDummyNode,\n  simplify: simplify,\n  asNonCompoundGraph: asNonCompoundGraph,\n  successorWeights: successorWeights,\n  predecessorWeights: predecessorWeights,\n  intersectRect: intersectRect,\n  buildLayerMatrix: buildLayerMatrix,\n  normalizeRanks: normalizeRanks,\n  removeEmptyRanks: removeEmptyRanks,\n  addBorderNode: addBorderNode,\n  maxRank: maxRank,\n  partition: partition,\n  time: time,\n  notime: notime\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function(v) { simplified.setNode(v, g.node(v)); });\n  _.forEach(g.edges(), function(e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen)\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function(v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function(e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function(v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function(e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function(v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function(e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error(\"Not possible to find intersection inside of the rectangle\");\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = h * dx / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = w * dy / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function() { return []; });\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(_.map(g.nodes(), function(v) { return g.node(v).rank; }));\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (_.has(node, \"rank\")) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(_.map(g.nodes(), function(v) { return g.node(v).rank; }));\n\n  var layers = [];\n  _.forEach(g.nodes(), function(v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function(vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function(v) { g.node(v).rank += delta; });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, \"border\", node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(_.map(g.nodes(), function(v) {\n    var rank = g.node(v).rank;\n    if (!_.isUndefined(rank)) {\n      return rank;\n    }\n  }));\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function(value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + \" time: \" + (_.now() - start) + \"ms\");\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar util = require(\"./util\");\n\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function(edge) { normalizeEdge(g, edge); });\n}\n\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  var dummy, attrs, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0, height: 0,\n      edgeLabel: edgeLabel, edgeObj: e,\n      rank: vRank\n    };\n    dummy = util.addDummyNode(g, \"edge\", attrs, \"_d\");\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = \"edge-label\";\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function(v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === \"edge-label\") {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = {\n  longestPath: longestPath,\n  slack: slack\n};\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (_.has(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(_.map(g.outEdges(v), function(e) {\n      return dfs(e.w) - g.edge(e).minlen;\n    }));\n\n    if (rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n        rank === undefined || // return value of _.map([]) for Lodash 4\n        rank === null) { // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar Graph = require(\"../graphlib\").Graph;\nvar slack = require(\"./util\").slack;\n\nmodule.exports = feasibleTree;\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function(e) {\n      var edgeV = e.v,\n        w = (v === edgeV) ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function(e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function(v) {\n    g.node(v).rank += delta;\n  });\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar feasibleTree = require(\"./feasible-tree\");\nvar slack = require(\"./util\").slack;\nvar initRank = require(\"./util\").longestPath;\nvar preorder = require(\"../graphlib\").alg.preorder;\nvar postorder = require(\"../graphlib\").alg.postorder;\nvar simplify = require(\"../util\").simplify;\n\nmodule.exports = networkSimplex;\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  initRank(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function(v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function(e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function(w) {\n    if (!_.has(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function(e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function(edge) {\n    return flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n           flip !== isDescendant(t, t.node(edge.w), tailLabel);\n  });\n\n  return _.minBy(candidates, function(edge) { return slack(g, edge); });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function(v) { return !g.node(v).parent; });\n  var vs = preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function(v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "\"use strict\";\n\nvar rankUtil = require(\"./util\");\nvar longestPath = rankUtil.longestPath;\nvar feasibleTree = require(\"./feasible-tree\");\nvar networkSimplex = require(\"./network-simplex\");\n\nmodule.exports = rank;\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch(g.graph().ranker) {\n  case \"network-simplex\": networkSimplexRanker(g); break;\n  case \"tight-tree\": tightTreeRanker(g); break;\n  case \"longest-path\": longestPathRanker(g); break;\n  default: networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "var _ = require(\"./lodash\");\n\nmodule.exports = parentDummyChains;\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function(v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca &&\n               g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (pathIdx < path.length - 1 &&\n               g.node(pathV = path[pathIdx + 1]).minRank <= node.rank) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent &&\n           (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "var _ = require(\"./lodash\");\nvar util = require(\"./util\");\n\nmodule.exports = {\n  run: run,\n  cleanup: cleanup\n};\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, \"root\", {}, \"_root\");\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function(e) { g.edge(e).minlen *= nodeSep; });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function(child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, \"_bt\");\n  var bottom = util.addBorderNode(g, \"_bb\");\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function(child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function(child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function(v) { dfs(v, 1); });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(g.edges(), function(acc, e) {\n    return acc + g.edge(e).weight;\n  }, 0);\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "var _ = require(\"./lodash\");\nvar util = require(\"./util\");\n\nmodule.exports = addBorderSegments;\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (_.has(node, \"minRank\")) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1;\n        rank < maxRank;\n        ++rank) {\n        addBorderNode(g, \"borderLeft\", \"_bl\", v, node, rank);\n        addBorderNode(g, \"borderRight\", \"_br\", v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, \"border\", label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "\"use strict\";\n\nvar _ = require(\"./lodash\");\n\nmodule.exports = {\n  adjust: adjust,\n  undo: undo\n};\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"bt\" || rankDir === \"rl\") {\n    reverseY(g);\n  }\n\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function(v) { swapWidthHeightOne(g.node(v)); });\n  _.forEach(g.edges(), function(e) { swapWidthHeightOne(g.edge(e)); });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function(v) { reverseYOne(g.node(v)); });\n\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (_.has(edge, \"y\")) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function(v) { swapXYOne(g.node(v)); });\n\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (_.has(edge, \"x\")) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = initOrder;\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nfunction initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function(v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(_.map(simpleNodes, function(v) { return g.node(v).rank; }));\n  var layers = _.map(_.range(maxRank + 1), function() { return []; });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function(v) { return g.node(v).rank; });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = crossCount;\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i-1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(southLayer,\n    _.map(southLayer, function (v, i) { return i; }));\n  var southEntries = _.flatten(_.map(northLayer, function(v) {\n    return _.sortBy(_.map(g.outEdges(v), function(e) {\n      return { pos: southPos[e.w], weight: g.edge(e).weight };\n    }), \"pos\");\n  }), true);\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function() { return 0; });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(southEntries.forEach(function(entry) {\n    var index = entry.pos + firstIndex;\n    tree[index] += entry.weight;\n    var weightSum = 0;\n    while (index > 0) {\n      if (index % 2) {\n        weightSum += tree[index + 1];\n      }\n      index = (index - 1) >> 1;\n      tree[index] += entry.weight;\n    }\n    cc += entry.weight * weightSum;\n  }));\n\n  return cc;\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = barycenter;\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function(v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(inV, function(acc, e) {\n        var edge = g.edge(e),\n          nodeU = g.node(e.v);\n        return {\n          sum: acc.sum + (edge.weight * nodeU.order),\n          weight: acc.weight + edge.weight\n        };\n      }, { sum: 0, weight: 0 });\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight\n      };\n    }\n  });\n}\n\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = resolveConflicts;\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function(entry, i) {\n    var tmp = mappedEntries[entry.v] = {\n      indegree: 0,\n      \"in\": [],\n      out: [],\n      vs: [entry.v],\n      i: i\n    };\n    if (!_.isUndefined(entry.barycenter)) {\n      tmp.barycenter = entry.barycenter;\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function(e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function(entry) {\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function(uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (_.isUndefined(uEntry.barycenter) ||\n          _.isUndefined(vEntry.barycenter) ||\n          uEntry.barycenter >= vEntry.barycenter) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function(wEntry) {\n      wEntry[\"in\"].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry[\"in\"].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(_.filter(entries, function(entry) { return !entry.merged; }),\n    function(entry) {\n      return _.pick(entry, [\"vs\", \"i\", \"barycenter\", \"weight\"]);\n    });\n\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "var _ = require(\"../lodash\");\nvar util = require(\"../util\");\n\nmodule.exports = sort;\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function(entry) {\n    return _.has(entry, \"barycenter\");\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function(entry) { return -entry.i; }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs, true) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function(entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "var _ = require(\"../lodash\");\nvar barycenter = require(\"./barycenter\");\nvar resolveConflicts = require(\"./resolve-conflicts\");\nvar sort = require(\"./sort\");\n\nmodule.exports = sortSubgraph;\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight: undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function(w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function(entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (_.has(subgraphResult, \"barycenter\")) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br], true);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!_.has(result, \"barycenter\")) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter = (result.barycenter * result.weight +\n                           blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function(entry) {\n    entry.vs = _.flatten(entry.vs.map(function(v) {\n      if (subgraphs[v]) {\n        return subgraphs[v].vs;\n      }\n      return v;\n    }), true);\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter = (target.barycenter * target.weight +\n                         other.barycenter * other.weight) /\n                        (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "var _ = require(\"../lodash\");\nvar Graph = require(\"../graphlib\").Graph;\n\nmodule.exports = buildLayerGraph;\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true }).setGraph({ root: root })\n      .setDefaultNodeLabel(function(v) { return g.node(v); });\n\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || node.minRank <= rank && rank <= node.maxRank) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function(e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (_.has(node, \"minRank\")) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank]\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId(\"_root\"))));\n  return v;\n}\n", "var _ = require(\"../lodash\");\n\nmodule.exports = addSubgraphConstraints;\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function(v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar initOrder = require(\"./init-order\");\nvar crossCount = require(\"./cross-count\");\nvar sortSubgraph = require(\"./sort-subgraph\");\nvar buildLayerGraph = require(\"./build-layer-graph\");\nvar addSubgraphConstraints = require(\"./add-subgraph-constraints\");\nvar Graph = require(\"../graphlib\").Graph;\nvar util = require(\"../util\");\n\nmodule.exports = order;\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), \"inEdges\"),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), \"outEdges\");\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function(rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function(lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function(v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function(layer) {\n    _.forEach(layer, function(v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar Graph = require(\"../graphlib\").Graph;\nvar util = require(\"../util\");\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nmodule.exports = {\n  positionX: positionX,\n  findType1Conflicts: findType1Conflicts,\n  findType2Conflicts: findType2Conflicts,\n  addConflict: addConflict,\n  hasConflict: hasConflict,\n  verticalAlignment: verticalAlignment,\n  horizontalCompaction: horizontalCompaction,\n  alignCoordinates: alignCoordinates,\n  findSmallestWidthAlignment: findSmallestWidthAlignment,\n  balance: balance\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var\n      // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function(v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i +1), function(scanNode) {\n          _.forEach(g.predecessors(scanNode), function(u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) &&\n                !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function(i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function(u) {\n          var uNode = g.node(u);\n          if (uNode.dummy &&\n              (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function(v, southLookahead) {\n      if (g.node(v).dummy === \"border\") {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function(u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return _.has(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function(layer) {\n    _.forEach(layer, function(v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function(layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function(v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function(w) { return pos[w]; });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v &&\n              prevIdx < pos[w] &&\n              !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? \"borderLeft\" : \"borderRight\";\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function(acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function(acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function(v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function(layer) {\n    var u;\n    _.forEach(layer, function(v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach([\"u\", \"d\"], function(vert) {\n    _.forEach([\"l\", \"r\"], function(horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === \"l\" ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function(x) { return x + delta; });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function(ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(\n    findType1Conflicts(g, layering),\n    findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach([\"u\", \"d\"], function(vert) {\n    adjustedLayering = vert === \"u\" ? layering : _.values(layering).reverse();\n    _.forEach([\"l\", \"r\"], function(horiz) {\n      if (horiz === \"r\") {\n        adjustedLayering = _.map(adjustedLayering, function(inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === \"u\" ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering,\n        align.root, align.align, horiz === \"r\");\n      if (horiz === \"r\") {\n        xs = _.mapValues(xs, function(x) { return -x; });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function(g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (_.has(vLabel, \"labelpos\")) {\n      switch (vLabel.labelpos.toLowerCase()) {\n      case \"l\": delta = -vLabel.width / 2; break;\n      case \"r\": delta = vLabel.width / 2; break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (_.has(wLabel, \"labelpos\")) {\n      switch (wLabel.labelpos.toLowerCase()) {\n      case \"l\": delta = wLabel.width / 2; break;\n      case \"r\": delta = -wLabel.width / 2; break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar util = require(\"../util\");\nvar positionX = require(\"./bk\").positionX;\n\nmodule.exports = position;\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forEach(positionX(g), function(x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function(layer) {\n    var maxHeight = _.max(_.map(layer, function(v) { return g.node(v).height; }));\n    _.forEach(layer, function(v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n\n", "\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar acyclic = require(\"./acyclic\");\nvar normalize = require(\"./normalize\");\nvar rank = require(\"./rank\");\nvar normalizeRanks = require(\"./util\").normalizeRanks;\nvar parentDummyChains = require(\"./parent-dummy-chains\");\nvar removeEmptyRanks = require(\"./util\").removeEmptyRanks;\nvar nestingGraph = require(\"./nesting-graph\");\nvar addBorderSegments = require(\"./add-border-segments\");\nvar coordinateSystem = require(\"./coordinate-system\");\nvar order = require(\"./order\");\nvar position = require(\"./position\");\nvar util = require(\"./util\");\nvar Graph = require(\"./graphlib\").Graph;\n\nmodule.exports = layout;\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time(\"layout\", function() {\n    var layoutGraph = \n      time(\"  buildLayoutGraph\", function() { return buildLayoutGraph(g); });\n    time(\"  runLayout\",        function() { runLayout(layoutGraph, time); });\n    time(\"  updateInputGraph\", function() { updateInputGraph(g, layoutGraph); });\n  });\n}\n\nfunction runLayout(g, time) {\n  time(\"    makeSpaceForEdgeLabels\", function() { makeSpaceForEdgeLabels(g); });\n  time(\"    removeSelfEdges\",        function() { removeSelfEdges(g); });\n  time(\"    acyclic\",                function() { acyclic.run(g); });\n  time(\"    nestingGraph.run\",       function() { nestingGraph.run(g); });\n  time(\"    rank\",                   function() { rank(util.asNonCompoundGraph(g)); });\n  time(\"    injectEdgeLabelProxies\", function() { injectEdgeLabelProxies(g); });\n  time(\"    removeEmptyRanks\",       function() { removeEmptyRanks(g); });\n  time(\"    nestingGraph.cleanup\",   function() { nestingGraph.cleanup(g); });\n  time(\"    normalizeRanks\",         function() { normalizeRanks(g); });\n  time(\"    assignRankMinMax\",       function() { assignRankMinMax(g); });\n  time(\"    removeEdgeLabelProxies\", function() { removeEdgeLabelProxies(g); });\n  time(\"    normalize.run\",          function() { normalize.run(g); });\n  time(\"    parentDummyChains\",      function() { parentDummyChains(g); });\n  time(\"    addBorderSegments\",      function() { addBorderSegments(g); });\n  time(\"    order\",                  function() { order(g); });\n  time(\"    insertSelfEdges\",        function() { insertSelfEdges(g); });\n  time(\"    adjustCoordinateSystem\", function() { coordinateSystem.adjust(g); });\n  time(\"    position\",               function() { position(g); });\n  time(\"    positionSelfEdges\",      function() { positionSelfEdges(g); });\n  time(\"    removeBorderNodes\",      function() { removeBorderNodes(g); });\n  time(\"    normalize.undo\",         function() { normalize.undo(g); });\n  time(\"    fixupEdgeLabelCoords\",   function() { fixupEdgeLabelCoords(g); });\n  time(\"    undoCoordinateSystem\",   function() { coordinateSystem.undo(g); });\n  time(\"    translateGraph\",         function() { translateGraph(g); });\n  time(\"    assignNodeIntersects\",   function() { assignNodeIntersects(g); });\n  time(\"    reversePoints\",          function() { reversePointsForReversedEdges(g); });\n  time(\"    acyclic.undo\",           function() { acyclic.undo(g); });\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function(v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function(e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (_.has(layoutLabel, \"x\")) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = [\"nodesep\", \"edgesep\", \"ranksep\", \"marginx\", \"marginy\"];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: \"tb\" };\nvar graphAttrs = [\"acyclicer\", \"ranker\", \"rankdir\", \"align\"];\nvar nodeNumAttrs = [\"width\", \"height\"];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = [\"minlen\", \"weight\", \"width\", \"height\", \"labeloffset\"];\nvar edgeDefaults = {\n  minlen: 1, weight: 1, width: 0, height: 0,\n  labeloffset: 10, labelpos: \"r\"\n};\nvar edgeAttrs = [\"labelpos\"];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(_.merge({},\n    graphDefaults,\n    selectNumberAttrs(graph, graphNumAttrs),\n    _.pick(graph, graphAttrs)));\n\n  _.forEach(inputGraph.nodes(), function(v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function(e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(e, _.merge({},\n      edgeDefaults,\n      selectNumberAttrs(edge, edgeNumAttrs),\n      _.pick(edge, edgeAttrs)));\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== \"c\") {\n      if (graph.rankdir === \"TB\" || graph.rankdir === \"BT\") {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, \"edge-proxy\", label, \"_ep\");\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (node.dummy === \"edge-proxy\") {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function(v) { getExtremes(g.node(v)); });\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (_.has(edge, \"x\")) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function(p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (_.has(edge, \"x\")) { edge.x -= minX; }\n    if (_.has(edge, \"y\")) { edge.y -= minY; }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (_.has(edge, \"x\")) {\n      if (edge.labelpos === \"l\" || edge.labelpos === \"r\") {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n      case \"l\": edge.x -= edge.width / 2 + edge.labeloffset; break;\n      case \"r\": edge.x += edge.width / 2 + edge.labeloffset; break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function(v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function(v) {\n    if (g.node(v).dummy === \"border\") {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function(e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function(layer) {\n    var orderShift = 0;\n    _.forEach(layer, function(v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function(selfEdge) {\n        util.addDummyNode(g, \"selfedge\", {\n          width: selfEdge.label.width,\n          height: selfEdge.label.height,\n          rank: node.rank,\n          order: i + (++orderShift),\n          e: selfEdge.e,\n          label: selfEdge.label\n        }, \"_se\");\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (node.dummy === \"selfedge\") {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + 2 * dx / 3, y: y - dy },\n        { x: x + 5 * dx / 6, y: y - dy },\n        { x: x +     dx    , y: y },\n        { x: x + 5 * dx / 6, y: y + dy },\n        { x: x + 2 * dx / 3, y: y + dy }\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function(v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n", "var _ = require(\"./lodash\");\nvar util = require(\"./util\");\nvar Graph = require(\"./graphlib\").Graph;\n\nmodule.exports = {\n  debugOrdering: debugOrdering\n};\n\n/* istanbul ignore next */\nfunction debugOrdering(g) {\n  var layerMatrix = util.buildLayerMatrix(g);\n\n  var h = new Graph({ compound: true, multigraph: true }).setGraph({});\n\n  _.forEach(g.nodes(), function(v) {\n    h.setNode(v, { label: v });\n    h.setParent(v, \"layer\" + g.node(v).rank);\n  });\n\n  _.forEach(g.edges(), function(e) {\n    h.setEdge(e.v, e.w, {}, e.name);\n  });\n\n  _.forEach(layerMatrix, function(layer, i) {\n    var layerV = \"layer\" + i;\n    h.setNode(layerV, { rank: \"same\" });\n    _.reduce(layer, function(u, v) {\n      h.setEdge(u, v, { style: \"invis\" });\n      return v;\n    });\n  });\n\n  return h;\n}\n", "module.exports = \"0.8.5\";\n", "/*\nCopyright (c) 2012-2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nmodule.exports = {\n  graphlib: require(\"./lib/graphlib\"),\n\n  layout: require(\"./lib/layout\"),\n  debug: require(\"./lib/debug\"),\n  util: {\n    time: require(\"./lib/util\").time,\n    notime: require(\"./lib/util\").notime\n  },\n  version: require(\"./lib/version\")\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAGhB,QAAI,qBAAqB;AA4BzB,aAAS,MAAM,OAAO;AACpB,aAAO,UAAU,OAAO,kBAAkB;AAAA,IAC5C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnCjB;AAAA;AAAA,QAAI,WAAW;AASf,aAAS,aAAa,OAAO;AAC3B,aAAO,OAAO,SAAS,aAAa,QAAQ;AAAA,IAC9C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,WAAW;AADf,QAEI,eAAe;AAFnB,QAGI,UAAU;AAgCd,aAAS,QAAQ,YAAY,UAAU;AACrC,UAAI,OAAO,QAAQ,UAAU,IAAI,YAAY;AAC7C,aAAO,KAAK,YAAY,aAAa,QAAQ,CAAC;AAAA,IAChD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxCjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA,QAAI,WAAW;AAUf,aAAS,WAAW,YAAY,WAAW;AACzC,UAAI,SAAS,CAAC;AACd,eAAS,YAAY,SAAS,OAAO,OAAOA,aAAY;AACtD,YAAI,UAAU,OAAO,OAAOA,WAAU,GAAG;AACvC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,aAAa;AADjB,QAEI,eAAe;AAFnB,QAGI,UAAU;AA2Cd,aAAS,OAAO,YAAY,WAAW;AACrC,UAAI,OAAO,QAAQ,UAAU,IAAI,cAAc;AAC/C,aAAO,KAAK,YAAY,aAAa,WAAW,CAAC,CAAC;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnDjB;AAAA;AACA,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAUjC,aAAS,QAAQ,QAAQ,KAAK;AAC5B,aAAO,UAAU,QAAQ,eAAe,KAAK,QAAQ,GAAG;AAAA,IAC1D;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,UAAU;AA6Bd,aAAS,IAAI,QAAQ,MAAM;AACzB,aAAO,UAAU,QAAQ,QAAQ,QAAQ,MAAM,OAAO;AAAA,IACxD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,SAAS;AADb,QAEI,cAAc;AAFlB,QAGI,UAAU;AAHd,QAII,cAAc;AAJlB,QAKI,WAAW;AALf,QAMI,cAAc;AANlB,QAOI,eAAe;AAGnB,QAAI,SAAS;AAAb,QACI,SAAS;AAGb,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAmCjC,aAAS,QAAQ,OAAO;AACtB,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAK,MAChB,QAAQ,KAAK,KAAK,OAAO,SAAS,YAAY,OAAO,MAAM,UAAU,cACpE,SAAS,KAAK,KAAK,aAAa,KAAK,KAAK,YAAY,KAAK,IAAI;AACnE,eAAO,CAAC,MAAM;AAAA,MAChB;AACA,UAAI,MAAM,OAAO,KAAK;AACtB,UAAI,OAAO,UAAU,OAAO,QAAQ;AAClC,eAAO,CAAC,MAAM;AAAA,MAChB;AACA,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,CAAC,SAAS,KAAK,EAAE;AAAA,MAC1B;AACA,eAAS,OAAO,OAAO;AACrB,YAAI,eAAe,KAAK,OAAO,GAAG,GAAG;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5EjB;AAAA;AAiBA,aAAS,YAAY,OAAO;AAC1B,aAAO,UAAU;AAAA,IACnB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAYA,aAAS,YAAY,OAAO,UAAU,aAAa,WAAW;AAC5D,UAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM;AAEvC,UAAI,aAAa,QAAQ;AACvB,sBAAc,MAAM,EAAE,KAAK;AAAA,MAC7B;AACA,aAAO,EAAE,QAAQ,QAAQ;AACvB,sBAAc,SAAS,aAAa,MAAM,KAAK,GAAG,OAAO,KAAK;AAAA,MAChE;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAaA,aAAS,WAAW,YAAY,UAAU,aAAa,WAAW,UAAU;AAC1E,eAAS,YAAY,SAAS,OAAO,OAAOC,aAAY;AACtD,sBAAc,aACT,YAAY,OAAO,SACpB,SAAS,aAAa,OAAO,OAAOA,WAAU;AAAA,MACpD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,eAAe;AAFnB,QAGI,aAAa;AAHjB,QAII,UAAU;AAuCd,aAAS,OAAO,YAAY,UAAU,aAAa;AACjD,UAAI,OAAO,QAAQ,UAAU,IAAI,cAAc,YAC3C,YAAY,UAAU,SAAS;AAEnC,aAAO,KAAK,YAAY,aAAa,UAAU,CAAC,GAAG,aAAa,WAAW,QAAQ;AAAA,IACrF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClDjB;AAAA;AAAA,QAAI,eAAe;AASnB,QAAI,YAAY,aAAa,QAAQ;AAErC,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AACA,QAAI,gBAAgB;AAApB,QACI,oBAAoB;AADxB,QAEI,wBAAwB;AAF5B,QAGI,sBAAsB;AAH1B,QAII,eAAe,oBAAoB,wBAAwB;AAJ/D,QAKI,aAAa;AAGjB,QAAI,WAAW,MAAM,gBAAgB;AAArC,QACI,UAAU,MAAM,eAAe;AADnC,QAEI,SAAS;AAFb,QAGI,aAAa,QAAQ,UAAU,MAAM,SAAS;AAHlD,QAII,cAAc,OAAO,gBAAgB;AAJzC,QAKI,aAAa;AALjB,QAMI,aAAa;AANjB,QAOI,QAAQ;AAGZ,QAAI,WAAW,aAAa;AAA5B,QACI,WAAW,MAAM,aAAa;AADlC,QAEI,YAAY,QAAQ,QAAQ,QAAQ,CAAC,aAAa,YAAY,UAAU,EAAE,KAAK,GAAG,IAAI,MAAM,WAAW,WAAW;AAFtH,QAGI,QAAQ,WAAW,WAAW;AAHlC,QAII,WAAW,QAAQ,CAAC,cAAc,UAAU,KAAK,SAAS,YAAY,YAAY,QAAQ,EAAE,KAAK,GAAG,IAAI;AAG5G,QAAI,YAAY,OAAO,SAAS,QAAQ,SAAS,OAAO,WAAW,OAAO,GAAG;AAS7E,aAAS,YAAY,QAAQ;AAC3B,UAAI,SAAS,UAAU,YAAY;AACnC,aAAO,UAAU,KAAK,MAAM,GAAG;AAC7B,UAAE;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3CjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,cAAc;AASlB,aAAS,WAAW,QAAQ;AAC1B,aAAO,WAAW,MAAM,IACpB,YAAY,MAAM,IAClB,UAAU,MAAM;AAAA,IACtB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,SAAS;AADb,QAEI,cAAc;AAFlB,QAGI,WAAW;AAHf,QAII,aAAa;AAGjB,QAAI,SAAS;AAAb,QACI,SAAS;AAuBb,aAAS,KAAK,YAAY;AACxB,UAAI,cAAc,MAAM;AACtB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,UAAU,GAAG;AAC3B,eAAO,SAAS,UAAU,IAAI,WAAW,UAAU,IAAI,WAAW;AAAA,MACpE;AACA,UAAI,MAAM,OAAO,UAAU;AAC3B,UAAI,OAAO,UAAU,OAAO,QAAQ;AAClC,eAAO,WAAW;AAAA,MACpB;AACA,aAAO,SAAS,UAAU,EAAE;AAAA,IAC9B;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7CjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,aAAa;AAFjB,QAGI,eAAe;AAHnB,QAII,eAAe;AAJnB,QAKI,UAAU;AALd,QAMI,WAAW;AANf,QAOI,aAAa;AAPjB,QAQI,WAAW;AARf,QASI,eAAe;AAgCnB,aAAS,UAAU,QAAQ,UAAU,aAAa;AAChD,UAAI,QAAQ,QAAQ,MAAM,GACtB,YAAY,SAAS,SAAS,MAAM,KAAK,aAAa,MAAM;AAEhE,iBAAW,aAAa,UAAU,CAAC;AACnC,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,UAAU,OAAO;AAC5B,YAAI,WAAW;AACb,wBAAc,QAAQ,IAAI,SAAO,CAAC;AAAA,QACpC,WACS,SAAS,MAAM,GAAG;AACzB,wBAAc,WAAW,IAAI,IAAI,WAAW,aAAa,MAAM,CAAC,IAAI,CAAC;AAAA,QACvE,OACK;AACH,wBAAc,CAAC;AAAA,QACjB;AAAA,MACF;AACA,OAAC,YAAY,YAAY,YAAY,QAAQ,SAAS,OAAO,OAAOC,SAAQ;AAC1E,eAAO,SAAS,aAAa,OAAO,OAAOA,OAAM;AAAA,MACnD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChEjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,eAAe;AA2BnB,aAAS,kBAAkB,OAAO;AAChC,aAAO,aAAa,KAAK,KAAK,YAAY,KAAK;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,WAAW;AAFf,QAGI,oBAAoB;AAkBxB,QAAI,QAAQ,SAAS,SAAS,QAAQ;AACpC,aAAO,SAAS,YAAY,QAAQ,GAAG,mBAAmB,IAAI,CAAC;AAAA,IACjE,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAAA,QAAI,WAAW;AAYf,aAAS,WAAW,QAAQ,OAAO;AACjC,aAAO,SAAS,OAAO,SAAS,KAAK;AACnC,eAAO,OAAO,GAAG;AAAA,MACnB,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,OAAO;AA4BX,aAAS,OAAO,QAAQ;AACtB,aAAO,UAAU,OAAO,CAAC,IAAI,WAAW,QAAQ,KAAK,MAAM,CAAC;AAAA,IAC9D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjCjB;AAAA;AAEA,QAAI;AAEJ,QAAI,OAAO,cAAY,YAAY;AACjC,UAAI;AACF,iBAAS;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,KAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,MAAM;AAAA,UACN,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ;AACX,eAAS,OAAO;AAAA,IAClB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjCjB;AAAA;AAAA;AAEA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,QAAI,oBAAoB;AACxB,QAAI,aAAa;AACjB,QAAI,iBAAiB;AAYrB,aAAS,MAAM,MAAM;AACnB,WAAK,cAAc,EAAE,IAAI,MAAM,UAAU,IAAI,KAAK,WAAW;AAC7D,WAAK,gBAAgB,EAAE,IAAI,MAAM,YAAY,IAAI,KAAK,aAAa;AACnE,WAAK,cAAc,EAAE,IAAI,MAAM,UAAU,IAAI,KAAK,WAAW;AAG7D,WAAK,SAAS;AAGd,WAAK,sBAAsB,EAAE,SAAS,MAAS;AAG/C,WAAK,sBAAsB,EAAE,SAAS,MAAS;AAG/C,WAAK,SAAS,CAAC;AAEf,UAAI,KAAK,aAAa;AAEpB,aAAK,UAAU,CAAC;AAGhB,aAAK,YAAY,CAAC;AAClB,aAAK,UAAU,UAAU,IAAI,CAAC;AAAA,MAChC;AAGA,WAAK,MAAM,CAAC;AAGZ,WAAK,SAAS,CAAC;AAGf,WAAK,OAAO,CAAC;AAGb,WAAK,QAAQ,CAAC;AAGd,WAAK,YAAY,CAAC;AAGlB,WAAK,cAAc,CAAC;AAAA,IACtB;AAGA,UAAM,UAAU,aAAa;AAG7B,UAAM,UAAU,aAAa;AAK7B,UAAM,UAAU,aAAa,WAAW;AACtC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,eAAe,WAAW;AACxC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,aAAa,WAAW;AACtC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,WAAW,SAAS,OAAO;AACzC,WAAK,SAAS;AACd,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,QAAQ,WAAW;AACjC,aAAO,KAAK;AAAA,IACd;AAKA,UAAM,UAAU,sBAAsB,SAAS,YAAY;AACzD,UAAI,CAAC,EAAE,WAAW,UAAU,GAAG;AAC7B,qBAAa,EAAE,SAAS,UAAU;AAAA,MACpC;AACA,WAAK,sBAAsB;AAC3B,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,YAAY,WAAW;AACrC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,QAAQ,WAAW;AACjC,aAAO,EAAE,KAAK,KAAK,MAAM;AAAA,IAC3B;AAEA,UAAM,UAAU,UAAU,WAAW;AACnC,UAAI,OAAO;AACX,aAAO,EAAE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;AACxC,eAAO,EAAE,QAAQ,KAAK,IAAI,CAAC,CAAC;AAAA,MAC9B,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,QAAQ,WAAW;AACjC,UAAI,OAAO;AACX,aAAO,EAAE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;AACxC,eAAO,EAAE,QAAQ,KAAK,KAAK,CAAC,CAAC;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,WAAW,SAAS,IAAI,OAAO;AAC7C,UAAI,OAAO;AACX,UAAI,OAAO;AACX,QAAE,KAAK,IAAI,SAAS,GAAG;AACrB,YAAI,KAAK,SAAS,GAAG;AACnB,eAAK,QAAQ,GAAG,KAAK;AAAA,QACvB,OAAO;AACL,eAAK,QAAQ,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,UAAU,SAAS,GAAG,OAAO;AAC3C,UAAI,EAAE,IAAI,KAAK,QAAQ,CAAC,GAAG;AACzB,YAAI,UAAU,SAAS,GAAG;AACxB,eAAK,OAAO,CAAC,IAAI;AAAA,QACnB;AACA,eAAO;AAAA,MACT;AAEA,WAAK,OAAO,CAAC,IAAI,UAAU,SAAS,IAAI,QAAQ,KAAK,oBAAoB,CAAC;AAC1E,UAAI,KAAK,aAAa;AACpB,aAAK,QAAQ,CAAC,IAAI;AAClB,aAAK,UAAU,CAAC,IAAI,CAAC;AACrB,aAAK,UAAU,UAAU,EAAE,CAAC,IAAI;AAAA,MAClC;AACA,WAAK,IAAI,CAAC,IAAI,CAAC;AACf,WAAK,OAAO,CAAC,IAAI,CAAC;AAClB,WAAK,KAAK,CAAC,IAAI,CAAC;AAChB,WAAK,MAAM,CAAC,IAAI,CAAC;AACjB,QAAE,KAAK;AACP,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,OAAO,SAAS,GAAG;AACjC,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAEA,UAAM,UAAU,UAAU,SAAS,GAAG;AACpC,aAAO,EAAE,IAAI,KAAK,QAAQ,CAAC;AAAA,IAC7B;AAEA,UAAM,UAAU,aAAc,SAAS,GAAG;AACxC,UAAI,OAAO;AACX,UAAI,EAAE,IAAI,KAAK,QAAQ,CAAC,GAAG;AACzB,YAAI,aAAa,SAAS,GAAG;AAAE,eAAK,WAAW,KAAK,UAAU,CAAC,CAAC;AAAA,QAAG;AACnE,eAAO,KAAK,OAAO,CAAC;AACpB,YAAI,KAAK,aAAa;AACpB,eAAK,4BAA4B,CAAC;AAClC,iBAAO,KAAK,QAAQ,CAAC;AACrB,YAAE,KAAK,KAAK,SAAS,CAAC,GAAG,SAAS,OAAO;AACvC,iBAAK,UAAU,KAAK;AAAA,UACtB,CAAC;AACD,iBAAO,KAAK,UAAU,CAAC;AAAA,QACzB;AACA,UAAE,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,UAAU;AACtC,eAAO,KAAK,IAAI,CAAC;AACjB,eAAO,KAAK,OAAO,CAAC;AACpB,UAAE,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC,GAAG,UAAU;AACvC,eAAO,KAAK,KAAK,CAAC;AAClB,eAAO,KAAK,MAAM,CAAC;AACnB,UAAE,KAAK;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,YAAY,SAAS,GAAG,QAAQ;AAC9C,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC7D;AAEA,UAAI,EAAE,YAAY,MAAM,GAAG;AACzB,iBAAS;AAAA,MACX,OAAO;AAEL,kBAAU;AACV,iBAAS,WAAW,QAClB,CAAC,EAAE,YAAY,QAAQ,GACvB,WAAW,KAAK,OAAO,QAAQ,GAAG;AAClC,cAAI,aAAa,GAAG;AAClB,kBAAM,IAAI,MAAM,aAAa,SAAQ,mBAAmB,IACxC,uBAAuB;AAAA,UACzC;AAAA,QACF;AAEA,aAAK,QAAQ,MAAM;AAAA,MACrB;AAEA,WAAK,QAAQ,CAAC;AACd,WAAK,4BAA4B,CAAC;AAClC,WAAK,QAAQ,CAAC,IAAI;AAClB,WAAK,UAAU,MAAM,EAAE,CAAC,IAAI;AAC5B,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,8BAA8B,SAAS,GAAG;AACxD,aAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC;AAAA,IAC1C;AAEA,UAAM,UAAU,SAAS,SAAS,GAAG;AACnC,UAAI,KAAK,aAAa;AACpB,YAAI,SAAS,KAAK,QAAQ,CAAC;AAC3B,YAAI,WAAW,YAAY;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,UAAM,UAAU,WAAW,SAAS,GAAG;AACrC,UAAI,EAAE,YAAY,CAAC,GAAG;AACpB,YAAI;AAAA,MACN;AAEA,UAAI,KAAK,aAAa;AACpB,YAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,YAAI,UAAU;AACZ,iBAAO,EAAE,KAAK,QAAQ;AAAA,QACxB;AAAA,MACF,WAAW,MAAM,YAAY;AAC3B,eAAO,KAAK,MAAM;AAAA,MACpB,WAAW,KAAK,QAAQ,CAAC,GAAG;AAC1B,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AAEA,UAAM,UAAU,eAAe,SAAS,GAAG;AACzC,UAAI,SAAS,KAAK,OAAO,CAAC;AAC1B,UAAI,QAAQ;AACV,eAAO,EAAE,KAAK,MAAM;AAAA,MACtB;AAAA,IACF;AAEA,UAAM,UAAU,aAAa,SAAS,GAAG;AACvC,UAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,UAAI,OAAO;AACT,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AAAA,IACF;AAEA,UAAM,UAAU,YAAY,SAAS,GAAG;AACtC,UAAI,QAAQ,KAAK,aAAa,CAAC;AAC/B,UAAI,OAAO;AACT,eAAO,EAAE,MAAM,OAAO,KAAK,WAAW,CAAC,CAAC;AAAA,MAC1C;AAAA,IACF;AAEA,UAAM,UAAU,SAAS,SAAU,GAAG;AACpC,UAAI;AACJ,UAAI,KAAK,WAAW,GAAG;AACrB,oBAAY,KAAK,WAAW,CAAC;AAAA,MAC/B,OAAO;AACL,oBAAY,KAAK,UAAU,CAAC;AAAA,MAC9B;AACA,aAAO,UAAU,WAAW;AAAA,IAC9B;AAEA,UAAM,UAAU,cAAc,SAAS,QAAQ;AAC7C,UAAI,OAAO,IAAI,KAAK,YAAY;AAAA,QAC9B,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,MACjB,CAAC;AAED,WAAK,SAAS,KAAK,MAAM,CAAC;AAE1B,UAAI,OAAO;AACX,QAAE,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACrC,YAAI,OAAO,CAAC,GAAG;AACb,eAAK,QAAQ,GAAG,KAAK;AAAA,QACvB;AAAA,MACF,CAAC;AAED,QAAE,KAAK,KAAK,WAAW,SAAS,GAAG;AACjC,YAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC,GAAG;AAC1C,eAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF,CAAC;AAED,UAAI,UAAU,CAAC;AACf,eAAS,WAAW,GAAG;AACrB,YAAI,SAAS,KAAK,OAAO,CAAC;AAC1B,YAAI,WAAW,UAAa,KAAK,QAAQ,MAAM,GAAG;AAChD,kBAAQ,CAAC,IAAI;AACb,iBAAO;AAAA,QACT,WAAW,UAAU,SAAS;AAC5B,iBAAO,QAAQ,MAAM;AAAA,QACvB,OAAO;AACL,iBAAO,WAAW,MAAM;AAAA,QAC1B;AAAA,MACF;AAEA,UAAI,KAAK,aAAa;AACpB,UAAE,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG;AAC/B,eAAK,UAAU,GAAG,WAAW,CAAC,CAAC;AAAA,QACjC,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AAIA,UAAM,UAAU,sBAAsB,SAAS,YAAY;AACzD,UAAI,CAAC,EAAE,WAAW,UAAU,GAAG;AAC7B,qBAAa,EAAE,SAAS,UAAU;AAAA,MACpC;AACA,WAAK,sBAAsB;AAC3B,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,YAAY,WAAW;AACrC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,QAAQ,WAAW;AACjC,aAAO,EAAE,OAAO,KAAK,SAAS;AAAA,IAChC;AAEA,UAAM,UAAU,UAAU,SAAS,IAAI,OAAO;AAC5C,UAAI,OAAO;AACX,UAAI,OAAO;AACX,QAAE,OAAO,IAAI,SAAS,GAAG,GAAG;AAC1B,YAAI,KAAK,SAAS,GAAG;AACnB,eAAK,QAAQ,GAAG,GAAG,KAAK;AAAA,QAC1B,OAAO;AACL,eAAK,QAAQ,GAAG,CAAC;AAAA,QACnB;AACA,eAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,IACT;AAMA,UAAM,UAAU,UAAU,WAAW;AACnC,UAAI,GAAG,GAAG,MAAM;AAChB,UAAI,iBAAiB;AACrB,UAAI,OAAO,UAAU,CAAC;AAEtB,UAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,MAAM;AAC5D,YAAI,KAAK;AACT,YAAI,KAAK;AACT,eAAO,KAAK;AACZ,YAAI,UAAU,WAAW,GAAG;AAC1B,kBAAQ,UAAU,CAAC;AACnB,2BAAiB;AAAA,QACnB;AAAA,MACF,OAAO;AACL,YAAI;AACJ,YAAI,UAAU,CAAC;AACf,eAAO,UAAU,CAAC;AAClB,YAAI,UAAU,SAAS,GAAG;AACxB,kBAAQ,UAAU,CAAC;AACnB,2BAAiB;AAAA,QACnB;AAAA,MACF;AAEA,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,CAAC,EAAE,YAAY,IAAI,GAAG;AACxB,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,IAAI,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AACjD,UAAI,EAAE,IAAI,KAAK,aAAa,CAAC,GAAG;AAC9B,YAAI,gBAAgB;AAClB,eAAK,YAAY,CAAC,IAAI;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,EAAE,YAAY,IAAI,KAAK,CAAC,KAAK,eAAe;AAC/C,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAIA,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AAEd,WAAK,YAAY,CAAC,IAAI,iBAAiB,QAAQ,KAAK,oBAAoB,GAAG,GAAG,IAAI;AAElF,UAAI,UAAU,cAAc,KAAK,aAAa,GAAG,GAAG,IAAI;AAExD,UAAI,QAAQ;AACZ,UAAI,QAAQ;AAEZ,aAAO,OAAO,OAAO;AACrB,WAAK,UAAU,CAAC,IAAI;AACpB,2BAAqB,KAAK,OAAO,CAAC,GAAG,CAAC;AACtC,2BAAqB,KAAK,MAAM,CAAC,GAAG,CAAC;AACrC,WAAK,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,WAAK,KAAK,CAAC,EAAE,CAAC,IAAI;AAClB,WAAK;AACL,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,OAAO,SAAS,GAAG,GAAG,MAAM;AAC1C,UAAI,IAAK,UAAU,WAAW,IAC1B,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC7C,aAAO,KAAK,YAAY,CAAC;AAAA,IAC3B;AAEA,UAAM,UAAU,UAAU,SAAS,GAAG,GAAG,MAAM;AAC7C,UAAI,IAAK,UAAU,WAAW,IAC1B,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC7C,aAAO,EAAE,IAAI,KAAK,aAAa,CAAC;AAAA,IAClC;AAEA,UAAM,UAAU,aAAa,SAAS,GAAG,GAAG,MAAM;AAChD,UAAI,IAAK,UAAU,WAAW,IAC1B,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC7C,UAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,UAAI,MAAM;AACR,YAAI,KAAK;AACT,YAAI,KAAK;AACT,eAAO,KAAK,YAAY,CAAC;AACzB,eAAO,KAAK,UAAU,CAAC;AACvB,+BAAuB,KAAK,OAAO,CAAC,GAAG,CAAC;AACxC,+BAAuB,KAAK,MAAM,CAAC,GAAG,CAAC;AACvC,eAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACpB,eAAO,KAAK,KAAK,CAAC,EAAE,CAAC;AACrB,aAAK;AAAA,MACP;AACA,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,UAAU,SAAS,GAAG,GAAG;AACvC,UAAI,MAAM,KAAK,IAAI,CAAC;AACpB,UAAI,KAAK;AACP,YAAI,QAAQ,EAAE,OAAO,GAAG;AACxB,YAAI,CAAC,GAAG;AACN,iBAAO;AAAA,QACT;AACA,eAAO,EAAE,OAAO,OAAO,SAAS,MAAM;AAAE,iBAAO,KAAK,MAAM;AAAA,QAAG,CAAC;AAAA,MAChE;AAAA,IACF;AAEA,UAAM,UAAU,WAAW,SAAS,GAAG,GAAG;AACxC,UAAI,OAAO,KAAK,KAAK,CAAC;AACtB,UAAI,MAAM;AACR,YAAI,QAAQ,EAAE,OAAO,IAAI;AACzB,YAAI,CAAC,GAAG;AACN,iBAAO;AAAA,QACT;AACA,eAAO,EAAE,OAAO,OAAO,SAAS,MAAM;AAAE,iBAAO,KAAK,MAAM;AAAA,QAAG,CAAC;AAAA,MAChE;AAAA,IACF;AAEA,UAAM,UAAU,YAAY,SAAS,GAAG,GAAG;AACzC,UAAI,UAAU,KAAK,QAAQ,GAAG,CAAC;AAC/B,UAAI,SAAS;AACX,eAAO,QAAQ,OAAO,KAAK,SAAS,GAAG,CAAC,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,aAAS,qBAAqB,KAAK,GAAG;AACpC,UAAI,IAAI,CAAC,GAAG;AACV,YAAI,CAAC;AAAA,MACP,OAAO;AACL,YAAI,CAAC,IAAI;AAAA,MACX;AAAA,IACF;AAEA,aAAS,uBAAuB,KAAK,GAAG;AACtC,UAAI,CAAC,EAAE,IAAI,CAAC,GAAG;AAAE,eAAO,IAAI,CAAC;AAAA,MAAG;AAAA,IAClC;AAEA,aAAS,aAAa,YAAY,IAAI,IAAI,MAAM;AAC9C,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AACb,UAAI,CAAC,cAAc,IAAI,GAAG;AACxB,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AACA,aAAO,IAAI,iBAAiB,IAAI,kBACpB,EAAE,YAAY,IAAI,IAAI,oBAAoB;AAAA,IACxD;AAEA,aAAS,cAAc,YAAY,IAAI,IAAI,MAAM;AAC/C,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AACb,UAAI,CAAC,cAAc,IAAI,GAAG;AACxB,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AACA,UAAI,UAAW,EAAE,GAAM,EAAK;AAC5B,UAAI,MAAM;AACR,gBAAQ,OAAO;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,YAAY,YAAY,SAAS;AACxC,aAAO,aAAa,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,IAAI;AAAA,IACpE;AAAA;AAAA;;;ACnhBA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AACA,WAAO,UAAU;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA;AAAA;;;ACJA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,QAAQ;AAEZ,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAEA,aAAS,MAAM,GAAG;AAChB,UAAI,OAAO;AAAA,QACT,SAAS;AAAA,UACP,UAAU,EAAE,WAAW;AAAA,UACvB,YAAY,EAAE,aAAa;AAAA,UAC3B,UAAU,EAAE,WAAW;AAAA,QACzB;AAAA,QACA,OAAO,WAAW,CAAC;AAAA,QACnB,OAAO,WAAW,CAAC;AAAA,MACrB;AACA,UAAI,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC,GAAG;AAC7B,aAAK,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;AAAA,MAChC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AAClC,YAAI,YAAY,EAAE,KAAK,CAAC;AACxB,YAAI,SAAS,EAAE,OAAO,CAAC;AACvB,YAAI,OAAO,EAAE,EAAK;AAClB,YAAI,CAAC,EAAE,YAAY,SAAS,GAAG;AAC7B,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,CAAC,EAAE,YAAY,MAAM,GAAG;AAC1B,eAAK,SAAS;AAAA,QAChB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AAClC,YAAI,YAAY,EAAE,KAAK,CAAC;AACxB,YAAI,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE;AAC5B,YAAI,CAAC,EAAE,YAAY,EAAE,IAAI,GAAG;AAC1B,eAAK,OAAO,EAAE;AAAA,QAChB;AACA,YAAI,CAAC,EAAE,YAAY,SAAS,GAAG;AAC7B,eAAK,QAAQ;AAAA,QACf;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,aAAS,KAAK,MAAM;AAClB,UAAI,IAAI,IAAI,MAAM,KAAK,OAAO,EAAE,SAAS,KAAK,KAAK;AACnD,QAAE,KAAK,KAAK,OAAO,SAAS,OAAO;AACjC,UAAE,QAAQ,MAAM,GAAG,MAAM,KAAK;AAC9B,YAAI,MAAM,QAAQ;AAChB,YAAE,UAAU,MAAM,GAAG,MAAM,MAAM;AAAA,QACnC;AAAA,MACF,CAAC;AACD,QAAE,KAAK,KAAK,OAAO,SAAS,OAAO;AACjC,UAAE,QAAQ,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,MAAM,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,MACrE,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjEA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,aAAS,WAAW,GAAG;AACrB,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,CAAC;AACb,UAAI;AAEJ,eAAS,IAAI,GAAG;AACd,YAAI,EAAE,IAAI,SAAS,CAAC,EAAG;AACvB,gBAAQ,CAAC,IAAI;AACb,aAAK,KAAK,CAAC;AACX,UAAE,KAAK,EAAE,WAAW,CAAC,GAAG,GAAG;AAC3B,UAAE,KAAK,EAAE,aAAa,CAAC,GAAG,GAAG;AAAA,MAC/B;AAEA,QAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG;AAC5B,eAAO,CAAC;AACR,YAAI,CAAC;AACL,YAAI,KAAK,QAAQ;AACf,gBAAM,KAAK,IAAI;AAAA,QACjB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;AC1BA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AASjB,aAAS,gBAAgB;AACvB,WAAK,OAAO,CAAC;AACb,WAAK,cAAc,CAAC;AAAA,IACtB;AAKA,kBAAc,UAAU,OAAO,WAAW;AACxC,aAAO,KAAK,KAAK;AAAA,IACnB;AAKA,kBAAc,UAAU,OAAO,WAAW;AACxC,aAAO,KAAK,KAAK,IAAI,SAAS,GAAG;AAAE,eAAO,EAAE;AAAA,MAAK,CAAC;AAAA,IACpD;AAKA,kBAAc,UAAU,MAAM,SAAS,KAAK;AAC1C,aAAO,EAAE,IAAI,KAAK,aAAa,GAAG;AAAA,IACpC;AAQA,kBAAc,UAAU,WAAW,SAAS,KAAK;AAC/C,UAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,UAAI,UAAU,QAAW;AACvB,eAAO,KAAK,KAAK,KAAK,EAAE;AAAA,MAC1B;AAAA,IACF;AAMA,kBAAc,UAAU,MAAM,WAAW;AACvC,UAAI,KAAK,KAAK,MAAM,GAAG;AACrB,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AACA,aAAO,KAAK,KAAK,CAAC,EAAE;AAAA,IACtB;AAUA,kBAAc,UAAU,MAAM,SAAS,KAAK,UAAU;AACpD,UAAI,aAAa,KAAK;AACtB,YAAM,OAAO,GAAG;AAChB,UAAI,CAAC,EAAE,IAAI,YAAY,GAAG,GAAG;AAC3B,YAAI,MAAM,KAAK;AACf,YAAI,QAAQ,IAAI;AAChB,mBAAW,GAAG,IAAI;AAClB,YAAI,KAAK,EAAC,KAAU,SAAkB,CAAC;AACvC,aAAK,UAAU,KAAK;AACpB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAKA,kBAAc,UAAU,YAAY,WAAW;AAC7C,WAAK,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC;AAClC,UAAI,MAAM,KAAK,KAAK,IAAI;AACxB,aAAO,KAAK,YAAY,IAAI,GAAG;AAC/B,WAAK,SAAS,CAAC;AACf,aAAO,IAAI;AAAA,IACb;AASA,kBAAc,UAAU,WAAW,SAAS,KAAK,UAAU;AACzD,UAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,UAAI,WAAW,KAAK,KAAK,KAAK,EAAE,UAAU;AACxC,cAAM,IAAI,MAAM,yDACF,MAAM,WAAW,KAAK,KAAK,KAAK,EAAE,WAAW,WAAW,QAAQ;AAAA,MAChF;AACA,WAAK,KAAK,KAAK,EAAE,WAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACtB;AAEA,kBAAc,UAAU,WAAW,SAAS,GAAG;AAC7C,UAAI,MAAM,KAAK;AACf,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAI,UAAU;AACd,UAAI,IAAI,IAAI,QAAQ;AAClB,kBAAU,IAAI,CAAC,EAAE,WAAW,IAAI,OAAO,EAAE,WAAW,IAAI;AACxD,YAAI,IAAI,IAAI,QAAQ;AAClB,oBAAU,IAAI,CAAC,EAAE,WAAW,IAAI,OAAO,EAAE,WAAW,IAAI;AAAA,QAC1D;AACA,YAAI,YAAY,GAAG;AACjB,eAAK,MAAM,GAAG,OAAO;AACrB,eAAK,SAAS,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAEA,kBAAc,UAAU,YAAY,SAAS,OAAO;AAClD,UAAI,MAAM,KAAK;AACf,UAAI,WAAW,IAAI,KAAK,EAAE;AAC1B,UAAI;AACJ,aAAO,UAAU,GAAG;AAClB,iBAAS,SAAS;AAClB,YAAI,IAAI,MAAM,EAAE,WAAW,UAAU;AACnC;AAAA,QACF;AACA,aAAK,MAAM,OAAO,MAAM;AACxB,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,kBAAc,UAAU,QAAQ,SAAS,GAAG,GAAG;AAC7C,UAAI,MAAM,KAAK;AACf,UAAI,aAAa,KAAK;AACtB,UAAI,WAAW,IAAI,CAAC;AACpB,UAAI,WAAW,IAAI,CAAC;AACpB,UAAI,CAAC,IAAI;AACT,UAAI,CAAC,IAAI;AACT,iBAAW,SAAS,GAAG,IAAI;AAC3B,iBAAW,SAAS,GAAG,IAAI;AAAA,IAC7B;AAAA;AAAA;;;ACvJA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,gBAAgB;AAEpB,WAAO,UAAU;AAEjB,QAAI,sBAAsB,EAAE,SAAS,CAAC;AAEtC,aAAS,SAAS,GAAG,QAAQ,UAAU,QAAQ;AAC7C,aAAO;AAAA,QAAY;AAAA,QAAG,OAAO,MAAM;AAAA,QACjC,YAAY;AAAA,QACZ,UAAU,SAAS,GAAG;AAAE,iBAAO,EAAE,SAAS,CAAC;AAAA,QAAG;AAAA,MAAC;AAAA,IACnD;AAEA,aAAS,YAAY,GAAG,QAAQ,UAAU,QAAQ;AAChD,UAAI,UAAU,CAAC;AACf,UAAI,KAAK,IAAI,cAAc;AAC3B,UAAI,GAAG;AAEP,UAAI,kBAAkB,SAAS,MAAM;AACnC,YAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrC,YAAI,SAAS,QAAQ,CAAC;AACtB,YAAI,SAAS,SAAS,IAAI;AAC1B,YAAI,WAAW,OAAO,WAAW;AAEjC,YAAI,SAAS,GAAG;AACd,gBAAM,IAAI,MAAM,8DACe,OAAO,cAAc,MAAM;AAAA,QAC5D;AAEA,YAAI,WAAW,OAAO,UAAU;AAC9B,iBAAO,WAAW;AAClB,iBAAO,cAAc;AACrB,aAAG,SAAS,GAAG,QAAQ;AAAA,QACzB;AAAA,MACF;AAEA,QAAE,MAAM,EAAE,QAAQ,SAASC,IAAG;AAC5B,YAAI,WAAWA,OAAM,SAAS,IAAI,OAAO;AACzC,gBAAQA,EAAC,IAAI,EAAE,SAAmB;AAClC,WAAG,IAAIA,IAAG,QAAQ;AAAA,MACpB,CAAC;AAED,aAAO,GAAG,KAAK,IAAI,GAAG;AACpB,YAAI,GAAG,UAAU;AACjB,iBAAS,QAAQ,CAAC;AAClB,YAAI,OAAO,aAAa,OAAO,mBAAmB;AAChD;AAAA,QACF;AAEA,eAAO,CAAC,EAAE,QAAQ,eAAe;AAAA,MACnC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACrDA;AAAA;AAAA,QAAI,WAAW;AACf,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,aAAS,YAAY,GAAG,YAAY,UAAU;AAC5C,aAAO,EAAE,UAAU,EAAE,MAAM,GAAG,SAAS,KAAK,GAAG;AAC7C,YAAI,CAAC,IAAI,SAAS,GAAG,GAAG,YAAY,QAAQ;AAAA,MAC9C,GAAG,CAAC,CAAC;AAAA,IACP;AAAA;AAAA;;;ACTA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,aAAS,OAAO,GAAG;AACjB,UAAI,QAAQ;AACZ,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,CAAC;AACf,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG;AACd,YAAI,QAAQ,QAAQ,CAAC,IAAI;AAAA,UACvB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AACA,cAAM,KAAK,CAAC;AAEZ,UAAE,WAAW,CAAC,EAAE,QAAQ,SAASC,IAAG;AAClC,cAAI,CAAC,EAAE,IAAI,SAASA,EAAC,GAAG;AACtB,gBAAIA,EAAC;AACL,kBAAM,UAAU,KAAK,IAAI,MAAM,SAAS,QAAQA,EAAC,EAAE,OAAO;AAAA,UAC5D,WAAW,QAAQA,EAAC,EAAE,SAAS;AAC7B,kBAAM,UAAU,KAAK,IAAI,MAAM,SAAS,QAAQA,EAAC,EAAE,KAAK;AAAA,UAC1D;AAAA,QACF,CAAC;AAED,YAAI,MAAM,YAAY,MAAM,OAAO;AACjC,cAAI,OAAO,CAAC;AACZ,cAAI;AACJ,aAAG;AACD,gBAAI,MAAM,IAAI;AACd,oBAAQ,CAAC,EAAE,UAAU;AACrB,iBAAK,KAAK,CAAC;AAAA,UACb,SAAS,MAAM;AACf,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAAA,MACF;AAEA,QAAE,MAAM,EAAE,QAAQ,SAAS,GAAG;AAC5B,YAAI,CAAC,EAAE,IAAI,SAAS,CAAC,GAAG;AACtB,cAAI,CAAC;AAAA,QACP;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;AC9CA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,SAAS;AAEb,WAAO,UAAU;AAEjB,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,OAAO,OAAO,CAAC,GAAG,SAAS,MAAM;AACxC,eAAO,KAAK,SAAS,KAAM,KAAK,WAAW,KAAK,EAAE,QAAQ,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MAC5E,CAAC;AAAA,IACH;AAAA;AAAA;;;ACTA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,QAAI,sBAAsB,EAAE,SAAS,CAAC;AAEtC,aAAS,cAAc,GAAG,UAAU,QAAQ;AAC1C,aAAO;AAAA,QAAiB;AAAA,QACtB,YAAY;AAAA,QACZ,UAAU,SAAS,GAAG;AAAE,iBAAO,EAAE,SAAS,CAAC;AAAA,QAAG;AAAA,MAAC;AAAA,IACnD;AAEA,aAAS,iBAAiB,GAAG,UAAU,QAAQ;AAC7C,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,EAAE,MAAM;AAEpB,YAAM,QAAQ,SAAS,GAAG;AACxB,gBAAQ,CAAC,IAAI,CAAC;AACd,gBAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE;AAC9B,cAAM,QAAQ,SAAS,GAAG;AACxB,cAAI,MAAM,GAAG;AACX,oBAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,OAAO,kBAAkB;AAAA,UACvD;AAAA,QACF,CAAC;AACD,eAAO,CAAC,EAAE,QAAQ,SAAS,MAAM;AAC/B,cAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrC,cAAI,IAAI,SAAS,IAAI;AACrB,kBAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,GAAG,aAAa,EAAE;AAAA,QAChD,CAAC;AAAA,MACH,CAAC;AAED,YAAM,QAAQ,SAAS,GAAG;AACxB,YAAI,OAAO,QAAQ,CAAC;AACpB,cAAM,QAAQ,SAAS,GAAG;AACxB,cAAI,OAAO,QAAQ,CAAC;AACpB,gBAAM,QAAQ,SAAS,GAAG;AACxB,gBAAI,KAAK,KAAK,CAAC;AACf,gBAAI,KAAK,KAAK,CAAC;AACf,gBAAI,KAAK,KAAK,CAAC;AACf,gBAAI,cAAc,GAAG,WAAW,GAAG;AACnC,gBAAI,cAAc,GAAG,UAAU;AAC7B,iBAAG,WAAW;AACd,iBAAG,cAAc,GAAG;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjDA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AACjB,YAAQ,iBAAiB;AAEzB,aAAS,QAAQ,GAAG;AAClB,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,CAAC;AAEf,eAAS,MAAM,MAAM;AACnB,YAAI,EAAE,IAAI,OAAO,IAAI,GAAG;AACtB,gBAAM,IAAI,eAAe;AAAA,QAC3B;AAEA,YAAI,CAAC,EAAE,IAAI,SAAS,IAAI,GAAG;AACzB,gBAAM,IAAI,IAAI;AACd,kBAAQ,IAAI,IAAI;AAChB,YAAE,KAAK,EAAE,aAAa,IAAI,GAAG,KAAK;AAClC,iBAAO,MAAM,IAAI;AACjB,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAAA,MACF;AAEA,QAAE,KAAK,EAAE,MAAM,GAAG,KAAK;AAEvB,UAAI,EAAE,KAAK,OAAO,MAAM,EAAE,UAAU,GAAG;AACrC,cAAM,IAAI,eAAe;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB;AAAA,IAAC;AAC3B,mBAAe,YAAY,IAAI,MAAM;AAAA;AAAA;;;AClCrC;AAAA;AAAA,QAAI,UAAU;AAEd,WAAO,UAAU;AAEjB,aAAS,UAAU,GAAG;AACpB,UAAI;AACF,gBAAQ,CAAC;AAAA,MACX,SAAS,GAAG;AACV,YAAI,aAAa,QAAQ,gBAAgB;AACvC,iBAAO;AAAA,QACT;AACA,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACdA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAUjB,aAAS,IAAI,GAAG,IAAI,OAAO;AACzB,UAAI,CAAC,EAAE,QAAQ,EAAE,GAAG;AAClB,aAAK,CAAC,EAAE;AAAA,MACV;AAEA,UAAI,cAAc,EAAE,WAAW,IAAI,EAAE,aAAa,EAAE,WAAW,KAAK,CAAC;AAErE,UAAI,MAAM,CAAC;AACX,UAAI,UAAU,CAAC;AACf,QAAE,KAAK,IAAI,SAAS,GAAG;AACrB,YAAI,CAAC,EAAE,QAAQ,CAAC,GAAG;AACjB,gBAAM,IAAI,MAAM,+BAA+B,CAAC;AAAA,QAClD;AAEA,cAAM,GAAG,GAAG,UAAU,QAAQ,SAAS,YAAY,GAAG;AAAA,MACxD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,MAAM,GAAG,GAAG,WAAW,SAAS,YAAY,KAAK;AACxD,UAAI,CAAC,EAAE,IAAI,SAAS,CAAC,GAAG;AACtB,gBAAQ,CAAC,IAAI;AAEb,YAAI,CAAC,WAAW;AAAE,cAAI,KAAK,CAAC;AAAA,QAAG;AAC/B,UAAE,KAAK,WAAW,CAAC,GAAG,SAAS,GAAG;AAChC,gBAAM,GAAG,GAAG,WAAW,SAAS,YAAY,GAAG;AAAA,QACjD,CAAC;AACD,YAAI,WAAW;AAAE,cAAI,KAAK,CAAC;AAAA,QAAG;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;ACzCA;AAAA;AAAA,QAAI,MAAM;AAEV,WAAO,UAAU;AAEjB,aAAS,UAAU,GAAG,IAAI;AACxB,aAAO,IAAI,GAAG,IAAI,MAAM;AAAA,IAC1B;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI,MAAM;AAEV,WAAO,UAAU;AAEjB,aAAS,SAAS,GAAG,IAAI;AACvB,aAAO,IAAI,GAAG,IAAI,KAAK;AAAA,IACzB;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,QAAQ;AACZ,QAAI,gBAAgB;AAEpB,WAAO,UAAU;AAEjB,aAAS,KAAK,GAAG,YAAY;AAC3B,UAAI,SAAS,IAAI,MAAM;AACvB,UAAI,UAAU,CAAC;AACf,UAAI,KAAK,IAAI,cAAc;AAC3B,UAAI;AAEJ,eAAS,gBAAgB,MAAM;AAC7B,YAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrC,YAAI,MAAM,GAAG,SAAS,CAAC;AACvB,YAAI,QAAQ,QAAW;AACrB,cAAI,aAAa,WAAW,IAAI;AAChC,cAAI,aAAa,KAAK;AACpB,oBAAQ,CAAC,IAAI;AACb,eAAG,SAAS,GAAG,UAAU;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,EAAE,UAAU,MAAM,GAAG;AACvB,eAAO;AAAA,MACT;AAEA,QAAE,KAAK,EAAE,MAAM,GAAG,SAASC,IAAG;AAC5B,WAAG,IAAIA,IAAG,OAAO,iBAAiB;AAClC,eAAO,QAAQA,EAAC;AAAA,MAClB,CAAC;AAGD,SAAG,SAAS,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC;AAE3B,UAAI,OAAO;AACX,aAAO,GAAG,KAAK,IAAI,GAAG;AACpB,YAAI,GAAG,UAAU;AACjB,YAAI,EAAE,IAAI,SAAS,CAAC,GAAG;AACrB,iBAAO,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAAA,QAC9B,WAAW,MAAM;AACf,gBAAM,IAAI,MAAM,mCAAmC,CAAC;AAAA,QACtD,OAAO;AACL,iBAAO;AAAA,QACT;AAEA,UAAE,UAAU,CAAC,EAAE,QAAQ,eAAe;AAAA,MACxC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnDA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA;AAAA;;;ACZA;AAAA;AA8BA,QAAI,MAAM;AAEV,WAAO,UAAU;AAAA,MACf,OAAO,IAAI;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,MACL,SAAS,IAAI;AAAA,IACf;AAAA;AAAA;;;ACrCA,IAAAC,oBAAA;AAAA;AAEA,QAAI;AAEJ,QAAI,OAAO,cAAY,YAAY;AACjC,UAAI;AACF,mBAAW;AAAA,MACb,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAEA,QAAI,CAAC,UAAU;AACb,iBAAW,OAAO;AAAA,IACpB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAI,YAAY;AAGhB,QAAI,kBAAkB;AAAtB,QACI,qBAAqB;AAoBzB,aAAS,UAAU,OAAO;AACxB,aAAO,UAAU,OAAO,kBAAkB,kBAAkB;AAAA,IAC9D;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,KAAK;AADT,QAEI,iBAAiB;AAFrB,QAGI,SAAS;AAGb,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAuBjC,QAAI,WAAW,SAAS,SAAS,QAAQ,SAAS;AAChD,eAAS,OAAO,MAAM;AAEtB,UAAI,QAAQ;AACZ,UAAI,SAAS,QAAQ;AACrB,UAAI,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAEtC,UAAI,SAAS,eAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG;AAC1D,iBAAS;AAAA,MACX;AAEA,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,SAAS,QAAQ,KAAK;AAC1B,YAAI,QAAQ,OAAO,MAAM;AACzB,YAAI,aAAa;AACjB,YAAI,cAAc,MAAM;AAExB,eAAO,EAAE,aAAa,aAAa;AACjC,cAAI,MAAM,MAAM,UAAU;AAC1B,cAAI,QAAQ,OAAO,GAAG;AAEtB,cAAI,UAAU,UACT,GAAG,OAAO,YAAY,GAAG,CAAC,KAAK,CAAC,eAAe,KAAK,QAAQ,GAAG,GAAI;AACtE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;AC/DjB;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,eAAe;AADnB,QAEI,SAAS;AA8Bb,aAAS,MAAM,QAAQ,UAAU;AAC/B,aAAO,UAAU,OACb,SACA,QAAQ,QAAQ,aAAa,QAAQ,GAAG,MAAM;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtCjB;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,KAAK;AAWT,aAAS,iBAAiB,QAAQ,KAAK,OAAO;AAC5C,UAAK,UAAU,UAAa,CAAC,GAAG,OAAO,GAAG,GAAG,KAAK,KAC7C,UAAU,UAAa,EAAE,OAAO,SAAU;AAC7C,wBAAgB,QAAQ,KAAK,KAAK;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAQA,aAAS,QAAQ,QAAQ,KAAK;AAC5B,UAAI,QAAQ,iBAAiB,OAAO,OAAO,GAAG,MAAM,YAAY;AAC9D;AAAA,MACF;AAEA,UAAI,OAAO,aAAa;AACtB;AAAA,MACF;AAEA,aAAO,OAAO,GAAG;AAAA,IACnB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,SAAS;AA0Bb,aAAS,cAAc,OAAO;AAC5B,aAAO,WAAW,OAAO,OAAO,KAAK,CAAC;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,mBAAmB;AAAvB,QACI,cAAc;AADlB,QAEI,kBAAkB;AAFtB,QAGI,YAAY;AAHhB,QAII,kBAAkB;AAJtB,QAKI,cAAc;AALlB,QAMI,UAAU;AANd,QAOI,oBAAoB;AAPxB,QAQI,WAAW;AARf,QASI,aAAa;AATjB,QAUI,WAAW;AAVf,QAWI,gBAAgB;AAXpB,QAYI,eAAe;AAZnB,QAaI,UAAU;AAbd,QAcI,gBAAgB;AAiBpB,aAAS,cAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY,OAAO;AAClF,UAAI,WAAW,QAAQ,QAAQ,GAAG,GAC9B,WAAW,QAAQ,QAAQ,GAAG,GAC9B,UAAU,MAAM,IAAI,QAAQ;AAEhC,UAAI,SAAS;AACX,yBAAiB,QAAQ,KAAK,OAAO;AACrC;AAAA,MACF;AACA,UAAI,WAAW,aACX,WAAW,UAAU,UAAW,MAAM,IAAK,QAAQ,QAAQ,KAAK,IAChE;AAEJ,UAAI,WAAW,aAAa;AAE5B,UAAI,UAAU;AACZ,YAAI,QAAQ,QAAQ,QAAQ,GACxB,SAAS,CAAC,SAAS,SAAS,QAAQ,GACpC,UAAU,CAAC,SAAS,CAAC,UAAU,aAAa,QAAQ;AAExD,mBAAW;AACX,YAAI,SAAS,UAAU,SAAS;AAC9B,cAAI,QAAQ,QAAQ,GAAG;AACrB,uBAAW;AAAA,UACb,WACS,kBAAkB,QAAQ,GAAG;AACpC,uBAAW,UAAU,QAAQ;AAAA,UAC/B,WACS,QAAQ;AACf,uBAAW;AACX,uBAAW,YAAY,UAAU,IAAI;AAAA,UACvC,WACS,SAAS;AAChB,uBAAW;AACX,uBAAW,gBAAgB,UAAU,IAAI;AAAA,UAC3C,OACK;AACH,uBAAW,CAAC;AAAA,UACd;AAAA,QACF,WACS,cAAc,QAAQ,KAAK,YAAY,QAAQ,GAAG;AACzD,qBAAW;AACX,cAAI,YAAY,QAAQ,GAAG;AACzB,uBAAW,cAAc,QAAQ;AAAA,UACnC,WACS,CAAC,SAAS,QAAQ,KAAK,WAAW,QAAQ,GAAG;AACpD,uBAAW,gBAAgB,QAAQ;AAAA,UACrC;AAAA,QACF,OACK;AACH,qBAAW;AAAA,QACb;AAAA,MACF;AACA,UAAI,UAAU;AAEZ,cAAM,IAAI,UAAU,QAAQ;AAC5B,kBAAU,UAAU,UAAU,UAAU,YAAY,KAAK;AACzD,cAAM,QAAQ,EAAE,QAAQ;AAAA,MAC1B;AACA,uBAAiB,QAAQ,KAAK,QAAQ;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7FjB;AAAA;AAAA,QAAI,QAAQ;AAAZ,QACI,mBAAmB;AADvB,QAEI,UAAU;AAFd,QAGI,gBAAgB;AAHpB,QAII,WAAW;AAJf,QAKI,SAAS;AALb,QAMI,UAAU;AAad,aAAS,UAAU,QAAQ,QAAQ,UAAU,YAAY,OAAO;AAC9D,UAAI,WAAW,QAAQ;AACrB;AAAA,MACF;AACA,cAAQ,QAAQ,SAAS,UAAU,KAAK;AACtC,kBAAU,QAAQ,IAAI;AACtB,YAAI,SAAS,QAAQ,GAAG;AACtB,wBAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY,KAAK;AAAA,QAC3E,OACK;AACH,cAAI,WAAW,aACX,WAAW,QAAQ,QAAQ,GAAG,GAAG,UAAW,MAAM,IAAK,QAAQ,QAAQ,KAAK,IAC5E;AAEJ,cAAI,aAAa,QAAW;AAC1B,uBAAW;AAAA,UACb;AACA,2BAAiB,QAAQ,KAAK,QAAQ;AAAA,QACxC;AAAA,MACF,GAAG,MAAM;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzCjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,iBAAiB;AASrB,aAAS,eAAe,UAAU;AAChC,aAAO,SAAS,SAAS,QAAQ,SAAS;AACxC,YAAI,QAAQ,IACR,SAAS,QAAQ,QACjB,aAAa,SAAS,IAAI,QAAQ,SAAS,CAAC,IAAI,QAChD,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAEtC,qBAAc,SAAS,SAAS,KAAK,OAAO,cAAc,cACrD,UAAU,cACX;AAEJ,YAAI,SAAS,eAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG;AAC1D,uBAAa,SAAS,IAAI,SAAY;AACtC,mBAAS;AAAA,QACX;AACA,iBAAS,OAAO,MAAM;AACtB,eAAO,EAAE,QAAQ,QAAQ;AACvB,cAAI,SAAS,QAAQ,KAAK;AAC1B,cAAI,QAAQ;AACV,qBAAS,QAAQ,QAAQ,OAAO,UAAU;AAAA,UAC5C;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,iBAAiB;AAiCrB,QAAI,QAAQ,eAAe,SAAS,QAAQ,QAAQ,UAAU;AAC5D,gBAAU,QAAQ,QAAQ,QAAQ;AAAA,IACpC,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACtCjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,UAAU;AAFd,QAGI,WAAW;AAHf,QAII,QAAQ;AAYZ,aAAS,QAAQ,QAAQ,MAAM,OAAO,YAAY;AAChD,UAAI,CAAC,SAAS,MAAM,GAAG;AACrB,eAAO;AAAA,MACT;AACA,aAAO,SAAS,MAAM,MAAM;AAE5B,UAAI,QAAQ,IACR,SAAS,KAAK,QACd,YAAY,SAAS,GACrB,SAAS;AAEb,aAAO,UAAU,QAAQ,EAAE,QAAQ,QAAQ;AACzC,YAAI,MAAM,MAAM,KAAK,KAAK,CAAC,GACvB,WAAW;AAEf,YAAI,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,aAAa;AACvE,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,WAAW;AACtB,cAAI,WAAW,OAAO,GAAG;AACzB,qBAAW,aAAa,WAAW,UAAU,KAAK,MAAM,IAAI;AAC5D,cAAI,aAAa,QAAW;AAC1B,uBAAW,SAAS,QAAQ,IACxB,WACC,QAAQ,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UACxC;AAAA,QACF;AACA,oBAAY,QAAQ,KAAK,QAAQ;AACjC,iBAAS,OAAO,GAAG;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClDjB;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,UAAU;AADd,QAEI,WAAW;AAWf,aAAS,WAAW,QAAQ,OAAO,WAAW;AAC5C,UAAI,QAAQ,IACR,SAAS,MAAM,QACf,SAAS,CAAC;AAEd,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,OAAO,MAAM,KAAK,GAClB,QAAQ,QAAQ,QAAQ,IAAI;AAEhC,YAAI,UAAU,OAAO,IAAI,GAAG;AAC1B,kBAAQ,QAAQ,SAAS,MAAM,MAAM,GAAG,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,QAAQ;AAWZ,aAAS,SAAS,QAAQ,OAAO;AAC/B,aAAO,WAAW,QAAQ,OAAO,SAAS,OAAO,MAAM;AACrD,eAAO,MAAM,QAAQ,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,WAAW;AAmBf,QAAI,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC1C,aAAO,UAAU,OAAO,CAAC,IAAI,SAAS,QAAQ,KAAK;AAAA,IACrD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAI,WAAW;AAGf,QAAI,YAAY;AAmBhB,aAAS,SAAS,QAAQ;AACxB,UAAI,KAAK,EAAE;AACX,aAAO,SAAS,MAAM,IAAI;AAAA,IAC5B;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3BjB;AAAA;AASA,aAAS,cAAc,OAAO,QAAQ,YAAY;AAChD,UAAI,QAAQ,IACR,SAAS,MAAM,QACf,aAAa,OAAO,QACpB,SAAS,CAAC;AAEd,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,aAAa,OAAO,KAAK,IAAI;AACjD,mBAAW,QAAQ,MAAM,KAAK,GAAG,KAAK;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,gBAAgB;AAkBpB,aAAS,UAAU,OAAO,QAAQ;AAChC,aAAO,cAAc,SAAS,CAAC,GAAG,UAAU,CAAC,GAAG,WAAW;AAAA,IAC7D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvBjB,IAAAC,kBAAA;AAAA;AAEA,QAAI;AAEJ,QAAI,OAAO,cAAY,YAAY;AACjC,UAAI;AACF,iBAAS;AAAA,UACP,WAAW;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,KAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW;AAAA,QACb;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ;AACX,eAAS,OAAO;AAAA,IAClB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3CjB;AAAA;AAKA,WAAO,UAAU;AAEjB,aAAS,OAAO;AACd,UAAI,WAAW,CAAC;AAChB,eAAS,QAAQ,SAAS,QAAQ;AAClC,WAAK,YAAY;AAAA,IACnB;AAEA,SAAK,UAAU,UAAU,WAAW;AAClC,UAAI,WAAW,KAAK;AACpB,UAAI,QAAQ,SAAS;AACrB,UAAI,UAAU,UAAU;AACtB,eAAO,KAAK;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAK,UAAU,UAAU,SAAS,OAAO;AACvC,UAAI,WAAW,KAAK;AACpB,UAAI,MAAM,SAAS,MAAM,OAAO;AAC9B,eAAO,KAAK;AAAA,MACd;AACA,YAAM,QAAQ,SAAS;AACvB,eAAS,MAAM,QAAQ;AACvB,eAAS,QAAQ;AACjB,YAAM,QAAQ;AAAA,IAChB;AAEA,SAAK,UAAU,WAAW,WAAW;AACnC,UAAI,OAAO,CAAC;AACZ,UAAI,WAAW,KAAK;AACpB,UAAI,OAAO,SAAS;AACpB,aAAO,SAAS,UAAU;AACxB,aAAK,KAAK,KAAK,UAAU,MAAM,cAAc,CAAC;AAC9C,eAAO,KAAK;AAAA,MACd;AACA,aAAO,MAAM,KAAK,KAAK,IAAI,IAAI;AAAA,IACjC;AAEA,aAAS,OAAO,OAAO;AACrB,YAAM,MAAM,QAAQ,MAAM;AAC1B,YAAM,MAAM,QAAQ,MAAM;AAC1B,aAAO,MAAM;AACb,aAAO,MAAM;AAAA,IACf;AAEA,aAAS,eAAe,GAAG,GAAG;AAC5B,UAAI,MAAM,WAAW,MAAM,SAAS;AAClC,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACvDA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,QAAQ,oBAAsB;AAClC,QAAI,OAAO;AASX,WAAO,UAAU;AAEjB,QAAI,oBAAoB,EAAE,SAAS,CAAC;AAEpC,aAAS,UAAU,GAAG,UAAU;AAC9B,UAAI,EAAE,UAAU,KAAK,GAAG;AACtB,eAAO,CAAC;AAAA,MACV;AACA,UAAI,QAAQ,WAAW,GAAG,YAAY,iBAAiB;AACvD,UAAI,UAAU,YAAY,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO;AAGnE,aAAO,EAAE,QAAQ,EAAE,IAAI,SAAS,SAAS,GAAG;AAC1C,eAAO,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;AAAA,MAC5B,CAAC,GAAG,IAAI;AAAA,IACV;AAEA,aAAS,YAAY,GAAG,SAAS,SAAS;AACxC,UAAI,UAAU,CAAC;AACf,UAAI,UAAU,QAAQ,QAAQ,SAAS,CAAC;AACxC,UAAI,QAAQ,QAAQ,CAAC;AAErB,UAAI;AACJ,aAAO,EAAE,UAAU,GAAG;AACpB,eAAQ,QAAQ,MAAM,QAAQ,GAAM;AAAE,qBAAW,GAAG,SAAS,SAAS,KAAK;AAAA,QAAG;AAC9E,eAAQ,QAAQ,QAAQ,QAAQ,GAAI;AAAE,qBAAW,GAAG,SAAS,SAAS,KAAK;AAAA,QAAG;AAC9E,YAAI,EAAE,UAAU,GAAG;AACjB,mBAAS,IAAI,QAAQ,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3C,oBAAQ,QAAQ,CAAC,EAAE,QAAQ;AAC3B,gBAAI,OAAO;AACT,wBAAU,QAAQ,OAAO,WAAW,GAAG,SAAS,SAAS,OAAO,IAAI,CAAC;AACrE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG,SAAS,SAAS,OAAO,qBAAqB;AACnE,UAAI,UAAU,sBAAsB,CAAC,IAAI;AAEzC,QAAE,QAAQ,EAAE,QAAQ,MAAM,CAAC,GAAG,SAAS,MAAM;AAC3C,YAAI,SAAS,EAAE,KAAK,IAAI;AACxB,YAAI,SAAS,EAAE,KAAK,KAAK,CAAC;AAE1B,YAAI,qBAAqB;AACvB,kBAAQ,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAAA,QACvC;AAEA,eAAO,OAAO;AACd,qBAAa,SAAS,SAAS,MAAM;AAAA,MACvC,CAAC;AAED,QAAE,QAAQ,EAAE,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM;AAC5C,YAAI,SAAS,EAAE,KAAK,IAAI;AACxB,YAAI,IAAI,KAAK;AACb,YAAI,SAAS,EAAE,KAAK,CAAC;AACrB,eAAO,IAAI,KAAK;AAChB,qBAAa,SAAS,SAAS,MAAM;AAAA,MACvC,CAAC;AAED,QAAE,WAAW,MAAM,CAAC;AAEpB,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG,UAAU;AAC/B,UAAI,WAAW,IAAI,MAAM;AACzB,UAAI,QAAQ;AACZ,UAAI,SAAS;AAEb,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,iBAAS,QAAQ,GAAG,EAAE,GAAM,MAAM,GAAG,KAAK,EAAE,CAAC;AAAA,MAC/C,CAAC;AAID,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,aAAa,SAAS,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK;AAC5C,YAAI,SAAS,SAAS,CAAC;AACvB,YAAI,aAAa,aAAa;AAC9B,iBAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,UAAU;AACrC,iBAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,OAAO,MAAM;AAC1D,gBAAS,KAAK,IAAI,OAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,IAAI,KAAM,MAAM;AAAA,MAC/D,CAAC;AAED,UAAI,UAAU,EAAE,MAAM,SAAS,QAAQ,CAAC,EAAE,IAAI,WAAW;AAAE,eAAO,IAAI,KAAK;AAAA,MAAG,CAAC;AAC/E,UAAI,UAAU,QAAQ;AAEtB,QAAE,QAAQ,SAAS,MAAM,GAAG,SAAS,GAAG;AACtC,qBAAa,SAAS,SAAS,SAAS,KAAK,CAAC,CAAC;AAAA,MACjD,CAAC;AAED,aAAO,EAAE,OAAO,UAAU,SAAkB,QAAiB;AAAA,IAC/D;AAEA,aAAS,aAAa,SAAS,SAAS,OAAO;AAC7C,UAAI,CAAC,MAAM,KAAK;AACd,gBAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,MAC1B,WAAW,CAAC,MAAM,IAAI,GAAG;AACvB,gBAAQ,QAAQ,SAAS,CAAC,EAAE,QAAQ,KAAK;AAAA,MAC3C,OAAO;AACL,gBAAQ,MAAM,MAAM,MAAM,IAAI,IAAI,OAAO,EAAE,QAAQ,KAAK;AAAA,MAC1D;AAAA,IACF;AAAA;AAAA;;;ACrHA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,YAAY;AAEhB,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAEA,aAAS,IAAI,GAAG;AACd,UAAI,MAAO,EAAE,MAAM,EAAE,cAAc,WAC/B,UAAU,GAAG,SAAS,CAAC,CAAC,IACxB,OAAO,CAAC;AACZ,QAAE,QAAQ,KAAK,SAAS,GAAG;AACzB,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,UAAE,WAAW,CAAC;AACd,cAAM,cAAc,EAAE;AACtB,cAAM,WAAW;AACjB,UAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,SAAS,KAAK,CAAC;AAAA,MAC9C,CAAC;AAED,eAAS,SAASC,IAAG;AACnB,eAAO,SAAS,GAAG;AACjB,iBAAOA,GAAE,KAAK,CAAC,EAAE;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,OAAO,GAAG;AACjB,UAAI,MAAM,CAAC;AACX,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG;AACd,YAAI,EAAE,IAAI,SAAS,CAAC,GAAG;AACrB;AAAA,QACF;AACA,gBAAQ,CAAC,IAAI;AACb,cAAM,CAAC,IAAI;AACX,UAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,SAAS,GAAG;AACnC,cAAI,EAAE,IAAI,OAAO,EAAE,CAAC,GAAG;AACrB,gBAAI,KAAK,CAAC;AAAA,UACZ,OAAO;AACL,gBAAI,EAAE,CAAC;AAAA,UACT;AAAA,QACF,CAAC;AACD,eAAO,MAAM,CAAC;AAAA,MAChB;AAEA,QAAE,QAAQ,EAAE,MAAM,GAAG,GAAG;AACxB,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,GAAG;AACf,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,YAAI,MAAM,UAAU;AAClB,YAAE,WAAW,CAAC;AAEd,cAAI,cAAc,MAAM;AACxB,iBAAO,MAAM;AACb,iBAAO,MAAM;AACb,YAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,WAAW;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AClEA;AAAA;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,QAAQ,oBAAsB;AAElC,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAKA,aAAS,aAAa,GAAG,MAAM,OAAO,MAAM;AAC1C,UAAI;AACJ,SAAG;AACD,YAAI,EAAE,SAAS,IAAI;AAAA,MACrB,SAAS,EAAE,QAAQ,CAAC;AAEpB,YAAM,QAAQ;AACd,QAAE,QAAQ,GAAG,KAAK;AAClB,aAAO;AAAA,IACT;AAMA,aAAS,SAAS,GAAG;AACnB,UAAI,aAAa,IAAI,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;AAC/C,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,mBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC;AACtE,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,cAAc,WAAW,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ,EAAE;AACtE,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,mBAAW,QAAQ,EAAE,GAAG,EAAE,GAAG;AAAA,UAC3B,QAAQ,YAAY,SAAS,MAAM;AAAA,UACnC,QAAQ,KAAK,IAAI,YAAY,QAAQ,MAAM,MAAM;AAAA,QACnD,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,GAAG;AAC7B,UAAI,aAAa,IAAI,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC;AAC/E,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ;AACzB,qBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,QACjC;AAAA,MACF,CAAC;AACD,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,mBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,MACjC,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,YAAY,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AAC3C,YAAI,OAAO,CAAC;AACZ,UAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,SAAS,GAAG;AACnC,eAAK,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,KAAK,KAAK,EAAE,KAAK,CAAC,EAAE;AAAA,QAC3C,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AACD,aAAO,EAAE,UAAU,EAAE,MAAM,GAAG,SAAS;AAAA,IACzC;AAEA,aAAS,mBAAmB,GAAG;AAC7B,UAAI,YAAY,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AAC3C,YAAI,QAAQ,CAAC;AACb,UAAE,QAAQ,EAAE,QAAQ,CAAC,GAAG,SAAS,GAAG;AAClC,gBAAM,EAAE,CAAC,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,EAAE,KAAK,CAAC,EAAE;AAAA,QAC7C,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AACD,aAAO,EAAE,UAAU,EAAE,MAAM,GAAG,SAAS;AAAA,IACzC;AAMA,aAAS,cAAc,MAAM,OAAO;AAClC,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AAIb,UAAI,KAAK,MAAM,IAAI;AACnB,UAAI,KAAK,MAAM,IAAI;AACnB,UAAI,IAAI,KAAK,QAAQ;AACrB,UAAI,IAAI,KAAK,SAAS;AAEtB,UAAI,CAAC,MAAM,CAAC,IAAI;AACd,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,UAAI,IAAI;AACR,UAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AAEvC,YAAI,KAAK,GAAG;AACV,cAAI,CAAC;AAAA,QACP;AACA,aAAK,IAAI,KAAK;AACd,aAAK;AAAA,MACP,OAAO;AAEL,YAAI,KAAK,GAAG;AACV,cAAI,CAAC;AAAA,QACP;AACA,aAAK;AACL,aAAK,IAAI,KAAK;AAAA,MAChB;AAEA,aAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG;AAAA,IAChC;AAMA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,WAAW,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW;AAAE,eAAO,CAAC;AAAA,MAAG,CAAC;AACvE,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,OAAO,KAAK;AAChB,YAAI,CAAC,EAAE,YAAY,IAAI,GAAG;AACxB,mBAAS,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAC/B;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAMA,aAAS,eAAe,GAAG;AACzB,UAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,EAAE;AAAA,MAAM,CAAC,CAAC;AACxE,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,EAAE,IAAI,MAAM,MAAM,GAAG;AACvB,eAAK,QAAQ;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,GAAG;AAE3B,UAAI,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,EAAE;AAAA,MAAM,CAAC,CAAC;AAE3E,UAAI,SAAS,CAAC;AACd,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC,EAAE,OAAO;AAC5B,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI,CAAC;AAAA,QAClB;AACA,eAAO,IAAI,EAAE,KAAK,CAAC;AAAA,MACrB,CAAC;AAED,UAAI,QAAQ;AACZ,UAAI,iBAAiB,EAAE,MAAM,EAAE;AAC/B,QAAE,QAAQ,QAAQ,SAAS,IAAI,GAAG;AAChC,YAAI,EAAE,YAAY,EAAE,KAAK,IAAI,mBAAmB,GAAG;AACjD,YAAE;AAAA,QACJ,WAAW,OAAO;AAChB,YAAE,QAAQ,IAAI,SAAS,GAAG;AAAE,cAAE,KAAK,CAAC,EAAE,QAAQ;AAAA,UAAO,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,cAAc,GAAG,QAAQ,MAAM,OAAO;AAC7C,UAAI,OAAO;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,UAAI,UAAU,UAAU,GAAG;AACzB,aAAK,OAAO;AACZ,aAAK,QAAQ;AAAA,MACf;AACA,aAAO,aAAa,GAAG,UAAU,MAAM,MAAM;AAAA,IAC/C;AAEA,aAAS,QAAQ,GAAG;AAClB,aAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG;AACxC,YAAI,OAAO,EAAE,KAAK,CAAC,EAAE;AACrB,YAAI,CAAC,EAAE,YAAY,IAAI,GAAG;AACxB,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAOA,aAAS,UAAU,YAAY,IAAI;AACjC,UAAI,SAAS,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;AAChC,QAAE,QAAQ,YAAY,SAAS,OAAO;AACpC,YAAI,GAAG,KAAK,GAAG;AACb,iBAAO,IAAI,KAAK,KAAK;AAAA,QACvB,OAAO;AACL,iBAAO,IAAI,KAAK,KAAK;AAAA,QACvB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAMA,aAAS,KAAK,MAAM,IAAI;AACtB,UAAI,QAAQ,EAAE,IAAI;AAClB,UAAI;AACF,eAAO,GAAG;AAAA,MACZ,UAAE;AACA,gBAAQ,IAAI,OAAO,aAAa,EAAE,IAAI,IAAI,SAAS,IAAI;AAAA,MACzD;AAAA,IACF;AAEA,aAAS,OAAO,MAAM,IAAI;AACxB,aAAO,GAAG;AAAA,IACZ;AAAA;AAAA;;;AC7OA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAkBA,aAAS,IAAI,GAAG;AACd,QAAE,MAAM,EAAE,cAAc,CAAC;AACzB,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,MAAM;AAAE,sBAAc,GAAG,IAAI;AAAA,MAAG,CAAC;AAAA,IACjE;AAEA,aAAS,cAAc,GAAG,GAAG;AAC3B,UAAI,IAAI,EAAE;AACV,UAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,UAAI,IAAI,EAAE;AACV,UAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,UAAI,OAAO,EAAE;AACb,UAAI,YAAY,EAAE,KAAK,CAAC;AACxB,UAAI,YAAY,UAAU;AAE1B,UAAI,UAAU,QAAQ,EAAG;AAEzB,QAAE,WAAW,CAAC;AAEd,UAAI,OAAO,OAAO;AAClB,WAAK,IAAI,GAAG,EAAE,OAAO,QAAQ,OAAO,EAAE,GAAG,EAAE,OAAO;AAChD,kBAAU,SAAS,CAAC;AACpB,gBAAQ;AAAA,UACN,OAAO;AAAA,UAAG,QAAQ;AAAA,UAClB;AAAA,UAAsB,SAAS;AAAA,UAC/B,MAAM;AAAA,QACR;AACA,gBAAQ,KAAK,aAAa,GAAG,QAAQ,OAAO,IAAI;AAChD,YAAI,UAAU,WAAW;AACvB,gBAAM,QAAQ,UAAU;AACxB,gBAAM,SAAS,UAAU;AACzB,gBAAM,QAAQ;AACd,gBAAM,WAAW,UAAU;AAAA,QAC7B;AACA,UAAE,QAAQ,GAAG,OAAO,EAAE,QAAQ,UAAU,OAAO,GAAG,IAAI;AACtD,YAAI,MAAM,GAAG;AACX,YAAE,MAAM,EAAE,YAAY,KAAK,KAAK;AAAA,QAClC;AACA,YAAI;AAAA,MACN;AAEA,QAAE,QAAQ,GAAG,GAAG,EAAE,QAAQ,UAAU,OAAO,GAAG,IAAI;AAAA,IACpD;AAEA,aAAS,KAAK,GAAG;AACf,QAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,SAAS,GAAG;AAC3C,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,YAAY,KAAK;AACrB,YAAI;AACJ,UAAE,QAAQ,KAAK,SAAS,SAAS;AACjC,eAAO,KAAK,OAAO;AACjB,cAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AACrB,YAAE,WAAW,CAAC;AACd,oBAAU,OAAO,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAC9C,cAAI,KAAK,UAAU,cAAc;AAC/B,sBAAU,IAAI,KAAK;AACnB,sBAAU,IAAI,KAAK;AACnB,sBAAU,QAAQ,KAAK;AACvB,sBAAU,SAAS,KAAK;AAAA,UAC1B;AACA,cAAI;AACJ,iBAAO,EAAE,KAAK,CAAC;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;ACzFA,IAAAC,gBAAA;AAAA;AAAA;AAEA,QAAI,IAAI;AAER,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAuBA,aAAS,YAAY,GAAG;AACtB,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG;AACd,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,YAAI,EAAE,IAAI,SAAS,CAAC,GAAG;AACrB,iBAAO,MAAM;AAAA,QACf;AACA,gBAAQ,CAAC,IAAI;AAEb,YAAI,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,GAAG,SAAS,GAAG;AAChD,iBAAO,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AAAA,QAC9B,CAAC,CAAC;AAEF,YAAI,SAAS,OAAO;AAAA,QAChB,SAAS;AAAA,QACT,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AAEA,eAAQ,MAAM,OAAO;AAAA,MACvB;AAEA,QAAE,QAAQ,EAAE,QAAQ,GAAG,GAAG;AAAA,IAC5B;AAMA,aAAS,MAAM,GAAG,GAAG;AACnB,aAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACzD;AAAA;AAAA;;;AC9DA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,QAAQ,oBAAuB;AACnC,QAAI,QAAQ,gBAAkB;AAE9B,WAAO,UAAU;AA2BjB,aAAS,aAAa,GAAG;AACvB,UAAI,IAAI,IAAI,MAAM,EAAE,UAAU,MAAM,CAAC;AAGrC,UAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;AACvB,UAAI,OAAO,EAAE,UAAU;AACvB,QAAE,QAAQ,OAAO,CAAC,CAAC;AAEnB,UAAI,MAAM;AACV,aAAO,UAAU,GAAG,CAAC,IAAI,MAAM;AAC7B,eAAO,iBAAiB,GAAG,CAAC;AAC5B,gBAAQ,EAAE,QAAQ,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AAC3D,mBAAW,GAAG,GAAG,KAAK;AAAA,MACxB;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,UAAU,GAAG,GAAG;AACvB,eAAS,IAAI,GAAG;AACd,UAAE,QAAQ,EAAE,UAAU,CAAC,GAAG,SAAS,GAAG;AACpC,cAAI,QAAQ,EAAE,GACZ,IAAK,MAAM,QAAS,EAAE,IAAI;AAC5B,cAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG;AACjC,cAAE,QAAQ,GAAG,CAAC,CAAC;AACf,cAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;AAClB,gBAAI,CAAC;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH;AAEA,QAAE,QAAQ,EAAE,MAAM,GAAG,GAAG;AACxB,aAAO,EAAE,UAAU;AAAA,IACrB;AAMA,aAAS,iBAAiB,GAAG,GAAG;AAC9B,aAAO,EAAE,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG;AACpC,YAAI,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrC,iBAAO,MAAM,GAAG,CAAC;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,GAAG,GAAG,OAAO;AAC/B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,UAAE,KAAK,CAAC,EAAE,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA;AAAA;;;ACxFA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,eAAe;AACnB,QAAI,QAAQ,gBAAkB;AAC9B,QAAI,WAAW,gBAAkB;AACjC,QAAI,WAAW,oBAAuB,IAAI;AAC1C,QAAI,YAAY,oBAAuB,IAAI;AAC3C,QAAI,WAAW,eAAmB;AAElC,WAAO,UAAU;AAGjB,mBAAe,mBAAmB;AAClC,mBAAe,gBAAgB;AAC/B,mBAAe,eAAe;AAC9B,mBAAe,YAAY;AAC3B,mBAAe,YAAY;AAC3B,mBAAe,gBAAgB;AAmC/B,aAAS,eAAe,GAAG;AACzB,UAAI,SAAS,CAAC;AACd,eAAS,CAAC;AACV,UAAI,IAAI,aAAa,CAAC;AACtB,uBAAiB,CAAC;AAClB,oBAAc,GAAG,CAAC;AAElB,UAAI,GAAG;AACP,aAAQ,IAAI,UAAU,CAAC,GAAI;AACzB,YAAI,UAAU,GAAG,GAAG,CAAC;AACrB,sBAAc,GAAG,GAAG,GAAG,CAAC;AAAA,MAC1B;AAAA,IACF;AAKA,aAAS,cAAc,GAAG,GAAG;AAC3B,UAAI,KAAK,UAAU,GAAG,EAAE,MAAM,CAAC;AAC/B,WAAK,GAAG,MAAM,GAAG,GAAG,SAAS,CAAC;AAC9B,QAAE,QAAQ,IAAI,SAAS,GAAG;AACxB,uBAAe,GAAG,GAAG,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,aAAS,eAAe,GAAG,GAAG,OAAO;AACnC,UAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,UAAI,SAAS,SAAS;AACtB,QAAE,KAAK,OAAO,MAAM,EAAE,WAAW,aAAa,GAAG,GAAG,KAAK;AAAA,IAC3D;AAMA,aAAS,aAAa,GAAG,GAAG,OAAO;AACjC,UAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,UAAI,SAAS,SAAS;AAEtB,UAAI,cAAc;AAElB,UAAI,YAAY,EAAE,KAAK,OAAO,MAAM;AAEpC,UAAI,WAAW;AAEf,UAAI,CAAC,WAAW;AACd,sBAAc;AACd,oBAAY,EAAE,KAAK,QAAQ,KAAK;AAAA,MAClC;AAEA,iBAAW,UAAU;AAErB,QAAE,QAAQ,EAAE,UAAU,KAAK,GAAG,SAAS,GAAG;AACxC,YAAI,YAAY,EAAE,MAAM,OACtB,QAAQ,YAAY,EAAE,IAAI,EAAE;AAE9B,YAAI,UAAU,QAAQ;AACpB,cAAI,eAAe,cAAc,aAC/B,cAAc,EAAE,KAAK,CAAC,EAAE;AAE1B,sBAAY,eAAe,cAAc,CAAC;AAC1C,cAAI,WAAW,GAAG,OAAO,KAAK,GAAG;AAC/B,gBAAI,gBAAgB,EAAE,KAAK,OAAO,KAAK,EAAE;AACzC,wBAAY,eAAe,CAAC,gBAAgB;AAAA,UAC9C;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM,MAAM;AACpC,UAAI,UAAU,SAAS,GAAG;AACxB,eAAO,KAAK,MAAM,EAAE,CAAC;AAAA,MACvB;AACA,sBAAgB,MAAM,CAAC,GAAG,GAAG,IAAI;AAAA,IACnC;AAEA,aAAS,gBAAgB,MAAM,SAAS,SAAS,GAAG,QAAQ;AAC1D,UAAI,MAAM;AACV,UAAI,QAAQ,KAAK,KAAK,CAAC;AAEvB,cAAQ,CAAC,IAAI;AACb,QAAE,QAAQ,KAAK,UAAU,CAAC,GAAG,SAAS,GAAG;AACvC,YAAI,CAAC,EAAE,IAAI,SAAS,CAAC,GAAG;AACtB,oBAAU,gBAAgB,MAAM,SAAS,SAAS,GAAG,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAED,YAAM,MAAM;AACZ,YAAM,MAAM;AACZ,UAAI,QAAQ;AACV,cAAM,SAAS;AAAA,MACjB,OAAO;AAEL,eAAO,MAAM;AAAA,MACf;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM;AACvB,aAAO,EAAE,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG;AACtC,eAAO,KAAK,KAAK,CAAC,EAAE,WAAW;AAAA,MACjC,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,GAAG,GAAG,MAAM;AAC7B,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AAKb,UAAI,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG;AACpB,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,MACX;AAEA,UAAI,SAAS,EAAE,KAAK,CAAC;AACrB,UAAI,SAAS,EAAE,KAAK,CAAC;AACrB,UAAI,YAAY;AAChB,UAAI,OAAO;AAIX,UAAI,OAAO,MAAM,OAAO,KAAK;AAC3B,oBAAY;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,EAAE,OAAO,EAAE,MAAM,GAAG,SAASC,OAAM;AAClD,eAAO,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS,KAClD,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS;AAAA,MAC3D,CAAC;AAED,aAAO,EAAE,MAAM,YAAY,SAASA,OAAM;AAAE,eAAO,MAAM,GAAGA,KAAI;AAAA,MAAG,CAAC;AAAA,IACtE;AAEA,aAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACjC,UAAI,IAAI,EAAE;AACV,UAAI,IAAI,EAAE;AACV,QAAE,WAAW,GAAG,CAAC;AACjB,QAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACtB,uBAAiB,CAAC;AAClB,oBAAc,GAAG,CAAC;AAClB,kBAAY,GAAG,CAAC;AAAA,IAClB;AAEA,aAAS,YAAY,GAAG,GAAG;AACzB,UAAI,OAAO,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,eAAO,CAAC,EAAE,KAAK,CAAC,EAAE;AAAA,MAAQ,CAAC;AACtE,UAAI,KAAK,SAAS,GAAG,IAAI;AACzB,WAAK,GAAG,MAAM,CAAC;AACf,QAAE,QAAQ,IAAI,SAAS,GAAG;AACxB,YAAI,SAAS,EAAE,KAAK,CAAC,EAAE,QACrB,OAAO,EAAE,KAAK,GAAG,MAAM,GACvB,UAAU;AAEZ,YAAI,CAAC,MAAM;AACT,iBAAO,EAAE,KAAK,QAAQ,CAAC;AACvB,oBAAU;AAAA,QACZ;AAEA,UAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,MAAM,EAAE,QAAQ,UAAU,KAAK,SAAS,CAAC,KAAK;AAAA,MACxE,CAAC;AAAA,IACH;AAKA,aAAS,WAAW,MAAM,GAAG,GAAG;AAC9B,aAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,IAC1B;AAMA,aAAS,aAAa,MAAM,QAAQ,WAAW;AAC7C,aAAO,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,UAAU;AAAA,IAChE;AAAA;AAAA;;;ACzOA;AAAA;AAAA;AAEA,QAAI,WAAW;AACf,QAAI,cAAc,SAAS;AAC3B,QAAI,eAAe;AACnB,QAAI,iBAAiB;AAErB,WAAO,UAAU;AAqBjB,aAAS,KAAK,GAAG;AACf,cAAO,EAAE,MAAM,EAAE,QAAQ;AAAA,QACzB,KAAK;AAAmB,+BAAqB,CAAC;AAAG;AAAA,QACjD,KAAK;AAAc,0BAAgB,CAAC;AAAG;AAAA,QACvC,KAAK;AAAgB,4BAAkB,CAAC;AAAG;AAAA,QAC3C;AAAS,+BAAqB,CAAC;AAAA,MAC/B;AAAA,IACF;AAGA,QAAI,oBAAoB;AAExB,aAAS,gBAAgB,GAAG;AAC1B,kBAAY,CAAC;AACb,mBAAa,CAAC;AAAA,IAChB;AAEA,aAAS,qBAAqB,GAAG;AAC/B,qBAAe,CAAC;AAAA,IAClB;AAAA;AAAA;;;AC/CA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,aAAS,kBAAkB,GAAG;AAC5B,UAAI,gBAAgB,UAAU,CAAC;AAE/B,QAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,SAAS,GAAG;AAC3C,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,UAAU,KAAK;AACnB,YAAI,WAAW,SAAS,GAAG,eAAe,QAAQ,GAAG,QAAQ,CAAC;AAC9D,YAAI,OAAO,SAAS;AACpB,YAAI,MAAM,SAAS;AACnB,YAAI,UAAU;AACd,YAAI,QAAQ,KAAK,OAAO;AACxB,YAAI,YAAY;AAEhB,eAAO,MAAM,QAAQ,GAAG;AACtB,iBAAO,EAAE,KAAK,CAAC;AAEf,cAAI,WAAW;AACb,oBAAQ,QAAQ,KAAK,OAAO,OAAO,OAC5B,EAAE,KAAK,KAAK,EAAE,UAAU,KAAK,MAAM;AACxC;AAAA,YACF;AAEA,gBAAI,UAAU,KAAK;AACjB,0BAAY;AAAA,YACd;AAAA,UACF;AAEA,cAAI,CAAC,WAAW;AACd,mBAAO,UAAU,KAAK,SAAS,KACxB,EAAE,KAAK,QAAQ,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK,MAAM;AAC7D;AAAA,YACF;AACA,oBAAQ,KAAK,OAAO;AAAA,UACtB;AAEA,YAAE,UAAU,GAAG,KAAK;AACpB,cAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAIA,aAAS,SAAS,GAAG,eAAe,GAAG,GAAG;AACxC,UAAI,QAAQ,CAAC;AACb,UAAI,QAAQ,CAAC;AACb,UAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,UAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,UAAI;AACJ,UAAI;AAGJ,eAAS;AACT,SAAG;AACD,iBAAS,EAAE,OAAO,MAAM;AACxB,cAAM,KAAK,MAAM;AAAA,MACnB,SAAS,WACC,cAAc,MAAM,EAAE,MAAM,OAAO,MAAM,cAAc,MAAM,EAAE;AACzE,YAAM;AAGN,eAAS;AACT,cAAQ,SAAS,EAAE,OAAO,MAAM,OAAO,KAAK;AAC1C,cAAM,KAAK,MAAM;AAAA,MACnB;AAEA,aAAO,EAAE,MAAM,MAAM,OAAO,MAAM,QAAQ,CAAC,GAAG,IAAS;AAAA,IACzD;AAEA,aAAS,UAAU,GAAG;AACpB,UAAI,SAAS,CAAC;AACd,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG;AACd,YAAI,MAAM;AACV,UAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,GAAG;AAC5B,eAAO,CAAC,IAAI,EAAE,KAAU,KAAK,MAAM;AAAA,MACrC;AACA,QAAE,QAAQ,EAAE,SAAS,GAAG,GAAG;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACrFA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAyBA,aAAS,IAAI,GAAG;AACd,UAAI,OAAO,KAAK,aAAa,GAAG,QAAQ,CAAC,GAAG,OAAO;AACnD,UAAI,SAAS,WAAW,CAAC;AACzB,UAAI,SAAS,EAAE,IAAI,EAAE,OAAO,MAAM,CAAC,IAAI;AACvC,UAAI,UAAU,IAAI,SAAS;AAE3B,QAAE,MAAM,EAAE,cAAc;AAGxB,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,UAAE,KAAK,CAAC,EAAE,UAAU;AAAA,MAAS,CAAC;AAGjE,UAAI,SAAS,WAAW,CAAC,IAAI;AAG7B,QAAE,QAAQ,EAAE,SAAS,GAAG,SAAS,OAAO;AACtC,YAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MACrD,CAAC;AAID,QAAE,MAAM,EAAE,iBAAiB;AAAA,IAC7B;AAEA,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AACxD,UAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,UAAI,CAAC,SAAS,QAAQ;AACpB,YAAI,MAAM,MAAM;AACd,YAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAAA,QACnD;AACA;AAAA,MACF;AAEA,UAAI,MAAM,KAAK,cAAc,GAAG,KAAK;AACrC,UAAI,SAAS,KAAK,cAAc,GAAG,KAAK;AACxC,UAAI,QAAQ,EAAE,KAAK,CAAC;AAEpB,QAAE,UAAU,KAAK,CAAC;AAClB,YAAM,YAAY;AAClB,QAAE,UAAU,QAAQ,CAAC;AACrB,YAAM,eAAe;AAErB,QAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,YAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAEnD,YAAI,YAAY,EAAE,KAAK,KAAK;AAC5B,YAAI,WAAW,UAAU,YAAY,UAAU,YAAY;AAC3D,YAAI,cAAc,UAAU,eAAe,UAAU,eAAe;AACpE,YAAI,aAAa,UAAU,YAAY,SAAS,IAAI;AACpD,YAAI,SAAS,aAAa,cAAc,IAAI,SAAS,OAAO,CAAC,IAAI;AAEjE,UAAE,QAAQ,KAAK,UAAU;AAAA,UACvB,QAAQ;AAAA,UACR;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AAED,UAAE,QAAQ,aAAa,QAAQ;AAAA,UAC7B,QAAQ;AAAA,UACR;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AAChB,UAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,GAAG,QAAQ,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,MAChE;AAAA,IACF;AAEA,aAAS,WAAW,GAAG;AACrB,UAAI,SAAS,CAAC;AACd,eAASC,KAAI,GAAG,OAAO;AACrB,YAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,YAAI,YAAY,SAAS,QAAQ;AAC/B,YAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,YAAAA,KAAI,OAAO,QAAQ,CAAC;AAAA,UACtB,CAAC;AAAA,QACH;AACA,eAAO,CAAC,IAAI;AAAA,MACd;AACA,QAAE,QAAQ,EAAE,SAAS,GAAG,SAAS,GAAG;AAAE,QAAAA,KAAI,GAAG,CAAC;AAAA,MAAG,CAAC;AAClD,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS,KAAK,GAAG;AAC1C,eAAO,MAAM,EAAE,KAAK,CAAC,EAAE;AAAA,MACzB,GAAG,CAAC;AAAA,IACN;AAEA,aAAS,QAAQ,GAAG;AAClB,UAAI,aAAa,EAAE,MAAM;AACzB,QAAE,WAAW,WAAW,WAAW;AACnC,aAAO,WAAW;AAClB,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,aAAa;AACpB,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;ACnIA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,kBAAkB,GAAG;AAC5B,eAAS,IAAI,GAAG;AACd,YAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,SAAS,QAAQ;AACnB,YAAE,QAAQ,UAAU,GAAG;AAAA,QACzB;AAEA,YAAI,EAAE,IAAI,MAAM,SAAS,GAAG;AAC1B,eAAK,aAAa,CAAC;AACnB,eAAK,cAAc,CAAC;AACpB,mBAAS,OAAO,KAAK,SAAS,UAAU,KAAK,UAAU,GACrD,OAAO,SACP,EAAE,MAAM;AACR,0BAAc,GAAG,cAAc,OAAO,GAAG,MAAM,IAAI;AACnD,0BAAc,GAAG,eAAe,OAAO,GAAG,MAAM,IAAI;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AAEA,QAAE,QAAQ,EAAE,SAAS,GAAG,GAAG;AAAA,IAC7B;AAEA,aAAS,cAAc,GAAG,MAAM,QAAQ,IAAI,QAAQ,MAAM;AACxD,UAAI,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAY,YAAY,KAAK;AAChE,UAAI,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC;AAChC,UAAI,OAAO,KAAK,aAAa,GAAG,UAAU,OAAO,MAAM;AACvD,aAAO,IAAI,EAAE,IAAI,IAAI;AACrB,QAAE,UAAU,MAAM,EAAE;AACpB,UAAI,MAAM;AACR,UAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,EAAE,CAAC;AAAA,MACrC;AAAA,IACF;AAAA;AAAA;;;ACrCA;AAAA;AAAA;AAEA,QAAI,IAAI;AAER,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAEA,aAAS,OAAO,GAAG;AACjB,UAAI,UAAU,EAAE,MAAM,EAAE,QAAQ,YAAY;AAC5C,UAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,wBAAgB,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,KAAK,GAAG;AACf,UAAI,UAAU,EAAE,MAAM,EAAE,QAAQ,YAAY;AAC5C,UAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,iBAAS,CAAC;AAAA,MACZ;AAEA,UAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,eAAO,CAAC;AACR,wBAAgB,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,gBAAgB,GAAG;AAC1B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,2BAAmB,EAAE,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC;AACnE,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,2BAAmB,EAAE,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC;AAAA,IACrE;AAEA,aAAS,mBAAmB,OAAO;AACjC,UAAI,IAAI,MAAM;AACd,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS;AAAA,IACjB;AAEA,aAAS,SAAS,GAAG;AACnB,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,oBAAY,EAAE,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC;AAE5D,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAE,QAAQ,KAAK,QAAQ,WAAW;AAClC,YAAI,EAAE,IAAI,MAAM,GAAG,GAAG;AACpB,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,YAAY,OAAO;AAC1B,YAAM,IAAI,CAAC,MAAM;AAAA,IACnB;AAEA,aAAS,OAAO,GAAG;AACjB,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,kBAAU,EAAE,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC;AAE1D,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAE,QAAQ,KAAK,QAAQ,SAAS;AAChC,YAAI,EAAE,IAAI,MAAM,GAAG,GAAG;AACpB,oBAAU,IAAI;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,OAAO;AACxB,UAAI,IAAI,MAAM;AACd,YAAM,IAAI,MAAM;AAChB,YAAM,IAAI;AAAA,IACZ;AAAA;AAAA;;;ACvEA;AAAA;AAAA;AAEA,QAAI,IAAI;AAER,WAAO,UAAU;AAajB,aAAS,UAAU,GAAG;AACpB,UAAI,UAAU,CAAC;AACf,UAAI,cAAc,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS,GAAG;AAChD,eAAO,CAAC,EAAE,SAAS,CAAC,EAAE;AAAA,MACxB,CAAC;AACD,UAAI,UAAU,EAAE,IAAI,EAAE,IAAI,aAAa,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,EAAE;AAAA,MAAM,CAAC,CAAC;AAC9E,UAAI,SAAS,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC,GAAG,WAAW;AAAE,eAAO,CAAC;AAAA,MAAG,CAAC;AAElE,eAAS,IAAI,GAAG;AACd,YAAI,EAAE,IAAI,SAAS,CAAC,EAAG;AACvB,gBAAQ,CAAC,IAAI;AACb,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,eAAO,KAAK,IAAI,EAAE,KAAK,CAAC;AACxB,UAAE,QAAQ,EAAE,WAAW,CAAC,GAAG,GAAG;AAAA,MAChC;AAEA,UAAI,YAAY,EAAE,OAAO,aAAa,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,EAAE;AAAA,MAAM,CAAC;AAC5E,QAAE,QAAQ,WAAW,GAAG;AAExB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACrCA;AAAA;AAAA;AAEA,QAAI,IAAI;AAER,WAAO,UAAU;AAkBjB,aAAS,WAAW,GAAG,UAAU;AAC/B,UAAI,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,cAAM,mBAAmB,GAAG,SAAS,IAAE,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,GAAG,YAAY,YAAY;AAIrD,UAAI,WAAW,EAAE;AAAA,QAAU;AAAA,QACzB,EAAE,IAAI,YAAY,SAAU,GAAG,GAAG;AAAE,iBAAO;AAAA,QAAG,CAAC;AAAA,MAAC;AAClD,UAAI,eAAe,EAAE,QAAQ,EAAE,IAAI,YAAY,SAAS,GAAG;AACzD,eAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,GAAG,SAAS,GAAG;AAC/C,iBAAO,EAAE,KAAK,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC,EAAE,OAAO;AAAA,QACxD,CAAC,GAAG,KAAK;AAAA,MACX,CAAC,GAAG,IAAI;AAGR,UAAI,aAAa;AACjB,aAAO,aAAa,WAAW,OAAQ,gBAAe;AACtD,UAAI,WAAW,IAAI,aAAa;AAChC,oBAAc;AACd,UAAI,OAAO,EAAE,IAAI,IAAI,MAAM,QAAQ,GAAG,WAAW;AAAE,eAAO;AAAA,MAAG,CAAC;AAG9D,UAAI,KAAK;AACT,QAAE,QAAQ,aAAa,QAAQ,SAAS,OAAO;AAC7C,YAAI,QAAQ,MAAM,MAAM;AACxB,aAAK,KAAK,KAAK,MAAM;AACrB,YAAI,YAAY;AAChB,eAAO,QAAQ,GAAG;AAChB,cAAI,QAAQ,GAAG;AACb,yBAAa,KAAK,QAAQ,CAAC;AAAA,UAC7B;AACA,kBAAS,QAAQ,KAAM;AACvB,eAAK,KAAK,KAAK,MAAM;AAAA,QACvB;AACA,cAAM,MAAM,SAAS;AAAA,MACvB,CAAC,CAAC;AAEF,aAAO;AAAA,IACT;AAAA;AAAA;;;AClEA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,aAAS,WAAW,GAAG,SAAS;AAC9B,aAAO,EAAE,IAAI,SAAS,SAAS,GAAG;AAChC,YAAI,MAAM,EAAE,QAAQ,CAAC;AACrB,YAAI,CAAC,IAAI,QAAQ;AACf,iBAAO,EAAE,EAAK;AAAA,QAChB,OAAO;AACL,cAAI,SAAS,EAAE,OAAO,KAAK,SAAS,KAAK,GAAG;AAC1C,gBAAI,OAAO,EAAE,KAAK,CAAC,GACjB,QAAQ,EAAE,KAAK,EAAE,CAAC;AACpB,mBAAO;AAAA,cACL,KAAK,IAAI,MAAO,KAAK,SAAS,MAAM;AAAA,cACpC,QAAQ,IAAI,SAAS,KAAK;AAAA,YAC5B;AAAA,UACF,GAAG,EAAE,KAAK,GAAG,QAAQ,EAAE,CAAC;AAExB,iBAAO;AAAA,YACL;AAAA,YACA,YAAY,OAAO,MAAM,OAAO;AAAA,YAChC,QAAQ,OAAO;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAEA,QAAI,IAAI;AAER,WAAO,UAAU;AA2BjB,aAAS,iBAAiB,SAAS,IAAI;AACrC,UAAI,gBAAgB,CAAC;AACrB,QAAE,QAAQ,SAAS,SAAS,OAAO,GAAG;AACpC,YAAI,MAAM,cAAc,MAAM,CAAC,IAAI;AAAA,UACjC,UAAU;AAAA,UACV,MAAM,CAAC;AAAA,UACP,KAAK,CAAC;AAAA,UACN,IAAI,CAAC,MAAM,CAAC;AAAA,UACZ;AAAA,QACF;AACA,YAAI,CAAC,EAAE,YAAY,MAAM,UAAU,GAAG;AACpC,cAAI,aAAa,MAAM;AACvB,cAAI,SAAS,MAAM;AAAA,QACrB;AAAA,MACF,CAAC;AAED,QAAE,QAAQ,GAAG,MAAM,GAAG,SAAS,GAAG;AAChC,YAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,YAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,YAAI,CAAC,EAAE,YAAY,MAAM,KAAK,CAAC,EAAE,YAAY,MAAM,GAAG;AACpD,iBAAO;AACP,iBAAO,IAAI,KAAK,cAAc,EAAE,CAAC,CAAC;AAAA,QACpC;AAAA,MACF,CAAC;AAED,UAAI,YAAY,EAAE,OAAO,eAAe,SAAS,OAAO;AACtD,eAAO,CAAC,MAAM;AAAA,MAChB,CAAC;AAED,aAAO,mBAAmB,SAAS;AAAA,IACrC;AAEA,aAAS,mBAAmB,WAAW;AACrC,UAAI,UAAU,CAAC;AAEf,eAAS,SAAS,QAAQ;AACxB,eAAO,SAAS,QAAQ;AACtB,cAAI,OAAO,QAAQ;AACjB;AAAA,UACF;AACA,cAAI,EAAE,YAAY,OAAO,UAAU,KAC/B,EAAE,YAAY,OAAO,UAAU,KAC/B,OAAO,cAAc,OAAO,YAAY;AAC1C,yBAAa,QAAQ,MAAM;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAEA,eAAS,UAAU,QAAQ;AACzB,eAAO,SAAS,QAAQ;AACtB,iBAAO,IAAI,EAAE,KAAK,MAAM;AACxB,cAAI,EAAE,OAAO,aAAa,GAAG;AAC3B,sBAAU,KAAK,MAAM;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAEA,aAAO,UAAU,QAAQ;AACvB,YAAI,QAAQ,UAAU,IAAI;AAC1B,gBAAQ,KAAK,KAAK;AAClB,UAAE,QAAQ,MAAM,IAAI,EAAE,QAAQ,GAAG,SAAS,KAAK,CAAC;AAChD,UAAE,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,MACvC;AAEA,aAAO,EAAE;AAAA,QAAI,EAAE,OAAO,SAAS,SAASC,QAAO;AAAE,iBAAO,CAACA,OAAM;AAAA,QAAQ,CAAC;AAAA,QACtE,SAASA,QAAO;AACd,iBAAO,EAAE,KAAKA,QAAO,CAAC,MAAM,KAAK,cAAc,QAAQ,CAAC;AAAA,QAC1D;AAAA,MAAC;AAAA,IAEL;AAEA,aAAS,aAAa,QAAQ,QAAQ;AACpC,UAAI,MAAM;AACV,UAAI,SAAS;AAEb,UAAI,OAAO,QAAQ;AACjB,eAAO,OAAO,aAAa,OAAO;AAClC,kBAAU,OAAO;AAAA,MACnB;AAEA,UAAI,OAAO,QAAQ;AACjB,eAAO,OAAO,aAAa,OAAO;AAClC,kBAAU,OAAO;AAAA,MACnB;AAEA,aAAO,KAAK,OAAO,GAAG,OAAO,OAAO,EAAE;AACtC,aAAO,aAAa,MAAM;AAC1B,aAAO,SAAS;AAChB,aAAO,IAAI,KAAK,IAAI,OAAO,GAAG,OAAO,CAAC;AACtC,aAAO,SAAS;AAAA,IAClB;AAAA;AAAA;;;ACzHA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,KAAK,SAAS,WAAW;AAChC,UAAI,QAAQ,KAAK,UAAU,SAAS,SAAS,OAAO;AAClD,eAAO,EAAE,IAAI,OAAO,YAAY;AAAA,MAClC,CAAC;AACD,UAAI,WAAW,MAAM,KACnB,aAAa,EAAE,OAAO,MAAM,KAAK,SAAS,OAAO;AAAE,eAAO,CAAC,MAAM;AAAA,MAAG,CAAC,GACrE,KAAK,CAAC,GACN,MAAM,GACN,SAAS,GACT,UAAU;AAEZ,eAAS,KAAK,gBAAgB,CAAC,CAAC,SAAS,CAAC;AAE1C,gBAAU,kBAAkB,IAAI,YAAY,OAAO;AAEnD,QAAE,QAAQ,UAAU,SAAU,OAAO;AACnC,mBAAW,MAAM,GAAG;AACpB,WAAG,KAAK,MAAM,EAAE;AAChB,eAAO,MAAM,aAAa,MAAM;AAChC,kBAAU,MAAM;AAChB,kBAAU,kBAAkB,IAAI,YAAY,OAAO;AAAA,MACrD,CAAC;AAED,UAAI,SAAS,EAAE,IAAI,EAAE,QAAQ,IAAI,IAAI,EAAE;AACvC,UAAI,QAAQ;AACV,eAAO,aAAa,MAAM;AAC1B,eAAO,SAAS;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,kBAAkB,IAAI,YAAY,OAAO;AAChD,UAAI;AACJ,aAAO,WAAW,WAAW,OAAO,EAAE,KAAK,UAAU,GAAG,KAAK,OAAO;AAClE,mBAAW,IAAI;AACf,WAAG,KAAK,KAAK,EAAE;AACf;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,MAAM;AAC7B,aAAO,SAAS,QAAQ,QAAQ;AAC9B,YAAI,OAAO,aAAa,OAAO,YAAY;AACzC,iBAAO;AAAA,QACT,WAAW,OAAO,aAAa,OAAO,YAAY;AAChD,iBAAO;AAAA,QACT;AAEA,eAAO,CAAC,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;;;ACxDA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,aAAa;AACjB,QAAI,mBAAmB;AACvB,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,aAAa,GAAG,GAAG,IAAI,WAAW;AACzC,UAAI,UAAU,EAAE,SAAS,CAAC;AAC1B,UAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAI,KAAK,OAAO,KAAK,aAAa;AAClC,UAAI,KAAK,OAAO,KAAK,cAAa;AAClC,UAAI,YAAY,CAAC;AAEjB,UAAI,IAAI;AACN,kBAAU,EAAE,OAAO,SAAS,SAAS,GAAG;AACtC,iBAAO,MAAM,MAAM,MAAM;AAAA,QAC3B,CAAC;AAAA,MACH;AAEA,UAAI,cAAc,WAAW,GAAG,OAAO;AACvC,QAAE,QAAQ,aAAa,SAAS,OAAO;AACrC,YAAI,EAAE,SAAS,MAAM,CAAC,EAAE,QAAQ;AAC9B,cAAI,iBAAiB,aAAa,GAAG,MAAM,GAAG,IAAI,SAAS;AAC3D,oBAAU,MAAM,CAAC,IAAI;AACrB,cAAI,EAAE,IAAI,gBAAgB,YAAY,GAAG;AACvC,6BAAiB,OAAO,cAAc;AAAA,UACxC;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,UAAU,iBAAiB,aAAa,EAAE;AAC9C,sBAAgB,SAAS,SAAS;AAElC,UAAI,SAAS,KAAK,SAAS,SAAS;AAEpC,UAAI,IAAI;AACN,eAAO,KAAK,EAAE,QAAQ,CAAC,IAAI,OAAO,IAAI,EAAE,GAAG,IAAI;AAC/C,YAAI,EAAE,aAAa,EAAE,EAAE,QAAQ;AAC7B,cAAI,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,GACvC,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;AACvC,cAAI,CAAC,EAAE,IAAI,QAAQ,YAAY,GAAG;AAChC,mBAAO,aAAa;AACpB,mBAAO,SAAS;AAAA,UAClB;AACA,iBAAO,cAAc,OAAO,aAAa,OAAO,SAC3B,OAAO,QAAQ,OAAO,UAAU,OAAO,SAAS;AACrE,iBAAO,UAAU;AAAA,QACnB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,SAAS,WAAW;AAC3C,QAAE,QAAQ,SAAS,SAAS,OAAO;AACjC,cAAM,KAAK,EAAE,QAAQ,MAAM,GAAG,IAAI,SAAS,GAAG;AAC5C,cAAI,UAAU,CAAC,GAAG;AAChB,mBAAO,UAAU,CAAC,EAAE;AAAA,UACtB;AACA,iBAAO;AAAA,QACT,CAAC,GAAG,IAAI;AAAA,MACV,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,QAAQ,OAAO;AACvC,UAAI,CAAC,EAAE,YAAY,OAAO,UAAU,GAAG;AACrC,eAAO,cAAc,OAAO,aAAa,OAAO,SAC3B,MAAM,aAAa,MAAM,WACzB,OAAO,SAAS,MAAM;AAC3C,eAAO,UAAU,MAAM;AAAA,MACzB,OAAO;AACL,eAAO,aAAa,MAAM;AAC1B,eAAO,SAAS,MAAM;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;;;AC3EA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,QAAQ,oBAAuB;AAEnC,WAAO,UAAU;AAgCjB,aAAS,gBAAgB,GAAG,MAAM,cAAc;AAC9C,UAAI,OAAO,eAAe,CAAC,GACzB,SAAS,IAAI,MAAM,EAAE,UAAU,KAAK,CAAC,EAAE,SAAS,EAAE,KAAW,CAAC,EAC3D,oBAAoB,SAAS,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC;AAAA,MAAG,CAAC;AAE1D,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC,GACjB,SAAS,EAAE,OAAO,CAAC;AAErB,YAAI,KAAK,SAAS,QAAQ,KAAK,WAAW,QAAQ,QAAQ,KAAK,SAAS;AACtE,iBAAO,QAAQ,CAAC;AAChB,iBAAO,UAAU,GAAG,UAAU,IAAI;AAGlC,YAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,GAAG,SAAS,GAAG;AACxC,gBAAI,IAAI,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,GAC1B,OAAO,OAAO,KAAK,GAAG,CAAC,GACvB,SAAS,CAAC,EAAE,YAAY,IAAI,IAAI,KAAK,SAAS;AAChD,mBAAO,QAAQ,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,SAAS,OAAO,CAAC;AAAA,UAC5D,CAAC;AAED,cAAI,EAAE,IAAI,MAAM,SAAS,GAAG;AAC1B,mBAAO,QAAQ,GAAG;AAAA,cAChB,YAAY,KAAK,WAAW,IAAI;AAAA,cAChC,aAAa,KAAK,YAAY,IAAI;AAAA,YACpC,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,GAAG;AACzB,UAAI;AACJ,aAAO,EAAE,QAAS,IAAI,EAAE,SAAS,OAAO,CAAE,EAAE;AAC5C,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxEA;AAAA;AAAA,QAAI,IAAI;AAER,WAAO,UAAU;AAEjB,aAAS,uBAAuB,GAAG,IAAI,IAAI;AACzC,UAAI,OAAO,CAAC,GACV;AAEF,QAAE,QAAQ,IAAI,SAAS,GAAG;AACxB,YAAI,QAAQ,EAAE,OAAO,CAAC,GACpB,QACA;AACF,eAAO,OAAO;AACZ,mBAAS,EAAE,OAAO,KAAK;AACvB,cAAI,QAAQ;AACV,wBAAY,KAAK,MAAM;AACvB,iBAAK,MAAM,IAAI;AAAA,UACjB,OAAO;AACL,wBAAY;AACZ,uBAAW;AAAA,UACb;AACA,cAAI,aAAa,cAAc,OAAO;AACpC,eAAG,QAAQ,WAAW,KAAK;AAC3B;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IAyBH;AAAA;AAAA;;;ACpDA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,YAAY;AAChB,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,yBAAyB;AAC7B,QAAI,QAAQ,oBAAuB;AACnC,QAAI,OAAO;AAEX,WAAO,UAAU;AAiBjB,aAAS,MAAM,GAAG;AAChB,UAAI,UAAU,KAAK,QAAQ,CAAC,GAC1B,kBAAkB,iBAAiB,GAAG,EAAE,MAAM,GAAG,UAAU,CAAC,GAAG,SAAS,GACxE,gBAAgB,iBAAiB,GAAG,EAAE,MAAM,UAAU,GAAG,IAAI,EAAE,GAAG,UAAU;AAE9E,UAAI,WAAW,UAAU,CAAC;AAC1B,kBAAY,GAAG,QAAQ;AAEvB,UAAI,SAAS,OAAO,mBAClB;AAEF,eAAS,IAAI,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU;AAC3D,yBAAiB,IAAI,IAAI,kBAAkB,eAAe,IAAI,KAAK,CAAC;AAEpE,mBAAW,KAAK,iBAAiB,CAAC;AAClC,YAAI,KAAK,WAAW,GAAG,QAAQ;AAC/B,YAAI,KAAK,QAAQ;AACf,qBAAW;AACX,iBAAO,EAAE,UAAU,QAAQ;AAC3B,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,kBAAY,GAAG,IAAI;AAAA,IACrB;AAEA,aAAS,iBAAiB,GAAG,OAAO,cAAc;AAChD,aAAO,EAAE,IAAI,OAAO,SAAS,MAAM;AACjC,eAAO,gBAAgB,GAAG,MAAM,YAAY;AAAA,MAC9C,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,aAAa,WAAW;AAChD,UAAI,KAAK,IAAI,MAAM;AACnB,QAAE,QAAQ,aAAa,SAAS,IAAI;AAClC,YAAI,OAAO,GAAG,MAAM,EAAE;AACtB,YAAI,SAAS,aAAa,IAAI,MAAM,IAAI,SAAS;AACjD,UAAE,QAAQ,OAAO,IAAI,SAAS,GAAG,GAAG;AAClC,aAAG,KAAK,CAAC,EAAE,QAAQ;AAAA,QACrB,CAAC;AACD,+BAAuB,IAAI,IAAI,OAAO,EAAE;AAAA,MAC1C,CAAC;AAAA,IACH;AAEA,aAAS,YAAY,GAAG,UAAU;AAChC,QAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,UAAE,QAAQ,OAAO,SAAS,GAAG,GAAG;AAC9B,YAAE,KAAK,CAAC,EAAE,QAAQ;AAAA,QACpB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA;;;AC9EA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,QAAQ,oBAAuB;AACnC,QAAI,OAAO;AAOX,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAmBA,aAAS,mBAAmB,GAAG,UAAU;AACvC,UAAI,YAAY,CAAC;AAEjB,eAAS,WAAW,WAAW,OAAO;AACpC,YAGE,KAAK,GAGL,UAAU,GACV,kBAAkB,UAAU,QAC5B,WAAW,EAAE,KAAK,KAAK;AAEzB,UAAE,QAAQ,OAAO,SAAS,GAAG,GAAG;AAC9B,cAAI,IAAI,0BAA0B,GAAG,CAAC,GACpC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ;AAE7B,cAAI,KAAK,MAAM,UAAU;AACvB,cAAE,QAAQ,MAAM,MAAM,SAAS,IAAG,CAAC,GAAG,SAAS,UAAU;AACvD,gBAAE,QAAQ,EAAE,aAAa,QAAQ,GAAG,SAAS,GAAG;AAC9C,oBAAI,SAAS,EAAE,KAAK,CAAC,GACnB,OAAO,OAAO;AAChB,qBAAK,OAAO,MAAM,KAAK,SACnB,EAAE,OAAO,SAAS,EAAE,KAAK,QAAQ,EAAE,QAAQ;AAC7C,8BAAY,WAAW,GAAG,QAAQ;AAAA,gBACpC;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AACD,sBAAU,IAAI;AACd,iBAAK;AAAA,UACP;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAEA,QAAE,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,GAAG,UAAU;AACvC,UAAI,YAAY,CAAC;AAEjB,eAAS,KAAK,OAAO,UAAU,UAAU,iBAAiB,iBAAiB;AACzE,YAAI;AACJ,UAAE,QAAQ,EAAE,MAAM,UAAU,QAAQ,GAAG,SAAS,GAAG;AACjD,cAAI,MAAM,CAAC;AACX,cAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,cAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,SAAS,GAAG;AACvC,kBAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,kBAAI,MAAM,UACL,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,kBAAkB;AACpE,4BAAY,WAAW,GAAG,CAAC;AAAA,cAC7B;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAGA,eAAS,WAAW,OAAO,OAAO;AAChC,YAAI,eAAe,IACjB,cACA,WAAW;AAEb,UAAE,QAAQ,OAAO,SAAS,GAAG,gBAAgB;AAC3C,cAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,gBAAI,eAAe,EAAE,aAAa,CAAC;AACnC,gBAAI,aAAa,QAAQ;AACvB,6BAAe,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE;AACvC,mBAAK,OAAO,UAAU,gBAAgB,cAAc,YAAY;AAChE,yBAAW;AACX,6BAAe;AAAA,YACjB;AAAA,UACF;AACA,eAAK,OAAO,UAAU,MAAM,QAAQ,cAAc,MAAM,MAAM;AAAA,QAChE,CAAC;AAED,eAAO;AAAA,MACT;AAEA,QAAE,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,aAAS,0BAA0B,GAAG,GAAG;AACvC,UAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,eAAO,EAAE,KAAK,EAAE,aAAa,CAAC,GAAG,SAAS,GAAG;AAC3C,iBAAO,EAAE,KAAK,CAAC,EAAE;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS,YAAY,WAAW,GAAG,GAAG;AACpC,UAAI,IAAI,GAAG;AACT,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AAEA,UAAI,aAAa,UAAU,CAAC;AAC5B,UAAI,CAAC,YAAY;AACf,kBAAU,CAAC,IAAI,aAAa,CAAC;AAAA,MAC/B;AACA,iBAAW,CAAC,IAAI;AAAA,IAClB;AAEA,aAAS,YAAY,WAAW,GAAG,GAAG;AACpC,UAAI,IAAI,GAAG;AACT,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AACA,aAAO,EAAE,IAAI,UAAU,CAAC,GAAG,CAAC;AAAA,IAC9B;AAUA,aAAS,kBAAkB,GAAG,UAAU,WAAW,YAAY;AAC7D,UAAI,OAAO,CAAC,GACV,QAAQ,CAAC,GACT,MAAM,CAAC;AAKT,QAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,UAAE,QAAQ,OAAO,SAAS,GAAG,OAAO;AAClC,eAAK,CAAC,IAAI;AACV,gBAAM,CAAC,IAAI;AACX,cAAI,CAAC,IAAI;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAED,QAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,YAAI,UAAU;AACd,UAAE,QAAQ,OAAO,SAAS,GAAG;AAC3B,cAAI,KAAK,WAAW,CAAC;AACrB,cAAI,GAAG,QAAQ;AACb,iBAAK,EAAE,OAAO,IAAI,SAASC,IAAG;AAAE,qBAAO,IAAIA,EAAC;AAAA,YAAG,CAAC;AAChD,gBAAI,MAAM,GAAG,SAAS,KAAK;AAC3B,qBAAS,IAAI,KAAK,MAAM,EAAE,GAAG,KAAK,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG;AAC7D,kBAAI,IAAI,GAAG,CAAC;AACZ,kBAAI,MAAM,CAAC,MAAM,KACb,UAAU,IAAI,CAAC,KACf,CAAC,YAAY,WAAW,GAAG,CAAC,GAAG;AACjC,sBAAM,CAAC,IAAI;AACX,sBAAM,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;AAC3B,0BAAU,IAAI,CAAC;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,aAAO,EAAE,MAAY,MAAa;AAAA,IACpC;AAEA,aAAS,qBAAqB,GAAG,UAAU,MAAM,OAAO,YAAY;AAMlE,UAAI,KAAK,CAAC,GACR,SAAS,gBAAgB,GAAG,UAAU,MAAM,UAAU,GACtD,aAAa,aAAa,eAAe;AAE3C,eAAS,QAAQ,WAAW,eAAe;AACzC,YAAI,QAAQ,OAAO,MAAM;AACzB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,UAAU,CAAC;AACf,eAAO,MAAM;AACX,cAAI,QAAQ,IAAI,GAAG;AACjB,sBAAU,IAAI;AAAA,UAChB,OAAO;AACL,oBAAQ,IAAI,IAAI;AAChB,kBAAM,KAAK,IAAI;AACf,oBAAQ,MAAM,OAAO,cAAc,IAAI,CAAC;AAAA,UAC1C;AAEA,iBAAO,MAAM,IAAI;AAAA,QACnB;AAAA,MACF;AAGA,eAAS,MAAM,MAAM;AACnB,WAAG,IAAI,IAAI,OAAO,QAAQ,IAAI,EAAE,OAAO,SAAS,KAAK,GAAG;AACtD,iBAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,QAC/C,GAAG,CAAC;AAAA,MACN;AAGA,eAAS,MAAM,MAAM;AACnB,YAAI,MAAM,OAAO,SAAS,IAAI,EAAE,OAAO,SAAS,KAAK,GAAG;AACtD,iBAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,QAC/C,GAAG,OAAO,iBAAiB;AAE3B,YAAI,OAAO,EAAE,KAAK,IAAI;AACtB,YAAI,QAAQ,OAAO,qBAAqB,KAAK,eAAe,YAAY;AACtE,aAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG;AAAA,QACnC;AAAA,MACF;AAEA,cAAQ,OAAO,OAAO,aAAa,KAAK,MAAM,CAAC;AAC/C,cAAQ,OAAO,OAAO,WAAW,KAAK,MAAM,CAAC;AAG7C,QAAE,QAAQ,OAAO,SAAS,GAAG;AAC3B,WAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,MACpB,CAAC;AAED,aAAO;AAAA,IACT;AAGA,aAAS,gBAAgB,GAAG,UAAU,MAAM,YAAY;AACtD,UAAI,aAAa,IAAI,MAAM,GACzB,aAAa,EAAE,MAAM,GACrB,QAAQ,IAAI,WAAW,SAAS,WAAW,SAAS,UAAU;AAEhE,QAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,YAAI;AACJ,UAAE,QAAQ,OAAO,SAAS,GAAG;AAC3B,cAAI,QAAQ,KAAK,CAAC;AAClB,qBAAW,QAAQ,KAAK;AACxB,cAAI,GAAG;AACL,gBAAI,QAAQ,KAAK,CAAC,GAChB,UAAU,WAAW,KAAK,OAAO,KAAK;AACxC,uBAAW,QAAQ,OAAO,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,UACzE;AACA,cAAI;AAAA,QACN,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAKA,aAAS,2BAA2B,GAAG,KAAK;AAC1C,aAAO,EAAE,MAAM,EAAE,OAAO,GAAG,GAAG,SAAU,IAAI;AAC1C,YAAI,MAAM,OAAO;AACjB,YAAI,MAAM,OAAO;AAEjB,UAAE,MAAM,IAAI,SAAU,GAAG,GAAG;AAC1B,cAAI,YAAY,MAAM,GAAG,CAAC,IAAI;AAE9B,gBAAM,KAAK,IAAI,IAAI,WAAW,GAAG;AACjC,gBAAM,KAAK,IAAI,IAAI,WAAW,GAAG;AAAA,QACnC,CAAC;AAED,eAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AASA,aAAS,iBAAiB,KAAK,SAAS;AACtC,UAAI,cAAc,EAAE,OAAO,OAAO,GAChC,aAAa,EAAE,IAAI,WAAW,GAC9B,aAAa,EAAE,IAAI,WAAW;AAEhC,QAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,SAAS,MAAM;AACnC,UAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,SAAS,OAAO;AACpC,cAAI,YAAY,OAAO,OACrB,KAAK,IAAI,SAAS,GAClB;AACF,cAAI,OAAO,QAAS;AAEpB,cAAI,SAAS,EAAE,OAAO,EAAE;AACxB,kBAAQ,UAAU,MAAM,aAAa,EAAE,IAAI,MAAM,IAAI,aAAa,EAAE,IAAI,MAAM;AAE9E,cAAI,OAAO;AACT,gBAAI,SAAS,IAAI,EAAE,UAAU,IAAI,SAAS,GAAG;AAAE,qBAAO,IAAI;AAAA,YAAO,CAAC;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,aAAS,QAAQ,KAAK,OAAO;AAC3B,aAAO,EAAE,UAAU,IAAI,IAAI,SAAS,QAAQ,GAAG;AAC7C,YAAI,OAAO;AACT,iBAAO,IAAI,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,QACnC,OAAO;AACL,cAAI,KAAK,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC,CAAC;AAC/B,kBAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,GAAG;AACpB,UAAI,WAAW,KAAK,iBAAiB,CAAC;AACtC,UAAI,YAAY,EAAE;AAAA,QAChB,mBAAmB,GAAG,QAAQ;AAAA,QAC9B,mBAAmB,GAAG,QAAQ;AAAA,MAAC;AAEjC,UAAI,MAAM,CAAC;AACX,UAAI;AACJ,QAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,SAAS,MAAM;AACnC,2BAAmB,SAAS,MAAM,WAAW,EAAE,OAAO,QAAQ,EAAE,QAAQ;AACxE,UAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,SAAS,OAAO;AACpC,cAAI,UAAU,KAAK;AACjB,+BAAmB,EAAE,IAAI,kBAAkB,SAAS,OAAO;AACzD,qBAAO,EAAE,OAAO,KAAK,EAAE,QAAQ;AAAA,YACjC,CAAC;AAAA,UACH;AAEA,cAAI,cAAc,SAAS,MAAM,EAAE,eAAe,EAAE,YAAY,KAAK,CAAC;AACtE,cAAI,QAAQ,kBAAkB,GAAG,kBAAkB,WAAW,UAAU;AACxE,cAAI,KAAK;AAAA,YAAqB;AAAA,YAAG;AAAA,YAC/B,MAAM;AAAA,YAAM,MAAM;AAAA,YAAO,UAAU;AAAA,UAAG;AACxC,cAAI,UAAU,KAAK;AACjB,iBAAK,EAAE,UAAU,IAAI,SAAS,GAAG;AAAE,qBAAO,CAAC;AAAA,YAAG,CAAC;AAAA,UACjD;AACA,cAAI,OAAO,KAAK,IAAI;AAAA,QACtB,CAAC;AAAA,MACH,CAAC;AAED,UAAI,gBAAgB,2BAA2B,GAAG,GAAG;AACrD,uBAAiB,KAAK,aAAa;AACnC,aAAO,QAAQ,KAAK,EAAE,MAAM,EAAE,KAAK;AAAA,IACrC;AAEA,aAAS,IAAI,SAAS,SAAS,YAAY;AACzC,aAAO,SAAS,GAAG,GAAG,GAAG;AACvB,YAAI,SAAS,EAAE,KAAK,CAAC;AACrB,YAAI,SAAS,EAAE,KAAK,CAAC;AACrB,YAAI,MAAM;AACV,YAAI;AAEJ,eAAO,OAAO,QAAQ;AACtB,YAAI,EAAE,IAAI,QAAQ,UAAU,GAAG;AAC7B,kBAAQ,OAAO,SAAS,YAAY,GAAG;AAAA,YACvC,KAAK;AAAK,sBAAQ,CAAC,OAAO,QAAQ;AAAG;AAAA,YACrC,KAAK;AAAK,sBAAQ,OAAO,QAAQ;AAAG;AAAA,UACpC;AAAA,QACF;AACA,YAAI,OAAO;AACT,iBAAO,aAAa,QAAQ,CAAC;AAAA,QAC/B;AACA,gBAAQ;AAER,gBAAQ,OAAO,QAAQ,UAAU,WAAW;AAC5C,gBAAQ,OAAO,QAAQ,UAAU,WAAW;AAE5C,eAAO,OAAO,QAAQ;AACtB,YAAI,EAAE,IAAI,QAAQ,UAAU,GAAG;AAC7B,kBAAQ,OAAO,SAAS,YAAY,GAAG;AAAA,YACvC,KAAK;AAAK,sBAAQ,OAAO,QAAQ;AAAG;AAAA,YACpC,KAAK;AAAK,sBAAQ,CAAC,OAAO,QAAQ;AAAG;AAAA,UACrC;AAAA,QACF;AACA,YAAI,OAAO;AACT,iBAAO,aAAa,QAAQ,CAAC;AAAA,QAC/B;AACA,gBAAQ;AAER,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,aAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACnB;AAAA;AAAA;;;AClaA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,OAAO;AACX,QAAI,YAAY,aAAgB;AAEhC,WAAO,UAAU;AAEjB,aAAS,SAAS,GAAG;AACnB,UAAI,KAAK,mBAAmB,CAAC;AAE7B,gBAAU,CAAC;AACX,QAAE,QAAQ,UAAU,CAAC,GAAG,SAAS,GAAG,GAAG;AACrC,UAAE,KAAK,CAAC,EAAE,IAAI;AAAA,MAChB,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,GAAG;AACpB,UAAI,WAAW,KAAK,iBAAiB,CAAC;AACtC,UAAI,UAAU,EAAE,MAAM,EAAE;AACxB,UAAI,QAAQ;AACZ,QAAE,QAAQ,UAAU,SAAS,OAAO;AAClC,YAAI,YAAY,EAAE,IAAI,EAAE,IAAI,OAAO,SAAS,GAAG;AAAE,iBAAO,EAAE,KAAK,CAAC,EAAE;AAAA,QAAQ,CAAC,CAAC;AAC5E,UAAE,QAAQ,OAAO,SAAS,GAAG;AAC3B,YAAE,KAAK,CAAC,EAAE,IAAI,QAAQ,YAAY;AAAA,QACpC,CAAC;AACD,iBAAS,YAAY;AAAA,MACvB,CAAC;AAAA,IACH;AAAA;AAAA;;;AC5BA;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,iBAAiB,eAAkB;AACvC,QAAI,oBAAoB;AACxB,QAAI,mBAAmB,eAAkB;AACzC,QAAI,eAAe;AACnB,QAAI,oBAAoB;AACxB,QAAI,mBAAmB;AACvB,QAAI,QAAQ;AACZ,QAAI,WAAW;AACf,QAAI,OAAO;AACX,QAAI,QAAQ,oBAAsB;AAElC,WAAO,UAAU;AAEjB,aAAS,OAAO,GAAG,MAAM;AACvB,UAAI,OAAO,QAAQ,KAAK,cAAc,KAAK,OAAO,KAAK;AACvD,WAAK,UAAU,WAAW;AACxB,YAAI,cACF,KAAK,sBAAsB,WAAW;AAAE,iBAAO,iBAAiB,CAAC;AAAA,QAAG,CAAC;AACvE,aAAK,eAAsB,WAAW;AAAE,oBAAU,aAAa,IAAI;AAAA,QAAG,CAAC;AACvE,aAAK,sBAAsB,WAAW;AAAE,2BAAiB,GAAG,WAAW;AAAA,QAAG,CAAC;AAAA,MAC7E,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,GAAG,MAAM;AAC1B,WAAK,8BAA8B,WAAW;AAAE,+BAAuB,CAAC;AAAA,MAAG,CAAC;AAC5E,WAAK,uBAA8B,WAAW;AAAE,wBAAgB,CAAC;AAAA,MAAG,CAAC;AACrE,WAAK,eAA8B,WAAW;AAAE,gBAAQ,IAAI,CAAC;AAAA,MAAG,CAAC;AACjE,WAAK,wBAA8B,WAAW;AAAE,qBAAa,IAAI,CAAC;AAAA,MAAG,CAAC;AACtE,WAAK,YAA8B,WAAW;AAAE,aAAK,KAAK,mBAAmB,CAAC,CAAC;AAAA,MAAG,CAAC;AACnF,WAAK,8BAA8B,WAAW;AAAE,+BAAuB,CAAC;AAAA,MAAG,CAAC;AAC5E,WAAK,wBAA8B,WAAW;AAAE,yBAAiB,CAAC;AAAA,MAAG,CAAC;AACtE,WAAK,4BAA8B,WAAW;AAAE,qBAAa,QAAQ,CAAC;AAAA,MAAG,CAAC;AAC1E,WAAK,sBAA8B,WAAW;AAAE,uBAAe,CAAC;AAAA,MAAG,CAAC;AACpE,WAAK,wBAA8B,WAAW;AAAE,yBAAiB,CAAC;AAAA,MAAG,CAAC;AACtE,WAAK,8BAA8B,WAAW;AAAE,+BAAuB,CAAC;AAAA,MAAG,CAAC;AAC5E,WAAK,qBAA8B,WAAW;AAAE,kBAAU,IAAI,CAAC;AAAA,MAAG,CAAC;AACnE,WAAK,yBAA8B,WAAW;AAAE,0BAAkB,CAAC;AAAA,MAAG,CAAC;AACvE,WAAK,yBAA8B,WAAW;AAAE,0BAAkB,CAAC;AAAA,MAAG,CAAC;AACvE,WAAK,aAA8B,WAAW;AAAE,cAAM,CAAC;AAAA,MAAG,CAAC;AAC3D,WAAK,uBAA8B,WAAW;AAAE,wBAAgB,CAAC;AAAA,MAAG,CAAC;AACrE,WAAK,8BAA8B,WAAW;AAAE,yBAAiB,OAAO,CAAC;AAAA,MAAG,CAAC;AAC7E,WAAK,gBAA8B,WAAW;AAAE,iBAAS,CAAC;AAAA,MAAG,CAAC;AAC9D,WAAK,yBAA8B,WAAW;AAAE,0BAAkB,CAAC;AAAA,MAAG,CAAC;AACvE,WAAK,yBAA8B,WAAW;AAAE,0BAAkB,CAAC;AAAA,MAAG,CAAC;AACvE,WAAK,sBAA8B,WAAW;AAAE,kBAAU,KAAK,CAAC;AAAA,MAAG,CAAC;AACpE,WAAK,4BAA8B,WAAW;AAAE,6BAAqB,CAAC;AAAA,MAAG,CAAC;AAC1E,WAAK,4BAA8B,WAAW;AAAE,yBAAiB,KAAK,CAAC;AAAA,MAAG,CAAC;AAC3E,WAAK,sBAA8B,WAAW;AAAE,uBAAe,CAAC;AAAA,MAAG,CAAC;AACpE,WAAK,4BAA8B,WAAW;AAAE,6BAAqB,CAAC;AAAA,MAAG,CAAC;AAC1E,WAAK,qBAA8B,WAAW;AAAE,sCAA8B,CAAC;AAAA,MAAG,CAAC;AACnF,WAAK,oBAA8B,WAAW;AAAE,gBAAQ,KAAK,CAAC;AAAA,MAAG,CAAC;AAAA,IACpE;AAQA,aAAS,iBAAiB,YAAY,aAAa;AACjD,QAAE,QAAQ,WAAW,MAAM,GAAG,SAAS,GAAG;AACxC,YAAI,aAAa,WAAW,KAAK,CAAC;AAClC,YAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,YAAI,YAAY;AACd,qBAAW,IAAI,YAAY;AAC3B,qBAAW,IAAI,YAAY;AAE3B,cAAI,YAAY,SAAS,CAAC,EAAE,QAAQ;AAClC,uBAAW,QAAQ,YAAY;AAC/B,uBAAW,SAAS,YAAY;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAED,QAAE,QAAQ,WAAW,MAAM,GAAG,SAAS,GAAG;AACxC,YAAI,aAAa,WAAW,KAAK,CAAC;AAClC,YAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,mBAAW,SAAS,YAAY;AAChC,YAAI,EAAE,IAAI,aAAa,GAAG,GAAG;AAC3B,qBAAW,IAAI,YAAY;AAC3B,qBAAW,IAAI,YAAY;AAAA,QAC7B;AAAA,MACF,CAAC;AAED,iBAAW,MAAM,EAAE,QAAQ,YAAY,MAAM,EAAE;AAC/C,iBAAW,MAAM,EAAE,SAAS,YAAY,MAAM,EAAE;AAAA,IAClD;AAEA,QAAI,gBAAgB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;AAC1E,QAAI,gBAAgB,EAAE,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,KAAK;AAC3E,QAAI,aAAa,CAAC,aAAa,UAAU,WAAW,OAAO;AAC3D,QAAI,eAAe,CAAC,SAAS,QAAQ;AACrC,QAAI,eAAe,EAAE,OAAO,GAAG,QAAQ,EAAE;AACzC,QAAI,eAAe,CAAC,UAAU,UAAU,SAAS,UAAU,aAAa;AACxE,QAAI,eAAe;AAAA,MACjB,QAAQ;AAAA,MAAG,QAAQ;AAAA,MAAG,OAAO;AAAA,MAAG,QAAQ;AAAA,MACxC,aAAa;AAAA,MAAI,UAAU;AAAA,IAC7B;AACA,QAAI,YAAY,CAAC,UAAU;AAQ3B,aAAS,iBAAiB,YAAY;AACpC,UAAI,IAAI,IAAI,MAAM,EAAE,YAAY,MAAM,UAAU,KAAK,CAAC;AACtD,UAAI,QAAQ,aAAa,WAAW,MAAM,CAAC;AAE3C,QAAE,SAAS,EAAE;AAAA,QAAM,CAAC;AAAA,QAClB;AAAA,QACA,kBAAkB,OAAO,aAAa;AAAA,QACtC,EAAE,KAAK,OAAO,UAAU;AAAA,MAAC,CAAC;AAE5B,QAAE,QAAQ,WAAW,MAAM,GAAG,SAAS,GAAG;AACxC,YAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,UAAE,QAAQ,GAAG,EAAE,SAAS,kBAAkB,MAAM,YAAY,GAAG,YAAY,CAAC;AAC5E,UAAE,UAAU,GAAG,WAAW,OAAO,CAAC,CAAC;AAAA,MACrC,CAAC;AAED,QAAE,QAAQ,WAAW,MAAM,GAAG,SAAS,GAAG;AACxC,YAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,UAAE,QAAQ,GAAG,EAAE;AAAA,UAAM,CAAC;AAAA,UACpB;AAAA,UACA,kBAAkB,MAAM,YAAY;AAAA,UACpC,EAAE,KAAK,MAAM,SAAS;AAAA,QAAC,CAAC;AAAA,MAC5B,CAAC;AAED,aAAO;AAAA,IACT;AAUA,aAAS,uBAAuB,GAAG;AACjC,UAAI,QAAQ,EAAE,MAAM;AACpB,YAAM,WAAW;AACjB,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,UAAU;AACf,YAAI,KAAK,SAAS,YAAY,MAAM,KAAK;AACvC,cAAI,MAAM,YAAY,QAAQ,MAAM,YAAY,MAAM;AACpD,iBAAK,SAAS,KAAK;AAAA,UACrB,OAAO;AACL,iBAAK,UAAU,KAAK;AAAA,UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAQA,aAAS,uBAAuB,GAAG;AACjC,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,cAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,cAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,cAAI,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,MAAM,EAAK;AACzD,eAAK,aAAa,GAAG,cAAc,OAAO,KAAK;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,UAAU;AACd,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,WAAW;AAClB,eAAK,UAAU,EAAE,KAAK,KAAK,SAAS,EAAE;AACtC,eAAK,UAAU,EAAE,KAAK,KAAK,YAAY,EAAE;AACzC,oBAAU,EAAE,IAAI,SAAS,KAAK,OAAO;AAAA,QACvC;AAAA,MACF,CAAC;AACD,QAAE,MAAM,EAAE,UAAU;AAAA,IACtB;AAEA,aAAS,uBAAuB,GAAG;AACjC,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,UAAU,cAAc;AAC/B,YAAE,KAAK,KAAK,CAAC,EAAE,YAAY,KAAK;AAChC,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,eAAe,GAAG;AACzB,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO;AACX,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO;AACX,UAAI,aAAa,EAAE,MAAM;AACzB,UAAI,UAAU,WAAW,WAAW;AACpC,UAAI,UAAU,WAAW,WAAW;AAEpC,eAAS,YAAY,OAAO;AAC1B,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM;AACd,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,MACjC;AAEA,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAAE,oBAAY,EAAE,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC;AAC5D,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,EAAE,IAAI,MAAM,GAAG,GAAG;AACpB,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AAED,cAAQ;AACR,cAAQ;AAER,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACZ,CAAC;AAED,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAE,QAAQ,KAAK,QAAQ,SAAS,GAAG;AACjC,YAAE,KAAK;AACP,YAAE,KAAK;AAAA,QACT,CAAC;AACD,YAAI,EAAE,IAAI,MAAM,GAAG,GAAG;AAAE,eAAK,KAAK;AAAA,QAAM;AACxC,YAAI,EAAE,IAAI,MAAM,GAAG,GAAG;AAAE,eAAK,KAAK;AAAA,QAAM;AAAA,MAC1C,CAAC;AAED,iBAAW,QAAQ,OAAO,OAAO;AACjC,iBAAW,SAAS,OAAO,OAAO;AAAA,IACpC;AAEA,aAAS,qBAAqB,GAAG;AAC/B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,YAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,YAAI,IAAI;AACR,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,CAAC;AACf,eAAK;AACL,eAAK;AAAA,QACP,OAAO;AACL,eAAK,KAAK,OAAO,CAAC;AAClB,eAAK,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,QACzC;AACA,aAAK,OAAO,QAAQ,KAAK,cAAc,OAAO,EAAE,CAAC;AACjD,aAAK,OAAO,KAAK,KAAK,cAAc,OAAO,EAAE,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AAEA,aAAS,qBAAqB,GAAG;AAC/B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,EAAE,IAAI,MAAM,GAAG,GAAG;AACpB,cAAI,KAAK,aAAa,OAAO,KAAK,aAAa,KAAK;AAClD,iBAAK,SAAS,KAAK;AAAA,UACrB;AACA,kBAAQ,KAAK,UAAU;AAAA,YACvB,KAAK;AAAK,mBAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAAa;AAAA,YACvD,KAAK;AAAK,mBAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAAa;AAAA,UACvD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,GAAG;AACxC,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,UAAU;AACjB,eAAK,OAAO,QAAQ;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,GAAG;AAC5B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,EAAE,SAAS,CAAC,EAAE,QAAQ;AACxB,cAAI,OAAO,EAAE,KAAK,CAAC;AACnB,cAAI,IAAI,EAAE,KAAK,KAAK,SAAS;AAC7B,cAAI,IAAI,EAAE,KAAK,KAAK,YAAY;AAChC,cAAI,IAAI,EAAE,KAAK,EAAE,KAAK,KAAK,UAAU,CAAC;AACtC,cAAI,IAAI,EAAE,KAAK,EAAE,KAAK,KAAK,WAAW,CAAC;AAEvC,eAAK,QAAQ,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAC/B,eAAK,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC,eAAK,IAAI,EAAE,IAAI,KAAK,QAAQ;AAC5B,eAAK,IAAI,EAAE,IAAI,KAAK,SAAS;AAAA,QAC/B;AAAA,MACF,CAAC;AAED,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,GAAG;AAC1B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,EAAE,MAAM,EAAE,GAAG;AACf,cAAI,OAAO,EAAE,KAAK,EAAE,CAAC;AACrB,cAAI,CAAC,KAAK,WAAW;AACnB,iBAAK,YAAY,CAAC;AAAA,UACpB;AACA,eAAK,UAAU,KAAK,EAAE,GAAM,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;AAC9C,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,GAAG;AAC1B,UAAI,SAAS,KAAK,iBAAiB,CAAC;AACpC,QAAE,QAAQ,QAAQ,SAAS,OAAO;AAChC,YAAI,aAAa;AACjB,UAAE,QAAQ,OAAO,SAAS,GAAG,GAAG;AAC9B,cAAI,OAAO,EAAE,KAAK,CAAC;AACnB,eAAK,QAAQ,IAAI;AACjB,YAAE,QAAQ,KAAK,WAAW,SAAS,UAAU;AAC3C,iBAAK,aAAa,GAAG,YAAY;AAAA,cAC/B,OAAO,SAAS,MAAM;AAAA,cACtB,QAAQ,SAAS,MAAM;AAAA,cACvB,MAAM,KAAK;AAAA,cACX,OAAO,IAAK,EAAE;AAAA,cACd,GAAG,SAAS;AAAA,cACZ,OAAO,SAAS;AAAA,YAClB,GAAG,KAAK;AAAA,UACV,CAAC;AACD,iBAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,GAAG;AAC5B,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,UAAU,YAAY;AAC7B,cAAI,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;AAC9B,cAAI,IAAI,SAAS,IAAI,SAAS,QAAQ;AACtC,cAAI,IAAI,SAAS;AACjB,cAAI,KAAK,KAAK,IAAI;AAClB,cAAI,KAAK,SAAS,SAAS;AAC3B,YAAE,QAAQ,KAAK,GAAG,KAAK,KAAK;AAC5B,YAAE,WAAW,CAAC;AACd,eAAK,MAAM,SAAS;AAAA,YAClB,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,YAC/B,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,YAC/B,EAAE,GAAG,IAAQ,IAAQ,EAAK;AAAA,YAC1B,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,YAC/B,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,UACjC;AACA,eAAK,MAAM,IAAI,KAAK;AACpB,eAAK,MAAM,IAAI,KAAK;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,KAAK,OAAO;AACrC,aAAO,EAAE,UAAU,EAAE,KAAK,KAAK,KAAK,GAAG,MAAM;AAAA,IAC/C;AAEA,aAAS,aAAa,OAAO;AAC3B,UAAI,WAAW,CAAC;AAChB,QAAE,QAAQ,OAAO,SAAS,GAAG,GAAG;AAC9B,iBAAS,EAAE,YAAY,CAAC,IAAI;AAAA,MAC9B,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACvYA;AAAA;AAAA,QAAI,IAAI;AACR,QAAI,OAAO;AACX,QAAI,QAAQ,oBAAsB;AAElC,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAGA,aAAS,cAAc,GAAG;AACxB,UAAI,cAAc,KAAK,iBAAiB,CAAC;AAEzC,UAAI,IAAI,IAAI,MAAM,EAAE,UAAU,MAAM,YAAY,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;AAEnE,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,UAAE,QAAQ,GAAG,EAAE,OAAO,EAAE,CAAC;AACzB,UAAE,UAAU,GAAG,UAAU,EAAE,KAAK,CAAC,EAAE,IAAI;AAAA,MACzC,CAAC;AAED,QAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG;AAC/B,UAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI;AAAA,MAChC,CAAC;AAED,QAAE,QAAQ,aAAa,SAAS,OAAO,GAAG;AACxC,YAAI,SAAS,UAAU;AACvB,UAAE,QAAQ,QAAQ,EAAE,MAAM,OAAO,CAAC;AAClC,UAAE,OAAO,OAAO,SAAS,GAAG,GAAG;AAC7B,YAAE,QAAQ,GAAG,GAAG,EAAE,OAAO,QAAQ,CAAC;AAClC,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjCA,IAAAC,mBAAA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAsBA,WAAO,UAAU;AAAA,MACf,UAAU;AAAA,MAEV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,MAAM,eAAsB;AAAA,QAC5B,QAAQ,eAAsB;AAAA,MAChC;AAAA,MACA,SAAS;AAAA,IACX;AAAA;AAAA;", "names": ["collection", "collection", "object", "v", "w", "v", "require_graphlib", "require_lodash", "g", "require_util", "edge", "dfs", "entry", "w", "require_version"]}
// Generated by React Router

import type { GetInfo, GetAnnotations } from "react-router/internal";

type Module = typeof import("../index.js")

type Info = GetInfo<{
  file: "routes\admin\entities
o-code\index.tsx",
  module: Module
}>

type Matches = [{
  id: "root";
  module: typeof import("../../../../../root.js");
}, {
  id: "routes/admin";
  module: typeof import("../../../../admin.js");
}, {
  id: "routes/admin/entities";
  module: typeof import("../../../entities.js");
}, {
  id: "routes/admin/entities/no-code";
  module: typeof import("../../no-code.js");
}, {
  id: "routes/admin/entities/no-code/index";
  module: typeof import("../index.js");
}];

type Annotations = GetAnnotations<Info & { module: Module, matches: Matches }>;

export namespace Route {
  // links
  export type LinkDescriptors = Annotations["LinkDescriptors"];
  export type LinksFunction = Annotations["LinksFunction"];

  // meta
  export type MetaArgs = Annotations["MetaArgs"];
  export type MetaDescriptors = Annotations["MetaDescriptors"];
  export type MetaFunction = Annotations["MetaFunction"];

  // headers
  export type HeadersArgs = Annotations["HeadersArgs"];
  export type HeadersFunction = Annotations["HeadersFunction"];

  // unstable_middleware
  export type unstable_MiddlewareFunction = Annotations["unstable_MiddlewareFunction"];

  // unstable_clientMiddleware
  export type unstable_ClientMiddlewareFunction = Annotations["unstable_ClientMiddlewareFunction"];

  // loader
  export type LoaderArgs = Annotations["LoaderArgs"];

  // clientLoader
  export type ClientLoaderArgs = Annotations["ClientLoaderArgs"];

  // action
  export type ActionArgs = Annotations["ActionArgs"];

  // clientAction
  export type ClientActionArgs = Annotations["ClientActionArgs"];

  // HydrateFallback
  export type HydrateFallbackProps = Annotations["HydrateFallbackProps"];

  // Component
  export type ComponentProps = Annotations["ComponentProps"];

  // ErrorBoundary
  export type ErrorBoundaryProps = Annotations["ErrorBoundaryProps"];
}
import { Link, useLocation, useNavigate, useParams } from "react-router";
import clsx from "clsx";
import { useRef, useState } from "react";
import UrlUtils from "~/utils/app/UrlUtils";
import Tabs from "../tabs/Tabs";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import { cn } from "~/lib/utils";
import InputSelect from "../input/InputSelect";

export type IconDto = {
  name: string;
  href: string;
  prefetch?: "intent" | "render" | "none";
  icon?: React.ReactNode;
  iconSelected?: React.ReactNode;
  bottom?: boolean;
  exact?: boolean;
  textIcon?: string;
  textIconSelected?: string;
  hidden?: boolean;
};
export default function SidebarIconsLayout({
  children,
  items,
  label,
  scrollRestoration,
}: {
  children: React.ReactNode;
  items: IconDto[];
  label?: {
    align: "bottom" | "right";
  };
  scrollRestoration?: boolean;
}) {
  const mainElement = useRef<HTMLDivElement>(null);

  const navigate = useNavigate();
  const location = useLocation();
  const isCreatePage = location.pathname.includes("/new");
  const isAdminRoute = location.pathname.startsWith("/admin");
  // useElementScrollRestoration({ apply: scrollRestoration ?? false }, mainElement);

  function findExactRoute(element: IconDto) {
    if (element.exact) {
      return UrlUtils.stripTrailingSlash(location.pathname) === UrlUtils.stripTrailingSlash(element.href);
    } else {
      return (location.pathname + location.search).includes(element.href);
    }
  }
  const currentTab = items.find((element) => findExactRoute(element));

  const isAdminPage = location.pathname.includes("/admin") || location.pathname.includes("/settings");
  const isAppProfilePage = location.pathname.startsWith("/app/") && location.pathname.includes("settings");
  const isLogPage=location.pathname.startsWith("/app/") && location.pathname.includes("settings/logs");
  const isApiPage=location.pathname.startsWith("/app/") && location.pathname.includes("settings/api");
  return (
    <div className={`sm:flex sm:flex-row ${isAppProfilePage?"md:ml-4 min-sm:ml-0.5 mt-5":""}`}>
      {/* Mobile-only header */}
      {isAppProfilePage && (
        <div className="bg-background w-full px-4 py-4 sm:hidden">
          <InputSelect
            className="borderless text-xs sm:text-xs"
            name="feature-type"
            setValue={(e) => {
              navigate(e?.toString() ?? "", { replace: true });
            }}
            options={items.map((item) => ({
              name: item.name,
              value: item.name,
            }))}
            defaultValue={items.find((item) => item.name === location.pathname.split("/").pop())?.name || "Profile"}
          />
        </div>
      )}

      {/* Sidebar (hidden on mobile) */}
      <div
        className={cn(
          `border-border hidden flex-none flex-col items-center justify-between overflow-y-auto border-r sm:flex ${isAppProfilePage ? "w-50 overflow-hidden rounded-lg gap-2.5 mt-2 opacity-100 border-none " : ""}`,
          label?.align === "bottom" && "lg:text-center",
          isAppProfilePage?"min-h-[calc(100vh-150px)]":"min-h-[calc(100vh-70px)]"
        )}
      >
        {isAdminPage && (
          <div className={`ml-2 flex w-full flex-col`}>
          {isAppProfilePage&&  <span className="text-muted-foreground mb-1.5 pl-3 align-middle text-[0.75rem] leading-none font-normal">Profile Actions</span>}
            {items
              .filter((f) => !f.bottom && !f.hidden)
              .map((item, idx) => (
                <IconLink key={idx} {...item} current={currentTab?.name === item.name} label={label} />
              ))}
          </div>
        )}
        {items.filter((f) => f.bottom && !f.hidden).length > 0 && (
          <div className="flex w-full flex-col space-y-2 pb-5">
            {items
              .filter((f) => f.bottom && !f.hidden)
              .map((item, idx) => (
                <IconLink key={idx} {...item} current={currentTab?.name === item.name} label={label} />
              ))}
          </div>
        )}
      </div>
     {!isAppProfilePage && (
        <div className={`border-border bg-background w-full border-b py-2 shadow-2xs sm:hidden`}>
        {/* <div className="mx-auto flex max-w-5xl items-center justify-between gap-2 px-4 sm:px-6 lg:px-8 xl:max-w-7xl 2xl:max-w-(--breakpoint-2xl)">
          <Tabs
            tabs={items
              .filter((f) => !f.hidden)
              .map((i) => {
                return { name: i.name, routePath: i.href };
              })}
            className="grow"
          />
        </div> */}
      </div>)}

      {/* Main content area */}
      <div className={`w-full overflow-x-hidden  bg-background  ${isCreatePage ? 'h-[calc(100vh-50px)] overflow-y-auto' : ''} ${isAppProfilePage?"mx-4 sm:ml-4 sm:mr-4 overflow-hidden   border border-input rounded-md max-h-screen h-[87vh] max-w-[calc(100vw-2rem)] ":""} ${isLogPage&&"px-3"} ${isApiPage&&"px-3"}`}>
      <div
        ref={mainElement}
        className={`w-full overflow-x-hidden  bg-background  ${isCreatePage ? 'h-[calc(100vh-50px)] ' : ''} ${isAppProfilePage?"  max-h-screen h-[87vh] max-w-[calc(100vw-2rem)] ":""}`}
      >
        {children}
      </div>
       </div>
    </div>
  );
}

function IconLink({
  name,
  href,
  prefetch,
  icon,
  current,
  iconSelected,
  label,
  textIcon,
  textIconSelected,
}: {
  name: string;
  href: string;
  prefetch?: "intent" | "render" | "none";
  icon?: React.ReactNode;
  iconSelected?: React.ReactNode;
  current: boolean;
  label?: {
    align: "bottom" | "right";
  };
  textIcon?: string;
  textIconSelected?: string;
}) {
  const location=useLocation();
  const isAppProfilePage = location.pathname.startsWith("/app/") && location.pathname.includes("settings");
  return (
    <div className={clsx("flex w-full gap-1 px-1 py-1")}>
      <div>

      <div className={`bg-primary h-8 w-[2px] rotate-0 rounded-[10px] opacity-100 ${current ? "block" : "hidden"}`}></div>
      </div>

      <Link
        prefetch={prefetch}
        to={href}
        className={clsx(
          "hover:border-border hover:bg-input/30  flex w-full flex-1 items-center justify-center rounded-md border px-2 text-xs",
          current ? "border-border bg-primary-light text-primary hover:bg-primary-light/90" : "text-foreground border-transparent",
          !label ? "w-11" : "lg:w-auto lg:justify-start",
          label?.align === "bottom" && "flex-col space-y-1",
          label?.align === "right" && "flex-row gap-2",
          isAppProfilePage ?"py-1.5":" py-2"
        )}
      >
        {textIcon !== undefined && textIconSelected !== undefined ? (
          <div>
            {current ? (
              <EntityIcon className={`${current ? "text-primary" : "text-muted-foreground"} text-muted-foreground h-5 w-5`} icon={textIconSelected} />
            ) : (
              <EntityIcon className={`${current ? "text-primary" : "text-muted-foreground"} text-muted-foreground h-5 w-5`} icon={textIcon} />
            )}
          </div>
        ) : (
          <div>{current ? iconSelected : icon}</div>
        )}
        {label !== undefined && (
          <div className={clsx([icon, iconSelected, textIcon, textIconSelected].some((f) => f !== undefined) && `hidden lg:block ${isAppProfilePage&&"font-normal"}`)}>{name}</div>
        )}
      </Link>
    </div>
  );
}

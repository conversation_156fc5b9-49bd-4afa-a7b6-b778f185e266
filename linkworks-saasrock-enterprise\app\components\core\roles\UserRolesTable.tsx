import { useTranslation } from "react-i18next";
import EmptyState from "~/components/ui/emptyState/EmptyState";
import { RoleWithPermissions } from "~/utils/db/permissions/roles.db.server";
import { UserWithRoles } from "~/utils/db/users.db.server";
import UserBadge from "../users/UserBadge";
import clsx from "clsx";
import CheckIcon from "~/components/ui/icons/CheckIcon";
import XIcon from "~/components/ui/icons/XIcon";
import { Link } from "react-router";
import { useAppOrAdminData } from "~/utils/data/useAppOrAdminData";
import { useLocation } from "react-router";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import { PaginationDto } from "~/application/dtos/data/PaginationDto";
import TablePagination from "~/components/ui/tables/TablePagination";
import UserAvatarBadge from "../users/UserAvatarBadge";
interface Props {
  items: UserWithRoles[];
  roles: RoleWithPermissions[];
  className?: string;
  onChange: (item: UserWithRoles, role: RoleWithPermissions, add: any) => void;
  tenantId?: string | null;
  disabled?: boolean;
  onRoleClick?: (role: RoleWithPermissions) => void;
  actions?: {
    onEditRoute?: (item: UserWithRoles) => string;
    onImpersonate?: (item: UserWithRoles) => void;
  };
  pagination?: PaginationDto;
  onPaginationChange?: (page: number, pageSize: number) => void;
}

export default function UserRolesTable({
  items,
  roles,
  className,
  onChange,
  tenantId = null,
  disabled,
  onRoleClick,
  actions,
  pagination,
  onPaginationChange,
}: Props) {
  const { t } = useTranslation();
  const location = useLocation();
  const isInAppSettings = location.pathname.startsWith("/app/") && location.pathname.includes("settings");
  const appOrAdminData = useAppOrAdminData();

  return (
    <div>
      {(() => {
        if (items.length === 0) {
          return (
            <div>
              <EmptyState
                className="bg-background"
                captions={{
                  thereAreNo: t("app.users.empty"),
                }}
              />
            </div>
          );
        } else {
          return (
            <div>
              <div>
               <div className="overflow-auto w-full">
 <div
  className={clsx(
    "inline-block border border-border sm:rounded-sm min-w-full align-middle relative border rounded-md",
    isInAppSettings ? "h-[350px]" : "h-auto"
  )}
>
  

    

                        <table className="divide-border min-w-full divide-y">
                          <thead className="pt-2 bg-secondary sticky top-0 z-10">
                            <tr>
                              
                              <th
                                scope="col"
                                className="text-muted-foreground w-64 truncate px-3 py-3 pl-4 text-left text-xs font-medium tracking-wider select-none"
                              >
                                <div className="text-muted-foreground flex items-center space-x-5">
                                   <UserAvatarBadge avatar={null} className="h-9 w-9" />
                                   <div className="border-r-[2px] border-border  h-7  -ml-5" /> 
                                  <div>{t("models.user.AccountName")}</div>
                                   <div className="border-r-[2px] border-border h-7 ml-16 mx-auto"></div>
                                </div>
                              </th>

                              {roles.map((role, idx) => {
                                return (
                                  <th
                                    key={idx}
                                    scope="col"
                                    className="text-muted-foreground truncate px-3 py-3 text-center text-xs font-medium tracking-wider select-none"
                                  >
                                    <div className="text-muted-foreground flex items-center justify-center space-x-1">
                                      {onRoleClick ? (
                                        <button type="button" onClick={() => onRoleClick(role)} className="hover:underline">
                                          {role.name}
                                        </button>
                                      ) : (
                                        <div>{role.name}</div>
                                      )}
                                      <div className="border-r-[2px] border-border ml-16 h-7 mx-2" /> 
                                    </div>
                                  </th>
                                );
                              })}

                              {actions && (
                                <th
                                  scope="col"
                                  className="sticky right-0 text-muted-foreground border-border border-b px-3 py-3 text-center text-xs font-semibold tracking-wide bg-secondary"
                                >
                                  {t("shared.actions")}
                                </th>
                              )}
                            </tr>
                          </thead>
                          <tbody className="divide-border bg-background divide-y">
                            {items.map((item, idx) => {
                              return (
                                <tr key={idx} className={clsx(isInAppSettings && (idx % 2 === 1 ? "bg-secondary" : "bg-muted-background"))}>
                                  <td className="text-muted-foreground px-0 py-3 pl-4 text-sm whitespace-nowrap">
                                    <UserBadge
                                      item={item}
                                      withAvatar={true}
                                      admin={item.admin}
                                      href={!actions?.onEditRoute ? undefined : actions.onEditRoute(item)}
                                      roles={item.roles}
                                    />
                                  </td>
                                  {roles.map((role) => {
                                    const existing = item.roles.find((f) => f.roleId === role.id && f.tenantId === tenantId) !== undefined;
                                    return (
                                      <td key={role.name} className="text-muted-foreground px-1 py-1 text-center text-sm whitespace-nowrap">
                                        <div className="flex justify-start pl-2">
                                          <button
                                            type="button"
                                            disabled={disabled}
                                            onClick={() => onChange(item, role, !existing)}
                                            className={clsx(
                                              "inline-flex h-[22px] items-center rounded-md px-2 py-1 text-xs font-bold",
                                              existing
                                                ? "!bg-[#d5f8e5] !font-medium !text-[#202229] text-green-600 dark:bg-green-900 dark:text-green-400"
                                                : "!bg-[#f2e9db] !font-medium !text-[#202229] text-red-600 dark:bg-red-900 dark:text-red-400",
                                              disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer"
                                            )}
                                          >
                                            {existing ? "Yes" : "No"}
                                          </button>
                                        </div>
                                      </td>
                                    );
                                  })}
                                  {/* {onEditRoute && (
                                      <td className="whitespace-nowrap px-1 py-1 text-center text-sm text-muted-foreground">
                                        <Link to={onEditRoute(item)} className="hover:underline">
                                          {t("shared.edit")}
                                        </Link>
                                      </td>
                                    )} */}
                                  {actions && (
                                    <td className="sticky right-0 text-muted-foreground px-1 py-3 text-center text-sm whitespace-nowrap bg-secondary">
                                      <div className="flex items-center space-x-2">
                                        {actions.onImpersonate && (
                                          <button
                                            type="button"
                                            disabled={item.id === appOrAdminData.user.id}
                                            onClick={() => (actions?.onImpersonate ? actions?.onImpersonate(item) : {})}
                                            className={clsx(
                                              "text-muted-foreground",
                                              item.id !== appOrAdminData.user.id ? "hover:underline" : "cursor-not-allowed"
                                            )}
                                          >
                                            {t("models.user.impersonate")}
                                          </button>
                                        )}
                                        {actions?.onEditRoute && (
                                          <div className="flex w-full justify-center">
                                            <div className="ml-2">
                                              <ButtonSecondary
                                                type="button"
                                                onClick={() => (window.location.href = actions.onEditRoute!(item))}
                                                className="border-input hover:bg-secondary h-[28px] w-[64px] rounded-sm border px-2 py-[2px] text-sm font-normal shadow-2xs"
                                              >
                                                {t("shared.edit")}
                                              </ButtonSecondary>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </td>
                                  )}
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      
                      {pagination && onPaginationChange && (
                        <div className="sticky bottom-0 bg-background border-t border-border z-10 mt-0">
                          <TablePagination
                            page={pagination.page}
                            pageSize={pagination.pageSize}
                            totalItems={pagination.totalItems}
                            totalPages={pagination.totalPages}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                
              </div>
            </div>
          );
        }
      })()}
    </div>
  );
}

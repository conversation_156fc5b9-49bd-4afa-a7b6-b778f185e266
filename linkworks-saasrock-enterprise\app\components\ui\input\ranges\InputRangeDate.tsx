import clsx from "clsx";
import { ReactNode, useEffect, useRef, useState } from "react";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import HintTooltip from "~/components/ui/tooltips/HintTooltip";
import { Input } from "../../input";
 
interface Props {
  name: string;
  title: string;
  valueMin?: Date | null;
  defaultValueMin?: Date | null;
  onChangeMin?: (date: Date) => void;
  valueMax?: Date | null;
  defaultValueMax?: Date | null;
  onChangeMax?: (date: Date) => void;
  className?: string;
  help?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  hint?: ReactNode;
  icon?: string;
  darkMode?: boolean;
  autoFocus?: boolean;
  minStartDate?: Date | string | null;
  maxStartDate?: Date | string | null;
  minEndDate?: Date | string | null;
  maxEndDate?: Date | string | null;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  minRangeDuration?: number; // in days
  maxRangeDuration?: number; // in days
}
export default function InputDateRangeDate({
  name,
  title,
  valueMin,
  defaultValueMin,
  valueMax,
  defaultValueMax,
  onChangeMin,
  onChangeMax,
  className,
  help,
  disabled = false,
  readOnly = false,
  required = false,
  hint,
  icon,
  darkMode,
  autoFocus,
  minStartDate,
  maxStartDate,
  minEndDate,
  maxEndDate,
  disablePastDates = false,
  disableFutureDates = false,
  minRangeDuration,
  maxRangeDuration,
}: Props) {
  // useImperativeHandle(ref, () => ({ inputMin, inputMax }));
  // const inputMin = useRef<HTMLInputElement>(null);
  // const inputMax = useRef<HTMLInputElement>(null);
 
  const [actualMin, setActualMin] = useState<string>("");
  const [actualMax, setActualMax] = useState<string>("");
  const minDatePickerRef = useRef(null);
  const maxDatePickerRef = useRef(null);
 
  // Note: Validation is handled at the backend level in PropertyHelper.validatePropertyValue
 
  // Helper function to format date in local timezone
  const formatDateForInput = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
 
  // Helper function to get today's date at midnight
  const getTodayAtMidnight = (): Date => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  };
 
  const getEffectiveMinDate = (isEndDate: boolean = false): string | undefined => {
    const constraints: Date[] = [];
 
    if (disablePastDates) {
      // Use helper function to get today's date at midnight
      constraints.push(getTodayAtMidnight());
    }
 
    if (isEndDate) {
      if (minEndDate) {
        const minEndDateObj = minEndDate instanceof Date ? minEndDate : new Date(minEndDate);
        constraints.push(minEndDateObj);
      }
      // End date should be after start date
      if (actualMin) {
        const startDate = new Date(actualMin);
 
        // Apply minimum range duration constraint
        if (minRangeDuration) {
          const minDurationDays = parseInt(minRangeDuration.toString());
          startDate.setDate(startDate.getDate() + minDurationDays);
        } else {
          startDate.setDate(startDate.getDate() + 1); // Default: at least 1 day after start
        }
 
        constraints.push(startDate);
      }
    } else {
      // For start date
      if (minStartDate) {
        const minStartDateObj = minStartDate instanceof Date ? minStartDate : new Date(minStartDate);
        constraints.push(minStartDateObj);
      }
 
      // If end date is already selected, apply duration constraints for start date
      if (actualMax) {
        const endDate = new Date(actualMax);
 
        // Start date must be at least maxRangeDuration days before end date
        if (maxRangeDuration) {
          const maxDurationDays = parseInt(maxRangeDuration.toString());
          const earliestStartDate = new Date(endDate);
          earliestStartDate.setDate(earliestStartDate.getDate() - maxDurationDays);
          constraints.push(earliestStartDate);
        }
      }
    }
 
    if (constraints.length === 0) return undefined;
    const latestDate = new Date(Math.max(...constraints.map(d => d.getTime())));
    return formatDateForInput(latestDate);
  };
 
  const getEffectiveMaxDate = (isEndDate: boolean = false): string | undefined => {
    const constraints: Date[] = [];
 
    if (disableFutureDates) {
      // Use helper function to get today's date at midnight
      constraints.push(getTodayAtMidnight());
    }
 
    if (isEndDate) {
      if (maxEndDate) {
        const maxEndDateObj = maxEndDate instanceof Date ? maxEndDate : new Date(maxEndDate);
        constraints.push(maxEndDateObj);
      }
 
      // Apply maximum range duration constraint for end date
      if (actualMin && maxRangeDuration) {
        const startDate = new Date(actualMin);
        const maxDurationDays = parseInt(maxRangeDuration.toString());
        startDate.setDate(startDate.getDate() + maxDurationDays);
        constraints.push(startDate);
      }
    } else {
      if (maxStartDate) {
        const maxStartDateObj = maxStartDate instanceof Date ? maxStartDate : new Date(maxStartDate);
        constraints.push(maxStartDateObj);
      }
 
      // Apply minimum range duration constraint for start date
      if (actualMin && minRangeDuration) {
        const endDate = new Date(actualMin);
        const minDurationDays = parseInt(minRangeDuration.toString());
        endDate.setDate(endDate.getDate() + minDurationDays);
        constraints.push(endDate);
      }
      // Start date should be before end date
      if (actualMax) {
        const endDate = new Date(actualMax);
 
        // Apply minimum range duration constraint - start date must be at least minRangeDuration days before end date
        if (minRangeDuration) {
          const minDurationDays = parseInt(minRangeDuration.toString());
          endDate.setDate(endDate.getDate() - minDurationDays);
        } else {
          endDate.setDate(endDate.getDate() - 1); // Default: at least 1 day before end
        }
 
        constraints.push(endDate);
      }
    }
 
    if (constraints.length === 0) return undefined;
    const earliestDate = new Date(Math.min(...constraints.map(d => d.getTime())));
    return formatDateForInput(earliestDate);
  };
 
  useEffect(() => {
    const value = valueMin || defaultValueMin;
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setActualMin(date.toISOString().split("T")[0]);
      }
    }
  }, [defaultValueMin, valueMin]);
 
  useEffect(() => {
    const value = valueMax || defaultValueMax;
    if (value) {
      const date = new Date(value);
      if (date) {
        setActualMax(date.toISOString().split("T")[0]);
      }
    }
  }, [defaultValueMax, valueMax]);
 
  useEffect(() => {
    if (onChangeMin && actualMin) {
      const date = new Date(actualMin);
      if (!isNaN(date.getTime())) {
        onChangeMin(date);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actualMin]);
 
  useEffect(() => {
    if (onChangeMax && actualMax) {
      const date = new Date(actualMax);
      if (!isNaN(date.getTime())) {
        onChangeMax(date);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actualMax]);
 
  return (
    <div className={clsx(className, !darkMode && "")}>
      <label htmlFor={name} className="flex justify-between space-x-2 text-xs font-medium">
        <div className="flex items-center space-x-1">
          <div className="truncate">
            {title}
            {required && <span className="text-destructive ml-1">*</span>}
          </div>
 
          {help && <HintTooltip text={help} />}
        </div>
        {hint}
      </label>
      <div className="relative mt-1 flex w-full rounded-md">
        {icon && (
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <EntityIcon className="text-muted-foreground h-5 w-5" icon={icon} />
          </div>
        )}
 
        <div className="flex w-full items-center space-x-2">
          <Input
            type="date"
            ref={minDatePickerRef}
            id={name}
            name={`${name}-min`}
            required={required}
            value={actualMin}
            onChange={(e) => {
              const selectedDate = e.target.value;
 
              // Additional validation for past dates
              if (disablePastDates && selectedDate) {
                const selected = new Date(selectedDate + 'T00:00:00'); // Ensure local timezone
                const today = getTodayAtMidnight();
 
                // Prevent selection of dates before today
                if (selected.getTime() < today.getTime()) {
                  console.warn('InputRangeDate: Attempted to select past date, preventing selection');
                  return;
                }
              }
 
              setActualMin(selectedDate);
            }}
            disabled={disabled}
            readOnly={readOnly}
            autoFocus={autoFocus}
            {...(getEffectiveMinDate(false) ? { min: getEffectiveMinDate(false) } : {})}
            {...(getEffectiveMaxDate(false) ? { max: getEffectiveMaxDate(false) } : {})}
            className={clsx(
              icon && "pl-10",
              "border-input rounded-lg shadow-none text-foreground [&::-webkit-datetime-edit]:text-foreground [&::-webkit-datetime-edit-text]:text-foreground [&::-webkit-datetime-edit-month-field]:text-foreground [&::-webkit-datetime-edit-day-field]:text-foreground [&::-webkit-datetime-edit-year-field]:text-foreground",
              (disabled || readOnly) && "cursor-not-allowed bg-secondary/90"
            )}
          />
          <span className="text-muted-foreground">-</span>
          <Input
            type="date"
            ref={maxDatePickerRef}
            id={name}
            name={`${name}-max`}
            required={required}
            value={actualMax}
            onChange={(e) => {
              const selectedDate = e.target.value;
 
              // Additional validation for past dates on end date
              if (disablePastDates && selectedDate) {
                const selected = new Date(selectedDate + 'T00:00:00'); // Ensure local timezone
                const today = getTodayAtMidnight();
 
                // Prevent selection of dates before today
                if (selected.getTime() < today.getTime()) {
                  console.warn('InputRangeDate: Attempted to select past end date, preventing selection');
                  return;
                }
              }
 
              setActualMax(selectedDate);
            }}
            disabled={disabled}
            readOnly={readOnly}
            autoFocus={autoFocus}
            {...(getEffectiveMinDate(true) ? { min: getEffectiveMinDate(true) } : {})}
            {...(getEffectiveMaxDate(true) ? { max: getEffectiveMaxDate(true) } : {})}
            className={clsx(
              icon && "pl-10",
              "border-input rounded-lg shadow-none text-foreground [&::-webkit-datetime-edit]:text-foreground [&::-webkit-datetime-edit-text]:text-foreground [&::-webkit-datetime-edit-month-field]:text-foreground [&::-webkit-datetime-edit-day-field]:text-foreground [&::-webkit-datetime-edit-year-field]:text-foreground",
              (disabled || readOnly) && "cursor-not-allowed bg-secondary/90"
            )}
          />
        </div>
      </div>
    </div>
  );
}
 
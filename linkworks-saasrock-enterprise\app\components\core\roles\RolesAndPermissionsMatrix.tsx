import { Permission } from "@prisma/client";
import clsx from "clsx";
import { useState } from "react";
import { RowHeaderDisplayDto } from "~/application/dtos/data/RowHeaderDisplayDto";
import CheckIcon from "~/components/ui/icons/CheckIcon";
import XIcon from "~/components/ui/icons/XIcon";
import InputSearch from "~/components/ui/input/InputSearch";
import TableSimple from "~/components/ui/tables/TableSimple";
import { RoleWithPermissions } from "~/utils/db/permissions/roles.db.server";
import { useSearchParams } from "react-router";
import TablePagination from "~/components/ui/tables/TablePagination";
import { PaginationDto } from "~/application/dtos/data/PaginationDto";
import { useEscapeKeypress } from "~/utils/shared/KeypressUtils";
import { useNavigate } from "react-router";
import UrlUtils from "~/utils/app/UrlUtils";
import { useParams } from "react-router";
interface Props {
  roles: RoleWithPermissions[];
  permissions: Permission[];
  className?: string;
  onClose?: () => void; 
}
export default function RolesAndPermissionsMatrix({ roles, permissions, className,onClose  }: Props) {
  const [searchInput, setSearchInput] = useState("");
  const [searchParams, setSearchParams] = useSearchParams();
  const page = Number(searchParams.get("page") ?? 1);
  const pageSize = Number(searchParams.get("pageSize") ?? 10);
  const navigate = useNavigate();
  const params = useParams();
  useEscapeKeypress(() => {
  if (onClose) onClose();
});

  function handlePageChange(newPage: number) {
    searchParams.set("page", newPage.toString());
    setSearchParams(searchParams);
  }

  function handlePageSizeChange(newSize: number) {
    searchParams.set("pageSize", newSize.toString());
    searchParams.set("page", "1"); // reset to first page
    setSearchParams(searchParams);
  }

  function getHeaders() {
    const headers: RowHeaderDisplayDto<Permission>[] = [];
    headers.push({
      name: "permission",
      title: "Permission",
      className: "w-full",
      value: (item) => (
        <div className="max-w-xs truncate">
          <div>{item.name}</div>
          <div className="text-muted-foreground truncate text-sm">{item.description}</div>
        </div>
      ),
    });
    roles.forEach((role) => {
      headers.push({
        name: role.name,
       title: `${role.name} (${role.permissions.length})`,
        align: "center",
        value: (permission) => {
          const existing = role.permissions.find((f) => f.permission.name === permission.name);
          return (
            <div className="flex justify-center">
              <div
                className={clsx(
                  "inline-flex h-[22px] items-center rounded-md px-2 py-1 text-xs font-bold",
                  existing
                    ? "!bg-[#d5f8e5] !font-medium !text-[#202229] text-green-600 dark:bg-green-900 dark:text-green-400"
                    : "!bg-[#f2e9db] !font-medium !text-[#202229] text-red-600 dark:bg-red-900 dark:text-red-400"
                )}
              >
                {existing ? "Yes" : "No"}
              </div>
            </div>
          );
        },
      });
    });

    return headers;
  }
  function filteredItems() {
    if (!searchInput) return permissions;
    return permissions.filter(
      (f) => f.name.toLowerCase().includes(searchInput.toLowerCase()) || f.description.toLowerCase().includes(searchInput.toLowerCase())
    );
  }
  const filtered = filteredItems();
  const totalItems = filtered.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const pagedItems = filtered.slice((page - 1) * pageSize, page * pageSize);

  return (
    <div className="space-y-6">
      {onClose && (
  <div className="absolute right-4 top-[22px] z-50">
     
    <button
      type="button"
      onClick={onClose}
      className="cursor-pointer"
    >
      <svg className="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path
          fillRule="evenodd"
          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
          clipRule="evenodd"
        />
      </svg>
    </button>
  </div>
)}


     
      <div className="w-full">
        <InputSearch value={searchInput} setValue={setSearchInput} className="w-full" placeholder="Search account" />
      </div>

      <div className={className}>
        <div className="overflow-x-auto">
          
            <TableSimple
              items={pagedItems}
              headers={getHeaders()}
              pagination={{
                page,
                pageSize,
                totalItems,
                totalPages,
              }}
              className={() => "h-14 text-sm"}
            />
          
        </div>
      </div>
    </div>
  );
}

import {
  require_node_fs
} from "./chunk-JWPFZ2IK.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// browser-external:node:module
var require_node_module = __commonJS({
  "browser-external:node:module"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:module" has been externalized for browser compatibility. Cannot access "node:module.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:path
var require_node_path = __commonJS({
  "browser-external:node:path"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:path" has been externalized for browser compatibility. Cannot access "node:path.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:process
var require_node_process = __commonJS({
  "browser-external:node:process"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:process" has been externalized for browser compatibility. Cannot access "node:process.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:url
var require_node_url = __commonJS({
  "browser-external:node:url"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:url" has been externalized for browser compatibility. Cannot access "node:url.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:child_process
var require_node_child_process = __commonJS({
  "browser-external:node:child_process"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:child_process" has been externalized for browser compatibility. Cannot access "node:child_process.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:fs/promises
var require_promises = __commonJS({
  "browser-external:node:fs/promises"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:fs/promises" has been externalized for browser compatibility. Cannot access "node:fs/promises.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:os
var require_node_os = __commonJS({
  "browser-external:node:os"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:os" has been externalized for browser compatibility. Cannot access "node:os.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:util
var require_node_util = __commonJS({
  "browser-external:node:util"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:util" has been externalized for browser compatibility. Cannot access "node:util.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:async_hooks
var require_node_async_hooks = __commonJS({
  "browser-external:node:async_hooks"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:async_hooks" has been externalized for browser compatibility. Cannot access "node:async_hooks.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:events
var require_node_events = __commonJS({
  "browser-external:node:events"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:events" has been externalized for browser compatibility. Cannot access "node:events.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// node_modules/@prisma/client/runtime/library.mjs
var __banner_node_module = __toESM(require_node_module(), 1);
var __banner_node_path = __toESM(require_node_path(), 1);
var process = __toESM(require_node_process(), 1);
var __banner_node_url = __toESM(require_node_url(), 1);
var import_node_fs = __toESM(require_node_fs(), 1);
var import_node_child_process = __toESM(require_node_child_process(), 1);
var import_promises = __toESM(require_promises(), 1);
var import_node_os = __toESM(require_node_os(), 1);
var import_node_util = __toESM(require_node_util(), 1);
var import_node_process = __toESM(require_node_process(), 1);
var import_node_path = __toESM(require_node_path(), 1);
var import_node_fs2 = __toESM(require_node_fs(), 1);
var import_node_path2 = __toESM(require_node_path(), 1);
var import_node_fs3 = __toESM(require_node_fs(), 1);
var import_node_path3 = __toESM(require_node_path(), 1);
var import_node_fs4 = __toESM(require_node_fs(), 1);
var import_node_async_hooks = __toESM(require_node_async_hooks(), 1);
var import_node_events = __toESM(require_node_events(), 1);
var import_node_fs5 = __toESM(require_node_fs(), 1);
var import_node_path4 = __toESM(require_node_path(), 1);
var import_node_fs6 = __toESM(require_node_fs(), 1);
var import_node_path5 = __toESM(require_node_path(), 1);
var import_node_os2 = __toESM(require_node_os(), 1);
var import_node_path6 = __toESM(require_node_path(), 1);
var __filename = __banner_node_url.fileURLToPath(import.meta.url);
var __dirname = __banner_node_path.dirname(__filename);
var require2 = __banner_node_module.createRequire(import.meta.url);
var nu = Object.create;
var Kn = Object.defineProperty;
var iu = Object.getOwnPropertyDescriptor;
var ou = Object.getOwnPropertyNames;
var su = Object.getPrototypeOf;
var au = Object.prototype.hasOwnProperty;
var fr = ((e10) => typeof require2 < "u" ? require2 : typeof Proxy < "u" ? new Proxy(e10, { get: (r, t) => (typeof require2 < "u" ? require2 : r)[t] }) : e10)(function(e10) {
  if (typeof require2 < "u") return require2.apply(this, arguments);
  throw Error('Dynamic require of "' + e10 + '" is not supported');
});
var Ro = (e10, r) => () => (e10 && (r = e10(e10 = 0)), r);
var te = (e10, r) => () => (r || e10((r = { exports: {} }).exports, r), r.exports);
var gr = (e10, r) => {
  for (var t in r) Kn(e10, t, { get: r[t], enumerable: true });
};
var lu = (e10, r, t, n) => {
  if (r && typeof r == "object" || typeof r == "function") for (let i of ou(r)) !au.call(e10, i) && i !== t && Kn(e10, i, { get: () => r[i], enumerable: !(n = iu(r, i)) || n.enumerable });
  return e10;
};
var ne = (e10, r, t) => (t = e10 != null ? nu(su(e10)) : {}, lu(r || !e10 || !e10.__esModule ? Kn(t, "default", { value: e10, enumerable: true }) : t, e10));
var li = te((Vg, Xo) => {
  "use strict";
  Xo.exports = (e10, r = process.argv) => {
    let t = e10.startsWith("-") ? "" : e10.length === 1 ? "-" : "--", n = r.indexOf(t + e10), i = r.indexOf("--");
    return n !== -1 && (i === -1 || n < i);
  };
});
var ts = te((Bg, rs) => {
  "use strict";
  var Sc = fr("node:os"), es = fr("node:tty"), pe = li(), { env: U } = process, Qe;
  pe("no-color") || pe("no-colors") || pe("color=false") || pe("color=never") ? Qe = 0 : (pe("color") || pe("colors") || pe("color=true") || pe("color=always")) && (Qe = 1);
  "FORCE_COLOR" in U && (U.FORCE_COLOR === "true" ? Qe = 1 : U.FORCE_COLOR === "false" ? Qe = 0 : Qe = U.FORCE_COLOR.length === 0 ? 1 : Math.min(parseInt(U.FORCE_COLOR, 10), 3));
  function ui(e10) {
    return e10 === 0 ? false : { level: e10, hasBasic: true, has256: e10 >= 2, has16m: e10 >= 3 };
  }
  function ci(e10, r) {
    if (Qe === 0) return 0;
    if (pe("color=16m") || pe("color=full") || pe("color=truecolor")) return 3;
    if (pe("color=256")) return 2;
    if (e10 && !r && Qe === void 0) return 0;
    let t = Qe || 0;
    if (U.TERM === "dumb") return t;
    if (process.platform === "win32") {
      let n = Sc.release().split(".");
      return Number(n[0]) >= 10 && Number(n[2]) >= 10586 ? Number(n[2]) >= 14931 ? 3 : 2 : 1;
    }
    if ("CI" in U) return ["TRAVIS", "CIRCLECI", "APPVEYOR", "GITLAB_CI", "GITHUB_ACTIONS", "BUILDKITE"].some((n) => n in U) || U.CI_NAME === "codeship" ? 1 : t;
    if ("TEAMCITY_VERSION" in U) return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(U.TEAMCITY_VERSION) ? 1 : 0;
    if (U.COLORTERM === "truecolor") return 3;
    if ("TERM_PROGRAM" in U) {
      let n = parseInt((U.TERM_PROGRAM_VERSION || "").split(".")[0], 10);
      switch (U.TERM_PROGRAM) {
        case "iTerm.app":
          return n >= 3 ? 3 : 2;
        case "Apple_Terminal":
          return 2;
      }
    }
    return /-256(color)?$/i.test(U.TERM) ? 2 : /^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(U.TERM) || "COLORTERM" in U ? 1 : t;
  }
  function Rc(e10) {
    let r = ci(e10, e10 && e10.isTTY);
    return ui(r);
  }
  rs.exports = { supportsColor: Rc, stdout: ui(ci(true, es.isatty(1))), stderr: ui(ci(true, es.isatty(2))) };
});
var os = te((Ug, is) => {
  "use strict";
  var Cc = ts(), Er = li();
  function ns(e10) {
    if (/^\d{3,4}$/.test(e10)) {
      let t = /(\d{1,2})(\d{2})/.exec(e10) || [];
      return { major: 0, minor: parseInt(t[1], 10), patch: parseInt(t[2], 10) };
    }
    let r = (e10 || "").split(".").map((t) => parseInt(t, 10));
    return { major: r[0], minor: r[1], patch: r[2] };
  }
  function pi(e10) {
    let { CI: r, FORCE_HYPERLINK: t, NETLIFY: n, TEAMCITY_VERSION: i, TERM_PROGRAM: o, TERM_PROGRAM_VERSION: s, VTE_VERSION: a, TERM: l } = process.env;
    if (t) return !(t.length > 0 && parseInt(t, 10) === 0);
    if (Er("no-hyperlink") || Er("no-hyperlinks") || Er("hyperlink=false") || Er("hyperlink=never")) return false;
    if (Er("hyperlink=true") || Er("hyperlink=always") || n) return true;
    if (!Cc.supportsColor(e10) || e10 && !e10.isTTY) return false;
    if ("WT_SESSION" in process.env) return true;
    if (process.platform === "win32" || r || i) return false;
    if (o) {
      let u = ns(s || "");
      switch (o) {
        case "iTerm.app":
          return u.major === 3 ? u.minor >= 1 : u.major > 3;
        case "WezTerm":
          return u.major >= 20200620;
        case "vscode":
          return u.major > 1 || u.major === 1 && u.minor >= 72;
        case "ghostty":
          return true;
      }
    }
    if (a) {
      if (a === "0.50.0") return false;
      let u = ns(a);
      return u.major > 0 || u.minor >= 50;
    }
    switch (l) {
      case "alacritty":
        return true;
    }
    return false;
  }
  is.exports = { supportsHyperlink: pi, stdout: pi(process.stdout), stderr: pi(process.stderr) };
});
var ss = te((th, Ac) => {
  Ac.exports = { name: "@prisma/internals", version: "6.12.0", description: "This package is intended for Prisma's internal use", main: "dist/index.js", types: "dist/index.d.ts", repository: { type: "git", url: "https://github.com/prisma/prisma.git", directory: "packages/internals" }, homepage: "https://www.prisma.io", author: "Tim Suchanek <<EMAIL>>", bugs: "https://github.com/prisma/prisma/issues", license: "Apache-2.0", scripts: { dev: "DEV=true tsx helpers/build.ts", build: "tsx helpers/build.ts", test: "dotenv -e ../../.db.env -- jest --silent", prepublishOnly: "pnpm run build" }, files: ["README.md", "dist", "!**/libquery_engine*", "!dist/get-generators/engines/*", "scripts"], devDependencies: { "@babel/helper-validator-identifier": "7.25.9", "@opentelemetry/api": "1.9.0", "@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/babel__helper-validator-identifier": "7.15.2", "@types/jest": "29.5.14", "@types/node": "18.19.76", "@types/resolve": "1.20.6", archiver: "6.0.2", "checkpoint-client": "1.1.33", "cli-truncate": "4.0.0", dotenv: "16.5.0", esbuild: "0.25.5", "escape-string-regexp": "5.0.0", execa: "5.1.1", "fast-glob": "3.3.3", "find-up": "7.0.0", "fp-ts": "2.16.9", "fs-extra": "11.3.0", "fs-jetpack": "5.1.0", "global-dirs": "4.0.0", globby: "11.1.0", "identifier-regex": "1.0.0", "indent-string": "4.0.0", "is-windows": "1.0.2", "is-wsl": "3.1.0", jest: "29.7.0", "jest-junit": "16.0.0", kleur: "4.1.5", "mock-stdin": "1.0.0", "new-github-issue-url": "0.2.1", "node-fetch": "3.3.2", "npm-packlist": "5.1.3", open: "7.4.2", "p-map": "4.0.0", "read-package-up": "11.0.0", resolve: "1.22.10", "string-width": "7.2.0", "strip-ansi": "6.0.1", "strip-indent": "4.0.0", "temp-dir": "2.0.0", tempy: "1.0.1", "terminal-link": "4.0.0", tmp: "0.2.3", "ts-node": "10.9.2", "ts-pattern": "5.6.2", "ts-toolbelt": "9.6.0", typescript: "5.4.5", yarn: "1.22.22" }, dependencies: { "@prisma/config": "workspace:*", "@prisma/debug": "workspace:*", "@prisma/dmmf": "workspace:*", "@prisma/driver-adapter-utils": "workspace:*", "@prisma/engines": "workspace:*", "@prisma/fetch-engine": "workspace:*", "@prisma/generator": "workspace:*", "@prisma/generator-helper": "workspace:*", "@prisma/get-platform": "workspace:*", "@prisma/prisma-schema-wasm": "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc", "@prisma/schema-engine-wasm": "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc", "@prisma/schema-files-loader": "workspace:*", arg: "5.0.2", prompts: "2.4.2" }, peerDependencies: { typescript: ">=5.1.0" }, peerDependenciesMeta: { typescript: { optional: true } }, sideEffects: false };
});
var fi = te((uh, Oc) => {
  Oc.exports = { name: "@prisma/engines-version", version: "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc", main: "index.js", types: "index.d.ts", license: "Apache-2.0", author: "Tim Suchanek <<EMAIL>>", prisma: { enginesVersion: "8047c96bbd92db98a2abc7c9323ce77c02c89dbc" }, repository: { type: "git", url: "https://github.com/prisma/engines-wrapper.git", directory: "packages/engines-version" }, devDependencies: { "@types/node": "18.19.76", typescript: "4.9.5" }, files: ["index.js", "index.d.ts"], scripts: { build: "tsc -d" } };
});
var Yt = te((Kt) => {
  "use strict";
  Object.defineProperty(Kt, "__esModule", { value: true });
  Kt.enginesVersion = void 0;
  Kt.enginesVersion = fi().prisma.enginesVersion;
});
var ps = te((Sh, cs) => {
  "use strict";
  cs.exports = (e10) => {
    let r = e10.match(/^[ \t]*(?=\S)/gm);
    return r ? r.reduce((t, n) => Math.min(t, n.length), 1 / 0) : 0;
  };
});
var Ei = te((Ah, fs) => {
  "use strict";
  fs.exports = (e10, r = 1, t) => {
    if (t = { indent: " ", includeEmptyLines: false, ...t }, typeof e10 != "string") throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e10}\``);
    if (typeof r != "number") throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof r}\``);
    if (typeof t.indent != "string") throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof t.indent}\``);
    if (r === 0) return e10;
    let n = t.includeEmptyLines ? /^/gm : /^(?!\s*$)/gm;
    return e10.replace(n, t.indent.repeat(r));
  };
});
var bs = te((Dh, ys) => {
  "use strict";
  ys.exports = ({ onlyFirst: e10 = false } = {}) => {
    let r = ["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)", "(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");
    return new RegExp(r, e10 ? void 0 : "g");
  };
});
var Pi = te((Oh, Es) => {
  "use strict";
  var Bc = bs();
  Es.exports = (e10) => typeof e10 == "string" ? e10.replace(Bc(), "") : e10;
});
var ws = te((Mh, Uc) => {
  Uc.exports = { name: "dotenv", version: "16.5.0", description: "Loads environment variables from .env file", main: "lib/main.js", types: "lib/main.d.ts", exports: { ".": { types: "./lib/main.d.ts", require: "./lib/main.js", default: "./lib/main.js" }, "./config": "./config.js", "./config.js": "./config.js", "./lib/env-options": "./lib/env-options.js", "./lib/env-options.js": "./lib/env-options.js", "./lib/cli-options": "./lib/cli-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./package.json": "./package.json" }, scripts: { "dts-check": "tsc --project tests/types/tsconfig.json", lint: "standard", pretest: "npm run lint && npm run dts-check", test: "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=lcov", prerelease: "npm test", release: "standard-version" }, repository: { type: "git", url: "git://github.com/motdotla/dotenv.git" }, homepage: "https://github.com/motdotla/dotenv#readme", funding: "https://dotenvx.com", keywords: ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], readmeFilename: "README.md", license: "BSD-2-Clause", devDependencies: { "@types/node": "^18.11.3", decache: "^4.6.2", sinon: "^14.0.1", standard: "^17.0.0", "standard-version": "^9.5.0", tap: "^19.2.0", typescript: "^4.8.4" }, engines: { node: ">=12" }, browser: { fs: false } };
});
var Ss = te(($h, _e) => {
  "use strict";
  var Si = fr("node:fs"), Ri = fr("node:path"), Gc = fr("node:os"), Qc = fr("node:crypto"), Wc = ws(), vs = Wc.version, Jc = /(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;
  function Hc(e10) {
    let r = {}, t = e10.toString();
    t = t.replace(/\r\n?/mg, `
`);
    let n;
    for (; (n = Jc.exec(t)) != null; ) {
      let i = n[1], o = n[2] || "";
      o = o.trim();
      let s = o[0];
      o = o.replace(/^(['"`])([\s\S]*)\1$/mg, "$2"), s === '"' && (o = o.replace(/\\n/g, `
`), o = o.replace(/\\r/g, "\r")), r[i] = o;
    }
    return r;
  }
  function Kc(e10) {
    let r = Ts(e10), t = V.configDotenv({ path: r });
    if (!t.parsed) {
      let s = new Error(`MISSING_DATA: Cannot parse ${r} for an unknown reason`);
      throw s.code = "MISSING_DATA", s;
    }
    let n = Ps(e10).split(","), i = n.length, o;
    for (let s = 0; s < i; s++) try {
      let a = n[s].trim(), l = zc(t, a);
      o = V.decrypt(l.ciphertext, l.key);
      break;
    } catch (a) {
      if (s + 1 >= i) throw a;
    }
    return V.parse(o);
  }
  function Yc(e10) {
    console.log(`[dotenv@${vs}][WARN] ${e10}`);
  }
  function rt(e10) {
    console.log(`[dotenv@${vs}][DEBUG] ${e10}`);
  }
  function Ps(e10) {
    return e10 && e10.DOTENV_KEY && e10.DOTENV_KEY.length > 0 ? e10.DOTENV_KEY : process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0 ? process.env.DOTENV_KEY : "";
  }
  function zc(e10, r) {
    let t;
    try {
      t = new URL(r);
    } catch (a) {
      if (a.code === "ERR_INVALID_URL") {
        let l = new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");
        throw l.code = "INVALID_DOTENV_KEY", l;
      }
      throw a;
    }
    let n = t.password;
    if (!n) {
      let a = new Error("INVALID_DOTENV_KEY: Missing key part");
      throw a.code = "INVALID_DOTENV_KEY", a;
    }
    let i = t.searchParams.get("environment");
    if (!i) {
      let a = new Error("INVALID_DOTENV_KEY: Missing environment part");
      throw a.code = "INVALID_DOTENV_KEY", a;
    }
    let o = `DOTENV_VAULT_${i.toUpperCase()}`, s = e10.parsed[o];
    if (!s) {
      let a = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);
      throw a.code = "NOT_FOUND_DOTENV_ENVIRONMENT", a;
    }
    return { ciphertext: s, key: n };
  }
  function Ts(e10) {
    let r = null;
    if (e10 && e10.path && e10.path.length > 0) if (Array.isArray(e10.path)) for (let t of e10.path) Si.existsSync(t) && (r = t.endsWith(".vault") ? t : `${t}.vault`);
    else r = e10.path.endsWith(".vault") ? e10.path : `${e10.path}.vault`;
    else r = Ri.resolve(process.cwd(), ".env.vault");
    return Si.existsSync(r) ? r : null;
  }
  function xs(e10) {
    return e10[0] === "~" ? Ri.join(Gc.homedir(), e10.slice(1)) : e10;
  }
  function Zc(e10) {
    !!(e10 && e10.debug) && rt("Loading env from encrypted .env.vault");
    let t = V._parseVault(e10), n = process.env;
    return e10 && e10.processEnv != null && (n = e10.processEnv), V.populate(n, t, e10), { parsed: t };
  }
  function Xc(e10) {
    let r = Ri.resolve(process.cwd(), ".env"), t = "utf8", n = !!(e10 && e10.debug);
    e10 && e10.encoding ? t = e10.encoding : n && rt("No encoding is specified. UTF-8 is used by default");
    let i = [r];
    if (e10 && e10.path) if (!Array.isArray(e10.path)) i = [xs(e10.path)];
    else {
      i = [];
      for (let l of e10.path) i.push(xs(l));
    }
    let o, s = {};
    for (let l of i) try {
      let u = V.parse(Si.readFileSync(l, { encoding: t }));
      V.populate(s, u, e10);
    } catch (u) {
      n && rt(`Failed to load ${l} ${u.message}`), o = u;
    }
    let a = process.env;
    return e10 && e10.processEnv != null && (a = e10.processEnv), V.populate(a, s, e10), o ? { parsed: s, error: o } : { parsed: s };
  }
  function ep(e10) {
    if (Ps(e10).length === 0) return V.configDotenv(e10);
    let r = Ts(e10);
    return r ? V._configVault(e10) : (Yc(`You set DOTENV_KEY but you are missing a .env.vault file at ${r}. Did you forget to build it?`), V.configDotenv(e10));
  }
  function rp(e10, r) {
    let t = Buffer.from(r.slice(-64), "hex"), n = Buffer.from(e10, "base64"), i = n.subarray(0, 12), o = n.subarray(-16);
    n = n.subarray(12, -16);
    try {
      let s = Qc.createDecipheriv("aes-256-gcm", t, i);
      return s.setAuthTag(o), `${s.update(n)}${s.final()}`;
    } catch (s) {
      let a = s instanceof RangeError, l = s.message === "Invalid key length", u = s.message === "Unsupported state or unable to authenticate data";
      if (a || l) {
        let c = new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");
        throw c.code = "INVALID_DOTENV_KEY", c;
      } else if (u) {
        let c = new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");
        throw c.code = "DECRYPTION_FAILED", c;
      } else throw s;
    }
  }
  function tp(e10, r, t = {}) {
    let n = !!(t && t.debug), i = !!(t && t.override);
    if (typeof r != "object") {
      let o = new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");
      throw o.code = "OBJECT_REQUIRED", o;
    }
    for (let o of Object.keys(r)) Object.prototype.hasOwnProperty.call(e10, o) ? (i === true && (e10[o] = r[o]), n && rt(i === true ? `"${o}" is already defined and WAS overwritten` : `"${o}" is already defined and was NOT overwritten`)) : e10[o] = r[o];
  }
  var V = { configDotenv: Xc, _configVault: Zc, _parseVault: Kc, config: ep, decrypt: rp, parse: Hc, populate: tp };
  _e.exports.configDotenv = V.configDotenv;
  _e.exports._configVault = V._configVault;
  _e.exports._parseVault = V._parseVault;
  _e.exports.config = V.config;
  _e.exports.decrypt = V.decrypt;
  _e.exports.parse = V.parse;
  _e.exports.populate = V.populate;
  _e.exports = V;
});
var Is = te((Wh, rn) => {
  "use strict";
  rn.exports = (e10 = {}) => {
    let r;
    if (e10.repoUrl) r = e10.repoUrl;
    else if (e10.user && e10.repo) r = `https://github.com/${e10.user}/${e10.repo}`;
    else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");
    let t = new URL(`${r}/issues/new`), n = ["body", "title", "labels", "template", "milestone", "assignee", "projects"];
    for (let i of n) {
      let o = e10[i];
      if (o !== void 0) {
        if (i === "labels" || i === "projects") {
          if (!Array.isArray(o)) throw new TypeError(`The \`${i}\` option should be an array`);
          o = o.join(",");
        }
        t.searchParams.set(i, o);
      }
    }
    return t.toString();
  };
  rn.exports.default = rn.exports;
});
var $i = te((Tb, Ys) => {
  "use strict";
  Ys.exports = /* @__PURE__ */ function() {
    function e10(r, t, n, i, o) {
      return r < t || n < t ? r > n ? n + 1 : r + 1 : i === o ? t : t + 1;
    }
    return function(r, t) {
      if (r === t) return 0;
      if (r.length > t.length) {
        var n = r;
        r = t, t = n;
      }
      for (var i = r.length, o = t.length; i > 0 && r.charCodeAt(i - 1) === t.charCodeAt(o - 1); ) i--, o--;
      for (var s = 0; s < i && r.charCodeAt(s) === t.charCodeAt(s); ) s++;
      if (i -= s, o -= s, i === 0 || o < 3) return o;
      var a = 0, l, u, c, p, d, f, h, g, S, P, R, b, k = [];
      for (l = 0; l < i; l++) k.push(l + 1), k.push(r.charCodeAt(s + l));
      for (var me = k.length - 1; a < o - 3; ) for (S = t.charCodeAt(s + (u = a)), P = t.charCodeAt(s + (c = a + 1)), R = t.charCodeAt(s + (p = a + 2)), b = t.charCodeAt(s + (d = a + 3)), f = a += 4, l = 0; l < me; l += 2) h = k[l], g = k[l + 1], u = e10(h, u, c, S, g), c = e10(u, c, p, P, g), p = e10(c, p, d, R, g), f = e10(p, d, f, b, g), k[l] = f, d = p, p = c, c = u, u = h;
      for (; a < o; ) for (S = t.charCodeAt(s + (u = a)), f = ++a, l = 0; l < me; l += 2) h = k[l], k[l] = f = e10(h, u, f, S, k[l + 1]), u = h;
      return f;
    };
  }();
});
var ra = Ro(() => {
  "use strict";
});
var ta = Ro(() => {
  "use strict";
});
var Io = {};
gr(Io, { defineExtension: () => Co, getExtensionContext: () => Ao });
function Co(e10) {
  return typeof e10 == "function" ? e10 : (r) => r.$extends(e10);
}
function Ao(e10) {
  return e10;
}
var Do = {};
gr(Do, { validator: () => ko });
function ko(...e10) {
  return (r) => r;
}
var $t = {};
gr($t, { $: () => Fo, bgBlack: () => bu, bgBlue: () => vu, bgCyan: () => Tu, bgGreen: () => wu, bgMagenta: () => Pu, bgRed: () => Eu, bgWhite: () => Su, bgYellow: () => xu, black: () => fu, blue: () => tr, bold: () => Q, cyan: () => ke, dim: () => Ae, gray: () => Wr, green: () => qe, grey: () => yu, hidden: () => du, inverse: () => pu, italic: () => cu, magenta: () => gu, red: () => ue, reset: () => uu, strikethrough: () => mu, underline: () => K, white: () => hu, yellow: () => Ie });
var Yn;
var Oo;
var _o;
var No;
var Lo = true;
typeof process < "u" && ({ FORCE_COLOR: Yn, NODE_DISABLE_COLORS: Oo, NO_COLOR: _o, TERM: No } = process.env || {}, Lo = process.stdout && process.stdout.isTTY);
var Fo = { enabled: !Oo && _o == null && No !== "dumb" && (Yn != null && Yn !== "0" || Lo) };
function N(e10, r) {
  let t = new RegExp(`\\x1b\\[${r}m`, "g"), n = `\x1B[${e10}m`, i = `\x1B[${r}m`;
  return function(o) {
    return !Fo.enabled || o == null ? o : n + (~("" + o).indexOf(i) ? o.replace(t, i + n) : o) + i;
  };
}
var uu = N(0, 0);
var Q = N(1, 22);
var Ae = N(2, 22);
var cu = N(3, 23);
var K = N(4, 24);
var pu = N(7, 27);
var du = N(8, 28);
var mu = N(9, 29);
var fu = N(30, 39);
var ue = N(31, 39);
var qe = N(32, 39);
var Ie = N(33, 39);
var tr = N(34, 39);
var gu = N(35, 39);
var ke = N(36, 39);
var hu = N(37, 39);
var Wr = N(90, 39);
var yu = N(90, 39);
var bu = N(40, 49);
var Eu = N(41, 49);
var wu = N(42, 49);
var xu = N(43, 49);
var vu = N(44, 49);
var Pu = N(45, 49);
var Tu = N(46, 49);
var Su = N(47, 49);
var Ru = 100;
var Mo = ["green", "yellow", "blue", "magenta", "cyan", "red"];
var Jr = [];
var $o = Date.now();
var Cu = 0;
var zn = typeof process < "u" ? process.env : {};
globalThis.DEBUG ??= zn.DEBUG ?? "";
globalThis.DEBUG_COLORS ??= zn.DEBUG_COLORS ? zn.DEBUG_COLORS === "true" : true;
var Hr = { enable(e10) {
  typeof e10 == "string" && (globalThis.DEBUG = e10);
}, disable() {
  let e10 = globalThis.DEBUG;
  return globalThis.DEBUG = "", e10;
}, enabled(e10) {
  let r = globalThis.DEBUG.split(",").map((i) => i.replace(/[.+?^${}()|[\]\\]/g, "\\$&")), t = r.some((i) => i === "" || i[0] === "-" ? false : e10.match(RegExp(i.split("*").join(".*") + "$"))), n = r.some((i) => i === "" || i[0] !== "-" ? false : e10.match(RegExp(i.slice(1).split("*").join(".*") + "$")));
  return t && !n;
}, log: (...e10) => {
  let [r, t, ...n] = e10;
  (console.warn ?? console.log)(`${r} ${t}`, ...n);
}, formatters: {} };
function Au(e10) {
  let r = { color: Mo[Cu++ % Mo.length], enabled: Hr.enabled(e10), namespace: e10, log: Hr.log, extend: () => {
  } }, t = (...n) => {
    let { enabled: i, namespace: o, color: s, log: a } = r;
    if (n.length !== 0 && Jr.push([o, ...n]), Jr.length > Ru && Jr.shift(), Hr.enabled(o) || i) {
      let l = n.map((c) => typeof c == "string" ? c : Iu(c)), u = `+${Date.now() - $o}ms`;
      $o = Date.now(), globalThis.DEBUG_COLORS ? a($t[s](Q(o)), ...l, $t[s](u)) : a(o, ...l, u);
    }
  };
  return new Proxy(t, { get: (n, i) => r[i], set: (n, i, o) => r[i] = o });
}
var L = new Proxy(Au, { get: (e10, r) => Hr[r], set: (e10, r, t) => Hr[r] = t });
function Iu(e10, r = 2) {
  let t = /* @__PURE__ */ new Set();
  return JSON.stringify(e10, (n, i) => {
    if (typeof i == "object" && i !== null) {
      if (t.has(i)) return "[Circular *]";
      t.add(i);
    } else if (typeof i == "bigint") return i.toString();
    return i;
  }, r);
}
function qo(e10 = 7500) {
  let r = Jr.map(([t, ...n]) => `${t} ${n.map((i) => typeof i == "string" ? i : JSON.stringify(i)).join(" ")}`).join(`
`);
  return r.length < e10 ? r : r.slice(-e10);
}
function jo() {
  Jr.length = 0;
}
var hr = L;
function Zn() {
  let e10 = process.env.PRISMA_QUERY_ENGINE_LIBRARY;
  if (!(e10 && import_node_fs.default.existsSync(e10)) && process.arch === "ia32") throw new Error('The default query engine type (Node-API, "library") is currently not supported for 32bit Node. Please set `engineType = "binary"` in the "generator" block of your "schema.prisma" file (or use the environment variables "PRISMA_CLIENT_ENGINE_TYPE=binary" and/or "PRISMA_CLI_QUERY_ENGINE_TYPE=binary".)');
}
var Xn = ["darwin", "darwin-arm64", "debian-openssl-1.0.x", "debian-openssl-1.1.x", "debian-openssl-3.0.x", "rhel-openssl-1.0.x", "rhel-openssl-1.1.x", "rhel-openssl-3.0.x", "linux-arm64-openssl-1.1.x", "linux-arm64-openssl-1.0.x", "linux-arm64-openssl-3.0.x", "linux-arm-openssl-1.1.x", "linux-arm-openssl-1.0.x", "linux-arm-openssl-3.0.x", "linux-musl", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-1.1.x", "linux-musl-arm64-openssl-3.0.x", "linux-nixos", "linux-static-x64", "linux-static-arm64", "windows", "freebsd11", "freebsd12", "freebsd13", "freebsd14", "freebsd15", "openbsd", "netbsd", "arm"];
var qt = "libquery_engine";
function jt(e10, r) {
  let t = r === "url";
  return e10.includes("windows") ? t ? "query_engine.dll.node" : `query_engine-${e10}.dll.node` : e10.includes("darwin") ? t ? `${qt}.dylib.node` : `${qt}-${e10}.dylib.node` : t ? `${qt}.so.node` : `${qt}-${e10}.so.node`;
}
var De = Symbol.for("@ts-pattern/matcher");
var Du = Symbol.for("@ts-pattern/isVariadic");
var Bt = "@ts-pattern/anonymous-select-key";
var ei = (e10) => !!(e10 && typeof e10 == "object");
var Vt = (e10) => e10 && !!e10[De];
var Ee = (e10, r, t) => {
  if (Vt(e10)) {
    let n = e10[De](), { matched: i, selections: o } = n.match(r);
    return i && o && Object.keys(o).forEach((s) => t(s, o[s])), i;
  }
  if (ei(e10)) {
    if (!ei(r)) return false;
    if (Array.isArray(e10)) {
      if (!Array.isArray(r)) return false;
      let n = [], i = [], o = [];
      for (let s of e10.keys()) {
        let a = e10[s];
        Vt(a) && a[Du] ? o.push(a) : o.length ? i.push(a) : n.push(a);
      }
      if (o.length) {
        if (o.length > 1) throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");
        if (r.length < n.length + i.length) return false;
        let s = r.slice(0, n.length), a = i.length === 0 ? [] : r.slice(-i.length), l = r.slice(n.length, i.length === 0 ? 1 / 0 : -i.length);
        return n.every((u, c) => Ee(u, s[c], t)) && i.every((u, c) => Ee(u, a[c], t)) && (o.length === 0 || Ee(o[0], l, t));
      }
      return e10.length === r.length && e10.every((s, a) => Ee(s, r[a], t));
    }
    return Reflect.ownKeys(e10).every((n) => {
      let i = e10[n];
      return (n in r || Vt(o = i) && o[De]().matcherType === "optional") && Ee(i, r[n], t);
      var o;
    });
  }
  return Object.is(r, e10);
};
var Ge = (e10) => {
  var r, t, n;
  return ei(e10) ? Vt(e10) ? (r = (t = (n = e10[De]()).getSelectionKeys) == null ? void 0 : t.call(n)) != null ? r : [] : Array.isArray(e10) ? Kr(e10, Ge) : Kr(Object.values(e10), Ge) : [];
};
var Kr = (e10, r) => e10.reduce((t, n) => t.concat(r(n)), []);
function ce(e10) {
  return Object.assign(e10, { optional: () => Ou(e10), and: (r) => $(e10, r), or: (r) => _u(e10, r), select: (r) => r === void 0 ? Vo(e10) : Vo(r, e10) });
}
function Ou(e10) {
  return ce({ [De]: () => ({ match: (r) => {
    let t = {}, n = (i, o) => {
      t[i] = o;
    };
    return r === void 0 ? (Ge(e10).forEach((i) => n(i, void 0)), { matched: true, selections: t }) : { matched: Ee(e10, r, n), selections: t };
  }, getSelectionKeys: () => Ge(e10), matcherType: "optional" }) });
}
function $(...e10) {
  return ce({ [De]: () => ({ match: (r) => {
    let t = {}, n = (i, o) => {
      t[i] = o;
    };
    return { matched: e10.every((i) => Ee(i, r, n)), selections: t };
  }, getSelectionKeys: () => Kr(e10, Ge), matcherType: "and" }) });
}
function _u(...e10) {
  return ce({ [De]: () => ({ match: (r) => {
    let t = {}, n = (i, o) => {
      t[i] = o;
    };
    return Kr(e10, Ge).forEach((i) => n(i, void 0)), { matched: e10.some((i) => Ee(i, r, n)), selections: t };
  }, getSelectionKeys: () => Kr(e10, Ge), matcherType: "or" }) });
}
function A(e10) {
  return { [De]: () => ({ match: (r) => ({ matched: !!e10(r) }) }) };
}
function Vo(...e10) {
  let r = typeof e10[0] == "string" ? e10[0] : void 0, t = e10.length === 2 ? e10[1] : typeof e10[0] == "string" ? void 0 : e10[0];
  return ce({ [De]: () => ({ match: (n) => {
    let i = { [r ?? Bt]: n };
    return { matched: t === void 0 || Ee(t, n, (o, s) => {
      i[o] = s;
    }), selections: i };
  }, getSelectionKeys: () => [r ?? Bt].concat(t === void 0 ? [] : Ge(t)) }) });
}
function ye(e10) {
  return typeof e10 == "number";
}
function je(e10) {
  return typeof e10 == "string";
}
function Ve(e10) {
  return typeof e10 == "bigint";
}
var ng = ce(A(function(e10) {
  return true;
}));
var Be = (e10) => Object.assign(ce(e10), { startsWith: (r) => {
  return Be($(e10, (t = r, A((n) => je(n) && n.startsWith(t)))));
  var t;
}, endsWith: (r) => {
  return Be($(e10, (t = r, A((n) => je(n) && n.endsWith(t)))));
  var t;
}, minLength: (r) => Be($(e10, ((t) => A((n) => je(n) && n.length >= t))(r))), length: (r) => Be($(e10, ((t) => A((n) => je(n) && n.length === t))(r))), maxLength: (r) => Be($(e10, ((t) => A((n) => je(n) && n.length <= t))(r))), includes: (r) => {
  return Be($(e10, (t = r, A((n) => je(n) && n.includes(t)))));
  var t;
}, regex: (r) => {
  return Be($(e10, (t = r, A((n) => je(n) && !!n.match(t)))));
  var t;
} });
var ig = Be(A(je));
var be = (e10) => Object.assign(ce(e10), { between: (r, t) => be($(e10, ((n, i) => A((o) => ye(o) && n <= o && i >= o))(r, t))), lt: (r) => be($(e10, ((t) => A((n) => ye(n) && n < t))(r))), gt: (r) => be($(e10, ((t) => A((n) => ye(n) && n > t))(r))), lte: (r) => be($(e10, ((t) => A((n) => ye(n) && n <= t))(r))), gte: (r) => be($(e10, ((t) => A((n) => ye(n) && n >= t))(r))), int: () => be($(e10, A((r) => ye(r) && Number.isInteger(r)))), finite: () => be($(e10, A((r) => ye(r) && Number.isFinite(r)))), positive: () => be($(e10, A((r) => ye(r) && r > 0))), negative: () => be($(e10, A((r) => ye(r) && r < 0))) });
var og = be(A(ye));
var Ue = (e10) => Object.assign(ce(e10), { between: (r, t) => Ue($(e10, ((n, i) => A((o) => Ve(o) && n <= o && i >= o))(r, t))), lt: (r) => Ue($(e10, ((t) => A((n) => Ve(n) && n < t))(r))), gt: (r) => Ue($(e10, ((t) => A((n) => Ve(n) && n > t))(r))), lte: (r) => Ue($(e10, ((t) => A((n) => Ve(n) && n <= t))(r))), gte: (r) => Ue($(e10, ((t) => A((n) => Ve(n) && n >= t))(r))), positive: () => Ue($(e10, A((r) => Ve(r) && r > 0))), negative: () => Ue($(e10, A((r) => Ve(r) && r < 0))) });
var sg = Ue(A(Ve));
var ag = ce(A(function(e10) {
  return typeof e10 == "boolean";
}));
var lg = ce(A(function(e10) {
  return typeof e10 == "symbol";
}));
var ug = ce(A(function(e10) {
  return e10 == null;
}));
var cg = ce(A(function(e10) {
  return e10 != null;
}));
var ri = class extends Error {
  constructor(r) {
    let t;
    try {
      t = JSON.stringify(r);
    } catch {
      t = r;
    }
    super(`Pattern matching error: no pattern matches value ${t}`), this.input = void 0, this.input = r;
  }
};
var ti = { matched: false, value: void 0 };
function yr(e10) {
  return new ni(e10, ti);
}
var ni = class e {
  constructor(r, t) {
    this.input = void 0, this.state = void 0, this.input = r, this.state = t;
  }
  with(...r) {
    if (this.state.matched) return this;
    let t = r[r.length - 1], n = [r[0]], i;
    r.length === 3 && typeof r[1] == "function" ? i = r[1] : r.length > 2 && n.push(...r.slice(1, r.length - 1));
    let o = false, s = {}, a = (u, c) => {
      o = true, s[u] = c;
    }, l = !n.some((u) => Ee(u, this.input, a)) || i && !i(this.input) ? ti : { matched: true, value: t(o ? Bt in s ? s[Bt] : s : this.input, this.input) };
    return new e(this.input, l);
  }
  when(r, t) {
    if (this.state.matched) return this;
    let n = !!r(this.input);
    return new e(this.input, n ? { matched: true, value: t(this.input, this.input) } : ti);
  }
  otherwise(r) {
    return this.state.matched ? this.state.value : r(this.input);
  }
  exhaustive() {
    if (this.state.matched) return this.state.value;
    throw new ri(this.input);
  }
  run() {
    return this.exhaustive();
  }
  returnType() {
    return this;
  }
};
var Nu = { warn: Ie("prisma:warn") };
var Lu = { warn: () => !process.env.PRISMA_DISABLE_WARNINGS };
function Ut(e10, ...r) {
  Lu.warn() && console.warn(`${Nu.warn} ${e10}`, ...r);
}
var $u = (0, import_node_util.promisify)(import_node_child_process.default.exec);
var z = hr("prisma:get-platform");
var qu = ["1.0.x", "1.1.x", "3.0.x"];
async function Qo() {
  let e10 = import_node_os.default.platform(), r = process.arch;
  if (e10 === "freebsd") {
    let s = await Qt("freebsd-version");
    if (s && s.trim().length > 0) {
      let l = /^(\d+)\.?/.exec(s);
      if (l) return { platform: "freebsd", targetDistro: `freebsd${l[1]}`, arch: r };
    }
  }
  if (e10 !== "linux") return { platform: e10, arch: r };
  let t = await Vu(), n = await Ku(), i = Uu({ arch: r, archFromUname: n, familyDistro: t.familyDistro }), { libssl: o } = await Gu(i);
  return { platform: "linux", libssl: o, arch: r, archFromUname: n, ...t };
}
function ju(e10) {
  let r = /^ID="?([^"\n]*)"?$/im, t = /^ID_LIKE="?([^"\n]*)"?$/im, n = r.exec(e10), i = n && n[1] && n[1].toLowerCase() || "", o = t.exec(e10), s = o && o[1] && o[1].toLowerCase() || "", a = yr({ id: i, idLike: s }).with({ id: "alpine" }, ({ id: l }) => ({ targetDistro: "musl", familyDistro: l, originalDistro: l })).with({ id: "raspbian" }, ({ id: l }) => ({ targetDistro: "arm", familyDistro: "debian", originalDistro: l })).with({ id: "nixos" }, ({ id: l }) => ({ targetDistro: "nixos", originalDistro: l, familyDistro: "nixos" })).with({ id: "debian" }, { id: "ubuntu" }, ({ id: l }) => ({ targetDistro: "debian", familyDistro: "debian", originalDistro: l })).with({ id: "rhel" }, { id: "centos" }, { id: "fedora" }, ({ id: l }) => ({ targetDistro: "rhel", familyDistro: "rhel", originalDistro: l })).when(({ idLike: l }) => l.includes("debian") || l.includes("ubuntu"), ({ id: l }) => ({ targetDistro: "debian", familyDistro: "debian", originalDistro: l })).when(({ idLike: l }) => i === "arch" || l.includes("arch"), ({ id: l }) => ({ targetDistro: "debian", familyDistro: "arch", originalDistro: l })).when(({ idLike: l }) => l.includes("centos") || l.includes("fedora") || l.includes("rhel") || l.includes("suse"), ({ id: l }) => ({ targetDistro: "rhel", familyDistro: "rhel", originalDistro: l })).otherwise(({ id: l }) => ({ targetDistro: void 0, familyDistro: void 0, originalDistro: l }));
  return z(`Found distro info:
${JSON.stringify(a, null, 2)}`), a;
}
async function Vu() {
  let e10 = "/etc/os-release";
  try {
    let r = await import_promises.default.readFile(e10, { encoding: "utf-8" });
    return ju(r);
  } catch {
    return { targetDistro: void 0, familyDistro: void 0, originalDistro: void 0 };
  }
}
function Bu(e10) {
  let r = /^OpenSSL\s(\d+\.\d+)\.\d+/.exec(e10);
  if (r) {
    let t = `${r[1]}.x`;
    return Wo(t);
  }
}
function Bo(e10) {
  let r = /libssl\.so\.(\d)(\.\d)?/.exec(e10);
  if (r) {
    let t = `${r[1]}${r[2] ?? ".0"}.x`;
    return Wo(t);
  }
}
function Wo(e10) {
  let r = (() => {
    if (Ho(e10)) return e10;
    let t = e10.split(".");
    return t[1] = "0", t.join(".");
  })();
  if (qu.includes(r)) return r;
}
function Uu(e10) {
  return yr(e10).with({ familyDistro: "musl" }, () => (z('Trying platform-specific paths for "alpine"'), ["/lib", "/usr/lib"])).with({ familyDistro: "debian" }, ({ archFromUname: r }) => (z('Trying platform-specific paths for "debian" (and "ubuntu")'), [`/usr/lib/${r}-linux-gnu`, `/lib/${r}-linux-gnu`])).with({ familyDistro: "rhel" }, () => (z('Trying platform-specific paths for "rhel"'), ["/lib64", "/usr/lib64"])).otherwise(({ familyDistro: r, arch: t, archFromUname: n }) => (z(`Don't know any platform-specific paths for "${r}" on ${t} (${n})`), []));
}
async function Gu(e10) {
  let r = 'grep -v "libssl.so.0"', t = await Uo(e10);
  if (t) {
    z(`Found libssl.so file using platform-specific paths: ${t}`);
    let o = Bo(t);
    if (z(`The parsed libssl version is: ${o}`), o) return { libssl: o, strategy: "libssl-specific-path" };
  }
  z('Falling back to "ldconfig" and other generic paths');
  let n = await Qt(`ldconfig -p | sed "s/.*=>s*//" | sed "s|.*/||" | grep libssl | sort | ${r}`);
  if (n || (n = await Uo(["/lib64", "/usr/lib64", "/lib", "/usr/lib"])), n) {
    z(`Found libssl.so file using "ldconfig" or other generic paths: ${n}`);
    let o = Bo(n);
    if (z(`The parsed libssl version is: ${o}`), o) return { libssl: o, strategy: "ldconfig" };
  }
  let i = await Qt("openssl version -v");
  if (i) {
    z(`Found openssl binary with version: ${i}`);
    let o = Bu(i);
    if (z(`The parsed openssl version is: ${o}`), o) return { libssl: o, strategy: "openssl-binary" };
  }
  return z("Couldn't find any version of libssl or OpenSSL in the system"), {};
}
async function Uo(e10) {
  for (let r of e10) {
    let t = await Qu(r);
    if (t) return t;
  }
}
async function Qu(e10) {
  try {
    return (await import_promises.default.readdir(e10)).find((t) => t.startsWith("libssl.so.") && !t.startsWith("libssl.so.0"));
  } catch (r) {
    if (r.code === "ENOENT") return;
    throw r;
  }
}
async function nr() {
  let { binaryTarget: e10 } = await Jo();
  return e10;
}
function Wu(e10) {
  return e10.binaryTarget !== void 0;
}
async function oi() {
  let { memoized: e10, ...r } = await Jo();
  return r;
}
var Gt = {};
async function Jo() {
  if (Wu(Gt)) return Promise.resolve({ ...Gt, memoized: true });
  let e10 = await Qo(), r = Ju(e10);
  return Gt = { ...e10, binaryTarget: r }, { ...Gt, memoized: false };
}
function Ju(e10) {
  let { platform: r, arch: t, archFromUname: n, libssl: i, targetDistro: o, familyDistro: s, originalDistro: a } = e10;
  r === "linux" && !["x64", "arm64"].includes(t) && Ut(`Prisma only officially supports Linux on amd64 (x86_64) and arm64 (aarch64) system architectures (detected "${t}" instead). If you are using your own custom Prisma engines, you can ignore this warning, as long as you've compiled the engines for your system architecture "${n}".`);
  let l = "1.1.x";
  if (r === "linux" && i === void 0) {
    let c = yr({ familyDistro: s }).with({ familyDistro: "debian" }, () => "Please manually install OpenSSL via `apt-get update -y && apt-get install -y openssl` and try installing Prisma again. If you're running Prisma on Docker, add this command to your Dockerfile, or switch to an image that already has OpenSSL installed.").otherwise(() => "Please manually install OpenSSL and try installing Prisma again.");
    Ut(`Prisma failed to detect the libssl/openssl version to use, and may not work as expected. Defaulting to "openssl-${l}".
${c}`);
  }
  let u = "debian";
  if (r === "linux" && o === void 0 && z(`Distro is "${a}". Falling back to Prisma engines built for "${u}".`), r === "darwin" && t === "arm64") return "darwin-arm64";
  if (r === "darwin") return "darwin";
  if (r === "win32") return "windows";
  if (r === "freebsd") return o;
  if (r === "openbsd") return "openbsd";
  if (r === "netbsd") return "netbsd";
  if (r === "linux" && o === "nixos") return "linux-nixos";
  if (r === "linux" && t === "arm64") return `${o === "musl" ? "linux-musl-arm64" : "linux-arm64"}-openssl-${i || l}`;
  if (r === "linux" && t === "arm") return `linux-arm-openssl-${i || l}`;
  if (r === "linux" && o === "musl") {
    let c = "linux-musl";
    return !i || Ho(i) ? c : `${c}-openssl-${i}`;
  }
  return r === "linux" && o && i ? `${o}-openssl-${i}` : (r !== "linux" && Ut(`Prisma detected unknown OS "${r}" and may not work as expected. Defaulting to "linux".`), i ? `${u}-openssl-${i}` : o ? `${o}-openssl-${l}` : `${u}-openssl-${l}`);
}
async function Hu(e10) {
  try {
    return await e10();
  } catch {
    return;
  }
}
function Qt(e10) {
  return Hu(async () => {
    let r = await $u(e10);
    return z(`Command "${e10}" successfully returned "${r.stdout}"`), r.stdout;
  });
}
async function Ku() {
  return typeof import_node_os.default.machine == "function" ? import_node_os.default.machine() : (await Qt("uname -m"))?.trim();
}
function Ho(e10) {
  return e10.startsWith("1.");
}
var Jt = {};
gr(Jt, { beep: () => xc, clearScreen: () => yc, clearTerminal: () => bc, cursorBackward: () => tc, cursorDown: () => ec, cursorForward: () => rc, cursorGetPosition: () => oc, cursorHide: () => lc, cursorLeft: () => zo, cursorMove: () => Xu, cursorNextLine: () => sc, cursorPrevLine: () => ac, cursorRestorePosition: () => ic, cursorSavePosition: () => nc, cursorShow: () => uc, cursorTo: () => Zu, cursorUp: () => Yo, enterAlternativeScreen: () => Ec, eraseDown: () => mc, eraseEndLine: () => pc, eraseLine: () => Zo, eraseLines: () => cc, eraseScreen: () => si, eraseStartLine: () => dc, eraseUp: () => fc, exitAlternativeScreen: () => wc, iTerm: () => Tc, image: () => Pc, link: () => vc, scrollDown: () => hc, scrollUp: () => gc });
var Wt = globalThis.window?.document !== void 0;
var vg = globalThis.process?.versions?.node !== void 0;
var Pg = globalThis.process?.versions?.bun !== void 0;
var Tg = globalThis.Deno?.version?.deno !== void 0;
var Sg = globalThis.process?.versions?.electron !== void 0;
var Rg = globalThis.navigator?.userAgent?.includes("jsdom") === true;
var Cg = typeof WorkerGlobalScope < "u" && globalThis instanceof WorkerGlobalScope;
var Ag = typeof DedicatedWorkerGlobalScope < "u" && globalThis instanceof DedicatedWorkerGlobalScope;
var Ig = typeof SharedWorkerGlobalScope < "u" && globalThis instanceof SharedWorkerGlobalScope;
var kg = typeof ServiceWorkerGlobalScope < "u" && globalThis instanceof ServiceWorkerGlobalScope;
var Yr = globalThis.navigator?.userAgentData?.platform;
var Dg = Yr === "macOS" || globalThis.navigator?.platform === "MacIntel" || globalThis.navigator?.userAgent?.includes(" Mac ") === true || globalThis.process?.platform === "darwin";
var Og = Yr === "Windows" || globalThis.navigator?.platform === "Win32" || globalThis.process?.platform === "win32";
var _g = Yr === "Linux" || globalThis.navigator?.platform?.startsWith("Linux") === true || globalThis.navigator?.userAgent?.includes(" Linux ") === true || globalThis.process?.platform === "linux";
var Ng = Yr === "iOS" || globalThis.navigator?.platform === "MacIntel" && globalThis.navigator?.maxTouchPoints > 1 || /iPad|iPhone|iPod/.test(globalThis.navigator?.platform);
var Lg = Yr === "Android" || globalThis.navigator?.platform === "Android" || globalThis.navigator?.userAgent?.includes(" Android ") === true || globalThis.process?.platform === "android";
var I = "\x1B[";
var Zr = "\x1B]";
var br = "\x07";
var zr = ";";
var Ko = !Wt && import_node_process.default.env.TERM_PROGRAM === "Apple_Terminal";
var Yu = !Wt && import_node_process.default.platform === "win32";
var zu = Wt ? () => {
  throw new Error("`process.cwd()` only works in Node.js, not the browser.");
} : import_node_process.default.cwd;
var Zu = (e10, r) => {
  if (typeof e10 != "number") throw new TypeError("The `x` argument is required");
  return typeof r != "number" ? I + (e10 + 1) + "G" : I + (r + 1) + zr + (e10 + 1) + "H";
};
var Xu = (e10, r) => {
  if (typeof e10 != "number") throw new TypeError("The `x` argument is required");
  let t = "";
  return e10 < 0 ? t += I + -e10 + "D" : e10 > 0 && (t += I + e10 + "C"), r < 0 ? t += I + -r + "A" : r > 0 && (t += I + r + "B"), t;
};
var Yo = (e10 = 1) => I + e10 + "A";
var ec = (e10 = 1) => I + e10 + "B";
var rc = (e10 = 1) => I + e10 + "C";
var tc = (e10 = 1) => I + e10 + "D";
var zo = I + "G";
var nc = Ko ? "\x1B7" : I + "s";
var ic = Ko ? "\x1B8" : I + "u";
var oc = I + "6n";
var sc = I + "E";
var ac = I + "F";
var lc = I + "?25l";
var uc = I + "?25h";
var cc = (e10) => {
  let r = "";
  for (let t = 0; t < e10; t++) r += Zo + (t < e10 - 1 ? Yo() : "");
  return e10 && (r += zo), r;
};
var pc = I + "K";
var dc = I + "1K";
var Zo = I + "2K";
var mc = I + "J";
var fc = I + "1J";
var si = I + "2J";
var gc = I + "S";
var hc = I + "T";
var yc = "\x1Bc";
var bc = Yu ? `${si}${I}0f` : `${si}${I}3J${I}H`;
var Ec = I + "?1049h";
var wc = I + "?1049l";
var xc = br;
var vc = (e10, r) => [Zr, "8", zr, zr, r, br, e10, Zr, "8", zr, zr, br].join("");
var Pc = (e10, r = {}) => {
  let t = `${Zr}1337;File=inline=1`;
  return r.width && (t += `;width=${r.width}`), r.height && (t += `;height=${r.height}`), r.preserveAspectRatio === false && (t += ";preserveAspectRatio=0"), t + ":" + Buffer.from(e10).toString("base64") + br;
};
var Tc = { setCwd: (e10 = zu()) => `${Zr}50;CurrentDir=${e10}${br}`, annotation(e10, r = {}) {
  let t = `${Zr}1337;`, n = r.x !== void 0, i = r.y !== void 0;
  if ((n || i) && !(n && i && r.length !== void 0)) throw new Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");
  return e10 = e10.replaceAll("|", ""), t += r.isHidden ? "AddHiddenAnnotation=" : "AddAnnotation=", r.length > 0 ? t += (n ? [e10, r.length, r.x, r.y] : [r.length, e10]).join("|") : t += e10, t + br;
} };
var Ht = ne(os(), 1);
function ir(e10, r, { target: t = "stdout", ...n } = {}) {
  return Ht.default[t] ? Jt.link(e10, r) : n.fallback === false ? e10 : typeof n.fallback == "function" ? n.fallback(e10, r) : `${e10} (​${r}​)`;
}
ir.isSupported = Ht.default.stdout;
ir.stderr = (e10, r, t = {}) => ir(e10, r, { target: "stderr", ...t });
ir.stderr.isSupported = Ht.default.stderr;
function di(e10) {
  return ir(e10, e10, { fallback: K });
}
var Ic = ss();
var mi = Ic.version;
function wr(e10) {
  let r = kc();
  return r || (e10?.config.engineType === "library" ? "library" : e10?.config.engineType === "binary" ? "binary" : e10?.config.engineType === "client" ? "client" : Dc(e10));
}
function kc() {
  let e10 = process.env.PRISMA_CLIENT_ENGINE_TYPE;
  return e10 === "library" ? "library" : e10 === "binary" ? "binary" : e10 === "client" ? "client" : void 0;
}
function Dc(e10) {
  return e10?.previewFeatures.includes("queryCompiler") ? "client" : "library";
}
var _c = ne(Yt());
var Nc = ne(Yt());
var gh = L("prisma:engines");
function as() {
  return import_node_path.default.join(__dirname, "../");
}
import_node_path.default.join(__dirname, "../query-engine-darwin");
import_node_path.default.join(__dirname, "../query-engine-darwin-arm64");
import_node_path.default.join(__dirname, "../query-engine-debian-openssl-1.0.x");
import_node_path.default.join(__dirname, "../query-engine-debian-openssl-1.1.x");
import_node_path.default.join(__dirname, "../query-engine-debian-openssl-3.0.x");
import_node_path.default.join(__dirname, "../query-engine-linux-static-x64");
import_node_path.default.join(__dirname, "../query-engine-linux-static-arm64");
import_node_path.default.join(__dirname, "../query-engine-rhel-openssl-1.0.x");
import_node_path.default.join(__dirname, "../query-engine-rhel-openssl-1.1.x");
import_node_path.default.join(__dirname, "../query-engine-rhel-openssl-3.0.x");
import_node_path.default.join(__dirname, "../libquery_engine-darwin.dylib.node");
import_node_path.default.join(__dirname, "../libquery_engine-darwin-arm64.dylib.node");
import_node_path.default.join(__dirname, "../libquery_engine-debian-openssl-1.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-debian-openssl-1.1.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-debian-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-arm64-openssl-1.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-arm64-openssl-1.1.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-arm64-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-musl.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-musl-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-rhel-openssl-1.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-rhel-openssl-1.1.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-rhel-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../query_engine-windows.dll.node");
var us = hr("chmodPlusX");
function gi(e10) {
  if (process.platform === "win32") return;
  let r = import_node_fs2.default.statSync(e10), t = r.mode | 64 | 8 | 1;
  if (r.mode === t) {
    us(`Execution permissions of ${e10} are fine`);
    return;
  }
  let n = t.toString(8).slice(-3);
  us(`Have to call chmodPlusX on ${e10}`), import_node_fs2.default.chmodSync(e10, n);
}
function hi(e10) {
  let r = e10.e, t = (a) => `Prisma cannot find the required \`${a}\` system library in your system`, n = r.message.includes("cannot open shared object file"), i = `Please refer to the documentation about Prisma's system requirements: ${di("https://pris.ly/d/system-requirements")}`, o = `Unable to require(\`${Ae(e10.id)}\`).`, s = yr({ message: r.message, code: r.code }).with({ code: "ENOENT" }, () => "File does not exist.").when(({ message: a }) => n && a.includes("libz"), () => `${t("libz")}. Please install it and try again.`).when(({ message: a }) => n && a.includes("libgcc_s"), () => `${t("libgcc_s")}. Please install it and try again.`).when(({ message: a }) => n && a.includes("libssl"), () => {
    let a = e10.platformInfo.libssl ? `openssl-${e10.platformInfo.libssl}` : "openssl";
    return `${t("libssl")}. Please install ${a} and try again.`;
  }).when(({ message: a }) => a.includes("GLIBC"), () => `Prisma has detected an incompatible version of the \`glibc\` C standard library installed in your system. This probably means your system may be too old to run Prisma. ${i}`).when(({ message: a }) => e10.platformInfo.platform === "linux" && a.includes("symbol not found"), () => `The Prisma engines are not compatible with your system ${e10.platformInfo.originalDistro} on (${e10.platformInfo.archFromUname}) which uses the \`${e10.platformInfo.binaryTarget}\` binaryTarget by default. ${i}`).otherwise(() => `The Prisma engines do not seem to be compatible with your system. ${i}`);
  return `${o}
${s}

Details: ${r.message}`;
}
var ds = ne(ps(), 1);
function yi(e10) {
  let r = (0, ds.default)(e10);
  if (r === 0) return e10;
  let t = new RegExp(`^[ \\t]{${r}}`, "gm");
  return e10.replace(t, "");
}
var ms = "prisma+postgres";
var zt = `${ms}:`;
function Zt(e10) {
  return e10?.toString().startsWith(`${zt}//`) ?? false;
}
function bi(e10) {
  if (!Zt(e10)) return false;
  let { host: r } = new URL(e10);
  return r.includes("localhost") || r.includes("127.0.0.1") || r.includes("[::1]");
}
var gs = ne(Ei());
function xi(e10) {
  return String(new wi(e10));
}
var wi = class {
  constructor(r) {
    this.config = r;
  }
  toString() {
    let { config: r } = this, t = r.provider.fromEnvVar ? `env("${r.provider.fromEnvVar}")` : r.provider.value, n = JSON.parse(JSON.stringify({ provider: t, binaryTargets: Lc(r.binaryTargets) }));
    return `generator ${r.name} {
${(0, gs.default)(Fc(n), 2)}
}`;
  }
};
function Lc(e10) {
  let r;
  if (e10.length > 0) {
    let t = e10.find((n) => n.fromEnvVar !== null);
    t ? r = `env("${t.fromEnvVar}")` : r = e10.map((n) => n.native ? "native" : n.value);
  } else r = void 0;
  return r;
}
function Fc(e10) {
  let r = Object.keys(e10).reduce((t, n) => Math.max(t, n.length), 0);
  return Object.entries(e10).map(([t, n]) => `${t.padEnd(r)} = ${Mc(n)}`).join(`
`);
}
function Mc(e10) {
  return JSON.parse(JSON.stringify(e10, (r, t) => Array.isArray(t) ? `[${t.map((n) => JSON.stringify(n)).join(", ")}]` : JSON.stringify(t)));
}
var et = {};
gr(et, { error: () => jc, info: () => qc, log: () => $c, query: () => Vc, should: () => hs, tags: () => Xr, warn: () => vi });
var Xr = { error: ue("prisma:error"), warn: Ie("prisma:warn"), info: ke("prisma:info"), query: tr("prisma:query") };
var hs = { warn: () => !process.env.PRISMA_DISABLE_WARNINGS };
function $c(...e10) {
  console.log(...e10);
}
function vi(e10, ...r) {
  hs.warn() && console.warn(`${Xr.warn} ${e10}`, ...r);
}
function qc(e10, ...r) {
  console.info(`${Xr.info} ${e10}`, ...r);
}
function jc(e10, ...r) {
  console.error(`${Xr.error} ${e10}`, ...r);
}
function Vc(e10, ...r) {
  console.log(`${Xr.query} ${e10}`, ...r);
}
function Xt(e10, r) {
  if (!e10) throw new Error(`${r}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`);
}
function Oe(e10, r) {
  throw new Error(r);
}
function Ti(e10) {
  return import_node_path2.default.sep === import_node_path2.default.posix.sep ? e10 : e10.split(import_node_path2.default.sep).join(import_node_path2.default.posix.sep);
}
var Ii = ne(Ss());
function Rs(e10) {
  let r = e10.ignoreProcessEnv ? {} : process.env, t = (n) => n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o, s) {
    let a = /(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);
    if (!a) return o;
    let l = a[1], u, c;
    if (l === "\\") c = a[0], u = c.replace("\\$", "$");
    else {
      let p = a[2];
      c = a[0].substring(l.length), u = Object.hasOwnProperty.call(r, p) ? r[p] : e10.parsed[p] || "", u = t(u);
    }
    return o.replace(c, u);
  }, n) ?? n;
  for (let n in e10.parsed) {
    let i = Object.hasOwnProperty.call(r, n) ? r[n] : e10.parsed[n];
    e10.parsed[n] = t(i);
  }
  for (let n in e10.parsed) r[n] = e10.parsed[n];
  return e10;
}
var Ai = hr("prisma:tryLoadEnv");
function nt({ rootEnvPath: e10, schemaEnvPath: r }, t = { conflictCheck: "none" }) {
  let n = Cs(e10);
  t.conflictCheck !== "none" && np(n, r, t.conflictCheck);
  let i = null;
  return As(n?.path, r) || (i = Cs(r)), !n && !i && Ai("No Environment variables loaded"), i?.dotenvResult.error ? console.error(ue(Q("Schema Env Error: ")) + i.dotenvResult.error) : { message: [n?.message, i?.message].filter(Boolean).join(`
`), parsed: { ...n?.dotenvResult?.parsed, ...i?.dotenvResult?.parsed } };
}
function np(e10, r, t) {
  let n = e10?.dotenvResult.parsed, i = !As(e10?.path, r);
  if (n && r && i && import_node_fs3.default.existsSync(r)) {
    let o = Ii.default.parse(import_node_fs3.default.readFileSync(r)), s = [];
    for (let a in o) n[a] === o[a] && s.push(a);
    if (s.length > 0) {
      let a = import_node_path3.default.relative(process.cwd(), e10.path), l = import_node_path3.default.relative(process.cwd(), r);
      if (t === "error") {
        let u = `There is a conflict between env var${s.length > 1 ? "s" : ""} in ${K(a)} and ${K(l)}
Conflicting env vars:
${s.map((c) => `  ${Q(c)}`).join(`
`)}

We suggest to move the contents of ${K(l)} to ${K(a)} to consolidate your env vars.
`;
        throw new Error(u);
      } else if (t === "warn") {
        let u = `Conflict for env var${s.length > 1 ? "s" : ""} ${s.map((c) => Q(c)).join(", ")} in ${K(a)} and ${K(l)}
Env vars from ${K(l)} overwrite the ones from ${K(a)}
      `;
        console.warn(`${Ie("warn(prisma)")} ${u}`);
      }
    }
  }
}
function Cs(e10) {
  if (ip(e10)) {
    Ai(`Environment variables loaded from ${e10}`);
    let r = Ii.default.config({ path: e10, debug: process.env.DOTENV_CONFIG_DEBUG ? true : void 0 });
    return { dotenvResult: Rs(r), message: Ae(`Environment variables loaded from ${import_node_path3.default.relative(process.cwd(), e10)}`), path: e10 };
  } else Ai(`Environment variables not found at ${e10}`);
  return null;
}
function As(e10, r) {
  return e10 && r && import_node_path3.default.resolve(e10) === import_node_path3.default.resolve(r);
}
function ip(e10) {
  return !!(e10 && import_node_fs3.default.existsSync(e10));
}
function ki(e10, r) {
  return Object.prototype.hasOwnProperty.call(e10, r);
}
function xr(e10, r) {
  let t = {};
  for (let n of Object.keys(e10)) t[n] = r(e10[n], n);
  return t;
}
function Di(e10, r) {
  if (e10.length === 0) return;
  let t = e10[0];
  for (let n = 1; n < e10.length; n++) r(t, e10[n]) < 0 && (t = e10[n]);
  return t;
}
function x(e10, r) {
  Object.defineProperty(e10, "name", { value: r, configurable: true });
}
var ks = /* @__PURE__ */ new Set();
var tn = (e10, r, ...t) => {
  ks.has(e10) || (ks.add(e10), vi(r, ...t));
};
var T = class e2 extends Error {
  clientVersion;
  errorCode;
  retryable;
  constructor(r, t, n) {
    super(r), this.name = "PrismaClientInitializationError", this.clientVersion = t, this.errorCode = n, Error.captureStackTrace(e2);
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientInitializationError";
  }
};
x(T, "PrismaClientInitializationError");
var Z = class extends Error {
  code;
  meta;
  clientVersion;
  batchRequestIdx;
  constructor(r, { code: t, clientVersion: n, meta: i, batchRequestIdx: o }) {
    super(r), this.name = "PrismaClientKnownRequestError", this.code = t, this.clientVersion = n, this.meta = i, Object.defineProperty(this, "batchRequestIdx", { value: o, enumerable: false, writable: true });
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientKnownRequestError";
  }
};
x(Z, "PrismaClientKnownRequestError");
var de = class extends Error {
  clientVersion;
  constructor(r, t) {
    super(r), this.name = "PrismaClientRustPanicError", this.clientVersion = t;
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientRustPanicError";
  }
};
x(de, "PrismaClientRustPanicError");
var q = class extends Error {
  clientVersion;
  batchRequestIdx;
  constructor(r, { clientVersion: t, batchRequestIdx: n }) {
    super(r), this.name = "PrismaClientUnknownRequestError", this.clientVersion = t, Object.defineProperty(this, "batchRequestIdx", { value: n, writable: true, enumerable: false });
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientUnknownRequestError";
  }
};
x(q, "PrismaClientUnknownRequestError");
var X = class extends Error {
  name = "PrismaClientValidationError";
  clientVersion;
  constructor(r, { clientVersion: t }) {
    super(r), this.clientVersion = t;
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientValidationError";
  }
};
x(X, "PrismaClientValidationError");
var vr = 9e15;
var Ke = 1e9;
var Oi = "0123456789abcdef";
var an = "2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058";
var ln = "3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789";
var _i = { precision: 20, rounding: 4, modulo: 1, toExpNeg: -7, toExpPos: 21, minE: -vr, maxE: vr, crypto: false };
var Ns;
var Ne;
var w = true;
var cn = "[DecimalError] ";
var He = cn + "Invalid argument: ";
var Ls = cn + "Precision limit exceeded";
var Fs = cn + "crypto unavailable";
var Ms = "[object Decimal]";
var Y = Math.floor;
var B = Math.pow;
var op = /^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i;
var sp = /^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i;
var ap = /^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i;
var $s = /^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;
var fe = 1e7;
var E = 7;
var lp = 9007199254740991;
var up = an.length - 1;
var Ni = ln.length - 1;
var m = { toStringTag: Ms };
m.absoluteValue = m.abs = function() {
  var e10 = new this.constructor(this);
  return e10.s < 0 && (e10.s = 1), y(e10);
};
m.ceil = function() {
  return y(new this.constructor(this), this.e + 1, 2);
};
m.clampedTo = m.clamp = function(e10, r) {
  var t, n = this, i = n.constructor;
  if (e10 = new i(e10), r = new i(r), !e10.s || !r.s) return new i(NaN);
  if (e10.gt(r)) throw Error(He + r);
  return t = n.cmp(e10), t < 0 ? e10 : n.cmp(r) > 0 ? r : new i(n);
};
m.comparedTo = m.cmp = function(e10) {
  var r, t, n, i, o = this, s = o.d, a = (e10 = new o.constructor(e10)).d, l = o.s, u = e10.s;
  if (!s || !a) return !l || !u ? NaN : l !== u ? l : s === a ? 0 : !s ^ l < 0 ? 1 : -1;
  if (!s[0] || !a[0]) return s[0] ? l : a[0] ? -u : 0;
  if (l !== u) return l;
  if (o.e !== e10.e) return o.e > e10.e ^ l < 0 ? 1 : -1;
  for (n = s.length, i = a.length, r = 0, t = n < i ? n : i; r < t; ++r) if (s[r] !== a[r]) return s[r] > a[r] ^ l < 0 ? 1 : -1;
  return n === i ? 0 : n > i ^ l < 0 ? 1 : -1;
};
m.cosine = m.cos = function() {
  var e10, r, t = this, n = t.constructor;
  return t.d ? t.d[0] ? (e10 = n.precision, r = n.rounding, n.precision = e10 + Math.max(t.e, t.sd()) + E, n.rounding = 1, t = cp(n, Us(n, t)), n.precision = e10, n.rounding = r, y(Ne == 2 || Ne == 3 ? t.neg() : t, e10, r, true)) : new n(1) : new n(NaN);
};
m.cubeRoot = m.cbrt = function() {
  var e10, r, t, n, i, o, s, a, l, u, c = this, p = c.constructor;
  if (!c.isFinite() || c.isZero()) return new p(c);
  for (w = false, o = c.s * B(c.s * c, 1 / 3), !o || Math.abs(o) == 1 / 0 ? (t = W(c.d), e10 = c.e, (o = (e10 - t.length + 1) % 3) && (t += o == 1 || o == -2 ? "0" : "00"), o = B(t, 1 / 3), e10 = Y((e10 + 1) / 3) - (e10 % 3 == (e10 < 0 ? -1 : 2)), o == 1 / 0 ? t = "5e" + e10 : (t = o.toExponential(), t = t.slice(0, t.indexOf("e") + 1) + e10), n = new p(t), n.s = c.s) : n = new p(o.toString()), s = (e10 = p.precision) + 3; ; ) if (a = n, l = a.times(a).times(a), u = l.plus(c), n = _(u.plus(c).times(a), u.plus(l), s + 2, 1), W(a.d).slice(0, s) === (t = W(n.d)).slice(0, s)) if (t = t.slice(s - 3, s + 1), t == "9999" || !i && t == "4999") {
    if (!i && (y(a, e10 + 1, 0), a.times(a).times(a).eq(c))) {
      n = a;
      break;
    }
    s += 4, i = 1;
  } else {
    (!+t || !+t.slice(1) && t.charAt(0) == "5") && (y(n, e10 + 1, 1), r = !n.times(n).times(n).eq(c));
    break;
  }
  return w = true, y(n, e10, p.rounding, r);
};
m.decimalPlaces = m.dp = function() {
  var e10, r = this.d, t = NaN;
  if (r) {
    if (e10 = r.length - 1, t = (e10 - Y(this.e / E)) * E, e10 = r[e10], e10) for (; e10 % 10 == 0; e10 /= 10) t--;
    t < 0 && (t = 0);
  }
  return t;
};
m.dividedBy = m.div = function(e10) {
  return _(this, new this.constructor(e10));
};
m.dividedToIntegerBy = m.divToInt = function(e10) {
  var r = this, t = r.constructor;
  return y(_(r, new t(e10), 0, 1, 1), t.precision, t.rounding);
};
m.equals = m.eq = function(e10) {
  return this.cmp(e10) === 0;
};
m.floor = function() {
  return y(new this.constructor(this), this.e + 1, 3);
};
m.greaterThan = m.gt = function(e10) {
  return this.cmp(e10) > 0;
};
m.greaterThanOrEqualTo = m.gte = function(e10) {
  var r = this.cmp(e10);
  return r == 1 || r === 0;
};
m.hyperbolicCosine = m.cosh = function() {
  var e10, r, t, n, i, o = this, s = o.constructor, a = new s(1);
  if (!o.isFinite()) return new s(o.s ? 1 / 0 : NaN);
  if (o.isZero()) return a;
  t = s.precision, n = s.rounding, s.precision = t + Math.max(o.e, o.sd()) + 4, s.rounding = 1, i = o.d.length, i < 32 ? (e10 = Math.ceil(i / 3), r = (1 / dn(4, e10)).toString()) : (e10 = 16, r = "2.3283064365386962890625e-10"), o = Pr(s, 1, o.times(r), new s(1), true);
  for (var l, u = e10, c = new s(8); u--; ) l = o.times(o), o = a.minus(l.times(c.minus(l.times(c))));
  return y(o, s.precision = t, s.rounding = n, true);
};
m.hyperbolicSine = m.sinh = function() {
  var e10, r, t, n, i = this, o = i.constructor;
  if (!i.isFinite() || i.isZero()) return new o(i);
  if (r = o.precision, t = o.rounding, o.precision = r + Math.max(i.e, i.sd()) + 4, o.rounding = 1, n = i.d.length, n < 3) i = Pr(o, 2, i, i, true);
  else {
    e10 = 1.4 * Math.sqrt(n), e10 = e10 > 16 ? 16 : e10 | 0, i = i.times(1 / dn(5, e10)), i = Pr(o, 2, i, i, true);
    for (var s, a = new o(5), l = new o(16), u = new o(20); e10--; ) s = i.times(i), i = i.times(a.plus(s.times(l.times(s).plus(u))));
  }
  return o.precision = r, o.rounding = t, y(i, r, t, true);
};
m.hyperbolicTangent = m.tanh = function() {
  var e10, r, t = this, n = t.constructor;
  return t.isFinite() ? t.isZero() ? new n(t) : (e10 = n.precision, r = n.rounding, n.precision = e10 + 7, n.rounding = 1, _(t.sinh(), t.cosh(), n.precision = e10, n.rounding = r)) : new n(t.s);
};
m.inverseCosine = m.acos = function() {
  var e10 = this, r = e10.constructor, t = e10.abs().cmp(1), n = r.precision, i = r.rounding;
  return t !== -1 ? t === 0 ? e10.isNeg() ? we(r, n, i) : new r(0) : new r(NaN) : e10.isZero() ? we(r, n + 4, i).times(0.5) : (r.precision = n + 6, r.rounding = 1, e10 = new r(1).minus(e10).div(e10.plus(1)).sqrt().atan(), r.precision = n, r.rounding = i, e10.times(2));
};
m.inverseHyperbolicCosine = m.acosh = function() {
  var e10, r, t = this, n = t.constructor;
  return t.lte(1) ? new n(t.eq(1) ? 0 : NaN) : t.isFinite() ? (e10 = n.precision, r = n.rounding, n.precision = e10 + Math.max(Math.abs(t.e), t.sd()) + 4, n.rounding = 1, w = false, t = t.times(t).minus(1).sqrt().plus(t), w = true, n.precision = e10, n.rounding = r, t.ln()) : new n(t);
};
m.inverseHyperbolicSine = m.asinh = function() {
  var e10, r, t = this, n = t.constructor;
  return !t.isFinite() || t.isZero() ? new n(t) : (e10 = n.precision, r = n.rounding, n.precision = e10 + 2 * Math.max(Math.abs(t.e), t.sd()) + 6, n.rounding = 1, w = false, t = t.times(t).plus(1).sqrt().plus(t), w = true, n.precision = e10, n.rounding = r, t.ln());
};
m.inverseHyperbolicTangent = m.atanh = function() {
  var e10, r, t, n, i = this, o = i.constructor;
  return i.isFinite() ? i.e >= 0 ? new o(i.abs().eq(1) ? i.s / 0 : i.isZero() ? i : NaN) : (e10 = o.precision, r = o.rounding, n = i.sd(), Math.max(n, e10) < 2 * -i.e - 1 ? y(new o(i), e10, r, true) : (o.precision = t = n - i.e, i = _(i.plus(1), new o(1).minus(i), t + e10, 1), o.precision = e10 + 4, o.rounding = 1, i = i.ln(), o.precision = e10, o.rounding = r, i.times(0.5))) : new o(NaN);
};
m.inverseSine = m.asin = function() {
  var e10, r, t, n, i = this, o = i.constructor;
  return i.isZero() ? new o(i) : (r = i.abs().cmp(1), t = o.precision, n = o.rounding, r !== -1 ? r === 0 ? (e10 = we(o, t + 4, n).times(0.5), e10.s = i.s, e10) : new o(NaN) : (o.precision = t + 6, o.rounding = 1, i = i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(), o.precision = t, o.rounding = n, i.times(2)));
};
m.inverseTangent = m.atan = function() {
  var e10, r, t, n, i, o, s, a, l, u = this, c = u.constructor, p = c.precision, d = c.rounding;
  if (u.isFinite()) {
    if (u.isZero()) return new c(u);
    if (u.abs().eq(1) && p + 4 <= Ni) return s = we(c, p + 4, d).times(0.25), s.s = u.s, s;
  } else {
    if (!u.s) return new c(NaN);
    if (p + 4 <= Ni) return s = we(c, p + 4, d).times(0.5), s.s = u.s, s;
  }
  for (c.precision = a = p + 10, c.rounding = 1, t = Math.min(28, a / E + 2 | 0), e10 = t; e10; --e10) u = u.div(u.times(u).plus(1).sqrt().plus(1));
  for (w = false, r = Math.ceil(a / E), n = 1, l = u.times(u), s = new c(u), i = u; e10 !== -1; ) if (i = i.times(l), o = s.minus(i.div(n += 2)), i = i.times(l), s = o.plus(i.div(n += 2)), s.d[r] !== void 0) for (e10 = r; s.d[e10] === o.d[e10] && e10--; ) ;
  return t && (s = s.times(2 << t - 1)), w = true, y(s, c.precision = p, c.rounding = d, true);
};
m.isFinite = function() {
  return !!this.d;
};
m.isInteger = m.isInt = function() {
  return !!this.d && Y(this.e / E) > this.d.length - 2;
};
m.isNaN = function() {
  return !this.s;
};
m.isNegative = m.isNeg = function() {
  return this.s < 0;
};
m.isPositive = m.isPos = function() {
  return this.s > 0;
};
m.isZero = function() {
  return !!this.d && this.d[0] === 0;
};
m.lessThan = m.lt = function(e10) {
  return this.cmp(e10) < 0;
};
m.lessThanOrEqualTo = m.lte = function(e10) {
  return this.cmp(e10) < 1;
};
m.logarithm = m.log = function(e10) {
  var r, t, n, i, o, s, a, l, u = this, c = u.constructor, p = c.precision, d = c.rounding, f = 5;
  if (e10 == null) e10 = new c(10), r = true;
  else {
    if (e10 = new c(e10), t = e10.d, e10.s < 0 || !t || !t[0] || e10.eq(1)) return new c(NaN);
    r = e10.eq(10);
  }
  if (t = u.d, u.s < 0 || !t || !t[0] || u.eq(1)) return new c(t && !t[0] ? -1 / 0 : u.s != 1 ? NaN : t ? 0 : 1 / 0);
  if (r) if (t.length > 1) o = true;
  else {
    for (i = t[0]; i % 10 === 0; ) i /= 10;
    o = i !== 1;
  }
  if (w = false, a = p + f, s = Je(u, a), n = r ? un(c, a + 10) : Je(e10, a), l = _(s, n, a, 1), it(l.d, i = p, d)) do
    if (a += 10, s = Je(u, a), n = r ? un(c, a + 10) : Je(e10, a), l = _(s, n, a, 1), !o) {
      +W(l.d).slice(i + 1, i + 15) + 1 == 1e14 && (l = y(l, p + 1, 0));
      break;
    }
  while (it(l.d, i += 10, d));
  return w = true, y(l, p, d);
};
m.minus = m.sub = function(e10) {
  var r, t, n, i, o, s, a, l, u, c, p, d, f = this, h = f.constructor;
  if (e10 = new h(e10), !f.d || !e10.d) return !f.s || !e10.s ? e10 = new h(NaN) : f.d ? e10.s = -e10.s : e10 = new h(e10.d || f.s !== e10.s ? f : NaN), e10;
  if (f.s != e10.s) return e10.s = -e10.s, f.plus(e10);
  if (u = f.d, d = e10.d, a = h.precision, l = h.rounding, !u[0] || !d[0]) {
    if (d[0]) e10.s = -e10.s;
    else if (u[0]) e10 = new h(f);
    else return new h(l === 3 ? -0 : 0);
    return w ? y(e10, a, l) : e10;
  }
  if (t = Y(e10.e / E), c = Y(f.e / E), u = u.slice(), o = c - t, o) {
    for (p = o < 0, p ? (r = u, o = -o, s = d.length) : (r = d, t = c, s = u.length), n = Math.max(Math.ceil(a / E), s) + 2, o > n && (o = n, r.length = 1), r.reverse(), n = o; n--; ) r.push(0);
    r.reverse();
  } else {
    for (n = u.length, s = d.length, p = n < s, p && (s = n), n = 0; n < s; n++) if (u[n] != d[n]) {
      p = u[n] < d[n];
      break;
    }
    o = 0;
  }
  for (p && (r = u, u = d, d = r, e10.s = -e10.s), s = u.length, n = d.length - s; n > 0; --n) u[s++] = 0;
  for (n = d.length; n > o; ) {
    if (u[--n] < d[n]) {
      for (i = n; i && u[--i] === 0; ) u[i] = fe - 1;
      --u[i], u[n] += fe;
    }
    u[n] -= d[n];
  }
  for (; u[--s] === 0; ) u.pop();
  for (; u[0] === 0; u.shift()) --t;
  return u[0] ? (e10.d = u, e10.e = pn(u, t), w ? y(e10, a, l) : e10) : new h(l === 3 ? -0 : 0);
};
m.modulo = m.mod = function(e10) {
  var r, t = this, n = t.constructor;
  return e10 = new n(e10), !t.d || !e10.s || e10.d && !e10.d[0] ? new n(NaN) : !e10.d || t.d && !t.d[0] ? y(new n(t), n.precision, n.rounding) : (w = false, n.modulo == 9 ? (r = _(t, e10.abs(), 0, 3, 1), r.s *= e10.s) : r = _(t, e10, 0, n.modulo, 1), r = r.times(e10), w = true, t.minus(r));
};
m.naturalExponential = m.exp = function() {
  return Li(this);
};
m.naturalLogarithm = m.ln = function() {
  return Je(this);
};
m.negated = m.neg = function() {
  var e10 = new this.constructor(this);
  return e10.s = -e10.s, y(e10);
};
m.plus = m.add = function(e10) {
  var r, t, n, i, o, s, a, l, u, c, p = this, d = p.constructor;
  if (e10 = new d(e10), !p.d || !e10.d) return !p.s || !e10.s ? e10 = new d(NaN) : p.d || (e10 = new d(e10.d || p.s === e10.s ? p : NaN)), e10;
  if (p.s != e10.s) return e10.s = -e10.s, p.minus(e10);
  if (u = p.d, c = e10.d, a = d.precision, l = d.rounding, !u[0] || !c[0]) return c[0] || (e10 = new d(p)), w ? y(e10, a, l) : e10;
  if (o = Y(p.e / E), n = Y(e10.e / E), u = u.slice(), i = o - n, i) {
    for (i < 0 ? (t = u, i = -i, s = c.length) : (t = c, n = o, s = u.length), o = Math.ceil(a / E), s = o > s ? o + 1 : s + 1, i > s && (i = s, t.length = 1), t.reverse(); i--; ) t.push(0);
    t.reverse();
  }
  for (s = u.length, i = c.length, s - i < 0 && (i = s, t = c, c = u, u = t), r = 0; i; ) r = (u[--i] = u[i] + c[i] + r) / fe | 0, u[i] %= fe;
  for (r && (u.unshift(r), ++n), s = u.length; u[--s] == 0; ) u.pop();
  return e10.d = u, e10.e = pn(u, n), w ? y(e10, a, l) : e10;
};
m.precision = m.sd = function(e10) {
  var r, t = this;
  if (e10 !== void 0 && e10 !== !!e10 && e10 !== 1 && e10 !== 0) throw Error(He + e10);
  return t.d ? (r = qs(t.d), e10 && t.e + 1 > r && (r = t.e + 1)) : r = NaN, r;
};
m.round = function() {
  var e10 = this, r = e10.constructor;
  return y(new r(e10), e10.e + 1, r.rounding);
};
m.sine = m.sin = function() {
  var e10, r, t = this, n = t.constructor;
  return t.isFinite() ? t.isZero() ? new n(t) : (e10 = n.precision, r = n.rounding, n.precision = e10 + Math.max(t.e, t.sd()) + E, n.rounding = 1, t = dp(n, Us(n, t)), n.precision = e10, n.rounding = r, y(Ne > 2 ? t.neg() : t, e10, r, true)) : new n(NaN);
};
m.squareRoot = m.sqrt = function() {
  var e10, r, t, n, i, o, s = this, a = s.d, l = s.e, u = s.s, c = s.constructor;
  if (u !== 1 || !a || !a[0]) return new c(!u || u < 0 && (!a || a[0]) ? NaN : a ? s : 1 / 0);
  for (w = false, u = Math.sqrt(+s), u == 0 || u == 1 / 0 ? (r = W(a), (r.length + l) % 2 == 0 && (r += "0"), u = Math.sqrt(r), l = Y((l + 1) / 2) - (l < 0 || l % 2), u == 1 / 0 ? r = "5e" + l : (r = u.toExponential(), r = r.slice(0, r.indexOf("e") + 1) + l), n = new c(r)) : n = new c(u.toString()), t = (l = c.precision) + 3; ; ) if (o = n, n = o.plus(_(s, o, t + 2, 1)).times(0.5), W(o.d).slice(0, t) === (r = W(n.d)).slice(0, t)) if (r = r.slice(t - 3, t + 1), r == "9999" || !i && r == "4999") {
    if (!i && (y(o, l + 1, 0), o.times(o).eq(s))) {
      n = o;
      break;
    }
    t += 4, i = 1;
  } else {
    (!+r || !+r.slice(1) && r.charAt(0) == "5") && (y(n, l + 1, 1), e10 = !n.times(n).eq(s));
    break;
  }
  return w = true, y(n, l, c.rounding, e10);
};
m.tangent = m.tan = function() {
  var e10, r, t = this, n = t.constructor;
  return t.isFinite() ? t.isZero() ? new n(t) : (e10 = n.precision, r = n.rounding, n.precision = e10 + 10, n.rounding = 1, t = t.sin(), t.s = 1, t = _(t, new n(1).minus(t.times(t)).sqrt(), e10 + 10, 0), n.precision = e10, n.rounding = r, y(Ne == 2 || Ne == 4 ? t.neg() : t, e10, r, true)) : new n(NaN);
};
m.times = m.mul = function(e10) {
  var r, t, n, i, o, s, a, l, u, c = this, p = c.constructor, d = c.d, f = (e10 = new p(e10)).d;
  if (e10.s *= c.s, !d || !d[0] || !f || !f[0]) return new p(!e10.s || d && !d[0] && !f || f && !f[0] && !d ? NaN : !d || !f ? e10.s / 0 : e10.s * 0);
  for (t = Y(c.e / E) + Y(e10.e / E), l = d.length, u = f.length, l < u && (o = d, d = f, f = o, s = l, l = u, u = s), o = [], s = l + u, n = s; n--; ) o.push(0);
  for (n = u; --n >= 0; ) {
    for (r = 0, i = l + n; i > n; ) a = o[i] + f[n] * d[i - n - 1] + r, o[i--] = a % fe | 0, r = a / fe | 0;
    o[i] = (o[i] + r) % fe | 0;
  }
  for (; !o[--s]; ) o.pop();
  return r ? ++t : o.shift(), e10.d = o, e10.e = pn(o, t), w ? y(e10, p.precision, p.rounding) : e10;
};
m.toBinary = function(e10, r) {
  return Fi(this, 2, e10, r);
};
m.toDecimalPlaces = m.toDP = function(e10, r) {
  var t = this, n = t.constructor;
  return t = new n(t), e10 === void 0 ? t : (ie(e10, 0, Ke), r === void 0 ? r = n.rounding : ie(r, 0, 8), y(t, e10 + t.e + 1, r));
};
m.toExponential = function(e10, r) {
  var t, n = this, i = n.constructor;
  return e10 === void 0 ? t = xe(n, true) : (ie(e10, 0, Ke), r === void 0 ? r = i.rounding : ie(r, 0, 8), n = y(new i(n), e10 + 1, r), t = xe(n, true, e10 + 1)), n.isNeg() && !n.isZero() ? "-" + t : t;
};
m.toFixed = function(e10, r) {
  var t, n, i = this, o = i.constructor;
  return e10 === void 0 ? t = xe(i) : (ie(e10, 0, Ke), r === void 0 ? r = o.rounding : ie(r, 0, 8), n = y(new o(i), e10 + i.e + 1, r), t = xe(n, false, e10 + n.e + 1)), i.isNeg() && !i.isZero() ? "-" + t : t;
};
m.toFraction = function(e10) {
  var r, t, n, i, o, s, a, l, u, c, p, d, f = this, h = f.d, g = f.constructor;
  if (!h) return new g(f);
  if (u = t = new g(1), n = l = new g(0), r = new g(n), o = r.e = qs(h) - f.e - 1, s = o % E, r.d[0] = B(10, s < 0 ? E + s : s), e10 == null) e10 = o > 0 ? r : u;
  else {
    if (a = new g(e10), !a.isInt() || a.lt(u)) throw Error(He + a);
    e10 = a.gt(r) ? o > 0 ? r : u : a;
  }
  for (w = false, a = new g(W(h)), c = g.precision, g.precision = o = h.length * E * 2; p = _(a, r, 0, 1, 1), i = t.plus(p.times(n)), i.cmp(e10) != 1; ) t = n, n = i, i = u, u = l.plus(p.times(i)), l = i, i = r, r = a.minus(p.times(i)), a = i;
  return i = _(e10.minus(t), n, 0, 1, 1), l = l.plus(i.times(u)), t = t.plus(i.times(n)), l.s = u.s = f.s, d = _(u, n, o, 1).minus(f).abs().cmp(_(l, t, o, 1).minus(f).abs()) < 1 ? [u, n] : [l, t], g.precision = c, w = true, d;
};
m.toHexadecimal = m.toHex = function(e10, r) {
  return Fi(this, 16, e10, r);
};
m.toNearest = function(e10, r) {
  var t = this, n = t.constructor;
  if (t = new n(t), e10 == null) {
    if (!t.d) return t;
    e10 = new n(1), r = n.rounding;
  } else {
    if (e10 = new n(e10), r === void 0 ? r = n.rounding : ie(r, 0, 8), !t.d) return e10.s ? t : e10;
    if (!e10.d) return e10.s && (e10.s = t.s), e10;
  }
  return e10.d[0] ? (w = false, t = _(t, e10, 0, r, 1).times(e10), w = true, y(t)) : (e10.s = t.s, t = e10), t;
};
m.toNumber = function() {
  return +this;
};
m.toOctal = function(e10, r) {
  return Fi(this, 8, e10, r);
};
m.toPower = m.pow = function(e10) {
  var r, t, n, i, o, s, a = this, l = a.constructor, u = +(e10 = new l(e10));
  if (!a.d || !e10.d || !a.d[0] || !e10.d[0]) return new l(B(+a, u));
  if (a = new l(a), a.eq(1)) return a;
  if (n = l.precision, o = l.rounding, e10.eq(1)) return y(a, n, o);
  if (r = Y(e10.e / E), r >= e10.d.length - 1 && (t = u < 0 ? -u : u) <= lp) return i = js(l, a, t, n), e10.s < 0 ? new l(1).div(i) : y(i, n, o);
  if (s = a.s, s < 0) {
    if (r < e10.d.length - 1) return new l(NaN);
    if ((e10.d[r] & 1) == 0 && (s = 1), a.e == 0 && a.d[0] == 1 && a.d.length == 1) return a.s = s, a;
  }
  return t = B(+a, u), r = t == 0 || !isFinite(t) ? Y(u * (Math.log("0." + W(a.d)) / Math.LN10 + a.e + 1)) : new l(t + "").e, r > l.maxE + 1 || r < l.minE - 1 ? new l(r > 0 ? s / 0 : 0) : (w = false, l.rounding = a.s = 1, t = Math.min(12, (r + "").length), i = Li(e10.times(Je(a, n + t)), n), i.d && (i = y(i, n + 5, 1), it(i.d, n, o) && (r = n + 10, i = y(Li(e10.times(Je(a, r + t)), r), r + 5, 1), +W(i.d).slice(n + 1, n + 15) + 1 == 1e14 && (i = y(i, n + 1, 0)))), i.s = s, w = true, l.rounding = o, y(i, n, o));
};
m.toPrecision = function(e10, r) {
  var t, n = this, i = n.constructor;
  return e10 === void 0 ? t = xe(n, n.e <= i.toExpNeg || n.e >= i.toExpPos) : (ie(e10, 1, Ke), r === void 0 ? r = i.rounding : ie(r, 0, 8), n = y(new i(n), e10, r), t = xe(n, e10 <= n.e || n.e <= i.toExpNeg, e10)), n.isNeg() && !n.isZero() ? "-" + t : t;
};
m.toSignificantDigits = m.toSD = function(e10, r) {
  var t = this, n = t.constructor;
  return e10 === void 0 ? (e10 = n.precision, r = n.rounding) : (ie(e10, 1, Ke), r === void 0 ? r = n.rounding : ie(r, 0, 8)), y(new n(t), e10, r);
};
m.toString = function() {
  var e10 = this, r = e10.constructor, t = xe(e10, e10.e <= r.toExpNeg || e10.e >= r.toExpPos);
  return e10.isNeg() && !e10.isZero() ? "-" + t : t;
};
m.truncated = m.trunc = function() {
  return y(new this.constructor(this), this.e + 1, 1);
};
m.valueOf = m.toJSON = function() {
  var e10 = this, r = e10.constructor, t = xe(e10, e10.e <= r.toExpNeg || e10.e >= r.toExpPos);
  return e10.isNeg() ? "-" + t : t;
};
function W(e10) {
  var r, t, n, i = e10.length - 1, o = "", s = e10[0];
  if (i > 0) {
    for (o += s, r = 1; r < i; r++) n = e10[r] + "", t = E - n.length, t && (o += We(t)), o += n;
    s = e10[r], n = s + "", t = E - n.length, t && (o += We(t));
  } else if (s === 0) return "0";
  for (; s % 10 === 0; ) s /= 10;
  return o + s;
}
function ie(e10, r, t) {
  if (e10 !== ~~e10 || e10 < r || e10 > t) throw Error(He + e10);
}
function it(e10, r, t, n) {
  var i, o, s, a;
  for (o = e10[0]; o >= 10; o /= 10) --r;
  return --r < 0 ? (r += E, i = 0) : (i = Math.ceil((r + 1) / E), r %= E), o = B(10, E - r), a = e10[i] % o | 0, n == null ? r < 3 ? (r == 0 ? a = a / 100 | 0 : r == 1 && (a = a / 10 | 0), s = t < 4 && a == 99999 || t > 3 && a == 49999 || a == 5e4 || a == 0) : s = (t < 4 && a + 1 == o || t > 3 && a + 1 == o / 2) && (e10[i + 1] / o / 100 | 0) == B(10, r - 2) - 1 || (a == o / 2 || a == 0) && (e10[i + 1] / o / 100 | 0) == 0 : r < 4 ? (r == 0 ? a = a / 1e3 | 0 : r == 1 ? a = a / 100 | 0 : r == 2 && (a = a / 10 | 0), s = (n || t < 4) && a == 9999 || !n && t > 3 && a == 4999) : s = ((n || t < 4) && a + 1 == o || !n && t > 3 && a + 1 == o / 2) && (e10[i + 1] / o / 1e3 | 0) == B(10, r - 3) - 1, s;
}
function on(e10, r, t) {
  for (var n, i = [0], o, s = 0, a = e10.length; s < a; ) {
    for (o = i.length; o--; ) i[o] *= r;
    for (i[0] += Oi.indexOf(e10.charAt(s++)), n = 0; n < i.length; n++) i[n] > t - 1 && (i[n + 1] === void 0 && (i[n + 1] = 0), i[n + 1] += i[n] / t | 0, i[n] %= t);
  }
  return i.reverse();
}
function cp(e10, r) {
  var t, n, i;
  if (r.isZero()) return r;
  n = r.d.length, n < 32 ? (t = Math.ceil(n / 3), i = (1 / dn(4, t)).toString()) : (t = 16, i = "2.3283064365386962890625e-10"), e10.precision += t, r = Pr(e10, 1, r.times(i), new e10(1));
  for (var o = t; o--; ) {
    var s = r.times(r);
    r = s.times(s).minus(s).times(8).plus(1);
  }
  return e10.precision -= t, r;
}
var _ = /* @__PURE__ */ function() {
  function e10(n, i, o) {
    var s, a = 0, l = n.length;
    for (n = n.slice(); l--; ) s = n[l] * i + a, n[l] = s % o | 0, a = s / o | 0;
    return a && n.unshift(a), n;
  }
  function r(n, i, o, s) {
    var a, l;
    if (o != s) l = o > s ? 1 : -1;
    else for (a = l = 0; a < o; a++) if (n[a] != i[a]) {
      l = n[a] > i[a] ? 1 : -1;
      break;
    }
    return l;
  }
  function t(n, i, o, s) {
    for (var a = 0; o--; ) n[o] -= a, a = n[o] < i[o] ? 1 : 0, n[o] = a * s + n[o] - i[o];
    for (; !n[0] && n.length > 1; ) n.shift();
  }
  return function(n, i, o, s, a, l) {
    var u, c, p, d, f, h, g, S, P, R, b, k, me, se, Qr, j, re, Ce, J, mr, Mt = n.constructor, Hn = n.s == i.s ? 1 : -1, H = n.d, O = i.d;
    if (!H || !H[0] || !O || !O[0]) return new Mt(!n.s || !i.s || (H ? O && H[0] == O[0] : !O) ? NaN : H && H[0] == 0 || !O ? Hn * 0 : Hn / 0);
    for (l ? (f = 1, c = n.e - i.e) : (l = fe, f = E, c = Y(n.e / f) - Y(i.e / f)), J = O.length, re = H.length, P = new Mt(Hn), R = P.d = [], p = 0; O[p] == (H[p] || 0); p++) ;
    if (O[p] > (H[p] || 0) && c--, o == null ? (se = o = Mt.precision, s = Mt.rounding) : a ? se = o + (n.e - i.e) + 1 : se = o, se < 0) R.push(1), h = true;
    else {
      if (se = se / f + 2 | 0, p = 0, J == 1) {
        for (d = 0, O = O[0], se++; (p < re || d) && se--; p++) Qr = d * l + (H[p] || 0), R[p] = Qr / O | 0, d = Qr % O | 0;
        h = d || p < re;
      } else {
        for (d = l / (O[0] + 1) | 0, d > 1 && (O = e10(O, d, l), H = e10(H, d, l), J = O.length, re = H.length), j = J, b = H.slice(0, J), k = b.length; k < J; ) b[k++] = 0;
        mr = O.slice(), mr.unshift(0), Ce = O[0], O[1] >= l / 2 && ++Ce;
        do
          d = 0, u = r(O, b, J, k), u < 0 ? (me = b[0], J != k && (me = me * l + (b[1] || 0)), d = me / Ce | 0, d > 1 ? (d >= l && (d = l - 1), g = e10(O, d, l), S = g.length, k = b.length, u = r(g, b, S, k), u == 1 && (d--, t(g, J < S ? mr : O, S, l))) : (d == 0 && (u = d = 1), g = O.slice()), S = g.length, S < k && g.unshift(0), t(b, g, k, l), u == -1 && (k = b.length, u = r(O, b, J, k), u < 1 && (d++, t(b, J < k ? mr : O, k, l))), k = b.length) : u === 0 && (d++, b = [0]), R[p++] = d, u && b[0] ? b[k++] = H[j] || 0 : (b = [H[j]], k = 1);
        while ((j++ < re || b[0] !== void 0) && se--);
        h = b[0] !== void 0;
      }
      R[0] || R.shift();
    }
    if (f == 1) P.e = c, Ns = h;
    else {
      for (p = 1, d = R[0]; d >= 10; d /= 10) p++;
      P.e = p + c * f - 1, y(P, a ? o + P.e + 1 : o, s, h);
    }
    return P;
  };
}();
function y(e10, r, t, n) {
  var i, o, s, a, l, u, c, p, d, f = e10.constructor;
  e: if (r != null) {
    if (p = e10.d, !p) return e10;
    for (i = 1, a = p[0]; a >= 10; a /= 10) i++;
    if (o = r - i, o < 0) o += E, s = r, c = p[d = 0], l = c / B(10, i - s - 1) % 10 | 0;
    else if (d = Math.ceil((o + 1) / E), a = p.length, d >= a) if (n) {
      for (; a++ <= d; ) p.push(0);
      c = l = 0, i = 1, o %= E, s = o - E + 1;
    } else break e;
    else {
      for (c = a = p[d], i = 1; a >= 10; a /= 10) i++;
      o %= E, s = o - E + i, l = s < 0 ? 0 : c / B(10, i - s - 1) % 10 | 0;
    }
    if (n = n || r < 0 || p[d + 1] !== void 0 || (s < 0 ? c : c % B(10, i - s - 1)), u = t < 4 ? (l || n) && (t == 0 || t == (e10.s < 0 ? 3 : 2)) : l > 5 || l == 5 && (t == 4 || n || t == 6 && (o > 0 ? s > 0 ? c / B(10, i - s) : 0 : p[d - 1]) % 10 & 1 || t == (e10.s < 0 ? 8 : 7)), r < 1 || !p[0]) return p.length = 0, u ? (r -= e10.e + 1, p[0] = B(10, (E - r % E) % E), e10.e = -r || 0) : p[0] = e10.e = 0, e10;
    if (o == 0 ? (p.length = d, a = 1, d--) : (p.length = d + 1, a = B(10, E - o), p[d] = s > 0 ? (c / B(10, i - s) % B(10, s) | 0) * a : 0), u) for (; ; ) if (d == 0) {
      for (o = 1, s = p[0]; s >= 10; s /= 10) o++;
      for (s = p[0] += a, a = 1; s >= 10; s /= 10) a++;
      o != a && (e10.e++, p[0] == fe && (p[0] = 1));
      break;
    } else {
      if (p[d] += a, p[d] != fe) break;
      p[d--] = 0, a = 1;
    }
    for (o = p.length; p[--o] === 0; ) p.pop();
  }
  return w && (e10.e > f.maxE ? (e10.d = null, e10.e = NaN) : e10.e < f.minE && (e10.e = 0, e10.d = [0])), e10;
}
function xe(e10, r, t) {
  if (!e10.isFinite()) return Bs(e10);
  var n, i = e10.e, o = W(e10.d), s = o.length;
  return r ? (t && (n = t - s) > 0 ? o = o.charAt(0) + "." + o.slice(1) + We(n) : s > 1 && (o = o.charAt(0) + "." + o.slice(1)), o = o + (e10.e < 0 ? "e" : "e+") + e10.e) : i < 0 ? (o = "0." + We(-i - 1) + o, t && (n = t - s) > 0 && (o += We(n))) : i >= s ? (o += We(i + 1 - s), t && (n = t - i - 1) > 0 && (o = o + "." + We(n))) : ((n = i + 1) < s && (o = o.slice(0, n) + "." + o.slice(n)), t && (n = t - s) > 0 && (i + 1 === s && (o += "."), o += We(n))), o;
}
function pn(e10, r) {
  var t = e10[0];
  for (r *= E; t >= 10; t /= 10) r++;
  return r;
}
function un(e10, r, t) {
  if (r > up) throw w = true, t && (e10.precision = t), Error(Ls);
  return y(new e10(an), r, 1, true);
}
function we(e10, r, t) {
  if (r > Ni) throw Error(Ls);
  return y(new e10(ln), r, t, true);
}
function qs(e10) {
  var r = e10.length - 1, t = r * E + 1;
  if (r = e10[r], r) {
    for (; r % 10 == 0; r /= 10) t--;
    for (r = e10[0]; r >= 10; r /= 10) t++;
  }
  return t;
}
function We(e10) {
  for (var r = ""; e10--; ) r += "0";
  return r;
}
function js(e10, r, t, n) {
  var i, o = new e10(1), s = Math.ceil(n / E + 4);
  for (w = false; ; ) {
    if (t % 2 && (o = o.times(r), Os(o.d, s) && (i = true)), t = Y(t / 2), t === 0) {
      t = o.d.length - 1, i && o.d[t] === 0 && ++o.d[t];
      break;
    }
    r = r.times(r), Os(r.d, s);
  }
  return w = true, o;
}
function Ds(e10) {
  return e10.d[e10.d.length - 1] & 1;
}
function Vs(e10, r, t) {
  for (var n, i, o = new e10(r[0]), s = 0; ++s < r.length; ) {
    if (i = new e10(r[s]), !i.s) {
      o = i;
      break;
    }
    n = o.cmp(i), (n === t || n === 0 && o.s === t) && (o = i);
  }
  return o;
}
function Li(e10, r) {
  var t, n, i, o, s, a, l, u = 0, c = 0, p = 0, d = e10.constructor, f = d.rounding, h = d.precision;
  if (!e10.d || !e10.d[0] || e10.e > 17) return new d(e10.d ? e10.d[0] ? e10.s < 0 ? 0 : 1 / 0 : 1 : e10.s ? e10.s < 0 ? 0 : e10 : NaN);
  for (r == null ? (w = false, l = h) : l = r, a = new d(0.03125); e10.e > -2; ) e10 = e10.times(a), p += 5;
  for (n = Math.log(B(2, p)) / Math.LN10 * 2 + 5 | 0, l += n, t = o = s = new d(1), d.precision = l; ; ) {
    if (o = y(o.times(e10), l, 1), t = t.times(++c), a = s.plus(_(o, t, l, 1)), W(a.d).slice(0, l) === W(s.d).slice(0, l)) {
      for (i = p; i--; ) s = y(s.times(s), l, 1);
      if (r == null) if (u < 3 && it(s.d, l - n, f, u)) d.precision = l += 10, t = o = a = new d(1), c = 0, u++;
      else return y(s, d.precision = h, f, w = true);
      else return d.precision = h, s;
    }
    s = a;
  }
}
function Je(e10, r) {
  var t, n, i, o, s, a, l, u, c, p, d, f = 1, h = 10, g = e10, S = g.d, P = g.constructor, R = P.rounding, b = P.precision;
  if (g.s < 0 || !S || !S[0] || !g.e && S[0] == 1 && S.length == 1) return new P(S && !S[0] ? -1 / 0 : g.s != 1 ? NaN : S ? 0 : g);
  if (r == null ? (w = false, c = b) : c = r, P.precision = c += h, t = W(S), n = t.charAt(0), Math.abs(o = g.e) < 15e14) {
    for (; n < 7 && n != 1 || n == 1 && t.charAt(1) > 3; ) g = g.times(e10), t = W(g.d), n = t.charAt(0), f++;
    o = g.e, n > 1 ? (g = new P("0." + t), o++) : g = new P(n + "." + t.slice(1));
  } else return u = un(P, c + 2, b).times(o + ""), g = Je(new P(n + "." + t.slice(1)), c - h).plus(u), P.precision = b, r == null ? y(g, b, R, w = true) : g;
  for (p = g, l = s = g = _(g.minus(1), g.plus(1), c, 1), d = y(g.times(g), c, 1), i = 3; ; ) {
    if (s = y(s.times(d), c, 1), u = l.plus(_(s, new P(i), c, 1)), W(u.d).slice(0, c) === W(l.d).slice(0, c)) if (l = l.times(2), o !== 0 && (l = l.plus(un(P, c + 2, b).times(o + ""))), l = _(l, new P(f), c, 1), r == null) if (it(l.d, c - h, R, a)) P.precision = c += h, u = s = g = _(p.minus(1), p.plus(1), c, 1), d = y(g.times(g), c, 1), i = a = 1;
    else return y(l, P.precision = b, R, w = true);
    else return P.precision = b, l;
    l = u, i += 2;
  }
}
function Bs(e10) {
  return String(e10.s * e10.s / 0);
}
function sn(e10, r) {
  var t, n, i;
  for ((t = r.indexOf(".")) > -1 && (r = r.replace(".", "")), (n = r.search(/e/i)) > 0 ? (t < 0 && (t = n), t += +r.slice(n + 1), r = r.substring(0, n)) : t < 0 && (t = r.length), n = 0; r.charCodeAt(n) === 48; n++) ;
  for (i = r.length; r.charCodeAt(i - 1) === 48; --i) ;
  if (r = r.slice(n, i), r) {
    if (i -= n, e10.e = t = t - n - 1, e10.d = [], n = (t + 1) % E, t < 0 && (n += E), n < i) {
      for (n && e10.d.push(+r.slice(0, n)), i -= E; n < i; ) e10.d.push(+r.slice(n, n += E));
      r = r.slice(n), n = E - r.length;
    } else n -= i;
    for (; n--; ) r += "0";
    e10.d.push(+r), w && (e10.e > e10.constructor.maxE ? (e10.d = null, e10.e = NaN) : e10.e < e10.constructor.minE && (e10.e = 0, e10.d = [0]));
  } else e10.e = 0, e10.d = [0];
  return e10;
}
function pp(e10, r) {
  var t, n, i, o, s, a, l, u, c;
  if (r.indexOf("_") > -1) {
    if (r = r.replace(/(\d)_(?=\d)/g, "$1"), $s.test(r)) return sn(e10, r);
  } else if (r === "Infinity" || r === "NaN") return +r || (e10.s = NaN), e10.e = NaN, e10.d = null, e10;
  if (sp.test(r)) t = 16, r = r.toLowerCase();
  else if (op.test(r)) t = 2;
  else if (ap.test(r)) t = 8;
  else throw Error(He + r);
  for (o = r.search(/p/i), o > 0 ? (l = +r.slice(o + 1), r = r.substring(2, o)) : r = r.slice(2), o = r.indexOf("."), s = o >= 0, n = e10.constructor, s && (r = r.replace(".", ""), a = r.length, o = a - o, i = js(n, new n(t), o, o * 2)), u = on(r, t, fe), c = u.length - 1, o = c; u[o] === 0; --o) u.pop();
  return o < 0 ? new n(e10.s * 0) : (e10.e = pn(u, c), e10.d = u, w = false, s && (e10 = _(e10, i, a * 4)), l && (e10 = e10.times(Math.abs(l) < 54 ? B(2, l) : or.pow(2, l))), w = true, e10);
}
function dp(e10, r) {
  var t, n = r.d.length;
  if (n < 3) return r.isZero() ? r : Pr(e10, 2, r, r);
  t = 1.4 * Math.sqrt(n), t = t > 16 ? 16 : t | 0, r = r.times(1 / dn(5, t)), r = Pr(e10, 2, r, r);
  for (var i, o = new e10(5), s = new e10(16), a = new e10(20); t--; ) i = r.times(r), r = r.times(o.plus(i.times(s.times(i).minus(a))));
  return r;
}
function Pr(e10, r, t, n, i) {
  var o, s, a, l, u = 1, c = e10.precision, p = Math.ceil(c / E);
  for (w = false, l = t.times(t), a = new e10(n); ; ) {
    if (s = _(a.times(l), new e10(r++ * r++), c, 1), a = i ? n.plus(s) : n.minus(s), n = _(s.times(l), new e10(r++ * r++), c, 1), s = a.plus(n), s.d[p] !== void 0) {
      for (o = p; s.d[o] === a.d[o] && o--; ) ;
      if (o == -1) break;
    }
    o = a, a = n, n = s, s = o, u++;
  }
  return w = true, s.d.length = p + 1, s;
}
function dn(e10, r) {
  for (var t = e10; --r; ) t *= e10;
  return t;
}
function Us(e10, r) {
  var t, n = r.s < 0, i = we(e10, e10.precision, 1), o = i.times(0.5);
  if (r = r.abs(), r.lte(o)) return Ne = n ? 4 : 1, r;
  if (t = r.divToInt(i), t.isZero()) Ne = n ? 3 : 2;
  else {
    if (r = r.minus(t.times(i)), r.lte(o)) return Ne = Ds(t) ? n ? 2 : 3 : n ? 4 : 1, r;
    Ne = Ds(t) ? n ? 1 : 4 : n ? 3 : 2;
  }
  return r.minus(i).abs();
}
function Fi(e10, r, t, n) {
  var i, o, s, a, l, u, c, p, d, f = e10.constructor, h = t !== void 0;
  if (h ? (ie(t, 1, Ke), n === void 0 ? n = f.rounding : ie(n, 0, 8)) : (t = f.precision, n = f.rounding), !e10.isFinite()) c = Bs(e10);
  else {
    for (c = xe(e10), s = c.indexOf("."), h ? (i = 2, r == 16 ? t = t * 4 - 3 : r == 8 && (t = t * 3 - 2)) : i = r, s >= 0 && (c = c.replace(".", ""), d = new f(1), d.e = c.length - s, d.d = on(xe(d), 10, i), d.e = d.d.length), p = on(c, 10, i), o = l = p.length; p[--l] == 0; ) p.pop();
    if (!p[0]) c = h ? "0p+0" : "0";
    else {
      if (s < 0 ? o-- : (e10 = new f(e10), e10.d = p, e10.e = o, e10 = _(e10, d, t, n, 0, i), p = e10.d, o = e10.e, u = Ns), s = p[t], a = i / 2, u = u || p[t + 1] !== void 0, u = n < 4 ? (s !== void 0 || u) && (n === 0 || n === (e10.s < 0 ? 3 : 2)) : s > a || s === a && (n === 4 || u || n === 6 && p[t - 1] & 1 || n === (e10.s < 0 ? 8 : 7)), p.length = t, u) for (; ++p[--t] > i - 1; ) p[t] = 0, t || (++o, p.unshift(1));
      for (l = p.length; !p[l - 1]; --l) ;
      for (s = 0, c = ""; s < l; s++) c += Oi.charAt(p[s]);
      if (h) {
        if (l > 1) if (r == 16 || r == 8) {
          for (s = r == 16 ? 4 : 3, --l; l % s; l++) c += "0";
          for (p = on(c, i, r), l = p.length; !p[l - 1]; --l) ;
          for (s = 1, c = "1."; s < l; s++) c += Oi.charAt(p[s]);
        } else c = c.charAt(0) + "." + c.slice(1);
        c = c + (o < 0 ? "p" : "p+") + o;
      } else if (o < 0) {
        for (; ++o; ) c = "0" + c;
        c = "0." + c;
      } else if (++o > l) for (o -= l; o--; ) c += "0";
      else o < l && (c = c.slice(0, o) + "." + c.slice(o));
    }
    c = (r == 16 ? "0x" : r == 2 ? "0b" : r == 8 ? "0o" : "") + c;
  }
  return e10.s < 0 ? "-" + c : c;
}
function Os(e10, r) {
  if (e10.length > r) return e10.length = r, true;
}
function mp(e10) {
  return new this(e10).abs();
}
function fp(e10) {
  return new this(e10).acos();
}
function gp(e10) {
  return new this(e10).acosh();
}
function hp(e10, r) {
  return new this(e10).plus(r);
}
function yp(e10) {
  return new this(e10).asin();
}
function bp(e10) {
  return new this(e10).asinh();
}
function Ep(e10) {
  return new this(e10).atan();
}
function wp(e10) {
  return new this(e10).atanh();
}
function xp(e10, r) {
  e10 = new this(e10), r = new this(r);
  var t, n = this.precision, i = this.rounding, o = n + 4;
  return !e10.s || !r.s ? t = new this(NaN) : !e10.d && !r.d ? (t = we(this, o, 1).times(r.s > 0 ? 0.25 : 0.75), t.s = e10.s) : !r.d || e10.isZero() ? (t = r.s < 0 ? we(this, n, i) : new this(0), t.s = e10.s) : !e10.d || r.isZero() ? (t = we(this, o, 1).times(0.5), t.s = e10.s) : r.s < 0 ? (this.precision = o, this.rounding = 1, t = this.atan(_(e10, r, o, 1)), r = we(this, o, 1), this.precision = n, this.rounding = i, t = e10.s < 0 ? t.minus(r) : t.plus(r)) : t = this.atan(_(e10, r, o, 1)), t;
}
function vp(e10) {
  return new this(e10).cbrt();
}
function Pp(e10) {
  return y(e10 = new this(e10), e10.e + 1, 2);
}
function Tp(e10, r, t) {
  return new this(e10).clamp(r, t);
}
function Sp(e10) {
  if (!e10 || typeof e10 != "object") throw Error(cn + "Object expected");
  var r, t, n, i = e10.defaults === true, o = ["precision", 1, Ke, "rounding", 0, 8, "toExpNeg", -vr, 0, "toExpPos", 0, vr, "maxE", 0, vr, "minE", -vr, 0, "modulo", 0, 9];
  for (r = 0; r < o.length; r += 3) if (t = o[r], i && (this[t] = _i[t]), (n = e10[t]) !== void 0) if (Y(n) === n && n >= o[r + 1] && n <= o[r + 2]) this[t] = n;
  else throw Error(He + t + ": " + n);
  if (t = "crypto", i && (this[t] = _i[t]), (n = e10[t]) !== void 0) if (n === true || n === false || n === 0 || n === 1) if (n) if (typeof crypto < "u" && crypto && (crypto.getRandomValues || crypto.randomBytes)) this[t] = true;
  else throw Error(Fs);
  else this[t] = false;
  else throw Error(He + t + ": " + n);
  return this;
}
function Rp(e10) {
  return new this(e10).cos();
}
function Cp(e10) {
  return new this(e10).cosh();
}
function Gs(e10) {
  var r, t, n;
  function i(o) {
    var s, a, l, u = this;
    if (!(u instanceof i)) return new i(o);
    if (u.constructor = i, _s(o)) {
      u.s = o.s, w ? !o.d || o.e > i.maxE ? (u.e = NaN, u.d = null) : o.e < i.minE ? (u.e = 0, u.d = [0]) : (u.e = o.e, u.d = o.d.slice()) : (u.e = o.e, u.d = o.d ? o.d.slice() : o.d);
      return;
    }
    if (l = typeof o, l === "number") {
      if (o === 0) {
        u.s = 1 / o < 0 ? -1 : 1, u.e = 0, u.d = [0];
        return;
      }
      if (o < 0 ? (o = -o, u.s = -1) : u.s = 1, o === ~~o && o < 1e7) {
        for (s = 0, a = o; a >= 10; a /= 10) s++;
        w ? s > i.maxE ? (u.e = NaN, u.d = null) : s < i.minE ? (u.e = 0, u.d = [0]) : (u.e = s, u.d = [o]) : (u.e = s, u.d = [o]);
        return;
      }
      if (o * 0 !== 0) {
        o || (u.s = NaN), u.e = NaN, u.d = null;
        return;
      }
      return sn(u, o.toString());
    }
    if (l === "string") return (a = o.charCodeAt(0)) === 45 ? (o = o.slice(1), u.s = -1) : (a === 43 && (o = o.slice(1)), u.s = 1), $s.test(o) ? sn(u, o) : pp(u, o);
    if (l === "bigint") return o < 0 ? (o = -o, u.s = -1) : u.s = 1, sn(u, o.toString());
    throw Error(He + o);
  }
  if (i.prototype = m, i.ROUND_UP = 0, i.ROUND_DOWN = 1, i.ROUND_CEIL = 2, i.ROUND_FLOOR = 3, i.ROUND_HALF_UP = 4, i.ROUND_HALF_DOWN = 5, i.ROUND_HALF_EVEN = 6, i.ROUND_HALF_CEIL = 7, i.ROUND_HALF_FLOOR = 8, i.EUCLID = 9, i.config = i.set = Sp, i.clone = Gs, i.isDecimal = _s, i.abs = mp, i.acos = fp, i.acosh = gp, i.add = hp, i.asin = yp, i.asinh = bp, i.atan = Ep, i.atanh = wp, i.atan2 = xp, i.cbrt = vp, i.ceil = Pp, i.clamp = Tp, i.cos = Rp, i.cosh = Cp, i.div = Ap, i.exp = Ip, i.floor = kp, i.hypot = Dp, i.ln = Op, i.log = _p, i.log10 = Lp, i.log2 = Np, i.max = Fp, i.min = Mp, i.mod = $p, i.mul = qp, i.pow = jp, i.random = Vp, i.round = Bp, i.sign = Up, i.sin = Gp, i.sinh = Qp, i.sqrt = Wp, i.sub = Jp, i.sum = Hp, i.tan = Kp, i.tanh = Yp, i.trunc = zp, e10 === void 0 && (e10 = {}), e10 && e10.defaults !== true) for (n = ["precision", "rounding", "toExpNeg", "toExpPos", "maxE", "minE", "modulo", "crypto"], r = 0; r < n.length; ) e10.hasOwnProperty(t = n[r++]) || (e10[t] = this[t]);
  return i.config(e10), i;
}
function Ap(e10, r) {
  return new this(e10).div(r);
}
function Ip(e10) {
  return new this(e10).exp();
}
function kp(e10) {
  return y(e10 = new this(e10), e10.e + 1, 3);
}
function Dp() {
  var e10, r, t = new this(0);
  for (w = false, e10 = 0; e10 < arguments.length; ) if (r = new this(arguments[e10++]), r.d) t.d && (t = t.plus(r.times(r)));
  else {
    if (r.s) return w = true, new this(1 / 0);
    t = r;
  }
  return w = true, t.sqrt();
}
function _s(e10) {
  return e10 instanceof or || e10 && e10.toStringTag === Ms || false;
}
function Op(e10) {
  return new this(e10).ln();
}
function _p(e10, r) {
  return new this(e10).log(r);
}
function Np(e10) {
  return new this(e10).log(2);
}
function Lp(e10) {
  return new this(e10).log(10);
}
function Fp() {
  return Vs(this, arguments, -1);
}
function Mp() {
  return Vs(this, arguments, 1);
}
function $p(e10, r) {
  return new this(e10).mod(r);
}
function qp(e10, r) {
  return new this(e10).mul(r);
}
function jp(e10, r) {
  return new this(e10).pow(r);
}
function Vp(e10) {
  var r, t, n, i, o = 0, s = new this(1), a = [];
  if (e10 === void 0 ? e10 = this.precision : ie(e10, 1, Ke), n = Math.ceil(e10 / E), this.crypto) if (crypto.getRandomValues) for (r = crypto.getRandomValues(new Uint32Array(n)); o < n; ) i = r[o], i >= 429e7 ? r[o] = crypto.getRandomValues(new Uint32Array(1))[0] : a[o++] = i % 1e7;
  else if (crypto.randomBytes) {
    for (r = crypto.randomBytes(n *= 4); o < n; ) i = r[o] + (r[o + 1] << 8) + (r[o + 2] << 16) + ((r[o + 3] & 127) << 24), i >= 214e7 ? crypto.randomBytes(4).copy(r, o) : (a.push(i % 1e7), o += 4);
    o = n / 4;
  } else throw Error(Fs);
  else for (; o < n; ) a[o++] = Math.random() * 1e7 | 0;
  for (n = a[--o], e10 %= E, n && e10 && (i = B(10, E - e10), a[o] = (n / i | 0) * i); a[o] === 0; o--) a.pop();
  if (o < 0) t = 0, a = [0];
  else {
    for (t = -1; a[0] === 0; t -= E) a.shift();
    for (n = 1, i = a[0]; i >= 10; i /= 10) n++;
    n < E && (t -= E - n);
  }
  return s.e = t, s.d = a, s;
}
function Bp(e10) {
  return y(e10 = new this(e10), e10.e + 1, this.rounding);
}
function Up(e10) {
  return e10 = new this(e10), e10.d ? e10.d[0] ? e10.s : 0 * e10.s : e10.s || NaN;
}
function Gp(e10) {
  return new this(e10).sin();
}
function Qp(e10) {
  return new this(e10).sinh();
}
function Wp(e10) {
  return new this(e10).sqrt();
}
function Jp(e10, r) {
  return new this(e10).sub(r);
}
function Hp() {
  var e10 = 0, r = arguments, t = new this(r[e10]);
  for (w = false; t.s && ++e10 < r.length; ) t = t.plus(r[e10]);
  return w = true, y(t, this.precision, this.rounding);
}
function Kp(e10) {
  return new this(e10).tan();
}
function Yp(e10) {
  return new this(e10).tanh();
}
function zp(e10) {
  return y(e10 = new this(e10), e10.e + 1, 1);
}
m[Symbol.for("nodejs.util.inspect.custom")] = m.toString;
m[Symbol.toStringTag] = "Decimal";
var or = m.constructor = Gs(_i);
an = new or(an);
ln = new or(ln);
var Le = or;
function ot(e10) {
  return e10 === null ? e10 : Array.isArray(e10) ? e10.map(ot) : typeof e10 == "object" ? Zp(e10) ? Xp(e10) : e10.constructor !== null && e10.constructor.name !== "Object" ? e10 : xr(e10, ot) : e10;
}
function Zp(e10) {
  return e10 !== null && typeof e10 == "object" && typeof e10.$type == "string";
}
function Xp({ $type: e10, value: r }) {
  switch (e10) {
    case "BigInt":
      return BigInt(r);
    case "Bytes": {
      let { buffer: t, byteOffset: n, byteLength: i } = Buffer.from(r, "base64");
      return new Uint8Array(t, n, i);
    }
    case "DateTime":
      return new Date(r);
    case "Decimal":
      return new Le(r);
    case "Json":
      return JSON.parse(r);
    default:
      Oe(r, "Unknown tagged value");
  }
}
var ve = class {
  _map = /* @__PURE__ */ new Map();
  get(r) {
    return this._map.get(r)?.value;
  }
  set(r, t) {
    this._map.set(r, { value: t });
  }
  getOrCreate(r, t) {
    let n = this._map.get(r);
    if (n) return n.value;
    let i = t();
    return this.set(r, i), i;
  }
};
function Ye(e10) {
  return e10.substring(0, 1).toLowerCase() + e10.substring(1);
}
function Qs(e10, r) {
  let t = {};
  for (let n of e10) {
    let i = n[r];
    t[i] = n;
  }
  return t;
}
function st(e10) {
  let r;
  return { get() {
    return r || (r = { value: e10() }), r.value;
  } };
}
function ed(e10) {
  return { models: Mi(e10.models), enums: Mi(e10.enums), types: Mi(e10.types) };
}
function Mi(e10) {
  let r = {};
  for (let { name: t, ...n } of e10) r[t] = n;
  return r;
}
function Tr(e10) {
  return e10 instanceof Date || Object.prototype.toString.call(e10) === "[object Date]";
}
function mn(e10) {
  return e10.toString() !== "Invalid Date";
}
function Sr(e10) {
  return or.isDecimal(e10) ? true : e10 !== null && typeof e10 == "object" && typeof e10.s == "number" && typeof e10.e == "number" && typeof e10.toFixed == "function" && Array.isArray(e10.d);
}
var fn = {};
gr(fn, { ModelAction: () => Rr, datamodelEnumToSchemaEnum: () => rd });
function rd(e10) {
  return { name: e10.name, values: e10.values.map((r) => r.name) };
}
var Rr = ((b) => (b.findUnique = "findUnique", b.findUniqueOrThrow = "findUniqueOrThrow", b.findFirst = "findFirst", b.findFirstOrThrow = "findFirstOrThrow", b.findMany = "findMany", b.create = "create", b.createMany = "createMany", b.createManyAndReturn = "createManyAndReturn", b.update = "update", b.updateMany = "updateMany", b.updateManyAndReturn = "updateManyAndReturn", b.upsert = "upsert", b.delete = "delete", b.deleteMany = "deleteMany", b.groupBy = "groupBy", b.count = "count", b.aggregate = "aggregate", b.findRaw = "findRaw", b.aggregateRaw = "aggregateRaw", b))(Rr || {});
var Ks = ne(Ei());
var Ws = { keyword: ke, entity: ke, value: (e10) => Q(tr(e10)), punctuation: tr, directive: ke, function: ke, variable: (e10) => Q(tr(e10)), string: (e10) => Q(qe(e10)), boolean: Ie, number: ke, comment: Wr };
var td = (e10) => e10;
var gn = {};
var nd = 0;
var v = { manual: gn.Prism && gn.Prism.manual, disableWorkerMessageHandler: gn.Prism && gn.Prism.disableWorkerMessageHandler, util: { encode: function(e10) {
  if (e10 instanceof ge) {
    let r = e10;
    return new ge(r.type, v.util.encode(r.content), r.alias);
  } else return Array.isArray(e10) ? e10.map(v.util.encode) : e10.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/\u00a0/g, " ");
}, type: function(e10) {
  return Object.prototype.toString.call(e10).slice(8, -1);
}, objId: function(e10) {
  return e10.__id || Object.defineProperty(e10, "__id", { value: ++nd }), e10.__id;
}, clone: function e3(r, t) {
  let n, i, o = v.util.type(r);
  switch (t = t || {}, o) {
    case "Object":
      if (i = v.util.objId(r), t[i]) return t[i];
      n = {}, t[i] = n;
      for (let s in r) r.hasOwnProperty(s) && (n[s] = e3(r[s], t));
      return n;
    case "Array":
      return i = v.util.objId(r), t[i] ? t[i] : (n = [], t[i] = n, r.forEach(function(s, a) {
        n[a] = e3(s, t);
      }), n);
    default:
      return r;
  }
} }, languages: { extend: function(e10, r) {
  let t = v.util.clone(v.languages[e10]);
  for (let n in r) t[n] = r[n];
  return t;
}, insertBefore: function(e10, r, t, n) {
  n = n || v.languages;
  let i = n[e10], o = {};
  for (let a in i) if (i.hasOwnProperty(a)) {
    if (a == r) for (let l in t) t.hasOwnProperty(l) && (o[l] = t[l]);
    t.hasOwnProperty(a) || (o[a] = i[a]);
  }
  let s = n[e10];
  return n[e10] = o, v.languages.DFS(v.languages, function(a, l) {
    l === s && a != e10 && (this[a] = o);
  }), o;
}, DFS: function e4(r, t, n, i) {
  i = i || {};
  let o = v.util.objId;
  for (let s in r) if (r.hasOwnProperty(s)) {
    t.call(r, s, r[s], n || s);
    let a = r[s], l = v.util.type(a);
    l === "Object" && !i[o(a)] ? (i[o(a)] = true, e4(a, t, null, i)) : l === "Array" && !i[o(a)] && (i[o(a)] = true, e4(a, t, s, i));
  }
} }, plugins: {}, highlight: function(e10, r, t) {
  let n = { code: e10, grammar: r, language: t };
  return v.hooks.run("before-tokenize", n), n.tokens = v.tokenize(n.code, n.grammar), v.hooks.run("after-tokenize", n), ge.stringify(v.util.encode(n.tokens), n.language);
}, matchGrammar: function(e10, r, t, n, i, o, s) {
  for (let g in t) {
    if (!t.hasOwnProperty(g) || !t[g]) continue;
    if (g == s) return;
    let S = t[g];
    S = v.util.type(S) === "Array" ? S : [S];
    for (let P = 0; P < S.length; ++P) {
      let R = S[P], b = R.inside, k = !!R.lookbehind, me = !!R.greedy, se = 0, Qr = R.alias;
      if (me && !R.pattern.global) {
        let j = R.pattern.toString().match(/[imuy]*$/)[0];
        R.pattern = RegExp(R.pattern.source, j + "g");
      }
      R = R.pattern || R;
      for (let j = n, re = i; j < r.length; re += r[j].length, ++j) {
        let Ce = r[j];
        if (r.length > e10.length) return;
        if (Ce instanceof ge) continue;
        if (me && j != r.length - 1) {
          R.lastIndex = re;
          var p = R.exec(e10);
          if (!p) break;
          var c = p.index + (k ? p[1].length : 0), d = p.index + p[0].length, a = j, l = re;
          for (let O = r.length; a < O && (l < d || !r[a].type && !r[a - 1].greedy); ++a) l += r[a].length, c >= l && (++j, re = l);
          if (r[j] instanceof ge) continue;
          u = a - j, Ce = e10.slice(re, l), p.index -= re;
        } else {
          R.lastIndex = 0;
          var p = R.exec(Ce), u = 1;
        }
        if (!p) {
          if (o) break;
          continue;
        }
        k && (se = p[1] ? p[1].length : 0);
        var c = p.index + se, p = p[0].slice(se), d = c + p.length, f = Ce.slice(0, c), h = Ce.slice(d);
        let J = [j, u];
        f && (++j, re += f.length, J.push(f));
        let mr = new ge(g, b ? v.tokenize(p, b) : p, Qr, p, me);
        if (J.push(mr), h && J.push(h), Array.prototype.splice.apply(r, J), u != 1 && v.matchGrammar(e10, r, t, j, re, true, g), o) break;
      }
    }
  }
}, tokenize: function(e10, r) {
  let t = [e10], n = r.rest;
  if (n) {
    for (let i in n) r[i] = n[i];
    delete r.rest;
  }
  return v.matchGrammar(e10, t, r, 0, 0, false), t;
}, hooks: { all: {}, add: function(e10, r) {
  let t = v.hooks.all;
  t[e10] = t[e10] || [], t[e10].push(r);
}, run: function(e10, r) {
  let t = v.hooks.all[e10];
  if (!(!t || !t.length)) for (var n = 0, i; i = t[n++]; ) i(r);
} }, Token: ge };
v.languages.clike = { comment: [{ pattern: /(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/, lookbehind: true }, { pattern: /(^|[^\\:])\/\/.*/, lookbehind: true, greedy: true }], string: { pattern: /(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/, greedy: true }, "class-name": { pattern: /((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i, lookbehind: true, inside: { punctuation: /[.\\]/ } }, keyword: /\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/, boolean: /\b(?:true|false)\b/, function: /\w+(?=\()/, number: /\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i, operator: /--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/, punctuation: /[{}[\];(),.:]/ };
v.languages.javascript = v.languages.extend("clike", { "class-name": [v.languages.clike["class-name"], { pattern: /(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/, lookbehind: true }], keyword: [{ pattern: /((?:^|})\s*)(?:catch|finally)\b/, lookbehind: true }, { pattern: /(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/, lookbehind: true }], number: /\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/, function: /[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/, operator: /-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/ });
v.languages.javascript["class-name"][0].pattern = /(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;
v.languages.insertBefore("javascript", "keyword", { regex: { pattern: /((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/, lookbehind: true, greedy: true }, "function-variable": { pattern: /[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/, alias: "function" }, parameter: [{ pattern: /(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/, lookbehind: true, inside: v.languages.javascript }, { pattern: /[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i, inside: v.languages.javascript }, { pattern: /(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/, lookbehind: true, inside: v.languages.javascript }, { pattern: /((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/, lookbehind: true, inside: v.languages.javascript }], constant: /\b[A-Z](?:[A-Z_]|\dx?)*\b/ });
v.languages.markup && v.languages.markup.tag.addInlined("script", "javascript");
v.languages.js = v.languages.javascript;
v.languages.typescript = v.languages.extend("javascript", { keyword: /\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/, builtin: /\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/ });
v.languages.ts = v.languages.typescript;
function ge(e10, r, t, n, i) {
  this.type = e10, this.content = r, this.alias = t, this.length = (n || "").length | 0, this.greedy = !!i;
}
ge.stringify = function(e10, r) {
  return typeof e10 == "string" ? e10 : Array.isArray(e10) ? e10.map(function(t) {
    return ge.stringify(t, r);
  }).join("") : id(e10.type)(e10.content);
};
function id(e10) {
  return Ws[e10] || td;
}
function Js(e10) {
  return od(e10, v.languages.javascript);
}
function od(e10, r) {
  return v.tokenize(e10, r).map((n) => ge.stringify(n)).join("");
}
function Hs(e10) {
  return yi(e10);
}
var hn = class e5 {
  firstLineNumber;
  lines;
  static read(r) {
    let t;
    try {
      t = import_node_fs4.default.readFileSync(r, "utf-8");
    } catch {
      return null;
    }
    return e5.fromContent(t);
  }
  static fromContent(r) {
    let t = r.split(/\r?\n/);
    return new e5(1, t);
  }
  constructor(r, t) {
    this.firstLineNumber = r, this.lines = t;
  }
  get lastLineNumber() {
    return this.firstLineNumber + this.lines.length - 1;
  }
  mapLineAt(r, t) {
    if (r < this.firstLineNumber || r > this.lines.length + this.firstLineNumber) return this;
    let n = r - this.firstLineNumber, i = [...this.lines];
    return i[n] = t(i[n]), new e5(this.firstLineNumber, i);
  }
  mapLines(r) {
    return new e5(this.firstLineNumber, this.lines.map((t, n) => r(t, this.firstLineNumber + n)));
  }
  lineAt(r) {
    return this.lines[r - this.firstLineNumber];
  }
  prependSymbolAt(r, t) {
    return this.mapLines((n, i) => i === r ? `${t} ${n}` : `  ${n}`);
  }
  slice(r, t) {
    let n = this.lines.slice(r - 1, t).join(`
`);
    return new e5(r, Hs(n).split(`
`));
  }
  highlight() {
    let r = Js(this.toString());
    return new e5(this.firstLineNumber, r.split(`
`));
  }
  toString() {
    return this.lines.join(`
`);
  }
};
var ad = { red: ue, gray: Wr, dim: Ae, bold: Q, underline: K, highlightSource: (e10) => e10.highlight() };
var ld = { red: (e10) => e10, gray: (e10) => e10, dim: (e10) => e10, bold: (e10) => e10, underline: (e10) => e10, highlightSource: (e10) => e10 };
function ud({ message: e10, originalMethod: r, isPanic: t, callArguments: n }) {
  return { functionName: `prisma.${r}()`, message: e10, isPanic: t ?? false, callArguments: n };
}
function cd({ callsite: e10, message: r, originalMethod: t, isPanic: n, callArguments: i }, o) {
  let s = ud({ message: r, originalMethod: t, isPanic: n, callArguments: i });
  if (!e10 || typeof window < "u" || process.env.NODE_ENV === "production") return s;
  let a = e10.getLocation();
  if (!a || !a.lineNumber || !a.columnNumber) return s;
  let l = Math.max(1, a.lineNumber - 3), u = hn.read(a.fileName)?.slice(l, a.lineNumber), c = u?.lineAt(a.lineNumber);
  if (u && c) {
    let p = dd(c), d = pd(c);
    if (!d) return s;
    s.functionName = `${d.code})`, s.location = a, n || (u = u.mapLineAt(a.lineNumber, (h) => h.slice(0, d.openingBraceIndex))), u = o.highlightSource(u);
    let f = String(u.lastLineNumber).length;
    if (s.contextLines = u.mapLines((h, g) => o.gray(String(g).padStart(f)) + " " + h).mapLines((h) => o.dim(h)).prependSymbolAt(a.lineNumber, o.bold(o.red("→"))), i) {
      let h = p + f + 1;
      h += 2, s.callArguments = (0, Ks.default)(i, h).slice(h);
    }
  }
  return s;
}
function pd(e10) {
  let r = Object.keys(Rr).join("|"), n = new RegExp(String.raw`\.(${r})\(`).exec(e10);
  if (n) {
    let i = n.index + n[0].length, o = e10.lastIndexOf(" ", n.index) + 1;
    return { code: e10.slice(o, i), openingBraceIndex: i };
  }
  return null;
}
function dd(e10) {
  let r = 0;
  for (let t = 0; t < e10.length; t++) {
    if (e10.charAt(t) !== " ") return r;
    r++;
  }
  return r;
}
function md({ functionName: e10, location: r, message: t, isPanic: n, contextLines: i, callArguments: o }, s) {
  let a = [""], l = r ? " in" : ":";
  if (n ? (a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)), a.push(s.red(`It occurred in the ${s.bold(`\`${e10}\``)} invocation${l}`))) : a.push(s.red(`Invalid ${s.bold(`\`${e10}\``)} invocation${l}`)), r && a.push(s.underline(fd(r))), i) {
    a.push("");
    let u = [i.toString()];
    o && (u.push(o), u.push(s.dim(")"))), a.push(u.join("")), o && a.push("");
  } else a.push(""), o && a.push(o), a.push("");
  return a.push(t), a.join(`
`);
}
function fd(e10) {
  let r = [e10.fileName];
  return e10.lineNumber && r.push(String(e10.lineNumber)), e10.columnNumber && r.push(String(e10.columnNumber)), r.join(":");
}
function yn(e10) {
  let r = e10.showColors ? ad : ld, t;
  return t = cd(e10, r), md(t, r);
}
var ia = ne($i());
function Xs(e10, r, t) {
  let n = ea(e10), i = gd(n), o = yd(i);
  o ? bn(o, r, t) : r.addErrorMessage(() => "Unknown error");
}
function ea(e10) {
  return e10.errors.flatMap((r) => r.kind === "Union" ? ea(r) : [r]);
}
function gd(e10) {
  let r = /* @__PURE__ */ new Map(), t = [];
  for (let n of e10) {
    if (n.kind !== "InvalidArgumentType") {
      t.push(n);
      continue;
    }
    let i = `${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`, o = r.get(i);
    o ? r.set(i, { ...n, argument: { ...n.argument, typeNames: hd(o.argument.typeNames, n.argument.typeNames) } }) : r.set(i, n);
  }
  return t.push(...r.values()), t;
}
function hd(e10, r) {
  return [...new Set(e10.concat(r))];
}
function yd(e10) {
  return Di(e10, (r, t) => {
    let n = zs(r), i = zs(t);
    return n !== i ? n - i : Zs(r) - Zs(t);
  });
}
function zs(e10) {
  let r = 0;
  return Array.isArray(e10.selectionPath) && (r += e10.selectionPath.length), Array.isArray(e10.argumentPath) && (r += e10.argumentPath.length), r;
}
function Zs(e10) {
  switch (e10.kind) {
    case "InvalidArgumentValue":
    case "ValueTooLarge":
      return 20;
    case "InvalidArgumentType":
      return 10;
    case "RequiredArgumentMissing":
      return -10;
    default:
      return 0;
  }
}
var ae = class {
  constructor(r, t) {
    this.name = r;
    this.value = t;
  }
  isRequired = false;
  makeRequired() {
    return this.isRequired = true, this;
  }
  write(r) {
    let { colors: { green: t } } = r.context;
    r.addMarginSymbol(t(this.isRequired ? "+" : "?")), r.write(t(this.name)), this.isRequired || r.write(t("?")), r.write(t(": ")), typeof this.value == "string" ? r.write(t(this.value)) : r.write(this.value);
  }
};
ta();
var Cr = class {
  constructor(r = 0, t) {
    this.context = t;
    this.currentIndent = r;
  }
  lines = [];
  currentLine = "";
  currentIndent = 0;
  marginSymbol;
  afterNextNewLineCallback;
  write(r) {
    return typeof r == "string" ? this.currentLine += r : r.write(this), this;
  }
  writeJoined(r, t, n = (i, o) => o.write(i)) {
    let i = t.length - 1;
    for (let o = 0; o < t.length; o++) n(t[o], this), o !== i && this.write(r);
    return this;
  }
  writeLine(r) {
    return this.write(r).newLine();
  }
  newLine() {
    this.lines.push(this.indentedCurrentLine()), this.currentLine = "", this.marginSymbol = void 0;
    let r = this.afterNextNewLineCallback;
    return this.afterNextNewLineCallback = void 0, r?.(), this;
  }
  withIndent(r) {
    return this.indent(), r(this), this.unindent(), this;
  }
  afterNextNewline(r) {
    return this.afterNextNewLineCallback = r, this;
  }
  indent() {
    return this.currentIndent++, this;
  }
  unindent() {
    return this.currentIndent > 0 && this.currentIndent--, this;
  }
  addMarginSymbol(r) {
    return this.marginSymbol = r, this;
  }
  toString() {
    return this.lines.concat(this.indentedCurrentLine()).join(`
`);
  }
  getCurrentLineLength() {
    return this.currentLine.length;
  }
  indentedCurrentLine() {
    let r = this.currentLine.padStart(this.currentLine.length + 2 * this.currentIndent);
    return this.marginSymbol ? this.marginSymbol + r.slice(1) : r;
  }
};
ra();
var En = class {
  constructor(r) {
    this.value = r;
  }
  write(r) {
    r.write(this.value);
  }
  markAsError() {
    this.value.markAsError();
  }
};
var wn = (e10) => e10;
var xn = { bold: wn, red: wn, green: wn, dim: wn, enabled: false };
var na = { bold: Q, red: ue, green: qe, dim: Ae, enabled: true };
var Ar = { write(e10) {
  e10.writeLine(",");
} };
var Pe = class {
  constructor(r) {
    this.contents = r;
  }
  isUnderlined = false;
  color = (r) => r;
  underline() {
    return this.isUnderlined = true, this;
  }
  setColor(r) {
    return this.color = r, this;
  }
  write(r) {
    let t = r.getCurrentLineLength();
    r.write(this.color(this.contents)), this.isUnderlined && r.afterNextNewline(() => {
      r.write(" ".repeat(t)).writeLine(this.color("~".repeat(this.contents.length)));
    });
  }
};
var ze = class {
  hasError = false;
  markAsError() {
    return this.hasError = true, this;
  }
};
var Ir = class extends ze {
  items = [];
  addItem(r) {
    return this.items.push(new En(r)), this;
  }
  getField(r) {
    return this.items[r];
  }
  getPrintWidth() {
    return this.items.length === 0 ? 2 : Math.max(...this.items.map((t) => t.value.getPrintWidth())) + 2;
  }
  write(r) {
    if (this.items.length === 0) {
      this.writeEmpty(r);
      return;
    }
    this.writeWithItems(r);
  }
  writeEmpty(r) {
    let t = new Pe("[]");
    this.hasError && t.setColor(r.context.colors.red).underline(), r.write(t);
  }
  writeWithItems(r) {
    let { colors: t } = r.context;
    r.writeLine("[").withIndent(() => r.writeJoined(Ar, this.items).newLine()).write("]"), this.hasError && r.afterNextNewline(() => {
      r.writeLine(t.red("~".repeat(this.getPrintWidth())));
    });
  }
  asObject() {
  }
};
var kr = class e6 extends ze {
  fields = {};
  suggestions = [];
  addField(r) {
    this.fields[r.name] = r;
  }
  addSuggestion(r) {
    this.suggestions.push(r);
  }
  getField(r) {
    return this.fields[r];
  }
  getDeepField(r) {
    let [t, ...n] = r, i = this.getField(t);
    if (!i) return;
    let o = i;
    for (let s of n) {
      let a;
      if (o.value instanceof e6 ? a = o.value.getField(s) : o.value instanceof Ir && (a = o.value.getField(Number(s))), !a) return;
      o = a;
    }
    return o;
  }
  getDeepFieldValue(r) {
    return r.length === 0 ? this : this.getDeepField(r)?.value;
  }
  hasField(r) {
    return !!this.getField(r);
  }
  removeAllFields() {
    this.fields = {};
  }
  removeField(r) {
    delete this.fields[r];
  }
  getFields() {
    return this.fields;
  }
  isEmpty() {
    return Object.keys(this.fields).length === 0;
  }
  getFieldValue(r) {
    return this.getField(r)?.value;
  }
  getDeepSubSelectionValue(r) {
    let t = this;
    for (let n of r) {
      if (!(t instanceof e6)) return;
      let i = t.getSubSelectionValue(n);
      if (!i) return;
      t = i;
    }
    return t;
  }
  getDeepSelectionParent(r) {
    let t = this.getSelectionParent();
    if (!t) return;
    let n = t;
    for (let i of r) {
      let o = n.value.getFieldValue(i);
      if (!o || !(o instanceof e6)) return;
      let s = o.getSelectionParent();
      if (!s) return;
      n = s;
    }
    return n;
  }
  getSelectionParent() {
    let r = this.getField("select")?.value.asObject();
    if (r) return { kind: "select", value: r };
    let t = this.getField("include")?.value.asObject();
    if (t) return { kind: "include", value: t };
  }
  getSubSelectionValue(r) {
    return this.getSelectionParent()?.value.fields[r].value;
  }
  getPrintWidth() {
    let r = Object.values(this.fields);
    return r.length == 0 ? 2 : Math.max(...r.map((n) => n.getPrintWidth())) + 2;
  }
  write(r) {
    let t = Object.values(this.fields);
    if (t.length === 0 && this.suggestions.length === 0) {
      this.writeEmpty(r);
      return;
    }
    this.writeWithContents(r, t);
  }
  asObject() {
    return this;
  }
  writeEmpty(r) {
    let t = new Pe("{}");
    this.hasError && t.setColor(r.context.colors.red).underline(), r.write(t);
  }
  writeWithContents(r, t) {
    r.writeLine("{").withIndent(() => {
      r.writeJoined(Ar, [...t, ...this.suggestions]).newLine();
    }), r.write("}"), this.hasError && r.afterNextNewline(() => {
      r.writeLine(r.context.colors.red("~".repeat(this.getPrintWidth())));
    });
  }
};
var G = class extends ze {
  constructor(t) {
    super();
    this.text = t;
  }
  getPrintWidth() {
    return this.text.length;
  }
  write(t) {
    let n = new Pe(this.text);
    this.hasError && n.underline().setColor(t.context.colors.red), t.write(n);
  }
  asObject() {
  }
};
var at = class {
  fields = [];
  addField(r, t) {
    return this.fields.push({ write(n) {
      let { green: i, dim: o } = n.context.colors;
      n.write(i(o(`${r}: ${t}`))).addMarginSymbol(i(o("+")));
    } }), this;
  }
  write(r) {
    let { colors: { green: t } } = r.context;
    r.writeLine(t("{")).withIndent(() => {
      r.writeJoined(Ar, this.fields).newLine();
    }).write(t("}")).addMarginSymbol(t("+"));
  }
};
function bn(e10, r, t) {
  switch (e10.kind) {
    case "MutuallyExclusiveFields":
      bd(e10, r);
      break;
    case "IncludeOnScalar":
      Ed(e10, r);
      break;
    case "EmptySelection":
      wd(e10, r, t);
      break;
    case "UnknownSelectionField":
      Td(e10, r);
      break;
    case "InvalidSelectionValue":
      Sd(e10, r);
      break;
    case "UnknownArgument":
      Rd(e10, r);
      break;
    case "UnknownInputField":
      Cd(e10, r);
      break;
    case "RequiredArgumentMissing":
      Ad(e10, r);
      break;
    case "InvalidArgumentType":
      Id(e10, r);
      break;
    case "InvalidArgumentValue":
      kd(e10, r);
      break;
    case "ValueTooLarge":
      Dd(e10, r);
      break;
    case "SomeFieldsMissing":
      Od(e10, r);
      break;
    case "TooManyFieldsGiven":
      _d(e10, r);
      break;
    case "Union":
      Xs(e10, r, t);
      break;
    default:
      throw new Error("not implemented: " + e10.kind);
  }
}
function bd(e10, r) {
  let t = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  t && (t.getField(e10.firstField)?.markAsError(), t.getField(e10.secondField)?.markAsError()), r.addErrorMessage((n) => `Please ${n.bold("either")} use ${n.green(`\`${e10.firstField}\``)} or ${n.green(`\`${e10.secondField}\``)}, but ${n.red("not both")} at the same time.`);
}
function Ed(e10, r) {
  let [t, n] = lt(e10.selectionPath), i = e10.outputType, o = r.arguments.getDeepSelectionParent(t)?.value;
  if (o && (o.getField(n)?.markAsError(), i)) for (let s of i.fields) s.isRelation && o.addSuggestion(new ae(s.name, "true"));
  r.addErrorMessage((s) => {
    let a = `Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;
    return i ? a += ` on model ${s.bold(i.name)}. ${ut(s)}` : a += ".", a += `
Note that ${s.bold("include")} statements only accept relation fields.`, a;
  });
}
function wd(e10, r, t) {
  let n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  if (n) {
    let i = n.getField("omit")?.value.asObject();
    if (i) {
      xd(e10, r, i);
      return;
    }
    if (n.hasField("select")) {
      vd(e10, r);
      return;
    }
  }
  if (t?.[Ye(e10.outputType.name)]) {
    Pd(e10, r);
    return;
  }
  r.addErrorMessage(() => `Unknown field at "${e10.selectionPath.join(".")} selection"`);
}
function xd(e10, r, t) {
  t.removeAllFields();
  for (let n of e10.outputType.fields) t.addSuggestion(new ae(n.name, "false"));
  r.addErrorMessage((n) => `The ${n.red("omit")} statement includes every field of the model ${n.bold(e10.outputType.name)}. At least one field must be included in the result`);
}
function vd(e10, r) {
  let t = e10.outputType, n = r.arguments.getDeepSelectionParent(e10.selectionPath)?.value, i = n?.isEmpty() ?? false;
  n && (n.removeAllFields(), aa(n, t)), r.addErrorMessage((o) => i ? `The ${o.red("`select`")} statement for type ${o.bold(t.name)} must not be empty. ${ut(o)}` : `The ${o.red("`select`")} statement for type ${o.bold(t.name)} needs ${o.bold("at least one truthy value")}.`);
}
function Pd(e10, r) {
  let t = new at();
  for (let i of e10.outputType.fields) i.isRelation || t.addField(i.name, "false");
  let n = new ae("omit", t).makeRequired();
  if (e10.selectionPath.length === 0) r.arguments.addSuggestion(n);
  else {
    let [i, o] = lt(e10.selectionPath), a = r.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);
    if (a) {
      let l = a?.value.asObject() ?? new kr();
      l.addSuggestion(n), a.value = l;
    }
  }
  r.addErrorMessage((i) => `The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e10.outputType.name)}. At least one field must be included in the result`);
}
function Td(e10, r) {
  let t = la(e10.selectionPath, r);
  if (t.parentKind !== "unknown") {
    t.field.markAsError();
    let n = t.parent;
    switch (t.parentKind) {
      case "select":
        aa(n, e10.outputType);
        break;
      case "include":
        Nd(n, e10.outputType);
        break;
      case "omit":
        Ld(n, e10.outputType);
        break;
    }
  }
  r.addErrorMessage((n) => {
    let i = [`Unknown field ${n.red(`\`${t.fieldName}\``)}`];
    return t.parentKind !== "unknown" && i.push(`for ${n.bold(t.parentKind)} statement`), i.push(`on model ${n.bold(`\`${e10.outputType.name}\``)}.`), i.push(ut(n)), i.join(" ");
  });
}
function Sd(e10, r) {
  let t = la(e10.selectionPath, r);
  t.parentKind !== "unknown" && t.field.value.markAsError(), r.addErrorMessage((n) => `Invalid value for selection field \`${n.red(t.fieldName)}\`: ${e10.underlyingError}`);
}
function Rd(e10, r) {
  let t = e10.argumentPath[0], n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  n && (n.getField(t)?.markAsError(), Fd(n, e10.arguments)), r.addErrorMessage((i) => oa(i, t, e10.arguments.map((o) => o.name)));
}
function Cd(e10, r) {
  let [t, n] = lt(e10.argumentPath), i = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  if (i) {
    i.getDeepField(e10.argumentPath)?.markAsError();
    let o = i.getDeepFieldValue(t)?.asObject();
    o && ua(o, e10.inputType);
  }
  r.addErrorMessage((o) => oa(o, n, e10.inputType.fields.map((s) => s.name)));
}
function oa(e10, r, t) {
  let n = [`Unknown argument \`${e10.red(r)}\`.`], i = $d(r, t);
  return i && n.push(`Did you mean \`${e10.green(i)}\`?`), t.length > 0 && n.push(ut(e10)), n.join(" ");
}
function Ad(e10, r) {
  let t;
  r.addErrorMessage((l) => t?.value instanceof G && t.value.text === "null" ? `Argument \`${l.green(o)}\` must not be ${l.red("null")}.` : `Argument \`${l.green(o)}\` is missing.`);
  let n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  if (!n) return;
  let [i, o] = lt(e10.argumentPath), s = new at(), a = n.getDeepFieldValue(i)?.asObject();
  if (a) if (t = a.getField(o), t && a.removeField(o), e10.inputTypes.length === 1 && e10.inputTypes[0].kind === "object") {
    for (let l of e10.inputTypes[0].fields) s.addField(l.name, l.typeNames.join(" | "));
    a.addSuggestion(new ae(o, s).makeRequired());
  } else {
    let l = e10.inputTypes.map(sa).join(" | ");
    a.addSuggestion(new ae(o, l).makeRequired());
  }
}
function sa(e10) {
  return e10.kind === "list" ? `${sa(e10.elementType)}[]` : e10.name;
}
function Id(e10, r) {
  let t = e10.argument.name, n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  n && n.getDeepFieldValue(e10.argumentPath)?.markAsError(), r.addErrorMessage((i) => {
    let o = vn("or", e10.argument.typeNames.map((s) => i.green(s)));
    return `Argument \`${i.bold(t)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e10.inferredType)}.`;
  });
}
function kd(e10, r) {
  let t = e10.argument.name, n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  n && n.getDeepFieldValue(e10.argumentPath)?.markAsError(), r.addErrorMessage((i) => {
    let o = [`Invalid value for argument \`${i.bold(t)}\``];
    if (e10.underlyingError && o.push(`: ${e10.underlyingError}`), o.push("."), e10.argument.typeNames.length > 0) {
      let s = vn("or", e10.argument.typeNames.map((a) => i.green(a)));
      o.push(` Expected ${s}.`);
    }
    return o.join("");
  });
}
function Dd(e10, r) {
  let t = e10.argument.name, n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject(), i;
  if (n) {
    let s = n.getDeepField(e10.argumentPath)?.value;
    s?.markAsError(), s instanceof G && (i = s.text);
  }
  r.addErrorMessage((o) => {
    let s = ["Unable to fit value"];
    return i && s.push(o.red(i)), s.push(`into a 64-bit signed integer for field \`${o.bold(t)}\``), s.join(" ");
  });
}
function Od(e10, r) {
  let t = e10.argumentPath[e10.argumentPath.length - 1], n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject();
  if (n) {
    let i = n.getDeepFieldValue(e10.argumentPath)?.asObject();
    i && ua(i, e10.inputType);
  }
  r.addErrorMessage((i) => {
    let o = [`Argument \`${i.bold(t)}\` of type ${i.bold(e10.inputType.name)} needs`];
    return e10.constraints.minFieldCount === 1 ? e10.constraints.requiredFields ? o.push(`${i.green("at least one of")} ${vn("or", e10.constraints.requiredFields.map((s) => `\`${i.bold(s)}\``))} arguments.`) : o.push(`${i.green("at least one")} argument.`) : o.push(`${i.green(`at least ${e10.constraints.minFieldCount}`)} arguments.`), o.push(ut(i)), o.join(" ");
  });
}
function _d(e10, r) {
  let t = e10.argumentPath[e10.argumentPath.length - 1], n = r.arguments.getDeepSubSelectionValue(e10.selectionPath)?.asObject(), i = [];
  if (n) {
    let o = n.getDeepFieldValue(e10.argumentPath)?.asObject();
    o && (o.markAsError(), i = Object.keys(o.getFields()));
  }
  r.addErrorMessage((o) => {
    let s = [`Argument \`${o.bold(t)}\` of type ${o.bold(e10.inputType.name)} needs`];
    return e10.constraints.minFieldCount === 1 && e10.constraints.maxFieldCount == 1 ? s.push(`${o.green("exactly one")} argument,`) : e10.constraints.maxFieldCount == 1 ? s.push(`${o.green("at most one")} argument,`) : s.push(`${o.green(`at most ${e10.constraints.maxFieldCount}`)} arguments,`), s.push(`but you provided ${vn("and", i.map((a) => o.red(a)))}. Please choose`), e10.constraints.maxFieldCount === 1 ? s.push("one.") : s.push(`${e10.constraints.maxFieldCount}.`), s.join(" ");
  });
}
function aa(e10, r) {
  for (let t of r.fields) e10.hasField(t.name) || e10.addSuggestion(new ae(t.name, "true"));
}
function Nd(e10, r) {
  for (let t of r.fields) t.isRelation && !e10.hasField(t.name) && e10.addSuggestion(new ae(t.name, "true"));
}
function Ld(e10, r) {
  for (let t of r.fields) !e10.hasField(t.name) && !t.isRelation && e10.addSuggestion(new ae(t.name, "true"));
}
function Fd(e10, r) {
  for (let t of r) e10.hasField(t.name) || e10.addSuggestion(new ae(t.name, t.typeNames.join(" | ")));
}
function la(e10, r) {
  let [t, n] = lt(e10), i = r.arguments.getDeepSubSelectionValue(t)?.asObject();
  if (!i) return { parentKind: "unknown", fieldName: n };
  let o = i.getFieldValue("select")?.asObject(), s = i.getFieldValue("include")?.asObject(), a = i.getFieldValue("omit")?.asObject(), l = o?.getField(n);
  return o && l ? { parentKind: "select", parent: o, field: l, fieldName: n } : (l = s?.getField(n), s && l ? { parentKind: "include", field: l, parent: s, fieldName: n } : (l = a?.getField(n), a && l ? { parentKind: "omit", field: l, parent: a, fieldName: n } : { parentKind: "unknown", fieldName: n }));
}
function ua(e10, r) {
  if (r.kind === "object") for (let t of r.fields) e10.hasField(t.name) || e10.addSuggestion(new ae(t.name, t.typeNames.join(" | ")));
}
function lt(e10) {
  let r = [...e10], t = r.pop();
  if (!t) throw new Error("unexpected empty path");
  return [r, t];
}
function ut({ green: e10, enabled: r }) {
  return "Available options are " + (r ? `listed in ${e10("green")}` : "marked with ?") + ".";
}
function vn(e10, r) {
  if (r.length === 1) return r[0];
  let t = [...r], n = t.pop();
  return `${t.join(", ")} ${e10} ${n}`;
}
var Md = 3;
function $d(e10, r) {
  let t = 1 / 0, n;
  for (let i of r) {
    let o = (0, ia.default)(e10, i);
    o > Md || o < t && (t = o, n = i);
  }
  return n;
}
var ct = class {
  modelName;
  name;
  typeName;
  isList;
  isEnum;
  constructor(r, t, n, i, o) {
    this.modelName = r, this.name = t, this.typeName = n, this.isList = i, this.isEnum = o;
  }
  _toGraphQLInputType() {
    let r = this.isList ? "List" : "", t = this.isEnum ? "Enum" : "";
    return `${r}${t}${this.typeName}FieldRefInput<${this.modelName}>`;
  }
};
function Dr(e10) {
  return e10 instanceof ct;
}
var Pn = Symbol();
var ji = /* @__PURE__ */ new WeakMap();
var Fe = class {
  constructor(r) {
    r === Pn ? ji.set(this, `Prisma.${this._getName()}`) : ji.set(this, `new Prisma.${this._getNamespace()}.${this._getName()}()`);
  }
  _getName() {
    return this.constructor.name;
  }
  toString() {
    return ji.get(this);
  }
};
var pt = class extends Fe {
  _getNamespace() {
    return "NullTypes";
  }
};
var dt = class extends pt {
  #e;
};
Bi(dt, "DbNull");
var mt = class extends pt {
  #e;
};
Bi(mt, "JsonNull");
var ft = class extends pt {
  #e;
};
Bi(ft, "AnyNull");
var Vi = { classes: { DbNull: dt, JsonNull: mt, AnyNull: ft }, instances: { DbNull: new dt(Pn), JsonNull: new mt(Pn), AnyNull: new ft(Pn) } };
function Bi(e10, r) {
  Object.defineProperty(e10, "name", { value: r, configurable: true });
}
var ca = ": ";
var Tn = class {
  constructor(r, t) {
    this.name = r;
    this.value = t;
  }
  hasError = false;
  markAsError() {
    this.hasError = true;
  }
  getPrintWidth() {
    return this.name.length + this.value.getPrintWidth() + ca.length;
  }
  write(r) {
    let t = new Pe(this.name);
    this.hasError && t.underline().setColor(r.context.colors.red), r.write(t).write(ca).write(this.value);
  }
};
var Ui = class {
  arguments;
  errorMessages = [];
  constructor(r) {
    this.arguments = r;
  }
  write(r) {
    r.write(this.arguments);
  }
  addErrorMessage(r) {
    this.errorMessages.push(r);
  }
  renderAllMessages(r) {
    return this.errorMessages.map((t) => t(r)).join(`
`);
  }
};
function Or(e10) {
  return new Ui(pa(e10));
}
function pa(e10) {
  let r = new kr();
  for (let [t, n] of Object.entries(e10)) {
    let i = new Tn(t, da(n));
    r.addField(i);
  }
  return r;
}
function da(e10) {
  if (typeof e10 == "string") return new G(JSON.stringify(e10));
  if (typeof e10 == "number" || typeof e10 == "boolean") return new G(String(e10));
  if (typeof e10 == "bigint") return new G(`${e10}n`);
  if (e10 === null) return new G("null");
  if (e10 === void 0) return new G("undefined");
  if (Sr(e10)) return new G(`new Prisma.Decimal("${e10.toFixed()}")`);
  if (e10 instanceof Uint8Array) return Buffer.isBuffer(e10) ? new G(`Buffer.alloc(${e10.byteLength})`) : new G(`new Uint8Array(${e10.byteLength})`);
  if (e10 instanceof Date) {
    let r = mn(e10) ? e10.toISOString() : "Invalid Date";
    return new G(`new Date("${r}")`);
  }
  return e10 instanceof Fe ? new G(`Prisma.${e10._getName()}`) : Dr(e10) ? new G(`prisma.${Ye(e10.modelName)}.$fields.${e10.name}`) : Array.isArray(e10) ? qd(e10) : typeof e10 == "object" ? pa(e10) : new G(Object.prototype.toString.call(e10));
}
function qd(e10) {
  let r = new Ir();
  for (let t of e10) r.addItem(da(t));
  return r;
}
function Sn(e10, r) {
  let t = r === "pretty" ? na : xn, n = e10.renderAllMessages(t), i = new Cr(0, { colors: t }).write(e10).toString();
  return { message: n, args: i };
}
function Rn({ args: e10, errors: r, errorFormat: t, callsite: n, originalMethod: i, clientVersion: o, globalOmit: s }) {
  let a = Or(e10);
  for (let p of r) bn(p, a, s);
  let { message: l, args: u } = Sn(a, t), c = yn({ message: l, callsite: n, originalMethod: i, showColors: t === "pretty", callArguments: u });
  throw new X(c, { clientVersion: o });
}
function Te(e10) {
  return e10.replace(/^./, (r) => r.toLowerCase());
}
function fa(e10, r, t) {
  let n = Te(t);
  return !r.result || !(r.result.$allModels || r.result[n]) ? e10 : jd({ ...e10, ...ma(r.name, e10, r.result.$allModels), ...ma(r.name, e10, r.result[n]) });
}
function jd(e10) {
  let r = new ve(), t = (n, i) => r.getOrCreate(n, () => i.has(n) ? [n] : (i.add(n), e10[n] ? e10[n].needs.flatMap((o) => t(o, i)) : [n]));
  return xr(e10, (n) => ({ ...n, needs: t(n.name, /* @__PURE__ */ new Set()) }));
}
function ma(e10, r, t) {
  return t ? xr(t, ({ needs: n, compute: i }, o) => ({ name: o, needs: n ? Object.keys(n).filter((s) => n[s]) : [], compute: Vd(r, o, i) })) : {};
}
function Vd(e10, r, t) {
  let n = e10?.[r]?.compute;
  return n ? (i) => t({ ...i, [r]: n(i) }) : t;
}
function ga(e10, r) {
  if (!r) return e10;
  let t = { ...e10 };
  for (let n of Object.values(r)) if (e10[n.name]) for (let i of n.needs) t[i] = true;
  return t;
}
function ha(e10, r) {
  if (!r) return e10;
  let t = { ...e10 };
  for (let n of Object.values(r)) if (!e10[n.name]) for (let i of n.needs) delete t[i];
  return t;
}
var Cn = class {
  constructor(r, t) {
    this.extension = r;
    this.previous = t;
  }
  computedFieldsCache = new ve();
  modelExtensionsCache = new ve();
  queryCallbacksCache = new ve();
  clientExtensions = st(() => this.extension.client ? { ...this.previous?.getAllClientExtensions(), ...this.extension.client } : this.previous?.getAllClientExtensions());
  batchCallbacks = st(() => {
    let r = this.previous?.getAllBatchQueryCallbacks() ?? [], t = this.extension.query?.$__internalBatch;
    return t ? r.concat(t) : r;
  });
  getAllComputedFields(r) {
    return this.computedFieldsCache.getOrCreate(r, () => fa(this.previous?.getAllComputedFields(r), this.extension, r));
  }
  getAllClientExtensions() {
    return this.clientExtensions.get();
  }
  getAllModelExtensions(r) {
    return this.modelExtensionsCache.getOrCreate(r, () => {
      let t = Te(r);
      return !this.extension.model || !(this.extension.model[t] || this.extension.model.$allModels) ? this.previous?.getAllModelExtensions(r) : { ...this.previous?.getAllModelExtensions(r), ...this.extension.model.$allModels, ...this.extension.model[t] };
    });
  }
  getAllQueryCallbacks(r, t) {
    return this.queryCallbacksCache.getOrCreate(`${r}:${t}`, () => {
      let n = this.previous?.getAllQueryCallbacks(r, t) ?? [], i = [], o = this.extension.query;
      return !o || !(o[r] || o.$allModels || o[t] || o.$allOperations) ? n : (o[r] !== void 0 && (o[r][t] !== void 0 && i.push(o[r][t]), o[r].$allOperations !== void 0 && i.push(o[r].$allOperations)), r !== "$none" && o.$allModels !== void 0 && (o.$allModels[t] !== void 0 && i.push(o.$allModels[t]), o.$allModels.$allOperations !== void 0 && i.push(o.$allModels.$allOperations)), o[t] !== void 0 && i.push(o[t]), o.$allOperations !== void 0 && i.push(o.$allOperations), n.concat(i));
    });
  }
  getAllBatchQueryCallbacks() {
    return this.batchCallbacks.get();
  }
};
var _r = class e7 {
  constructor(r) {
    this.head = r;
  }
  static empty() {
    return new e7();
  }
  static single(r) {
    return new e7(new Cn(r));
  }
  isEmpty() {
    return this.head === void 0;
  }
  append(r) {
    return new e7(new Cn(r, this.head));
  }
  getAllComputedFields(r) {
    return this.head?.getAllComputedFields(r);
  }
  getAllClientExtensions() {
    return this.head?.getAllClientExtensions();
  }
  getAllModelExtensions(r) {
    return this.head?.getAllModelExtensions(r);
  }
  getAllQueryCallbacks(r, t) {
    return this.head?.getAllQueryCallbacks(r, t) ?? [];
  }
  getAllBatchQueryCallbacks() {
    return this.head?.getAllBatchQueryCallbacks() ?? [];
  }
};
var An = class {
  constructor(r) {
    this.name = r;
  }
};
function ya(e10) {
  return e10 instanceof An;
}
function Bd(e10) {
  return new An(e10);
}
var ba = Symbol();
var gt = class {
  constructor(r) {
    if (r !== ba) throw new Error("Skip instance can not be constructed directly");
  }
  ifUndefined(r) {
    return r === void 0 ? Gi : r;
  }
};
var Gi = new gt(ba);
function Se(e10) {
  return e10 instanceof gt;
}
var Ud = { findUnique: "findUnique", findUniqueOrThrow: "findUniqueOrThrow", findFirst: "findFirst", findFirstOrThrow: "findFirstOrThrow", findMany: "findMany", count: "aggregate", create: "createOne", createMany: "createMany", createManyAndReturn: "createManyAndReturn", update: "updateOne", updateMany: "updateMany", updateManyAndReturn: "updateManyAndReturn", upsert: "upsertOne", delete: "deleteOne", deleteMany: "deleteMany", executeRaw: "executeRaw", queryRaw: "queryRaw", aggregate: "aggregate", groupBy: "groupBy", runCommandRaw: "runCommandRaw", findRaw: "findRaw", aggregateRaw: "aggregateRaw" };
var Ea = "explicitly `undefined` values are not allowed";
function Wi({ modelName: e10, action: r, args: t, runtimeDataModel: n, extensions: i = _r.empty(), callsite: o, clientMethod: s, errorFormat: a, clientVersion: l, previewFeatures: u, globalOmit: c }) {
  let p = new Qi({ runtimeDataModel: n, modelName: e10, action: r, rootArgs: t, callsite: o, extensions: i, selectionPath: [], argumentPath: [], originalMethod: s, errorFormat: a, clientVersion: l, previewFeatures: u, globalOmit: c });
  return { modelName: e10, action: Ud[r], query: ht(t, p) };
}
function ht({ select: e10, include: r, ...t } = {}, n) {
  let i = t.omit;
  return delete t.omit, { arguments: xa(t, n), selection: Gd(e10, r, i, n) };
}
function Gd(e10, r, t, n) {
  return e10 ? (r ? n.throwValidationError({ kind: "MutuallyExclusiveFields", firstField: "include", secondField: "select", selectionPath: n.getSelectionPath() }) : t && n.throwValidationError({ kind: "MutuallyExclusiveFields", firstField: "omit", secondField: "select", selectionPath: n.getSelectionPath() }), Hd(e10, n)) : Qd(n, r, t);
}
function Qd(e10, r, t) {
  let n = {};
  return e10.modelOrType && !e10.isRawAction() && (n.$composites = true, n.$scalars = true), r && Wd(n, r, e10), Jd(n, t, e10), n;
}
function Wd(e10, r, t) {
  for (let [n, i] of Object.entries(r)) {
    if (Se(i)) continue;
    let o = t.nestSelection(n);
    if (Ji(i, o), i === false || i === void 0) {
      e10[n] = false;
      continue;
    }
    let s = t.findField(n);
    if (s && s.kind !== "object" && t.throwValidationError({ kind: "IncludeOnScalar", selectionPath: t.getSelectionPath().concat(n), outputType: t.getOutputTypeDescription() }), s) {
      e10[n] = ht(i === true ? {} : i, o);
      continue;
    }
    if (i === true) {
      e10[n] = true;
      continue;
    }
    e10[n] = ht(i, o);
  }
}
function Jd(e10, r, t) {
  let n = t.getComputedFields(), i = { ...t.getGlobalOmit(), ...r }, o = ha(i, n);
  for (let [s, a] of Object.entries(o)) {
    if (Se(a)) continue;
    Ji(a, t.nestSelection(s));
    let l = t.findField(s);
    n?.[s] && !l || (e10[s] = !a);
  }
}
function Hd(e10, r) {
  let t = {}, n = r.getComputedFields(), i = ga(e10, n);
  for (let [o, s] of Object.entries(i)) {
    if (Se(s)) continue;
    let a = r.nestSelection(o);
    Ji(s, a);
    let l = r.findField(o);
    if (!(n?.[o] && !l)) {
      if (s === false || s === void 0 || Se(s)) {
        t[o] = false;
        continue;
      }
      if (s === true) {
        l?.kind === "object" ? t[o] = ht({}, a) : t[o] = true;
        continue;
      }
      t[o] = ht(s, a);
    }
  }
  return t;
}
function wa(e10, r) {
  if (e10 === null) return null;
  if (typeof e10 == "string" || typeof e10 == "number" || typeof e10 == "boolean") return e10;
  if (typeof e10 == "bigint") return { $type: "BigInt", value: String(e10) };
  if (Tr(e10)) {
    if (mn(e10)) return { $type: "DateTime", value: e10.toISOString() };
    r.throwValidationError({ kind: "InvalidArgumentValue", selectionPath: r.getSelectionPath(), argumentPath: r.getArgumentPath(), argument: { name: r.getArgumentName(), typeNames: ["Date"] }, underlyingError: "Provided Date object is invalid" });
  }
  if (ya(e10)) return { $type: "Param", value: e10.name };
  if (Dr(e10)) return { $type: "FieldRef", value: { _ref: e10.name, _container: e10.modelName } };
  if (Array.isArray(e10)) return Kd(e10, r);
  if (ArrayBuffer.isView(e10)) {
    let { buffer: t, byteOffset: n, byteLength: i } = e10;
    return { $type: "Bytes", value: Buffer.from(t, n, i).toString("base64") };
  }
  if (Yd(e10)) return e10.values;
  if (Sr(e10)) return { $type: "Decimal", value: e10.toFixed() };
  if (e10 instanceof Fe) {
    if (e10 !== Vi.instances[e10._getName()]) throw new Error("Invalid ObjectEnumValue");
    return { $type: "Enum", value: e10._getName() };
  }
  if (zd(e10)) return e10.toJSON();
  if (typeof e10 == "object") return xa(e10, r);
  r.throwValidationError({ kind: "InvalidArgumentValue", selectionPath: r.getSelectionPath(), argumentPath: r.getArgumentPath(), argument: { name: r.getArgumentName(), typeNames: [] }, underlyingError: `We could not serialize ${Object.prototype.toString.call(e10)} value. Serialize the object to JSON or implement a ".toJSON()" method on it` });
}
function xa(e10, r) {
  if (e10.$type) return { $type: "Raw", value: e10 };
  let t = {};
  for (let n in e10) {
    let i = e10[n], o = r.nestArgument(n);
    Se(i) || (i !== void 0 ? t[n] = wa(i, o) : r.isPreviewFeatureOn("strictUndefinedChecks") && r.throwValidationError({ kind: "InvalidArgumentValue", argumentPath: o.getArgumentPath(), selectionPath: r.getSelectionPath(), argument: { name: r.getArgumentName(), typeNames: [] }, underlyingError: Ea }));
  }
  return t;
}
function Kd(e10, r) {
  let t = [];
  for (let n = 0; n < e10.length; n++) {
    let i = r.nestArgument(String(n)), o = e10[n];
    if (o === void 0 || Se(o)) {
      let s = o === void 0 ? "undefined" : "Prisma.skip";
      r.throwValidationError({ kind: "InvalidArgumentValue", selectionPath: i.getSelectionPath(), argumentPath: i.getArgumentPath(), argument: { name: `${r.getArgumentName()}[${n}]`, typeNames: [] }, underlyingError: `Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values` });
    }
    t.push(wa(o, i));
  }
  return t;
}
function Yd(e10) {
  return typeof e10 == "object" && e10 !== null && e10.__prismaRawParameters__ === true;
}
function zd(e10) {
  return typeof e10 == "object" && e10 !== null && typeof e10.toJSON == "function";
}
function Ji(e10, r) {
  e10 === void 0 && r.isPreviewFeatureOn("strictUndefinedChecks") && r.throwValidationError({ kind: "InvalidSelectionValue", selectionPath: r.getSelectionPath(), underlyingError: Ea });
}
var Qi = class e8 {
  constructor(r) {
    this.params = r;
    this.params.modelName && (this.modelOrType = this.params.runtimeDataModel.models[this.params.modelName] ?? this.params.runtimeDataModel.types[this.params.modelName]);
  }
  modelOrType;
  throwValidationError(r) {
    Rn({ errors: [r], originalMethod: this.params.originalMethod, args: this.params.rootArgs ?? {}, callsite: this.params.callsite, errorFormat: this.params.errorFormat, clientVersion: this.params.clientVersion, globalOmit: this.params.globalOmit });
  }
  getSelectionPath() {
    return this.params.selectionPath;
  }
  getArgumentPath() {
    return this.params.argumentPath;
  }
  getArgumentName() {
    return this.params.argumentPath[this.params.argumentPath.length - 1];
  }
  getOutputTypeDescription() {
    if (!(!this.params.modelName || !this.modelOrType)) return { name: this.params.modelName, fields: this.modelOrType.fields.map((r) => ({ name: r.name, typeName: "boolean", isRelation: r.kind === "object" })) };
  }
  isRawAction() {
    return ["executeRaw", "queryRaw", "runCommandRaw", "findRaw", "aggregateRaw"].includes(this.params.action);
  }
  isPreviewFeatureOn(r) {
    return this.params.previewFeatures.includes(r);
  }
  getComputedFields() {
    if (this.params.modelName) return this.params.extensions.getAllComputedFields(this.params.modelName);
  }
  findField(r) {
    return this.modelOrType?.fields.find((t) => t.name === r);
  }
  nestSelection(r) {
    let t = this.findField(r), n = t?.kind === "object" ? t.type : void 0;
    return new e8({ ...this.params, modelName: n, selectionPath: this.params.selectionPath.concat(r) });
  }
  getGlobalOmit() {
    return this.params.modelName && this.shouldApplyGlobalOmit() ? this.params.globalOmit?.[Ye(this.params.modelName)] ?? {} : {};
  }
  shouldApplyGlobalOmit() {
    switch (this.params.action) {
      case "findFirst":
      case "findFirstOrThrow":
      case "findUniqueOrThrow":
      case "findMany":
      case "upsert":
      case "findUnique":
      case "createManyAndReturn":
      case "create":
      case "update":
      case "updateManyAndReturn":
      case "delete":
        return true;
      case "executeRaw":
      case "aggregateRaw":
      case "runCommandRaw":
      case "findRaw":
      case "createMany":
      case "deleteMany":
      case "groupBy":
      case "updateMany":
      case "count":
      case "aggregate":
      case "queryRaw":
        return false;
      default:
        Oe(this.params.action, "Unknown action");
    }
  }
  nestArgument(r) {
    return new e8({ ...this.params, argumentPath: this.params.argumentPath.concat(r) });
  }
};
function va(e10) {
  if (!e10._hasPreviewFlag("metrics")) throw new X("`metrics` preview feature must be enabled in order to access metrics API", { clientVersion: e10._clientVersion });
}
var yt = class {
  _client;
  constructor(r) {
    this._client = r;
  }
  prometheus(r) {
    return va(this._client), this._client._engine.metrics({ format: "prometheus", ...r });
  }
  json(r) {
    return va(this._client), this._client._engine.metrics({ format: "json", ...r });
  }
};
function Zd(e10, r) {
  let t = st(() => Xd(r));
  Object.defineProperty(e10, "dmmf", { get: () => t.get() });
}
function Xd(e10) {
  return { datamodel: { models: Hi(e10.models), enums: Hi(e10.enums), types: Hi(e10.types) } };
}
function Hi(e10) {
  return Object.entries(e10).map(([r, t]) => ({ name: r, ...t }));
}
var Ki = /* @__PURE__ */ new WeakMap();
var In = "$$PrismaTypedSql";
var bt = class {
  constructor(r, t) {
    Ki.set(this, { sql: r, values: t }), Object.defineProperty(this, In, { value: In });
  }
  get sql() {
    return Ki.get(this).sql;
  }
  get values() {
    return Ki.get(this).values;
  }
};
function em(e10) {
  return (...r) => new bt(e10, r);
}
function kn(e10) {
  return e10 != null && e10[In] === In;
}
var tu = ne(fi());
var le = class e9 {
  constructor(r, t) {
    if (r.length - 1 !== t.length) throw r.length === 0 ? new TypeError("Expected at least 1 string") : new TypeError(`Expected ${r.length} strings to have ${r.length - 1} values`);
    let n = t.reduce((s, a) => s + (a instanceof e9 ? a.values.length : 1), 0);
    this.values = new Array(n), this.strings = new Array(n + 1), this.strings[0] = r[0];
    let i = 0, o = 0;
    for (; i < t.length; ) {
      let s = t[i++], a = r[i];
      if (s instanceof e9) {
        this.strings[o] += s.strings[0];
        let l = 0;
        for (; l < s.values.length; ) this.values[o++] = s.values[l++], this.strings[o] = s.strings[l];
        this.strings[o] += a;
      } else this.values[o++] = s, this.strings[o] = a;
    }
  }
  get sql() {
    let r = this.strings.length, t = 1, n = this.strings[0];
    for (; t < r; ) n += `?${this.strings[t++]}`;
    return n;
  }
  get statement() {
    let r = this.strings.length, t = 1, n = this.strings[0];
    for (; t < r; ) n += `:${t}${this.strings[t++]}`;
    return n;
  }
  get text() {
    let r = this.strings.length, t = 1, n = this.strings[0];
    for (; t < r; ) n += `$${t}${this.strings[t++]}`;
    return n;
  }
  inspect() {
    return { sql: this.sql, statement: this.statement, text: this.text, values: this.values };
  }
};
function rm(e10, r = ",", t = "", n = "") {
  if (e10.length === 0) throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");
  return new le([t, ...Array(e10.length - 1).fill(r), n], e10);
}
function Pa(e10) {
  return new le([e10], []);
}
var tm = Pa("");
function Ta(e10, ...r) {
  return new le(e10, r);
}
function Et(e10) {
  return { getKeys() {
    return Object.keys(e10);
  }, getPropertyValue(r) {
    return e10[r];
  } };
}
function ee(e10, r) {
  return { getKeys() {
    return [e10];
  }, getPropertyValue() {
    return r();
  } };
}
function sr(e10) {
  let r = new ve();
  return { getKeys() {
    return e10.getKeys();
  }, getPropertyValue(t) {
    return r.getOrCreate(t, () => e10.getPropertyValue(t));
  }, getPropertyDescriptor(t) {
    return e10.getPropertyDescriptor?.(t);
  } };
}
var Dn = { enumerable: true, configurable: true, writable: true };
function On(e10) {
  let r = new Set(e10);
  return { getPrototypeOf: () => Object.prototype, getOwnPropertyDescriptor: () => Dn, has: (t, n) => r.has(n), set: (t, n, i) => r.add(n) && Reflect.set(t, n, i), ownKeys: () => [...r] };
}
var Sa = Symbol.for("nodejs.util.inspect.custom");
function he(e10, r) {
  let t = nm(r), n = /* @__PURE__ */ new Set(), i = new Proxy(e10, { get(o, s) {
    if (n.has(s)) return o[s];
    let a = t.get(s);
    return a ? a.getPropertyValue(s) : o[s];
  }, has(o, s) {
    if (n.has(s)) return true;
    let a = t.get(s);
    return a ? a.has?.(s) ?? true : Reflect.has(o, s);
  }, ownKeys(o) {
    let s = Ra(Reflect.ownKeys(o), t), a = Ra(Array.from(t.keys()), t);
    return [.../* @__PURE__ */ new Set([...s, ...a, ...n])];
  }, set(o, s, a) {
    return t.get(s)?.getPropertyDescriptor?.(s)?.writable === false ? false : (n.add(s), Reflect.set(o, s, a));
  }, getOwnPropertyDescriptor(o, s) {
    let a = Reflect.getOwnPropertyDescriptor(o, s);
    if (a && !a.configurable) return a;
    let l = t.get(s);
    return l ? l.getPropertyDescriptor ? { ...Dn, ...l?.getPropertyDescriptor(s) } : Dn : a;
  }, defineProperty(o, s, a) {
    return n.add(s), Reflect.defineProperty(o, s, a);
  }, getPrototypeOf: () => Object.prototype });
  return i[Sa] = function() {
    let o = { ...this };
    return delete o[Sa], o;
  }, i;
}
function nm(e10) {
  let r = /* @__PURE__ */ new Map();
  for (let t of e10) {
    let n = t.getKeys();
    for (let i of n) r.set(i, t);
  }
  return r;
}
function Ra(e10, r) {
  return e10.filter((t) => r.get(t)?.has?.(t) ?? true);
}
function Nr(e10) {
  return { getKeys() {
    return e10;
  }, has() {
    return false;
  }, getPropertyValue() {
  } };
}
function Lr(e10, r) {
  return { batch: e10, transaction: r?.kind === "batch" ? { isolationLevel: r.options.isolationLevel } : void 0 };
}
function Ca(e10) {
  if (e10 === void 0) return "";
  let r = Or(e10);
  return new Cr(0, { colors: xn }).write(r).toString();
}
var im = "P2037";
function Fr({ error: e10, user_facing_error: r }, t, n) {
  return r.error_code ? new Z(om(r, n), { code: r.error_code, clientVersion: t, meta: r.meta, batchRequestIdx: r.batch_request_idx }) : new q(e10, { clientVersion: t, batchRequestIdx: r.batch_request_idx });
}
function om(e10, r) {
  let t = e10.message;
  return (r === "postgresql" || r === "postgres" || r === "mysql") && e10.error_code === im && (t += `
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`), t;
}
var wt = "<unknown>";
function Aa(e10) {
  var r = e10.split(`
`);
  return r.reduce(function(t, n) {
    var i = lm(n) || cm(n) || mm(n) || ym(n) || gm(n);
    return i && t.push(i), t;
  }, []);
}
var sm = /^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i;
var am = /\((\S*)(?::(\d+))(?::(\d+))\)/;
function lm(e10) {
  var r = sm.exec(e10);
  if (!r) return null;
  var t = r[2] && r[2].indexOf("native") === 0, n = r[2] && r[2].indexOf("eval") === 0, i = am.exec(r[2]);
  return n && i != null && (r[2] = i[1], r[3] = i[2], r[4] = i[3]), { file: t ? null : r[2], methodName: r[1] || wt, arguments: t ? [r[2]] : [], lineNumber: r[3] ? +r[3] : null, column: r[4] ? +r[4] : null };
}
var um = /^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;
function cm(e10) {
  var r = um.exec(e10);
  return r ? { file: r[2], methodName: r[1] || wt, arguments: [], lineNumber: +r[3], column: r[4] ? +r[4] : null } : null;
}
var pm = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i;
var dm = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i;
function mm(e10) {
  var r = pm.exec(e10);
  if (!r) return null;
  var t = r[3] && r[3].indexOf(" > eval") > -1, n = dm.exec(r[3]);
  return t && n != null && (r[3] = n[1], r[4] = n[2], r[5] = null), { file: r[3], methodName: r[1] || wt, arguments: r[2] ? r[2].split(",") : [], lineNumber: r[4] ? +r[4] : null, column: r[5] ? +r[5] : null };
}
var fm = /^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;
function gm(e10) {
  var r = fm.exec(e10);
  return r ? { file: r[3], methodName: r[1] || wt, arguments: [], lineNumber: +r[4], column: r[5] ? +r[5] : null } : null;
}
var hm = /^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;
function ym(e10) {
  var r = hm.exec(e10);
  return r ? { file: r[2], methodName: r[1] || wt, arguments: [], lineNumber: +r[3], column: r[4] ? +r[4] : null } : null;
}
var Yi = class {
  getLocation() {
    return null;
  }
};
var zi = class {
  _error;
  constructor() {
    this._error = new Error();
  }
  getLocation() {
    let r = this._error.stack;
    if (!r) return null;
    let n = Aa(r).find((i) => {
      if (!i.file) return false;
      let o = Ti(i.file);
      return o !== "<anonymous>" && !o.includes("@prisma") && !o.includes("/packages/client/src/runtime/") && !o.endsWith("/runtime/binary.js") && !o.endsWith("/runtime/library.js") && !o.endsWith("/runtime/edge.js") && !o.endsWith("/runtime/edge-esm.js") && !o.startsWith("internal/") && !i.methodName.includes("new ") && !i.methodName.includes("getCallSite") && !i.methodName.includes("Proxy.") && i.methodName.split(".").length < 4;
    });
    return !n || !n.file ? null : { fileName: n.file, lineNumber: n.lineNumber, columnNumber: n.column };
  }
};
function Ze(e10) {
  return e10 === "minimal" ? typeof $EnabledCallSite == "function" && e10 !== "minimal" ? new $EnabledCallSite() : new Yi() : new zi();
}
var Ia = { _avg: true, _count: true, _sum: true, _min: true, _max: true };
function Mr(e10 = {}) {
  let r = Em(e10);
  return Object.entries(r).reduce((n, [i, o]) => (Ia[i] !== void 0 ? n.select[i] = { select: o } : n[i] = o, n), { select: {} });
}
function Em(e10 = {}) {
  return typeof e10._count == "boolean" ? { ...e10, _count: { _all: e10._count } } : e10;
}
function _n(e10 = {}) {
  return (r) => (typeof e10._count == "boolean" && (r._count = r._count._all), r);
}
function ka(e10, r) {
  let t = _n(e10);
  return r({ action: "aggregate", unpacker: t, argsMapper: Mr })(e10);
}
function wm(e10 = {}) {
  let { select: r, ...t } = e10;
  return typeof r == "object" ? Mr({ ...t, _count: r }) : Mr({ ...t, _count: { _all: true } });
}
function xm(e10 = {}) {
  return typeof e10.select == "object" ? (r) => _n(e10)(r)._count : (r) => _n(e10)(r)._count._all;
}
function Da(e10, r) {
  return r({ action: "count", unpacker: xm(e10), argsMapper: wm })(e10);
}
function vm(e10 = {}) {
  let r = Mr(e10);
  if (Array.isArray(r.by)) for (let t of r.by) typeof t == "string" && (r.select[t] = true);
  else typeof r.by == "string" && (r.select[r.by] = true);
  return r;
}
function Pm(e10 = {}) {
  return (r) => (typeof e10?._count == "boolean" && r.forEach((t) => {
    t._count = t._count._all;
  }), r);
}
function Oa(e10, r) {
  return r({ action: "groupBy", unpacker: Pm(e10), argsMapper: vm })(e10);
}
function _a(e10, r, t) {
  if (r === "aggregate") return (n) => ka(n, t);
  if (r === "count") return (n) => Da(n, t);
  if (r === "groupBy") return (n) => Oa(n, t);
}
function Na(e10, r) {
  let t = r.fields.filter((i) => !i.relationName), n = Qs(t, "name");
  return new Proxy({}, { get(i, o) {
    if (o in i || typeof o == "symbol") return i[o];
    let s = n[o];
    if (s) return new ct(e10, o, s.type, s.isList, s.kind === "enum");
  }, ...On(Object.keys(n)) });
}
var La = (e10) => Array.isArray(e10) ? e10 : e10.split(".");
var Zi = (e10, r) => La(r).reduce((t, n) => t && t[n], e10);
var Fa = (e10, r, t) => La(r).reduceRight((n, i, o, s) => Object.assign({}, Zi(e10, s.slice(0, o)), { [i]: n }), t);
function Tm(e10, r) {
  return e10 === void 0 || r === void 0 ? [] : [...r, "select", e10];
}
function Sm(e10, r, t) {
  return r === void 0 ? e10 ?? {} : Fa(r, t, e10 || true);
}
function Xi(e10, r, t, n, i, o) {
  let a = e10._runtimeDataModel.models[r].fields.reduce((l, u) => ({ ...l, [u.name]: u }), {});
  return (l) => {
    let u = Ze(e10._errorFormat), c = Tm(n, i), p = Sm(l, o, c), d = t({ dataPath: c, callsite: u })(p), f = Rm(e10, r);
    return new Proxy(d, { get(h, g) {
      if (!f.includes(g)) return h[g];
      let P = [a[g].type, t, g], R = [c, p];
      return Xi(e10, ...P, ...R);
    }, ...On([...f, ...Object.getOwnPropertyNames(d)]) });
  };
}
function Rm(e10, r) {
  return e10._runtimeDataModel.models[r].fields.filter((t) => t.kind === "object").map((t) => t.name);
}
var Cm = ["findUnique", "findUniqueOrThrow", "findFirst", "findFirstOrThrow", "create", "update", "upsert", "delete"];
var Am = ["aggregate", "count", "groupBy"];
function eo(e10, r) {
  let t = e10._extensions.getAllModelExtensions(r) ?? {}, n = [Im(e10, r), Dm(e10, r), Et(t), ee("name", () => r), ee("$name", () => r), ee("$parent", () => e10._appliedParent)];
  return he({}, n);
}
function Im(e10, r) {
  let t = Te(r), n = Object.keys(Rr).concat("count");
  return { getKeys() {
    return n;
  }, getPropertyValue(i) {
    let o = i, s = (a) => (l) => {
      let u = Ze(e10._errorFormat);
      return e10._createPrismaPromise((c) => {
        let p = { args: l, dataPath: [], action: o, model: r, clientMethod: `${t}.${i}`, jsModelName: t, transaction: c, callsite: u };
        return e10._request({ ...p, ...a });
      }, { action: o, args: l, model: r });
    };
    return Cm.includes(o) ? Xi(e10, r, s) : km(i) ? _a(e10, i, s) : s({});
  } };
}
function km(e10) {
  return Am.includes(e10);
}
function Dm(e10, r) {
  return sr(ee("fields", () => {
    let t = e10._runtimeDataModel.models[r];
    return Na(r, t);
  }));
}
function Ma(e10) {
  return e10.replace(/^./, (r) => r.toUpperCase());
}
var ro = Symbol();
function xt(e10) {
  let r = [Om(e10), _m(e10), ee(ro, () => e10), ee("$parent", () => e10._appliedParent)], t = e10._extensions.getAllClientExtensions();
  return t && r.push(Et(t)), he(e10, r);
}
function Om(e10) {
  let r = Object.getPrototypeOf(e10._originalClient), t = [...new Set(Object.getOwnPropertyNames(r))];
  return { getKeys() {
    return t;
  }, getPropertyValue(n) {
    return e10[n];
  } };
}
function _m(e10) {
  let r = Object.keys(e10._runtimeDataModel.models), t = r.map(Te), n = [...new Set(r.concat(t))];
  return sr({ getKeys() {
    return n;
  }, getPropertyValue(i) {
    let o = Ma(i);
    if (e10._runtimeDataModel.models[o] !== void 0) return eo(e10, o);
    if (e10._runtimeDataModel.models[i] !== void 0) return eo(e10, i);
  }, getPropertyDescriptor(i) {
    if (!t.includes(i)) return { enumerable: false };
  } });
}
function $a(e10) {
  return e10[ro] ? e10[ro] : e10;
}
function qa(e10) {
  if (typeof e10 == "function") return e10(this);
  if (e10.client?.__AccelerateEngine) {
    let t = e10.client.__AccelerateEngine;
    this._originalClient._engine = new t(this._originalClient._accelerateEngineConfig);
  }
  let r = Object.create(this._originalClient, { _extensions: { value: this._extensions.append(e10) }, _appliedParent: { value: this, configurable: true }, $use: { value: void 0 }, $on: { value: void 0 } });
  return xt(r);
}
function ja({ result: e10, modelName: r, select: t, omit: n, extensions: i }) {
  let o = i.getAllComputedFields(r);
  if (!o) return e10;
  let s = [], a = [];
  for (let l of Object.values(o)) {
    if (n) {
      if (n[l.name]) continue;
      let u = l.needs.filter((c) => n[c]);
      u.length > 0 && a.push(Nr(u));
    } else if (t) {
      if (!t[l.name]) continue;
      let u = l.needs.filter((c) => !t[c]);
      u.length > 0 && a.push(Nr(u));
    }
    Nm(e10, l.needs) && s.push(Lm(l, he(e10, s)));
  }
  return s.length > 0 || a.length > 0 ? he(e10, [...s, ...a]) : e10;
}
function Nm(e10, r) {
  return r.every((t) => ki(e10, t));
}
function Lm(e10, r) {
  return sr(ee(e10.name, () => e10.compute(r)));
}
function Nn({ visitor: e10, result: r, args: t, runtimeDataModel: n, modelName: i }) {
  if (Array.isArray(r)) {
    for (let s = 0; s < r.length; s++) r[s] = Nn({ result: r[s], args: t, modelName: i, runtimeDataModel: n, visitor: e10 });
    return r;
  }
  let o = e10(r, i, t) ?? r;
  return t.include && Va({ includeOrSelect: t.include, result: o, parentModelName: i, runtimeDataModel: n, visitor: e10 }), t.select && Va({ includeOrSelect: t.select, result: o, parentModelName: i, runtimeDataModel: n, visitor: e10 }), o;
}
function Va({ includeOrSelect: e10, result: r, parentModelName: t, runtimeDataModel: n, visitor: i }) {
  for (let [o, s] of Object.entries(e10)) {
    if (!s || r[o] == null || Se(s)) continue;
    let l = n.models[t].fields.find((c) => c.name === o);
    if (!l || l.kind !== "object" || !l.relationName) continue;
    let u = typeof s == "object" ? s : {};
    r[o] = Nn({ visitor: i, result: r[o], args: u, modelName: l.type, runtimeDataModel: n });
  }
}
function Ba({ result: e10, modelName: r, args: t, extensions: n, runtimeDataModel: i, globalOmit: o }) {
  return n.isEmpty() || e10 == null || typeof e10 != "object" || !i.models[r] ? e10 : Nn({ result: e10, args: t ?? {}, modelName: r, runtimeDataModel: i, visitor: (a, l, u) => {
    let c = Te(l);
    return ja({ result: a, modelName: c, select: u.select, omit: u.select ? void 0 : { ...o?.[c], ...u.omit }, extensions: n });
  } });
}
var Fm = ["$connect", "$disconnect", "$on", "$transaction", "$use", "$extends"];
var Ua = Fm;
function Ga(e10) {
  if (e10 instanceof le) return Mm(e10);
  if (kn(e10)) return $m(e10);
  if (Array.isArray(e10)) {
    let t = [e10[0]];
    for (let n = 1; n < e10.length; n++) t[n] = vt(e10[n]);
    return t;
  }
  let r = {};
  for (let t in e10) r[t] = vt(e10[t]);
  return r;
}
function Mm(e10) {
  return new le(e10.strings, e10.values);
}
function $m(e10) {
  return new bt(e10.sql, e10.values);
}
function vt(e10) {
  if (typeof e10 != "object" || e10 == null || e10 instanceof Fe || Dr(e10)) return e10;
  if (Sr(e10)) return new Le(e10.toFixed());
  if (Tr(e10)) return /* @__PURE__ */ new Date(+e10);
  if (ArrayBuffer.isView(e10)) return e10.slice(0);
  if (Array.isArray(e10)) {
    let r = e10.length, t;
    for (t = Array(r); r--; ) t[r] = vt(e10[r]);
    return t;
  }
  if (typeof e10 == "object") {
    let r = {};
    for (let t in e10) t === "__proto__" ? Object.defineProperty(r, t, { value: vt(e10[t]), configurable: true, enumerable: true, writable: true }) : r[t] = vt(e10[t]);
    return r;
  }
  Oe(e10, "Unknown value");
}
function Wa(e10, r, t, n = 0) {
  return e10._createPrismaPromise((i) => {
    let o = r.customDataProxyFetch;
    return "transaction" in r && i !== void 0 && (r.transaction?.kind === "batch" && r.transaction.lock.then(), r.transaction = i), n === t.length ? e10._executeRequest(r) : t[n]({ model: r.model, operation: r.model ? r.action : r.clientMethod, args: Ga(r.args ?? {}), __internalParams: r, query: (s, a = r) => {
      let l = a.customDataProxyFetch;
      return a.customDataProxyFetch = Ya(o, l), a.args = s, Wa(e10, a, t, n + 1);
    } });
  });
}
function Ja(e10, r) {
  let { jsModelName: t, action: n, clientMethod: i } = r, o = t ? n : i;
  if (e10._extensions.isEmpty()) return e10._executeRequest(r);
  let s = e10._extensions.getAllQueryCallbacks(t ?? "$none", o);
  return Wa(e10, r, s);
}
function Ha(e10) {
  return (r) => {
    let t = { requests: r }, n = r[0].extensions.getAllBatchQueryCallbacks();
    return n.length ? Ka(t, n, 0, e10) : e10(t);
  };
}
function Ka(e10, r, t, n) {
  if (t === r.length) return n(e10);
  let i = e10.customDataProxyFetch, o = e10.requests[0].transaction;
  return r[t]({ args: { queries: e10.requests.map((s) => ({ model: s.modelName, operation: s.action, args: s.args })), transaction: o ? { isolationLevel: o.kind === "batch" ? o.isolationLevel : void 0 } : void 0 }, __internalParams: e10, query(s, a = e10) {
    let l = a.customDataProxyFetch;
    return a.customDataProxyFetch = Ya(i, l), Ka(a, r, t + 1, n);
  } });
}
var Qa = (e10) => e10;
function Ya(e10 = Qa, r = Qa) {
  return (t) => e10(r(t));
}
var za = L("prisma:client");
var Za = { Vercel: "vercel", "Netlify CI": "netlify" };
function Xa({ postinstall: e10, ciName: r, clientVersion: t }) {
  if (za("checkPlatformCaching:postinstall", e10), za("checkPlatformCaching:ciName", r), e10 === true && r && r in Za) {
    let n = `Prisma has detected that this project was built on ${r}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${Za[r]}-build`;
    throw console.error(n), new T(n, t);
  }
}
function el(e10, r) {
  return e10 ? e10.datasources ? e10.datasources : e10.datasourceUrl ? { [r[0]]: { url: e10.datasourceUrl } } : {} : {};
}
var qm = () => globalThis.process?.release?.name === "node";
var jm = () => !!globalThis.Bun || !!globalThis.process?.versions?.bun;
var Vm = () => !!globalThis.Deno;
var Bm = () => typeof globalThis.Netlify == "object";
var Um = () => typeof globalThis.EdgeRuntime == "object";
var Gm = () => globalThis.navigator?.userAgent === "Cloudflare-Workers";
function Qm() {
  return [[Bm, "netlify"], [Um, "edge-light"], [Gm, "workerd"], [Vm, "deno"], [jm, "bun"], [qm, "node"]].flatMap((t) => t[0]() ? [t[1]] : []).at(0) ?? "";
}
var Wm = { node: "Node.js", workerd: "Cloudflare Workers", deno: "Deno and Deno Deploy", netlify: "Netlify Edge Functions", "edge-light": "Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)" };
function to() {
  let e10 = Qm();
  return { id: e10, prettyName: Wm[e10] || e10, isEdge: ["workerd", "deno", "netlify", "edge-light"].includes(e10) };
}
function Ln(e10) {
  let { runtimeBinaryTarget: r } = e10;
  return `Add "${r}" to \`binaryTargets\` in the "schema.prisma" file and run \`prisma generate\` after saving it:

${Jm(e10)}`;
}
function Jm(e10) {
  let { generator: r, generatorBinaryTargets: t, runtimeBinaryTarget: n } = e10, i = { fromEnvVar: null, value: n }, o = [...t, i];
  return xi({ ...r, binaryTargets: o });
}
function Xe(e10) {
  let { runtimeBinaryTarget: r } = e10;
  return `Prisma Client could not locate the Query Engine for runtime "${r}".`;
}
function er(e10) {
  let { searchedLocations: r } = e10;
  return `The following locations have been searched:
${[...new Set(r)].map((i) => `  ${i}`).join(`
`)}`;
}
function rl(e10) {
  let { runtimeBinaryTarget: r } = e10;
  return `${Xe(e10)}

This happened because \`binaryTargets\` have been pinned, but the actual deployment also required "${r}".
${Ln(e10)}

${er(e10)}`;
}
function Fn(e10) {
  return `We would appreciate if you could take the time to share some information with us.
Please help us by answering a few questions: https://pris.ly/${e10}`;
}
function Mn(e10) {
  let { errorStack: r } = e10;
  return r?.match(/\/\.next|\/next@|\/next\//) ? `

We detected that you are using Next.js, learn how to fix this: https://pris.ly/d/engine-not-found-nextjs.` : "";
}
function tl(e10) {
  let { queryEngineName: r } = e10;
  return `${Xe(e10)}${Mn(e10)}

This is likely caused by a bundler that has not copied "${r}" next to the resulting bundle.
Ensure that "${r}" has been copied next to the bundle or in "${e10.expectedLocation}".

${Fn("engine-not-found-bundler-investigation")}

${er(e10)}`;
}
function nl(e10) {
  let { runtimeBinaryTarget: r, generatorBinaryTargets: t } = e10, n = t.find((i) => i.native);
  return `${Xe(e10)}

This happened because Prisma Client was generated for "${n?.value ?? "unknown"}", but the actual deployment required "${r}".
${Ln(e10)}

${er(e10)}`;
}
function il(e10) {
  let { queryEngineName: r } = e10;
  return `${Xe(e10)}${Mn(e10)}

This is likely caused by tooling that has not copied "${r}" to the deployment folder.
Ensure that you ran \`prisma generate\` and that "${r}" has been copied to "${e10.expectedLocation}".

${Fn("engine-not-found-tooling-investigation")}

${er(e10)}`;
}
var Km = L("prisma:client:engines:resolveEnginePath");
var Ym = () => new RegExp("runtime[\\\\/]library\\.m?js$");
async function ol(e10, r) {
  let t = { binary: process.env.PRISMA_QUERY_ENGINE_BINARY, library: process.env.PRISMA_QUERY_ENGINE_LIBRARY }[e10] ?? r.prismaPath;
  if (t !== void 0) return t;
  let { enginePath: n, searchedLocations: i } = await zm(e10, r);
  if (Km("enginePath", n), n !== void 0 && e10 === "binary" && gi(n), n !== void 0) return r.prismaPath = n;
  let o = await nr(), s = r.generator?.binaryTargets ?? [], a = s.some((d) => d.native), l = !s.some((d) => d.value === o), u = __filename.match(Ym()) === null, c = { searchedLocations: i, generatorBinaryTargets: s, generator: r.generator, runtimeBinaryTarget: o, queryEngineName: sl(e10, o), expectedLocation: import_node_path5.default.relative(process.cwd(), r.dirname), errorStack: new Error().stack }, p;
  throw a && l ? p = nl(c) : l ? p = rl(c) : u ? p = tl(c) : p = il(c), new T(p, r.clientVersion);
}
async function zm(e10, r) {
  let t = await nr(), n = [], i = [r.dirname, import_node_path5.default.resolve(__dirname, ".."), r.generator?.output?.value ?? __dirname, import_node_path5.default.resolve(__dirname, "../../../.prisma/client"), "/tmp/prisma-engines", r.cwd];
  __filename.includes("resolveEnginePath") && i.push(as());
  for (let o of i) {
    let s = sl(e10, t), a = import_node_path5.default.join(o, s);
    if (n.push(o), import_node_fs6.default.existsSync(a)) return { enginePath: a, searchedLocations: n };
  }
  return { enginePath: void 0, searchedLocations: n };
}
function sl(e10, r) {
  return e10 === "library" ? jt(r, "fs") : `query-engine-${r}${r === "windows" ? ".exe" : ""}`;
}
var no = ne(Pi());
function al(e10) {
  return e10 ? e10.replace(/".*"/g, '"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g, (r) => `${r[0]}5`) : "";
}
function ll(e10) {
  return e10.split(`
`).map((r) => r.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/, "").replace(/\+\d+\s*ms$/, "")).join(`
`);
}
var ul = ne(Is());
function cl({ title: e10, user: r = "prisma", repo: t = "prisma", template: n = "bug_report.yml", body: i }) {
  return (0, ul.default)({ user: r, repo: t, template: n, title: e10, body: i });
}
function pl({ version: e10, binaryTarget: r, title: t, description: n, engineVersion: i, database: o, query: s }) {
  let a = qo(6e3 - (s?.length ?? 0)), l = ll((0, no.default)(a)), u = n ? `# Description
\`\`\`
${n}
\`\`\`` : "", c = (0, no.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${r?.padEnd(19)}|
| Prisma Client   | ${e10?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${u}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s ? al(s) : ""}
\`\`\`
`), p = cl({ title: t, body: c });
  return `${t}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${K(p)}

If you want the Prisma team to look into it, please open the link above 🙏
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`;
}
function io(e10) {
  return e10.name === "DriverAdapterError" && typeof e10.cause == "object";
}
function qn(e10) {
  return { ok: true, value: e10, map(r) {
    return qn(r(e10));
  }, flatMap(r) {
    return r(e10);
  } };
}
function ar(e10) {
  return { ok: false, error: e10, map() {
    return ar(e10);
  }, flatMap() {
    return ar(e10);
  } };
}
var dl = L("driver-adapter-utils");
var oo = class {
  registeredErrors = [];
  consumeError(r) {
    return this.registeredErrors[r];
  }
  registerNewError(r) {
    let t = 0;
    for (; this.registeredErrors[t] !== void 0; ) t++;
    return this.registeredErrors[t] = { error: r }, t;
  }
};
var jn = (e10, r = new oo()) => {
  let t = { adapterName: e10.adapterName, errorRegistry: r, queryRaw: Me(r, e10.queryRaw.bind(e10)), executeRaw: Me(r, e10.executeRaw.bind(e10)), executeScript: Me(r, e10.executeScript.bind(e10)), dispose: Me(r, e10.dispose.bind(e10)), provider: e10.provider, startTransaction: async (...n) => (await Me(r, e10.startTransaction.bind(e10))(...n)).map((o) => Zm(r, o)) };
  return e10.getConnectionInfo && (t.getConnectionInfo = Xm(r, e10.getConnectionInfo.bind(e10))), t;
};
var Zm = (e10, r) => ({ adapterName: r.adapterName, provider: r.provider, options: r.options, queryRaw: Me(e10, r.queryRaw.bind(r)), executeRaw: Me(e10, r.executeRaw.bind(r)), commit: Me(e10, r.commit.bind(r)), rollback: Me(e10, r.rollback.bind(r)) });
function Me(e10, r) {
  return async (...t) => {
    try {
      return qn(await r(...t));
    } catch (n) {
      if (dl("[error@wrapAsync]", n), io(n)) return ar(n.cause);
      let i = e10.registerNewError(n);
      return ar({ kind: "GenericJs", id: i });
    }
  };
}
function Xm(e10, r) {
  return (...t) => {
    try {
      return qn(r(...t));
    } catch (n) {
      if (dl("[error@wrapSync]", n), io(n)) return ar(n.cause);
      let i = e10.registerNewError(n);
      return ar({ kind: "GenericJs", id: i });
    }
  };
}
var ml = "6.12.0";
function $r({ inlineDatasources: e10, overrideDatasources: r, env: t, clientVersion: n }) {
  let i, o = Object.keys(e10)[0], s = e10[o]?.url, a = r[o]?.url;
  if (o === void 0 ? i = void 0 : a ? i = a : s?.value ? i = s.value : s?.fromEnvVar && (i = t[s.fromEnvVar]), s?.fromEnvVar !== void 0 && i === void 0) throw new T(`error: Environment variable not found: ${s.fromEnvVar}.`, n);
  if (i === void 0) throw new T("error: Missing URL environment variable, value, or override.", n);
  return i;
}
var Vn = class extends Error {
  clientVersion;
  cause;
  constructor(r, t) {
    super(r), this.clientVersion = t.clientVersion, this.cause = t.cause;
  }
  get [Symbol.toStringTag]() {
    return this.name;
  }
};
var oe = class extends Vn {
  isRetryable;
  constructor(r, t) {
    super(r, t), this.isRetryable = t.isRetryable ?? true;
  }
};
function C(e10, r) {
  return { ...e10, isRetryable: r };
}
var lr = class extends oe {
  name = "InvalidDatasourceError";
  code = "P6001";
  constructor(r, t) {
    super(r, C(t, false));
  }
};
x(lr, "InvalidDatasourceError");
function fl(e10) {
  let r = { clientVersion: e10.clientVersion }, t = Object.keys(e10.inlineDatasources)[0], n = $r({ inlineDatasources: e10.inlineDatasources, overrideDatasources: e10.overrideDatasources, clientVersion: e10.clientVersion, env: { ...e10.env, ...typeof process < "u" ? process.env : {} } }), i;
  try {
    i = new URL(n);
  } catch {
    throw new lr(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``, r);
  }
  let { protocol: o, searchParams: s } = i;
  if (o !== "prisma:" && o !== zt) throw new lr(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``, r);
  let a = s.get("api_key");
  if (a === null || a.length < 1) throw new lr(`Error validating datasource \`${t}\`: the URL must contain a valid API key`, r);
  let l = bi(i) ? "http:" : "https:", u = new URL(i.href.replace(o, l));
  return { apiKey: a, url: u };
}
var gl = ne(Yt());
var Bn = class {
  apiKey;
  tracingHelper;
  logLevel;
  logQueries;
  engineHash;
  constructor({ apiKey: r, tracingHelper: t, logLevel: n, logQueries: i, engineHash: o }) {
    this.apiKey = r, this.tracingHelper = t, this.logLevel = n, this.logQueries = i, this.engineHash = o;
  }
  build({ traceparent: r, transactionId: t } = {}) {
    let n = { Accept: "application/json", Authorization: `Bearer ${this.apiKey}`, "Content-Type": "application/json", "Prisma-Engine-Hash": this.engineHash, "Prisma-Engine-Version": gl.enginesVersion };
    this.tracingHelper.isEnabled() && (n.traceparent = r ?? this.tracingHelper.getTraceParent()), t && (n["X-Transaction-Id"] = t);
    let i = this.#e();
    return i.length > 0 && (n["X-Capture-Telemetry"] = i.join(", ")), n;
  }
  #e() {
    let r = [];
    return this.tracingHelper.isEnabled() && r.push("tracing"), this.logLevel && r.push(this.logLevel), this.logQueries && r.push("query"), r;
  }
};
function rf(e10) {
  return e10[0] * 1e3 + e10[1] / 1e6;
}
function so(e10) {
  return new Date(rf(e10));
}
var qr = class extends oe {
  name = "ForcedRetryError";
  code = "P5001";
  constructor(r) {
    super("This request must be retried", C(r, true));
  }
};
x(qr, "ForcedRetryError");
var ur = class extends oe {
  name = "NotImplementedYetError";
  code = "P5004";
  constructor(r, t) {
    super(r, C(t, false));
  }
};
x(ur, "NotImplementedYetError");
var F = class extends oe {
  response;
  constructor(r, t) {
    super(r, t), this.response = t.response;
    let n = this.response.headers.get("prisma-request-id");
    if (n) {
      let i = `(The request id was: ${n})`;
      this.message = this.message + " " + i;
    }
  }
};
var cr = class extends F {
  name = "SchemaMissingError";
  code = "P5005";
  constructor(r) {
    super("Schema needs to be uploaded", C(r, true));
  }
};
x(cr, "SchemaMissingError");
var ao = "This request could not be understood by the server";
var Pt = class extends F {
  name = "BadRequestError";
  code = "P5000";
  constructor(r, t, n) {
    super(t || ao, C(r, false)), n && (this.code = n);
  }
};
x(Pt, "BadRequestError");
var Tt = class extends F {
  name = "HealthcheckTimeoutError";
  code = "P5013";
  logs;
  constructor(r, t) {
    super("Engine not started: healthcheck timeout", C(r, true)), this.logs = t;
  }
};
x(Tt, "HealthcheckTimeoutError");
var St = class extends F {
  name = "EngineStartupError";
  code = "P5014";
  logs;
  constructor(r, t, n) {
    super(t, C(r, true)), this.logs = n;
  }
};
x(St, "EngineStartupError");
var Rt = class extends F {
  name = "EngineVersionNotSupportedError";
  code = "P5012";
  constructor(r) {
    super("Engine version is not supported", C(r, false));
  }
};
x(Rt, "EngineVersionNotSupportedError");
var lo = "Request timed out";
var Ct = class extends F {
  name = "GatewayTimeoutError";
  code = "P5009";
  constructor(r, t = lo) {
    super(t, C(r, false));
  }
};
x(Ct, "GatewayTimeoutError");
var tf = "Interactive transaction error";
var At = class extends F {
  name = "InteractiveTransactionError";
  code = "P5015";
  constructor(r, t = tf) {
    super(t, C(r, false));
  }
};
x(At, "InteractiveTransactionError");
var nf = "Request parameters are invalid";
var It = class extends F {
  name = "InvalidRequestError";
  code = "P5011";
  constructor(r, t = nf) {
    super(t, C(r, false));
  }
};
x(It, "InvalidRequestError");
var uo = "Requested resource does not exist";
var kt = class extends F {
  name = "NotFoundError";
  code = "P5003";
  constructor(r, t = uo) {
    super(t, C(r, false));
  }
};
x(kt, "NotFoundError");
var co = "Unknown server error";
var jr = class extends F {
  name = "ServerError";
  code = "P5006";
  logs;
  constructor(r, t, n) {
    super(t || co, C(r, true)), this.logs = n;
  }
};
x(jr, "ServerError");
var po = "Unauthorized, check your connection string";
var Dt = class extends F {
  name = "UnauthorizedError";
  code = "P5007";
  constructor(r, t = po) {
    super(t, C(r, false));
  }
};
x(Dt, "UnauthorizedError");
var mo = "Usage exceeded, retry again later";
var Ot = class extends F {
  name = "UsageExceededError";
  code = "P5008";
  constructor(r, t = mo) {
    super(t, C(r, true));
  }
};
x(Ot, "UsageExceededError");
async function of(e10) {
  let r;
  try {
    r = await e10.text();
  } catch {
    return { type: "EmptyError" };
  }
  try {
    let t = JSON.parse(r);
    if (typeof t == "string") switch (t) {
      case "InternalDataProxyError":
        return { type: "DataProxyError", body: t };
      default:
        return { type: "UnknownTextError", body: t };
    }
    if (typeof t == "object" && t !== null) {
      if ("is_panic" in t && "message" in t && "error_code" in t) return { type: "QueryEngineError", body: t };
      if ("EngineNotStarted" in t || "InteractiveTransactionMisrouted" in t || "InvalidRequestError" in t) {
        let n = Object.values(t)[0].reason;
        return typeof n == "string" && !["SchemaMissing", "EngineVersionNotSupported"].includes(n) ? { type: "UnknownJsonError", body: t } : { type: "DataProxyError", body: t };
      }
    }
    return { type: "UnknownJsonError", body: t };
  } catch {
    return r === "" ? { type: "EmptyError" } : { type: "UnknownTextError", body: r };
  }
}
async function _t(e10, r) {
  if (e10.ok) return;
  let t = { clientVersion: r, response: e10 }, n = await of(e10);
  if (n.type === "QueryEngineError") throw new Z(n.body.message, { code: n.body.error_code, clientVersion: r });
  if (n.type === "DataProxyError") {
    if (n.body === "InternalDataProxyError") throw new jr(t, "Internal Data Proxy error");
    if ("EngineNotStarted" in n.body) {
      if (n.body.EngineNotStarted.reason === "SchemaMissing") return new cr(t);
      if (n.body.EngineNotStarted.reason === "EngineVersionNotSupported") throw new Rt(t);
      if ("EngineStartupError" in n.body.EngineNotStarted.reason) {
        let { msg: i, logs: o } = n.body.EngineNotStarted.reason.EngineStartupError;
        throw new St(t, i, o);
      }
      if ("KnownEngineStartupError" in n.body.EngineNotStarted.reason) {
        let { msg: i, error_code: o } = n.body.EngineNotStarted.reason.KnownEngineStartupError;
        throw new T(i, r, o);
      }
      if ("HealthcheckTimeout" in n.body.EngineNotStarted.reason) {
        let { logs: i } = n.body.EngineNotStarted.reason.HealthcheckTimeout;
        throw new Tt(t, i);
      }
    }
    if ("InteractiveTransactionMisrouted" in n.body) {
      let i = { IDParseError: "Could not parse interactive transaction ID", NoQueryEngineFoundError: "Could not find Query Engine for the specified host and transaction ID", TransactionStartError: "Could not start interactive transaction" };
      throw new At(t, i[n.body.InteractiveTransactionMisrouted.reason]);
    }
    if ("InvalidRequestError" in n.body) throw new It(t, n.body.InvalidRequestError.reason);
  }
  if (e10.status === 401 || e10.status === 403) throw new Dt(t, Vr(po, n));
  if (e10.status === 404) return new kt(t, Vr(uo, n));
  if (e10.status === 429) throw new Ot(t, Vr(mo, n));
  if (e10.status === 504) throw new Ct(t, Vr(lo, n));
  if (e10.status >= 500) throw new jr(t, Vr(co, n));
  if (e10.status >= 400) throw new Pt(t, Vr(ao, n));
}
function Vr(e10, r) {
  return r.type === "EmptyError" ? e10 : `${e10}: ${JSON.stringify(r)}`;
}
function hl(e10) {
  let r = Math.pow(2, e10) * 50, t = Math.ceil(Math.random() * r) - Math.ceil(r / 2), n = r + t;
  return new Promise((i) => setTimeout(() => i(n), n));
}
var $e = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
function yl(e10) {
  let r = new TextEncoder().encode(e10), t = "", n = r.byteLength, i = n % 3, o = n - i, s, a, l, u, c;
  for (let p = 0; p < o; p = p + 3) c = r[p] << 16 | r[p + 1] << 8 | r[p + 2], s = (c & 16515072) >> 18, a = (c & 258048) >> 12, l = (c & 4032) >> 6, u = c & 63, t += $e[s] + $e[a] + $e[l] + $e[u];
  return i == 1 ? (c = r[o], s = (c & 252) >> 2, a = (c & 3) << 4, t += $e[s] + $e[a] + "==") : i == 2 && (c = r[o] << 8 | r[o + 1], s = (c & 64512) >> 10, a = (c & 1008) >> 4, l = (c & 15) << 2, t += $e[s] + $e[a] + $e[l] + "="), t;
}
function bl(e10) {
  if (!!e10.generator?.previewFeatures.some((t) => t.toLowerCase().includes("metrics"))) throw new T("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate", e10.clientVersion);
}
var El = { "@prisma/debug": "workspace:*", "@prisma/engines-version": "6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc", "@prisma/fetch-engine": "workspace:*", "@prisma/get-platform": "workspace:*" };
var Nt = class extends oe {
  name = "RequestError";
  code = "P5010";
  constructor(r, t) {
    super(`Cannot fetch data from service:
${r}`, C(t, true));
  }
};
x(Nt, "RequestError");
async function pr(e10, r, t = (n) => n) {
  let { clientVersion: n, ...i } = r, o = t(fetch);
  try {
    return await o(e10, i);
  } catch (s) {
    let a = s.message ?? "Unknown error";
    throw new Nt(a, { clientVersion: n, cause: s });
  }
}
var af = /^[1-9][0-9]*\.[0-9]+\.[0-9]+$/;
var wl = L("prisma:client:dataproxyEngine");
async function lf(e10, r) {
  let t = El["@prisma/engines-version"], n = r.clientVersion ?? "unknown";
  if (process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION || globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION) return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION || globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;
  if (e10.includes("accelerate") && n !== "0.0.0" && n !== "in-memory") return n;
  let [i, o] = n?.split("-") ?? [];
  if (o === void 0 && af.test(i)) return i;
  if (o !== void 0 || n === "0.0.0" || n === "in-memory") {
    let [s] = t.split("-") ?? [], [a, l, u] = s.split("."), c = uf(`<=${a}.${l}.${u}`), p = await pr(c, { clientVersion: n });
    if (!p.ok) throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text() || "<empty body>"}`);
    let d = await p.text();
    wl("length of body fetched from unpkg.com", d.length);
    let f;
    try {
      f = JSON.parse(d);
    } catch (h) {
      throw console.error("JSON.parse error: body fetched from unpkg.com: ", d), h;
    }
    return f.version;
  }
  throw new ur("Only `major.minor.patch` versions are supported by Accelerate.", { clientVersion: n });
}
async function xl(e10, r) {
  let t = await lf(e10, r);
  return wl("version", t), t;
}
function uf(e10) {
  return encodeURI(`https://unpkg.com/prisma@${e10}/package.json`);
}
var vl = 3;
var Lt = L("prisma:client:dataproxyEngine");
var Ft = class {
  name = "DataProxyEngine";
  inlineSchema;
  inlineSchemaHash;
  inlineDatasources;
  config;
  logEmitter;
  env;
  clientVersion;
  engineHash;
  tracingHelper;
  remoteClientVersion;
  host;
  headerBuilder;
  startPromise;
  protocol;
  constructor(r) {
    bl(r), this.config = r, this.env = r.env, this.inlineSchema = yl(r.inlineSchema), this.inlineDatasources = r.inlineDatasources, this.inlineSchemaHash = r.inlineSchemaHash, this.clientVersion = r.clientVersion, this.engineHash = r.engineVersion, this.logEmitter = r.logEmitter, this.tracingHelper = r.tracingHelper;
  }
  apiKey() {
    return this.headerBuilder.apiKey;
  }
  version() {
    return this.engineHash;
  }
  async start() {
    this.startPromise !== void 0 && await this.startPromise, this.startPromise = (async () => {
      let { apiKey: r, url: t } = this.getURLAndAPIKey();
      this.host = t.host, this.protocol = t.protocol, this.headerBuilder = new Bn({ apiKey: r, tracingHelper: this.tracingHelper, logLevel: this.config.logLevel ?? "error", logQueries: this.config.logQueries, engineHash: this.engineHash }), this.remoteClientVersion = await xl(this.host, this.config), Lt("host", this.host), Lt("protocol", this.protocol);
    })(), await this.startPromise;
  }
  async stop() {
  }
  propagateResponseExtensions(r) {
    r?.logs?.length && r.logs.forEach((t) => {
      switch (t.level) {
        case "debug":
        case "trace":
          Lt(t);
          break;
        case "error":
        case "warn":
        case "info": {
          this.logEmitter.emit(t.level, { timestamp: so(t.timestamp), message: t.attributes.message ?? "", target: t.target });
          break;
        }
        case "query": {
          this.logEmitter.emit("query", { query: t.attributes.query ?? "", timestamp: so(t.timestamp), duration: t.attributes.duration_ms ?? 0, params: t.attributes.params ?? "", target: t.target });
          break;
        }
        default:
          t.level;
      }
    }), r?.traces?.length && this.tracingHelper.dispatchEngineSpans(r.traces);
  }
  onBeforeExit() {
    throw new Error('"beforeExit" hook is not applicable to the remote query engine');
  }
  async url(r) {
    return await this.start(), `${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${r}`;
  }
  async uploadSchema() {
    let r = { name: "schemaUpload", internal: true };
    return this.tracingHelper.runInChildSpan(r, async () => {
      let t = await pr(await this.url("schema"), { method: "PUT", headers: this.headerBuilder.build(), body: this.inlineSchema, clientVersion: this.clientVersion });
      t.ok || Lt("schema response status", t.status);
      let n = await _t(t, this.clientVersion);
      if (n) throw this.logEmitter.emit("warn", { message: `Error while uploading schema: ${n.message}`, timestamp: /* @__PURE__ */ new Date(), target: "" }), n;
      this.logEmitter.emit("info", { message: `Schema (re)uploaded (hash: ${this.inlineSchemaHash})`, timestamp: /* @__PURE__ */ new Date(), target: "" });
    });
  }
  request(r, { traceparent: t, interactiveTransaction: n, customDataProxyFetch: i }) {
    return this.requestInternal({ body: r, traceparent: t, interactiveTransaction: n, customDataProxyFetch: i });
  }
  async requestBatch(r, { traceparent: t, transaction: n, customDataProxyFetch: i }) {
    let o = n?.kind === "itx" ? n.options : void 0, s = Lr(r, n);
    return (await this.requestInternal({ body: s, customDataProxyFetch: i, interactiveTransaction: o, traceparent: t })).map((l) => (l.extensions && this.propagateResponseExtensions(l.extensions), "errors" in l ? this.convertProtocolErrorsToClientError(l.errors) : l));
  }
  requestInternal({ body: r, traceparent: t, customDataProxyFetch: n, interactiveTransaction: i }) {
    return this.withRetry({ actionGerund: "querying", callback: async ({ logHttpCall: o }) => {
      let s = i ? `${i.payload.endpoint}/graphql` : await this.url("graphql");
      o(s);
      let a = await pr(s, { method: "POST", headers: this.headerBuilder.build({ traceparent: t, transactionId: i?.id }), body: JSON.stringify(r), clientVersion: this.clientVersion }, n);
      a.ok || Lt("graphql response status", a.status), await this.handleError(await _t(a, this.clientVersion));
      let l = await a.json();
      if (l.extensions && this.propagateResponseExtensions(l.extensions), "errors" in l) throw this.convertProtocolErrorsToClientError(l.errors);
      return "batchResult" in l ? l.batchResult : l;
    } });
  }
  async transaction(r, t, n) {
    let i = { start: "starting", commit: "committing", rollback: "rolling back" };
    return this.withRetry({ actionGerund: `${i[r]} transaction`, callback: async ({ logHttpCall: o }) => {
      if (r === "start") {
        let s = JSON.stringify({ max_wait: n.maxWait, timeout: n.timeout, isolation_level: n.isolationLevel }), a = await this.url("transaction/start");
        o(a);
        let l = await pr(a, { method: "POST", headers: this.headerBuilder.build({ traceparent: t.traceparent }), body: s, clientVersion: this.clientVersion });
        await this.handleError(await _t(l, this.clientVersion));
        let u = await l.json(), { extensions: c } = u;
        c && this.propagateResponseExtensions(c);
        let p = u.id, d = u["data-proxy"].endpoint;
        return { id: p, payload: { endpoint: d } };
      } else {
        let s = `${n.payload.endpoint}/${r}`;
        o(s);
        let a = await pr(s, { method: "POST", headers: this.headerBuilder.build({ traceparent: t.traceparent }), clientVersion: this.clientVersion });
        await this.handleError(await _t(a, this.clientVersion));
        let l = await a.json(), { extensions: u } = l;
        u && this.propagateResponseExtensions(u);
        return;
      }
    } });
  }
  getURLAndAPIKey() {
    return fl({ clientVersion: this.clientVersion, env: this.env, inlineDatasources: this.inlineDatasources, overrideDatasources: this.config.overrideDatasources });
  }
  metrics() {
    throw new ur("Metrics are not yet supported for Accelerate", { clientVersion: this.clientVersion });
  }
  async withRetry(r) {
    for (let t = 0; ; t++) {
      let n = (i) => {
        this.logEmitter.emit("info", { message: `Calling ${i} (n=${t})`, timestamp: /* @__PURE__ */ new Date(), target: "" });
      };
      try {
        return await r.callback({ logHttpCall: n });
      } catch (i) {
        if (!(i instanceof oe) || !i.isRetryable) throw i;
        if (t >= vl) throw i instanceof qr ? i.cause : i;
        this.logEmitter.emit("warn", { message: `Attempt ${t + 1}/${vl} failed for ${r.actionGerund}: ${i.message ?? "(unknown)"}`, timestamp: /* @__PURE__ */ new Date(), target: "" });
        let o = await hl(t);
        this.logEmitter.emit("warn", { message: `Retrying after ${o}ms`, timestamp: /* @__PURE__ */ new Date(), target: "" });
      }
    }
  }
  async handleError(r) {
    if (r instanceof cr) throw await this.uploadSchema(), new qr({ clientVersion: this.clientVersion, cause: r });
    if (r) throw r;
  }
  convertProtocolErrorsToClientError(r) {
    return r.length === 1 ? Fr(r[0], this.config.clientVersion, this.config.activeProvider) : new q(JSON.stringify(r), { clientVersion: this.config.clientVersion });
  }
  applyPendingMigrations() {
    throw new Error("Method not implemented.");
  }
};
function Pl(e10) {
  if (e10?.kind === "itx") return e10.options.id;
}
var fo = Symbol("PrismaLibraryEngineCache");
function pf() {
  let e10 = globalThis;
  return e10[fo] === void 0 && (e10[fo] = {}), e10[fo];
}
function df(e10) {
  let r = pf();
  if (r[e10] !== void 0) return r[e10];
  let t = import_node_path6.default.toNamespacedPath(e10), n = { exports: {} }, i = 0;
  return process.platform !== "win32" && (i = import_node_os2.default.constants.dlopen.RTLD_LAZY | import_node_os2.default.constants.dlopen.RTLD_DEEPBIND), process.dlopen(n, t, i), r[e10] = n.exports, n.exports;
}
var Sl = { async loadLibrary(e10) {
  let r = await oi(), t = await ol("library", e10);
  try {
    return e10.tracingHelper.runInChildSpan({ name: "loadLibrary", internal: true }, () => df(t));
  } catch (n) {
    let i = hi({ e: n, platformInfo: r, id: t });
    throw new T(i, e10.clientVersion);
  }
} };
var go;
var Rl = { async loadLibrary(e10) {
  let { clientVersion: r, adapter: t, engineWasm: n } = e10;
  if (t === void 0) throw new T(`The \`adapter\` option for \`PrismaClient\` is required in this context (${to().prettyName})`, r);
  if (n === void 0) throw new T("WASM engine was unexpectedly `undefined`", r);
  go === void 0 && (go = (async () => {
    let o = await n.getRuntime(), s = await n.getQueryEngineWasmModule();
    if (s == null) throw new T("The loaded wasm module was unexpectedly `undefined` or `null` once loaded", r);
    let a = { "./query_engine_bg.js": o }, l = new WebAssembly.Instance(s, a), u = l.exports.__wbindgen_start;
    return o.__wbg_set_wasm(l.exports), u(), o.QueryEngine;
  })());
  let i = await go;
  return { debugPanic() {
    return Promise.reject("{}");
  }, dmmf() {
    return Promise.resolve("{}");
  }, version() {
    return { commit: "unknown", version: "unknown" };
  }, QueryEngine: i };
} };
var mf = "P2036";
var Re = L("prisma:client:libraryEngine");
function ff(e10) {
  return e10.item_type === "query" && "query" in e10;
}
function gf(e10) {
  return "level" in e10 ? e10.level === "error" && e10.message === "PANIC" : false;
}
var Cl = [...Xn, "native"];
var hf = 0xffffffffffffffffn;
var ho = 1n;
function yf() {
  let e10 = ho++;
  return ho > hf && (ho = 1n), e10;
}
var Br = class {
  name = "LibraryEngine";
  engine;
  libraryInstantiationPromise;
  libraryStartingPromise;
  libraryStoppingPromise;
  libraryStarted;
  executingQueryPromise;
  config;
  QueryEngineConstructor;
  libraryLoader;
  library;
  logEmitter;
  libQueryEnginePath;
  binaryTarget;
  datasourceOverrides;
  datamodel;
  logQueries;
  logLevel;
  lastQuery;
  loggerRustPanic;
  tracingHelper;
  adapterPromise;
  versionInfo;
  constructor(r, t) {
    this.libraryLoader = t ?? Sl, r.engineWasm !== void 0 && (this.libraryLoader = t ?? Rl), this.config = r, this.libraryStarted = false, this.logQueries = r.logQueries ?? false, this.logLevel = r.logLevel ?? "error", this.logEmitter = r.logEmitter, this.datamodel = r.inlineSchema, this.tracingHelper = r.tracingHelper, r.enableDebugLogs && (this.logLevel = "debug");
    let n = Object.keys(r.overrideDatasources)[0], i = r.overrideDatasources[n]?.url;
    n !== void 0 && i !== void 0 && (this.datasourceOverrides = { [n]: i }), this.libraryInstantiationPromise = this.instantiateLibrary();
  }
  wrapEngine(r) {
    return { applyPendingMigrations: r.applyPendingMigrations?.bind(r), commitTransaction: this.withRequestId(r.commitTransaction.bind(r)), connect: this.withRequestId(r.connect.bind(r)), disconnect: this.withRequestId(r.disconnect.bind(r)), metrics: r.metrics?.bind(r), query: this.withRequestId(r.query.bind(r)), rollbackTransaction: this.withRequestId(r.rollbackTransaction.bind(r)), sdlSchema: r.sdlSchema?.bind(r), startTransaction: this.withRequestId(r.startTransaction.bind(r)), trace: r.trace.bind(r), free: r.free?.bind(r) };
  }
  withRequestId(r) {
    return async (...t) => {
      let n = yf().toString();
      try {
        return await r(...t, n);
      } finally {
        if (this.tracingHelper.isEnabled()) {
          let i = await this.engine?.trace(n);
          if (i) {
            let o = JSON.parse(i);
            this.tracingHelper.dispatchEngineSpans(o.spans);
          }
        }
      }
    };
  }
  async applyPendingMigrations() {
    throw new Error("Cannot call this method from this type of engine instance");
  }
  async transaction(r, t, n) {
    await this.start();
    let i = await this.adapterPromise, o = JSON.stringify(t), s;
    if (r === "start") {
      let l = JSON.stringify({ max_wait: n.maxWait, timeout: n.timeout, isolation_level: n.isolationLevel });
      s = await this.engine?.startTransaction(l, o);
    } else r === "commit" ? s = await this.engine?.commitTransaction(n.id, o) : r === "rollback" && (s = await this.engine?.rollbackTransaction(n.id, o));
    let a = this.parseEngineResponse(s);
    if (bf(a)) {
      let l = this.getExternalAdapterError(a, i?.errorRegistry);
      throw l ? l.error : new Z(a.message, { code: a.error_code, clientVersion: this.config.clientVersion, meta: a.meta });
    } else if (typeof a.message == "string") throw new q(a.message, { clientVersion: this.config.clientVersion });
    return a;
  }
  async instantiateLibrary() {
    if (Re("internalSetup"), this.libraryInstantiationPromise) return this.libraryInstantiationPromise;
    Zn(), this.binaryTarget = await this.getCurrentBinaryTarget(), await this.tracingHelper.runInChildSpan("load_engine", () => this.loadEngine()), this.version();
  }
  async getCurrentBinaryTarget() {
    {
      if (this.binaryTarget) return this.binaryTarget;
      let r = await this.tracingHelper.runInChildSpan("detect_platform", () => nr());
      if (!Cl.includes(r)) throw new T(`Unknown ${ue("PRISMA_QUERY_ENGINE_LIBRARY")} ${ue(Q(r))}. Possible binaryTargets: ${qe(Cl.join(", "))} or a path to the query engine library.
You may have to run ${qe("prisma generate")} for your changes to take effect.`, this.config.clientVersion);
      return r;
    }
  }
  parseEngineResponse(r) {
    if (!r) throw new q("Response from the Engine was empty", { clientVersion: this.config.clientVersion });
    try {
      return JSON.parse(r);
    } catch {
      throw new q("Unable to JSON.parse response from engine", { clientVersion: this.config.clientVersion });
    }
  }
  async loadEngine() {
    if (!this.engine) {
      this.QueryEngineConstructor || (this.library = await this.libraryLoader.loadLibrary(this.config), this.QueryEngineConstructor = this.library.QueryEngine);
      try {
        let r = new WeakRef(this);
        this.adapterPromise || (this.adapterPromise = this.config.adapter?.connect()?.then(jn));
        let t = await this.adapterPromise;
        t && Re("Using driver adapter: %O", t), this.engine = this.wrapEngine(new this.QueryEngineConstructor({ datamodel: this.datamodel, env: process.env, logQueries: this.config.logQueries ?? false, ignoreEnvVarErrors: true, datasourceOverrides: this.datasourceOverrides ?? {}, logLevel: this.logLevel, configDir: this.config.cwd, engineProtocol: "json", enableTracing: this.tracingHelper.isEnabled() }, (n) => {
          r.deref()?.logger(n);
        }, t));
      } catch (r) {
        let t = r, n = this.parseInitError(t.message);
        throw typeof n == "string" ? t : new T(n.message, this.config.clientVersion, n.error_code);
      }
    }
  }
  logger(r) {
    let t = this.parseEngineResponse(r);
    t && (t.level = t?.level.toLowerCase() ?? "unknown", ff(t) ? this.logEmitter.emit("query", { timestamp: /* @__PURE__ */ new Date(), query: t.query, params: t.params, duration: Number(t.duration_ms), target: t.module_path }) : gf(t) ? this.loggerRustPanic = new de(yo(this, `${t.message}: ${t.reason} in ${t.file}:${t.line}:${t.column}`), this.config.clientVersion) : this.logEmitter.emit(t.level, { timestamp: /* @__PURE__ */ new Date(), message: t.message, target: t.module_path }));
  }
  parseInitError(r) {
    try {
      return JSON.parse(r);
    } catch {
    }
    return r;
  }
  parseRequestError(r) {
    try {
      return JSON.parse(r);
    } catch {
    }
    return r;
  }
  onBeforeExit() {
    throw new Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.');
  }
  async start() {
    if (this.libraryInstantiationPromise || (this.libraryInstantiationPromise = this.instantiateLibrary()), await this.libraryInstantiationPromise, await this.libraryStoppingPromise, this.libraryStartingPromise) return Re(`library already starting, this.libraryStarted: ${this.libraryStarted}`), this.libraryStartingPromise;
    if (this.libraryStarted) return;
    let r = async () => {
      Re("library starting");
      try {
        let t = { traceparent: this.tracingHelper.getTraceParent() };
        await this.engine?.connect(JSON.stringify(t)), this.libraryStarted = true, this.adapterPromise || (this.adapterPromise = this.config.adapter?.connect()?.then(jn)), await this.adapterPromise, Re("library started");
      } catch (t) {
        let n = this.parseInitError(t.message);
        throw typeof n == "string" ? t : new T(n.message, this.config.clientVersion, n.error_code);
      } finally {
        this.libraryStartingPromise = void 0;
      }
    };
    return this.libraryStartingPromise = this.tracingHelper.runInChildSpan("connect", r), this.libraryStartingPromise;
  }
  async stop() {
    if (await this.libraryInstantiationPromise, await this.libraryStartingPromise, await this.executingQueryPromise, this.libraryStoppingPromise) return Re("library is already stopping"), this.libraryStoppingPromise;
    if (!this.libraryStarted) {
      await (await this.adapterPromise)?.dispose(), this.adapterPromise = void 0;
      return;
    }
    let r = async () => {
      await new Promise((n) => setImmediate(n)), Re("library stopping");
      let t = { traceparent: this.tracingHelper.getTraceParent() };
      await this.engine?.disconnect(JSON.stringify(t)), this.engine?.free && this.engine.free(), this.engine = void 0, this.libraryStarted = false, this.libraryStoppingPromise = void 0, this.libraryInstantiationPromise = void 0, await (await this.adapterPromise)?.dispose(), this.adapterPromise = void 0, Re("library stopped");
    };
    return this.libraryStoppingPromise = this.tracingHelper.runInChildSpan("disconnect", r), this.libraryStoppingPromise;
  }
  version() {
    return this.versionInfo = this.library?.version(), this.versionInfo?.version ?? "unknown";
  }
  debugPanic(r) {
    return this.library?.debugPanic(r);
  }
  async request(r, { traceparent: t, interactiveTransaction: n }) {
    Re(`sending request, this.libraryStarted: ${this.libraryStarted}`);
    let i = JSON.stringify({ traceparent: t }), o = JSON.stringify(r);
    try {
      await this.start();
      let s = await this.adapterPromise;
      this.executingQueryPromise = this.engine?.query(o, i, n?.id), this.lastQuery = o;
      let a = this.parseEngineResponse(await this.executingQueryPromise);
      if (a.errors) throw a.errors.length === 1 ? this.buildQueryError(a.errors[0], s?.errorRegistry) : new q(JSON.stringify(a.errors), { clientVersion: this.config.clientVersion });
      if (this.loggerRustPanic) throw this.loggerRustPanic;
      return { data: a };
    } catch (s) {
      if (s instanceof T) throw s;
      if (s.code === "GenericFailure" && s.message?.startsWith("PANIC:")) throw new de(yo(this, s.message), this.config.clientVersion);
      let a = this.parseRequestError(s.message);
      throw typeof a == "string" ? s : new q(`${a.message}
${a.backtrace}`, { clientVersion: this.config.clientVersion });
    }
  }
  async requestBatch(r, { transaction: t, traceparent: n }) {
    Re("requestBatch");
    let i = Lr(r, t);
    await this.start();
    let o = await this.adapterPromise;
    this.lastQuery = JSON.stringify(i), this.executingQueryPromise = this.engine?.query(this.lastQuery, JSON.stringify({ traceparent: n }), Pl(t));
    let s = await this.executingQueryPromise, a = this.parseEngineResponse(s);
    if (a.errors) throw a.errors.length === 1 ? this.buildQueryError(a.errors[0], o?.errorRegistry) : new q(JSON.stringify(a.errors), { clientVersion: this.config.clientVersion });
    let { batchResult: l, errors: u } = a;
    if (Array.isArray(l)) return l.map((c) => c.errors && c.errors.length > 0 ? this.loggerRustPanic ?? this.buildQueryError(c.errors[0], o?.errorRegistry) : { data: c });
    throw u && u.length === 1 ? new Error(u[0].error) : new Error(JSON.stringify(a));
  }
  buildQueryError(r, t) {
    if (r.user_facing_error.is_panic) return new de(yo(this, r.user_facing_error.message), this.config.clientVersion);
    let n = this.getExternalAdapterError(r.user_facing_error, t);
    return n ? n.error : Fr(r, this.config.clientVersion, this.config.activeProvider);
  }
  getExternalAdapterError(r, t) {
    if (r.error_code === mf && t) {
      let n = r.meta?.id;
      Xt(typeof n == "number", "Malformed external JS error received from the engine");
      let i = t.consumeError(n);
      return Xt(i, "External error with reported id was not registered"), i;
    }
  }
  async metrics(r) {
    await this.start();
    let t = await this.engine.metrics(JSON.stringify(r));
    return r.format === "prometheus" ? t : this.parseEngineResponse(t);
  }
};
function bf(e10) {
  return typeof e10 == "object" && e10 !== null && e10.error_code !== void 0;
}
function yo(e10, r) {
  return pl({ binaryTarget: e10.binaryTarget, title: r, version: e10.config.clientVersion, engineVersion: e10.versionInfo?.commit, database: e10.config.activeProvider, query: e10.lastQuery });
}
function Al({ url: e10, adapter: r, copyEngine: t, targetBuildType: n }) {
  let i = [], o = [], s = (g) => {
    i.push({ _tag: "warning", value: g });
  }, a = (g) => {
    let S = g.join(`
`);
    o.push({ _tag: "error", value: S });
  }, l = !!e10?.startsWith("prisma://"), u = Zt(e10), c = !!r, p = l || u;
  !c && t && p && s(["recommend--no-engine", "In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);
  let d = p || !t;
  c && (d || n === "edge") && (n === "edge" ? a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.", "Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]) : t ? l && a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.", "Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]) : a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.", "Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));
  let f = { accelerate: d, ppg: u, driverAdapters: c };
  function h(g) {
    return g.length > 0;
  }
  return h(o) ? { ok: false, diagnostics: { warnings: i, errors: o }, isUsing: f } : { ok: true, diagnostics: { warnings: i }, isUsing: f };
}
function Il({ copyEngine: e10 = true }, r) {
  let t;
  try {
    t = $r({ inlineDatasources: r.inlineDatasources, overrideDatasources: r.overrideDatasources, env: { ...r.env, ...process.env }, clientVersion: r.clientVersion });
  } catch {
  }
  let { ok: n, isUsing: i, diagnostics: o } = Al({ url: t, adapter: r.adapter, copyEngine: e10, targetBuildType: "library" });
  for (let p of o.warnings) tn(...p.value);
  if (!n) {
    let p = o.errors[0];
    throw new X(p.value, { clientVersion: r.clientVersion });
  }
  let s = wr(r.generator), a = s === "library", l = s === "binary", u = s === "client", c = (i.accelerate || i.ppg) && !i.driverAdapters;
  return i.accelerate ? new Ft(r) : (i.driverAdapters, a ? new Br(r) : (i.accelerate, new Br(r)));
}
function Un({ generator: e10 }) {
  return e10?.previewFeatures ?? [];
}
var kl = (e10) => ({ command: e10 });
var Dl = (e10) => e10.strings.reduce((r, t, n) => `${r}@P${n}${t}`);
function Ur(e10) {
  try {
    return Ol(e10, "fast");
  } catch {
    return Ol(e10, "slow");
  }
}
function Ol(e10, r) {
  return JSON.stringify(e10.map((t) => Nl(t, r)));
}
function Nl(e10, r) {
  if (Array.isArray(e10)) return e10.map((t) => Nl(t, r));
  if (typeof e10 == "bigint") return { prisma__type: "bigint", prisma__value: e10.toString() };
  if (Tr(e10)) return { prisma__type: "date", prisma__value: e10.toJSON() };
  if (Le.isDecimal(e10)) return { prisma__type: "decimal", prisma__value: e10.toJSON() };
  if (Buffer.isBuffer(e10)) return { prisma__type: "bytes", prisma__value: e10.toString("base64") };
  if (Ef(e10)) return { prisma__type: "bytes", prisma__value: Buffer.from(e10).toString("base64") };
  if (ArrayBuffer.isView(e10)) {
    let { buffer: t, byteOffset: n, byteLength: i } = e10;
    return { prisma__type: "bytes", prisma__value: Buffer.from(t, n, i).toString("base64") };
  }
  return typeof e10 == "object" && r === "slow" ? Ll(e10) : e10;
}
function Ef(e10) {
  return e10 instanceof ArrayBuffer || e10 instanceof SharedArrayBuffer ? true : typeof e10 == "object" && e10 !== null ? e10[Symbol.toStringTag] === "ArrayBuffer" || e10[Symbol.toStringTag] === "SharedArrayBuffer" : false;
}
function Ll(e10) {
  if (typeof e10 != "object" || e10 === null) return e10;
  if (typeof e10.toJSON == "function") return e10.toJSON();
  if (Array.isArray(e10)) return e10.map(_l);
  let r = {};
  for (let t of Object.keys(e10)) r[t] = _l(e10[t]);
  return r;
}
function _l(e10) {
  return typeof e10 == "bigint" ? e10.toString() : Ll(e10);
}
var wf = /^(\s*alter\s)/i;
var Fl = L("prisma:client");
function bo(e10, r, t, n) {
  if (!(e10 !== "postgresql" && e10 !== "cockroachdb") && t.length > 0 && wf.exec(r)) throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`);
}
var Eo = ({ clientMethod: e10, activeProvider: r }) => (t) => {
  let n = "", i;
  if (kn(t)) n = t.sql, i = { values: Ur(t.values), __prismaRawParameters__: true };
  else if (Array.isArray(t)) {
    let [o, ...s] = t;
    n = o, i = { values: Ur(s || []), __prismaRawParameters__: true };
  } else switch (r) {
    case "sqlite":
    case "mysql": {
      n = t.sql, i = { values: Ur(t.values), __prismaRawParameters__: true };
      break;
    }
    case "cockroachdb":
    case "postgresql":
    case "postgres": {
      n = t.text, i = { values: Ur(t.values), __prismaRawParameters__: true };
      break;
    }
    case "sqlserver": {
      n = Dl(t), i = { values: Ur(t.values), __prismaRawParameters__: true };
      break;
    }
    default:
      throw new Error(`The ${r} provider does not support ${e10}`);
  }
  return i?.values ? Fl(`prisma.${e10}(${n}, ${i.values})`) : Fl(`prisma.${e10}(${n})`), { query: n, parameters: i };
};
var Ml = { requestArgsToMiddlewareArgs(e10) {
  return [e10.strings, ...e10.values];
}, middlewareArgsToRequestArgs(e10) {
  let [r, ...t] = e10;
  return new le(r, t);
} };
var $l = { requestArgsToMiddlewareArgs(e10) {
  return [e10];
}, middlewareArgsToRequestArgs(e10) {
  return e10[0];
} };
function wo(e10) {
  return function(t, n) {
    let i, o = (s = e10) => {
      try {
        return s === void 0 || s?.kind === "itx" ? i ??= ql(t(s)) : ql(t(s));
      } catch (a) {
        return Promise.reject(a);
      }
    };
    return { get spec() {
      return n;
    }, then(s, a) {
      return o().then(s, a);
    }, catch(s) {
      return o().catch(s);
    }, finally(s) {
      return o().finally(s);
    }, requestTransaction(s) {
      let a = o(s);
      return a.requestTransaction ? a.requestTransaction(s) : a;
    }, [Symbol.toStringTag]: "PrismaPromise" };
  };
}
function ql(e10) {
  return typeof e10.then == "function" ? e10 : Promise.resolve(e10);
}
var xf = mi.split(".")[0];
var vf = { isEnabled() {
  return false;
}, getTraceParent() {
  return "00-10-10-00";
}, dispatchEngineSpans() {
}, getActiveContext() {
}, runInChildSpan(e10, r) {
  return r();
} };
var xo = class {
  isEnabled() {
    return this.getGlobalTracingHelper().isEnabled();
  }
  getTraceParent(r) {
    return this.getGlobalTracingHelper().getTraceParent(r);
  }
  dispatchEngineSpans(r) {
    return this.getGlobalTracingHelper().dispatchEngineSpans(r);
  }
  getActiveContext() {
    return this.getGlobalTracingHelper().getActiveContext();
  }
  runInChildSpan(r, t) {
    return this.getGlobalTracingHelper().runInChildSpan(r, t);
  }
  getGlobalTracingHelper() {
    let r = globalThis[`V${xf}_PRISMA_INSTRUMENTATION`], t = globalThis.PRISMA_INSTRUMENTATION;
    return r?.helper ?? t?.helper ?? vf;
  }
};
function jl() {
  return new xo();
}
function Vl(e10, r = () => {
}) {
  let t, n = new Promise((i) => t = i);
  return { then(i) {
    return --e10 === 0 && t(r()), i?.(n);
  } };
}
function Bl(e10) {
  return typeof e10 == "string" ? e10 : e10.reduce((r, t) => {
    let n = typeof t == "string" ? t : t.level;
    return n === "query" ? r : r && (t === "info" || r === "info") ? "info" : n;
  }, void 0);
}
var Gn = class {
  _middlewares = [];
  use(r) {
    this._middlewares.push(r);
  }
  get(r) {
    return this._middlewares[r];
  }
  has(r) {
    return !!this._middlewares[r];
  }
  length() {
    return this._middlewares.length;
  }
};
var Gl = ne(Pi());
function Qn(e10) {
  return typeof e10.batchRequestIdx == "number";
}
function Ul(e10) {
  if (e10.action !== "findUnique" && e10.action !== "findUniqueOrThrow") return;
  let r = [];
  return e10.modelName && r.push(e10.modelName), e10.query.arguments && r.push(vo(e10.query.arguments)), r.push(vo(e10.query.selection)), r.join("");
}
function vo(e10) {
  return `(${Object.keys(e10).sort().map((t) => {
    let n = e10[t];
    return typeof n == "object" && n !== null ? `(${t} ${vo(n)})` : t;
  }).join(" ")})`;
}
var Pf = { aggregate: false, aggregateRaw: false, createMany: true, createManyAndReturn: true, createOne: true, deleteMany: true, deleteOne: true, executeRaw: true, findFirst: false, findFirstOrThrow: false, findMany: false, findRaw: false, findUnique: false, findUniqueOrThrow: false, groupBy: false, queryRaw: false, runCommandRaw: true, updateMany: true, updateManyAndReturn: true, updateOne: true, upsertOne: true };
function Po(e10) {
  return Pf[e10];
}
var Wn = class {
  constructor(r) {
    this.options = r;
    this.batches = {};
  }
  batches;
  tickActive = false;
  request(r) {
    let t = this.options.batchBy(r);
    return t ? (this.batches[t] || (this.batches[t] = [], this.tickActive || (this.tickActive = true, process.nextTick(() => {
      this.dispatchBatches(), this.tickActive = false;
    }))), new Promise((n, i) => {
      this.batches[t].push({ request: r, resolve: n, reject: i });
    })) : this.options.singleLoader(r);
  }
  dispatchBatches() {
    for (let r in this.batches) {
      let t = this.batches[r];
      delete this.batches[r], t.length === 1 ? this.options.singleLoader(t[0].request).then((n) => {
        n instanceof Error ? t[0].reject(n) : t[0].resolve(n);
      }).catch((n) => {
        t[0].reject(n);
      }) : (t.sort((n, i) => this.options.batchOrder(n.request, i.request)), this.options.batchLoader(t.map((n) => n.request)).then((n) => {
        if (n instanceof Error) for (let i = 0; i < t.length; i++) t[i].reject(n);
        else for (let i = 0; i < t.length; i++) {
          let o = n[i];
          o instanceof Error ? t[i].reject(o) : t[i].resolve(o);
        }
      }).catch((n) => {
        for (let i = 0; i < t.length; i++) t[i].reject(n);
      }));
    }
  }
  get [Symbol.toStringTag]() {
    return "DataLoader";
  }
};
function dr(e10, r) {
  if (r === null) return r;
  switch (e10) {
    case "bigint":
      return BigInt(r);
    case "bytes": {
      let { buffer: t, byteOffset: n, byteLength: i } = Buffer.from(r, "base64");
      return new Uint8Array(t, n, i);
    }
    case "decimal":
      return new Le(r);
    case "datetime":
    case "date":
      return new Date(r);
    case "time":
      return /* @__PURE__ */ new Date(`1970-01-01T${r}Z`);
    case "bigint-array":
      return r.map((t) => dr("bigint", t));
    case "bytes-array":
      return r.map((t) => dr("bytes", t));
    case "decimal-array":
      return r.map((t) => dr("decimal", t));
    case "datetime-array":
      return r.map((t) => dr("datetime", t));
    case "date-array":
      return r.map((t) => dr("date", t));
    case "time-array":
      return r.map((t) => dr("time", t));
    default:
      return r;
  }
}
function To(e10) {
  let r = [], t = Tf(e10);
  for (let n = 0; n < e10.rows.length; n++) {
    let i = e10.rows[n], o = { ...t };
    for (let s = 0; s < i.length; s++) o[e10.columns[s]] = dr(e10.types[s], i[s]);
    r.push(o);
  }
  return r;
}
function Tf(e10) {
  let r = {};
  for (let t = 0; t < e10.columns.length; t++) r[e10.columns[t]] = null;
  return r;
}
var Sf = L("prisma:client:request_handler");
var Jn = class {
  client;
  dataloader;
  logEmitter;
  constructor(r, t) {
    this.logEmitter = t, this.client = r, this.dataloader = new Wn({ batchLoader: Ha(async ({ requests: n, customDataProxyFetch: i }) => {
      let { transaction: o, otelParentCtx: s } = n[0], a = n.map((p) => p.protocolQuery), l = this.client._tracingHelper.getTraceParent(s), u = n.some((p) => Po(p.protocolQuery.action));
      return (await this.client._engine.requestBatch(a, { traceparent: l, transaction: Rf(o), containsWrite: u, customDataProxyFetch: i })).map((p, d) => {
        if (p instanceof Error) return p;
        try {
          return this.mapQueryEngineResult(n[d], p);
        } catch (f) {
          return f;
        }
      });
    }), singleLoader: async (n) => {
      let i = n.transaction?.kind === "itx" ? Ql(n.transaction) : void 0, o = await this.client._engine.request(n.protocolQuery, { traceparent: this.client._tracingHelper.getTraceParent(), interactiveTransaction: i, isWrite: Po(n.protocolQuery.action), customDataProxyFetch: n.customDataProxyFetch });
      return this.mapQueryEngineResult(n, o);
    }, batchBy: (n) => n.transaction?.id ? `transaction-${n.transaction.id}` : Ul(n.protocolQuery), batchOrder(n, i) {
      return n.transaction?.kind === "batch" && i.transaction?.kind === "batch" ? n.transaction.index - i.transaction.index : 0;
    } });
  }
  async request(r) {
    try {
      return await this.dataloader.request(r);
    } catch (t) {
      let { clientMethod: n, callsite: i, transaction: o, args: s, modelName: a } = r;
      this.handleAndLogRequestError({ error: t, clientMethod: n, callsite: i, transaction: o, args: s, modelName: a, globalOmit: r.globalOmit });
    }
  }
  mapQueryEngineResult({ dataPath: r, unpacker: t }, n) {
    let i = n?.data, o = this.unpack(i, r, t);
    return process.env.PRISMA_CLIENT_GET_TIME ? { data: o } : o;
  }
  handleAndLogRequestError(r) {
    try {
      this.handleRequestError(r);
    } catch (t) {
      throw this.logEmitter && this.logEmitter.emit("error", { message: t.message, target: r.clientMethod, timestamp: /* @__PURE__ */ new Date() }), t;
    }
  }
  handleRequestError({ error: r, clientMethod: t, callsite: n, transaction: i, args: o, modelName: s, globalOmit: a }) {
    if (Sf(r), Cf(r, i)) throw r;
    if (r instanceof Z && Af(r)) {
      let u = Wl(r.meta);
      Rn({ args: o, errors: [u], callsite: n, errorFormat: this.client._errorFormat, originalMethod: t, clientVersion: this.client._clientVersion, globalOmit: a });
    }
    let l = r.message;
    if (n && (l = yn({ callsite: n, originalMethod: t, isPanic: r.isPanic, showColors: this.client._errorFormat === "pretty", message: l })), l = this.sanitizeMessage(l), r.code) {
      let u = s ? { modelName: s, ...r.meta } : r.meta;
      throw new Z(l, { code: r.code, clientVersion: this.client._clientVersion, meta: u, batchRequestIdx: r.batchRequestIdx });
    } else {
      if (r.isPanic) throw new de(l, this.client._clientVersion);
      if (r instanceof q) throw new q(l, { clientVersion: this.client._clientVersion, batchRequestIdx: r.batchRequestIdx });
      if (r instanceof T) throw new T(l, this.client._clientVersion);
      if (r instanceof de) throw new de(l, this.client._clientVersion);
    }
    throw r.clientVersion = this.client._clientVersion, r;
  }
  sanitizeMessage(r) {
    return this.client._errorFormat && this.client._errorFormat !== "pretty" ? (0, Gl.default)(r) : r;
  }
  unpack(r, t, n) {
    if (!r || (r.data && (r = r.data), !r)) return r;
    let i = Object.keys(r)[0], o = Object.values(r)[0], s = t.filter((u) => u !== "select" && u !== "include"), a = Zi(o, s), l = i === "queryRaw" ? To(a) : ot(a);
    return n ? n(l) : l;
  }
  get [Symbol.toStringTag]() {
    return "RequestHandler";
  }
};
function Rf(e10) {
  if (e10) {
    if (e10.kind === "batch") return { kind: "batch", options: { isolationLevel: e10.isolationLevel } };
    if (e10.kind === "itx") return { kind: "itx", options: Ql(e10) };
    Oe(e10, "Unknown transaction kind");
  }
}
function Ql(e10) {
  return { id: e10.id, payload: e10.payload };
}
function Cf(e10, r) {
  return Qn(e10) && r?.kind === "batch" && e10.batchRequestIdx !== r.index;
}
function Af(e10) {
  return e10.code === "P2009" || e10.code === "P2012";
}
function Wl(e10) {
  if (e10.kind === "Union") return { kind: "Union", errors: e10.errors.map(Wl) };
  if (Array.isArray(e10.selectionPath)) {
    let [, ...r] = e10.selectionPath;
    return { ...e10, selectionPath: r };
  }
  return e10;
}
var Jl = ml;
var Zl = ne($i());
var D = class extends Error {
  constructor(r) {
    super(r + `
Read more at https://pris.ly/d/client-constructor`), this.name = "PrismaClientConstructorValidationError";
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientConstructorValidationError";
  }
};
x(D, "PrismaClientConstructorValidationError");
var Hl = ["datasources", "datasourceUrl", "errorFormat", "adapter", "log", "transactionOptions", "omit", "__internal"];
var Kl = ["pretty", "colorless", "minimal"];
var Yl = ["info", "query", "warn", "error"];
var If = { datasources: (e10, { datasourceNames: r }) => {
  if (e10) {
    if (typeof e10 != "object" || Array.isArray(e10)) throw new D(`Invalid value ${JSON.stringify(e10)} for "datasources" provided to PrismaClient constructor`);
    for (let [t, n] of Object.entries(e10)) {
      if (!r.includes(t)) {
        let i = Gr(t, r) || ` Available datasources: ${r.join(", ")}`;
        throw new D(`Unknown datasource ${t} provided to PrismaClient constructor.${i}`);
      }
      if (typeof n != "object" || Array.isArray(n)) throw new D(`Invalid value ${JSON.stringify(e10)} for datasource "${t}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);
      if (n && typeof n == "object") for (let [i, o] of Object.entries(n)) {
        if (i !== "url") throw new D(`Invalid value ${JSON.stringify(e10)} for datasource "${t}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);
        if (typeof o != "string") throw new D(`Invalid value ${JSON.stringify(o)} for datasource "${t}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);
      }
    }
  }
}, adapter: (e10, r) => {
  if (!e10 && wr(r.generator) === "client") throw new D('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');
  if (e10 === null) return;
  if (e10 === void 0) throw new D('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');
  if (!Un(r).includes("driverAdapters")) throw new D('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');
  if (wr(r.generator) === "binary") throw new D('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.');
}, datasourceUrl: (e10) => {
  if (typeof e10 < "u" && typeof e10 != "string") throw new D(`Invalid value ${JSON.stringify(e10)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`);
}, errorFormat: (e10) => {
  if (e10) {
    if (typeof e10 != "string") throw new D(`Invalid value ${JSON.stringify(e10)} for "errorFormat" provided to PrismaClient constructor.`);
    if (!Kl.includes(e10)) {
      let r = Gr(e10, Kl);
      throw new D(`Invalid errorFormat ${e10} provided to PrismaClient constructor.${r}`);
    }
  }
}, log: (e10) => {
  if (!e10) return;
  if (!Array.isArray(e10)) throw new D(`Invalid value ${JSON.stringify(e10)} for "log" provided to PrismaClient constructor.`);
  function r(t) {
    if (typeof t == "string" && !Yl.includes(t)) {
      let n = Gr(t, Yl);
      throw new D(`Invalid log level "${t}" provided to PrismaClient constructor.${n}`);
    }
  }
  for (let t of e10) {
    r(t);
    let n = { level: r, emit: (i) => {
      let o = ["stdout", "event"];
      if (!o.includes(i)) {
        let s = Gr(i, o);
        throw new D(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`);
      }
    } };
    if (t && typeof t == "object") for (let [i, o] of Object.entries(t)) if (n[i]) n[i](o);
    else throw new D(`Invalid property ${i} for "log" provided to PrismaClient constructor`);
  }
}, transactionOptions: (e10) => {
  if (!e10) return;
  let r = e10.maxWait;
  if (r != null && r <= 0) throw new D(`Invalid value ${r} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);
  let t = e10.timeout;
  if (t != null && t <= 0) throw new D(`Invalid value ${t} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`);
}, omit: (e10, r) => {
  if (typeof e10 != "object") throw new D('"omit" option is expected to be an object.');
  if (e10 === null) throw new D('"omit" option can not be `null`');
  let t = [];
  for (let [n, i] of Object.entries(e10)) {
    let o = Df(n, r.runtimeDataModel);
    if (!o) {
      t.push({ kind: "UnknownModel", modelKey: n });
      continue;
    }
    for (let [s, a] of Object.entries(i)) {
      let l = o.fields.find((u) => u.name === s);
      if (!l) {
        t.push({ kind: "UnknownField", modelKey: n, fieldName: s });
        continue;
      }
      if (l.relationName) {
        t.push({ kind: "RelationInOmit", modelKey: n, fieldName: s });
        continue;
      }
      typeof a != "boolean" && t.push({ kind: "InvalidFieldValue", modelKey: n, fieldName: s });
    }
  }
  if (t.length > 0) throw new D(Of(e10, t));
}, __internal: (e10) => {
  if (!e10) return;
  let r = ["debug", "engine", "configOverride"];
  if (typeof e10 != "object") throw new D(`Invalid value ${JSON.stringify(e10)} for "__internal" to PrismaClient constructor`);
  for (let [t] of Object.entries(e10)) if (!r.includes(t)) {
    let n = Gr(t, r);
    throw new D(`Invalid property ${JSON.stringify(t)} for "__internal" provided to PrismaClient constructor.${n}`);
  }
} };
function Xl(e10, r) {
  for (let [t, n] of Object.entries(e10)) {
    if (!Hl.includes(t)) {
      let i = Gr(t, Hl);
      throw new D(`Unknown property ${t} provided to PrismaClient constructor.${i}`);
    }
    If[t](n, r);
  }
  if (e10.datasourceUrl && e10.datasources) throw new D('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them');
}
function Gr(e10, r) {
  if (r.length === 0 || typeof e10 != "string") return "";
  let t = kf(e10, r);
  return t ? ` Did you mean "${t}"?` : "";
}
function kf(e10, r) {
  if (r.length === 0) return null;
  let t = r.map((i) => ({ value: i, distance: (0, Zl.default)(e10, i) }));
  t.sort((i, o) => i.distance < o.distance ? -1 : 1);
  let n = t[0];
  return n.distance < 3 ? n.value : null;
}
function Df(e10, r) {
  return zl(r.models, e10) ?? zl(r.types, e10);
}
function zl(e10, r) {
  let t = Object.keys(e10).find((n) => Ye(n) === r);
  if (t) return e10[t];
}
function Of(e10, r) {
  let t = Or(e10);
  for (let o of r) switch (o.kind) {
    case "UnknownModel":
      t.arguments.getField(o.modelKey)?.markAsError(), t.addErrorMessage(() => `Unknown model name: ${o.modelKey}.`);
      break;
    case "UnknownField":
      t.arguments.getDeepField([o.modelKey, o.fieldName])?.markAsError(), t.addErrorMessage(() => `Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);
      break;
    case "RelationInOmit":
      t.arguments.getDeepField([o.modelKey, o.fieldName])?.markAsError(), t.addErrorMessage(() => 'Relations are already excluded by default and can not be specified in "omit".');
      break;
    case "InvalidFieldValue":
      t.arguments.getDeepFieldValue([o.modelKey, o.fieldName])?.markAsError(), t.addErrorMessage(() => "Omit field option value must be a boolean.");
      break;
  }
  let { message: n, args: i } = Sn(t, "colorless");
  return `Error validating "omit" option:

${i}

${n}`;
}
function eu(e10) {
  return e10.length === 0 ? Promise.resolve([]) : new Promise((r, t) => {
    let n = new Array(e10.length), i = null, o = false, s = 0, a = () => {
      o || (s++, s === e10.length && (o = true, i ? t(i) : r(n)));
    }, l = (u) => {
      o || (o = true, t(u));
    };
    for (let u = 0; u < e10.length; u++) e10[u].then((c) => {
      n[u] = c, a();
    }, (c) => {
      if (!Qn(c)) {
        l(c);
        return;
      }
      c.batchRequestIdx === u ? l(c) : (i || (i = c), a());
    });
  });
}
var rr = L("prisma:client");
typeof globalThis == "object" && (globalThis.NODE_CLIENT = true);
var Ff = { requestArgsToMiddlewareArgs: (e10) => e10, middlewareArgsToRequestArgs: (e10) => e10 };
var Mf = Symbol.for("prisma.client.transaction.id");
var $f = { id: 0, nextId() {
  return ++this.id;
} };
function qf(e10) {
  class r {
    _originalClient = this;
    _runtimeDataModel;
    _requestHandler;
    _connectionPromise;
    _disconnectionPromise;
    _engineConfig;
    _accelerateEngineConfig;
    _clientVersion;
    _errorFormat;
    _tracingHelper;
    _middlewares = new Gn();
    _previewFeatures;
    _activeProvider;
    _globalOmit;
    _extensions;
    _engine;
    _appliedParent;
    _createPrismaPromise = wo();
    constructor(n) {
      e10 = n?.__internal?.configOverride?.(e10) ?? e10, Xa(e10), n && Xl(n, e10);
      let i = new import_node_events.EventEmitter().on("error", () => {
      });
      this._extensions = _r.empty(), this._previewFeatures = Un(e10), this._clientVersion = e10.clientVersion ?? Jl, this._activeProvider = e10.activeProvider, this._globalOmit = n?.omit, this._tracingHelper = jl();
      let o = e10.relativeEnvPaths && { rootEnvPath: e10.relativeEnvPaths.rootEnvPath && import_node_path4.default.resolve(e10.dirname, e10.relativeEnvPaths.rootEnvPath), schemaEnvPath: e10.relativeEnvPaths.schemaEnvPath && import_node_path4.default.resolve(e10.dirname, e10.relativeEnvPaths.schemaEnvPath) }, s;
      if (n?.adapter) {
        s = n.adapter;
        let l = e10.activeProvider === "postgresql" || e10.activeProvider === "cockroachdb" ? "postgres" : e10.activeProvider;
        if (s.provider !== l) throw new T(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`, this._clientVersion);
        if (n.datasources || n.datasourceUrl !== void 0) throw new T("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.", this._clientVersion);
      }
      let a = !s && o && nt(o, { conflictCheck: "none" }) || e10.injectableEdgeEnv?.();
      try {
        let l = n ?? {}, u = l.__internal ?? {}, c = u.debug === true;
        c && L.enable("prisma:client");
        let p = import_node_path4.default.resolve(e10.dirname, e10.relativePath);
        import_node_fs5.default.existsSync(p) || (p = e10.dirname), rr("dirname", e10.dirname), rr("relativePath", e10.relativePath), rr("cwd", p);
        let d = u.engine || {};
        if (l.errorFormat ? this._errorFormat = l.errorFormat : process.env.NODE_ENV === "production" ? this._errorFormat = "minimal" : process.env.NO_COLOR ? this._errorFormat = "colorless" : this._errorFormat = "colorless", this._runtimeDataModel = e10.runtimeDataModel, this._engineConfig = { cwd: p, dirname: e10.dirname, enableDebugLogs: c, allowTriggerPanic: d.allowTriggerPanic, prismaPath: d.binaryPath ?? void 0, engineEndpoint: d.endpoint, generator: e10.generator, showColors: this._errorFormat === "pretty", logLevel: l.log && Bl(l.log), logQueries: l.log && !!(typeof l.log == "string" ? l.log === "query" : l.log.find((f) => typeof f == "string" ? f === "query" : f.level === "query")), env: a?.parsed ?? {}, flags: [], engineWasm: e10.engineWasm, compilerWasm: e10.compilerWasm, clientVersion: e10.clientVersion, engineVersion: e10.engineVersion, previewFeatures: this._previewFeatures, activeProvider: e10.activeProvider, inlineSchema: e10.inlineSchema, overrideDatasources: el(l, e10.datasourceNames), inlineDatasources: e10.inlineDatasources, inlineSchemaHash: e10.inlineSchemaHash, tracingHelper: this._tracingHelper, transactionOptions: { maxWait: l.transactionOptions?.maxWait ?? 2e3, timeout: l.transactionOptions?.timeout ?? 5e3, isolationLevel: l.transactionOptions?.isolationLevel }, logEmitter: i, isBundled: e10.isBundled, adapter: s }, this._accelerateEngineConfig = { ...this._engineConfig, accelerateUtils: { resolveDatasourceUrl: $r, getBatchRequestPayload: Lr, prismaGraphQLToJSError: Fr, PrismaClientUnknownRequestError: q, PrismaClientInitializationError: T, PrismaClientKnownRequestError: Z, debug: L("prisma:client:accelerateEngine"), engineVersion: tu.version, clientVersion: e10.clientVersion } }, rr("clientVersion", e10.clientVersion), this._engine = Il(e10, this._engineConfig), this._requestHandler = new Jn(this, i), l.log) for (let f of l.log) {
          let h = typeof f == "string" ? f : f.emit === "stdout" ? f.level : null;
          h && this.$on(h, (g) => {
            et.log(`${et.tags[h] ?? ""}`, g.message || g.query);
          });
        }
      } catch (l) {
        throw l.clientVersion = this._clientVersion, l;
      }
      return this._appliedParent = xt(this);
    }
    get [Symbol.toStringTag]() {
      return "PrismaClient";
    }
    $use(n) {
      this._middlewares.use(n);
    }
    $on(n, i) {
      return n === "beforeExit" ? this._engine.onBeforeExit(i) : n && this._engineConfig.logEmitter.on(n, i), this;
    }
    $connect() {
      try {
        return this._engine.start();
      } catch (n) {
        throw n.clientVersion = this._clientVersion, n;
      }
    }
    async $disconnect() {
      try {
        await this._engine.stop();
      } catch (n) {
        throw n.clientVersion = this._clientVersion, n;
      } finally {
        jo();
      }
    }
    $executeRawInternal(n, i, o, s) {
      let a = this._activeProvider;
      return this._request({ action: "executeRaw", args: o, transaction: n, clientMethod: i, argsMapper: Eo({ clientMethod: i, activeProvider: a }), callsite: Ze(this._errorFormat), dataPath: [], middlewareArgsMapper: s });
    }
    $executeRaw(n, ...i) {
      return this._createPrismaPromise((o) => {
        if (n.raw !== void 0 || n.sql !== void 0) {
          let [s, a] = ru(n, i);
          return bo(this._activeProvider, s.text, s.values, Array.isArray(n) ? "prisma.$executeRaw`<SQL>`" : "prisma.$executeRaw(sql`<SQL>`)"), this.$executeRawInternal(o, "$executeRaw", s, a);
        }
        throw new X("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n", { clientVersion: this._clientVersion });
      });
    }
    $executeRawUnsafe(n, ...i) {
      return this._createPrismaPromise((o) => (bo(this._activeProvider, n, i, "prisma.$executeRawUnsafe(<SQL>, [...values])"), this.$executeRawInternal(o, "$executeRawUnsafe", [n, ...i])));
    }
    $runCommandRaw(n) {
      if (e10.activeProvider !== "mongodb") throw new X(`The ${e10.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`, { clientVersion: this._clientVersion });
      return this._createPrismaPromise((i) => this._request({ args: n, clientMethod: "$runCommandRaw", dataPath: [], action: "runCommandRaw", argsMapper: kl, callsite: Ze(this._errorFormat), transaction: i }));
    }
    async $queryRawInternal(n, i, o, s) {
      let a = this._activeProvider;
      return this._request({ action: "queryRaw", args: o, transaction: n, clientMethod: i, argsMapper: Eo({ clientMethod: i, activeProvider: a }), callsite: Ze(this._errorFormat), dataPath: [], middlewareArgsMapper: s });
    }
    $queryRaw(n, ...i) {
      return this._createPrismaPromise((o) => {
        if (n.raw !== void 0 || n.sql !== void 0) return this.$queryRawInternal(o, "$queryRaw", ...ru(n, i));
        throw new X("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n", { clientVersion: this._clientVersion });
      });
    }
    $queryRawTyped(n) {
      return this._createPrismaPromise((i) => {
        if (!this._hasPreviewFlag("typedSql")) throw new X("`typedSql` preview feature must be enabled in order to access $queryRawTyped API", { clientVersion: this._clientVersion });
        return this.$queryRawInternal(i, "$queryRawTyped", n);
      });
    }
    $queryRawUnsafe(n, ...i) {
      return this._createPrismaPromise((o) => this.$queryRawInternal(o, "$queryRawUnsafe", [n, ...i]));
    }
    _transactionWithArray({ promises: n, options: i }) {
      let o = $f.nextId(), s = Vl(n.length), a = n.map((l, u) => {
        if (l?.[Symbol.toStringTag] !== "PrismaPromise") throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");
        let c = i?.isolationLevel ?? this._engineConfig.transactionOptions.isolationLevel, p = { kind: "batch", id: o, index: u, isolationLevel: c, lock: s };
        return l.requestTransaction?.(p) ?? l;
      });
      return eu(a);
    }
    async _transactionWithCallback({ callback: n, options: i }) {
      let o = { traceparent: this._tracingHelper.getTraceParent() }, s = { maxWait: i?.maxWait ?? this._engineConfig.transactionOptions.maxWait, timeout: i?.timeout ?? this._engineConfig.transactionOptions.timeout, isolationLevel: i?.isolationLevel ?? this._engineConfig.transactionOptions.isolationLevel }, a = await this._engine.transaction("start", o, s), l;
      try {
        let u = { kind: "itx", ...a };
        l = await n(this._createItxClient(u)), await this._engine.transaction("commit", o, a);
      } catch (u) {
        throw await this._engine.transaction("rollback", o, a).catch(() => {
        }), u;
      }
      return l;
    }
    _createItxClient(n) {
      return he(xt(he($a(this), [ee("_appliedParent", () => this._appliedParent._createItxClient(n)), ee("_createPrismaPromise", () => wo(n)), ee(Mf, () => n.id)])), [Nr(Ua)]);
    }
    $transaction(n, i) {
      let o;
      typeof n == "function" ? this._engineConfig.adapter?.adapterName === "@prisma/adapter-d1" ? o = () => {
        throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.");
      } : o = () => this._transactionWithCallback({ callback: n, options: i }) : o = () => this._transactionWithArray({ promises: n, options: i });
      let s = { name: "transaction", attributes: { method: "$transaction" } };
      return this._tracingHelper.runInChildSpan(s, o);
    }
    _request(n) {
      n.otelParentCtx = this._tracingHelper.getActiveContext();
      let i = n.middlewareArgsMapper ?? Ff, o = { args: i.requestArgsToMiddlewareArgs(n.args), dataPath: n.dataPath, runInTransaction: !!n.transaction, action: n.action, model: n.model }, s = { middleware: { name: "middleware", middleware: true, attributes: { method: "$use" }, active: false }, operation: { name: "operation", attributes: { method: o.action, model: o.model, name: o.model ? `${o.model}.${o.action}` : o.action } } }, a = -1, l = async (u) => {
        let c = this._middlewares.get(++a);
        if (c) return this._tracingHelper.runInChildSpan(s.middleware, (S) => c(u, (P) => (S?.end(), l(P))));
        let { runInTransaction: p, args: d, ...f } = u, h = { ...n, ...f };
        d && (h.args = i.middlewareArgsToRequestArgs(d)), n.transaction !== void 0 && p === false && delete h.transaction;
        let g = await Ja(this, h);
        return h.model ? Ba({ result: g, modelName: h.model, args: h.args, extensions: this._extensions, runtimeDataModel: this._runtimeDataModel, globalOmit: this._globalOmit }) : g;
      };
      return this._tracingHelper.runInChildSpan(s.operation, () => new import_node_async_hooks.AsyncResource("prisma-client-request").runInAsyncScope(() => l(o)));
    }
    async _executeRequest({ args: n, clientMethod: i, dataPath: o, callsite: s, action: a, model: l, argsMapper: u, transaction: c, unpacker: p, otelParentCtx: d, customDataProxyFetch: f }) {
      try {
        n = u ? u(n) : n;
        let h = { name: "serialize" }, g = this._tracingHelper.runInChildSpan(h, () => Wi({ modelName: l, runtimeDataModel: this._runtimeDataModel, action: a, args: n, clientMethod: i, callsite: s, extensions: this._extensions, errorFormat: this._errorFormat, clientVersion: this._clientVersion, previewFeatures: this._previewFeatures, globalOmit: this._globalOmit }));
        return L.enabled("prisma:client") && (rr("Prisma Client call:"), rr(`prisma.${i}(${Ca(n)})`), rr("Generated request:"), rr(JSON.stringify(g, null, 2) + `
`)), c?.kind === "batch" && await c.lock, this._requestHandler.request({ protocolQuery: g, modelName: l, action: a, clientMethod: i, dataPath: o, callsite: s, args: n, extensions: this._extensions, transaction: c, unpacker: p, otelParentCtx: d, otelChildCtx: this._tracingHelper.getActiveContext(), globalOmit: this._globalOmit, customDataProxyFetch: f });
      } catch (h) {
        throw h.clientVersion = this._clientVersion, h;
      }
    }
    $metrics = new yt(this);
    _hasPreviewFlag(n) {
      return !!this._engineConfig.previewFeatures?.includes(n);
    }
    $applyPendingMigrations() {
      return this._engine.applyPendingMigrations();
    }
    $extends = qa;
  }
  return r;
}
function ru(e10, r) {
  return jf(e10) ? [new le(e10, r), Ml] : [e10, $l];
}
function jf(e10) {
  return Array.isArray(e10) && Array.isArray(e10.raw);
}
var Vf = /* @__PURE__ */ new Set(["toJSON", "$$typeof", "asymmetricMatch", Symbol.iterator, Symbol.toStringTag, Symbol.isConcatSpreadable, Symbol.toPrimitive]);
function Bf(e10) {
  return new Proxy(e10, { get(r, t) {
    if (t in r) return r[t];
    if (!Vf.has(t)) throw new TypeError(`Invalid enum value: ${String(t)}`);
  } });
}
function Uf(e10) {
  nt(e10, { conflictCheck: "warn" });
}
export {
  fn as DMMF,
  L as Debug,
  Le as Decimal,
  Io as Extensions,
  yt as MetricsClient,
  T as PrismaClientInitializationError,
  Z as PrismaClientKnownRequestError,
  de as PrismaClientRustPanicError,
  q as PrismaClientUnknownRequestError,
  X as PrismaClientValidationError,
  Do as Public,
  le as Sql,
  Bd as createParam,
  Zd as defineDmmfProperty,
  ot as deserializeJsonResponse,
  To as deserializeRawResult,
  ed as dmmfToRuntimeDataModel,
  tm as empty,
  qf as getPrismaClient,
  to as getRuntime,
  rm as join,
  Bf as makeStrictEnum,
  em as makeTypedQueryFactory,
  Vi as objectEnumValues,
  Pa as raw,
  Wi as serializeJsonQuery,
  Gi as skip,
  Ta as sqltag,
  Uf as warnEnvConflicts,
  tn as warnOnce
};
/*! Bundled license information:

@prisma/client/runtime/library.mjs:
  (*! Bundled license information:
  
  decimal.js/decimal.mjs:
    (*!
     *  decimal.js v10.5.0
     *  An arbitrary-precision Decimal type for JavaScript.
     *  https://github.com/MikeMcl/decimal.js
     *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
     *  MIT Licence
     *)
  *)
*/
//# sourceMappingURL=@prisma_client_runtime_library.js.map

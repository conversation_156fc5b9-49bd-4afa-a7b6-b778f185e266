import { ReactNode, useState } from "react";
import clsx from "clsx";

interface Props {
  title: ReactNode;
  icon?: ReactNode;
  children: ReactNode;
  description?: string;
  className?: string;
  totalFields?: number;
  right?: ReactNode;
  isLast?: boolean;
  defaultOpen?: boolean;
  filled?: number;
  isDrawer?: boolean;
  lineStyle?: React.CSSProperties;
  inputGroupClassName?: string; 
}

export default function InputGroup({
  title,
  description,
  icon,
  children,
  right,
  className,
  lineStyle,
  totalFields = 0,
  isLast,
  filled = 0,
  defaultOpen = true,
  isDrawer,
  inputGroupClassName, 
}: Props) {
  const [isOpen, setIsOpen] = useState(() => !!defaultOpen);

  const toggleOpen = () => setIsOpen(!isOpen);

  // Drawer View
  if (isDrawer) {
    return (
      <div className={clsx("relative")}>
        <div className="">
          {description && <p className="text-sm text-gray-700">{description}</p>}
          <div className={clsx(className)}>{children}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx(inputGroupClassName)}>
    <div
      className={clsx(
        "border-input bg-primary-foreground relative w-full rounded-[12px] border-t-[1px] border-r-[1px] border-b-[1px] border-l-[1px] border-solid pt-3 pb-4 shadow-lg cursor-pointer",

      )}
    >
      <div
        onClick={toggleOpen}
        className={clsx("flex items-center justify-between pl-1", isOpen && "border-input bg-primary-foreground w-full border-b-[1px] border-solid pb-[13px]")}
      >
        <div className={clsx("flex cursor-pointer items-center")}>
          {isOpen ? (
            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-foreground z-5 pt-1">
              <rect width="28" height="28" rx="8" />
              <path
                d="M11.4314 11.5C10.7186 11.5 10.3617 12.3617 10.8657 12.8657L13.9343 15.9343C14.2467 16.2467 14.7533 16.2467 15.0657 15.9343L18.1343 12.8657C18.6383 12.3617 18.2814 11.5 17.5686 11.5H11.4314Z"
                fill="currentColor"
              />
            </svg>
          ) : (
            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-foreground z-5 pt-1">
              <rect width="28" height="28" rx="8" />
              <path
                d="M12 17.0686C12 17.7814 12.8617 18.1383 13.3657 17.6343L16.4343 14.5657C16.7467 14.2533 16.7467 13.7467 16.4343 13.4343L13.3657 10.3657C12.8617 9.86171 12 10.2186 12 10.9314V17.0686Z"
                fill="currentColor"
              />
            </svg>
          )}
          {icon}
          <span className={clsx("text-foreground flex items-center text-[16px] leading-[19px] font-bold", isOpen && "")}>{title}&nbsp;</span>
          <span className={clsx("text-muted-foreground ml-[2px] text-xs", isOpen && "")}>
            ({filled}/{totalFields} fields filled)
          </span>
        </div>
        {/* Right Content */}
        {right && <div className="hidden md:flex">{right}</div>}
      </div>

      {/* Description and Content */}
      <div className={`box-shadow: 0px 4px 14.9px 0px hsla(0, 0%, 72%, 0.25); relative px-4`}>
        {description && <p className="text-sm text-foreground">{description}</p>}
        <div className={clsx(isOpen ? "mt-[20px]" : "hidden", className)}>{children}</div>
      </div>
    </div>
    </div>
  );
}
